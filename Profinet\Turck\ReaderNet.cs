﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Turck.ReaderNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Turck;

/// <summary>Reader协议的实现</summary>
public class ReaderNet : DeviceTcpNet
{
  private bool successfullyInitialized = false;

  /// <summary>
  /// 实例化默认的构造方法<br />
  /// Instantiate the default constructor
  /// </summary>
  public ReaderNet()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>
  /// 使用指定的ip地址和端口来实例化一个对象<br />
  /// Instantiate an object with the specified IP address and port
  /// </summary>
  /// <param name="ipAddress">设备的Ip地址</param>
  /// <param name="port">设备的端口号</param>
  public ReaderNet(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new TurckReaderMessage();

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    this.successfullyInitialized = false;
    return base.InitializationOnConnect();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    this.successfullyInitialized = false;
    OperateResult operateResult = await base.InitializationOnConnectAsync();
    return operateResult;
  }

  /// <summary>获取设备的唯一的UID信息，本值会在连接上PLC之后自动赋值</summary>
  public string UID { get; private set; }

  /// <summary>获取当前设备的数据块总数量，本值会在连接上PLC之后自动赋值</summary>
  public byte NumberOfBlock { get; private set; }

  /// <summary>获取当前设备的每个数据块拥有的字节数，本值会在连接上PLC之后自动赋值</summary>
  public byte BytesOfBlock { get; private set; }

  /// <summary>读取指定地址的byte数据</summary>
  /// <param name="address">起始地址</param>
  /// <returns>是否读取成功的结果对象 -&gt; Whether to read the successful result object</returns>
  /// <example>参考<see cref="M:HslCommunication.Profinet.Turck.ReaderNet.Read(System.String,System.UInt16)" />的注释</example>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>
  /// 向设备中写入byte数据，返回值说明<br />
  /// </summary>
  /// <param name="address">起始地址</param>
  /// <param name="value">byte数据 -&gt; Byte data</param>
  /// <returns>是否写入成功的结果对象 -&gt; Whether to write a successful result object</returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Turck.ReaderNet.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Turck.ReaderNet.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  private OperateResult<byte[]> CheckResponseContent(byte[] content)
  {
    if (content[1] == (byte) 10 && content[2] == (byte) 10)
    {
      if (content[5] == (byte) 0 && content[6] == (byte) 2 && content[7] == (byte) 0)
        this.successfullyInitialized = false;
      return new OperateResult<byte[]>($"{ReaderNet.GetErrorText(content[5], content[6], content[7])} Source: {content.ToHexString(' ')}");
    }
    if (content[1] == (byte) 7 && content[2] == (byte) 7)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    return content.Length > 7 ? OperateResult.CreateSuccessResult<byte[]>(content.SelectMiddle<byte>(5, content.Length - 7)) : new OperateResult<byte[]>("Error message: " + content.ToHexString(' '));
  }

  private OperateResult<byte[]> ReadRaw(byte startBlock, byte lengthOfBlock)
  {
    List<byte[]> numArrayList = ReaderNet.BuildReadCommand(startBlock, lengthOfBlock, this.BytesOfBlock);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < numArrayList.Count; ++index)
    {
      OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(numArrayList[index]);
      if (!operateResult1.IsSuccess)
        return operateResult1;
      OperateResult<byte[]> operateResult2 = this.CheckResponseContent(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      byteList.AddRange((IEnumerable<byte>) operateResult2.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  private OperateResult WriteRaw(byte startBlock, byte lengthOfBlock, byte[] value)
  {
    List<byte[]> numArrayList = ReaderNet.BuildWriteCommand(startBlock, lengthOfBlock, this.BytesOfBlock, value);
    for (int index = 0; index < numArrayList.Count; ++index)
    {
      OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(numArrayList[index]);
      if (!operateResult1.IsSuccess)
        return (OperateResult) operateResult1;
      OperateResult<byte[]> operateResult2 = this.CheckResponseContent(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return (OperateResult) operateResult2;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> result = this.ReadRFIDInfo();
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    }
    OperateResult<ushort> address1 = ReaderNet.ParseAddress(address, false);
    if (!address1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) address1);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(address1.Content, length, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> operateResult = this.ReadRaw(startBlock, lengthOfBlock);
    return !operateResult.IsSuccess ? operateResult : OperateResult.CreateSuccessResult<byte[]>(operateResult.Content.SelectMiddle<byte>((int) address1.Content % (int) this.BytesOfBlock, (int) length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> result = this.ReadRFIDInfo();
      if (!result.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    }
    OperateResult<ushort> address1 = ReaderNet.ParseAddress(address, false);
    if (!address1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) address1);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(address1.Content, (ushort) value.Length, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> operateResult = this.ReadRaw(startBlock, lengthOfBlock);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    value.CopyTo((Array) operateResult.Content, (int) address1.Content % (int) this.BytesOfBlock);
    return this.WriteRaw(startBlock, lengthOfBlock, operateResult.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> result = this.ReadRFIDInfo();
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    }
    OperateResult<ushort> address1 = ReaderNet.ParseAddress(address, true);
    if (!address1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) address1);
    ushort address2 = (ushort) ((uint) address1.Content / 8U);
    ushort length1 = (ushort) (((int) address1.Content + (int) length - 1) / 8 - (int) address2 + 1);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(address2, length1, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> result1 = this.ReadRaw(startBlock, lengthOfBlock);
    return !result1.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result1) : OperateResult.CreateSuccessResult<bool[]>(result1.Content.SelectMiddle<byte>((int) address2 % (int) this.BytesOfBlock, (int) length1).ToBoolArray().SelectMiddle<bool>((int) address1.Content % 8, (int) length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> result = this.ReadRFIDInfo();
      if (!result.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    }
    OperateResult<ushort> address1 = ReaderNet.ParseAddress(address, true);
    if (!address1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) address1);
    ushort address2 = (ushort) ((uint) address1.Content / 8U);
    ushort length = (ushort) (((int) address1.Content + value.Length - 1) / 8 - (int) address2 + 1);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(address2, length, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> result1 = this.ReadRaw(startBlock, lengthOfBlock);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    bool[] boolArray = result1.Content.ToBoolArray();
    value.CopyTo((Array) boolArray, (int) address2 % (int) this.BytesOfBlock * 8 + (int) address1.Content % 8);
    return this.WriteRaw(startBlock, lengthOfBlock, boolArray.ToByteArray());
  }

  private async Task<OperateResult<byte[]>> ReadRawAsync(byte startBlock, byte lengthOfBlock)
  {
    List<byte[]> list = ReaderNet.BuildReadCommand(startBlock, lengthOfBlock, this.BytesOfBlock);
    List<byte> result = new List<byte>();
    for (int i = 0; i < list.Count; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(list[i]);
      if (!read.IsSuccess)
        return read;
      OperateResult<byte[]> check = this.CheckResponseContent(read.Content);
      if (!check.IsSuccess)
        return check;
      result.AddRange((IEnumerable<byte>) check.Content);
      read = (OperateResult<byte[]>) null;
      check = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(result.ToArray());
  }

  private async Task<OperateResult> WriteRawAsync(
    byte startBlock,
    byte lengthOfBlock,
    byte[] value)
  {
    List<byte[]> list = ReaderNet.BuildWriteCommand(startBlock, lengthOfBlock, this.BytesOfBlock, value);
    for (int i = 0; i < list.Count; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(list[i]);
      if (!read.IsSuccess)
        return (OperateResult) read;
      OperateResult<byte[]> check = this.CheckResponseContent(read.Content);
      if (!check.IsSuccess)
        return (OperateResult) check;
      read = (OperateResult<byte[]>) null;
      check = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> ini = await this.ReadRFIDInfoAsync();
      if (!ini.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) ini);
      ini = (OperateResult<string>) null;
    }
    OperateResult<ushort> addAnalysis = ReaderNet.ParseAddress(address, false);
    if (!addAnalysis.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) addAnalysis);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(addAnalysis.Content, length, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> read = await this.ReadRawAsync(startBlock, lengthOfBlock);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content.SelectMiddle<byte>((int) addAnalysis.Content % (int) this.BytesOfBlock, (int) length)) : read;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> ini = await this.ReadRFIDInfoAsync();
      if (!ini.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) ini);
      ini = (OperateResult<string>) null;
    }
    OperateResult<ushort> addAnalysis = ReaderNet.ParseAddress(address, false);
    if (!addAnalysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) addAnalysis);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(addAnalysis.Content, (ushort) value.Length, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> readRaw = await this.ReadRawAsync(startBlock, lengthOfBlock);
    if (!readRaw.IsSuccess)
      return (OperateResult) readRaw;
    value.CopyTo((Array) readRaw.Content, (int) addAnalysis.Content % (int) this.BytesOfBlock);
    OperateResult operateResult = await this.WriteRawAsync(startBlock, lengthOfBlock, readRaw.Content);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> ini = await this.ReadRFIDInfoAsync();
      if (!ini.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) ini);
      ini = (OperateResult<string>) null;
    }
    OperateResult<ushort> addAnalysis = ReaderNet.ParseAddress(address, true);
    if (!addAnalysis.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) addAnalysis);
    ushort byteStart = (ushort) ((uint) addAnalysis.Content / 8U);
    ushort byteLength = (ushort) (((int) addAnalysis.Content + (int) length - 1) / 8 - (int) byteStart + 1);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(byteStart, byteLength, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> read = await this.ReadRawAsync(startBlock, lengthOfBlock);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.SelectMiddle<byte>((int) byteStart % (int) this.BytesOfBlock, (int) byteLength).ToBoolArray().SelectMiddle<bool>((int) addAnalysis.Content % 8, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    if (!this.successfullyInitialized)
    {
      OperateResult<string> ini = await this.ReadRFIDInfoAsync();
      if (!ini.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) ini);
      ini = (OperateResult<string>) null;
    }
    OperateResult<ushort> addAnalysis = ReaderNet.ParseAddress(address, true);
    if (!addAnalysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) addAnalysis);
    ushort byteStart = (ushort) ((uint) addAnalysis.Content / 8U);
    ushort byteLength = (ushort) (((int) addAnalysis.Content + value.Length - 1) / 8 - (int) byteStart + 1);
    byte startBlock;
    byte lengthOfBlock;
    ReaderNet.CalculateBlockAddress(byteStart, byteLength, this.BytesOfBlock, out startBlock, out lengthOfBlock);
    OperateResult<byte[]> readRaw = await this.ReadRawAsync(startBlock, lengthOfBlock);
    if (!readRaw.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) readRaw);
    bool[] boolArray = readRaw.Content.ToBoolArray();
    value.CopyTo((Array) boolArray, (int) byteStart % (int) this.BytesOfBlock * 8 + (int) addAnalysis.Content % 8);
    OperateResult operateResult = await this.WriteRawAsync(startBlock, lengthOfBlock, boolArray.ToByteArray());
    return operateResult;
  }

  private OperateResult<string> ExtraUID(byte[] content)
  {
    OperateResult<byte[]> result = this.CheckResponseContent(content);
    if (result.IsSuccess)
    {
      this.UID = content.SelectMiddle<byte>(5, 8).ToHexString();
      this.NumberOfBlock = content[15];
      this.BytesOfBlock = (byte) ((uint) content[16 /*0x10*/] + 1U);
      this.successfullyInitialized = true;
      return OperateResult.CreateSuccessResult<string>(this.UID);
    }
    this.successfullyInitialized = false;
    return OperateResult.CreateFailedResult<string>((OperateResult) result);
  }

  /// <summary>读取载码体信息，并将读取的信息进行初始化</summary>
  /// <returns>返回UID信息</returns>
  public OperateResult<string> ReadRFIDInfo()
  {
    return this.ReadFromCoreServer(ReaderNet.PackReaderCommand(new byte[2]
    {
      (byte) 112 /*0x70*/,
      (byte) 0
    })).Then<string>(new Func<byte[], OperateResult<string>>(this.ExtraUID));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Turck.ReaderNet.ReadRFIDInfo" />
  public async Task<OperateResult<string>> ReadRFIDInfoAsync()
  {
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(ReaderNet.PackReaderCommand(new byte[2]
    {
      (byte) 112 /*0x70*/,
      (byte) 0
    }));
    return operateResult.Then<string>(new Func<byte[], OperateResult<string>>(this.ExtraUID));
  }

  /// <inheritdoc />
  public override string ToString() => $"ReaderNet[{this.IpAddress}:{this.Port}]";

  private static string GetErrorText(byte err1, byte err2, byte err3)
  {
    switch (err1)
    {
      case 1:
        return "Command not supported";
      case 2:
        return "Command not correctly detected, e.g. wrong format";
      case 3:
        return "Command option not supportde";
      case 15:
        return "Undefined/General error";
      case 16 /*0x10*/:
        return "Requested memory block not available";
      case 17:
        return "Requested memory block is already locked";
      case 18:
        return "Requested memory block is locked and cannot be written";
      case 19:
        return "Writing of requested memory block not successful";
      case 20:
        return "Requested memory block could not be locked";
      default:
        if (err1 > (byte) 0)
          return "Customer specific error codes";
        switch (err2)
        {
          case 1:
            return "CRC_ERR, telegram fault in the tag-response";
          case 2:
            return "TimeOut_ERR, no tag-response in the given time";
          case 4:
            return "Tag_ERR, tag defect, e.g. multiple crc-faults on the air interface";
          case 8:
            return "CHAIN_ERR, Tag has left the air interface before executing all commands";
          case 16 /*0x10*/:
            return "UID_ERR, other UID as expected was detected during addressed mode";
          default:
            if (err2 > (byte) 0)
              return StringResources.Language.UnknownError;
            switch (err3)
            {
              case 1:
                return "TRANS_ERR, transceiver defect, e.g. Flash-checksum";
              case 2:
                return "CMD_ERR, fault during execution of a command";
              case 4:
                return "syntax_ERR, telegram content not valid, e.g. requested tag-memory address not available";
              case 8:
                return "PS_ERR, power supply too low";
              case 16 /*0x10*/:
                return "CMD_UNKNOWN, unknown command code";
              default:
                return StringResources.Language.UnknownError;
            }
        }
    }
  }

  /// <summary>将字符串的地址解析出实际的整数地址，如果是位地址，支持使用小数点的形式 例如100.1</summary>
  /// <param name="address">地址信息</param>
  /// <param name="isBit">是否位地址</param>
  /// <returns>整数地址信息</returns>
  public static OperateResult<ushort> ParseAddress(string address, bool isBit)
  {
    try
    {
      if (!isBit)
        return OperateResult.CreateSuccessResult<ushort>(ushort.Parse(address));
      if (address.IndexOf('.') < 0)
        return OperateResult.CreateSuccessResult<ushort>(ushort.Parse(address));
      string[] strArray = address.Split(new char[1]{ '.' }, StringSplitOptions.RemoveEmptyEntries);
      return OperateResult.CreateSuccessResult<ushort>((ushort) (int.Parse(strArray[0]) * 8 + int.Parse(strArray[1])));
    }
    catch (Exception ex)
    {
      return new OperateResult<ushort>("Address input wrong, reason: " + ex.Message);
    }
  }

  /// <summary>计算缓存数据里的CRC校验信息，并返回CRC计算的结果</summary>
  /// <param name="data">数据信息</param>
  /// <param name="len">计算的长度信息</param>
  /// <returns>CRC计算结果</returns>
  public static byte[] CalculateCRC(byte[] data, int len)
  {
    int num1 = (int) ushort.MaxValue;
    int num2 = 33800;
    byte[] crc = new byte[2];
    for (int index1 = 0; index1 < len; ++index1)
    {
      num1 ^= (int) data[index1];
      for (int index2 = 0; index2 < 8; ++index2)
      {
        if ((num1 & 1) == 1)
          num1 = num1 >> 1 ^ num2;
        else
          num1 >>= 1;
      }
    }
    int num3 = ~num1;
    crc[0] = Convert.ToByte(num3 & (int) byte.MaxValue);
    crc[1] = Convert.ToByte(num3 >> 8 & (int) byte.MaxValue);
    return crc;
  }

  /// <summary>计算并填充CRC校验到原始数据中去</summary>
  /// <param name="data">原始的数据信息</param>
  /// <param name="len">计算的长度信息</param>
  public static void CalculateAndFillCRC(byte[] data, int len)
  {
    byte[] crc = ReaderNet.CalculateCRC(data, len);
    data[len] = crc[0];
    data[len + 1] = crc[1];
  }

  /// <summary>校验当前数据的CRC校验是否正确</summary>
  /// <param name="data">原始数据信息</param>
  /// <param name="len">长度数据信息</param>
  /// <returns>校验结果</returns>
  public static bool CheckCRC(byte[] data, int len)
  {
    byte[] crc = ReaderNet.CalculateCRC(data, len);
    return (int) data[len] == (int) crc[0] && (int) data[len + 1] == (int) crc[1];
  }

  /// <summary>将普通的命令打造成图尔克的reader协议完整命令</summary>
  /// <param name="command">命令信息</param>
  /// <returns>完整的命令包</returns>
  public static byte[] PackReaderCommand(byte[] command)
  {
    byte[] data = new byte[5 + command.Length];
    data[0] = (byte) 170;
    data[1] = (byte) data.Length;
    data[2] = (byte) data.Length;
    command.CopyTo((Array) data, 3);
    ReaderNet.CalculateAndFillCRC(data, 3 + command.Length);
    return data;
  }

  /// <summary>构建读取的数据块的命令信息，一次最多读取64个字节</summary>
  /// <param name="startBlock">需要读取的起始 Block。从 0 开始。</param>
  /// <param name="numberBlock">需要读取的 Block 数量。 从 0 开始。</param>
  /// <param name="bytesOfBlock">每个数据块占用的字节数</param>
  /// <returns>完整的命令报文信息</returns>
  private static List<byte[]> BuildReadCommand(
    byte startBlock,
    byte numberBlock,
    byte bytesOfBlock)
  {
    int everyLength = 64 /*0x40*/ / (int) bytesOfBlock;
    int[] array = SoftBasic.SplitIntegerToArray((int) numberBlock, everyLength);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      numArrayList.Add(ReaderNet.PackReaderCommand(new byte[4]
      {
        (byte) 104,
        (byte) 0,
        startBlock,
        (byte) (array[index] - 1)
      }));
      startBlock += (byte) array[index];
    }
    return numArrayList;
  }

  /// <summary>构建写入数据块的命令信息，一次最多写入64个字节</summary>
  /// <param name="startBlock">需要读取的起始 Block。从 0 开始。</param>
  /// <param name="numberBlock">需要读取的 Block 数量。 从 0 开始。</param>
  /// <param name="bytesOfBlock">每个数据块占用的字节数</param>
  /// <param name="value">写入的数据</param>
  /// <returns>完整的写入的命令报文信息</returns>
  private static List<byte[]> BuildWriteCommand(
    byte startBlock,
    byte numberBlock,
    byte bytesOfBlock,
    byte[] value)
  {
    if (value == null)
      value = new byte[0];
    int everyLength = 64 /*0x40*/ / (int) bytesOfBlock;
    int[] array = SoftBasic.SplitIntegerToArray((int) numberBlock, everyLength);
    List<byte[]> numArrayList = new List<byte[]>();
    int index1 = 0;
    for (int index2 = 0; index2 < array.Length; ++index2)
    {
      byte[] command = new byte[4 + array[index2] * (int) bytesOfBlock];
      command[0] = (byte) 105;
      command[1] = (byte) 0;
      command[2] = startBlock;
      command[3] = (byte) (array[index2] - 1);
      value.SelectMiddle<byte>(index1, array[index2] * (int) bytesOfBlock).CopyTo((Array) command, 4);
      startBlock += (byte) array[index2];
      index1 += array[index2] * (int) bytesOfBlock;
      numArrayList.Add(ReaderNet.PackReaderCommand(command));
    }
    return numArrayList;
  }

  private static void CalculateBlockAddress(
    ushort address,
    ushort length,
    byte bytesOfBlock,
    out byte startBlock,
    out byte lengthOfBlock)
  {
    startBlock = (byte) ((uint) address / (uint) bytesOfBlock);
    int num = (int) (byte) ((uint) ((int) address + (int) length - 1) / (uint) bytesOfBlock);
    lengthOfBlock = (byte) (num - (int) startBlock + 1);
  }
}
