﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.OpenEventArgs
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>Open协议的消息对象信息</summary>
public class OpenEventArgs : EventArgs
{
  /// <summary>实例化一个默认的对象</summary>
  public OpenEventArgs()
  {
  }

  /// <summary>指定Open的消息来实例化对象</summary>
  /// <param name="content">Open内容</param>
  public OpenEventArgs(string content) => this.Content = content;

  /// <summary>Open协议的消息内容</summary>
  public string Content { get; set; }
}
