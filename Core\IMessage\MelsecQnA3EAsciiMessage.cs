﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.MelsecQnA3EAsciiMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>基于MC协议的Qna兼容3E帧协议的ASCII通讯消息机制</summary>
public class MelsecQnA3EAsciiMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 18;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    return Convert.ToInt32(Encoding.ASCII.GetString(new byte[4]
    {
      this.HeadBytes[14],
      this.HeadBytes[15],
      this.HeadBytes[16 /*0x10*/],
      this.HeadBytes[17]
    }), 16 /*0x10*/);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes != null && this.HeadBytes[0] == (byte) 68 && this.HeadBytes[1] == (byte) 48 /*0x30*/ && this.HeadBytes[2] == (byte) 48 /*0x30*/ && this.HeadBytes[3] == (byte) 48 /*0x30*/;
  }
}
