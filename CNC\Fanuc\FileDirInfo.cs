﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.FileDirInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>文件或是文件夹的信息</summary>
public class FileDirInfo
{
  /// <summary>实例化一个默认的对象</summary>
  public FileDirInfo()
  {
  }

  /// <summary>使用原始字节来实例化对象</summary>
  /// <param name="byteTransform">字节变换对象</param>
  /// <param name="buffer">原始的字节信息</param>
  /// <param name="index">起始的索引信息</param>
  public FileDirInfo(IByteTransform byteTransform, byte[] buffer, int index)
  {
    this.IsDirectory = byteTransform.TransInt16(buffer, index) == (short) 0;
    this.Name = buffer.GetStringOrEndChar(index + 28, 36, Encoding.ASCII);
    if (this.IsDirectory)
      return;
    this.LastModified = new DateTime((int) byteTransform.TransInt16(buffer, index + 2), (int) byteTransform.TransInt16(buffer, index + 4), (int) byteTransform.TransInt16(buffer, index + 6), (int) byteTransform.TransInt16(buffer, index + 8), (int) byteTransform.TransInt16(buffer, index + 10), (int) byteTransform.TransInt16(buffer, index + 12));
    this.Size = byteTransform.TransInt32(buffer, index + 20);
  }

  /// <summary>是否为文件夹，True就是文件夹，False就是文件</summary>
  public bool IsDirectory { get; set; }

  /// <summary>文件或是文件夹的名称</summary>
  public string Name { get; set; }

  /// <summary>最后一次更新时间，当为文件的时候有效</summary>
  public DateTime LastModified { get; set; }

  /// <summary>文件的大小，当为文件的时候有效</summary>
  public int Size { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(this.IsDirectory ? "[PATH]   " : "[FILE]   ");
    stringBuilder.Append(this.Name.PadRight(40));
    if (!this.IsDirectory)
    {
      stringBuilder.Append("     ");
      stringBuilder.Append(this.LastModified.ToString("yyyy-MM-dd HH:mm:ss"));
      stringBuilder.Append("         ");
      stringBuilder.Append(SoftBasic.GetSizeDescription((long) this.Size));
    }
    return stringBuilder.ToString();
  }
}
