﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Keyence.KeyenceDLEN1
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Keyence;

/// <summary>基恩士的数字传感器的以太网模块，可以同时连接并读取多个传感器模块的功能代码</summary>
public class KeyenceDLEN1 : NetworkDoubleBase
{
  /// <summary>
  /// 实例化基恩士的Qna兼容3E帧协议的通讯对象<br />
  /// Instantiate Keyence Qna compatible 3E frame protocol communication object
  /// </summary>
  public KeyenceDLEN1() => this.ByteTransform = (IByteTransform) new RegularByteTransform();

  /// <summary>
  /// 指定ip地址及端口号来实例化一个基恩士的Qna兼容3E帧协议的通讯对象<br />
  /// Specify an IP address and port number to instantiate a Keynes Qna compatible 3E frame protocol communication object
  /// </summary>
  /// <param name="ipAddress">PLC的Ip地址</param>
  /// <param name="port">PLC的端口</param>
  public KeyenceDLEN1(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13, (byte) 10);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    OperateResult result = KeyenceDLEN1.CheckResponse(response);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : base.UnpackResponseContent(send, response);
  }

  /// <summary>使用M0命令读取所有的传感器的数据信息</summary>
  /// <param name="cmds">命令信息</param>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult<string[]> ReadByCommand(string[] cmds)
  {
    StringBuilder stringBuilder = new StringBuilder();
    for (int index = 0; index < cmds.Length; ++index)
    {
      stringBuilder.Append(cmds[index]);
      if (index < cmds.Length - 1)
        stringBuilder.Append(",");
    }
    stringBuilder.Append("\r\n");
    OperateResult<byte[]> result = this.ReadFromCoreServer(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result);
    return result.Content.Length <= 5 ? OperateResult.CreateSuccessResult<string[]>(new string[1]
    {
      Encoding.ASCII.GetString(result.Content)
    }) : OperateResult.CreateSuccessResult<string[]>(Encoding.ASCII.GetString(result.Content, 2, result.Content.Length - 5).Split(new char[1]
    {
      ','
    }, StringSplitOptions.RemoveEmptyEntries));
  }

  /// <inheritdoc />
  public override string ToString() => $"KeyenceDLEN1[{this.IpAddress}:{this.Port}]";

  /// <summary>坚持设备的返回的数据，并校验是否成功</summary>
  /// <param name="content">设备的返回数据信息</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult CheckResponse(byte[] content)
  {
    if (content.Length < 9 || !(Encoding.ASCII.GetString(content, 0, 2) == "ER"))
      return OperateResult.CreateSuccessResult();
    int int32 = Convert.ToInt32(Encoding.ASCII.GetString(content, 6, 3));
    switch (int32)
    {
      case 9:
        return new OperateResult(int32, "写入的数据超出有效范围。传感器不支持写入指定的 ID、数据编号。");
      case 12:
        return new OperateResult(int32, "无法执行动作指令的状态。传感器不支持写入指定的 ID、数据编号。");
      case 14:
        return new OperateResult(int32, "该地址处于禁止写入或无法写入的状态。");
      case 16 /*0x10*/:
        return new OperateResult(int32, "该数据编号处于禁止读取或无法读取的状态。");
      case 20:
        return new OperateResult(int32, "数据编号超出有效范围。");
      case 22:
        return new OperateResult(int32, "ID 超出有效范围。");
      case 31 /*0x1F*/:
        return new OperateResult(int32, "传感器不支持读取、写入指定的 ID、数据编号。当前模式下无法写入或本机正在进行通信初始化。");
      case 254:
        return new OperateResult(int32, "系统错误状态。请等待启动时间。请确认 D-bus 连接器等无异常。");
      case (int) byte.MaxValue:
        return new OperateResult(int32, "命令格式错误。");
      default:
        return new OperateResult(int32, StringResources.Language.UnknownError);
    }
  }
}
