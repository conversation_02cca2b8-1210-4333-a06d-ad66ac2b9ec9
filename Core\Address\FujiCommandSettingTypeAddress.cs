﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.FujiCommandSettingTypeAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text.RegularExpressions;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>富士CommandSettingsType的协议信息</summary>
public class FujiCommandSettingTypeAddress : DeviceAddressDataBase
{
  /// <summary>数据的代号信息</summary>
  public byte DataCode { get; set; }

  /// <summary>地址的头信息，缓存的情况</summary>
  public string AddressHeader { get; set; }

  /// <inheritdoc />
  public override void Parse(string address, ushort length) => base.Parse(address, length);

  /// <inheritdoc />
  public override string ToString() => this.AddressHeader + this.AddressStart.ToString();

  /// <summary>
  /// 从字符串地址解析fuji的实际地址信息，如果解析成功，则 <see cref="P:HslCommunication.OperateResult.IsSuccess" /> 为 True，取 <see cref="P:HslCommunication.OperateResult`1.Content" /> 值即可。
  /// </summary>
  /// <param name="address">字符串地址</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>是否解析成功</returns>
  public static OperateResult<FujiCommandSettingTypeAddress> ParseFrom(
    string address,
    ushort length)
  {
    try
    {
      FujiCommandSettingTypeAddress settingTypeAddress = new FujiCommandSettingTypeAddress();
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string str1;
      string str2;
      if (address.IndexOf('.') < 0)
      {
        Match match = Regex.Match(address, "^[A-Z]+");
        if (!match.Success)
          return new OperateResult<FujiCommandSettingTypeAddress>(StringResources.Language.NotSupportedDataType);
        str1 = match.Value;
        str2 = address.Substring(str1.Length);
      }
      else
      {
        string[] strArray = address.Split(new char[1]{ '.' }, StringSplitOptions.RemoveEmptyEntries);
        if (strArray[0][0] != 'W')
          return new OperateResult<FujiCommandSettingTypeAddress>(StringResources.Language.NotSupportedDataType);
        str1 = strArray[0];
        str2 = strArray[1];
      }
      settingTypeAddress.AddressHeader = str1;
      settingTypeAddress.AddressStart = Convert.ToInt32(str2);
      settingTypeAddress.Length = length;
      switch (str1)
      {
        case "TS":
          settingTypeAddress.DataCode = (byte) 10;
          break;
        case "TR":
          settingTypeAddress.DataCode = (byte) 11;
          break;
        case "CS":
          settingTypeAddress.DataCode = (byte) 12;
          break;
        case "CR":
          settingTypeAddress.DataCode = (byte) 13;
          break;
        case "BD":
          settingTypeAddress.DataCode = (byte) 14;
          break;
        case "WL":
          settingTypeAddress.DataCode = (byte) 20;
          break;
        case "B":
          settingTypeAddress.DataCode = (byte) 0;
          break;
        case "M":
          settingTypeAddress.DataCode = (byte) 1;
          break;
        case "K":
          settingTypeAddress.DataCode = (byte) 2;
          break;
        case "F":
          settingTypeAddress.DataCode = (byte) 3;
          break;
        case "A":
          settingTypeAddress.DataCode = (byte) 4;
          break;
        case "D":
          settingTypeAddress.DataCode = (byte) 5;
          break;
        case "S":
          settingTypeAddress.DataCode = (byte) 8;
          break;
        default:
          int num = str1.StartsWith("W") ? Convert.ToInt32(str1.Substring(1)) : throw new Exception(StringResources.Language.NotSupportedDataType);
          if (num == 9)
          {
            settingTypeAddress.DataCode = (byte) 9;
            break;
          }
          if (num >= 21 && num <= 26)
          {
            settingTypeAddress.DataCode = (byte) num;
            break;
          }
          if (num >= 30 && num <= 109)
          {
            settingTypeAddress.DataCode = (byte) num;
            break;
          }
          if (num >= 120 && num <= 123)
          {
            settingTypeAddress.DataCode = (byte) num;
            break;
          }
          settingTypeAddress.DataCode = num == 125 ? (byte) num : throw new Exception(StringResources.Language.NotSupportedDataType);
          break;
      }
      return OperateResult.CreateSuccessResult<FujiCommandSettingTypeAddress>(settingTypeAddress);
    }
    catch (Exception ex)
    {
      return new OperateResult<FujiCommandSettingTypeAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }
}
