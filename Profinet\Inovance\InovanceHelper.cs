﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Inovance.InovanceHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.ModBus;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Inovance;

/// <summary>
/// 汇川PLC的辅助类，提供一些地址解析的方法<br />
/// Auxiliary class of Yaskawa robot, providing some methods of address resolution
/// </summary>
public class InovanceHelper
{
  private static int CalculateStartAddress(string address)
  {
    if (address.IndexOf('.') < 0)
      return int.Parse(address);
    string[] strArray = address.Split(new char[1]{ '.' }, StringSplitOptions.RemoveEmptyEntries);
    return int.Parse(strArray[0]) * 8 + int.Parse(strArray[1]);
  }

  /// <summary>
  /// 按照字节读取汇川M地址的数据，地址示例： MB100，MB101，需要注意的是，MB100 及 MB101 的地址是 MW50 的数据。<br />
  /// Read the data of Inovance M address according to the byte, address example: MB100, MB101, it should be noted that the addresses of MB100 and MB101 are the data of MW50.
  /// </summary>
  /// <param name="modbus">汇川的PLC对象</param>
  /// <param name="address">地址信息</param>
  /// <returns>读取的结果数据</returns>
  public static OperateResult<byte> ReadByte(IModbus modbus, string address)
  {
    try
    {
      int num = 0;
      if (address.StartsWith("MB") || address.StartsWith("mb"))
        num = Convert.ToInt32(address.Substring(2));
      else if (address.StartsWith("M") || address.StartsWith("m"))
      {
        num = Convert.ToInt32(address.Substring(1));
      }
      else
      {
        OperateResult<string> operateResult = new OperateResult<string>(StringResources.Language.NotSupportedDataType);
      }
      OperateResult<byte[]> result = modbus.Read("MW" + (num / 2).ToString(), (ushort) 1);
      return !result.IsSuccess ? OperateResult.CreateFailedResult<byte>((OperateResult) result) : OperateResult.CreateSuccessResult<byte>(num % 2 == 0 ? result.Content[1] : result.Content[0]);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte>("Address prase failed: " + ex.Message);
    }
  }

  internal static OperateResult<string> ReadAMString(
    IModbus modbus,
    string address,
    ushort length,
    Encoding encoding)
  {
    int int32 = Convert.ToInt32(address.Substring(address.Length - 1));
    address = address.Substring(0, address.Length - 1) + (int32 - 1).ToString();
    OperateResult<byte[]> result = modbus.Read(address, length);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    if (modbus.IsStringReverse)
      result.Content = SoftBasic.BytesReverseByWord(result.Content);
    result.Content = result.Content.RemoveBegin<byte>(1);
    return OperateResult.CreateSuccessResult<string>(encoding.GetString(result.Content));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Inovance.InovanceHelper.ReadByte(HslCommunication.ModBus.IModbus,System.String)" />
  public static async Task<OperateResult<byte>> ReadByteAsync(IModbus modbus, string address)
  {
    try
    {
      int offset = 0;
      if (address.StartsWith("MB", StringComparison.OrdinalIgnoreCase))
        offset = Convert.ToInt32(address.Substring(2));
      else if (address.StartsWith("M", StringComparison.OrdinalIgnoreCase))
      {
        offset = Convert.ToInt32(address.Substring(1));
      }
      else
      {
        OperateResult<string> operateResult = new OperateResult<string>(StringResources.Language.NotSupportedDataType);
      }
      OperateResult<byte[]> read = await modbus.ReadAsync("MW" + (offset / 2).ToString(), (ushort) 1);
      return read.IsSuccess ? OperateResult.CreateSuccessResult<byte>(offset % 2 == 0 ? read.Content[1] : read.Content[0]) : OperateResult.CreateFailedResult<byte>((OperateResult) read);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte>("Address prase failed: " + ex.Message);
    }
  }

  internal static async Task<OperateResult<string>> ReadAMStringAsync(
    IModbus modbus,
    string address,
    ushort length,
    Encoding encoding)
  {
    int index = Convert.ToInt32(address.Substring(address.Length - 1));
    address = address.Substring(0, address.Length - 1) + (index - 1).ToString();
    OperateResult<byte[]> read = await modbus.ReadAsync(address, length);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    if (modbus.IsStringReverse)
      read.Content = SoftBasic.BytesReverseByWord(read.Content);
    read.Content = read.Content.RemoveBegin<byte>(1);
    return OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content));
  }

  /// <summary>
  /// 根据汇川PLC的地址，解析出转换后的modbus协议信息，适用AM,H3U,H5U系列的PLC<br />
  /// According to the address of Inovance PLC, analyze the converted modbus protocol information, which is suitable for AM, H3U, H5U series PLC
  /// </summary>
  /// <param name="series">PLC的系列</param>
  /// <param name="address">汇川plc的地址信息</param>
  /// <param name="modbusCode">原始的对应的modbus信息</param>
  /// <returns>Modbus格式的地址</returns>
  public static OperateResult<string> PraseInovanceAddress(
    InovanceSeries series,
    string address,
    byte modbusCode)
  {
    switch (series)
    {
      case InovanceSeries.AM:
        return InovanceHelper.PraseInovanceAMAddress(address, modbusCode);
      case InovanceSeries.H3U:
        return InovanceHelper.PraseInovanceH3UAddress(address, modbusCode);
      case InovanceSeries.H5U:
        return InovanceHelper.PraseInovanceH5UAddress(address, modbusCode);
      case InovanceSeries.Easy:
        return InovanceHelper.PraseInovanceH5UAddress(address, modbusCode);
      default:
        return new OperateResult<string>($"[{series}] Not supported series of plc");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Inovance.InovanceHelper.PraseInovanceAddress(HslCommunication.Profinet.Inovance.InovanceSeries,System.String,System.Byte)" />
  public static OperateResult<string> PraseInovanceAMAddress(string address, byte modbusCode)
  {
    try
    {
      string str = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        str = $"s={parameter.Content};";
      if (address.StartsWith("QX") || address.StartsWith("qx"))
        return OperateResult.CreateSuccessResult<string>(str + InovanceHelper.CalculateStartAddress(address.Substring(2)).ToString());
      if (address.StartsWith("Q") || address.StartsWith("q"))
        return OperateResult.CreateSuccessResult<string>(str + InovanceHelper.CalculateStartAddress(address.Substring(1)).ToString());
      if (address.StartsWith("IX") || address.StartsWith("ix"))
        return OperateResult.CreateSuccessResult<string>($"{str}x=2;{InovanceHelper.CalculateStartAddress(address.Substring(2)).ToString()}");
      if (address.StartsWith("I") || address.StartsWith("i"))
        return OperateResult.CreateSuccessResult<string>($"{str}x=2;{InovanceHelper.CalculateStartAddress(address.Substring(1)).ToString()}");
      if (address.StartsWith("MW") || address.StartsWith("mw"))
        return OperateResult.CreateSuccessResult<string>(str + address.Substring(2));
      if (address.StartsWith("MD") || address.StartsWith("md"))
      {
        if ((modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5) && address.IndexOf('.') > 0)
          return OperateResult.CreateSuccessResult<string>(str + address);
        int num = Convert.ToInt32(address.Substring(2)) * 2;
        return OperateResult.CreateSuccessResult<string>(str + num.ToString());
      }
      if (address.StartsWith("MB") || address.StartsWith("mb"))
      {
        int int32 = Convert.ToInt32(address.Substring(2));
        return int32 % 2 == 1 ? new OperateResult<string>($"Address[{address}] {StringResources.Language.AddressOffsetEven}") : OperateResult.CreateSuccessResult<string>(str + (int32 / 2).ToString());
      }
      if (address.StartsWith("MX") || address.StartsWith("mx"))
      {
        if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
        {
          if (address.IndexOf('.') > 0)
          {
            string[] strArray = address.Substring(2).SplitDot();
            int int32_1 = Convert.ToInt32(strArray[0]);
            int int32_2 = Convert.ToInt32(strArray[1]);
            return OperateResult.CreateSuccessResult<string>($"{str}{(int32_1 / 2).ToString()}.{(int32_1 % 2 * 8 + int32_2).ToString()}");
          }
          int int32 = Convert.ToInt32(address.Substring(2));
          return OperateResult.CreateSuccessResult<string>($"{str}{(int32 / 2).ToString()}.0");
        }
        int int32_3 = Convert.ToInt32(address.Substring(2));
        return OperateResult.CreateSuccessResult<string>(str + (int32_3 / 2).ToString());
      }
      if (address.StartsWith("M") || address.StartsWith("m"))
        return OperateResult.CreateSuccessResult<string>(str + address.Substring(1));
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWith("SMX") || address.StartsWith("smx"))
          return OperateResult.CreateSuccessResult<string>(str + $"x={(int) modbusCode + 48 /*0x30*/};" + InovanceHelper.CalculateStartAddress(address.Substring(3)).ToString());
        if (address.StartsWith("SM") || address.StartsWith("sm"))
          return OperateResult.CreateSuccessResult<string>(str + $"x={(int) modbusCode + 48 /*0x30*/};" + InovanceHelper.CalculateStartAddress(address.Substring(2)).ToString());
      }
      else
      {
        if (address.StartsWith("SDW") || address.StartsWith("sdw"))
          return OperateResult.CreateSuccessResult<string>(str + $"x={(int) modbusCode + 48 /*0x30*/};" + address.Substring(3));
        if (address.StartsWith("SD") || address.StartsWith("sd"))
          return OperateResult.CreateSuccessResult<string>(str + $"x={(int) modbusCode + 48 /*0x30*/};" + address.Substring(2));
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  private static int CalculateH3UStartAddress(string address)
  {
    if (address.IndexOf('.') < 0)
      return Convert.ToInt32(address, 8);
    string[] strArray = address.Split(new char[1]{ '.' }, StringSplitOptions.RemoveEmptyEntries);
    return Convert.ToInt32(strArray[0], 8) * 8 + int.Parse(strArray[1]);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Inovance.InovanceHelper.PraseInovanceAddress(HslCommunication.Profinet.Inovance.InovanceSeries,System.String,System.Byte)" />
  public static OperateResult<string> PraseInovanceH3UAddress(string address, byte modbusCode)
  {
    try
    {
      string station = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        station = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        string newAddress1;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[2]
        {
          "X",
          "Y"
        }, new int[2]{ 63488, 64512 }, new Func<string, int>(InovanceHelper.CalculateH3UStartAddress), out newAddress1))
          return OperateResult.CreateSuccessResult<string>(newAddress1);
        string newAddress2;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[4]
        {
          "SM",
          "S",
          "T",
          "C"
        }, new int[4]
        {
          9216,
          57344 /*0xE000*/,
          61440 /*0xF000*/,
          62464
        }, out newAddress2))
          return OperateResult.CreateSuccessResult<string>(newAddress2);
        if (address.StartsWith("M") || address.StartsWith("m"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 >= 8000 ? OperateResult.CreateSuccessResult<string>(station + (int32 - 8000 + 8000).ToString()) : OperateResult.CreateSuccessResult<string>(station + int32.ToString());
        }
        string newAddress3;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[3]
        {
          "D",
          "SD",
          "R"
        }, new int[3]{ 0, 9216, 12288 /*0x3000*/ }, out newAddress3))
          return OperateResult.CreateSuccessResult<string>(newAddress3);
      }
      else
      {
        string newAddress;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[4]
        {
          "D",
          "SD",
          "R",
          "T"
        }, new int[4]
        {
          0,
          9216,
          12288 /*0x3000*/,
          61440 /*0xF000*/
        }, out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
        if (address.StartsWith("C", StringComparison.InvariantCultureIgnoreCase))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 >= 200 ? OperateResult.CreateSuccessResult<string>(station + ((int32 - 200) * 2 + 63232).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 + 62464).ToString());
        }
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Inovance.InovanceHelper.PraseInovanceAddress(HslCommunication.Profinet.Inovance.InovanceSeries,System.String,System.Byte)" />
  public static OperateResult<string> PraseInovanceH5UAddress(string address, byte modbusCode)
  {
    try
    {
      string station = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        station = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        string newAddress1;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[2]
        {
          "X",
          "Y"
        }, new int[2]{ 63488, 64512 }, new Func<string, int>(InovanceHelper.CalculateH3UStartAddress), out newAddress1))
          return OperateResult.CreateSuccessResult<string>(newAddress1);
        string newAddress2;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[3]
        {
          "S",
          "B",
          "M"
        }, new int[3]
        {
          57344 /*0xE000*/,
          12288 /*0x3000*/,
          0
        }, out newAddress2))
          return OperateResult.CreateSuccessResult<string>(newAddress2);
        string newAddress3;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[2]
        {
          "D",
          "R"
        }, new int[2]{ 0, 12288 /*0x3000*/ }, out newAddress3))
          return OperateResult.CreateSuccessResult<string>(newAddress3);
      }
      else
      {
        string newAddress;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[2]
        {
          "D",
          "R"
        }, new int[2]{ 0, 12288 /*0x3000*/ }, out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }
}
