﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Types.CertificateDegree
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.Types;

/// <summary>证书等级</summary>
public enum CertificateDegree
{
  /// <summary>只允许读取数据的等级</summary>
  Read = 1,
  /// <summary>允许同时读写数据的等级</summary>
  ReadWrite = 2,
}
