﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.CJT.Helper.ICjt188
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Instrument.CJT.Helper;

/// <summary>CJT188设备的接口</summary>
public interface ICjt188 : IReadWriteDevice, IReadWriteNet
{
  /// <summary>仪表的类型</summary>
  byte InstrumentType { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.Station" />
  string Station { get; set; }

  /// <summary>
  /// 获取或设置是否在每一次的报文通信时，增加"FE FE"的命令头<br />
  /// Get or set whether to add the command header of "FE FE" in each message communication
  /// </summary>
  bool EnableCodeFE { get; set; }

  /// <summary>
  /// 激活设备的命令，只发送数据到设备，不等待设备数据返回<br />
  /// The command to activate the device, only send data to the device, do not wait for the device data to return
  /// </summary>
  /// <returns>是否发送成功</returns>
  OperateResult ActiveDeveice();

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.WriteAddress(HslCommunication.Instrument.CJT.Helper.ICjt188,System.String)" />
  OperateResult WriteAddress(string address);

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.ReadAddress(HslCommunication.Instrument.CJT.Helper.ICjt188)" />
  OperateResult<string> ReadAddress();

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.ReadStringArray(HslCommunication.Instrument.CJT.Helper.ICjt188,System.String)" />
  OperateResult<string[]> ReadStringArray(string address);
}
