﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.DeviceAddressDataBase
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>
/// 设备地址数据的信息，通常包含起始地址，数据类型，长度<br />
/// Device address data information, usually including the starting address, data type, length
/// </summary>
public class DeviceAddressDataBase
{
  /// <summary>
  /// 数字的起始地址，也就是偏移地址<br />
  /// The starting address of the number, which is the offset address
  /// </summary>
  public int AddressStart { get; set; }

  /// <summary>
  /// 读取的数据长度，单位是字节还是字取决于设备方<br />
  /// The length of the data read, the unit is byte or word depends on the device side
  /// </summary>
  public ushort Length { get; set; }

  /// <summary>
  /// 从指定的地址信息解析成真正的设备地址信息<br />
  /// Parsing from the specified address information into real device address information
  /// </summary>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  public virtual void Parse(string address, ushort length)
  {
    this.AddressStart = int.Parse(address);
    this.Length = length;
  }

  /// <inheritdoc />
  public override string ToString() => this.AddressStart.ToString();

  /// <summary>
  /// 获取地址不支持的描述信息<br />
  /// Get the description that the address is not supported
  /// </summary>
  /// <param name="address">地址信息</param>
  /// <param name="ex">解析地址时候的异常信息</param>
  /// <returns>地址不支持的信息</returns>
  public static string GetUnsupportedAddressInfo(string address, Exception ex = null)
  {
    return ex == null ? $"Address[{address}]: {StringResources.Language.NotSupportedDataType}" : $"Parse [{address}] failed: {ex.Message}";
  }
}
