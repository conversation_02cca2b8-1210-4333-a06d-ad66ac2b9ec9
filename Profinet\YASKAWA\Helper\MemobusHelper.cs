﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.YASKAWA.Helper;

/// <summary>Memobus的辅助类对象</summary>
public class MemobusHelper
{
  internal static byte[] PackCommandWithHeader(byte[] command, long id)
  {
    byte[] numArray = new byte[12 + command.Length];
    numArray[0] = (byte) 17;
    numArray[1] = (byte) id;
    numArray[2] = (byte) 0;
    numArray[3] = (byte) 0;
    numArray[6] = BitConverter.GetBytes(numArray.Length)[0];
    numArray[7] = BitConverter.GetBytes(numArray.Length)[1];
    command.CopyTo((Array) numArray, 12);
    return numArray;
  }

  internal static string GetErrorText(byte err)
  {
    switch (err)
    {
      case 1:
        return StringResources.Language.Memobus01;
      case 2:
        return StringResources.Language.Memobus02;
      case 3:
        return StringResources.Language.Memobus03;
      case 64 /*0x40*/:
        return StringResources.Language.Memobus40;
      case 65:
        return StringResources.Language.Memobus41;
      case 66:
        return StringResources.Language.Memobus42;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  internal static OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    try
    {
      if (send.Length > 15 && response.Length > 15)
      {
        if ((int) send[15] + 128 /*0x80*/ == (int) response[15] && response.Length >= 18)
          return new OperateResult<byte[]>((int) response[17], $"{MemobusHelper.GetErrorText(response[17])} Source: {response.ToHexString(' ')}");
        if ((int) send[15] != (int) response[15])
          return new OperateResult<byte[]>((int) response[15], "Send SFC not same as back SFC:" + response.ToHexString());
      }
      return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(12));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"UnpackResponseContent failed: {ex.Message}  Source: {response.ToHexString(' ')}");
    }
  }

  private static void SetByteHead(byte[] buffer, byte mfc, byte sfc, byte cpuTo, byte cpuFrom)
  {
    buffer[0] = BitConverter.GetBytes(buffer.Length - 2)[0];
    buffer[1] = BitConverter.GetBytes(buffer.Length - 2)[1];
    buffer[2] = mfc;
    buffer[3] = sfc;
    buffer[4] = (byte) (((uint) cpuTo << 4) + (uint) cpuFrom);
  }

  internal static byte GetAddressDataType(string address)
  {
    byte addressDataType = 77;
    if (address[0] == 'M' || address[0] == 'm')
      addressDataType = (byte) 77;
    else if (address[0] == 'G' || address[0] == 'g')
      addressDataType = (byte) 71;
    else if (address[0] == 'I' || address[0] == 'i')
      addressDataType = (byte) 73;
    else if (address[0] == 'O' || address[0] == 'o')
      addressDataType = (byte) 79;
    else if (address[0] == 'S' || address[0] == 's')
      addressDataType = (byte) 83;
    return addressDataType;
  }

  private static int CalculateBoolIndex(string address)
  {
    address = address[1] != 'B' && address[1] != 'b' ? address.Substring(1) : address.Substring(2);
    int length = address.IndexOf('.');
    return length > 0 ? Convert.ToInt32(address.Substring(0, length)) * 16 /*0x10*/ + HslHelper.CalculateBitStartIndex(address.Substring(length + 1)) : Convert.ToInt32(address.Substring(0, address.Length - 1)) * 16 /*0x10*/ + HslHelper.CalculateBitStartIndex(address.Substring(address.Length - 1));
  }

  /// <summary>构建读取的命令报文，支持功能码 01,02,03,04,09,0A</summary>
  /// <param name="mfc">主功能码</param>
  /// <param name="sfc">子功能码</param>
  /// <param name="cpuTo">目标的CPU编号</param>
  /// <param name="cpuFrom">发送源CPU编号</param>
  /// <param name="address">起始地址</param>
  /// <param name="length">读取地址长度</param>
  /// <returns>结果报文信息</returns>
  internal static OperateResult<byte[]> BuildReadCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    string address,
    ushort length)
  {
    if (address.StartsWith(new string[5]
    {
      "M",
      "G",
      "I",
      "O",
      "S"
    }))
    {
      byte addressDataType = MemobusHelper.GetAddressDataType(address);
      if (address[1] == 'B' || address[1] == 'b' || address.IndexOf('.') > 0)
      {
        int boolIndex = MemobusHelper.CalculateBoolIndex(address);
        byte[] buffer = new byte[16 /*0x10*/];
        MemobusHelper.SetByteHead(buffer, (byte) 67, (byte) 65, cpuTo, cpuFrom);
        buffer[6] = addressDataType;
        BitConverter.GetBytes(boolIndex).CopyTo((Array) buffer, 8);
        BitConverter.GetBytes(length).CopyTo((Array) buffer, 12);
        return OperateResult.CreateSuccessResult<byte[]>(buffer);
      }
      byte[] buffer1 = new byte[14];
      MemobusHelper.SetByteHead(buffer1, (byte) 67, (byte) 73, cpuTo, cpuFrom);
      buffer1[6] = addressDataType;
      BitConverter.GetBytes(Convert.ToUInt32(address.Substring(1))).CopyTo((Array) buffer1, 8);
      BitConverter.GetBytes(length).CopyTo((Array) buffer1, 12);
      return OperateResult.CreateSuccessResult<byte[]>(buffer1);
    }
    ushort result;
    if (!ushort.TryParse(address, out result))
      return new OperateResult<byte[]>($"Address[{address}] wrong, not supported");
    if (sfc == (byte) 1 || sfc == (byte) 2 || sfc == (byte) 3 || sfc == (byte) 4)
    {
      byte[] buffer = new byte[9];
      MemobusHelper.SetByteHead(buffer, mfc, sfc, cpuTo, cpuFrom);
      buffer[5] = BitConverter.GetBytes(result)[1];
      buffer[6] = BitConverter.GetBytes(result)[0];
      buffer[7] = BitConverter.GetBytes(length)[1];
      buffer[8] = BitConverter.GetBytes(length)[0];
      return OperateResult.CreateSuccessResult<byte[]>(buffer);
    }
    if (sfc != (byte) 9 && sfc != (byte) 10)
      return new OperateResult<byte[]>($"SFC:{sfc} {StringResources.Language.NotSupportedFunction}");
    byte[] buffer2 = new byte[10];
    MemobusHelper.SetByteHead(buffer2, mfc, sfc, cpuTo, cpuFrom);
    buffer2[6] = BitConverter.GetBytes(result)[0];
    buffer2[7] = BitConverter.GetBytes(result)[1];
    buffer2[8] = BitConverter.GetBytes(length)[0];
    buffer2[9] = BitConverter.GetBytes(length)[1];
    return OperateResult.CreateSuccessResult<byte[]>(buffer2);
  }

  internal static OperateResult<byte[]> BuildReadRandomCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    ushort[] address)
  {
    byte[] buffer = new byte[8 + address.Length * 2];
    MemobusHelper.SetByteHead(buffer, mfc, sfc, cpuTo, cpuFrom);
    buffer[6] = BitConverter.GetBytes(address.Length)[0];
    buffer[7] = BitConverter.GetBytes(address.Length)[1];
    for (int index = 0; index < address.Length; ++index)
    {
      buffer[8 + index * 2] = BitConverter.GetBytes(address[index])[0];
      buffer[8 + index * 2 + 1] = BitConverter.GetBytes(address[index])[1];
    }
    return OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  internal static OperateResult<byte[]> BuildReadRandomCommand(
    byte cpuTo,
    byte cpuFrom,
    string[] address)
  {
    byte[] buffer = new byte[8 + address.Length * 6];
    MemobusHelper.SetByteHead(buffer, (byte) 67, (byte) 77, cpuTo, cpuFrom);
    buffer[6] = BitConverter.GetBytes(address.Length)[0];
    buffer[7] = BitConverter.GetBytes(address.Length)[1];
    for (int index = 0; index < address.Length; ++index)
    {
      byte addressDataType = MemobusHelper.GetAddressDataType(address[index]);
      buffer[8 + index * 6] = addressDataType;
      buffer[8 + index * 6 + 1] = (byte) 2;
      BitConverter.GetBytes(Convert.ToUInt32(address[index].Substring(1))).CopyTo((Array) buffer, 8 + index * 6 + 2);
    }
    return OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  /// <summary>构建写入单一的线圈的状态变更的报文</summary>
  /// <param name="mfc">主功能码</param>
  /// <param name="sfc">子功能码</param>
  /// <param name="cpuTo">目标的CPU编号</param>
  /// <param name="cpuFrom">发送源CPU编号</param>
  /// <param name="address">起始地址</param>
  /// <param name="value">写入的通断值信息</param>
  /// <returns>写入的报文</returns>
  internal static OperateResult<byte[]> BuildWriteCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    ushort address,
    bool value)
  {
    byte[] buffer = new byte[9];
    MemobusHelper.SetByteHead(buffer, mfc, sfc, cpuTo, cpuFrom);
    buffer[5] = BitConverter.GetBytes(address)[1];
    buffer[6] = BitConverter.GetBytes(address)[0];
    buffer[7] = value ? byte.MaxValue : (byte) 0;
    buffer[8] = (byte) 0;
    return OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  internal static OperateResult<byte[]> BuildWriteCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    string address,
    bool[] value)
  {
    if (address.StartsWith(new string[5]
    {
      "M",
      "G",
      "I",
      "O",
      "S"
    }))
    {
      byte addressDataType = MemobusHelper.GetAddressDataType(address);
      if (address[1] != 'B' && address[1] != 'b' && address.IndexOf('.') <= 0)
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      int boolIndex = MemobusHelper.CalculateBoolIndex(address);
      byte[] buffer = new byte[16 /*0x10*/ + (value.Length + 7) / 8];
      MemobusHelper.SetByteHead(buffer, (byte) 67, (byte) 79, cpuTo, cpuFrom);
      buffer[6] = addressDataType;
      BitConverter.GetBytes(boolIndex).CopyTo((Array) buffer, 8);
      BitConverter.GetBytes(value.Length).CopyTo((Array) buffer, 12);
      value.ToByteArray().CopyTo((Array) buffer, 16 /*0x10*/);
      return OperateResult.CreateSuccessResult<byte[]>(buffer);
    }
    ushort result;
    if (!ushort.TryParse(address, out result))
      return new OperateResult<byte[]>($"Address[{address}] wrong, not supported");
    byte[] numArray = SoftBasic.BoolArrayToByte(value);
    byte[] buffer1 = new byte[9 + numArray.Length];
    MemobusHelper.SetByteHead(buffer1, mfc, sfc, cpuTo, cpuFrom);
    buffer1[5] = BitConverter.GetBytes(result)[1];
    buffer1[6] = BitConverter.GetBytes(result)[0];
    buffer1[7] = BitConverter.GetBytes(value.Length)[1];
    buffer1[8] = BitConverter.GetBytes(value.Length)[0];
    numArray.CopyTo((Array) buffer1, 9);
    return OperateResult.CreateSuccessResult<byte[]>(buffer1);
  }

  internal static OperateResult<byte[]> BuildWriteCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    ushort address,
    short value)
  {
    byte[] buffer = new byte[9];
    MemobusHelper.SetByteHead(buffer, mfc, sfc, cpuTo, cpuFrom);
    buffer[5] = BitConverter.GetBytes(address)[1];
    buffer[6] = BitConverter.GetBytes(address)[0];
    buffer[7] = BitConverter.GetBytes(value)[1];
    buffer[8] = BitConverter.GetBytes(value)[0];
    return OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  internal static OperateResult<byte[]> BuildWriteCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    ushort address,
    ushort value)
  {
    byte[] buffer = new byte[9];
    MemobusHelper.SetByteHead(buffer, mfc, sfc, cpuTo, cpuFrom);
    buffer[5] = BitConverter.GetBytes(address)[1];
    buffer[6] = BitConverter.GetBytes(address)[0];
    buffer[7] = BitConverter.GetBytes(value)[1];
    buffer[8] = BitConverter.GetBytes(value)[0];
    return OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  internal static OperateResult<byte[]> BuildWriteCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    string address,
    byte[] value)
  {
    if (address.StartsWithAndNumber(new string[5]
    {
      "M",
      "G",
      "I",
      "O",
      "S"
    }))
    {
      byte addressDataType = MemobusHelper.GetAddressDataType(address);
      byte[] buffer = new byte[14 + value.Length];
      MemobusHelper.SetByteHead(buffer, (byte) 67, (byte) 75, cpuTo, cpuFrom);
      buffer[6] = addressDataType;
      BitConverter.GetBytes(Convert.ToUInt32(address.Substring(1))).CopyTo((Array) buffer, 8);
      BitConverter.GetBytes(value.Length / 2).CopyTo((Array) buffer, 12);
      SoftBasic.BytesReverseByWord(value).CopyTo((Array) buffer, 14);
      return OperateResult.CreateSuccessResult<byte[]>(buffer);
    }
    ushort result;
    if (!ushort.TryParse(address, out result))
      return new OperateResult<byte[]>($"Address[{address}] wrong, not supported");
    if (sfc == (byte) 11)
    {
      byte[] buffer = new byte[10 + value.Length];
      MemobusHelper.SetByteHead(buffer, mfc, sfc, cpuTo, cpuFrom);
      buffer[6] = BitConverter.GetBytes(result)[0];
      buffer[7] = BitConverter.GetBytes(result)[1];
      buffer[8] = BitConverter.GetBytes(value.Length / 2)[0];
      buffer[9] = BitConverter.GetBytes(value.Length / 2)[1];
      SoftBasic.BytesReverseByWord(value).CopyTo((Array) buffer, 10);
      return OperateResult.CreateSuccessResult<byte[]>(buffer);
    }
    if (sfc != (byte) 16 /*0x10*/)
      return new OperateResult<byte[]>($"SFC:{sfc} {StringResources.Language.NotSupportedFunction}");
    byte[] buffer1 = new byte[9 + value.Length];
    MemobusHelper.SetByteHead(buffer1, mfc, sfc, cpuTo, cpuFrom);
    buffer1[5] = BitConverter.GetBytes(result)[1];
    buffer1[6] = BitConverter.GetBytes(result)[0];
    buffer1[7] = BitConverter.GetBytes(value.Length / 2)[1];
    buffer1[8] = BitConverter.GetBytes(value.Length / 2)[0];
    value.CopyTo((Array) buffer1, 9);
    return OperateResult.CreateSuccessResult<byte[]>(buffer1);
  }

  internal static OperateResult<byte[]> BuildWriteRandomCommand(
    byte mfc,
    byte sfc,
    byte cpuTo,
    byte cpuFrom,
    ushort[] address,
    byte[] value)
  {
    if (value.Length != address.Length * 2)
      return new OperateResult<byte[]>("value.Length must be twice as much as address.Length");
    byte[] buffer = new byte[8 + address.Length * 4];
    MemobusHelper.SetByteHead(buffer, mfc, sfc, cpuTo, cpuFrom);
    buffer[6] = BitConverter.GetBytes(address.Length)[0];
    buffer[7] = BitConverter.GetBytes(address.Length)[1];
    for (int index = 0; index < address.Length; ++index)
    {
      buffer[8 + index * 4] = BitConverter.GetBytes(address[index])[0];
      buffer[8 + index * 4 + 1] = BitConverter.GetBytes(address[index])[1];
      buffer[8 + index * 4 + 2] = value[index * 2 + 1];
      buffer[8 + index * 4 + 3] = value[index * 2];
    }
    return OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  /// <remarks>
  /// 默认使用功能码01，读取线圈操作，如果需要指定读取输入线圈，地址需要携带额外的参数，例如 x=2;100<br />
  /// The function code 01 is used by default to read the coil operation. If you need to specify the read input coil, the address needs to carry additional parameters, such as x=2;100
  /// </remarks>
  public static OperateResult<bool[]> ReadBool(IMemobus memobus, string address, ushort length)
  {
    OperateResult<byte[]> result1 = MemobusHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/), (byte) HslHelper.ExtractParameter(ref address, "x", 1), memobus.CpuTo, memobus.CpuFrom, address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = memobus.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
    return result2.Content[3] == (byte) 65 ? OperateResult.CreateSuccessResult<bool[]>(result2.Content.RemoveBegin<byte>(8).ToBoolArray().SelectBegin<bool>((int) length)) : OperateResult.CreateSuccessResult<bool[]>(result2.Content.RemoveBegin<byte>(5).ToBoolArray().SelectBegin<bool>((int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean)" />
  /// <remarks>
  /// 单一线圈的状态变更，使用的主功能码为0x20, 子功能码为0x05<br />
  /// The status of a single coil is changed, the main function code used is 0x20, and the sub function code is 0x05
  /// </remarks>
  public static OperateResult Write(IMemobus memobus, string address, bool value)
  {
    if (address.StartsWith(new string[5]
    {
      "M",
      "G",
      "I",
      "O",
      "S"
    }))
      return MemobusHelper.Write(memobus, address, new bool[1]
      {
        value
      });
    OperateResult<byte[]> operateResult = MemobusHelper.BuildWriteCommand((byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/), (byte) HslHelper.ExtractParameter(ref address, "x", 5), memobus.CpuTo, memobus.CpuFrom, ushort.Parse(address), value);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  /// <remarks>
  /// 多个线圈的状态更改，默认使用的是 0x0f 子功能码。<br />
  /// The status of multiple coils is changed, and the sub-function code 0x0f is used by default.
  /// </remarks>
  public static OperateResult Write(IMemobus memobus, string address, bool[] value)
  {
    OperateResult<byte[]> operateResult = MemobusHelper.BuildWriteCommand((byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/), (byte) HslHelper.ExtractParameter(ref address, "x", 15), memobus.CpuTo, memobus.CpuFrom, address, value);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult();
  }

  private static OperateResult<byte[]> ExtraContent(string address, byte[] content)
  {
    if (content[2] == (byte) 32 /*0x20*/)
    {
      if (content[3] == (byte) 3 || content[3] == (byte) 4)
        return OperateResult.CreateSuccessResult<byte[]>(content.RemoveBegin<byte>(5));
      return content[3] == (byte) 9 || content[3] == (byte) 10 ? OperateResult.CreateSuccessResult<byte[]>(SoftBasic.BytesReverseByWord(content.RemoveBegin<byte>(8))) : OperateResult.CreateSuccessResult<byte[]>(content.RemoveBegin<byte>(5));
    }
    if (content[2] != (byte) 67)
      return new OperateResult<byte[]>($"[{address}], mfc[{content[2]}] is not supported");
    return content[3] == (byte) 73 || content[3] == (byte) 77 ? OperateResult.CreateSuccessResult<byte[]>(SoftBasic.BytesReverseByWord(content.RemoveBegin<byte>(10))) : OperateResult.CreateSuccessResult<byte[]>(content.RemoveBegin<byte>(8));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read(System.String,System.UInt16)" />
  /// <remarks>
  /// 地址默认使用功能码03，如果需要指定其他的功能码地址，需要手动指定功能码，例如：x=4;100, x=9;100, x=10;100, 当然也可以写成 x=0x0A;100<br />
  /// The address uses function code 03 by default. If you need to specify other function code addresses,
  /// you need to manually specify the function code, for example: x=4;100, x=9;100, x=10;100, of course, it can also be written as x=0x0A; 100
  /// </remarks>
  public static OperateResult<byte[]> Read(IMemobus memobus, string address, ushort length)
  {
    OperateResult<byte[]> operateResult = MemobusHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/), (byte) HslHelper.ExtractParameter(ref address, "x", 3), memobus.CpuTo, memobus.CpuFrom, address, length);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : MemobusHelper.ExtraContent(address, result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Byte[])" />
  /// <remarks>
  /// 连续的寄存器写入操作，默认功能码是0x10，如果需要写入扩展的寄存器，使用 x=0xA;100 或是 x=10;100 即可。<br />
  /// For continuous register write operation, the default function code is 0x10. If you need to write an extended register, use x=0xA;100 or x=10;100.
  /// </remarks>
  public static OperateResult Write(IMemobus memobus, string address, byte[] value)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 16 /*0x10*/);
    if (sfc == (byte) 3)
      sfc = (byte) 16 /*0x10*/;
    if (sfc == (byte) 9)
      sfc = (byte) 11;
    OperateResult<byte[]> operateResult = MemobusHelper.BuildWriteCommand(parameter, sfc, memobus.CpuTo, memobus.CpuFrom, address, value);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int16)" />
  /// <remarks>
  /// 单一保持寄存器的值变更，使用的主功能码为0x20, 默认子功能码为0x06，也可以写入扩展的保持型寄存器，子功能码为0x0B<br />
  /// The value of a single hold register is changed, using a primary function code of 0x20 and a default subfunction code of 0x06, or an extended holding register with a subfunction code of 0x0B
  /// </remarks>
  public static OperateResult Write(
    IMemobus memobus,
    string address,
    short value,
    Func<string, short, OperateResult> writeShort)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 6);
    if (sfc == (byte) 11 || sfc == (byte) 9)
      return writeShort($"x={sfc};{address}", value);
    if (sfc == (byte) 3)
      sfc = (byte) 6;
    OperateResult<byte[]> operateResult = MemobusHelper.BuildWriteCommand(parameter, sfc, memobus.CpuTo, memobus.CpuFrom, ushort.Parse(address), value);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt16)" />
  /// <remarks>
  /// 单一保持寄存器的值变更，使用的主功能码为0x20, 默认子功能码为0x06<br />
  /// The value of a single hold register changes, using a primary function code of 0x20 and a default subfunction code of 0x06
  /// </remarks>
  public static OperateResult Write(
    IMemobus memobus,
    string address,
    ushort value,
    Func<string, ushort, OperateResult> writeUShort)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 6);
    if (sfc == (byte) 11 || sfc == (byte) 9)
      return writeUShort($"x={sfc};{address}", value);
    if (sfc == (byte) 3)
      sfc = (byte) 6;
    OperateResult<byte[]> operateResult = MemobusHelper.BuildWriteCommand(parameter, sfc, memobus.CpuTo, memobus.CpuFrom, ushort.Parse(address), value);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 随机读取扩展的保持寄存器的内容，也即读取不连续地址的字数据，可以指定多个地址，然后一次性读取所有的数据，然后解析出实际的数据<br />
  /// Randomly read the contents of the extended hold register, that is, read word data of discontinuous addresses,
  /// you can specify multiple addresses, then read all the data at once, and then parse out the actual data
  /// </summary>
  /// <remarks>注意，本方法只能针对扩展的保持寄存器进行读取</remarks>
  /// <param name="memobus">PLC通信对象</param>
  /// <param name="address">地址信息</param>
  /// <returns>读取的原始字节结果信息</returns>
  public static OperateResult<byte[]> ReadRandom(IMemobus memobus, ushort[] address)
  {
    OperateResult<byte[]> operateResult = MemobusHelper.BuildReadRandomCommand((byte) 32 /*0x20*/, (byte) 13, memobus.CpuTo, memobus.CpuFrom, address);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(SoftBasic.BytesReverseByWord(result.Content.RemoveBegin<byte>(8)));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.ReadRandom(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.UInt16[])" />
  /// <remarks>
  /// 本方法的地址支持 保持寄存器 M100, 数据寄存器 G100, 输入寄存器 I100, 输出寄存器 O100, 系统寄存器 S100,
  /// </remarks>
  public static OperateResult<byte[]> ReadRandom(IMemobus memobus, string[] address)
  {
    OperateResult<byte[]> operateResult = MemobusHelper.BuildReadRandomCommand(memobus.CpuTo, memobus.CpuFrom, address);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(SoftBasic.BytesReverseByWord(result.Content.RemoveBegin<byte>(8)));
  }

  /// <summary>
  /// 随机写入扩展的保持寄存器的内容，也即写入不连续的地址的字数据，字节数组的长度必须为地址数组长度的两倍，才能正确写入。<br />
  /// Write the contents of the extended hold registers randomly, that is, write word data for discontinuous addresses,
  /// and the byte array must be twice the length of the address array to be written correctly.
  /// </summary>
  /// <remarks>注意，本方法只能针对扩展的保持寄存器进行读取</remarks>
  /// <param name="memobus">PLC通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">数据信息</param>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult WriteRandom(IMemobus memobus, ushort[] address, byte[] value)
  {
    OperateResult<byte[]> operateResult = MemobusHelper.BuildWriteRandomCommand((byte) 32 /*0x20*/, (byte) 14, memobus.CpuTo, memobus.CpuFrom, address, value);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult<byte[]> result = memobus.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.ReadBool(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IMemobus memobus,
    string address,
    ushort length)
  {
    byte mfc = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 1);
    OperateResult<byte[]> command = MemobusHelper.BuildReadCommand(mfc, sfc, memobus.CpuTo, memobus.CpuFrom, address, length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? (read.Content[3] != (byte) 65 ? OperateResult.CreateSuccessResult<bool[]>(read.Content.RemoveBegin<byte>(5).ToBoolArray().SelectBegin<bool>((int) length)) : OperateResult.CreateSuccessResult<bool[]>(read.Content.RemoveBegin<byte>(8).ToBoolArray().SelectBegin<bool>((int) length))) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.Write(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(IMemobus memobus, string address, bool value)
  {
    if (address.StartsWith(new string[5]
    {
      "M",
      "G",
      "I",
      "O",
      "S"
    }))
    {
      OperateResult operateResult = await MemobusHelper.WriteAsync(memobus, address, new bool[1]
      {
        value
      }).ConfigureAwait(false);
      return operateResult;
    }
    byte mfc = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 5);
    OperateResult<byte[]> command = MemobusHelper.BuildWriteCommand(mfc, sfc, memobus.CpuTo, memobus.CpuFrom, ushort.Parse(address), value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.Write(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IMemobus memobus,
    string address,
    bool[] value)
  {
    byte mfc = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 15);
    OperateResult<byte[]> command = MemobusHelper.BuildWriteCommand(mfc, sfc, memobus.CpuTo, memobus.CpuFrom, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.Read(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IMemobus memobus,
    string address,
    ushort length)
  {
    byte mfc = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 3);
    OperateResult<byte[]> command = MemobusHelper.BuildReadCommand(mfc, sfc, memobus.CpuTo, memobus.CpuFrom, address, length);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? MemobusHelper.ExtraContent(address, read.Content) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.Write(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IMemobus memobus,
    string address,
    byte[] value)
  {
    byte mfc = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 16 /*0x10*/);
    if (sfc == (byte) 3)
      sfc = (byte) 16 /*0x10*/;
    if (sfc == (byte) 9)
      sfc = (byte) 11;
    OperateResult<byte[]> command = MemobusHelper.BuildWriteCommand(mfc, sfc, memobus.CpuTo, memobus.CpuFrom, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.Write(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.String,System.Int16,System.Func{System.String,System.Int16,HslCommunication.OperateResult})" />
  public static async Task<OperateResult> WriteAsync(
    IMemobus memobus,
    string address,
    short value,
    Func<string, short, Task<OperateResult>> writeShort)
  {
    byte mfc = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 6);
    if (sfc == (byte) 11 || sfc == (byte) 9)
    {
      OperateResult operateResult = await writeShort($"x={sfc};{address}", value);
      return operateResult;
    }
    if (sfc == (byte) 3)
      sfc = (byte) 6;
    OperateResult<byte[]> command = MemobusHelper.BuildWriteCommand(mfc, sfc, memobus.CpuTo, memobus.CpuFrom, ushort.Parse(address), value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.Write(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.String,System.UInt16,System.Func{System.String,System.UInt16,HslCommunication.OperateResult})" />
  public static async Task<OperateResult> WriteAsync(
    IMemobus memobus,
    string address,
    ushort value,
    Func<string, ushort, Task<OperateResult>> writeUShort)
  {
    byte mfc = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
    byte sfc = (byte) HslHelper.ExtractParameter(ref address, "x", 6);
    if (sfc == (byte) 11 || sfc == (byte) 9)
    {
      OperateResult operateResult = await writeUShort($"x={sfc};{address}", value);
      return operateResult;
    }
    if (sfc == (byte) 3)
      sfc = (byte) 6;
    OperateResult<byte[]> command = MemobusHelper.BuildWriteCommand(mfc, sfc, memobus.CpuTo, memobus.CpuFrom, ushort.Parse(address), value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.ReadRandom(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.UInt16[])" />
  public static async Task<OperateResult<byte[]>> ReadRandomAsync(
    IMemobus memobus,
    ushort[] address)
  {
    OperateResult<byte[]> command = MemobusHelper.BuildReadRandomCommand((byte) 32 /*0x20*/, (byte) 13, memobus.CpuTo, memobus.CpuFrom, address);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(SoftBasic.BytesReverseByWord(read.Content.RemoveBegin<byte>(8))) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.ReadRandom(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.UInt16[])" />
  public static async Task<OperateResult<byte[]>> ReadRandomAsync(
    IMemobus memobus,
    string[] address)
  {
    OperateResult<byte[]> command = MemobusHelper.BuildReadRandomCommand(memobus.CpuTo, memobus.CpuFrom, address);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(SoftBasic.BytesReverseByWord(read.Content.RemoveBegin<byte>(8))) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.Helper.MemobusHelper.WriteRandom(HslCommunication.Profinet.YASKAWA.Helper.IMemobus,System.UInt16[],System.Byte[])" />
  public static async Task<OperateResult> WriteRandomAsync(
    IMemobus memobus,
    ushort[] address,
    byte[] value)
  {
    OperateResult<byte[]> command = MemobusHelper.BuildWriteRandomCommand((byte) 32 /*0x20*/, (byte) 14, memobus.CpuTo, memobus.CpuFrom, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await memobus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }
}
