﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecCipNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Profinet.AllenBradley;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>三菱PLC的EIP协议的实现，当PLC使用了 QJ71EIP71 模块时就需要使用本类来访问</summary>
public class MelsecCipNet : AllenBradleyNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.#ctor" />
  public MelsecCipNet()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.#ctor(System.String,System.Int32)" />
  public MelsecCipNet(string ipAddress, int port = 44818)
    : base(ipAddress, port)
  {
  }

  /// <summary>
  /// Read data information, data length for read array length information
  /// </summary>
  /// <param name="address">Address format of the node</param>
  /// <param name="length">In the case of arrays, the length of the array </param>
  /// <returns>Result data with result object </returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return this.Read(new string[1]{ address }, new ushort[1]
    {
      length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecCipNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await this.ReadAsync(new string[1]
    {
      address
    }, new ushort[1]{ length });
    return operateResult;
  }
}
