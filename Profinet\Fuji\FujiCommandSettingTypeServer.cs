﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Fuji.FujiCommandSettingTypeServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;

#nullable disable
namespace HslCommunication.Profinet.Fuji;

/// <summary>
/// 富士Command-Setting-type协议实现的虚拟服务器，支持的地址为 B,M,K,D,W9,BD,F,A,WL,W21,W9。具体的支持列表参考api文档<br />
/// The virtual server implemented by Fuji Command-Setting-type protocol supports addresses B, M, K, D, W9, BD, F, A, WL, W21, W9.
/// For the specific support list, please refer to the api documentation
/// </summary>
/// <remarks>
/// 当前的虚拟服务器实例化之后，调用<see cref="M:HslCommunication.Core.Net.NetworkServerBase.ServerStart" /> 方法即可，还有个参数<see cref="P:HslCommunication.Profinet.Fuji.FujiCommandSettingTypeServer.DataSwap" />需要特别注意，约定了数据的大小端形式。
/// </remarks>
public class FujiCommandSettingTypeServer : DeviceServer
{
  private bool dataSwap = false;
  private SoftBuffer bBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer kBuffer;
  private SoftBuffer fBuffer;
  private SoftBuffer aBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer sBuffer;
  private SoftBuffer w9Buffer;
  private SoftBuffer bdBuffer;
  private SoftBuffer wlBuffer;
  private SoftBuffer w21Buffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个富士的服务器<br />
  /// </summary>
  public FujiCommandSettingTypeServer()
  {
    this.bBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.kBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.sBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.w9Buffer = new SoftBuffer(65536 /*0x010000*/);
    this.bdBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.fBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.aBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.wlBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.w21Buffer = new SoftBuffer(65536 /*0x010000*/);
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Fuji.FujiCommandSettingType.DataSwap" />
  public bool DataSwap
  {
    get => this.dataSwap;
    set
    {
      this.dataSwap = value;
      if (value)
        this.ByteTransform = (IByteTransform) new RegularByteTransform();
      else
        this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    }
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    try
    {
      OperateResult<byte[]> operateResult = FujiCommandSettingType.BuildReadCommand(address, length);
      if (!operateResult.IsSuccess)
        return operateResult;
      byte[] response = this.ReadByMessage(operateResult.Content);
      return FujiCommandSettingType.UnpackResponseContentHelper(operateResult.Content, response);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    try
    {
      OperateResult<byte[]> operateResult = FujiCommandSettingType.BuildWriteCommand(address, value);
      if (!operateResult.IsSuccess)
        return (OperateResult) operateResult;
      byte[] response = this.WriteByMessage(operateResult.Content);
      return (OperateResult) FujiCommandSettingType.UnpackResponseContentHelper(operateResult.Content, response);
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>从PLC读取byte类型的数据信息，通常针对步进寄存器，也就是 S100 的地址</summary>
  /// <param name="address">PLC地址数据，例如 S100</param>
  /// <returns>是否读取成功结果对象</returns>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>将Byte输入写入到PLC之中，通常针对步进寄存器，也就是 S100 的地址</summary>
  /// <param name="address">PLC地址数据，例如 S100</param>
  /// <param name="value">数据信息</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address) => base.ReadBool(address);

  /// <inheritdoc />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value) => base.Write(address, value);

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new FujiCommandSettingTypeMessage();
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return OperateResult.CreateSuccessResult<byte[]>(receive[0] != (byte) 0 || receive[2] != (byte) 0 ? (receive[0] != (byte) 1 || receive[2] != (byte) 0 ? this.PackResponseResult(receive, (byte) 32 /*0x20*/, (byte[]) null) : this.WriteByMessage(receive)) : this.ReadByMessage(receive));
  }

  private byte[] PackResponseResult(byte[] command, byte err, byte[] value)
  {
    if (err > (byte) 0 || command[0] == (byte) 1)
    {
      byte[] destinationArray = new byte[9];
      Array.Copy((Array) command, 0, (Array) destinationArray, 0, 9);
      destinationArray[1] = err;
      destinationArray[4] = (byte) 4;
      return destinationArray;
    }
    if (value == null)
      value = new byte[0];
    byte[] destinationArray1 = new byte[10 + value.Length];
    Array.Copy((Array) command, 0, (Array) destinationArray1, 0, 9);
    destinationArray1[4] = (byte) (5 + value.Length);
    value.CopyTo((Array) destinationArray1, 10);
    return destinationArray1;
  }

  private byte[] ReadByMessage(byte[] command)
  {
    int index = (int) command[5] + (int) command[6] * 256 /*0x0100*/;
    int length = (int) command[7] + (int) command[8] * 256 /*0x0100*/;
    if (command[3] == (byte) 0)
      return this.PackResponseResult(command, (byte) 0, this.bBuffer.GetBytes(index * 2, length));
    if (command[3] == (byte) 1)
      return this.PackResponseResult(command, (byte) 0, this.mBuffer.GetBytes(index * 2, length));
    if (command[3] == (byte) 2)
      return this.PackResponseResult(command, (byte) 0, this.kBuffer.GetBytes(index * 2, length));
    if (command[3] == (byte) 3)
      return this.PackResponseResult(command, (byte) 0, this.fBuffer.GetBytes(index * 2, length));
    if (command[3] == (byte) 4)
      return this.PackResponseResult(command, (byte) 0, this.aBuffer.GetBytes(index * 2, length));
    if (command[3] == (byte) 5)
      return this.PackResponseResult(command, (byte) 0, this.dBuffer.GetBytes(index * 2, length));
    if (command[3] == (byte) 8)
      return this.PackResponseResult(command, (byte) 0, this.sBuffer.GetBytes(index, length));
    if (command[3] == (byte) 9)
      return this.PackResponseResult(command, (byte) 0, this.w9Buffer.GetBytes(index * 4, length));
    if (command[3] == (byte) 14)
      return this.PackResponseResult(command, (byte) 0, this.bdBuffer.GetBytes(index * 4, length));
    if (command[3] == (byte) 20)
      return this.PackResponseResult(command, (byte) 0, this.wlBuffer.GetBytes(index * 2, length));
    return command[3] == (byte) 21 ? this.PackResponseResult(command, (byte) 0, this.w21Buffer.GetBytes(index * 2, length)) : this.PackResponseResult(command, (byte) 36, (byte[]) null);
  }

  private byte[] WriteByMessage(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackResponseResult(command, (byte) 34, (byte[]) null);
    int destIndex = (int) command[5] + (int) command[6] * 256 /*0x0100*/;
    int num = (int) command[7] + (int) command[8] * 256 /*0x0100*/;
    byte[] data = command.RemoveBegin<byte>(9);
    if (command[3] == (byte) 0)
      this.bBuffer.SetBytes(data, destIndex * 2);
    else if (command[3] == (byte) 1)
      this.mBuffer.SetBytes(data, destIndex * 2);
    else if (command[3] == (byte) 2)
      this.kBuffer.SetBytes(data, destIndex * 2);
    else if (command[3] == (byte) 3)
      this.fBuffer.SetBytes(data, destIndex * 2);
    else if (command[3] == (byte) 4)
      this.aBuffer.SetBytes(data, destIndex * 2);
    else if (command[3] == (byte) 5)
      this.dBuffer.SetBytes(data, destIndex * 2);
    else if (command[3] == (byte) 8)
      this.sBuffer.SetBytes(data, destIndex);
    else if (command[3] == (byte) 9)
      this.w9Buffer.SetBytes(data, destIndex * 4);
    else if (command[3] == (byte) 14)
      this.bdBuffer.SetBytes(data, destIndex * 4);
    else if (command[3] == (byte) 20)
    {
      this.wlBuffer.SetBytes(data, destIndex * 2);
    }
    else
    {
      if (command[3] != (byte) 21)
        return this.PackResponseResult(command, (byte) 36, (byte[]) null);
      this.w21Buffer.SetBytes(data, destIndex * 2);
    }
    return this.PackResponseResult(command, (byte) 0, (byte[]) null);
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 720896 /*0x0B0000*/)
      throw new Exception("File is not correct");
    this.bBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.kBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.fBuffer.SetBytes(content, 196608 /*0x030000*/, 0, 65536 /*0x010000*/);
    this.aBuffer.SetBytes(content, 262144 /*0x040000*/, 0, 65536 /*0x010000*/);
    this.dBuffer.SetBytes(content, 327680 /*0x050000*/, 0, 65536 /*0x010000*/);
    this.sBuffer.SetBytes(content, 393216 /*0x060000*/, 0, 65536 /*0x010000*/);
    this.w9Buffer.SetBytes(content, 458752 /*0x070000*/, 0, 65536 /*0x010000*/);
    this.bdBuffer.SetBytes(content, 524288 /*0x080000*/, 0, 65536 /*0x010000*/);
    this.wlBuffer.SetBytes(content, 589824 /*0x090000*/, 0, 65536 /*0x010000*/);
    this.w21Buffer.SetBytes(content, 655360 /*0x0A0000*/, 0, 65536 /*0x010000*/);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[720896 /*0x0B0000*/];
    Array.Copy((Array) this.bBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.mBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.kBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.fBuffer.GetBytes(), 0, (Array) destinationArray, 196608 /*0x030000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.aBuffer.GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dBuffer.GetBytes(), 0, (Array) destinationArray, 327680 /*0x050000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.sBuffer.GetBytes(), 0, (Array) destinationArray, 393216 /*0x060000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.w9Buffer.GetBytes(), 0, (Array) destinationArray, 458752 /*0x070000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.bdBuffer.GetBytes(), 0, (Array) destinationArray, 524288 /*0x080000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.wlBuffer.GetBytes(), 0, (Array) destinationArray, 589824 /*0x090000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.w21Buffer.GetBytes(), 0, (Array) destinationArray, 655360 /*0x0A0000*/, 65536 /*0x010000*/);
    return destinationArray;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.bBuffer?.Dispose();
      this.mBuffer?.Dispose();
      this.kBuffer?.Dispose();
      this.fBuffer?.Dispose();
      this.aBuffer?.Dispose();
      this.dBuffer?.Dispose();
      this.sBuffer?.Dispose();
      this.w9Buffer?.Dispose();
      this.bdBuffer?.Dispose();
      this.wlBuffer?.Dispose();
      this.w21Buffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"FujiCommandSettingTypeServer[{this.Port}]";
}
