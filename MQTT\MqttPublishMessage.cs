﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttPublishMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// Mqtt发送的消息封装对象，是对 <see cref="T:HslCommunication.MQTT.MqttApplicationMessage" /> 对象的封装，添加了序号，还有是否重发的信息<br />
/// The message encapsulation object sent by <PERSON><PERSON>tt is an encapsulation of the <see cref="T:HslCommunication.MQTT.MqttApplicationMessage" /> object, with the serial number added, and whether to retransmit
/// </summary>
public class MqttPublishMessage
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public MqttPublishMessage() => this.IsSendFirstTime = true;

  /// <summary>
  /// 是否第一次发送数据信息<br />
  /// Whether to send data information for the first time
  /// </summary>
  public bool IsSendFirstTime { get; set; }

  /// <summary>
  /// 当前的消息的标识符，当质量等级为0的时候，不需要重发以及考虑标识情况<br />
  /// The identifier of the current message, when the quality level is 0, do not need to retransmit and consider the identification situation
  /// </summary>
  public int Identifier { get; set; }

  /// <summary>
  /// 当前发布消息携带的mqtt的应用消息，包含主题，消息等级，负载。<br />
  /// The application message of mqtt carried in the current published message, including the subject, message level, and load.
  /// </summary>
  public MqttApplicationMessage Message { get; set; }
}
