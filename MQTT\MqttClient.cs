﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttClient
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Core.Security;
using HslCommunication.LogNet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Authentication;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// Mqtt协议的客户端实现，支持订阅消息，发布消息，详细的使用例子参考api文档<br />
/// The client implementation of the Mqtt protocol supports subscription messages and publishing messages. For detailed usage examples, refer to the api documentation.
/// </summary>
/// <remarks>
/// 这是一个MQTT的客户端实现，参照MQTT协议的3.1.1版本设计实现的。服务器可以是其他的组件提供的，其他的可以参考示例<br />
/// This is an MQTT client implementation, designed and implemented with reference to version 3.1.1 of the MQTT protocol. The server can be provided by other components.
/// </remarks>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test" title="简单的实例化" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test2" title="带用户名密码的实例化" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test9" title="如果使用证书的情况" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test10" title="简单的加密操作" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test3" title="连接示例" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test4" title="发布示例" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test5" title="订阅示例" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test8" title="网络重连示例" />
/// 当我们在一个多窗体的客户端里使用了<see cref="T:HslCommunication.MQTT.MqttClient" />类，可能很多界面都需要订阅主题，显示一些实时数据信息。只由主窗体来订阅再把数据传递给子窗体却不是很容易操作。
/// 所以在hsl里提供了更加便捷的操作方法。方便在每个子窗体界面中，订阅，显示，取消订阅操作。核心代码如下：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test9" title="子窗体订阅操作" />
/// 以下的例子是DEMO程序的一个例子代码，也可以作为参考
/// <code lang="cs" source="TestProject\HslCommunicationDemo\MQTT\FormMqttSubscribe.cs" region="Sample" title="DEMO子窗体" />
/// </example>
public class MqttClient : NetworkXBase, IDisposable
{
  private NetworkStream networkStream;
  private SslStream sslStream = (SslStream) null;
  private DateTime activeTime;
  private int isReConnectServer = 0;
  private List<MqttPublishMessage> publishMessages;
  private object listLock;
  private Dictionary<string, SubscribeTopic> subscribeTopics;
  private object connectLock;
  private object subscribeLock;
  private SoftIncrementCount incrementCount;
  private bool closed = false;
  private MqttConnectionOptions connectionOptions;
  private Timer timerCheck;
  private bool disposedValue;
  private RSACryptoServiceProvider cryptoServiceProvider = (RSACryptoServiceProvider) null;
  private AesCryptography aesCryptography = (AesCryptography) null;
  private AsyncCallback beginReceiveCallback = (AsyncCallback) null;

  /// <summary>实例化一个默认的对象</summary>
  /// <param name="options">配置信息</param>
  public MqttClient(MqttConnectionOptions options)
  {
    this.beginReceiveCallback = new AsyncCallback(this.ReceiveAsyncCallback);
    this.connectionOptions = options;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    this.listLock = new object();
    this.publishMessages = new List<MqttPublishMessage>();
    this.subscribeTopics = new Dictionary<string, SubscribeTopic>();
    this.activeTime = DateTime.Now;
    this.subscribeLock = new object();
    this.connectLock = new object();
  }

  /// <summary>
  /// 连接服务器，如果连接失败，请稍候重试。<br />
  /// Connect to the server. If the connection fails, try again later.
  /// </summary>
  /// <returns>连接是否成功</returns>
  public OperateResult ConnectServer()
  {
    if (this.connectionOptions == null)
      return new OperateResult("Optines is null");
    OperateResult<Socket> socketAndConnect = this.CreateSocketAndConnect(this.connectionOptions.IpAddress, this.connectionOptions.Port, this.connectionOptions.ConnectTimeout);
    if (!socketAndConnect.IsSuccess)
      return (OperateResult) socketAndConnect;
    RSACryptoServiceProvider rsa = (RSACryptoServiceProvider) null;
    if (this.connectionOptions.UseRSAProvider)
    {
      this.cryptoServiceProvider = new RSACryptoServiceProvider();
      OperateResult operateResult = this.Send(socketAndConnect.Content, MqttHelper.BuildMqttCommand(byte.MaxValue, (byte[]) null, HslSecurity.ByteEncrypt(this.cryptoServiceProvider.GetPEMPublicKey())).Content);
      if (!operateResult.IsSuccess)
        return operateResult;
      OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(socketAndConnect.Content, 10000);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      try
      {
        rsa = RSAHelper.CreateRsaProviderFromPublicKey(this.cryptoServiceProvider.DecryptLargeData(HslSecurity.ByteDecrypt(mqttMessage.Content2)));
      }
      catch (Exception ex)
      {
        socketAndConnect.Content?.Close();
        return new OperateResult("RSA check failed: " + ex.Message);
      }
    }
    OperateResult<byte[]> operateResult1 = MqttHelper.BuildConnectMqttCommand(this.connectionOptions, rsa: rsa);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    if (!this.ConnectionOptions.UseSSL)
    {
      OperateResult operateResult2 = this.Send(socketAndConnect.Content, operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(socketAndConnect.Content, 30000);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      OperateResult operateResult3 = MqttHelper.CheckConnectBack(mqttMessage.Content1, mqttMessage.Content2);
      if (!operateResult3.IsSuccess)
      {
        socketAndConnect.Content?.Close();
        return operateResult3;
      }
      if (this.connectionOptions.UseRSAProvider)
        this.aesCryptography = new AesCryptography(Encoding.UTF8.GetString(this.cryptoServiceProvider.Decrypt(mqttMessage.Content2.RemoveBegin<byte>(2), false)));
    }
    else
    {
      OperateResult<SslStream> sslStream = this.CreateSslStream(socketAndConnect.Content, true);
      if (!sslStream.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) sslStream);
      OperateResult operateResult4 = this.Send(sslStream.Content, operateResult1.Content);
      if (!operateResult4.IsSuccess)
        return operateResult4;
      OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(sslStream.Content, 30000);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      OperateResult operateResult5 = MqttHelper.CheckConnectBack(mqttMessage.Content1, mqttMessage.Content2);
      if (!operateResult5.IsSuccess)
      {
        socketAndConnect.Content?.Close();
        return operateResult5;
      }
    }
    this.incrementCount.ResetCurrentValue();
    this.closed = false;
    try
    {
      socketAndConnect.Content.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) socketAndConnect.Content);
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
    this.CoreSocket?.Close();
    this.CoreSocket = socketAndConnect.Content;
    this.IsConnected = true;
    MqttClient.OnClientConnectedDelegate onClientConnected = this.OnClientConnected;
    if (onClientConnected != null)
      onClientConnected(this);
    this.timerCheck?.Dispose();
    this.activeTime = DateTime.Now;
    if (this.UseTimerCheckDropped && (int) this.connectionOptions.KeepAliveSendInterval.TotalMilliseconds > 0)
      this.timerCheck = new Timer(new TimerCallback(this.TimerCheckServer), (object) null, 2000, (int) this.connectionOptions.KeepAliveSendInterval.TotalMilliseconds);
    return OperateResult.CreateSuccessResult();
  }

  private bool ValidateServerCertificate(
    object sender,
    X509Certificate certificate,
    X509Chain chain,
    SslPolicyErrors sslPolicyErrors)
  {
    return sslPolicyErrors == SslPolicyErrors.None || !this.connectionOptions.SSLSecure;
  }

  /// <summary>
  /// 关闭Mqtt服务器的连接。<br />
  /// Close the connection to the Mqtt server.
  /// </summary>
  public void ConnectClose()
  {
    lock (this.connectLock)
    {
      this.closed = true;
      this.IsConnected = false;
    }
    OperateResult<byte[]> operateResult = MqttHelper.BuildMqttCommand((byte) 14, (byte) 0, (byte[]) null, (byte[]) null);
    if (operateResult.IsSuccess)
      this.SendMqttBytes(operateResult.Content);
    this.timerCheck?.Dispose();
    HslHelper.ThreadSleep(20);
    this.CoreSocket?.Close();
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.ConnectServer" />
  public async Task<OperateResult> ConnectServerAsync()
  {
    if (this.connectionOptions == null)
      return new OperateResult("Optines is null");
    OperateResult<Socket> connect = await this.CreateSocketAndConnectAsync(this.connectionOptions.IpAddress, this.connectionOptions.Port, this.connectionOptions.ConnectTimeout);
    if (!connect.IsSuccess)
      return (OperateResult) connect;
    RSACryptoServiceProvider rsa = (RSACryptoServiceProvider) null;
    if (this.connectionOptions.UseRSAProvider)
    {
      this.cryptoServiceProvider = new RSACryptoServiceProvider();
      OperateResult sendKey = await this.SendAsync(connect.Content, MqttHelper.BuildMqttCommand(byte.MaxValue, (byte[]) null, HslSecurity.ByteEncrypt(this.cryptoServiceProvider.GetPEMPublicKey())).Content);
      if (!sendKey.IsSuccess)
        return sendKey;
      OperateResult<byte, byte[]> key = await this.ReceiveMqttMessageAsync(connect.Content, 10000);
      if (!key.IsSuccess)
        return (OperateResult) key;
      try
      {
        byte[] serverPublicToken = this.cryptoServiceProvider.DecryptLargeData(HslSecurity.ByteDecrypt(key.Content2));
        rsa = RSAHelper.CreateRsaProviderFromPublicKey(serverPublicToken);
        serverPublicToken = (byte[]) null;
      }
      catch (Exception ex)
      {
        connect.Content?.Close();
        return new OperateResult("RSA check failed: " + ex.Message);
      }
      sendKey = (OperateResult) null;
      key = (OperateResult<byte, byte[]>) null;
    }
    OperateResult<byte[]> command = MqttHelper.BuildConnectMqttCommand(this.connectionOptions, rsa: rsa);
    if (!command.IsSuccess)
      return (OperateResult) command;
    if (!this.ConnectionOptions.UseSSL)
    {
      OperateResult send = await this.SendAsync(connect.Content, command.Content);
      if (!send.IsSuccess)
        return send;
      OperateResult<byte, byte[]> receive = await this.ReceiveMqttMessageAsync(connect.Content, 30000);
      if (!receive.IsSuccess)
        return (OperateResult) receive;
      OperateResult check = MqttHelper.CheckConnectBack(receive.Content1, receive.Content2);
      if (!check.IsSuccess)
      {
        connect.Content?.Close();
        return check;
      }
      if (this.connectionOptions.UseRSAProvider)
      {
        string key = Encoding.UTF8.GetString(this.cryptoServiceProvider.Decrypt(receive.Content2.RemoveBegin<byte>(2), false));
        this.aesCryptography = new AesCryptography(key);
        key = (string) null;
      }
      send = (OperateResult) null;
      receive = (OperateResult<byte, byte[]>) null;
      check = (OperateResult) null;
    }
    else
    {
      OperateResult<SslStream> ssl = this.CreateSslStream(connect.Content, true);
      if (!ssl.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) ssl);
      OperateResult send = await this.SendAsync(ssl.Content, command.Content);
      if (!send.IsSuccess)
        return send;
      OperateResult<byte, byte[]> receive = await this.ReceiveMqttMessageAsync(ssl.Content, 30000);
      if (!receive.IsSuccess)
        return (OperateResult) receive;
      OperateResult check = MqttHelper.CheckConnectBack(receive.Content1, receive.Content2);
      if (!check.IsSuccess)
      {
        connect.Content?.Close();
        return check;
      }
      ssl = (OperateResult<SslStream>) null;
      send = (OperateResult) null;
      receive = (OperateResult<byte, byte[]>) null;
      check = (OperateResult) null;
    }
    this.incrementCount.ResetCurrentValue();
    this.closed = false;
    try
    {
      connect.Content.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) connect.Content);
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
    this.CoreSocket?.Close();
    this.CoreSocket = connect.Content;
    this.IsConnected = true;
    MqttClient.OnClientConnectedDelegate onClientConnected = this.OnClientConnected;
    if (onClientConnected != null)
      onClientConnected(this);
    this.timerCheck?.Dispose();
    this.activeTime = DateTime.Now;
    TimeSpan aliveSendInterval;
    int num;
    if (this.UseTimerCheckDropped)
    {
      aliveSendInterval = this.connectionOptions.KeepAliveSendInterval;
      num = (int) aliveSendInterval.TotalMilliseconds > 0 ? 1 : 0;
    }
    else
      num = 0;
    if (num != 0)
    {
      TimerCallback callback = new TimerCallback(this.TimerCheckServer);
      aliveSendInterval = this.connectionOptions.KeepAliveSendInterval;
      int totalMilliseconds = (int) aliveSendInterval.TotalMilliseconds;
      this.timerCheck = new Timer(callback, (object) null, 2000, totalMilliseconds);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.ConnectClose" />
  public async Task ConnectCloseAsync()
  {
    lock (this.connectLock)
    {
      this.closed = true;
      this.IsConnected = true;
    }
    OperateResult<byte[]> command = MqttHelper.BuildMqttCommand((byte) 14, (byte) 0, (byte[]) null, (byte[]) null);
    if (command.IsSuccess)
    {
      OperateResult operateResult = await this.SendMqttBytesAsync(command.Content);
    }
    this.timerCheck?.Dispose();
    HslHelper.ThreadSleep(20);
    Socket coreSocket = this.CoreSocket;
    if (coreSocket == null)
    {
      command = (OperateResult<byte[]>) null;
    }
    else
    {
      coreSocket.Close();
      command = (OperateResult<byte[]>) null;
    }
  }

  /// <summary>
  /// 发布一个MQTT协议的消息到服务器。该消息包含主题，负载数据，消息等级，是否保留信息。<br />
  /// Publish an MQTT protocol message to the server. The message contains the subject, payload data, message level, and whether to retain information.
  /// </summary>
  /// <param name="message">消息</param>
  /// <returns>发布结果</returns>
  /// <example>
  /// 参照 <see cref="T:HslCommunication.MQTT.MqttClient" /> 的示例说明。
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test" title="简单的实例化" />
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test4" title="发布示例" />
  /// </example>
  public OperateResult PublishMessage(MqttApplicationMessage message)
  {
    MqttPublishMessage mqttPublishMessage = new MqttPublishMessage()
    {
      Identifier = message.QualityOfServiceLevel == MqttQualityOfServiceLevel.AtMostOnce ? 0 : (int) this.incrementCount.GetCurrentValue(),
      Message = message
    };
    OperateResult<byte[]> operateResult = MqttHelper.BuildPublishMqttCommand(mqttPublishMessage, this.aesCryptography);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    if (message.QualityOfServiceLevel == MqttQualityOfServiceLevel.AtMostOnce)
      return this.SendMqttBytes(operateResult.Content);
    this.AddPublishMessage(mqttPublishMessage);
    return this.SendMqttBytes(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.PublishMessage(HslCommunication.MQTT.MqttApplicationMessage)" />
  public async Task<OperateResult> PublishMessageAsync(MqttApplicationMessage message)
  {
    MqttPublishMessage publishMessage = new MqttPublishMessage()
    {
      Identifier = message.QualityOfServiceLevel == MqttQualityOfServiceLevel.AtMostOnce ? 0 : (int) this.incrementCount.GetCurrentValue(),
      Message = message
    };
    OperateResult<byte[]> command = MqttHelper.BuildPublishMqttCommand(publishMessage, this.aesCryptography);
    if (!command.IsSuccess)
      return (OperateResult) command;
    if (message.QualityOfServiceLevel == MqttQualityOfServiceLevel.AtMostOnce)
    {
      OperateResult operateResult = await this.SendMqttBytesAsync(command.Content);
      return operateResult;
    }
    this.AddPublishMessage(publishMessage);
    OperateResult operateResult1 = await this.SendMqttBytesAsync(command.Content);
    return operateResult1;
  }

  /// <summary>
  /// 从服务器订阅一个或多个主题信息<br />
  /// Subscribe to one or more topics from the server
  /// </summary>
  /// <param name="topic">主题信息</param>
  /// <returns>订阅结果</returns>
  /// <example>
  /// 参照 <see cref="T:HslCommunication.MQTT.MqttClient" /> 的示例说明。
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test" title="简单的实例化" />
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test5" title="订阅示例" />
  /// </example>
  public OperateResult SubscribeMessage(string topic)
  {
    return this.SubscribeMessage(new string[1]{ topic });
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.SubscribeMessage(System.String)" />
  public OperateResult SubscribeMessage(string[] topics)
  {
    return this.SubscribeMessage(new MqttSubscribeMessage()
    {
      Identifier = (int) this.incrementCount.GetCurrentValue(),
      Topics = topics
    });
  }

  /// <summary>
  /// 向服务器订阅一个主题消息，可以指定订阅的主题数组，订阅的质量等级，还有消息标识符<br />
  /// To subscribe to a topic message from the server, you can specify the subscribed topic array,
  /// the subscription quality level, and the message identifier
  /// </summary>
  /// <param name="subcribeMessage">订阅的消息本体</param>
  /// <returns>是否订阅成功</returns>
  public OperateResult SubscribeMessage(MqttSubscribeMessage subcribeMessage)
  {
    if (subcribeMessage.Topics == null || subcribeMessage.Topics.Length == 0)
      return OperateResult.CreateSuccessResult();
    OperateResult<byte[]> operateResult1 = MqttHelper.BuildSubscribeMqttCommand(subcribeMessage);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult operateResult2 = this.SendMqttBytes(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    this.AddSubTopics(subcribeMessage.Topics);
    return OperateResult.CreateSuccessResult();
  }

  private void AddSubTopics(string[] topics)
  {
    lock (this.subscribeLock)
    {
      for (int index = 0; index < topics.Length; ++index)
      {
        if (!this.subscribeTopics.ContainsKey(topics[index]))
          this.subscribeTopics.Add(topics[index], new SubscribeTopic(topics[index]));
        this.subscribeTopics[topics[index]].AddSubscribeTick();
      }
    }
  }

  /// <summary>
  /// 获取已经订阅的主题信息，方便针对不同的界面订阅不同的主题。<br />
  /// Get subscribed topic information, which is convenient for subscribing to different topics for different interfaces.
  /// </summary>
  /// <param name="topic">主题信息</param>
  /// <returns>订阅主题信息</returns>
  public SubscribeTopic GetSubscribeTopic(string topic)
  {
    SubscribeTopic subscribeTopic = (SubscribeTopic) null;
    lock (this.subscribeLock)
    {
      if (this.subscribeTopics.ContainsKey(topic))
        subscribeTopic = this.subscribeTopics[topic];
    }
    return subscribeTopic;
  }

  /// <summary>
  /// 取消订阅多个主题信息，取消之后，当前的订阅数据就不在接收到，除非服务器强制推送。<br />
  /// Unsubscribe from multiple topic information. After cancellation, the current subscription data will not be received unless the server forces it to push it.
  /// </summary>
  /// <param name="topics">主题信息</param>
  /// <returns>取消订阅结果</returns>
  /// <example>
  /// 参照 <see cref="T:HslCommunication.MQTT.MqttClient" /> 的示例说明。
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test" title="简单的实例化" />
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test7" title="订阅示例" />
  /// </example>
  public OperateResult UnSubscribeMessage(string[] topics)
  {
    OperateResult<byte[]> operateResult1 = MqttHelper.BuildUnSubscribeMqttCommand(new MqttSubscribeMessage()
    {
      Identifier = (int) this.incrementCount.GetCurrentValue(),
      Topics = topics
    });
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult operateResult2 = this.SendMqttBytes(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    this.RemoveSubTopics(topics);
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 取消订阅指定的主题信息，取消之后，就不再接收当前主题的数据，除非服务器强制推送<br />
  /// Unsubscribe from the specified topic information. After cancellation, the data of the current topic will no longer be received unless the server forces push
  /// </summary>
  /// <param name="topic">主题信息</param>
  /// <returns>取消订阅结果</returns>
  /// <example>
  /// 参照 <see cref="T:HslCommunication.MQTT.MqttClient" /> 的示例说明。
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test" title="简单的实例化" />
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MQTTClient.cs" region="Test7" title="订阅示例" />
  /// </example>
  public OperateResult UnSubscribeMessage(string topic)
  {
    return this.UnSubscribeMessage(new string[1]{ topic });
  }

  private bool RemoveSubTopics(string[] topics)
  {
    bool flag = true;
    lock (this.subscribeLock)
    {
      for (int index = 0; index < topics.Length; ++index)
      {
        if (this.subscribeTopics.ContainsKey(topics[index]))
          this.subscribeTopics.Remove(topics[index]);
      }
    }
    return flag;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.SubscribeMessage(System.String)" />
  public async Task<OperateResult> SubscribeMessageAsync(string topic)
  {
    OperateResult operateResult = await this.SubscribeMessageAsync(new string[1]
    {
      topic
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.SubscribeMessage(System.String[])" />
  public async Task<OperateResult> SubscribeMessageAsync(string[] topics)
  {
    if (topics == null || topics.Length == 0)
      return OperateResult.CreateSuccessResult();
    MqttSubscribeMessage subcribeMessage = new MqttSubscribeMessage()
    {
      Identifier = (int) this.incrementCount.GetCurrentValue(),
      Topics = topics
    };
    OperateResult<byte[]> command = MqttHelper.BuildSubscribeMqttCommand(subcribeMessage);
    if (!command.IsSuccess)
      return (OperateResult) command;
    this.AddSubTopics(topics);
    OperateResult operateResult = await this.SendMqttBytesAsync(command.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.UnSubscribeMessage(System.String[])" />
  public async Task<OperateResult> UnSubscribeMessageAsync(string[] topics)
  {
    MqttSubscribeMessage subcribeMessage = new MqttSubscribeMessage()
    {
      Identifier = (int) this.incrementCount.GetCurrentValue(),
      Topics = topics
    };
    OperateResult<byte[]> command = MqttHelper.BuildUnSubscribeMqttCommand(subcribeMessage);
    this.RemoveSubTopics(topics);
    OperateResult operateResult = await this.SendMqttBytesAsync(command.Content);
    subcribeMessage = (MqttSubscribeMessage) null;
    command = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttClient.UnSubscribeMessage(System.String)" />
  public async Task<OperateResult> UnSubscribeMessageAsync(string topic)
  {
    OperateResult operateResult = await this.UnSubscribeMessageAsync(new string[1]
    {
      topic
    });
    return operateResult;
  }

  private void OnMqttNetworkError()
  {
    if (this.closed)
    {
      this.LogNet?.WriteDebug(this.ToString(), "Closed");
    }
    else
    {
      if (Interlocked.CompareExchange(ref this.isReConnectServer, 1, 0) != 0)
        return;
      try
      {
        this.IsConnected = false;
        this.timerCheck?.Dispose();
        this.timerCheck = (Timer) null;
        if (this.OnNetworkError == null)
        {
          this.LogNet?.WriteInfo(this.ToString(), "The network is abnormal, and the system is ready to automatically reconnect after 10 seconds.");
label_35:
          for (int index = 0; index < 10; ++index)
          {
            HslHelper.ThreadSleep(1000);
            this.LogNet?.WriteInfo(this.ToString(), $"Wait for {10 - index} second to connect to the server ...");
            if (this.closed)
            {
              this.LogNet?.WriteDebug(this.ToString(), "Closed");
              Interlocked.Exchange(ref this.isReConnectServer, 0);
              return;
            }
          }
          lock (this.connectLock)
          {
            if (this.closed)
            {
              this.LogNet?.WriteDebug(this.ToString(), "Closed");
              Interlocked.Exchange(ref this.isReConnectServer, 0);
              return;
            }
            if (this.ConnectServer().IsSuccess)
            {
              this.LogNet?.WriteInfo(this.ToString(), "Successfully connected to the server!");
            }
            else
            {
              this.LogNet?.WriteInfo(this.ToString(), "The connection failed. Prepare to reconnect after 10 seconds.");
              if (this.closed)
              {
                this.LogNet?.WriteDebug(this.ToString(), "Closed");
                Interlocked.Exchange(ref this.isReConnectServer, 0);
                return;
              }
              goto label_35;
            }
          }
        }
        else
        {
          EventHandler onNetworkError = this.OnNetworkError;
          if (onNetworkError != null)
            onNetworkError((object) this, new EventArgs());
        }
        Interlocked.Exchange(ref this.isReConnectServer, 0);
      }
      catch
      {
        Interlocked.Exchange(ref this.isReConnectServer, 0);
        throw;
      }
    }
  }

  private async void ReceiveAsyncCallback(IAsyncResult ar)
  {
    if (!(ar.AsyncState is Socket socket))
    {
      socket = (Socket) null;
    }
    else
    {
      try
      {
        socket.EndReceive(ar);
      }
      catch (ObjectDisposedException ex)
      {
        socket?.Close();
        ILogNet logNet = this.LogNet;
        if (logNet == null)
        {
          socket = (Socket) null;
          return;
        }
        logNet.WriteDebug(this.ToString(), "Closed");
        socket = (Socket) null;
        return;
      }
      catch (Exception ex)
      {
        socket?.Close();
        this.LogNet?.WriteDebug(this.ToString(), "ReceiveCallback Failed:" + ex.Message);
        this.OnMqttNetworkError();
        socket = (Socket) null;
        return;
      }
      if (this.closed)
      {
        ILogNet logNet = this.LogNet;
        if (logNet == null)
        {
          socket = (Socket) null;
        }
        else
        {
          logNet.WriteDebug(this.ToString(), "Closed");
          socket = (Socket) null;
        }
      }
      else
      {
        OperateResult<byte, byte[]> read = (OperateResult<byte, byte[]>) null;
        if (string.IsNullOrEmpty(this.connectionOptions.CertificateFile))
        {
          read = await this.ReceiveMqttMessageAsync(socket, 30000);
        }
        else
        {
          OperateResult<SslStream> ssl = this.CreateSslStream(socket);
          if (!ssl.IsSuccess)
          {
            socket?.Close();
            this.LogNet?.WriteDebug(this.ToString(), "CreateSslStream Failed:" + ssl.Message);
            this.OnMqttNetworkError();
            socket = (Socket) null;
            return;
          }
          read = await this.ReceiveMqttMessageAsync(ssl.Content, 30000);
          ssl = (OperateResult<SslStream>) null;
        }
        if (!read.IsSuccess)
        {
          this.OnMqttNetworkError();
          socket = (Socket) null;
        }
        else
        {
          byte mqttCode = read.Content1;
          byte[] data = read.Content2;
          int code = (int) mqttCode >> 4;
          switch (code)
          {
            case 3:
              this.ExtraPublishData(mqttCode, data);
              break;
            case 4:
              ILogNet logNet1 = this.LogNet;
              if (logNet1 != null)
              {
                logNet1.WriteDebug(this.ToString(), $"Code[{mqttCode:X2}] Publish Ack: {SoftBasic.ByteToHexString(data, ' ')}");
                break;
              }
              break;
            case 5:
              this.SendMqttBytes(MqttHelper.BuildMqttCommand((byte) 6, (byte) 2, data, new byte[0]).Content);
              ILogNet logNet2 = this.LogNet;
              if (logNet2 != null)
              {
                logNet2.WriteDebug(this.ToString(), $"Code[{mqttCode:X2}] Publish Rec: {SoftBasic.ByteToHexString(data, ' ')}");
                break;
              }
              break;
            case 7:
              ILogNet logNet3 = this.LogNet;
              if (logNet3 != null)
              {
                logNet3.WriteDebug(this.ToString(), $"Code[{mqttCode:X2}] Publish Complete: {SoftBasic.ByteToHexString(data, ' ')}");
                break;
              }
              break;
            case 9:
              ILogNet logNet4 = this.LogNet;
              if (logNet4 != null)
              {
                logNet4.WriteDebug(this.ToString(), $"Code[{mqttCode:X2}] Subscribe Ack: {SoftBasic.ByteToHexString(data, ' ')}");
                break;
              }
              break;
            case 11:
              ILogNet logNet5 = this.LogNet;
              if (logNet5 != null)
              {
                logNet5.WriteDebug(this.ToString(), $"Code[{mqttCode:X2}] UnSubscribe Ack: {SoftBasic.ByteToHexString(data, ' ')}");
                break;
              }
              break;
            case 13:
              this.activeTime = DateTime.Now;
              ILogNet logNet6 = this.LogNet;
              if (logNet6 != null)
              {
                logNet6.WriteDebug(this.ToString(), "Heart Code Check!");
                break;
              }
              break;
            default:
              ILogNet logNet7 = this.LogNet;
              if (logNet7 != null)
              {
                logNet7.WriteDebug(this.ToString(), $"Code[{mqttCode:X2}] {SoftBasic.ByteToHexString(data, ' ')}");
                break;
              }
              break;
          }
          try
          {
            socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) socket);
          }
          catch (Exception ex)
          {
            socket?.Close();
            this.LogNet?.WriteDebug(this.ToString(), "BeginReceive Failed:" + ex.Message);
            this.OnMqttNetworkError();
          }
          read = (OperateResult<byte, byte[]>) null;
          data = (byte[]) null;
          socket = (Socket) null;
        }
      }
    }
  }

  private void ExtraPublishData(byte mqttCode, byte[] data)
  {
    this.activeTime = DateTime.Now;
    OperateResult<string, byte[]> data1 = MqttHelper.ExtraMqttReceiveData(mqttCode, data, this.aesCryptography);
    if (!data1.IsSuccess)
    {
      this.LogNet?.WriteDebug(this.ToString(), data1.Message);
    }
    else
    {
      int qos = MqttHelper.ExtraQosFromMqttCode(mqttCode);
      MqttApplicationMessage message = new MqttApplicationMessage();
      message.Topic = data1.Content1;
      message.Retain = ((int) mqttCode & 1) == 1;
      message.QualityOfServiceLevel = MqttHelper.GetFromQos(qos);
      message.Payload = data1.Content2;
      MqttClient.MqttMessageReceiveDelegate mqttMessageReceived = this.OnMqttMessageReceived;
      if (mqttMessageReceived != null)
        mqttMessageReceived(this, message);
      this.GetSubscribeTopic(data1.Content1)?.TriggerSubscription(this, message);
    }
  }

  private void TimerCheckServer(object obj)
  {
    if (this.CoreSocket == null)
      return;
    if ((DateTime.Now - this.activeTime).TotalSeconds > this.connectionOptions.KeepAliveSendInterval.TotalSeconds * 3.0)
      this.OnMqttNetworkError();
    else if (!this.SendMqttBytes(MqttHelper.BuildMqttCommand((byte) 12, (byte) 0, new byte[0], new byte[0]).Content).IsSuccess)
      this.OnMqttNetworkError();
  }

  private void AddPublishMessage(MqttPublishMessage publishMessage)
  {
  }

  /// <summary>当接收到Mqtt订阅的信息的时候触发</summary>
  public event MqttClient.MqttMessageReceiveDelegate OnMqttMessageReceived;

  /// <summary>当网络发生异常的时候触发的事件，用户应该在事件里进行重连服务器</summary>
  public event EventHandler OnNetworkError;

  /// <summary>
  /// 当客户端连接成功触发事件，就算是重新连接服务器后，也是会触发的<br />
  /// The event is triggered when the client is connected successfully, even after reconnecting to the server.
  /// </summary>
  public event MqttClient.OnClientConnectedDelegate OnClientConnected;

  /// <summary>释放当前的对象</summary>
  /// <param name="disposing"></param>
  protected virtual void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (disposing)
    {
      this.incrementCount?.Dispose();
      this.timerCheck?.Dispose();
      this.OnClientConnected = (MqttClient.OnClientConnectedDelegate) null;
      this.OnMqttMessageReceived = (MqttClient.MqttMessageReceiveDelegate) null;
      this.OnNetworkError = (EventHandler) null;
    }
    this.disposedValue = true;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    this.Dispose(true);
    GC.SuppressFinalize((object) this);
  }

  private OperateResult<SslStream> CreateSslStream(Socket socket, bool createNew = false)
  {
    if (!createNew)
      return OperateResult.CreateSuccessResult<SslStream>(this.sslStream);
    this.networkStream?.Close();
    this.sslStream?.Close();
    this.networkStream = new NetworkStream(socket, false);
    this.sslStream = new SslStream((Stream) this.networkStream, false, new RemoteCertificateValidationCallback(this.ValidateServerCertificate), (LocalCertificateSelectionCallback) null);
    try
    {
      if (string.IsNullOrEmpty(this.ConnectionOptions.CertificateFile))
        this.sslStream.AuthenticateAsClient(this.connectionOptions.HostName);
      else
        this.sslStream.AuthenticateAsClient(this.connectionOptions.HostName, new X509CertificateCollection(new X509Certificate[1]
        {
          X509Certificate.CreateFromCertFile(this.ConnectionOptions.CertificateFile)
        }), SslProtocols.Tls | SslProtocols.Tls11 | SslProtocols.Tls12, false);
      return OperateResult.CreateSuccessResult<SslStream>(this.sslStream);
    }
    catch (Exception ex)
    {
      return new OperateResult<SslStream>(ex.Message);
    }
  }

  private OperateResult SendMqttBytes(byte[] data)
  {
    if (string.IsNullOrEmpty(this.connectionOptions.CertificateFile))
      return this.Send(this.CoreSocket, data);
    OperateResult<SslStream> sslStream = this.CreateSslStream(this.CoreSocket);
    return !sslStream.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) sslStream) : this.Send(sslStream.Content, data);
  }

  private async Task<OperateResult> SendMqttBytesAsync(byte[] data)
  {
    if (string.IsNullOrEmpty(this.connectionOptions.CertificateFile))
    {
      OperateResult operateResult = await this.SendAsync(this.CoreSocket, data);
      return operateResult;
    }
    OperateResult<SslStream> ssl = this.CreateSslStream(this.CoreSocket);
    if (!ssl.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) ssl);
    OperateResult operateResult1 = await this.SendAsync(ssl.Content, data);
    return operateResult1;
  }

  /// <summary>
  /// 获取当前的连接配置参数信息<br />
  /// Get current connection configuration parameter information
  /// </summary>
  public MqttConnectionOptions ConnectionOptions => this.connectionOptions;

  /// <summary>
  /// 获取或设置是否启动定时器去检测当前客户端是否超时掉线。默认为 <c>True</c><br />
  /// Get or set whether to start the timer to detect whether the current client timeout and disconnection. Default is <c>True</c>
  /// </summary>
  public bool UseTimerCheckDropped { get; set; } = true;

  /// <summary>
  /// 获取或设置当前的服务器连接是否成功，定时获取本属性可用于实时更新连接状态信息。<br />
  /// Get or set whether the current server connection is successful or not.
  /// This property can be obtained regularly and can be used to update the connection status information in real time.
  /// </summary>
  public bool IsConnected { get; private set; } = false;

  /// <summary>
  /// 获取当前的客户端对象已经订阅的所有的Topic信息<br />
  /// Get all Topic information that the current client object has subscribed to
  /// </summary>
  public string[] SubcribeTopics
  {
    get
    {
      lock (this.subscribeLock)
        return this.subscribeTopics.Keys.ToArray<string>();
    }
  }

  /// <summary>
  /// 获取或设置当前客户端关联的自定义的对象内容<br />
  /// Gets or sets the custom object content associated with the current client
  /// </summary>
  public object Tag { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"MqttClient[{this.connectionOptions.IpAddress}:{this.connectionOptions.Port}]";
  }

  /// <summary>
  /// 当接收到Mqtt订阅的信息的时候触发<br />
  /// Triggered when receiving Mqtt subscription information
  /// </summary>
  /// <param name="client">收到消息时候的client实例对象</param>
  /// <param name="message">Mqtt的消息对象</param>
  public delegate void MqttMessageReceiveDelegate(MqttClient client, MqttApplicationMessage message);

  /// <summary>
  /// 连接服务器成功的委托<br />
  /// Connection server successfully delegated
  /// </summary>
  public delegate void OnClientConnectedDelegate(MqttClient client);
}
