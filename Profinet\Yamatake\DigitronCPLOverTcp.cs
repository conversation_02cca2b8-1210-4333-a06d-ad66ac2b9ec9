﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Yamatake.DigitronCPLOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Yamatake.Helper;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Yamatake;

/// <summary>山武的数字指定调节器的通信协议，基于CPL转网口的实现，测试型号 SDC40B</summary>
public class DigitronCPLOverTcp : DeviceTcpNet
{
  /// <summary>实例化一个默认的对象</summary>
  public DigitronCPLOverTcp()
  {
    this.Station = (byte) 1;
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13, (byte) 10);
  }

  /// <summary>获取或设置当前的站号信息</summary>
  public byte Station { get; set; }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult1 = DigitronCPLHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station), address, length);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? operateResult2 : DigitronCPLHelper.ExtraActualResponse(operateResult2.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = DigitronCPLHelper.BuildWriteCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station), address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) DigitronCPLHelper.ExtraActualResponse(operateResult2.Content);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    byte station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station);
    OperateResult<byte[]> command = DigitronCPLHelper.BuildReadCommand(station, address, length);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? DigitronCPLHelper.ExtraActualResponse(read.Content) : read;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    byte station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station);
    OperateResult<byte[]> command = DigitronCPLHelper.BuildWriteCommand(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? (OperateResult) DigitronCPLHelper.ExtraActualResponse(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc />
  public override string ToString() => $"DigitronCPLOverTcp[{this.IpAddress}:{this.Port}]";
}
