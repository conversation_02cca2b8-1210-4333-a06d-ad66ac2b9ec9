﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.MemobusAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>扩展memobus协议的地址信息</summary>
public class MemobusAddress : DeviceAddressDataBase
{
  /// <summary>获取或设置当前地址对应的功能码信息</summary>
  public byte SFC { get; set; }

  /// <summary>获取或设置当前的地址对应的主功能码信息</summary>
  public byte MFC { get; set; }

  /// <inheritdoc />
  public override string ToString() => this.AddressStart.ToString();

  /// <summary>获取并解析出memobus地址的信息及功能码</summary>
  /// <param name="address">地址信息</param>
  /// <param name="isBit">是否位</param>
  /// <returns>memobus的地址信息</returns>
  public static OperateResult<MemobusAddress> ParseFrom(string address, bool isBit)
  {
    try
    {
      MemobusAddress memobusAddress = new MemobusAddress();
      memobusAddress.MFC = (byte) HslHelper.ExtractParameter(ref address, "mfc", 32 /*0x20*/);
      memobusAddress.SFC = (byte) HslHelper.ExtractParameter(ref address, "x", isBit ? 1 : 3);
      if (address.IndexOf('.') > 0)
      {
        int length = address.IndexOf('.');
        memobusAddress.AddressStart = Convert.ToInt32(address.Substring(0, length)) * 16 /*0x10*/ + HslHelper.CalculateBitStartIndex(address.Substring(length + 1));
        if (memobusAddress.SFC == (byte) 1)
          memobusAddress.SFC = (byte) 3;
      }
      else
        memobusAddress.AddressStart = (int) ushort.Parse(address);
      return OperateResult.CreateSuccessResult<MemobusAddress>(memobusAddress);
    }
    catch (Exception ex)
    {
      return new OperateResult<MemobusAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }
}
