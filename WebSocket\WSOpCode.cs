﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.WebSocket.WSOpCode
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.WebSocket;

/// <summary>websocket 协议的 op的枚举信息</summary>
public enum WSOpCode
{
  /// <summary>连续消息分片</summary>
  ContinuousMessageFragment = 0,
  /// <summary>文本消息分片</summary>
  TextMessageFragment = 1,
  /// <summary>二进制消息分片</summary>
  BinaryMessageFragment = 2,
  /// <summary>连接关闭</summary>
  ConnectionClose = 8,
  /// <summary>心跳检查</summary>
  HeartbeatPing = 9,
  /// <summary>心跳检查</summary>
  HeartbeatPong = 10, // 0x0000000A
}
