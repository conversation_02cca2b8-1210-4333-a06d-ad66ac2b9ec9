﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.DCS.DcsNanJingAuto
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using HslCommunication.ModBus;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.DCS;

/// <summary>南京自动化研究所的DCS系统，基于modbus实现，但是不是标准的实现</summary>
public class DcsNanJingAuto : ModbusTcpNet
{
  private byte[] headCommand = new byte[12]
  {
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 6,
    (byte) 1,
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1
  };

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public DcsNanJingAuto()
  {
  }

  /// <inheritdoc />
  public DcsNanJingAuto(string ipAddress, int port = 502, byte station = 1)
    : base(ipAddress, port, station)
  {
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new DcsNanJingAutoMessage();

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    this.MessageId.ResetCurrentValue(0L);
    this.headCommand[6] = this.Station;
    OperateResult operateResult1 = this.CommunicationPipe.Send(this.headCommand);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.CommunicationPipe.Receive(-1, 3000);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (this.CheckResponseStatus(operateResult2.Content) ? base.InitializationOnConnect() : new OperateResult("Check Status Response failed: " + operateResult2.Content.ToHexString(' ')));
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    this.MessageId.ResetCurrentValue(0L);
    this.headCommand[6] = this.Station;
    OperateResult send = await this.CommunicationPipe.SendAsync(this.headCommand).ConfigureAwait(false);
    if (!send.IsSuccess)
      return send;
    OperateResult<byte[]> receive = await this.CommunicationPipe.ReceiveAsync(-1, 3000).ConfigureAwait(false);
    return receive.IsSuccess ? (this.CheckResponseStatus(receive.Content) ? OperateResult.CreateSuccessResult() : new OperateResult("Check Status Response failed: " + receive.Content.ToHexString(' '))) : (OperateResult) receive;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackHeader = true)
  {
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Send} : {(this.LogMsgFormatBinary ? send.ToHexString(' ') : Encoding.ASCII.GetString(send))}");
    INetMessage newNetMessage = this.GetNewNetMessage();
    if (newNetMessage != null)
      newNetMessage.SendBytes = send;
    OperateResult result = pipe.Send(send);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result);
    if (this.ReceiveTimeOut < 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!hasResponseData)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (this.SleepTime > 0)
      HslHelper.ThreadSleep(this.SleepTime);
    OperateResult<byte[]> message = pipe.ReceiveMessage(newNetMessage, send);
    if (!message.IsSuccess)
      return message;
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Receive} : {(this.LogMsgFormatBinary ? message.Content.ToHexString(' ') : Encoding.ASCII.GetString(message.Content))}");
    if (message.Content.Length == 6 && this.CheckResponseStatus(message.Content))
      message = pipe.ReceiveMessage(newNetMessage, send);
    if (newNetMessage == null || newNetMessage.CheckHeadBytesLegal((byte[]) null))
      return OperateResult.CreateSuccessResult<byte[]>(message.Content);
    return new OperateResult<byte[]>($"{StringResources.Language.CommandHeadCodeCheckFailed}{Environment.NewLine}{StringResources.Language.Send}: {SoftBasic.ByteToHexString(send, ' ')}{Environment.NewLine}{StringResources.Language.Receive}: {SoftBasic.ByteToHexString(message.Content, ' ')}");
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackHeader = true)
  {
    byte[] sendValue = usePackHeader ? this.PackCommandWithHeader(send) : send;
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Send} : {(this.LogMsgFormatBinary ? sendValue.ToHexString(' ') : Encoding.ASCII.GetString(sendValue))}");
    INetMessage netMessage = this.GetNewNetMessage();
    if (netMessage != null)
      netMessage.SendBytes = sendValue;
    OperateResult sendResult = await pipe.SendAsync(sendValue).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(sendResult);
    if (this.ReceiveTimeOut < 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!hasResponseData)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (this.SleepTime > 0)
      await Task.Delay(this.SleepTime);
    OperateResult<byte[]> resultReceive = await pipe.ReceiveMessageAsync(netMessage, sendValue).ConfigureAwait(false);
    if (!resultReceive.IsSuccess)
      return resultReceive;
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Receive} : {(this.LogMsgFormatBinary ? resultReceive.Content.ToHexString(' ') : Encoding.ASCII.GetString(resultReceive.Content))}");
    if (resultReceive.Content.Length == 6 && this.CheckResponseStatus(resultReceive.Content))
      resultReceive = await pipe.ReceiveMessageAsync(netMessage, sendValue).ConfigureAwait(false);
    if (netMessage == null || netMessage.CheckHeadBytesLegal((byte[]) null))
      return this.UnpackResponseContent(sendValue, resultReceive.Content);
    return new OperateResult<byte[]>($"{StringResources.Language.CommandHeadCodeCheckFailed}{Environment.NewLine}{StringResources.Language.Send}: {SoftBasic.ByteToHexString(send, ' ')}{Environment.NewLine}{StringResources.Language.Receive}: {SoftBasic.ByteToHexString(resultReceive.Content, ' ')}");
  }

  private bool CheckResponseStatus(byte[] content)
  {
    if (content.Length < 6)
      return false;
    for (int index = content.Length - 4; index < content.Length; ++index)
    {
      if (content[index] > (byte) 0)
        return false;
    }
    return true;
  }
}
