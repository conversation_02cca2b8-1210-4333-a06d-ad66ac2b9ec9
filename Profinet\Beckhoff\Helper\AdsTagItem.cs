﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Beckhoff.Helper.AdsTagItem
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Beckhoff.Helper;

/// <summary>Ads标签信息</summary>
public class AdsTagItem
{
  /// <summary>实例化一个默认的对象</summary>
  /// <param name="name">标签名</param>
  /// <param name="buffer">缓存的数据对象</param>
  /// <param name="typeLength">类型的单位字节长度信息</param>
  public AdsTagItem(string name, byte[] buffer, int typeLength)
  {
    this.TagName = name;
    this.Buffer = buffer;
    this.TypeLength = typeLength;
  }

  /// <summary>标签的名称</summary>
  public string TagName { get; set; }

  /// <summary>标签的数据缓存信息</summary>
  public byte[] Buffer { get; set; }

  /// <summary>绝对地址信息</summary>
  public uint Location { get; set; }

  /// <summary>类型的单位字节长度信息</summary>
  public int TypeLength { get; private set; }
}
