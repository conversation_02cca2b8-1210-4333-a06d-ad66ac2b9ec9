﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.GraphicRender
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>图形的呈现方式</summary>
public enum GraphicRender
{
  /// <summary>直方图</summary>
  Histogram = 1,
  /// <summary>饼图</summary>
  Piechart = 2,
  /// <summary>折线图</summary>
  Linegraph = 3,
}
