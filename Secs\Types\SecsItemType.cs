﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Types.SecsItemType
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Secs.Types;

/// <summary>数据类型的定义</summary>
public enum SecsItemType
{
  /// <summary>列表数据类型，代号：L</summary>
  List,
  /// <summary>Bool值类型，代号：BOOLEAN</summary>
  Bool,
  /// <summary>二进制数据，代号：B</summary>
  Binary,
  /// <summary>ASCII编码的字符串，代号：A</summary>
  ASCII,
  /// <summary>JIS类型，代号：J</summary>
  JIS8,
  /// <summary>一个字节的有符号数据，代号：I1</summary>
  SByte,
  /// <summary>一个字节的无符号数据，代号：U1</summary>
  Byte,
  /// <summary>两个字节的有符号数据，代号：I2</summary>
  Int16,
  /// <summary>两个字节的无符号数据，代号：U2</summary>
  UInt16,
  /// <summary>四个字节的有符号数据，代号：I4</summary>
  Int32,
  /// <summary>四个字节的无符号数据，代号：U4</summary>
  UInt32,
  /// <summary>八个字节的有符号数据，代号：I8</summary>
  Int64,
  /// <summary>八个字节的无符号数据，代号：U8</summary>
  UInt64,
  /// <summary>四个字节的浮点数，代号：F4</summary>
  Single,
  /// <summary>八个字节的浮点数，代号：F8</summary>
  Double,
  /// <summary>这是一个空的类型信息</summary>
  None,
}
