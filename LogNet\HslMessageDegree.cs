﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.HslMessageDegree
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>记录消息的等级</summary>
public enum HslMessageDegree
{
  /// <summary>一条消息都不记录</summary>
  None = 1,
  /// <summary>记录致命等级及以上日志的消息</summary>
  FATAL = 2,
  /// <summary>记录异常等级及以上日志的消息</summary>
  ERROR = 3,
  /// <summary>记录警告等级及以上日志的消息</summary>
  WARN = 4,
  /// <summary>记录信息等级及以上日志的消息</summary>
  INFO = 5,
  /// <summary>记录调试等级及以上日志的信息</summary>
  DEBUG = 6,
}
