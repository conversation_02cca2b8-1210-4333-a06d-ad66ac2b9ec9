﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.SecsHsms
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Secs.Helper;
using HslCommunication.Secs.Message;
using HslCommunication.Secs.Types;
using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Secs;

/// <summary>
/// HSMS的协议实现，SECS基于TCP的版本，实现了secs1和secs2规约的封装，可以收发任意的功能码数据，业务逻辑部分需要二次开发
/// </summary>
/// <remarks>
/// </remarks>
/// <example>
/// 下面就看看基本的操作内容
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Secs\SecsGemSample.cs" region="Sample1" title="基本的读写" />
/// 如果想要手动处理下设备主要返回的数据，比如报警之类的，可以参考下面的方法
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Secs\SecsGemSample.cs" region="Sample3" title="事件回调处理" />
/// 关于<see cref="T:HslCommunication.Secs.Types.SecsValue" />类型，可以非常灵活的实例化，参考下面的示例代码
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Secs\SecsGemSample.cs" region="Sample2" title="SecsValue说明" />
/// </example>
public class SecsHsms : NetworkDoubleBase, ISecs
{
  private Encoding stringEncoding = Encoding.Default;
  private SoftIncrementCount incrementCount;
  private List<uint> identityQAs = new List<uint>();

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// instantiate a default object
  /// </summary>
  public SecsHsms()
  {
    this.incrementCount = new SoftIncrementCount((long) uint.MaxValue, 1L);
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.UseServerActivePush = true;
    this.Gem = new Gem((ISecs) this);
  }

  /// <summary>
  /// 指定ip地址和端口号来实例化一个默认的对象<br />
  /// Specify the IP address and port number to instantiate a default object
  /// </summary>
  /// <param name="ipAddress">PLC的Ip地址</param>
  /// <param name="port">PLC的端口</param>
  public SecsHsms(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new SecsHsmsMessage();

  /// <summary>获取或设置当前的DeivceID信息</summary>
  public ushort DeviceID { get; set; }

  /// <summary>获取或设置当前的GEM信息，可以用来方便的调用一些常用的功能接口，或是自己实现自定义的接口方法</summary>
  public Gem Gem { get; set; }

  /// <summary>是否使用S0F0来初始化当前的设备对象信息</summary>
  public bool InitializationS0F0 { get; set; } = false;

  /// <summary>获取或设置用于字符串解析的编码信息</summary>
  public Encoding StringEncoding
  {
    get => this.stringEncoding;
    set => this.stringEncoding = value;
  }

  /// <summary>
  /// 获取或设置是否自动处理S1F1功能码的返回，默认为True:自动处理，实际还包含 S1F13, S2F17 的自动处理，如果需要手动处理这些功能码，请设置为 False
  /// </summary>
  /// <remarks>因为考虑代码的兼容性升级，所以遗留了本属性手动设置</remarks>
  public bool AutoBackS1F1 { get; set; } = true;

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect(Socket socket)
  {
    if (this.InitializationS0F0)
      this.Send(socket, Secs1.BuildHSMSMessage(ushort.MaxValue, (byte) 0, (byte) 0, (ushort) 1, (uint) this.incrementCount.GetCurrentValue(), (byte[]) null, false));
    return base.InitializationOnConnect(socket);
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync(Socket socket)
  {
    if (this.InitializationS0F0)
    {
      OperateResult operateResult1 = await this.SendAsync(socket, Secs1.BuildHSMSMessage(ushort.MaxValue, (byte) 0, (byte) 0, (ushort) 1, (uint) this.incrementCount.GetCurrentValue(), (byte[]) null, false));
    }
    OperateResult operateResult = await base.InitializationOnConnectAsync(socket);
    return operateResult;
  }

  /// <inheritdoc />
  protected override bool DecideWhetherQAMessage(Socket socket, OperateResult<byte[]> receive)
  {
    if (!receive.IsSuccess)
      return false;
    byte[] content = receive.Content;
    SecsMessage secsMessage;
    try
    {
      secsMessage = new SecsMessage(content, 4);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), "DecideWhetherQAMessage.SecsMessage.cor", ex);
      return false;
    }
    secsMessage.StringEncoding = this.stringEncoding;
    if (secsMessage.StreamNo == (byte) 0 && secsMessage.FunctionNo == (byte) 0 && secsMessage.BlockNo % 2 == 1)
    {
      this.Send(socket, Secs1.BuildHSMSMessage(ushort.MaxValue, (byte) 0, (byte) 0, (ushort) (secsMessage.BlockNo + 1), secsMessage.MessageID, (byte[]) null, false));
      return false;
    }
    if ((int) secsMessage.FunctionNo % 2 == 0 && secsMessage.FunctionNo > (byte) 0)
    {
      bool flag = false;
      lock (this.identityQAs)
        flag = this.identityQAs.Remove(secsMessage.MessageID);
      if (flag)
        return flag;
    }
    if (this.AutoBackS1F1)
    {
      if (secsMessage.StreamNo == (byte) 1 && secsMessage.FunctionNo == (byte) 13)
      {
        this.SendByCommand((byte) 1, (byte) 14, new SecsValue((IEnumerable<object>) new object[2]
        {
          (object) new byte[1],
          (object) SecsValue.EmptyListValue()
        }).ToSourceBytes(), false);
        return false;
      }
      if (secsMessage.StreamNo == (byte) 2 && secsMessage.FunctionNo == (byte) 17)
      {
        this.SendByCommand((byte) 2, (byte) 18, new SecsValue(DateTime.Now.ToString("yyyyMMddHHmmssff")), false);
        return false;
      }
      if (secsMessage.StreamNo == (byte) 1 && secsMessage.FunctionNo == (byte) 1)
      {
        this.SendByCommand((byte) 1, (byte) 2, SecsValue.EmptyListValue(), false);
        return false;
      }
    }
    SecsHsms.OnSecsMessageReceivedDelegate secsMessageReceived = this.OnSecsMessageReceived;
    if (secsMessageReceived != null)
      secsMessageReceived((object) this, secsMessage);
    return false;
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.SendByCommand(System.Byte,System.Byte,System.Byte[],System.Boolean)" />
  public OperateResult<uint> SendByCommand(byte stream, byte function, byte[] data, bool back)
  {
    uint currentValue = (uint) this.incrementCount.GetCurrentValue();
    return this.ReadFromCoreServer(Secs1.BuildHSMSMessage(this.DeviceID, stream, function, (ushort) 0, currentValue, data, back), false).Convert<uint>(currentValue);
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.SendByCommand(System.Byte,System.Byte,HslCommunication.Secs.Types.SecsValue,System.Boolean)" />
  public OperateResult<uint> SendByCommand(byte stream, byte function, SecsValue data, bool back)
  {
    return this.SendByCommand(stream, function, data.ToSourceBytes(this.stringEncoding), back);
  }

  /// <summary>
  /// 发送Secs数据到服务器上，根据收到的<see cref="T:HslCommunication.Secs.Types.SecsMessage" />消息来自动确定功能码，消息号信息
  /// </summary>
  /// <param name="secsMessage">收到的Secs消息对象</param>
  /// <param name="data">Secs数据对象</param>
  /// <param name="back">是否返回</param>
  /// <returns>是否发送成功</returns>
  public OperateResult SendByCommand(SecsMessage secsMessage, SecsValue data, bool back)
  {
    return (OperateResult) this.ReadFromCoreServer(Secs1.BuildHSMSMessage(this.DeviceID, secsMessage.StreamNo, (byte) ((uint) secsMessage.FunctionNo + 1U), (ushort) 0, secsMessage.MessageID, data.ToSourceBytes(this.stringEncoding), back), false);
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.ReadSecsMessage(System.Byte,System.Byte,System.Byte[],System.Boolean)" />
  public OperateResult<SecsMessage> ReadSecsMessage(
    byte stream,
    byte function,
    byte[] data,
    bool back)
  {
    uint currentValue = (uint) this.incrementCount.GetCurrentValue();
    lock (this.identityQAs)
      this.identityQAs.Add(currentValue);
    OperateResult<byte[]> result = this.ReadFromCoreServer(Secs1.BuildHSMSMessage(this.DeviceID, stream, function, (ushort) 0, currentValue, data, back));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<SecsMessage>((OperateResult) result);
    return OperateResult.CreateSuccessResult<SecsMessage>(new SecsMessage(result.Content, 4)
    {
      StringEncoding = this.stringEncoding
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.ReadSecsMessage(System.Byte,System.Byte,HslCommunication.Secs.Types.SecsValue,System.Boolean)" />
  public OperateResult<SecsMessage> ReadSecsMessage(
    byte stream,
    byte function,
    SecsValue data,
    bool back)
  {
    return this.ReadSecsMessage(stream, function, data.ToSourceBytes(this.stringEncoding), back);
  }

  /// <summary>
  /// 发送控制命令，需要指定 <paramref name="pType" /> 以及 <paramref name="sType" /> 参数信息，返回是否发送成功
  /// </summary>
  /// <param name="pType">控制码一</param>
  /// <param name="sType">控制码二</param>
  /// <returns>是否发送成功</returns>
  public OperateResult SendControlMessage(byte pType, byte sType)
  {
    return (OperateResult) this.ReadFromCoreServer(Secs1.BuildHSMSMessage(ushort.MaxValue, (byte) 0, (byte) 0, (ushort) ((uint) pType * 256U /*0x0100*/ + (uint) sType), (uint) this.incrementCount.GetCurrentValue(), (byte[]) null, true), false);
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.SendByCommand(System.Byte,System.Byte,System.Byte[],System.Boolean)" />
  public async Task<OperateResult> SendByCommandAsync(
    byte stream,
    byte function,
    byte[] data,
    bool back)
  {
    byte[] command = Secs1.BuildHSMSMessage(this.DeviceID, stream, function, (ushort) 0, (uint) this.incrementCount.GetCurrentValue(), data, back);
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command, false);
    OperateResult operateResult1 = (OperateResult) operateResult;
    command = (byte[]) null;
    return operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.SendByCommand(System.Byte,System.Byte,HslCommunication.Secs.Types.SecsValue,System.Boolean)" />
  public async Task<OperateResult> SendByCommandAsync(
    byte stream,
    byte function,
    SecsValue data,
    bool back)
  {
    OperateResult operateResult = await this.SendByCommandAsync(stream, function, data.ToSourceBytes(this.stringEncoding), back);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.ReadSecsMessage(System.Byte,System.Byte,System.Byte[],System.Boolean)" />
  public async Task<OperateResult<SecsMessage>> ReadSecsMessageAsync(
    byte stream,
    byte function,
    byte[] data,
    bool back)
  {
    uint identityQA = (uint) this.incrementCount.GetCurrentValue();
    lock (this.identityQAs)
      this.identityQAs.Add(identityQA);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(Secs1.BuildHSMSMessage(this.DeviceID, stream, function, (ushort) 0, identityQA, data, back));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<SecsMessage>((OperateResult) read);
    return OperateResult.CreateSuccessResult<SecsMessage>(new SecsMessage(read.Content, 4)
    {
      StringEncoding = this.stringEncoding
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.ISecs.ReadSecsMessage(System.Byte,System.Byte,HslCommunication.Secs.Types.SecsValue,System.Boolean)" />
  public async Task<OperateResult<SecsMessage>> ReadSecsMessageAsync(
    byte stream,
    byte function,
    SecsValue data,
    bool back)
  {
    OperateResult<SecsMessage> operateResult = await this.ReadSecsMessageAsync(stream, function, data.ToSourceBytes(this.stringEncoding), back);
    return operateResult;
  }

  /// <summary>当接收到非应答消息的时候触发的事件</summary>
  public event SecsHsms.OnSecsMessageReceivedDelegate OnSecsMessageReceived;

  /// <inheritdoc />
  public override string ToString() => $"SecsHsms[{this.IpAddress}:{this.Port}]";

  /// <summary>Secs消息接收的事件</summary>
  /// <param name="sender">数据的发送方</param>
  /// <param name="secsMessage">消息内容</param>
  public delegate void OnSecsMessageReceivedDelegate(object sender, SecsMessage secsMessage);
}
