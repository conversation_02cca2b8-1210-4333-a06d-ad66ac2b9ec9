﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.LogSaveMode
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>日志文件的存储模式</summary>
public enum LogSaveMode
{
  /// <summary>单个文件的存储模式</summary>
  SingleFile = 1,
  /// <summary>根据文件的大小来存储，固定一个大小，不停的生成文件</summary>
  FileFixedSize = 2,
  /// <summary>根据时间来存储，可以设置年，季，月，日，小时等等</summary>
  Time = 3,
}
