﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.OmronFinsAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Profinet.Omron;
using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>欧姆龙的Fins协议的地址类对象</summary>
public class OmronFinsAddress : DeviceAddressDataBase
{
  /// <summary>进行位操作的指令</summary>
  public byte BitCode { get; set; }

  /// <summary>进行字操作的指令</summary>
  public byte WordCode { get; set; }

  /// <summary>从指定的地址信息解析成真正的设备地址信息</summary>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  public override void Parse(string address, ushort length)
  {
    OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(address, length, OmronPlcType.CSCJ);
    if (!from.IsSuccess)
      return;
    this.AddressStart = from.Content.AddressStart;
    this.Length = from.Content.Length;
    this.BitCode = from.Content.BitCode;
    this.WordCode = from.Content.WordCode;
  }

  /// <summary>
  /// 从实际的欧姆龙的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual Omron address
  /// </summary>
  /// <param name="address">欧姆龙的地址数据信息</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<OmronFinsAddress> ParseFrom(string address)
  {
    return OmronFinsAddress.ParseFrom(address, (ushort) 0, OmronPlcType.CSCJ);
  }

  private static int CalculateBitIndex(string address)
  {
    string[] strArray = address.SplitDot();
    int bitIndex = (int) ushort.Parse(strArray[0]) * 16 /*0x10*/;
    if (strArray.Length > 1)
      bitIndex += HslHelper.CalculateBitStartIndex(strArray[1]);
    return bitIndex;
  }

  /// <summary>
  /// 从实际的欧姆龙的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual Omron address
  /// </summary>
  /// <param name="address">欧姆龙的地址数据信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="plcType">PLC的类型信息</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<OmronFinsAddress> ParseFrom(
    string address,
    ushort length,
    OmronPlcType plcType)
  {
    OmronFinsAddress omronFinsAddress = new OmronFinsAddress();
    try
    {
      omronFinsAddress.Length = length;
      if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase))
      {
        if (plcType == OmronPlcType.CV)
        {
          omronFinsAddress.WordCode = (byte) 156;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2)) + 48 /*0x30*/;
        }
        else
        {
          omronFinsAddress.WordCode = (byte) 188;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2)) + 8192 /*0x2000*/;
        }
      }
      else if (address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
      {
        omronFinsAddress.WordCode = (byte) 220;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2)) + 4096 /*0x1000*/;
      }
      else if (address.StartsWith("DM", StringComparison.OrdinalIgnoreCase))
      {
        omronFinsAddress.BitCode = OmronFinsDataType.DM.BitCode;
        omronFinsAddress.WordCode = OmronFinsDataType.DM.WordCode;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2));
      }
      else if (address.StartsWith("TIM") || address.StartsWith("tim"))
      {
        if (plcType == OmronPlcType.CV)
        {
          omronFinsAddress.BitCode = (byte) 1;
          omronFinsAddress.WordCode = (byte) 129;
        }
        else
        {
          omronFinsAddress.BitCode = OmronFinsDataType.TIM.BitCode;
          omronFinsAddress.WordCode = OmronFinsDataType.TIM.WordCode;
        }
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(3));
      }
      else if (address.StartsWith("CNT") || address.StartsWith("cnt"))
      {
        if (plcType == OmronPlcType.CV)
        {
          omronFinsAddress.BitCode = (byte) 1;
          omronFinsAddress.WordCode = (byte) 129;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(3)) + 32768 /*0x8000*/;
        }
        else
        {
          omronFinsAddress.BitCode = OmronFinsDataType.TIM.BitCode;
          omronFinsAddress.WordCode = OmronFinsDataType.TIM.WordCode;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(3)) + 524288 /*0x080000*/;
        }
      }
      else if (address.StartsWith("CIO") || address.StartsWith("cio"))
      {
        if (plcType == OmronPlcType.CV)
        {
          omronFinsAddress.BitCode = (byte) 0;
          omronFinsAddress.WordCode = (byte) 128 /*0x80*/;
        }
        else
        {
          omronFinsAddress.BitCode = OmronFinsDataType.CIO.BitCode;
          omronFinsAddress.WordCode = OmronFinsDataType.CIO.WordCode;
        }
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(3));
      }
      else if (address.StartsWith("WR") || address.StartsWith("wr"))
      {
        omronFinsAddress.BitCode = OmronFinsDataType.WR.BitCode;
        omronFinsAddress.WordCode = OmronFinsDataType.WR.WordCode;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2));
      }
      else if (address.StartsWith("HR") || address.StartsWith("hr"))
      {
        omronFinsAddress.BitCode = OmronFinsDataType.HR.BitCode;
        omronFinsAddress.WordCode = OmronFinsDataType.HR.WordCode;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2));
      }
      else if (address.StartsWith("AR") || address.StartsWith("ar"))
      {
        if (plcType == OmronPlcType.CV)
        {
          omronFinsAddress.BitCode = (byte) 0;
          omronFinsAddress.WordCode = (byte) 128 /*0x80*/;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2)) + 45056 /*0xB000*/;
        }
        else
        {
          omronFinsAddress.BitCode = OmronFinsDataType.AR.BitCode;
          omronFinsAddress.WordCode = OmronFinsDataType.AR.WordCode;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2));
        }
      }
      else if (address.StartsWith("CF") || address.StartsWith("cf"))
      {
        omronFinsAddress.BitCode = (byte) 7;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(2));
      }
      else if (address.StartsWith("EM") || address.StartsWith("em") || address.StartsWith("E") || address.StartsWith("e"))
      {
        if (address.IndexOf('.') > 0)
        {
          int int32 = Convert.ToInt32(address.SplitDot()[0].Substring(address[1] == 'M' || address[1] == 'm' ? 2 : 1), 16 /*0x10*/);
          if (int32 < 16 /*0x10*/)
          {
            omronFinsAddress.BitCode = (byte) (32 /*0x20*/ + int32);
            omronFinsAddress.WordCode = plcType != OmronPlcType.CV ? (byte) (160 /*0xA0*/ + int32) : (byte) (144 /*0x90*/ + int32);
          }
          else
          {
            omronFinsAddress.BitCode = (byte) (224 /*0xE0*/ + int32 - 16 /*0x10*/);
            omronFinsAddress.WordCode = (byte) (96 /*0x60*/ + int32 - 16 /*0x10*/);
          }
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(address.IndexOf('.') + 1));
        }
        else
        {
          omronFinsAddress.BitCode = (byte) 10;
          omronFinsAddress.WordCode = (byte) 152;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(address[1] == 'M' || address[1] == 'm' ? 2 : 1));
        }
      }
      else if (address.StartsWith("D") || address.StartsWith("d"))
      {
        omronFinsAddress.BitCode = OmronFinsDataType.DM.BitCode;
        omronFinsAddress.WordCode = OmronFinsDataType.DM.WordCode;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(1));
      }
      else if (address.StartsWith("C") || address.StartsWith("c"))
      {
        if (plcType == OmronPlcType.CV)
        {
          omronFinsAddress.BitCode = (byte) 0;
          omronFinsAddress.WordCode = (byte) 128 /*0x80*/;
        }
        else
        {
          omronFinsAddress.BitCode = OmronFinsDataType.CIO.BitCode;
          omronFinsAddress.WordCode = OmronFinsDataType.CIO.WordCode;
        }
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(1));
      }
      else if (address.StartsWith("W") || address.StartsWith("w"))
      {
        omronFinsAddress.BitCode = OmronFinsDataType.WR.BitCode;
        omronFinsAddress.WordCode = OmronFinsDataType.WR.WordCode;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(1));
      }
      else if (address.StartsWith("H") || address.StartsWith("h"))
      {
        omronFinsAddress.BitCode = OmronFinsDataType.HR.BitCode;
        omronFinsAddress.WordCode = OmronFinsDataType.HR.WordCode;
        omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(1));
      }
      else
      {
        if (!address.StartsWith("A") && !address.StartsWith("a"))
          throw new Exception(StringResources.Language.NotSupportedDataType);
        if (plcType == OmronPlcType.CV)
        {
          omronFinsAddress.BitCode = (byte) 0;
          omronFinsAddress.WordCode = (byte) 128 /*0x80*/;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(1)) + 45056 /*0xB000*/;
        }
        else
        {
          omronFinsAddress.BitCode = OmronFinsDataType.AR.BitCode;
          omronFinsAddress.WordCode = OmronFinsDataType.AR.WordCode;
          omronFinsAddress.AddressStart = OmronFinsAddress.CalculateBitIndex(address.Substring(1));
        }
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<OmronFinsAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
    return OperateResult.CreateSuccessResult<OmronFinsAddress>(omronFinsAddress);
  }
}
