﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.VigorAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>丰炜PLC的地址类对象</summary>
public class VigorAddress : DeviceAddressDataBase
{
  /// <summary>
  /// 获取或设置等待读取的数据的代码<br />
  /// Get or set the code of the data waiting to be read
  /// </summary>
  public byte DataCode { get; set; }

  /// <inheritdoc />
  public override string ToString() => this.AddressStart.ToString();

  /// <summary>
  /// 从实际的丰炜PLC的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual Vigor address
  /// </summary>
  /// <param name="address">丰炜的地址数据信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="isBit">是否是对位进行访问的</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<VigorAddress> ParseFrom(string address, ushort length, bool isBit)
  {
    VigorAddress vigorAddress = new VigorAddress();
    try
    {
      vigorAddress.Length = length;
      if (isBit)
      {
        if (address.StartsWith("SM") || address.StartsWith("sm"))
        {
          vigorAddress.DataCode = (byte) 148;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(2));
        }
        else if (address.StartsWith("TS") || address.StartsWith("ts"))
        {
          vigorAddress.DataCode = (byte) 153;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(2));
        }
        else if (address.StartsWith("TC") || address.StartsWith("tc"))
        {
          vigorAddress.DataCode = (byte) 152;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(2));
        }
        else if (address.StartsWith("CS") || address.StartsWith("cs"))
        {
          vigorAddress.DataCode = (byte) 157;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(2));
        }
        else if (address.StartsWith("CC") || address.StartsWith("cc"))
        {
          vigorAddress.DataCode = (byte) 156;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(2));
        }
        else if (address.StartsWith("X") || address.StartsWith("x"))
        {
          vigorAddress.DataCode = (byte) 144 /*0x90*/;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(1));
        }
        else if (address.StartsWith("Y") || address.StartsWith("y"))
        {
          vigorAddress.DataCode = (byte) 145;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(1));
        }
        else if (address.StartsWith("M") || address.StartsWith("m"))
        {
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(1));
          if (vigorAddress.AddressStart >= 9000)
          {
            vigorAddress.AddressStart = 0;
            vigorAddress.DataCode = (byte) 148;
          }
          else
            vigorAddress.DataCode = (byte) 146;
        }
        else
        {
          if (!address.StartsWith("S") && !address.StartsWith("s"))
            return new OperateResult<VigorAddress>(StringResources.Language.NotSupportedDataType);
          vigorAddress.DataCode = (byte) 147;
          vigorAddress.AddressStart = Convert.ToInt32(address.Substring(1));
        }
      }
      else if (address.StartsWith("SD") || address.StartsWith("sd"))
      {
        vigorAddress.DataCode = (byte) 161;
        vigorAddress.AddressStart = Convert.ToInt32(address.Substring(2));
      }
      else if (address.StartsWith("D") || address.StartsWith("d"))
      {
        vigorAddress.AddressStart = Convert.ToInt32(address.Substring(1));
        if (vigorAddress.AddressStart >= 9000)
        {
          vigorAddress.DataCode = (byte) 161;
          vigorAddress.AddressStart -= 9000;
        }
        else
          vigorAddress.DataCode = (byte) 160 /*0xA0*/;
      }
      else if (address.StartsWith("R") || address.StartsWith("r"))
      {
        vigorAddress.DataCode = (byte) 162;
        vigorAddress.AddressStart = Convert.ToInt32(address.Substring(1));
      }
      else if (address.StartsWith("T") || address.StartsWith("t"))
      {
        vigorAddress.DataCode = (byte) 168;
        vigorAddress.AddressStart = Convert.ToInt32(address.Substring(1));
      }
      else
      {
        vigorAddress.AddressStart = address.StartsWith("C") || address.StartsWith("c") ? Convert.ToInt32(address.Substring(1)) : throw new Exception(StringResources.Language.NotSupportedDataType);
        vigorAddress.DataCode = vigorAddress.AddressStart < 200 ? (byte) 172 : (byte) 173;
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<VigorAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
    return OperateResult.CreateSuccessResult<VigorAddress>(vigorAddress);
  }
}
