﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslDeviceAddressAttribute
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>
/// 应用于Hsl组件库读取的动态地址解析，具体用法为创建一个类，创建数据属性，如果这个属性需要绑定PLC的真实数据，就在属性的特性上应用本特性。<br />
/// Applied to the dynamic address resolution read by the Hsl component library, the specific usage is to create a class and create data attributes.
/// If this attribute needs to be bound to the real data of the PLC, this feature is applied to the characteristics of the attribute.
/// </summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = true)]
public class HslDeviceAddressAttribute : Attribute
{
  /// <summary>
  /// 设备的类型，如果指定了特殊的PLC，那么该地址就可以支持多种不同PLC<br />
  /// The type of equipment, if a special PLC is specified, then the address can support a variety of different PLCs
  /// </summary>
  public Type DeviceType { get; set; }

  /// <summary>
  /// 数据的地址信息，真实的设备的地址信息<br />
  /// Data address information, real device address information
  /// </summary>
  public string Address { get; }

  /// <summary>
  /// 读取的数据长度<br />
  /// Length of data read
  /// </summary>
  public int Length { get; }

  /// <summary>如果关联了字符串类型的数据，则表示指定的字符编码，默认 ASCII 编码</summary>
  public string Encoding { get; set; } = "ASCII";

  /// <summary>
  /// 实例化一个地址特性，指定地址信息，用于单变量的数据<br />
  /// Instantiate an address feature, specify the address information, for single variable data
  /// </summary>
  /// <param name="address">真实的地址信息</param>
  public HslDeviceAddressAttribute(string address)
  {
    this.Address = address;
    this.Length = -1;
    this.DeviceType = (Type) null;
  }

  /// <summary>
  /// 实例化一个地址特性，指定地址信息，用于单变量的数据，并指定设备类型<br />
  /// Instantiate an address feature, specify address information, data for a single variable, and specify the device type
  /// </summary>
  /// <param name="address">真实的地址信息</param>
  /// <param name="deviceType">设备的地址信息</param>
  public HslDeviceAddressAttribute(string address, Type deviceType)
  {
    this.Address = address;
    this.Length = -1;
    this.DeviceType = deviceType;
  }

  /// <inheritdoc cref="M:HslCommunication.Reflection.HslDeviceAddressAttribute.#ctor(System.String,System.Int32,System.String)" />
  public HslDeviceAddressAttribute(string address, int length)
  {
    this.Address = address;
    this.Length = length;
    this.DeviceType = (Type) null;
  }

  /// <summary>
  /// 实例化一个地址特性，指定地址信息和数据长度，通常应用于数组的批量读取<br />
  /// Instantiate an address feature, specify address information and data length, usually used in batch reading of arrays
  /// </summary>
  /// <param name="address">真实的地址信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="encoding">如果是字符串类型的数据的话，就是字符串编码</param>
  public HslDeviceAddressAttribute(string address, int length, string encoding)
  {
    this.Address = address;
    this.Length = length;
    this.DeviceType = (Type) null;
    this.Encoding = encoding;
  }

  /// <inheritdoc cref="M:HslCommunication.Reflection.HslDeviceAddressAttribute.#ctor(System.String,System.Int32,System.Type,System.String)" />
  public HslDeviceAddressAttribute(string address, int length, Type deviceType)
  {
    this.Address = address;
    this.Length = length;
    this.DeviceType = deviceType;
  }

  /// <summary>
  /// 实例化一个地址特性，指定地址信息和数据长度，通常应用于数组的批量读取，并指定设备的类型，可用于不同种类的PLC<br />
  /// Instantiate an address feature, specify address information and data length, usually used in batch reading of arrays,
  /// and specify the type of equipment, which can be used for different types of PLCs
  /// </summary>
  /// <param name="address">真实的地址信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="deviceType">设备类型</param>
  /// <param name="encoding">如果是字符串类型的数据的话，就是字符串编码</param>
  public HslDeviceAddressAttribute(string address, int length, Type deviceType, string encoding)
  {
    this.Address = address;
    this.Length = length;
    this.DeviceType = deviceType;
    this.Encoding = encoding;
  }

  /// <summary>
  /// 获取数据的数量信息，如果小于0，则返回1<b />
  /// Get the quantity information of the data, if it is less than 0, return 1
  /// </summary>
  /// <returns>数据的个数</returns>
  public int GetDataLength() => this.Length < 0 ? 1 : this.Length;

  /// <inheritdoc />
  public override string ToString() => $"HslDeviceAddressAttribute[{this.Address}:{this.Length}]";

  /// <summary>
  /// 获取当前关联的编码信息，通常用于解析字符串的操作<br />
  /// Gets the encoding information of the current association, usually used for string parsing operations
  /// </summary>
  /// <returns>字符编码信息</returns>
  public System.Text.Encoding GetEncoding()
  {
    if (this.Encoding.Equals("ASCII", StringComparison.OrdinalIgnoreCase))
      return System.Text.Encoding.ASCII;
    if (this.Encoding.Equals("Unicode", StringComparison.OrdinalIgnoreCase))
      return System.Text.Encoding.Unicode;
    if (this.Encoding.Equals("BigEndianUnicode", StringComparison.OrdinalIgnoreCase))
      return System.Text.Encoding.BigEndianUnicode;
    if (this.Encoding.Equals("UTF8", StringComparison.OrdinalIgnoreCase))
      return System.Text.Encoding.UTF8;
    if (this.Encoding.Equals("ANSI", StringComparison.OrdinalIgnoreCase))
      return System.Text.Encoding.Default;
    if (this.Encoding.Equals("UTF32", StringComparison.OrdinalIgnoreCase))
      return System.Text.Encoding.UTF32;
    if (this.Encoding.Equals("UTF7", StringComparison.OrdinalIgnoreCase))
      return System.Text.Encoding.UTF7;
    return this.Encoding.Equals("GB2312", StringComparison.OrdinalIgnoreCase) ? System.Text.Encoding.GetEncoding("gb2312") : System.Text.Encoding.GetEncoding(this.Encoding);
  }
}
