﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.LSCpuStatus
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.LSIS;

/// <summary>Cpu status</summary>
public enum LSCpuStatus
{
  /// <summary>运行中</summary>
  RUN = 1,
  /// <summary>运行停止</summary>
  STOP = 2,
  /// <summary>错误状态</summary>
  ERROR = 3,
  /// <summary>调试模式</summary>
  DEBUG = 4,
}
