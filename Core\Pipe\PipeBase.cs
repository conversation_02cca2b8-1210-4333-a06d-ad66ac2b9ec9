﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeBase
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>管道的基础类对象</summary>
public class PipeBase : IDisposable
{
  private SimpleHybirdLock hybirdLock;

  /// <summary>实例化一个默认的对象</summary>
  public PipeBase() => this.hybirdLock = new SimpleHybirdLock();

  /// <inheritdoc cref="M:HslCommunication.Core.SimpleHybirdLock.Enter" />
  public bool PipeLockEnter() => this.hybirdLock.Enter();

  /// <inheritdoc cref="M:HslCommunication.Core.SimpleHybirdLock.Leave" />
  public bool PipeLockLeave() => this.hybirdLock.Leave();

  /// <inheritdoc cref="P:HslCommunication.Core.SimpleHybirdLock.LockingTick" />
  public int LockingTick => this.hybirdLock.LockingTick;

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public virtual void Dispose() => this.hybirdLock?.Dispose();
}
