﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.RKC.TemperatureControllerServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

#nullable disable
namespace HslCommunication.Instrument.RKC;

/// <summary>
/// RKC的CD/CH系列数字式温度控制器的虚拟设备，可以读取测量值，CT1输入值，CT2输入值等等，地址的地址需要参考API文档的示例<br />
/// The serial port object of RKC's CD/CH series digital temperature controller can read the measured value, CT1 input value,
/// CT2 input value, etc. The address of the address needs to refer to the example of the API document
/// </summary>
/// <remarks>
/// 只能使用ReadDouble(string),Write(string,double)方法来读写数据，设备的串口默认参数为 8-1-N,8 个数据位，一个停止位，无奇偶校验<br />
/// 地址支持站号信息，例如 s=2;M1
/// </remarks>
public class TemperatureControllerServer : DeviceServer
{
  private List<string> tagList;
  private string readTag = string.Empty;
  private byte station = 0;
  private Dictionary<string, double> tagValues;

  /// <summary>实例化一个默认的方法</summary>
  public TemperatureControllerServer()
  {
    string[] collection = new string[26]
    {
      "M1",
      "M2",
      "M3",
      "AA",
      "AB",
      "B1",
      "ER",
      "SR",
      "G1",
      "S1",
      "A1",
      "A2",
      "A3",
      "A4",
      "A5",
      "P1",
      "I1",
      "D1",
      "W1",
      "P2",
      "V1",
      "T0",
      "T1",
      "G2",
      "PB",
      "LK"
    };
    this.tagValues = new Dictionary<string, double>();
    this.tagList = new List<string>((IEnumerable<string>) collection);
    foreach (string key in collection)
      this.tagValues.Add(key, 0.0);
    this.LogMsgFormatBinary = false;
  }

  /// <summary>获取或设置当前的站号信息</summary>
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc />
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    string[] strArray = address.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
    double[] numArray = new double[strArray.Length];
    for (int index = 0; index < strArray.Length; ++index)
    {
      if (!this.tagValues.ContainsKey(strArray[index]))
        return new OperateResult<double[]>($"Tag[{strArray[index]}] is not exist");
      numArray[index] = this.tagValues[strArray[index]];
    }
    return OperateResult.CreateSuccessResult<double[]>(numArray);
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, double[] values)
  {
    string[] strArray = address.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
    for (int index = 0; index < strArray.Length; ++index)
    {
      if (!this.tagValues.ContainsKey(strArray[index]))
        return (OperateResult) new OperateResult<double[]>($"Tag[{strArray[index]}] is not exist");
      this.tagValues[strArray[index]] = values[index];
    }
    return OperateResult.CreateSuccessResult();
  }

  private OperateResult<byte[]> CreateResponseByAddress(string add)
  {
    if (!this.tagValues.ContainsKey(add))
      return new OperateResult<byte[]>($"Read tag [{add}] is not exist");
    this.readTag = add;
    List<byte> byteList = new List<byte>(20);
    byteList.Add((byte) 2);
    byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(add));
    byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(this.tagValues[add].ToString().PadLeft(6, '0').Substring(0, 6)));
    byteList.Add((byte) 3);
    int num = (int) byteList[3];
    for (int index = 4; index < byteList.Count; ++index)
      num ^= (int) byteList[index];
    byteList.Add((byte) num);
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length == 1)
    {
      if (!string.IsNullOrEmpty(this.readTag))
      {
        if (receive[0] == (byte) 6)
        {
          int index = this.tagList.IndexOf(this.readTag) + 1;
          if (index >= this.tagList.Count)
            index = 0;
          return this.CreateResponseByAddress(this.tagList[index]);
        }
        if (receive[0] == (byte) 21)
          return this.CreateResponseByAddress(this.readTag);
      }
      return new OperateResult<byte[]>("not legal: " + receive.ToHexString(' '));
    }
    byte num1 = byte.Parse(Encoding.ASCII.GetString(receive, 1, 2));
    if ((int) num1 != (int) this.Station)
      return new OperateResult<byte[]>($"Station not match, need [{this.Station}] but [{num1}]");
    if (receive[3] != (byte) 2)
      return this.CreateResponseByAddress(Encoding.ASCII.GetString(receive, 3, 2));
    if (!this.EnableWrite)
      return new OperateResult<byte[]>("Not allow client write");
    string key = Encoding.ASCII.GetString(receive, 4, 2);
    double num2 = double.Parse(Encoding.ASCII.GetString(receive, 6, receive.Length - 8));
    if (!this.tagValues.ContainsKey(key))
      return new OperateResult<byte[]>($"Write tag [{key}] is not exist");
    this.tagValues[key] = num2;
    return OperateResult.CreateSuccessResult<byte[]>(new byte[1]
    {
      (byte) 6
    });
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    MemoryStream ms = new MemoryStream();
    ms.Write(buffer.SelectBegin<byte>(receivedLength));
    return new RkcTemperatureMessage().CheckReceiveDataComplete((byte[]) null, ms);
  }

  /// <inheritdoc />
  public override string ToString() => $"TemperatureControllerServer[{this.Port}]";
}
