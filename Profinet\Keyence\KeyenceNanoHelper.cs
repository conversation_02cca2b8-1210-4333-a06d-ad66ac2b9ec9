﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Keyence.KeyenceNanoHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Keyence;

/// <summary>KeyenceNano的基本辅助方法</summary>
public class KeyenceNanoHelper
{
  /// <summary>
  /// 连接PLC的命令报文<br />
  /// Command message to connect to PLC
  /// </summary>
  /// <param name="station">当前PLC的站号信息</param>
  /// <param name="useStation">是否启动站号命令</param>
  public static byte[] GetConnectCmd(byte station, bool useStation)
  {
    return !useStation ? Encoding.ASCII.GetBytes("CR\r") : Encoding.ASCII.GetBytes($"CR {station:D2}\r");
  }

  /// <summary>
  /// 断开PLC连接的命令报文<br />
  /// Command message to disconnect PLC
  /// </summary>
  /// <param name="station">当前PLC的站号信息</param>
  /// <param name="useStation">是否启动站号命令</param>
  public static byte[] GetDisConnectCmd(byte station, bool useStation)
  {
    return Encoding.ASCII.GetBytes("CQ\r");
  }

  /// <summary>获取当前的地址类型是字数据的倍数关系</summary>
  /// <param name="type">地址的类型</param>
  /// <returns>倍数关系</returns>
  public static int GetWordAddressMultiple(string type)
  {
    if (type == "CTH" || type == "CTC" || type == "C" || type == "T" || type == "TS" || type == "TC" || type == "CS" || type == "CC" || type == "AT")
      return 2;
    return type == "DM" || type == "CM" || type == "TM" || type == "EM" || type == "FM" || type == "Z" || type == "W" || type == "ZF" || type == "VM" ? 1 : 1;
  }

  /// <summary>
  /// 建立读取PLC数据的指令，需要传入地址数据，以及读取的长度，地址示例参照类的说明文档<br />
  /// To create a command to read PLC data, you need to pass in the address data, and the length of the read. For an example of the address, refer to the class documentation
  /// </summary>
  /// <param name="address">软元件地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>是否建立成功</returns>
  public static OperateResult<List<byte[]>> BuildReadCommand(string address, ushort length)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, length);
    return !from.IsSuccess ? OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from) : KeyenceNanoHelper.BuildReadCommand(from.Content, length, false);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.BuildReadCommand(System.String,System.UInt16)" />
  public static OperateResult<List<byte[]>> BuildReadCommand(
    KeyenceNanoAddress address,
    ushort length,
    bool isBit)
  {
    if (length > (ushort) 1)
      length /= (ushort) KeyenceNanoHelper.GetWordAddressMultiple(address.DataCode);
    int[] array = SoftBasic.SplitIntegerToArray((int) length, address.SplitLength);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("RDS");
      stringBuilder.Append(" ");
      stringBuilder.Append(address.DataCode);
      stringBuilder.Append(address.GetAddressStartFormat());
      if (!isBit && address.IsBitAddressDefault())
        stringBuilder.Append(".U");
      stringBuilder.Append(" ");
      stringBuilder.Append(array[index].ToString());
      stringBuilder.Append("\r");
      numArrayList.Add(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
      address.AddressStart += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>
  /// 建立写入PLC数据的指令，需要传入地址数据，以及写入的数据信息，地址示例参照类的说明文档<br />
  /// To create a command to write PLC data, you need to pass in the address data and the written data information. For an example of the address, refer to the class documentation
  /// </summary>
  /// <param name="address">软元件地址</param>
  /// <param name="value">转换后的数据</param>
  /// <returns>是否成功的信息</returns>
  public static OperateResult<byte[]> BuildWriteCommand(string address, byte[] value)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append("WRS");
    stringBuilder.Append(" ");
    stringBuilder.Append(from.Content.DataCode);
    stringBuilder.Append(from.Content.GetAddressStartFormat());
    if (from.Content.IsBitAddressDefault())
      stringBuilder.Append(".U");
    stringBuilder.Append(" ");
    int num = value.Length / (KeyenceNanoHelper.GetWordAddressMultiple(from.Content.DataCode) * 2);
    stringBuilder.Append(num.ToString());
    for (int index = 0; index < num; ++index)
    {
      stringBuilder.Append(" ");
      stringBuilder.Append(BitConverter.ToUInt16(value, index * KeyenceNanoHelper.GetWordAddressMultiple(from.Content.DataCode) * 2));
    }
    stringBuilder.Append("\r");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>
  /// 构建写入扩展单元缓冲寄存器的报文命令，需要传入单元编号，地址，写入的数据，实际写入的数据格式才有无符号的方式<br />
  /// To construct a message command to write to the buffer register of the expansion unit, the unit number, address,
  /// and data to be written need to be passed in, and the format of the actually written data is unsigned.
  /// </summary>
  /// <param name="unit">单元编号0~48</param>
  /// <param name="address">地址0~32767</param>
  /// <param name="value">写入的数据信息，单次交互最大256个字</param>
  /// <returns>包含是否成功的报文对象</returns>
  public static OperateResult<byte[]> BuildWriteExpansionMemoryCommand(
    byte unit,
    ushort address,
    byte[] value)
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append("UWR");
    stringBuilder.Append(" ");
    stringBuilder.Append(unit);
    stringBuilder.Append(" ");
    stringBuilder.Append(address);
    stringBuilder.Append(".U");
    stringBuilder.Append(" ");
    int num = value.Length / 2;
    stringBuilder.Append(num.ToString());
    for (int index = 0; index < num; ++index)
    {
      stringBuilder.Append(" ");
      stringBuilder.Append(BitConverter.ToUInt16(value, index * 2));
    }
    stringBuilder.Append("\r");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>
  /// 建立写入bool数据的指令，针对地址类型为 R,CR,MR,LR<br />
  /// Create instructions to write bool data, address type is R, CR, MR, LR
  /// </summary>
  /// <param name="address">软元件地址</param>
  /// <param name="value">转换后的数据</param>
  /// <returns>是否成功的信息</returns>
  public static OperateResult<byte[]> BuildWriteCommand(string address, bool value)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    StringBuilder stringBuilder = new StringBuilder();
    if (value)
      stringBuilder.Append("ST");
    else
      stringBuilder.Append("RS");
    stringBuilder.Append(" ");
    stringBuilder.Append(from.Content.DataCode);
    stringBuilder.Append(from.Content.GetAddressStartFormat());
    stringBuilder.Append("\r");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>
  /// 批量写入数据位到plc地址，针对地址格式为 R,B,CR,MR,LR,VB<br />
  /// Write data bits in batches to the plc address, and the address format is R, B, CR, MR, LR, VB
  /// </summary>
  /// <param name="address">PLC的地址</param>
  /// <param name="value">等待写入的bool数组</param>
  /// <returns>写入bool数组的命令报文</returns>
  public static OperateResult<byte[]> BuildWriteCommand(string address, bool[] value)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append("WRS");
    stringBuilder.Append(" ");
    stringBuilder.Append(from.Content.DataCode);
    stringBuilder.Append(from.Content.GetAddressStartFormat());
    stringBuilder.Append(" ");
    stringBuilder.Append(value.Length.ToString());
    for (int index = 0; index < value.Length; ++index)
    {
      stringBuilder.Append(" ");
      stringBuilder.Append(value[index] ? "1" : "0");
    }
    stringBuilder.Append("\r");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  internal static string GetErrorText(string err)
  {
    if (err.StartsWith("E0"))
      return StringResources.Language.KeyenceNanoE0;
    if (err.StartsWith("E1"))
      return StringResources.Language.KeyenceNanoE1;
    if (err.StartsWith("E2"))
      return StringResources.Language.KeyenceNanoE2;
    if (err.StartsWith("E4"))
      return StringResources.Language.KeyenceNanoE4;
    if (err.StartsWith("E5"))
      return StringResources.Language.KeyenceNanoE5;
    return err.StartsWith("E6") ? StringResources.Language.KeyenceNanoE6 : $"{StringResources.Language.UnknownError} {err}";
  }

  /// <summary>
  /// 校验读取返回数据状态，主要返回的第一个字节是不是E<br />
  /// Check the status of the data returned from reading, whether the first byte returned is E
  /// </summary>
  /// <param name="ack">反馈信息</param>
  /// <returns>是否成功的信息</returns>
  public static OperateResult CheckPlcReadResponse(byte[] ack)
  {
    try
    {
      if (ack.Length == 0)
        return new OperateResult(StringResources.Language.MelsecFxReceiveZero);
      if (ack[0] == (byte) 69)
        return new OperateResult(KeyenceNanoHelper.GetErrorText(Encoding.ASCII.GetString(ack)));
      return ack[ack.Length - 1] != (byte) 10 && ack[ack.Length - 2] != (byte) 13 ? new OperateResult($"{StringResources.Language.MelsecFxAckWrong} Actual: {SoftBasic.ByteToHexString(ack, ' ')}") : OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return new OperateResult($"CheckPlcReadResponse failed: {ex.Message}{Environment.NewLine}{ack.ToHexString(' ')}");
    }
  }

  /// <summary>
  /// 校验写入返回数据状态，检测返回的数据是不是OK<br />
  /// Verify the status of the returned data written and check whether the returned data is OK
  /// </summary>
  /// <param name="ack">反馈信息</param>
  /// <returns>是否成功的信息</returns>
  public static OperateResult CheckPlcWriteResponse(byte[] ack)
  {
    try
    {
      if (ack.Length == 0)
        return new OperateResult(StringResources.Language.MelsecFxReceiveZero);
      return ack[0] == (byte) 79 && ack[1] == (byte) 75 ? OperateResult.CreateSuccessResult() : new OperateResult(KeyenceNanoHelper.GetErrorText(Encoding.ASCII.GetString(ack)));
    }
    catch (Exception ex)
    {
      return new OperateResult($"CheckPlcWriteResponse failed: {ex.Message}{Environment.NewLine}{ack.ToHexString(' ')}");
    }
  }

  /// <summary>
  /// 从PLC反馈的数据进行提炼Bool操作<br />
  /// Refine Bool operation from data fed back from PLC
  /// </summary>
  /// <param name="addressType">地址的数据类型</param>
  /// <param name="response">PLC反馈的真实数据</param>
  /// <returns>数据提炼后的真实数据</returns>
  public static OperateResult<bool[]> ExtractActualBoolData(string addressType, byte[] response)
  {
    try
    {
      if (string.IsNullOrEmpty(addressType))
        addressType = "R";
      string str = Encoding.Default.GetString(response.RemoveLast<byte>(2));
      if (addressType == "R" || addressType == "CR" || addressType == "MR" || addressType == "LR" || addressType == "B" || addressType == "VB")
        return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<string>) str.Split(new char[1]
        {
          ' '
        }, StringSplitOptions.RemoveEmptyEntries)).Select<string, bool>((Func<string, bool>) (m => m == "1")).ToArray<bool>());
      if (!(addressType == "T") && !(addressType == "C") && !(addressType == "CTH") && !(addressType == "CTC"))
        return new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
      return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<string>) str.Split(new char[1]
      {
        ' '
      }, StringSplitOptions.RemoveEmptyEntries)).Select<string, bool>((Func<string, bool>) (m => m.StartsWith("1"))).ToArray<bool>());
    }
    catch (Exception ex)
    {
      OperateResult<bool[]> actualBoolData = new OperateResult<bool[]>();
      actualBoolData.Message = $"Extract Msg：{ex.Message}{Environment.NewLine}Data: {SoftBasic.ByteToHexString(response)}";
      return actualBoolData;
    }
  }

  /// <summary>
  /// 从PLC反馈的数据进行提炼操作<br />
  /// Refining operation from data fed back from PLC
  /// </summary>
  /// <param name="addressType">地址的数据类型</param>
  /// <param name="response">PLC反馈的真实数据</param>
  /// <returns>数据提炼后的真实数据</returns>
  public static OperateResult<byte[]> ExtractActualData(string addressType, byte[] response)
  {
    try
    {
      if (string.IsNullOrEmpty(addressType))
        addressType = "R";
      string[] strArray = Encoding.Default.GetString(response.RemoveLast<byte>(2)).Split(new char[1]
      {
        ' '
      }, StringSplitOptions.RemoveEmptyEntries);
      if (addressType == "DM" || addressType == "EM" || addressType == "FM" || addressType == "ZF" || addressType == "W" || addressType == "TM" || addressType == "Z" || addressType == "CM" || addressType == "VM")
      {
        byte[] numArray = new byte[strArray.Length * 2];
        for (int index = 0; index < strArray.Length; ++index)
          BitConverter.GetBytes(ushort.Parse(strArray[index])).CopyTo((Array) numArray, index * 2);
        return OperateResult.CreateSuccessResult<byte[]>(numArray);
      }
      if (addressType == "AT" || addressType == "TC" || addressType == "CC" || addressType == "TS" || addressType == "CS")
      {
        byte[] numArray = new byte[strArray.Length * 4];
        for (int index = 0; index < strArray.Length; ++index)
          BitConverter.GetBytes(uint.Parse(strArray[index])).CopyTo((Array) numArray, index * 4);
        return OperateResult.CreateSuccessResult<byte[]>(numArray);
      }
      if (addressType == "T" || addressType == "C" || addressType == "CTH" || addressType == "CTC")
      {
        byte[] numArray = new byte[strArray.Length * 4];
        for (int index = 0; index < strArray.Length; ++index)
          BitConverter.GetBytes(uint.Parse(strArray[index].Split(new char[1]
          {
            ','
          }, StringSplitOptions.RemoveEmptyEntries)[1])).CopyTo((Array) numArray, index * 4);
        return OperateResult.CreateSuccessResult<byte[]>(numArray);
      }
      if (!(addressType == "R") && !(addressType == "B") && !(addressType == "MR") && !(addressType == "LR") && !(addressType == "CR") && !(addressType == "VB"))
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      byte[] numArray1 = new byte[strArray.Length * 2];
      for (int index = 0; index < strArray.Length; ++index)
        BitConverter.GetBytes(ushort.Parse(strArray[index])).CopyTo((Array) numArray1, index * 2);
      return OperateResult.CreateSuccessResult<byte[]>(numArray1);
    }
    catch (Exception ex)
    {
      OperateResult<byte[]> actualData = new OperateResult<byte[]>();
      actualData.Message = $"Extract Msg：{ex.Message}{Environment.NewLine}Data: {SoftBasic.ByteToHexString(response)}";
      return actualData;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read(System.String,System.UInt16)" />
  /// <remarks>
  /// 地址支持读取扩展单元缓冲存储器的数据，例如读取扩展单元号1，地址100的数据，地址写为 unit=1;100<br />
  /// The address supports reading the data in the buffer memory of the expansion unit, such as reading the data of the expansion unit number 1, address 100, and the address is written as unit=1;100
  /// </remarks>
  public static OperateResult<byte[]> Read(IReadWriteDevice keyence, string address, ushort length)
  {
    if (address.StartsWith("unit="))
    {
      byte parameter = (byte) HslHelper.ExtractParameter(ref address, "unit", 0);
      return !ushort.TryParse(address, out ushort _) ? new OperateResult<byte[]>("Address is not right, convert ushort wrong!") : KeyenceNanoHelper.ReadExpansionMemory(keyence, parameter, ushort.Parse(address), length);
    }
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    OperateResult<List<byte[]>> result1 = KeyenceNanoHelper.BuildReadCommand(from.Content, length, false);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = keyence.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      OperateResult result3 = KeyenceNanoHelper.CheckPlcReadResponse(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result3);
      OperateResult<byte[]> actualData = KeyenceNanoHelper.ExtractActualData(from.Content.DataCode, result2.Content);
      if (!actualData.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) actualData);
      byteList.AddRange((IEnumerable<byte>) actualData.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Byte[])" />
  public static OperateResult Write(IReadWriteDevice keyence, string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = KeyenceNanoHelper.BuildWriteCommand(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = keyence.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult operateResult3 = KeyenceNanoHelper.CheckPlcWriteResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.Read(HslCommunication.Core.IReadWriteDevice,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice keyence,
    string address,
    ushort length)
  {
    if (address.StartsWith("unit="))
    {
      byte unit = (byte) HslHelper.ExtractParameter(ref address, "unit", 0);
      if (!ushort.TryParse(address, out ushort _))
        return new OperateResult<byte[]>("Address is not right, convert ushort wrong!");
      OperateResult<byte[]> operateResult = await KeyenceNanoHelper.ReadExpansionMemoryAsync(keyence, unit, ushort.Parse(address), length);
      return operateResult;
    }
    OperateResult<KeyenceNanoAddress> addressResult = KeyenceNanoAddress.ParseFrom(address, length);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
    OperateResult<List<byte[]>> command = KeyenceNanoHelper.BuildReadCommand(addressResult.Content, length, false);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> array = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      OperateResult ackResult = KeyenceNanoHelper.CheckPlcReadResponse(read.Content);
      if (!ackResult.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(ackResult);
      OperateResult<byte[]> extra = KeyenceNanoHelper.ExtractActualData(addressResult.Content.DataCode, read.Content);
      if (!extra.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) extra);
      array.AddRange((IEnumerable<byte>) extra.Content);
      read = (OperateResult<byte[]>) null;
      ackResult = (OperateResult) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(array.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.Write(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice keyence,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> command = KeyenceNanoHelper.BuildWriteCommand(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult checkResult = KeyenceNanoHelper.CheckPlcWriteResponse(read.Content);
    return checkResult.IsSuccess ? OperateResult.CreateSuccessResult() : checkResult;
  }

  private static bool CheckBoolOnWordAddress(string address)
  {
    return Regex.IsMatch(address, "^(DM|CM|EM|FM|ZF|VM)[0-9]+\\.[0-9]+$", RegexOptions.IgnoreCase);
  }

  /// <inheritdoc />
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice keyence,
    string address,
    ushort length)
  {
    if (KeyenceNanoHelper.CheckBoolOnWordAddress(address))
      return HslHelper.ReadBool((IReadWriteNet) keyence, address, length);
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    OperateResult<List<byte[]>> result1 = KeyenceNanoHelper.BuildReadCommand(from.Content, length, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = keyence.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult result3 = KeyenceNanoHelper.CheckPlcReadResponse(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(result3);
      OperateResult<bool[]> actualBoolData = KeyenceNanoHelper.ExtractActualBoolData(from.Content.DataCode, result2.Content);
      if (!actualBoolData.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) actualBoolData);
      boolList.AddRange((IEnumerable<bool>) actualBoolData.Content);
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <inheritdoc />
  public static OperateResult Write(IReadWriteDevice keyence, string address, bool value)
  {
    if (KeyenceNanoHelper.CheckBoolOnWordAddress(address))
      return HslHelper.WriteBool((IReadWriteNet) keyence, address, new bool[1]
      {
        value
      });
    OperateResult<byte[]> operateResult1 = KeyenceNanoHelper.BuildWriteCommand(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = keyence.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult operateResult3 = KeyenceNanoHelper.CheckPlcWriteResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  public static OperateResult Write(IReadWriteDevice keyence, string address, bool[] value)
  {
    if (KeyenceNanoHelper.CheckBoolOnWordAddress(address))
      return HslHelper.WriteBool((IReadWriteNet) keyence, address, value);
    OperateResult<byte[]> operateResult1 = KeyenceNanoHelper.BuildWriteCommand(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = keyence.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult operateResult3 = KeyenceNanoHelper.CheckPlcWriteResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice keyence,
    string address,
    ushort length)
  {
    if (KeyenceNanoHelper.CheckBoolOnWordAddress(address))
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) keyence, address, length).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<KeyenceNanoAddress> addressResult = KeyenceNanoAddress.ParseFrom(address, length);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) addressResult);
    OperateResult<List<byte[]>> command = KeyenceNanoHelper.BuildReadCommand(addressResult.Content, length, true);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<bool> array = new List<bool>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(command.Content[i]).ConfigureAwait(false);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult ackResult = KeyenceNanoHelper.CheckPlcReadResponse(read.Content);
      if (!ackResult.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(ackResult);
      OperateResult<bool[]> extra = KeyenceNanoHelper.ExtractActualBoolData(addressResult.Content.DataCode, read.Content);
      if (!extra.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) extra);
      array.AddRange((IEnumerable<bool>) extra.Content);
      read = (OperateResult<byte[]>) null;
      ackResult = (OperateResult) null;
      extra = (OperateResult<bool[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(array.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice keyence,
    string address,
    bool value)
  {
    if (KeyenceNanoHelper.CheckBoolOnWordAddress(address))
    {
      OperateResult operateResult = await HslHelper.WriteBoolAsync((IReadWriteNet) keyence, address, new bool[1]
      {
        value
      }).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<byte[]> command = KeyenceNanoHelper.BuildWriteCommand(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult checkResult = KeyenceNanoHelper.CheckPlcWriteResponse(read.Content);
    return checkResult.IsSuccess ? OperateResult.CreateSuccessResult() : checkResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice keyence,
    string address,
    bool[] value)
  {
    if (KeyenceNanoHelper.CheckBoolOnWordAddress(address))
    {
      OperateResult operateResult = await HslHelper.WriteBoolAsync((IReadWriteNet) keyence, address, value).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<byte[]> command = KeyenceNanoHelper.BuildWriteCommand(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult checkResult = KeyenceNanoHelper.CheckPlcWriteResponse(read.Content);
    return checkResult.IsSuccess ? OperateResult.CreateSuccessResult() : checkResult;
  }

  private static OperateResult<KeyencePLCS> ExtraPlcType(OperateResult<byte[]> read)
  {
    if (!read.IsSuccess)
      return read.ConvertFailed<KeyencePLCS>();
    OperateResult operateResult = KeyenceNanoHelper.CheckPlcReadResponse(read.Content);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<KeyencePLCS>();
    string str = Encoding.ASCII.GetString(read.Content.RemoveLast<byte>(2));
    switch (str)
    {
      case "48":
      case "49":
        return OperateResult.CreateSuccessResult<KeyencePLCS>(KeyencePLCS.KV700);
      case "50":
        return OperateResult.CreateSuccessResult<KeyencePLCS>(KeyencePLCS.KV1000);
      case "51":
        return OperateResult.CreateSuccessResult<KeyencePLCS>(KeyencePLCS.KV3000);
      case "52":
        return OperateResult.CreateSuccessResult<KeyencePLCS>(KeyencePLCS.KV5000);
      case "53":
        return OperateResult.CreateSuccessResult<KeyencePLCS>(KeyencePLCS.KV5500);
      default:
        return new OperateResult<KeyencePLCS>("Unknow type:" + str);
    }
  }

  /// <summary>
  /// <c>[商业授权]</c> 查询PLC的型号信息<br />
  /// <b>[Authorization]</b> Query PLC model information
  /// </summary>
  /// <param name="keyence">PLC通信对象</param>
  /// <returns>包含型号的结果对象</returns>
  internal static OperateResult<KeyencePLCS> ReadPlcType(IReadWriteDevice keyence)
  {
    return !Authorization.asdniasnfaksndiqwhawfskhfaiw() ? new OperateResult<KeyencePLCS>(StringResources.Language.InsufficientPrivileges) : KeyenceNanoHelper.ExtraPlcType(keyence.ReadFromCoreServer(Encoding.ASCII.GetBytes("?K\r")));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice)" />
  internal static async Task<OperateResult<KeyencePLCS>> ReadPlcTypeAsync(IReadWriteDevice keyence)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<KeyencePLCS>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("?K\r")).ConfigureAwait(false);
    return KeyenceNanoHelper.ExtraPlcType(read);
  }

  /// <summary>
  /// <b>[商业授权]</b> 清除CPU单元发生的错误<br />
  /// <b>[Authorization]</b> Clear CPU unit errors
  /// </summary>
  /// <param name="keyence">PLC通信对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult ClearError(IReadWriteDevice keyence)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<int>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = keyence.ReadFromCoreServer(Encoding.ASCII.GetBytes("ER\r"));
    return !operateResult.IsSuccess ? (OperateResult) operateResult : KeyenceNanoHelper.CheckPlcWriteResponse(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.ClearError(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult> ClearErrorAsync(IReadWriteDevice keyence)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<int>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("ER\r")).ConfigureAwait(false);
    return read.IsSuccess ? KeyenceNanoHelper.CheckPlcWriteResponse(read.Content) : (OperateResult) read;
  }

  private static OperateResult<int> ExtraPlcMode(OperateResult<byte[]> read)
  {
    if (!read.IsSuccess)
      return read.ConvertFailed<int>();
    OperateResult operateResult = KeyenceNanoHelper.CheckPlcReadResponse(read.Content);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<int>();
    return Encoding.ASCII.GetString(read.Content.RemoveLast<byte>(2)) == "0" ? OperateResult.CreateSuccessResult<int>(0) : OperateResult.CreateSuccessResult<int>(1);
  }

  /// <summary>
  /// <c>[商业授权]</c> 读取当前PLC的模式，如果是0，代表 PROG模式或者梯形图未登录，如果为1，代表RUN模式<br />
  /// <b>[Authorization]</b> Read the current PLC mode, if it is 0, it means PROG mode or the ladder diagram is not registered, if it is 1, it means RUN mode
  /// </summary>
  /// <param name="keyence">PLC通信对象</param>
  /// <returns>包含模式的结果对象</returns>
  internal static OperateResult<int> ReadPlcMode(IReadWriteDevice keyence)
  {
    return !Authorization.asdniasnfaksndiqwhawfskhfaiw() ? new OperateResult<int>(StringResources.Language.InsufficientPrivileges) : KeyenceNanoHelper.ExtraPlcMode(keyence.ReadFromCoreServer(Encoding.ASCII.GetBytes("?M\r")));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.ReadPlcMode(HslCommunication.Core.IReadWriteDevice)" />
  internal static async Task<OperateResult<int>> ReadPlcModeAsync(IReadWriteDevice keyence)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<int>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("?M\r")).ConfigureAwait(false);
    return KeyenceNanoHelper.ExtraPlcMode(read);
  }

  /// <summary>
  /// <c>[商业授权]</c> 设置PLC的时间<br />
  /// <b>[Authorization]</b> Set PLC time
  /// </summary>
  /// <param name="keyence">PLC通信对象</param>
  /// <param name="dateTime">时间数据</param>
  /// <returns>是否设置成功</returns>
  public static OperateResult SetPlcDateTime(IReadWriteDevice keyence, DateTime dateTime)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<int>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = keyence.ReadFromCoreServer(Encoding.ASCII.GetBytes($"WRT {dateTime.Year - 2000:D2} {dateTime.Month:D2} {dateTime.Day:D2} " + $"{dateTime.Hour:D2} {dateTime.Minute:D2} {dateTime.Second:D2} {(int) dateTime.DayOfWeek}\r"));
    return !operateResult.IsSuccess ? (OperateResult) operateResult.ConvertFailed<int>() : KeyenceNanoHelper.CheckPlcWriteResponse(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.SetPlcDateTime(HslCommunication.Core.IReadWriteDevice,System.DateTime)" />
  public static async Task<OperateResult> SetPlcDateTimeAsync(
    IReadWriteDevice keyence,
    DateTime dateTime)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<int>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes($"WRT {dateTime.Year - 2000:D2} {dateTime.Month:D2} {dateTime.Day:D2} " + $"{dateTime.Hour:D2} {dateTime.Minute:D2} {dateTime.Second:D2} {(int) dateTime.DayOfWeek}\r")).ConfigureAwait(false);
    return read.IsSuccess ? KeyenceNanoHelper.CheckPlcWriteResponse(read.Content) : (OperateResult) read;
  }

  /// <summary>
  /// <c>[商业授权]</c> 读取指定软元件的注释信息<br />
  /// <b>[Authorization]</b> Read the comment information of the specified device
  /// </summary>
  /// <param name="keyence">PLC通信对象</param>
  /// <param name="address">软元件的地址</param>
  /// <returns>软元件的注释信息</returns>
  public static OperateResult<string> ReadAddressAnnotation(
    IReadWriteDevice keyence,
    string address)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<string>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult1 = keyence.ReadFromCoreServer(Encoding.ASCII.GetBytes($"RDC {address}\r"));
    if (!operateResult1.IsSuccess)
      return operateResult1.ConvertFailed<string>();
    OperateResult operateResult2 = KeyenceNanoHelper.CheckPlcReadResponse(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return operateResult2.ConvertFailed<string>();
    return OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(operateResult1.Content.RemoveLast<byte>(2)).Trim(' '));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.ReadAddressAnnotation(HslCommunication.Core.IReadWriteDevice,System.String)" />
  public static async Task<OperateResult<string>> ReadAddressAnnotationAsync(
    IReadWriteDevice keyence,
    string address)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<string>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes($"RDC {address}\r")).ConfigureAwait(false);
    if (!read.IsSuccess)
      return read.ConvertFailed<string>();
    OperateResult check = KeyenceNanoHelper.CheckPlcReadResponse(read.Content);
    if (!check.IsSuccess)
      return check.ConvertFailed<string>();
    return OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(read.Content.RemoveLast<byte>(2)).Trim(' '));
  }

  /// <summary>
  /// <c>[商业授权]</c> 从扩展单元缓冲存储器连续读取指定个数的数据，单位为字<br />
  /// <b>[Authorization]</b> Continuously read the specified number of data from the expansion unit buffer memory, the unit is word
  /// </summary>
  /// <param name="keyence">PLC的通信对象</param>
  /// <param name="unit">单元编号</param>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取的长度，单位为字</param>
  /// <returns>包含是否成功的原始字节数组</returns>
  public static OperateResult<byte[]> ReadExpansionMemory(
    IReadWriteDevice keyence,
    byte unit,
    ushort address,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult1 = keyence.ReadFromCoreServer(Encoding.ASCII.GetBytes($"URD {unit} {address}.U {length}\r"));
    if (!operateResult1.IsSuccess)
      return operateResult1.ConvertFailed<byte[]>();
    OperateResult operateResult2 = KeyenceNanoHelper.CheckPlcReadResponse(operateResult1.Content);
    return !operateResult2.IsSuccess ? operateResult2.ConvertFailed<byte[]>() : KeyenceNanoHelper.ExtractActualData("DM", operateResult1.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.ReadExpansionMemory(HslCommunication.Core.IReadWriteDevice,System.Byte,System.UInt16,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadExpansionMemoryAsync(
    IReadWriteDevice keyence,
    byte unit,
    ushort address,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes($"URD {unit} {address}.U {length}\r")).ConfigureAwait(false);
    if (!read.IsSuccess)
      return read.ConvertFailed<byte[]>();
    OperateResult check = KeyenceNanoHelper.CheckPlcReadResponse(read.Content);
    return check.IsSuccess ? KeyenceNanoHelper.ExtractActualData("DM", read.Content) : check.ConvertFailed<byte[]>();
  }

  /// <summary>
  /// <c>[商业授权]</c> 将原始字节数据写入到扩展的缓冲存储器，需要指定单元编号，偏移地址，写入的数据<br />
  ///  <b>[Authorization]</b> To write the original byte data to the extended buffer memory, you need to specify the unit number, offset address, and write data
  ///  </summary>
  /// <param name="keyence">PLC通信对象信息</param>
  /// <param name="unit">单元编号</param>
  /// <param name="address">偏移地址</param>
  /// <param name="value">等待写入的原始字节数据</param>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult WriteExpansionMemory(
    IReadWriteDevice keyence,
    byte unit,
    ushort address,
    byte[] value)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = keyence.ReadFromCoreServer(KeyenceNanoHelper.BuildWriteExpansionMemoryCommand(unit, address, value).Content);
    return !operateResult.IsSuccess ? (OperateResult) operateResult.ConvertFailed<byte[]>() : KeyenceNanoHelper.CheckPlcWriteResponse(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoHelper.WriteExpansionMemory(HslCommunication.Core.IReadWriteDevice,System.Byte,System.UInt16,System.Byte[])" />
  public static async Task<OperateResult> WriteExpansionMemoryAsync(
    IReadWriteDevice keyence,
    byte unit,
    ushort address,
    byte[] value)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> read = await keyence.ReadFromCoreServerAsync(KeyenceNanoHelper.BuildWriteExpansionMemoryCommand(unit, address, value).Content).ConfigureAwait(false);
    return read.IsSuccess ? KeyenceNanoHelper.CheckPlcWriteResponse(read.Content) : (OperateResult) read.ConvertFailed<byte[]>();
  }
}
