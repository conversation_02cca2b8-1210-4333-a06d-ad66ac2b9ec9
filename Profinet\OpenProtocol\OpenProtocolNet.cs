﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.OpenProtocolNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>
/// 开放以太网协议，在拧紧枪中应用广泛，本通信支持基本的问答机制以及订阅机制，支持完全自定义的参数指定及数据读取。<br />
/// Open Ethernet protocol, widely used in tightening guns, this communication supports basic question answering mechanism and subscription mechanism, supports fully customized parameter specification and data reading.
/// </summary>
/// <remarks>
/// 自定义的读取使用<see cref="M:HslCommunication.Profinet.OpenProtocol.OpenProtocolNet.ReadCustomer(System.Int32,System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.String})" />来实现，如果是订阅的数据，使用<see cref="E:HslCommunication.Profinet.OpenProtocol.OpenProtocolNet.OnReceivedOpenMessage" />绑定自己的方法触发。更详细的示例参考：http://api.hslcommunication.cn<br />
/// Custom reads are implemented using <see cref="M:HslCommunication.Profinet.OpenProtocol.OpenProtocolNet.ReadCustomer(System.Int32,System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.String})" />, and if it is subscribed data, use <see cref="E:HslCommunication.Profinet.OpenProtocol.OpenProtocolNet.OnReceivedOpenMessage" />
/// bind your own method to trigger it. For a more detailed example, refer to: http://api.hslcommunication.cn
/// </remarks>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OpenProtocolNetSample.cs" region="Usage" title="连接及自定义读取使用" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OpenProtocolNetSample.cs" region="Usage2" title="便捷的读取示例" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OpenProtocolNetSample.cs" region="Usage3" title="订阅事件处理操作" />
/// </example>
public class OpenProtocolNet : TcpNetCommunication
{
  private Timer timer;
  private ParameterSetMessages parameterSetMessages;
  private JobMessage jobMessage;
  private TighteningResultMessages tighteningResultMessages;
  private ToolMessages toolMessages;
  private TimeMessages timeMessages;
  private int revisonOnConnected;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public OpenProtocolNet()
  {
    this.revisonOnConnected = 1;
    this.CommunicationPipe.UseServerActivePush = true;
    this.timer = new Timer(new TimerCallback(this.ThreadKeepAlive), (object) null, 10000, 10000);
    this.parameterSetMessages = new ParameterSetMessages(this);
    this.jobMessage = new JobMessage(this);
    this.tighteningResultMessages = new TighteningResultMessages(this);
    this.toolMessages = new ToolMessages(this);
    this.timeMessages = new TimeMessages(this);
    this.LogMsgFormatBinary = false;
  }

  /// <summary>
  /// 使用指定的IP地址及端口来初始化对象<br />
  /// Use the specified IP address and port to initialize the object
  /// </summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  public OpenProtocolNet(string ipAddress, int port = 4545)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new OpenProtocolMessage();

  private void ThreadKeepAlive(object state)
  {
    if (this.CommunicationPipe.IsConnectError() || !this.KeepAliveMessageEnable)
      return;
    OperateResult<byte[]> operateResult = OpenProtocolNet.BuildReadCommand(9999, 1, -1, -1, (List<string>) null);
    if (!operateResult.IsSuccess)
      return;
    this.CommunicationPipe.Send(operateResult.Content);
  }

  /// <summary>
  /// 获取或设置当前 MID 9999 的心跳命令是否生效，默认为 <c>True</c><br />
  /// Get or set the current MID 9999's heartbeat command that is in effect, defaulting to <c>True</c>
  /// </summary>
  public bool KeepAliveMessageEnable { get; set; } = true;

  /// <summary>
  /// 额外的用于订阅的MID信息，将会自定范围指定MID的值增加1<br />
  /// Additional MID information for subscriptions will increase the value of the specified MID by 1 for the custom range
  /// </summary>
  public int[] ExtraSubscribeMID { get; set; }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    this.CommunicationPipe.UseServerActivePush = true;
    if (this.revisonOnConnected < 0)
      return base.InitializationOnConnect();
    OperateResult<byte[]> result1 = OpenProtocolNet.BuildReadCommand(1, this.revisonOnConnected, -1, -1, (List<string>) null);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult result2 = this.CommunicationPipe.Send(result1.Content);
    if (!result2.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>(result2);
    OperateResult<byte[]> message = this.CommunicationPipe.ReceiveMessage(this.GetNewNetMessage(), (byte[]) null, false);
    if (!message.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>("InitializationOnConnect failed", (OperateResult) message);
    string str = Encoding.ASCII.GetString(message.Content);
    return str.Substring(4, 4) == "0002" ? base.InitializationOnConnect() : new OperateResult("Failed:" + str.Substring(4, 4));
  }

  /// <inheritdoc />
  protected override OperateResult ExtraOnDisconnect()
  {
    OperateResult<byte[]> result = OpenProtocolNet.BuildReadCommand(3, 1, -1, -1, (List<string>) null);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result) : (OperateResult) this.ReadFromCoreServer(this.CommunicationPipe, result.Content, true, true);
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    this.CommunicationPipe.UseServerActivePush = true;
    if (this.revisonOnConnected >= 0)
    {
      OperateResult<byte[]> command = OpenProtocolNet.BuildReadCommand(1, this.revisonOnConnected, -1, -1, (List<string>) null);
      if (!command.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) command);
      OperateResult send = await this.CommunicationPipe.SendAsync(command.Content).ConfigureAwait(false);
      if (!send.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<string>(send);
      OperateResult<byte[]> receive = await this.CommunicationPipe.ReceiveMessageAsync(this.GetNewNetMessage(), (byte[]) null, false).ConfigureAwait(false);
      if (!receive.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<string>("InitializationOnConnect failed", (OperateResult) receive);
      string reply = Encoding.ASCII.GetString(receive.Content);
      if (!(reply.Substring(4, 4) == "0002"))
        return new OperateResult("Failed:" + reply.Substring(4, 4));
      OperateResult operateResult = await base.InitializationOnConnectAsync();
      return operateResult;
    }
    OperateResult operateResult1 = await base.InitializationOnConnectAsync();
    return operateResult1;
  }

  /// <summary>根据传入的MID来决定是用于订阅的消息ID，如果是订阅的ID，通常返回id+1</summary>
  /// <param name="mid">当前的mid号</param>
  /// <returns>如果是订阅的，请返回实际需要返回的id</returns>
  protected virtual int DecideSubscribeData(int mid)
  {
    if (mid == 15 || mid == 35 || mid == 52 || mid == 61 || mid == 71 || mid == 74 || mid == 76 || mid == 91 || mid == 101)
      return mid + 1;
    if (mid == 106 || mid == 107)
      return 108;
    if (mid == 121 || mid == 122 || mid == 123 || mid == 124)
      return 125;
    switch (mid)
    {
      case 152:
        return 153;
      case 211:
        return 212;
      case 217:
        return 218;
      case 221:
        return 222;
      case 242:
        return 243;
      case 251:
        return 252;
      case 401:
        return 402;
      case 421:
        return 422;
      default:
        return this.ExtraSubscribeMID == null || !((IEnumerable<int>) this.ExtraSubscribeMID).Contains<int>(mid) ? -1 : mid + 1;
    }
  }

  /// <inheritdoc />
  protected override bool DecideWhetherQAMessage(
    CommunicationPipe pipe,
    OperateResult<byte[]> receive)
  {
    if (receive.Content.Length >= 20)
    {
      int int32 = Convert.ToInt32(Encoding.ASCII.GetString(receive.Content, 4, 4));
      bool flag = receive.Content[11] == (byte) 48 /*0x30*/;
      if (int32 == 9999)
        return false;
      int mid = this.DecideSubscribeData(int32);
      if (mid > 0)
      {
        if (flag || this.AutoAckControllerMessage)
          pipe.Send(OpenProtocolNet.BuildReadCommand(mid, 1, -1, -1, (List<string>) null).Content);
        EventHandler<OpenEventArgs> receivedOpenMessage = this.OnReceivedOpenMessage;
        if (receivedOpenMessage != null)
          receivedOpenMessage((object) this, new OpenEventArgs(Encoding.ASCII.GetString(receive.Content).TrimEnd(new char[1])));
        return false;
      }
    }
    return base.DecideWhetherQAMessage(pipe, receive);
  }

  /// <summary>
  /// 使用自定义的命令读取数据，需要指定每个参数信息，然后返回字符串数据内容，根据实际的功能码，解析出实际的数据信息<br />
  /// To use a custom command to read data, you need to specify each parameter information, then return the string data content, and parse the actual data information according to the actual function code
  /// </summary>
  /// <param name="mid">The MID is four bytes long and is specified by four ASCII digits(‘0’…’9’). The MID describes how to interpret the message.</param>
  /// <param name="revison">The revision of the MID is specified by three ASCII digits(‘0’…’9’).The MID revision is unique per MID and is used in case several versions are available for the same MID. </param>
  /// <param name="stationId">The station the message is addressed to in the case of controller with multi-station configuration.The station ID is 1 byte long and is specified by one ASCII digit(‘0’…’9’). </param>
  /// <param name="spindleId">The spindle the message is addressed to in the case several spindles are connected to the same controller. The spindle ID is 2 bytes long and is specified by two ASCII digits (‘0’…’9’). </param>
  /// <param name="parameters">The Data Field is ASCII data representing the data. The data contains a list of parameters depending on the MID.Each parameter is represented with an ID and the parameter value. </param>
  /// <returns>结果数据信息</returns>
  [HslMqttApi(Description = "使用自定义的命令读取数据，需要指定每个参数信息，然后返回字符串数据内容，根据实际的功能码，解析出实际的数据信息")]
  public OperateResult<string> ReadCustomer(
    int mid,
    int revison,
    int stationId,
    int spindleId,
    List<string> parameters)
  {
    if (parameters == null)
      parameters = new List<string>();
    OperateResult<byte[]> result1 = OpenProtocolNet.BuildReadCommand(mid, revison, stationId, spindleId, parameters);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result2);
    OperateResult result3 = OpenProtocolNet.CheckRequestReplyMessages(result2.Content);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<string>(result3) : OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(result2.Content));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.OpenProtocol.OpenProtocolNet.ReadCustomer(System.Int32,System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.String})" />
  public async Task<OperateResult<string>> ReadCustomerAsync(
    int mid,
    int revison,
    int stationId,
    int spindleId,
    List<string> parameters)
  {
    if (parameters == null)
      parameters = new List<string>();
    OperateResult<byte[]> command = OpenProtocolNet.BuildReadCommand(mid, revison, stationId, spindleId, parameters);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) command);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    OperateResult check = OpenProtocolNet.CheckRequestReplyMessages(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(read.Content)) : OperateResult.CreateFailedResult<string>(check);
  }

  /// <summary>
  /// 获取或设置初始化连接时 MID0001 命令的版本号，默认为 1，如果设置小于 0，则表示不发送 MID0001 命令。<br />
  /// Get or set the version of the MID0001 command when initializing the connection, the default is 1, if the setting is less than 0, it means that the MID0001 command is not sent.
  /// </summary>
  /// <remarks>当连接的设备是 MT Focus 6000的控制器时，本值需要设置为 6</remarks>
  public int RevisonOnConnected
  {
    get => this.revisonOnConnected;
    set => this.revisonOnConnected = value;
  }

  /// <summary>
  /// 针对控制器的主动发送的消息，是否无视<c>Ack</c>标记，全部进行返回信号操作，默认为 <c>False</c>，将根据报文里的<c>Ack</c>信号来决定是否返回数据<br />
  /// If the default value is <c>False</c>, the controller will<c></c> decide whether to return data based on the <c>Ack</c> signal in the packet
  /// </summary>
  public bool AutoAckControllerMessage { get; set; } = false;

  /// <summary>
  /// 参数集合操作的相关属性，可以用来获取参数ID列表，设置数据等操作。<br />
  /// The properties related to parameter collection operations can be used to obtain parameter ID lists, set data, and other operations.
  /// </summary>
  public ParameterSetMessages ParameterSetMessages => this.parameterSetMessages;

  /// <summary>
  /// 任务消息的相关属性，可以用来获取任务的数据，订阅任务，取消订阅任务，选择任务，启动任务。<br />
  /// The relevant properties of task messages can be used to obtain task data, subscribe to tasks, unsubscribe tasks, select tasks, and start tasks.
  /// </summary>
  public JobMessage JobMessage => this.jobMessage;

  /// <summary>拧紧结果消息的操作属性</summary>
  public TighteningResultMessages TighteningResultMessages => this.tighteningResultMessages;

  /// <summary>工具消息的操作属性</summary>
  public ToolMessages ToolMessages => this.toolMessages;

  /// <summary>时间消息的属性</summary>
  public TimeMessages TimeMessages => this.timeMessages;

  /// <summary>当接收到OpenProtocol协议消息触发的事件</summary>
  public event EventHandler<OpenEventArgs> OnReceivedOpenMessage;

  /// <inheritdoc />
  public override string ToString() => $"OpenProtocolNet[{this.IpAddress}:{this.Port}]";

  /// <inheritdoc cref="M:HslCommunication.Profinet.OpenProtocol.OpenProtocolNet.BuildOpenProtocolMessage(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,System.String[])" />
  public static OperateResult<byte[]> BuildReadCommand(
    int mid,
    int revison,
    int stationId,
    int spindleId,
    List<string> parameters)
  {
    if (mid < 0 || mid > 9999)
      return new OperateResult<byte[]>("Mid must be between 0 - 9999");
    if (revison < 0 || revison > 999)
      return new OperateResult<byte[]>("revison must be between 0 - 999");
    if (stationId > 9)
      return new OperateResult<byte[]>("stationId must be between 0 - 9");
    if (spindleId > 99)
      return new OperateResult<byte[]>("spindleId must be between 0 - 99");
    int count = 0;
    parameters?.ForEach((Action<string>) (m => count += m.Length));
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append((20 + count).ToString("D4"));
    stringBuilder.Append(mid.ToString("D4"));
    stringBuilder.Append(revison.ToString("D3"));
    stringBuilder.Append('0');
    stringBuilder.Append(stationId < 0 ? "  " : stationId.ToString("D2"));
    stringBuilder.Append(spindleId < 0 ? "  " : spindleId.ToString("D2"));
    stringBuilder.Append(' ');
    stringBuilder.Append(' ');
    stringBuilder.Append(' ');
    stringBuilder.Append(' ');
    if (parameters != null)
    {
      for (int index = 0; index < parameters.Count; ++index)
        stringBuilder.Append(parameters[index]);
    }
    stringBuilder.Append(char.MinValue);
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>构建一个读取的初始报文</summary>
  /// <param name="mid">The MID is four bytes long and is specified by four ASCII digits(‘0’…’9’). The MID describes how to interpret the message.</param>
  /// <param name="revison">The revision of the MID is specified by three ASCII digits(‘0’…’9’).The MID revision is unique per MID and is used in case several versions are available for the same MID. </param>
  /// <param name="ack">The No Ack Flag is used when setting a subscription. If the No Ack flag is not set in a subscription it means that the subscriber will acknowledge each “push” message sent by the controller (reliable mode).</param>
  /// <param name="stationId">The station the message is addressed to in the case of controller with multi-station configuration.The station ID is 1 byte long and is specified by one ASCII digit(‘0’…’9’). </param>
  /// <param name="spindleId">The spindle the message is addressed to in the case several spindles are connected to the same controller. The spindle ID is 2 bytes long and is specified by two ASCII digits (‘0’…’9’). </param>
  /// <param name="withIndex">每个参数的前面，是否携带索引信息</param>
  /// <param name="parameters">The Data Field is ASCII data representing the data. The data contains a list of parameters depending on the MID.Each parameter is represented with an ID and the parameter value. </param>
  /// <returns>原始字节的报文信息</returns>
  public static OperateResult<byte[]> BuildOpenProtocolMessage(
    int mid,
    int revison,
    int ack,
    int stationId,
    int spindleId,
    bool withIndex,
    params string[] parameters)
  {
    if (mid < 0 || mid > 9999)
      return new OperateResult<byte[]>("Mid must be between 0 - 9999");
    if (revison < 0 || revison > 999)
      return new OperateResult<byte[]>("revison must be between 0 - 999");
    if (stationId > 9)
      return new OperateResult<byte[]>("stationId must be between 0 - 9");
    if (spindleId > 99)
      return new OperateResult<byte[]>("spindleId must be between 0 - 99");
    int num1 = 0;
    StringBuilder stringBuilder1 = new StringBuilder();
    stringBuilder1.Append(mid.ToString("D4"));
    stringBuilder1.Append(revison.ToString("D3"));
    stringBuilder1.Append(ack < 0 ? " " : ack.ToString("D1"));
    stringBuilder1.Append(stationId < 0 ? "  " : stationId.ToString("D2"));
    stringBuilder1.Append(spindleId < 0 ? "  " : spindleId.ToString("D2"));
    stringBuilder1.Append(' ');
    stringBuilder1.Append(' ');
    stringBuilder1.Append(' ');
    stringBuilder1.Append(' ');
    int num2;
    if (parameters != null)
    {
      for (int index = 0; index < parameters.Length; ++index)
      {
        if (withIndex)
        {
          StringBuilder stringBuilder2 = stringBuilder1;
          num2 = index + 1;
          string str = num2.ToString("D2");
          stringBuilder2.Append(str);
          stringBuilder1.Append(parameters[index]);
          num1 += 2 + parameters[index].Length;
        }
        else
        {
          stringBuilder1.Append(parameters[index]);
          num1 += parameters[index].Length;
        }
      }
    }
    stringBuilder1.Append(char.MinValue);
    StringBuilder stringBuilder3 = stringBuilder1;
    num2 = 20 + num1;
    string str1 = num2.ToString("D4");
    stringBuilder3.Insert(0, str1);
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder1.ToString()));
  }

  /// <summary>根据错误代码来获取到错误文本信息</summary>
  /// <param name="code">错误代号</param>
  /// <returns>错误文本</returns>
  public static string GetErrorText(int code)
  {
    switch (code)
    {
      case 1:
        return "Invalid data";
      case 2:
        return "Parameter set ID not present";
      case 3:
        return "Parameter set can not be set.";
      case 4:
        return "Parameter set not running";
      case 6:
        return "VIN upload subscription already exists";
      case 7:
        return "VIN upload subscription does not exists";
      case 8:
        return "VIN input source not granted";
      case 9:
        return "Last tightening result subscription already exists";
      case 10:
        return "Last tightening result subscription does not exist";
      case 11:
        return "Alarm subscription already exists";
      case 12:
        return "Alarm subscription does not exist";
      case 13:
        return "Parameter set selection subscription already exists";
      case 14:
        return "Parameter set selection subscription does not exist";
      case 15:
        return "Tightening ID requested not found";
      case 16 /*0x10*/:
        return "Connection rejected protocol busy";
      case 17:
        return "Job ID not present";
      case 18:
        return "Job info subscription already exists";
      case 19:
        return "Job info subscription does not exist";
      case 20:
        return "Job can not be set";
      case 21:
        return "Job not running";
      case 22:
        return "Not possible to execute dynamic Job request";
      case 23:
        return "Job batch decrement failed";
      case 30:
        return "Controller is not a sync Master/station controller";
      case 31 /*0x1F*/:
        return "Multi-spindle status subscription already exists";
      case 32 /*0x20*/:
        return "Multi-spindle status subscription does not exist";
      case 33:
        return "Multi-spindle result subscription already exists";
      case 34:
        return "Multi-spindle result subscription does not exist";
      case 40:
        return "Job line control info subscription already exists";
      case 41:
        return "Job line control info subscription does not exist";
      case 42:
        return "Identifier input source not granted";
      case 43:
        return "Multiple identifiers work order subscription already exists";
      case 44:
        return "Multiple identifiers work order subscription does not exist";
      case 50:
        return "Status external monitored inputs subscription already exists";
      case 51:
        return "Status external monitored inputs subscription does not exist";
      case 52:
        return "IO device not connected";
      case 53:
        return "Faulty IO device ID";
      case 58:
        return "No alarm present";
      case 59:
        return "Tool currently in use";
      case 60:
        return "No histogram available";
      case 70:
        return "Calibration failed";
      case 79:
        return "Command failed";
      case 80 /*0x50*/:
        return "Audi emergency status subscription exists";
      case 81:
        return "Audi emergency status subscription does not exist";
      case 82:
        return "Automatic/Manual mode subscribe already exist";
      case 83:
        return "Automatic/Manual mode subscribe does not exist";
      case 84:
        return "The relay function subscription already exists";
      case 85:
        return "The relay function subscription does not exist";
      case 86:
        return "The selector socket info subscription already exist";
      case 87:
        return "The selector socket info subscription does not exist";
      case 88:
        return "The digin info subscription already exist";
      case 89:
        return "The digin info subscription does not exist";
      case 90:
        return "Lock at bach done subscription already exist";
      case 91:
        return "Lock at bach done subscription does not exist";
      case 92:
        return "Open protocol commands disabled";
      case 93:
        return "Open protocol commands disabled subscription already exists";
      case 94:
        return "Open protocol commands disabled subscription does not exist";
      case 95:
        return "Reject request, PowerMACS is in manual mode";
      case 96 /*0x60*/:
        return "Client already connected";
      case 97:
        return "MID revision unsupported";
      case 98:
        return "Controller internal request timeout";
      case 99:
        return "Unknown MID";
      default:
        return StringResources.Language.UnknownError;
    }
  }

  private static string GetMid9998Text(int err)
  {
    switch (err)
    {
      case 1:
        return "Invalid length";
      case 2:
        return "Invalid revision = Not equal to an ASCII number 0 to 99";
      case 3:
        return "Invalid sequence number = Not next expected.";
      case 4:
        return "Inconsistency of “Number of messages”, “Message number”";
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>检查请求返回的消息是否合法的</summary>
  /// <param name="reply">返回的消息</param>
  /// <returns>是否合法的结果对象</returns>
  public static OperateResult CheckRequestReplyMessages(byte[] reply)
  {
    try
    {
      switch (Encoding.ASCII.GetString(reply, 4, 4))
      {
        case "0004":
          string str1 = Encoding.ASCII.GetString(reply, 20, 4);
          int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(reply, 24, 2));
          return int32_1 == 0 ? OperateResult.CreateSuccessResult() : new OperateResult(int32_1, $"The request MID {str1} Select parameter set failed: {OpenProtocolNet.GetErrorText(int32_1)}");
        case "9998":
          string str2 = Encoding.ASCII.GetString(reply, 20, 4);
          int int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(reply, 24, reply.Length - 24 - 1));
          return int32_2 == 0 ? OperateResult.CreateSuccessResult() : new OperateResult(int32_2, $"The request MID {str2} failed: {OpenProtocolNet.GetMid9998Text(int32_2)}");
        default:
          return OperateResult.CreateSuccessResult();
      }
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
  }
}
