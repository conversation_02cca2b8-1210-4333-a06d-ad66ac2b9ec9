﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeDebugRemote
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using System;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// 用于调试的远程管道信息<br />
/// Remote pipeline information for debugging
/// </summary>
/// <remarks>当前的管道主要用于调试，DEMO界面测试时可以直接连接远程的Debug服务器，方便调试，屏蔽了握手报文</remarks>
public class PipeDebugRemote : PipeTcpNet
{
  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSslNet.#ctor(System.Boolean)" />
  public PipeDebugRemote()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSslNet.#ctor(System.String,System.Int32,System.Boolean)" />
  public PipeDebugRemote(string ipAddress, int port)
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSslNet.#ctor(System.Net.Sockets.Socket,System.Net.IPEndPoint,System.Boolean)" />
  public PipeDebugRemote(Socket socket, IPEndPoint iPEndPoint)
  {
    this.Socket = socket;
    this.IpAddress = iPEndPoint.Address.ToString();
    this.Port = iPEndPoint.Port;
  }

  /// <summary>
  /// 获取或设置客户端的key信息，用于调试服务器区分不同的客户端，这样就支持多个客户端连接到同一个服务器，默认是0<br />
  /// Get or set the client's key information, used to distinguish different clients in the debug server, so that multiple clients can connect to the same server, the default is 0
  /// </summary>
  public ushort ClientKey { get; set; } = 0;

  private byte[] CreateHeaderBytes(byte[] sendValue)
  {
    byte[] headerBytes = new byte[6];
    BitConverter.GetBytes(this.ClientKey).CopyTo((Array) headerBytes, 0);
    BitConverter.GetBytes(sendValue.Length).CopyTo((Array) headerBytes, 2);
    return headerBytes;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    this.Send(this.CreateHeaderBytes(sendValue));
    return base.ReadFromCoreServer(netMessage, sendValue, hasResponseData, logMessage);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    OperateResult operateResult1 = await this.SendAsync(this.CreateHeaderBytes(sendValue));
    OperateResult<byte[]> operateResult = await base.ReadFromCoreServerAsync(netMessage, sendValue, hasResponseData, logMessage);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"PipeTcpNet[{this.IpAddress}:{this.Port}]";
}
