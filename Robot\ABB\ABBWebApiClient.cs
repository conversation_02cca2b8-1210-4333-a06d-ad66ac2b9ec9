﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.ABB.ABBWebApiClient
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;

#nullable disable
namespace HslCommunication.Robot.ABB;

/// <summary>
/// ABB机器人的web api接口的客户端，可以方便快速的获取到abb机器人的一些数据信息<br />
/// The client of ABB robot's web API interface can easily and quickly obtain some data information of ABB robot
/// </summary>
/// <remarks>
/// 参考的界面信息是：http://developercenter.robotstudio.com/webservice/api_reference
/// 
/// 关于额外的地址说明，如果想要查看，可以调用<see cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetSelectStrings" /> 返回字符串列表来看看。
/// </remarks>
public class ABBWebApiClient : NetworkWebApiRobotBase, IRobotNet
{
  /// <summary>
  /// 使用指定的ip地址来初始化对象<br />
  /// Initializes the object using the specified IP address
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  public ABBWebApiClient(string ipAddress)
    : base(ipAddress)
  {
  }

  /// <summary>
  /// 使用指定的ip地址和端口号来初始化对象<br />
  /// Initializes the object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号信息</param>
  public ABBWebApiClient(string ipAddress, int port)
    : base(ipAddress, port)
  {
  }

  /// <summary>
  /// 使用指定的ip地址，端口号，用户名，密码来初始化对象<br />
  /// Initialize the object with the specified IP address, port number, username, and password
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号信息</param>
  /// <param name="name">用户名</param>
  /// <param name="password">密码</param>
  public ABBWebApiClient(string ipAddress, int port, string name, string password)
    : base(ipAddress, port, name, password)
  {
  }

  /// <inheritdoc />
  [HslMqttApi(ApiTopic = "ReadRobotByte", Description = "Read the other side of the data information, usually designed for the GET method information.If you start with url=, you are using native address access")]
  public override OperateResult<byte[]> Read(string address) => base.Read(address);

  /// <inheritdoc />
  [HslMqttApi(ApiTopic = "ReadRobotString", Description = "The string data information that reads the other party information, usually designed for the GET method information.If you start with url=, you are using native address access")]
  public override OperateResult<string> ReadString(string address) => base.ReadString(address);

  /// <inheritdoc />
  [HslMqttApi(ApiTopic = "WriteRobotByte", Description = "Using POST to request data information from the other party, we need to start with url= to indicate that we are using native address access")]
  public override OperateResult Write(string address, byte[] value) => base.Write(address, value);

  /// <inheritdoc />
  [HslMqttApi(ApiTopic = "WriteRobotString", Description = "Using POST to request data information from the other party, we need to start with url= to indicate that we are using native address access")]
  public override OperateResult Write(string address, string value) => base.Write(address, value);

  /// <inheritdoc />
  protected override OperateResult<string> ReadByAddress(string address)
  {
    if (address.ToUpper() == "ErrorState".ToUpper())
      return this.GetErrorState();
    if (address.ToUpper() == "jointtarget".ToUpper() || address.ToUpper() == "PhysicalJoints".ToUpper())
      return this.GetJointTarget();
    if (address.ToUpper() == "SpeedRatio".ToUpper())
      return this.GetSpeedRatio();
    if (address.ToUpper() == "OperationMode".ToUpper())
      return this.GetOperationMode();
    if (address.ToUpper() == "CtrlState".ToUpper())
      return this.GetCtrlState();
    if (address.ToUpper() == "ioin".ToUpper())
      return this.GetIOIn();
    if (address.ToUpper() == "ioout".ToUpper())
      return this.GetIOOut();
    if (address.ToUpper() == "io2in".ToUpper())
      return this.GetIO2In();
    if (address.ToUpper() == "io2out".ToUpper())
      return this.GetIO2Out();
    if (address.ToUpper().StartsWith("log".ToUpper()))
    {
      int result;
      return address.Length > 3 && int.TryParse(address.Substring(3), out result) ? this.GetLog(result) : this.GetLog();
    }
    if (address.ToUpper() == "system".ToUpper())
      return this.GetSystem();
    if (address.ToUpper() == "robtarget".ToUpper())
      return this.GetRobotTarget();
    if (address.ToUpper() == "ServoEnable".ToUpper())
      return this.GetServoEnable();
    if (address.ToUpper() == "RapidExecution".ToUpper())
      return this.GetRapidExecution();
    return address.ToUpper() == "RapidTasks".ToUpper() ? this.GetRapidTasks() : base.ReadByAddress(address);
  }

  /// <inheritdoc />
  protected override async Task<OperateResult<string>> ReadByAddressAsync(string address)
  {
    if (address.ToUpper() == "ErrorState".ToUpper())
    {
      OperateResult<string> errorStateAsync = await this.GetErrorStateAsync();
      return errorStateAsync;
    }
    if (address.ToUpper() == "jointtarget".ToUpper())
    {
      OperateResult<string> jointTargetAsync = await this.GetJointTargetAsync();
      return jointTargetAsync;
    }
    if (address.ToUpper() == "PhysicalJoints".ToUpper())
    {
      OperateResult<string> jointTargetAsync = await this.GetJointTargetAsync();
      return jointTargetAsync;
    }
    if (address.ToUpper() == "SpeedRatio".ToUpper())
    {
      OperateResult<string> speedRatioAsync = await this.GetSpeedRatioAsync();
      return speedRatioAsync;
    }
    if (address.ToUpper() == "OperationMode".ToUpper())
    {
      OperateResult<string> operationModeAsync = await this.GetOperationModeAsync();
      return operationModeAsync;
    }
    if (address.ToUpper() == "CtrlState".ToUpper())
    {
      OperateResult<string> ctrlStateAsync = await this.GetCtrlStateAsync();
      return ctrlStateAsync;
    }
    if (address.ToUpper() == "ioin".ToUpper())
    {
      OperateResult<string> ioInAsync = await this.GetIOInAsync();
      return ioInAsync;
    }
    if (address.ToUpper() == "ioout".ToUpper())
    {
      OperateResult<string> ioOutAsync = await this.GetIOOutAsync();
      return ioOutAsync;
    }
    if (address.ToUpper() == "io2in".ToUpper())
    {
      OperateResult<string> io2InAsync = await this.GetIO2InAsync();
      return io2InAsync;
    }
    if (address.ToUpper() == "io2out".ToUpper())
    {
      OperateResult<string> io2OutAsync = await this.GetIO2OutAsync();
      return io2OutAsync;
    }
    if (address.ToUpper().StartsWith("log".ToUpper()))
    {
      int length;
      if (address.Length > 3 && int.TryParse(address.Substring(3), out length))
      {
        OperateResult<string> logAsync = await this.GetLogAsync(length);
        return logAsync;
      }
      OperateResult<string> logAsync1 = await this.GetLogAsync();
      return logAsync1;
    }
    if (address.ToUpper() == "system".ToUpper())
    {
      OperateResult<string> systemAsync = await this.GetSystemAsync();
      return systemAsync;
    }
    if (address.ToUpper() == "robtarget".ToUpper())
    {
      OperateResult<string> robotTargetAsync = await this.GetRobotTargetAsync();
      return robotTargetAsync;
    }
    if (address.ToUpper() == "ServoEnable".ToUpper())
    {
      OperateResult<string> servoEnableAsync = await this.GetServoEnableAsync();
      return servoEnableAsync;
    }
    if (address.ToUpper() == "RapidExecution".ToUpper())
    {
      OperateResult<string> rapidExecutionAsync = await this.GetRapidExecutionAsync();
      return rapidExecutionAsync;
    }
    if (address.ToUpper() == "RapidTasks".ToUpper())
    {
      OperateResult<string> rapidTasksAsync = await this.GetRapidTasksAsync();
      return rapidTasksAsync;
    }
    OperateResult<string> operateResult = await base.ReadByAddressAsync(address);
    return operateResult;
  }

  /// <summary>
  /// 获取当前支持的读取的地址列表<br />
  /// Gets a list of addresses for currently supported reads
  /// </summary>
  /// <returns>数组信息</returns>
  public static List<string> GetSelectStrings()
  {
    return new List<string>()
    {
      "ErrorState",
      "jointtarget",
      "PhysicalJoints",
      "SpeedRatio",
      "OperationMode",
      "CtrlState",
      "ioin",
      "ioout",
      "io2in",
      "io2out",
      "log",
      "system",
      "robtarget",
      "ServoEnable",
      "RapidExecution",
      "RapidTasks"
    };
  }

  private OperateResult<string> ParseSpanByClass(string content, string className)
  {
    Match match = Regex.Match(content, $"<span class=\"{className}\">[^<]+");
    return !match.Success ? new OperateResult<string>($"Parse None class [{className}] Span\r\n{content}") : OperateResult.CreateSuccessResult<string>(match.Value.Substring(match.Value.IndexOf('>') + 1));
  }

  private OperateResult<double[]> ParseDoubleListSpanByClass(string content, string className)
  {
    MatchCollection matchCollection = Regex.Matches(content, $"<span class=\"{className}\">[^<]+");
    double[] numArray = new double[matchCollection.Count];
    for (int i = 0; i < matchCollection.Count; ++i)
      numArray[i] = Convert.ToDouble(matchCollection[i].Value.Substring(matchCollection[i].Value.IndexOf('>') + 1));
    return OperateResult.CreateSuccessResult<double[]>(numArray);
  }

  private OperateResult<string> ParseListSpanByClass<T>(
    string content,
    string className,
    Func<string, T> trans)
  {
    MatchCollection matchCollection = Regex.Matches(content, $"<span class=\"{className}\">[^<]+");
    JArray jarray = new JArray();
    for (int i = 0; i < matchCollection.Count; ++i)
      jarray.Add((object) trans(matchCollection[i].Value.Substring(matchCollection[i].Value.IndexOf('>') + 1)));
    return OperateResult.CreateSuccessResult<string>(jarray.ToString());
  }

  private JObject ParseListByClass(string content)
  {
    XElement xelement = XElement.Parse(content);
    JObject listByClass = new JObject();
    foreach (XElement element in xelement.Elements((XName) "span"))
      listByClass.Add(element.Attribute((XName) "class").Value, (JToken) element.Value);
    return listByClass;
  }

  private OperateResult<string> ParseJObjectByClass(string content, string className)
  {
    Match match = Regex.Match(content, $"<li class=\"{className}\"[\\S\\s]+?</li>");
    return !match.Success ? new OperateResult<string>($"Parse None class [{className}] List\r\n{content}") : OperateResult.CreateSuccessResult<string>(this.ParseListByClass(match.Value).ToString());
  }

  private OperateResult<string> ParseJArrayByClass(string content, string className, int maxCount = 2147483647 /*0x7FFFFFFF*/)
  {
    MatchCollection matchCollection = Regex.Matches(content, $"<li class=\"{className}\"[\\S\\s]+?</li>");
    JArray jarray = new JArray();
    for (int i = 0; i < matchCollection.Count && i < maxCount; ++i)
      jarray.Add((JToken) this.ParseListByClass(matchCollection[i].Value));
    return OperateResult.CreateSuccessResult<string>(jarray.ToString());
  }

  /// <summary>
  /// 获取当前的控制状态，Content属性就是机器人的控制信息<br />
  /// Get the current control state. The Content attribute is the control information of the robot
  /// </summary>
  /// <returns>带有状态信息的结果类对象</returns>
  [HslMqttApi(Description = "Get the current control state. The Content attribute is the control information of the robot")]
  public OperateResult<string> GetCtrlState()
  {
    return this.ReadString("url=/rw/panel/ctrlstate").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "ctrlstate")));
  }

  /// <summary>
  /// 获取当前的错误状态，Content属性就是机器人的状态信息<br />
  /// Gets the current error state. The Content attribute is the state information of the robot
  /// </summary>
  /// <returns>带有状态信息的结果类对象</returns>
  [HslMqttApi(Description = "Gets the current error state. The Content attribute is the state information of the robot")]
  public OperateResult<string> GetErrorState()
  {
    return this.ReadString("url=/rw/motionsystem/errorstate").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "err-state")));
  }

  /// <summary>
  /// 获取当前机器人的物理关节点信息，返回json格式的关节信息<br />
  /// Get the physical node information of the current robot and return the joint information in json format
  /// </summary>
  /// <param name="mechunit">操作单元，默认为 ROB_1</param>
  /// <returns>带有关节信息的结果类对象</returns>
  [HslMqttApi(Description = "Get the physical node information of the current robot and return the joint information in json format")]
  public OperateResult<string> GetJointTarget(string mechunit = "ROB_1")
  {
    return this.ReadString($"url=/rw/motionsystem/mechunits/{mechunit}/jointtarget").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseListSpanByClass<double>(m, "((rax_[0-9]+)|(eax_[a-z]))", (Func<string, double>) (n => Convert.ToDouble(n)))));
  }

  /// <summary>
  /// 获取当前机器人的速度配比信息<br />
  /// Get the speed matching information of the current robot
  /// </summary>
  /// <returns>带有速度信息的结果类对象</returns>
  [HslMqttApi(Description = "Get the speed matching information of the current robot")]
  public OperateResult<string> GetSpeedRatio()
  {
    return this.ReadString("url=/rw/panel/speedratio").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "speedratio")));
  }

  /// <summary>
  /// 获取当前机器人的工作模式<br />
  /// Gets the current working mode of the robot
  /// </summary>
  /// <returns>带有工作模式信息的结果类对象</returns>
  [HslMqttApi(Description = "Gets the current working mode of the robot")]
  public OperateResult<string> GetOperationMode()
  {
    return this.ReadString("url=/rw/panel/opmode").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "opmode")));
  }

  /// <summary>
  /// 获取当前机器人的本机的输入IO<br />
  /// Gets the input IO of the current robot's native
  /// </summary>
  /// <returns>带有IO信息的结果类对象</returns>
  [HslMqttApi(Description = "Gets the input IO of the current robot's native")]
  public OperateResult<string> GetIOIn()
  {
    return this.ReadString("url=/rw/iosystem/devices/D652_10").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "indata")));
  }

  /// <summary>
  /// 获取当前机器人的本机的输出IO<br />
  /// Gets the output IO of the current robot's native
  /// </summary>
  /// <returns>带有IO信息的结果类对象</returns>
  [HslMqttApi(Description = "Gets the output IO of the current robot's native")]
  public OperateResult<string> GetIOOut()
  {
    return this.ReadString("url=/rw/iosystem/devices/D652_10").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "outdata")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetIOIn" />
  [HslMqttApi(Description = "Gets the input IO2 of the current robot's native")]
  public OperateResult<string> GetIO2In()
  {
    return this.ReadString("url=/rw/iosystem/devices/BK5250").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "indata")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetIOOut" />
  [HslMqttApi(Description = "Gets the output IO2 of the current robot's native")]
  public OperateResult<string> GetIO2Out()
  {
    return this.ReadString("url=/rw/iosystem/devices/BK5250").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "outdata")));
  }

  /// <summary>
  /// 获取当前机器人的日志记录，默认记录为10条<br />
  /// Gets the log record for the current robot, which is 10 by default
  /// </summary>
  /// <param name="logCount">读取的最大的日志总数</param>
  /// <returns>带有IO信息的结果类对象</returns>
  [HslMqttApi(Description = "Gets the log record for the current robot, which is 10 by default")]
  public OperateResult<string> GetLog(int logCount = 10)
  {
    return this.ReadString("url=/rw/elog/0?lang=zh&amp;resource=title").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJArrayByClass(m, "elog-message-li", logCount)));
  }

  /// <summary>
  /// 获取当前机器人的系统信息，版本号，唯一ID等信息<br />
  /// Get the current robot's system information, version number, unique ID and other information
  /// </summary>
  /// <returns>系统的基本信息</returns>
  [HslMqttApi(Description = "Get the current robot's system information, version number, unique ID and other information")]
  public OperateResult<string> GetSystem()
  {
    return this.ReadString("url=/rw/system").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "sys-system-li")));
  }

  /// <summary>
  /// 获取机器人的目标坐标信息<br />
  /// Get the current robot's target information
  /// </summary>
  /// <returns>系统的基本信息</returns>
  [HslMqttApi(Description = "Get the current robot's target information")]
  public OperateResult<string> GetRobotTarget()
  {
    return this.ReadString("url=/rw/motionsystem/mechunits/ROB_1/robtarget").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "ms-robtargets")));
  }

  /// <summary>
  /// 获取当前机器人的伺服使能状态<br />
  /// Get the current robot servo enable state
  /// </summary>
  /// <returns>机器人的伺服使能状态</returns>
  [HslMqttApi(Description = "Get the current robot servo enable state")]
  public OperateResult<string> GetServoEnable()
  {
    return this.ReadString("url=/rw/iosystem/signals/Local/DRV_1/DRV1K1").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "ios-signal")));
  }

  /// <summary>
  /// 获取当前机器人的当前程序运行状态<br />
  /// Get the current program running status of the current robot
  /// </summary>
  /// <returns>机器人的当前的程序运行状态</returns>
  [HslMqttApi(Description = "Get the current program running status of the current robot")]
  public OperateResult<string> GetRapidExecution()
  {
    return this.ReadString("url=/rw/rapid/execution").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "rap-execution")));
  }

  /// <summary>
  /// 获取当前机器人的任务列表<br />
  /// Get the task list of the current robot
  /// </summary>
  /// <returns>任务信息的列表</returns>
  [HslMqttApi(Description = "Get the task list of the current robot")]
  public OperateResult<string> GetRapidTasks()
  {
    return this.ReadString("url=/rw/rapid/tasks").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJArrayByClass(m, "rap-task-li")));
  }

  /// <summary>
  /// 根据给定的名称，获取当前用户的数据值信息。<br />
  /// According to the given name, gets the data value information of the current user
  /// </summary>
  /// <param name="name">数据名称信息，例如 nCurProgIndex</param>
  /// <returns>数值信息</returns>
  public OperateResult<double[]> GetUserValue(string name)
  {
    return this.ReadString(name.StartsWith("url=", StringComparison.OrdinalIgnoreCase) ? name : "url=/rw/rapid/symbol/data/RAPID/T_ROB1/user/" + name).Then<double[]>((Func<string, OperateResult<double[]>>) (m => this.ParseDoubleListSpanByClass(m, "value")));
  }

  /// <summary>
  /// 获取机器人的IO信号资源<br />
  /// Get an IO signal resource.
  /// </summary>
  /// <returns>系统的基本信息</returns>
  [HslMqttApi(Description = "Get the current robot's target information")]
  public OperateResult<string> GetAnIOSignal(string network = "Local", string unit = "DRV_1", string signal = "DRV1K1")
  {
    return this.ReadString($"url=/rw/iosystem/signals/{network}/{unit}/{signal}").Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "ios-signal")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetCtrlState" />
  public async Task<OperateResult<string>> GetCtrlStateAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/panel/ctrlstate").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "ctrlstate")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetErrorState" />
  public async Task<OperateResult<string>> GetErrorStateAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/motionsystem/errorstate").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "err-state")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetJointTarget(System.String)" />
  public async Task<OperateResult<string>> GetJointTargetAsync(string mechunit = "ROB_1")
  {
    OperateResult<string> operateResult = await this.ReadStringAsync($"url=/rw/motionsystem/mechunits/{mechunit}/jointtarget").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseListSpanByClass<double>(m, "((rax_[0-9]+)|(eax_[a-z]))", (Func<string, double>) (n => Convert.ToDouble(n)))));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetSpeedRatio" />
  public async Task<OperateResult<string>> GetSpeedRatioAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/panel/speedratio").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "speedratio")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetOperationMode" />
  public async Task<OperateResult<string>> GetOperationModeAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/panel/opmode").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "opmode")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetIOIn" />
  public async Task<OperateResult<string>> GetIOInAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/iosystem/devices/D652_10").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "indata")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetIOOut" />
  public async Task<OperateResult<string>> GetIOOutAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/iosystem/devices/D652_10").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "outdata")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetIOIn" />
  public async Task<OperateResult<string>> GetIO2InAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/iosystem/devices/BK5250").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "indata")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetIOOut" />
  public async Task<OperateResult<string>> GetIO2OutAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/iosystem/devices/BK5250").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseSpanByClass(m, "outdata")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetLog(System.Int32)" />
  public async Task<OperateResult<string>> GetLogAsync(int logCount = 10)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/elog/0?lang=zh&amp;resource=title").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJArrayByClass(m, "elog-message-li", logCount)));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetSystem" />
  public async Task<OperateResult<string>> GetSystemAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/system").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "sys-system-li")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetRobotTarget" />
  public async Task<OperateResult<string>> GetRobotTargetAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/motionsystem/mechunits/ROB_1/robtarget").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "ms-robtargets")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetServoEnable" />
  public async Task<OperateResult<string>> GetServoEnableAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/iosystem/signals/Local/DRV_1/DRV1K1").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "ios-signal")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetRapidExecution" />
  public async Task<OperateResult<string>> GetRapidExecutionAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/rapid/execution").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "rap-execution")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetRapidTasks" />
  public async Task<OperateResult<string>> GetRapidTasksAsync()
  {
    OperateResult<string> operateResult = await this.ReadStringAsync("url=/rw/rapid/tasks").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJArrayByClass(m, "rap-task-li")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetUserValue(System.String)" />
  public async Task<OperateResult<double[]>> GetUserValueAsync(string name)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(name.StartsWith("url=", StringComparison.OrdinalIgnoreCase) ? name : "url=/rw/rapid/symbol/data/RAPID/T_ROB1/user/" + name).ConfigureAwait(false);
    return operateResult.Then<double[]>((Func<string, OperateResult<double[]>>) (m => this.ParseDoubleListSpanByClass(m, "value")));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.ABB.ABBWebApiClient.GetAnIOSignal(System.String,System.String,System.String)" />
  public async Task<OperateResult<string>> GetAnIOSignalAsync(
    string network = "Local",
    string unit = "DRV_1",
    string signal = "DRV1K1")
  {
    OperateResult<string> operateResult = await this.ReadStringAsync($"url=/rw/iosystem/signals/{network}/{unit}/{signal}").ConfigureAwait(false);
    return operateResult.Then<string>((Func<string, OperateResult<string>>) (m => this.ParseJObjectByClass(m, "ios-signal")));
  }

  /// <inheritdoc />
  public override string ToString() => $"ABBWebApiClient[{this.IpAddress}:{this.Port}]";
}
