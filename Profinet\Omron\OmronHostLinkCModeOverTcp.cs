﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronHostLinkCModeOverTcp
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Omron.Helper;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// 欧姆龙的HostLink的C-Mode实现形式，当前的类是通过以太网透传实现。地址支持携带站号信息，例如：s=2;D100<br />
/// The C-Mode implementation form of Omron’s HostLink, the current class is realized through Ethernet transparent transmission.
/// Address supports carrying station number information, for example: s=2;D100
/// </summary>
/// <remarks>
/// 暂时只支持的字数据的读写操作，不支持位的读写操作。另外本模式下，程序要在监视模式运行才能写数据，欧姆龙官方回复的。
/// </remarks>
public class OmronHostLinkCModeOverTcp : DeviceTcpNet, IHostLinkCMode, IReadWriteNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.#ctor" />
  public OmronHostLinkCModeOverTcp()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.WordLength = (ushort) 1;
    this.ByteTransform.IsStringReverseByteWord = true;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronCipNet.#ctor(System.String,System.Int32)" />
  public OmronHostLinkCModeOverTcp(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.UnitNumber" />
  public byte UnitNumber { get; set; }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return HslHelper.ReadBool((IReadWriteNet) this, address, length, reverseByWord: true);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return HslHelper.WriteBool((IReadWriteNet) this, address, value, reverseByWord: true);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return OmronHostLinkCModeHelper.Read((IReadWriteDevice) this, this.UnitNumber, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return OmronHostLinkCModeHelper.Write((IReadWriteDevice) this, this.UnitNumber, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronHostLinkCModeOverTcp.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await OmronHostLinkCModeHelper.ReadAsync((IReadWriteDevice) this, this.UnitNumber, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronHostLinkCModeOverTcp.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await OmronHostLinkCModeHelper.WriteAsync((IReadWriteDevice) this, this.UnitNumber, address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) this, address, length, reverseByWord: true);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult operateResult = await HslHelper.WriteBoolAsync((IReadWriteNet) this, address, value, reverseByWord: true);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  [HslMqttApi("读取PLC的当前的型号信息")]
  public OperateResult<string> ReadPlcType() => this.ReadPlcType(this.UnitNumber);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult<string> ReadPlcType(byte unitNumber)
  {
    return OmronHostLinkCModeHelper.ReadPlcType((IReadWriteDevice) this, unitNumber);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.ReadPlcMode(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  [HslMqttApi("读取PLC当前的操作模式，0: 编程模式  1: 运行模式  2: 监视模式")]
  public OperateResult<int> ReadPlcMode() => this.ReadPlcMode(this.UnitNumber);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.ReadPlcMode(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult<int> ReadPlcMode(byte unitNumber)
  {
    return OmronHostLinkCModeHelper.ReadPlcMode((IReadWriteDevice) this, unitNumber);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.ChangePlcMode(HslCommunication.Core.IReadWriteDevice,System.Byte,System.Byte)" />
  [HslMqttApi("将当前PLC的模式变更为指定的模式，0: 编程模式  1: 运行模式  2: 监视模式")]
  public OperateResult ChangePlcMode(byte mode) => this.ChangePlcMode(this.UnitNumber, mode);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkCModeHelper.ChangePlcMode(HslCommunication.Core.IReadWriteDevice,System.Byte,System.Byte)" />
  public OperateResult ChangePlcMode(byte unitNumber, byte mode)
  {
    return OmronHostLinkCModeHelper.ChangePlcMode((IReadWriteDevice) this, unitNumber, mode);
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronHostLinkCModeOverTcp[{this.IpAddress}:{this.Port}]";
}
