﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.SAMMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>SAM身份证通信协议的消息</summary>
public class SAMMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 7;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes == null || this.HeadBytes[0] == (byte) 170 && this.HeadBytes[1] == (byte) 170 && this.HeadBytes[2] == (byte) 170 && this.HeadBytes[3] == (byte) 150 && this.HeadBytes[4] == (byte) 105;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.SAMMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    byte[] headBytes = this.HeadBytes;
    return headBytes != null && headBytes.Length >= 7 ? (int) this.HeadBytes[5] * 256 /*0x0100*/ + (int) this.HeadBytes[6] : 0;
  }
}
