﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.AdsNetMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>倍福的ADS协议的信息</summary>
public class AdsNetMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 6;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    byte[] headBytes = this.HeadBytes;
    if (headBytes == null || headBytes.Length < 6)
      return 0;
    int lengthByHeadBytes = BitConverter.ToInt32(this.HeadBytes, 2);
    if (lengthByHeadBytes > 100000000)
      lengthByHeadBytes = 100000000;
    if (lengthByHeadBytes < 0)
      lengthByHeadBytes = 0;
    return lengthByHeadBytes;
  }
}
