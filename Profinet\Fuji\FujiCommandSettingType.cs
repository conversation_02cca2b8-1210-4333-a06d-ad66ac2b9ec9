﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Fuji.FujiCommandSettingType
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Fuji;

/// <summary>
/// 基于Command-Setting-Type通信模式的协议实现，地址格式参数DEMO界面<br />
/// Protocol implementation based on Command-Setting-Type communication mode, address format parameter DEMO interface
/// </summary>
/// <remarks>
/// 本类实例化对象之后，还需要设置<see cref="P:HslCommunication.Profinet.Fuji.FujiCommandSettingType.DataSwap" />属性，根据实际情况来设置。
/// </remarks>
public class FujiCommandSettingType : DeviceTcpNet
{
  private bool dataSwap = false;

  /// <summary>实例化一个默认的对象</summary>
  public FujiCommandSettingType()
  {
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.WordLength = (ushort) 2;
  }

  /// <summary>使用指定的IP地址和端口号来实例化一个对象</summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号信息</param>
  public FujiCommandSettingType(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new FujiCommandSettingTypeMessage();
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return FujiCommandSettingType.UnpackResponseContentHelper(send, response);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = FujiCommandSettingType.BuildReadCommand(address, length);
    return !operateResult.IsSuccess ? operateResult : this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = FujiCommandSettingType.BuildWriteCommand(address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiCommandSettingType.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> bulid = FujiCommandSettingType.BuildReadCommand(address, length);
    if (!bulid.IsSuccess)
      return bulid;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(bulid.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiCommandSettingType.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> bulid = FujiCommandSettingType.BuildWriteCommand(address, value);
    if (!bulid.IsSuccess)
      return (OperateResult) bulid;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(bulid.Content);
    return (OperateResult) operateResult;
  }

  /// <summary>
  /// 读取指定地址的byte数据，地址格式 S100 <br />
  /// Reads the byte data of the specified address, the address format S100
  /// </summary>
  /// <param name="address">起始地址，格式为S100 </param>
  /// <returns>是否读取成功的结果对象</returns>
  /// <example>参考<see cref="M:HslCommunication.Profinet.Fuji.FujiCommandSettingType.Read(System.String,System.UInt16)" />的注释</example>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>
  /// 向PLC中写入byte数据，返回值说明<br />
  /// Write byte data to the PLC, return value description
  /// </summary>
  /// <param name="address">起始地址，格式为 S100</param>
  /// <param name="value">byte数据</param>
  /// <returns>是否写入成功的结果对象 </returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiCommandSettingType.Read(System.String,System.UInt16)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiCommandSettingType.Write(System.String,System.Byte[])" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  /// <summary>
  /// 获取或设置当前的对象是否进行数据交换操作，将根据PLC的实际值来设定。<br />
  /// Get or set whether the current object performs data exchange operation or not, it will be set according to the actual value of the PLC.
  /// </summary>
  public bool DataSwap
  {
    get => this.dataSwap;
    set
    {
      this.dataSwap = value;
      if (value)
        this.ByteTransform = (IByteTransform) new RegularByteTransform();
      else
        this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"FujiCommandSettingType[{this.IpAddress}:{this.Port}]";

  /// <summary>构建读取的报文指令</summary>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>报文构建的结果对象</returns>
  public static OperateResult<byte[]> BuildReadCommand(string address, ushort length)
  {
    OperateResult<FujiCommandSettingTypeAddress> from = FujiCommandSettingTypeAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    return OperateResult.CreateSuccessResult<byte[]>(new byte[9]
    {
      (byte) 0,
      (byte) 0,
      (byte) 0,
      from.Content.DataCode,
      (byte) 4,
      BitConverter.GetBytes(from.Content.AddressStart)[0],
      BitConverter.GetBytes(from.Content.AddressStart)[1],
      BitConverter.GetBytes(from.Content.Length)[0],
      BitConverter.GetBytes(from.Content.Length)[1]
    });
  }

  /// <summary>构建写入原始报文数据的请求信息</summary>
  /// <param name="address">地址数据</param>
  /// <param name="value">原始报文的数据</param>
  /// <returns>原始的写入报文数据</returns>
  public static OperateResult<byte[]> BuildWriteCommand(string address, byte[] value)
  {
    OperateResult<FujiCommandSettingTypeAddress> from = FujiCommandSettingTypeAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    byte[] numArray = new byte[9 + value.Length];
    numArray[0] = (byte) 1;
    numArray[1] = (byte) 0;
    numArray[2] = (byte) 0;
    numArray[3] = from.Content.DataCode;
    numArray[4] = (byte) (4 + value.Length);
    numArray[5] = BitConverter.GetBytes(from.Content.AddressStart)[0];
    numArray[6] = BitConverter.GetBytes(from.Content.AddressStart)[1];
    numArray[7] = BitConverter.GetBytes(from.Content.Length)[0];
    numArray[8] = BitConverter.GetBytes(from.Content.Length)[0];
    value.CopyTo((Array) numArray, 9);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>根据错误信息获取相关数据信息</summary>
  /// <param name="error">错误代号</param>
  /// <returns>实际的错误描述信息</returns>
  public static string GetErrorText(int error)
  {
    switch (error)
    {
      case 18:
        return "Write of data to the program area";
      case 32 /*0x20*/:
        return "Non-existing CMND code";
      case 33:
        return "Input data is not in the order of data corresponding to CMND";
      case 34:
        return "Operation only from the loader is effective. Operation from any other node is disabled";
      case 36:
        return "A non-existing module has been specified";
      case 50:
        return "An address out of the memory size has been specified";
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>根据PLC返回的数据，解析出实际的数据内容</summary>
  /// <param name="send">发送给PLC的数据</param>
  /// <param name="response">PLC返回的数据</param>
  /// <returns>结果数据信息</returns>
  public static OperateResult<byte[]> UnpackResponseContentHelper(byte[] send, byte[] response)
  {
    try
    {
      if (response[1] > (byte) 0)
        return new OperateResult<byte[]>(FujiCommandSettingType.GetErrorText((int) response[1]));
      if (response[0] == (byte) 1)
        return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      return response.Length < 10 ? new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataLengthTooShort}10, Source: {response.ToHexString(' ')}") : OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(10));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"UnpackResponseContentHelper failed: {ex.Message} Source: {response.ToHexString(' ')}");
    }
  }
}
