﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.DcsNanJingAutoMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>南京自动化研究所推出的DCS设备的消息类</summary>
public class DcsNanJingAutoMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 6;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    int? length = this.HeadBytes?.Length;
    int protocolHeadBytesLength = this.ProtocolHeadBytesLength;
    return length.GetValueOrDefault() >= protocolHeadBytesLength & length.HasValue ? (int) this.HeadBytes[4] * 256 /*0x0100*/ + (int) this.HeadBytes[5] : 0;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetHeadBytesIdentity" />
  public override int GetHeadBytesIdentity()
  {
    return (int) this.HeadBytes[0] * 256 /*0x0100*/ + (int) this.HeadBytes[1];
  }
}
