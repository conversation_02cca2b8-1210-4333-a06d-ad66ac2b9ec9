﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Security.RSAHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;

#nullable disable
namespace HslCommunication.Core.Security;

/// <summary>RSA加密解密算法的辅助方法，可以用PEM格式的密钥创建公钥，或是私钥对象，然后用来加解密操作。</summary>
public class RSAHelper
{
  private const string privateKeyHead = "-----BEGIN RSA PRIVATE KEY-----";
  private const string privateKeyEnd = "-----END RSA PRIVATE KEY-----";
  private const string publicKeyHead = "-----BEGIN PUBLIC KEY-----";
  private const string publicKeyEnd = "-----END PUBLIC KEY-----";
  private static readonly byte[] SeqOID = new byte[15]
  {
    (byte) 48 /*0x30*/,
    (byte) 13,
    (byte) 6,
    (byte) 9,
    (byte) 42,
    (byte) 134,
    (byte) 72,
    (byte) 134,
    (byte) 247,
    (byte) 13,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 5,
    (byte) 0
  };

  /// <summary>
  /// 使用 PEM 格式基于base64编码的私钥来创建一个 RSA 算法加密解密的对象，可以直接用于加密解密操作<br />
  /// Use the PEM format based on the base64-encoded private key to create an RSA algorithm encryption and decryption object,
  /// which can be directly used for encryption and decryption operations
  /// </summary>
  /// <param name="privateKeyString">私钥</param>
  /// <returns>RSA 算法的加密解密对象</returns>
  public static RSACryptoServiceProvider CreateRsaProviderFromPrivateKey(string privateKeyString)
  {
    privateKeyString = privateKeyString.Trim();
    if (privateKeyString.StartsWith("-----BEGIN RSA PRIVATE KEY-----"))
      privateKeyString = privateKeyString.Replace("-----BEGIN RSA PRIVATE KEY-----", string.Empty);
    if (privateKeyString.EndsWith("-----END RSA PRIVATE KEY-----"))
      privateKeyString = privateKeyString.Replace("-----END RSA PRIVATE KEY-----", string.Empty);
    return RSAHelper.CreateRsaProviderFromPrivateKey(Convert.FromBase64String(privateKeyString));
  }

  /// <summary>
  /// 使用原始的私钥数据（PEM格式）来创建一个 RSA 算法加密解密的对象，可以直接用于加密解密操作<br />
  /// Use the original private key data (PEM format) to create an RSA algorithm encryption and decryption object,
  /// which can be directly used for encryption and decryption operations
  /// </summary>
  /// <param name="privateKey">原始的私钥数据</param>
  /// <returns>RSA 算法的加密解密对象</returns>
  public static RSACryptoServiceProvider CreateRsaProviderFromPrivateKey(byte[] privateKey)
  {
    RSACryptoServiceProvider providerFromPrivateKey = new RSACryptoServiceProvider();
    RSAParameters parameters = new RSAParameters();
    using (BinaryReader binr = new BinaryReader((Stream) new MemoryStream(privateKey)))
    {
      switch (binr.ReadUInt16())
      {
        case 33072:
          int num1 = (int) binr.ReadByte();
          break;
        case 33328:
          int num2 = (int) binr.ReadInt16();
          break;
        default:
          throw new Exception("Unexpected value read binr.ReadUInt16()");
      }
      if (binr.ReadUInt16() != (ushort) 258)
        throw new Exception("Unexpected version");
      parameters.Modulus = binr.ReadByte() <= (byte) 0 ? binr.ReadBytes(RSAHelper.GetIntegerSize(binr)) : throw new Exception("Unexpected value read binr.ReadByte()");
      parameters.Exponent = binr.ReadBytes(RSAHelper.GetIntegerSize(binr));
      parameters.D = binr.ReadBytes(RSAHelper.GetIntegerSize(binr));
      parameters.P = binr.ReadBytes(RSAHelper.GetIntegerSize(binr));
      parameters.Q = binr.ReadBytes(RSAHelper.GetIntegerSize(binr));
      parameters.DP = binr.ReadBytes(RSAHelper.GetIntegerSize(binr));
      parameters.DQ = binr.ReadBytes(RSAHelper.GetIntegerSize(binr));
      parameters.InverseQ = binr.ReadBytes(RSAHelper.GetIntegerSize(binr));
    }
    providerFromPrivateKey.ImportParameters(parameters);
    return providerFromPrivateKey;
  }

  private static int GetIntegerSize(BinaryReader binr)
  {
    if (binr.ReadByte() != (byte) 2)
      return 0;
    byte num1 = binr.ReadByte();
    int integerSize;
    switch (num1)
    {
      case 129:
        integerSize = (int) binr.ReadByte();
        break;
      case 130:
        byte num2 = binr.ReadByte();
        integerSize = BitConverter.ToInt32(new byte[4]
        {
          binr.ReadByte(),
          num2,
          (byte) 0,
          (byte) 0
        }, 0);
        break;
      default:
        integerSize = (int) num1;
        break;
    }
    while (binr.ReadByte() == (byte) 0)
      --integerSize;
    binr.BaseStream.Seek(-1L, SeekOrigin.Current);
    return integerSize;
  }

  private static byte[] PackKeyHead(byte[] content)
  {
    if (content.Length < 256 /*0x0100*/)
    {
      byte[] numArray = SoftBasic.SpliceArray<byte>("30 81 00".ToHexBytes(), content);
      numArray[2] = BitConverter.GetBytes(numArray.Length - 3)[0];
      return numArray;
    }
    byte[] numArray1 = SoftBasic.SpliceArray<byte>("30 82 00 00".ToHexBytes(), content);
    numArray1[2] = BitConverter.GetBytes(numArray1.Length - 4)[1];
    numArray1[3] = BitConverter.GetBytes(numArray1.Length - 4)[0];
    return numArray1;
  }

  /// <summary>
  /// 从RSA的算法对象里，获取到PEM格式的原始私钥数据，如果需要存储，或是显示，只需要 Convert.ToBase64String 方法<br />
  /// Obtain the original private key data in PEM format from the RSA algorithm object. If you need to store or display it,
  /// you only need the Convert.ToBase64String method
  /// </summary>
  /// <param name="rsa">RSA 算法的加密解密对象</param>
  /// <returns>原始的私钥数据</returns>
  public static byte[] GetPrivateKeyFromRSA(RSACryptoServiceProvider rsa)
  {
    RSAParameters rsaParameters = rsa.ExportParameters(true);
    byte[] modulus = rsaParameters.Modulus;
    byte[] exponent = rsaParameters.Exponent;
    byte[] d = rsaParameters.D;
    byte[] p = rsaParameters.P;
    byte[] q = rsaParameters.Q;
    byte[] dp = rsaParameters.DP;
    byte[] dq = rsaParameters.DQ;
    byte[] inverseQ = rsaParameters.InverseQ;
    MemoryStream ms = new MemoryStream();
    ms.Write(new byte[3]{ (byte) 2, (byte) 1, (byte) 0 }, 0, 3);
    RSAHelper.WriteByteStream(ms, modulus);
    RSAHelper.WriteByteStream(ms, exponent);
    RSAHelper.WriteByteStream(ms, d);
    RSAHelper.WriteByteStream(ms, p);
    RSAHelper.WriteByteStream(ms, q);
    RSAHelper.WriteByteStream(ms, dp);
    RSAHelper.WriteByteStream(ms, dq);
    RSAHelper.WriteByteStream(ms, inverseQ);
    return RSAHelper.PackKeyHead(ms.ToArray());
  }

  private static void WriteByteStream(MemoryStream ms, byte[] data)
  {
    bool flag = data[0] > (byte) 127 /*0x7F*/;
    int num = flag ? data.Length + 1 : data.Length;
    ms.WriteByte((byte) 2);
    if (num < 128 /*0x80*/)
      ms.WriteByte((byte) num);
    else if (num < 256 /*0x0100*/)
    {
      ms.WriteByte((byte) 129);
      ms.WriteByte((byte) num);
    }
    else
    {
      ms.WriteByte((byte) 130);
      ms.WriteByte(BitConverter.GetBytes(num)[1]);
      ms.WriteByte(BitConverter.GetBytes(num)[0]);
    }
    if (flag)
      ms.WriteByte((byte) 0);
    ms.Write(data, 0, data.Length);
  }

  /// <summary>
  /// 从RSA的算法对象里，获取到PEM格式的原始公钥数据，如果需要存储，或是显示，只需要 Convert.ToBase64String 方法<br />
  /// Obtain the original public key data in PEM format from the RSA algorithm object. If you need to store or display it,
  /// you only need the Convert.ToBase64String method
  /// </summary>
  /// <param name="rsa">RSA 算法的加密解密对象</param>
  /// <returns>原始的公钥数据</returns>
  public static byte[] GetPublicKeyFromRSA(RSACryptoServiceProvider rsa)
  {
    RSAParameters rsaParameters = rsa.ExportParameters(false);
    byte[] modulus = rsaParameters.Modulus;
    byte[] exponent = rsaParameters.Exponent;
    MemoryStream ms = new MemoryStream();
    RSAHelper.WriteByteStream(ms, modulus);
    RSAHelper.WriteByteStream(ms, exponent);
    byte[] numArray = RSAHelper.PackKeyHead(SoftBasic.SpliceArray<byte>(new byte[1], RSAHelper.PackKeyHead(ms.ToArray())));
    numArray[0] = (byte) 3;
    return RSAHelper.PackKeyHead(SoftBasic.SpliceArray<byte>(RSAHelper.SeqOID, numArray));
  }

  /// <summary>
  /// PEM 格式基于base64编码的公钥来创建一个 RSA 算法加密解密的对象，可以直接用于加密或是验证签名操作<br />
  /// Use the original public key data (PEM format) to create an RSA algorithm encryption and decryption object,
  /// which can be directly used for encryption or signature verification
  /// </summary>
  /// <param name="publicKeyString">公钥</param>
  /// <returns>RSA 算法的加密解密对象</returns>
  public static RSACryptoServiceProvider CreateRsaProviderFromPublicKey(string publicKeyString)
  {
    publicKeyString = publicKeyString.Trim();
    if (publicKeyString.StartsWith("-----BEGIN PUBLIC KEY-----"))
      publicKeyString = publicKeyString.Replace("-----BEGIN PUBLIC KEY-----", string.Empty);
    if (publicKeyString.EndsWith("-----END PUBLIC KEY-----"))
      publicKeyString = publicKeyString.Replace("-----END PUBLIC KEY-----", string.Empty);
    return RSAHelper.CreateRsaProviderFromPublicKey(Convert.FromBase64String(publicKeyString));
  }

  /// <summary>
  /// 对原始字节的数据进行加密，不限制长度，因为RSA本身限制了117字节，所以此处进行数据切割加密。<br />
  /// Encrypt the original byte data without limiting the length, because RSA itself limits 117 bytes, so the data is cut and encrypted here.
  /// </summary>
  /// <param name="provider">RSA公钥对象</param>
  /// <param name="data">等待加密的原始数据</param>
  /// <returns>加密之后的结果信息</returns>
  public static byte[] EncryptLargeDataByRSA(RSACryptoServiceProvider provider, byte[] data)
  {
    MemoryStream memoryStream = new MemoryStream();
    List<byte[]> numArrayList = SoftBasic.ArraySplitByLength<byte>(data, 110);
    for (int index = 0; index < numArrayList.Count; ++index)
    {
      byte[] buffer = provider.Encrypt(numArrayList[index], false);
      memoryStream.Write(buffer, 0, buffer.Length);
    }
    return memoryStream.ToArray();
  }

  /// <summary>
  /// 对超过117字节限制的加密数据进行解密，解密出原始的字节数据，因为RSA本身限制了117字节，所以此处进行数据切割解密。<br />
  /// Decrypt the encrypted data that exceeds the 117-byte limit and decrypt the original byte data, because RSA itself limits 117 bytes, so the data is cut and decrypted here.
  /// </summary>
  /// <param name="provider">RSA私钥对象</param>
  /// <param name="data">等待解密的数据</param>
  /// <returns>解密之后的结果数据</returns>
  public static byte[] DecryptLargeDataByRSA(RSACryptoServiceProvider provider, byte[] data)
  {
    MemoryStream memoryStream = new MemoryStream();
    List<byte[]> numArrayList = SoftBasic.ArraySplitByLength<byte>(data, 128 /*0x80*/);
    for (int index = 0; index < numArrayList.Count; ++index)
    {
      byte[] buffer = provider.Decrypt(numArrayList[index], false);
      memoryStream.Write(buffer, 0, buffer.Length);
    }
    return memoryStream.ToArray();
  }

  /// <summary>
  /// 使用原始的公钥数据（PEM格式）来创建一个 RSA 算法加密解密的对象，可以直接用于加密或是验证签名操作<br />
  /// Use the original public key data (PEM format) to create an RSA algorithm encryption and decryption object,
  /// which can be directly used for encryption or signature verification
  /// </summary>
  /// <param name="publicKey">公钥</param>
  /// <returns>RSA 算法的加密解密对象</returns>
  public static RSACryptoServiceProvider CreateRsaProviderFromPublicKey(byte[] publicKey)
  {
    byte[] numArray1 = new byte[15];
    byte[] buffer = publicKey;
    int length = buffer.Length;
    using (MemoryStream input = new MemoryStream(buffer))
    {
      using (BinaryReader binaryReader = new BinaryReader((Stream) input))
      {
        switch (binaryReader.ReadUInt16())
        {
          case 33072:
            int num1 = (int) binaryReader.ReadByte();
            break;
          case 33328:
            int num2 = (int) binaryReader.ReadInt16();
            break;
          default:
            return (RSACryptoServiceProvider) null;
        }
        if (!RSAHelper.CompareBytearrays(binaryReader.ReadBytes(15), RSAHelper.SeqOID))
          return (RSACryptoServiceProvider) null;
        switch (binaryReader.ReadUInt16())
        {
          case 33027:
            int num3 = (int) binaryReader.ReadByte();
            break;
          case 33283:
            int num4 = (int) binaryReader.ReadInt16();
            break;
          default:
            return (RSACryptoServiceProvider) null;
        }
        if (binaryReader.ReadByte() > (byte) 0)
          return (RSACryptoServiceProvider) null;
        switch (binaryReader.ReadUInt16())
        {
          case 33072:
            int num5 = (int) binaryReader.ReadByte();
            break;
          case 33328:
            int num6 = (int) binaryReader.ReadInt16();
            break;
          default:
            return (RSACryptoServiceProvider) null;
        }
        ushort num7 = binaryReader.ReadUInt16();
        byte num8 = 0;
        byte num9;
        switch (num7)
        {
          case 33026:
            num9 = binaryReader.ReadByte();
            break;
          case 33282:
            num8 = binaryReader.ReadByte();
            num9 = binaryReader.ReadByte();
            break;
          default:
            return (RSACryptoServiceProvider) null;
        }
        int int32 = BitConverter.ToInt32(new byte[4]
        {
          num9,
          num8,
          (byte) 0,
          (byte) 0
        }, 0);
        if (binaryReader.PeekChar() == 0)
        {
          int num10 = (int) binaryReader.ReadByte();
          --int32;
        }
        byte[] numArray2 = binaryReader.ReadBytes(int32);
        if (binaryReader.ReadByte() != (byte) 2)
          return (RSACryptoServiceProvider) null;
        int count = (int) binaryReader.ReadByte();
        byte[] numArray3 = binaryReader.ReadBytes(count);
        RSACryptoServiceProvider providerFromPublicKey = new RSACryptoServiceProvider();
        providerFromPublicKey.ImportParameters(new RSAParameters()
        {
          Modulus = numArray2,
          Exponent = numArray3
        });
        return providerFromPublicKey;
      }
    }
  }

  private static bool CompareBytearrays(byte[] a, byte[] b)
  {
    if (a.Length != b.Length)
      return false;
    int index = 0;
    foreach (int num in a)
    {
      if (num != (int) b[index])
        return false;
      ++index;
    }
    return true;
  }
}
