﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.GE.GeSRTPServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Net;

#nullable disable
namespace HslCommunication.Profinet.GE;

/// <summary>
/// Ge的SRTP协议实现的虚拟PLC，支持I,Q,M,T,SA,SB,SC,S,G的位和字节读写，支持AI,AQ,R的字读写操作，支持读取当前时间及程序名称。<br />
/// Virtual PLC implemented by Ge's SRTP protocol, supports bit and byte read and write of I, Q, M, T, SA, SB, SC, S, G,
/// supports word read and write operations of AI, AQ, R, and supports reading Current time and program name.
/// </summary>
/// <remarks>
/// 实例化之后，直接调用 <see cref="M:HslCommunication.Core.Net.NetworkServerBase.ServerStart(System.Int32)" /> 方法就可以通信及交互，所有的地址都是从1开始的，地址示例：M1,M100, R1，
/// 具体的用法参考 HslCommunicationDemo 相关界面的源代码。
/// </remarks>
/// <example>
/// 地址的示例，参考 <see cref="T:HslCommunication.Profinet.GE.GeSRTPNet" /> 相关的示例说明
/// </example>
public class GeSRTPServer : DeviceServer
{
  private SoftBuffer iBuffer;
  private SoftBuffer qBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer tBuffer;
  private SoftBuffer saBuffer;
  private SoftBuffer sbBuffer;
  private SoftBuffer scBuffer;
  private SoftBuffer sBuffer;
  private SoftBuffer gBuffer;
  private SoftBuffer aiBuffer;
  private SoftBuffer aqBuffer;
  private SoftBuffer rBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public GeSRTPServer()
  {
    this.iBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.qBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.tBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.saBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.sbBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.scBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.sBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.gBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.aiBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.aqBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.GE.GeSRTPNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<GeSRTPAddress> from = GeSRTPAddress.ParseFrom(address, length, false);
    return !from.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) from) : OperateResult.CreateSuccessResult<byte[]>(this.ReadByCommand(from.Content.DataCode, (ushort) from.Content.AddressStart, from.Content.Length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.GE.GeSRTPNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<GeSRTPAddress> from = GeSRTPAddress.ParseFrom(address, (ushort) value.Length, false);
    return !from.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from) : this.WriteByCommand(from.Content.DataCode, (ushort) from.Content.AddressStart, from.Content.Length, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.GE.GeSRTPNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<GeSRTPAddress> from = GeSRTPAddress.ParseFrom(address, length, true);
    return !from.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) from) : OperateResult.CreateSuccessResult<bool[]>(this.GetSoftBufferFromDataCode(from.Content.DataCode, out bool _).GetBool(from.Content.AddressStart, (int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.GE.GeSRTPNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<GeSRTPAddress> from = GeSRTPAddress.ParseFrom(address, (ushort) value.Length, true);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    this.GetSoftBufferFromDataCode(from.Content.DataCode, out bool _).SetBool(value, from.Content.AddressStart);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new GeSRTPMessage();

  /// <inheritdoc />
  protected override OperateResult ThreadPoolLoginAfterClientCheck(
    PipeSession session,
    IPEndPoint endPoint)
  {
    CommunicationPipe communication = session.Communication;
    OperateResult<byte[]> message = communication.ReceiveMessage((INetMessage) new GeSRTPMessage(), (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
    if (!message.IsSuccess)
      return (OperateResult) message;
    byte[] numArray = new byte[56];
    numArray[0] = (byte) 1;
    numArray[8] = (byte) 15;
    this.LogSendMessage(numArray, session);
    OperateResult operateResult = communication.Send(numArray);
    return !operateResult.IsSuccess ? operateResult : base.ThreadPoolLoginAfterClientCheck(session, endPoint);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return OperateResult.CreateSuccessResult<byte[]>(receive[42] != (byte) 4 ? (receive[42] != (byte) 1 ? (receive[42] != (byte) 37 ? (receive[50] != (byte) 7 ? (byte[]) null : this.WriteByCommand(receive)) : this.ReadDateTimeByCommand(receive)) : this.ReadProgramNameByCommand(receive)) : this.ReadByCommand(receive));
  }

  private SoftBuffer GetSoftBufferFromDataCode(byte code, out bool isBit)
  {
    switch (code)
    {
      case 8:
        isBit = false;
        return this.rBuffer;
      case 10:
        isBit = false;
        return this.aiBuffer;
      case 12:
        isBit = false;
        return this.aqBuffer;
      case 16 /*0x10*/:
        isBit = false;
        return this.iBuffer;
      case 20:
        isBit = false;
        return this.tBuffer;
      case 22:
        isBit = false;
        return this.mBuffer;
      case 24:
        isBit = false;
        return this.saBuffer;
      case 26:
        isBit = false;
        return this.sbBuffer;
      case 28:
        isBit = false;
        return this.scBuffer;
      case 30:
        isBit = false;
        return this.tBuffer;
      case 56:
        isBit = false;
        return this.gBuffer;
      case 66:
        isBit = false;
        return this.qBuffer;
      case 70:
        isBit = true;
        return this.iBuffer;
      case 72:
        isBit = true;
        return this.qBuffer;
      case 74:
        isBit = true;
        return this.tBuffer;
      case 76:
        isBit = true;
        return this.mBuffer;
      case 78:
        isBit = true;
        return this.saBuffer;
      case 80 /*0x50*/:
        isBit = true;
        return this.sbBuffer;
      case 82:
        isBit = true;
        return this.scBuffer;
      case 84:
        isBit = true;
        return this.tBuffer;
      case 86:
        isBit = true;
        return this.gBuffer;
      default:
        isBit = false;
        return (SoftBuffer) null;
    }
  }

  private byte[] ReadByCommand(byte dataCode, ushort address, ushort length)
  {
    bool isBit;
    SoftBuffer bufferFromDataCode = this.GetSoftBufferFromDataCode(dataCode, out isBit);
    if (bufferFromDataCode == null)
      return (byte[]) null;
    if (isBit)
    {
      int newStart;
      ushort byteLength;
      HslHelper.CalculateStartBitIndexAndLength((int) address, length, out newStart, out byteLength, out int _);
      return bufferFromDataCode.GetBytes(newStart / 8, (int) byteLength);
    }
    return dataCode == (byte) 10 || dataCode == (byte) 12 || dataCode == (byte) 8 ? bufferFromDataCode.GetBytes((int) address * 2, (int) length * 2) : bufferFromDataCode.GetBytes((int) address, (int) length);
  }

  private byte[] ReadByCommand(byte[] command)
  {
    byte[] numArray1 = this.ReadByCommand(command[43], BitConverter.ToUInt16(command, 44), BitConverter.ToUInt16(command, 46));
    if (numArray1 == null)
      return (byte[]) null;
    if (numArray1.Length < 7)
    {
      byte[] hexBytes = "\r\n03 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 01 00 00 00 00 00 00 00 00 00 00 00 00 06 d4\r\n00 0e 00 00 00 60 01 a0 01 01 00 00 00 00 00 00\r\n00 00 ff 02 03 00 5c 01".ToHexBytes();
      numArray1.CopyTo((Array) hexBytes, 44);
      command.SelectMiddle<byte>(2, 2).CopyTo((Array) hexBytes, 2);
      return hexBytes;
    }
    byte[] numArray2 = new byte[56 + numArray1.Length];
    "03 00 03 00 00 00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 00 06 94\r\n\t\t\t\t00 0e 00 00 00 60 01 a0 00 00 0c 00 00 18 00 00 01 01 ff 02 03 00 5c 01".ToHexBytes().CopyTo((Array) numArray2, 0);
    command.SelectMiddle<byte>(2, 2).CopyTo((Array) numArray2, 2);
    numArray1.CopyTo((Array) numArray2, 56);
    BitConverter.GetBytes((ushort) numArray1.Length).CopyTo((Array) numArray2, 4);
    return numArray2;
  }

  private OperateResult WriteByCommand(byte dataCode, ushort address, ushort length, byte[] value)
  {
    bool isBit;
    SoftBuffer bufferFromDataCode = this.GetSoftBufferFromDataCode(dataCode, out isBit);
    if (bufferFromDataCode == null)
      return new OperateResult(StringResources.Language.NotSupportedDataType);
    if (isBit)
    {
      HslHelper.CalculateStartBitIndexAndLength((int) address, length, out int _, out ushort _, out int _);
      bufferFromDataCode.SetBool(value.ToBoolArray().SelectMiddle<bool>((int) address % 8, (int) length), (int) address);
    }
    else if (dataCode == (byte) 10 || dataCode == (byte) 12 || dataCode == (byte) 8)
    {
      if (value.Length % 2 == 1)
        return new OperateResult(StringResources.Language.GeSRTPWriteLengthMustBeEven);
      bufferFromDataCode.SetBytes(value, (int) address * 2);
    }
    else
      bufferFromDataCode.SetBytes(value, (int) address);
    return OperateResult.CreateSuccessResult();
  }

  private byte[] WriteByCommand(byte[] command)
  {
    if (!this.EnableWrite || !this.WriteByCommand(command[51], BitConverter.ToUInt16(command, 52), BitConverter.ToUInt16(command, 54), command.RemoveBegin<byte>(56)).IsSuccess)
      return (byte[]) null;
    byte[] hexBytes = "03 00 01 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 02 00 00 00 00 00 00 00 00 00 00 00 00 09 d4\r\n00 0e 00 00 00 60 01 a0 01 01 00 00 00 00 00 00\r\n00 00 ff 02 03 00 5c 01".ToHexBytes();
    command.SelectMiddle<byte>(2, 2).CopyTo((Array) hexBytes, 2);
    return hexBytes;
  }

  private byte[] ReadDateTimeByCommand(byte[] command)
  {
    byte[] hexBytes = "03 00 03 00 07 00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 00 06 94\r\n\t\t\t\t00 0e 00 00 00 60 01 a0 00 00 0c 00 00 18 00 00 01 01 ff 02 03 00 5c 01 00 00 00 00 00 00 03".ToHexBytes();
    DateTime now = DateTime.Now;
    now.Second.ToString("D2").ToHexBytes().CopyTo((Array) hexBytes, 56);
    now.Minute.ToString("D2").ToHexBytes().CopyTo((Array) hexBytes, 57);
    now.Hour.ToString("D2").ToHexBytes().CopyTo((Array) hexBytes, 58);
    now.Day.ToString("D2").ToHexBytes().CopyTo((Array) hexBytes, 59);
    now.Month.ToString("D2").ToHexBytes().CopyTo((Array) hexBytes, 60);
    (now.Year - 2000).ToString("D2").ToHexBytes().CopyTo((Array) hexBytes, 61);
    command.SelectMiddle<byte>(2, 2).CopyTo((Array) hexBytes, 2);
    return hexBytes;
  }

  private byte[] ReadProgramNameByCommand(byte[] command)
  {
    byte[] hexBytes = "\r\n03 00 07 00 2a 00 00 00 00 00 00 00 00 00 00 00 \r\n00 01 00 00 00 00 00 00 00 00 00 00 00 00 06 94 \r\n00 0e 00 00 00 62 01 a0 00 00 2a 00 00 18 00 00 \r\n01 01 ff 02 03 00 5c 01 00 00 00 00 00 00 00 00 \r\n01 00 00 00 00 00 00 00 00 00 50 41 43 34 30 30 \r\n00 00 00 00 00 00 00 00 00 00 03 00 01 50 05 18 \r\n01 21".ToHexBytes();
    command.SelectMiddle<byte>(2, 2).CopyTo((Array) hexBytes, 2);
    return hexBytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 983040 /*0x0F0000*/)
      throw new Exception("File is not correct");
    this.iBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.qBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.tBuffer.SetBytes(content, 196608 /*0x030000*/, 0, 65536 /*0x010000*/);
    this.saBuffer.SetBytes(content, 262144 /*0x040000*/, 0, 65536 /*0x010000*/);
    this.sbBuffer.SetBytes(content, 327680 /*0x050000*/, 0, 65536 /*0x010000*/);
    this.scBuffer.SetBytes(content, 393216 /*0x060000*/, 0, 65536 /*0x010000*/);
    this.sBuffer.SetBytes(content, 458752 /*0x070000*/, 0, 65536 /*0x010000*/);
    this.gBuffer.SetBytes(content, 524288 /*0x080000*/, 0, 65536 /*0x010000*/);
    this.aiBuffer.SetBytes(content, 589824 /*0x090000*/, 0, 131072 /*0x020000*/);
    this.aqBuffer.SetBytes(content, 720896 /*0x0B0000*/, 0, 131072 /*0x020000*/);
    this.rBuffer.SetBytes(content, 851968 /*0x0D0000*/, 0, 131072 /*0x020000*/);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[983040 /*0x0F0000*/];
    Array.Copy((Array) this.iBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.qBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.mBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.tBuffer.GetBytes(), 0, (Array) destinationArray, 196608 /*0x030000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.saBuffer.GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.sbBuffer.GetBytes(), 0, (Array) destinationArray, 327680 /*0x050000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.scBuffer.GetBytes(), 0, (Array) destinationArray, 393216 /*0x060000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.sBuffer.GetBytes(), 0, (Array) destinationArray, 458752 /*0x070000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.gBuffer.GetBytes(), 0, (Array) destinationArray, 524288 /*0x080000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.aiBuffer.GetBytes(), 0, (Array) destinationArray, 589824 /*0x090000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.aqBuffer.GetBytes(), 0, (Array) destinationArray, 720896 /*0x0B0000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.rBuffer.GetBytes(), 0, (Array) destinationArray, 851968 /*0x0D0000*/, 131072 /*0x020000*/);
    return destinationArray;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.iBuffer.Dispose();
      this.qBuffer.Dispose();
      this.mBuffer.Dispose();
      this.tBuffer.Dispose();
      this.saBuffer.Dispose();
      this.sbBuffer.Dispose();
      this.scBuffer.Dispose();
      this.sBuffer.Dispose();
      this.gBuffer.Dispose();
      this.aiBuffer.Dispose();
      this.aqBuffer.Dispose();
      this.rBuffer.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"GeSRTPServer[{this.Port}]";
}
