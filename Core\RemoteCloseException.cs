﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.RemoteCloseException
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// 远程对象关闭的异常信息<br />
/// Exception information of remote object close
/// </summary>
public class RemoteCloseException : Exception
{
  /// <summary>实例化一个默认的对象</summary>
  public RemoteCloseException()
    : base("Remote Closed Exception")
  {
  }
}
