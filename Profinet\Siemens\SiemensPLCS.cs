﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensPLCS
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>西门子的PLC类型，目前支持的访问类型</summary>
public enum SiemensPLCS
{
  /// <summary>1200系列</summary>
  S1200 = 1,
  /// <summary>300系列</summary>
  S300 = 2,
  /// <summary>400系列</summary>
  S400 = 3,
  /// <summary>1500系列PLC</summary>
  S1500 = 4,
  /// <summary>200的smart系列</summary>
  S200Smart = 5,
  /// <summary>200系统，需要额外配置以太网模块</summary>
  S200 = 6,
}
