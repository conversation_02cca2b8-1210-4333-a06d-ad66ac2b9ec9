﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Freedom.FreedomUdpNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Pipe;

#nullable disable
namespace HslCommunication.Profinet.Freedom;

/// <summary>
/// 基于UDP/IP协议的自由协议，需要在地址里传入报文信息，也可以传入数据偏移信息，<see cref="P:HslCommunication.Core.Device.DeviceCommunication.ByteTransform" />默认为<see cref="T:HslCommunication.Core.RegularByteTransform" />
/// </summary>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\FreedomExample.cs" region="Sample3" title="实例化" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\FreedomExample.cs" region="Sample4" title="读取" />
/// </example>
public class FreedomUdpNet : FreedomTcpNet
{
  /// <summary>实例化一个默认的对象</summary>
  public FreedomUdpNet()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.CommunicationPipe = (CommunicationPipe) new PipeUdpNet();
  }

  /// <summary>指定IP地址及端口号来实例化自由的TCP协议</summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口</param>
  public FreedomUdpNet(string ipAddress, int port)
  {
    this.CommunicationPipe = (CommunicationPipe) new PipeUdpNet();
    this.IpAddress = ipAddress;
    this.Port = port;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"FreedomUdpNet<{this.ByteTransform.GetType()}>[{this.IpAddress}:{this.Port}]";
  }
}
