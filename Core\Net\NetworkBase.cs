﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.NetworkBase
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Security;
using HslCommunication.Enthernet.Redis;
using HslCommunication.LogNet;
using HslCommunication.MQTT;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>
/// 本系统所有网络类的基类，该类为抽象类，无法进行实例化，如果想使用里面的方法来实现自定义的网络通信，请通过继承使用。<br />
/// The base class of all network classes in this system. This class is an abstract class and cannot be instantiated.
/// If you want to use the methods inside to implement custom network communication, please use it through inheritance.
/// </summary>
/// <remarks>
/// 本类提供了丰富的底层数据的收发支持，包含<see cref="T:HslCommunication.Core.IMessage.INetMessage" />消息的接收，<c>MQTT</c>以及<c>Redis</c>,<c>websocket</c>协议的实现
/// </remarks>
public abstract class NetworkBase
{
  /// <summary>
  /// 文件传输的时候的缓存大小，直接影响传输的速度，值越大，传输速度越快，越占内存，默认为100K大小<br />
  /// The size of the cache during file transfer directly affects the speed of the transfer. The larger the value, the faster the transfer speed and the more memory it takes. The default size is 100K.
  /// </summary>
  protected int fileCacheSize = 102400 /*0x019000*/;
  private int connectErrorCount = 0;

  /// <summary>
  /// 实例化一个NetworkBase对象，令牌的默认值为空，都是0x00<br />
  /// Instantiate a NetworkBase object, the default value of the token is empty, both are 0x00
  /// </summary>
  public NetworkBase()
  {
    this.Token = Guid.Empty;
    HslCommunication.Authorization.oasjodaiwfsodopsdjpasjpf();
  }

  /// <summary>
  /// 组件的日志工具，支持日志记录，只要实例化后，当前网络的基本信息，就以<see cref="F:HslCommunication.LogNet.HslMessageDegree.DEBUG" />等级进行输出<br />
  /// The component's logging tool supports logging. As long as the instantiation of the basic network information, the output will be output at <see cref="F:HslCommunication.LogNet.HslMessageDegree.DEBUG" />
  /// </summary>
  /// <remarks>
  /// 只要实例化即可以记录日志，实例化的对象需要实现接口 <see cref="T:HslCommunication.LogNet.ILogNet" /> ，本组件提供了三个日志记录类，你可以实现基于 <see cref="T:HslCommunication.LogNet.ILogNet" />  的对象。</remarks>
  /// <example>
  /// 如下的实例化适用于所有的Network及其派生类，以下举两个例子，三菱的设备类及服务器类
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="LogNetExample1" title="LogNet示例" />
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="LogNetExample2" title="LogNet示例" />
  /// </example>
  public ILogNet LogNet { get; set; }

  /// <summary>
  /// 网络类的身份令牌，在hsl协议的模式下会有效，在和设备进行通信的时候是无效的<br />
  /// Network-type identity tokens will be valid in the hsl protocol mode and will not be valid when communicating with the device
  /// </summary>
  /// <remarks>适用于Hsl协议相关的网络通信类，不适用于设备交互类。</remarks>
  /// <example>
  /// 此处以 <see cref="T:HslCommunication.Enthernet.NetSimplifyServer" /> 服务器类及 <see cref="T:HslCommunication.Enthernet.NetSimplifyClient" /> 客户端类的令牌设置举例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="TokenClientExample" title="Client示例" />
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="TokenServerExample" title="Server示例" />
  /// </example>
  public Guid Token { get; set; }

  /// <summary>
  /// 接收固定长度的字节数组，允许指定超时时间，默认为60秒，当length大于0时，接收固定长度的数据内容，当length小于0时，buffer长度的缓存数据<br />
  /// Receiving a fixed-length byte array, allowing a specified timeout time. The default is 60 seconds. When length is greater than 0,
  /// fixed-length data content is received. When length is less than 0, random data information of a length not greater than 2048 is received.
  /// </summary>
  /// <param name="socket">网络通讯的套接字<br />Network communication socket</param>
  /// <param name="buffer">等待接收的数据缓存信息</param>
  /// <param name="offset">开始接收数据的偏移地址</param>
  /// <param name="length">准备接收的数据长度，当length大于0时，接收固定长度的数据内容，当length小于0时，接收不大于1024长度的随机数据信息</param>
  /// <param name="timeOut">单位：毫秒，超时时间，默认为60秒，如果设置小于0，则不检查超时时间</param>
  /// <param name="reportProgress">当前接收数据的进度报告，有些协议支持传输非常大的数据内容，可以给与进度提示的功能</param>
  /// <returns>包含了字节数据的结果类</returns>
  protected OperateResult<int> Receive(
    Socket socket,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(0);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    try
    {
      socket.ReceiveTimeout = timeOut;
      if (length > 0)
      {
        NetSupport.ReceiveBytesFromSocket(socket, buffer, offset, length, reportProgress);
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int num = socket.Receive(buffer, offset, buffer.Length - offset, SocketFlags.None);
      return num != 0 ? OperateResult.CreateSuccessResult<int>(num) : throw new RemoteCloseException();
    }
    catch (RemoteCloseException ex)
    {
      socket?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return new OperateResult<int>(-this.connectErrorCount, "Socket Exception -> " + StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      socket?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return new OperateResult<int>(-this.connectErrorCount, "Socket Exception -> " + ex.Message);
    }
  }

  /// <summary>
  /// 接收固定长度的字节数组，允许指定超时时间，默认为60秒，当length大于0时，接收固定长度的数据内容，当length小于0时，接收不大于2048长度的随机数据信息<br />
  /// Receiving a fixed-length byte array, allowing a specified timeout time. The default is 60 seconds. When length is greater than 0,
  /// fixed-length data content is received. When length is less than 0, random data information of a length not greater than 2048 is received.
  /// </summary>
  /// <param name="socket">网络通讯的套接字<br />Network communication socket</param>
  /// <param name="length">准备接收的数据长度，当length大于0时，接收固定长度的数据内容，当length小于0时，接收不大于1024长度的随机数据信息</param>
  /// <param name="timeOut">单位：毫秒，超时时间，默认为60秒，如果设置小于0，则不检查超时时间</param>
  /// <param name="reportProgress">当前接收数据的进度报告，有些协议支持传输非常大的数据内容，可以给与进度提示的功能</param>
  /// <returns>包含了字节数据的结果类</returns>
  protected OperateResult<byte[]> Receive(
    Socket socket,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    int length1 = length > 0 ? length : 2048 /*0x0800*/;
    byte[] buffer;
    try
    {
      buffer = new byte[length1];
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<byte[]>($"Create byte[{length1}] buffer failed: " + ex.Message);
    }
    OperateResult<int> result = this.Receive(socket, buffer, 0, length, timeOut, reportProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(length > 0 ? buffer : buffer.SelectBegin<byte>(result.Content));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Receive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected OperateResult<int> Receive(
    SslStream ssl,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(0);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    try
    {
      ssl.ReadTimeout = timeOut;
      if (length > 0)
      {
        int num1 = 0;
        while (num1 < length)
        {
          int count = Math.Min(length - num1, 16384 /*0x4000*/);
          int num2 = ssl.Read(buffer, num1 + offset, count);
          num1 += num2;
          if (num2 == 0)
            throw new RemoteCloseException();
          if (reportProgress != null)
            reportProgress((long) num1, (long) length);
        }
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int num = ssl.Read(buffer, offset, buffer.Length - offset);
      return num != 0 ? OperateResult.CreateSuccessResult<int>(num) : throw new RemoteCloseException();
    }
    catch (RemoteCloseException ex)
    {
      ssl?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return new OperateResult<int>(-1, "Socket Exception -> " + StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      ssl?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return new OperateResult<int>(-1, "Socket Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Receive(System.Net.Sockets.Socket,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected OperateResult<byte[]> Receive(
    SslStream ssl,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    int length1 = length > 0 ? length : 2048 /*0x0800*/;
    byte[] buffer;
    try
    {
      buffer = new byte[length1];
    }
    catch (Exception ex)
    {
      ssl?.Close();
      return new OperateResult<byte[]>($"Create byte[{length1}] buffer failed: " + ex.Message);
    }
    OperateResult<int> result = this.Receive(ssl, buffer, 0, length, timeOut, reportProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(length > 0 ? buffer : buffer.SelectBegin<byte>(result.Content));
  }

  /// <summary>
  /// 接收一行命令数据，需要自己指定这个结束符，默认超时时间为60秒，也即是60000，单位是毫秒<br />
  /// To receive a line of command data, you need to specify the terminator yourself. The default timeout is 60 seconds, which is 60,000, in milliseconds.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="endCode">结束符信息</param>
  /// <param name="timeout">超时时间，默认为60000，单位为毫秒，也就是60秒</param>
  /// <returns>带有结果对象的数据信息</returns>
  protected OperateResult<byte[]> ReceiveCommandLineFromSocket(
    Socket socket,
    byte endCode,
    int timeout = 60000)
  {
    List<byte> byteList = new List<byte>(128 /*0x80*/);
    try
    {
      DateTime now = DateTime.Now;
      bool flag = false;
      while ((DateTime.Now - now).TotalMilliseconds < (double) timeout)
      {
        if (socket.Poll(timeout, SelectMode.SelectRead))
        {
          OperateResult<byte[]> commandLineFromSocket = this.Receive(socket, 1, timeout);
          if (!commandLineFromSocket.IsSuccess)
            return commandLineFromSocket;
          byteList.AddRange((IEnumerable<byte>) commandLineFromSocket.Content);
          if ((int) commandLineFromSocket.Content[0] == (int) endCode)
          {
            flag = true;
            break;
          }
        }
      }
      return !flag ? new OperateResult<byte[]>(StringResources.Language.ReceiveDataTimeout) : OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>
  /// 接收一行命令数据，需要自己指定这个结束符，默认超时时间为60秒，也即是60000，单位是毫秒<br />
  /// To receive a line of command data, you need to specify the terminator yourself. The default timeout is 60 seconds, which is 60,000, in milliseconds.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="endCode1">结束符1信息</param>
  /// <param name="endCode2">结束符2信息</param>
  /// 
  ///             /// <param name="timeout">超时时间，默认无穷大，单位毫秒</param>
  /// <returns>带有结果对象的数据信息</returns>
  protected OperateResult<byte[]> ReceiveCommandLineFromSocket(
    Socket socket,
    byte endCode1,
    byte endCode2,
    int timeout = 60000)
  {
    List<byte> byteList = new List<byte>(128 /*0x80*/);
    try
    {
      DateTime now = DateTime.Now;
      bool flag = false;
      while ((DateTime.Now - now).TotalMilliseconds < (double) timeout)
      {
        if (socket.Poll(timeout, SelectMode.SelectRead))
        {
          OperateResult<byte[]> commandLineFromSocket = this.Receive(socket, 1, timeout);
          if (!commandLineFromSocket.IsSuccess)
            return commandLineFromSocket;
          byteList.AddRange((IEnumerable<byte>) commandLineFromSocket.Content);
          if ((int) commandLineFromSocket.Content[0] == (int) endCode2 && byteList.Count > 1 && (int) byteList[byteList.Count - 2] == (int) endCode1)
          {
            flag = true;
            break;
          }
        }
      }
      return !flag ? new OperateResult<byte[]>(StringResources.Language.ReceiveDataTimeout) : OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>
  /// 接收一条完整的 <seealso cref="T:HslCommunication.Core.IMessage.INetMessage" /> 数据内容，需要指定超时时间，单位为毫秒。 <br />
  /// Receive a complete <seealso cref="T:HslCommunication.Core.IMessage.INetMessage" /> data content, Need to specify a timeout period in milliseconds
  /// </summary>
  /// <param name="socket">网络的套接字</param>
  /// <param name="timeOut">超时时间，单位：毫秒</param>
  /// <param name="netMessage">消息的格式定义</param>
  /// <param name="reportProgress">接收消息的时候的进度报告</param>
  /// <returns>带有是否成功的byte数组对象</returns>
  protected virtual OperateResult<byte[]> ReceiveByMessage(
    Socket socket,
    int timeOut,
    INetMessage netMessage,
    Action<long, long> reportProgress = null)
  {
    if (netMessage == null)
      return this.Receive(socket, -1, timeOut);
    if (netMessage.ProtocolHeadBytesLength < 0)
    {
      byte[] bytes = BitConverter.GetBytes(netMessage.ProtocolHeadBytesLength);
      int num = (int) bytes[3] & 15;
      OperateResult<byte[]> byMessage1 = (OperateResult<byte[]>) null;
      switch (num)
      {
        case 1:
          byMessage1 = this.ReceiveCommandLineFromSocket(socket, bytes[1], timeOut);
          break;
        case 2:
          byMessage1 = this.ReceiveCommandLineFromSocket(socket, bytes[1], bytes[0], timeOut);
          break;
      }
      if (byMessage1 == null)
        return new OperateResult<byte[]>("Receive by specified code failed, length check failed");
      if (!byMessage1.IsSuccess)
        return byMessage1;
      netMessage.HeadBytes = byMessage1.Content;
      if (!(netMessage is SpecifiedCharacterMessage characterMessage) || characterMessage.EndLength == (byte) 0)
        return byMessage1;
      OperateResult<byte[]> byMessage2 = this.Receive(socket, (int) characterMessage.EndLength, timeOut);
      if (!byMessage2.IsSuccess)
        return byMessage2;
      return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(byMessage1.Content, byMessage2.Content));
    }
    OperateResult<byte[]> byMessage3 = this.Receive(socket, netMessage.ProtocolHeadBytesLength, timeOut);
    if (!byMessage3.IsSuccess)
      return byMessage3;
    int length = netMessage.PependedUselesByteLength(byMessage3.Content);
    int num1 = 0;
    while (length >= netMessage.ProtocolHeadBytesLength)
    {
      byMessage3 = this.Receive(socket, netMessage.ProtocolHeadBytesLength, timeOut);
      if (!byMessage3.IsSuccess)
        return byMessage3;
      length = netMessage.PependedUselesByteLength(byMessage3.Content);
      ++num1;
      if (num1 > 10)
        break;
    }
    if (length > 0)
    {
      OperateResult<byte[]> byMessage4 = this.Receive(socket, length, timeOut);
      if (!byMessage4.IsSuccess)
        return byMessage4;
      byMessage3.Content = SoftBasic.SpliceArray<byte>(byMessage3.Content.RemoveBegin<byte>(length), byMessage4.Content);
    }
    netMessage.HeadBytes = byMessage3.Content;
    int lengthByHeadBytes = netMessage.GetContentLengthByHeadBytes();
    if (lengthByHeadBytes <= 0)
      return OperateResult.CreateSuccessResult<byte[]>(byMessage3.Content);
    byte[] buffer = new byte[netMessage.ProtocolHeadBytesLength + lengthByHeadBytes];
    byMessage3.Content.CopyTo((Array) buffer, 0);
    OperateResult result = (OperateResult) this.Receive(socket, buffer, netMessage.ProtocolHeadBytesLength, lengthByHeadBytes, timeOut, reportProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  /// <summary>
  /// 发送消息给套接字，直到完成的时候返回，经过测试，本方法是线程安全的。<br />
  /// Send a message to the socket until it returns when completed. After testing, this method is thread-safe.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="data">字节数据</param>
  /// <returns>发送是否成功的结果</returns>
  protected OperateResult Send(Socket socket, byte[] data)
  {
    return data == null ? OperateResult.CreateSuccessResult() : this.Send(socket, data, 0, data.Length);
  }

  /// <summary>
  /// 发送消息给套接字，直到完成的时候返回，经过测试，本方法是线程安全的。<br />
  /// Send a message to the socket until it returns when completed. After testing, this method is thread-safe.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="data">字节数据</param>
  /// <param name="offset">偏移的位置信息</param>
  /// <param name="size">发送的数据总数</param>
  /// <returns>发送是否成功的结果</returns>
  protected OperateResult Send(Socket socket, byte[] data, int offset, int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    try
    {
      int num1 = 0;
      do
      {
        int num2 = socket.Send(data, offset, size - num1, SocketFlags.None);
        num1 += num2;
        offset += num2;
      }
      while (num1 < size);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      socket?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return (OperateResult) new OperateResult<byte[]>(-this.connectErrorCount, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Send(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  protected OperateResult Send(SslStream ssl, byte[] data, int offset, int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    try
    {
      ssl.Write(data, offset, size);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      ssl?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return (OperateResult) new OperateResult<byte[]>(-this.connectErrorCount, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Send(System.Net.Sockets.Socket,System.Byte[])" />
  protected OperateResult Send(SslStream ssl, byte[] data)
  {
    return data == null ? OperateResult.CreateSuccessResult() : this.Send(ssl, data, 0, data.Length);
  }

  /// <summary>
  /// 创建一个新的socket对象并连接到远程的地址，默认超时时间为10秒钟，需要指定ip地址以及端口号信息<br />
  /// Create a new socket object and connect to the remote address. The default timeout is 10 seconds. You need to specify the IP address and port number.
  /// </summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <returns>返回套接字的封装结果对象</returns>
  /// <example>
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="CreateSocketAndConnectExample" title="创建连接示例" />
  /// </example>
  protected OperateResult<Socket> CreateSocketAndConnect(string ipAddress, int port)
  {
    return this.CreateSocketAndConnect(new IPEndPoint(IPAddress.Parse(ipAddress), port), 10000);
  }

  /// <summary>
  /// 创建一个新的socket对象并连接到远程的地址，需要指定ip地址以及端口号信息，还有超时时间，单位是毫秒<br />
  /// To create a new socket object and connect to a remote address, you need to specify the IP address and port number information, and the timeout period in milliseconds
  /// </summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="timeOut">连接的超时时间</param>
  /// <returns>返回套接字的封装结果对象</returns>
  /// <example>
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="CreateSocketAndConnectExample" title="创建连接示例" />
  /// </example>
  protected OperateResult<Socket> CreateSocketAndConnect(string ipAddress, int port, int timeOut)
  {
    return this.CreateSocketAndConnect(new IPEndPoint(IPAddress.Parse(ipAddress), port), timeOut);
  }

  /// <summary>
  /// 创建一个新的socket对象并连接到远程的地址，需要指定远程终结点，超时时间（单位是毫秒），如果需要绑定本地的IP或是端口，传入 local对象<br />
  /// To create a new socket object and connect to the remote address, you need to specify the remote endpoint,
  /// the timeout period (in milliseconds), if you need to bind the local IP or port, pass in the local object
  /// </summary>
  /// <param name="endPoint">连接的目标终结点</param>
  /// <param name="timeOut">连接的超时时间</param>
  /// <param name="local">如果需要绑定本地的IP地址，就需要设置当前的对象</param>
  /// <returns>返回套接字的封装结果对象</returns>
  /// <example>
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkBase.cs" region="CreateSocketAndConnectExample" title="创建连接示例" />
  /// </example>
  protected OperateResult<Socket> CreateSocketAndConnect(
    IPEndPoint endPoint,
    int timeOut,
    IPEndPoint local = null)
  {
    OperateResult<Socket> socketAndConnect = NetSupport.CreateSocketAndConnect(endPoint, timeOut, local);
    if (socketAndConnect.IsSuccess)
    {
      this.connectErrorCount = 0;
      return socketAndConnect;
    }
    if (this.connectErrorCount < 1000000000)
      ++this.connectErrorCount;
    return new OperateResult<Socket>(-this.connectErrorCount, socketAndConnect.Message);
  }

  /// <summary>
  /// 检查当前的头子节信息的令牌是否是正确的，仅用于某些特殊的协议实现<br />
  /// Check whether the token of the current header subsection information is correct, only for some special protocol implementations
  /// </summary>
  /// <param name="headBytes">头子节数据</param>
  /// <returns>令牌是验证成功</returns>
  protected bool CheckRemoteToken(byte[] headBytes)
  {
    return SoftBasic.IsByteTokenEquel(headBytes, this.Token);
  }

  /// <summary>
  /// [自校验] 发送字节数据并确认对方接收完成数据，如果结果异常，则结束通讯<br />
  /// [Self-check] Send the byte data and confirm that the other party has received the completed data. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="headCode">头指令</param>
  /// <param name="customer">用户指令</param>
  /// <param name="send">发送的数据</param>
  /// <returns>是否发送成功</returns>
  protected OperateResult SendBaseAndCheckReceive(
    Socket socket,
    int headCode,
    int customer,
    byte[] send)
  {
    send = HslProtocol.CommandBytes(headCode, customer, this.Token, send);
    OperateResult operateResult1 = this.Send(socket, send);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<long> operateResult2 = this.ReceiveLong(socket);
    if (!operateResult2.IsSuccess || operateResult2.Content == (long) send.Length)
      return (OperateResult) operateResult2;
    socket?.Close();
    return new OperateResult(StringResources.Language.CommandLengthCheckFailed);
  }

  /// <summary>
  /// [自校验] 发送字节数据并确认对方接收完成数据，如果结果异常，则结束通讯<br />
  /// [Self-check] Send the byte data and confirm that the other party has received the completed data. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="customer">用户指令</param>
  /// <param name="send">发送的数据</param>
  /// <returns>是否发送成功</returns>
  protected OperateResult SendBytesAndCheckReceive(Socket socket, int customer, byte[] send)
  {
    return this.SendBaseAndCheckReceive(socket, 1002, customer, send);
  }

  /// <summary>
  /// [自校验] 直接发送字符串数据并确认对方接收完成数据，如果结果异常，则结束通讯<br />
  /// [Self-checking] Send string data directly and confirm that the other party has received the completed data. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="customer">用户指令</param>
  /// <param name="send">发送的数据</param>
  /// <returns>是否发送成功</returns>
  protected OperateResult SendStringAndCheckReceive(Socket socket, int customer, string send)
  {
    byte[] bytes = string.IsNullOrEmpty(send) ? (byte[]) null : Encoding.Unicode.GetBytes(send);
    return this.SendBaseAndCheckReceive(socket, 1001, customer, bytes);
  }

  /// <summary>
  /// [自校验] 直接发送字符串数组并确认对方接收完成数据，如果结果异常，则结束通讯<br />
  /// [Self-check] Send string array directly and confirm that the other party has received the completed data. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="customer">用户指令</param>
  /// <param name="sends">发送的字符串数组</param>
  /// <returns>是否发送成功</returns>
  protected OperateResult SendStringAndCheckReceive(Socket socket, int customer, string[] sends)
  {
    return this.SendBaseAndCheckReceive(socket, 1005, customer, HslProtocol.PackStringArrayToByte(sends));
  }

  /// <summary>
  /// [自校验] 直接发送字符串数组并确认对方接收完成数据，如果结果异常，则结束通讯<br />
  /// [Self-check] Send string array directly and confirm that the other party has received the completed data. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="customer">用户指令</param>
  /// <param name="name">用户名</param>
  /// <param name="pwd">密码</param>
  /// <returns>是否发送成功</returns>
  protected OperateResult SendAccountAndCheckReceive(
    Socket socket,
    int customer,
    string name,
    string pwd)
  {
    return this.SendBaseAndCheckReceive(socket, 5, customer, HslProtocol.PackStringArrayToByte(new string[2]
    {
      name,
      pwd
    }));
  }

  /// <summary>
  /// [自校验] 接收一条完整的同步数据，包含头子节和内容字节，基础的数据，如果结果异常，则结束通讯<br />
  /// [Self-checking] Receive a complete synchronization data, including header subsection and content bytes, basic data, if the result is abnormal, the communication ends
  /// </summary>
  /// <param name="socket">套接字</param>
  /// <param name="timeOut">超时时间设置，如果为负数，则不检查超时</param>
  /// <returns>包含是否成功的结果对象</returns>
  /// <exception cref="T:System.ArgumentNullException">result</exception>
  protected OperateResult<byte[], byte[]> ReceiveAndCheckBytes(Socket socket, int timeOut)
  {
    OperateResult<byte[]> operateResult1 = this.Receive(socket, 32 /*0x20*/, timeOut);
    if (!operateResult1.IsSuccess)
      return operateResult1.ConvertFailed<byte[], byte[]>();
    if (!this.CheckRemoteToken(operateResult1.Content))
    {
      socket?.Close();
      return new OperateResult<byte[], byte[]>(StringResources.Language.TokenCheckFailed);
    }
    int int32 = BitConverter.ToInt32(operateResult1.Content, 28);
    OperateResult<byte[]> operateResult2 = this.Receive(socket, int32, timeOut);
    if (!operateResult2.IsSuccess)
      return operateResult2.ConvertFailed<byte[], byte[]>();
    OperateResult operateResult3 = this.SendLong(socket, (long) (32 /*0x20*/ + int32));
    if (!operateResult3.IsSuccess)
      return operateResult3.ConvertFailed<byte[], byte[]>();
    byte[] content1 = operateResult1.Content;
    byte[] content2 = operateResult2.Content;
    byte[] numArray = HslProtocol.CommandAnalysis(content1, content2);
    return OperateResult.CreateSuccessResult<byte[], byte[]>(content1, numArray);
  }

  /// <summary>
  /// [自校验] 从网络中接收一个字符串数据，如果结果异常，则结束通讯<br />
  /// [Self-checking] Receive a string of data from the network. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">套接字</param>
  /// <param name="timeOut">接收数据的超时时间</param>
  /// <returns>包含是否成功的结果对象</returns>
  protected OperateResult<int, string> ReceiveStringContentFromSocket(Socket socket, int timeOut = 30000)
  {
    OperateResult<byte[], byte[]> andCheckBytes = this.ReceiveAndCheckBytes(socket, timeOut);
    if (!andCheckBytes.IsSuccess)
      return OperateResult.CreateFailedResult<int, string>((OperateResult) andCheckBytes);
    if (BitConverter.ToInt32(andCheckBytes.Content1, 0) != 1001)
    {
      socket?.Close();
      return new OperateResult<int, string>(StringResources.Language.CommandHeadCodeCheckFailed);
    }
    if (andCheckBytes.Content2 == null)
      andCheckBytes.Content2 = new byte[0];
    return OperateResult.CreateSuccessResult<int, string>(BitConverter.ToInt32(andCheckBytes.Content1, 4), Encoding.Unicode.GetString(andCheckBytes.Content2));
  }

  /// <summary>
  /// [自校验] 从网络中接收一个字符串数组，如果结果异常，则结束通讯<br />
  /// [Self-check] Receive an array of strings from the network. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">套接字</param>
  /// <param name="timeOut">接收数据的超时时间</param>
  /// <returns>包含是否成功的结果对象</returns>
  protected OperateResult<int, string[]> ReceiveStringArrayContentFromSocket(
    Socket socket,
    int timeOut = 30000)
  {
    OperateResult<byte[], byte[]> andCheckBytes = this.ReceiveAndCheckBytes(socket, timeOut);
    if (!andCheckBytes.IsSuccess)
      return OperateResult.CreateFailedResult<int, string[]>((OperateResult) andCheckBytes);
    if (BitConverter.ToInt32(andCheckBytes.Content1, 0) != 1005)
    {
      socket?.Close();
      return new OperateResult<int, string[]>(StringResources.Language.CommandHeadCodeCheckFailed);
    }
    if (andCheckBytes.Content2 == null)
      andCheckBytes.Content2 = new byte[4];
    return OperateResult.CreateSuccessResult<int, string[]>(BitConverter.ToInt32(andCheckBytes.Content1, 4), HslProtocol.UnPackStringArrayFromByte(andCheckBytes.Content2));
  }

  /// <summary>
  /// [自校验] 从网络中接收一串字节数据，如果结果异常，则结束通讯<br />
  /// [Self-checking] Receive a string of byte data from the network. If the result is abnormal, the communication ends.
  /// </summary>
  /// <param name="socket">套接字的网络</param>
  /// <param name="timeout">超时时间</param>
  /// <returns>包含是否成功的结果对象</returns>
  protected OperateResult<int, byte[]> ReceiveBytesContentFromSocket(Socket socket, int timeout = 30000)
  {
    OperateResult<byte[], byte[]> andCheckBytes = this.ReceiveAndCheckBytes(socket, timeout);
    if (!andCheckBytes.IsSuccess)
      return OperateResult.CreateFailedResult<int, byte[]>((OperateResult) andCheckBytes);
    if (BitConverter.ToInt32(andCheckBytes.Content1, 0) == 1002)
      return OperateResult.CreateSuccessResult<int, byte[]>(BitConverter.ToInt32(andCheckBytes.Content1, 4), andCheckBytes.Content2);
    socket?.Close();
    return new OperateResult<int, byte[]>(StringResources.Language.CommandHeadCodeCheckFailed);
  }

  /// <summary>
  /// 从网络中接收Long数据<br />
  /// Receive Long data from the network
  /// </summary>
  /// <param name="socket">套接字网络</param>
  /// <returns>long数据结果</returns>
  private OperateResult<long> ReceiveLong(Socket socket)
  {
    OperateResult<byte[]> result = this.Receive(socket, 8, -1);
    return result.IsSuccess ? OperateResult.CreateSuccessResult<long>(BitConverter.ToInt64(result.Content, 0)) : OperateResult.CreateFailedResult<long>((OperateResult) result);
  }

  /// <summary>
  /// 将long数据发送到套接字<br />
  /// Send long data to the socket
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="value">long数据</param>
  /// <returns>是否发送成功</returns>
  private OperateResult SendLong(Socket socket, long value)
  {
    return this.Send(socket, BitConverter.GetBytes(value));
  }

  /// <summary>
  /// 发送一个流的所有数据到指定的网络套接字，需要指定发送的数据长度，支持按照百分比的进度报告<br />
  /// Send all the data of a stream to the specified network socket. You need to specify the length of the data to be sent. It supports the progress report in percentage.
  /// </summary>
  /// <param name="socket">套接字</param>
  /// <param name="stream">内存流</param>
  /// <param name="receive">发送的数据长度</param>
  /// <param name="report">进度报告的委托</param>
  /// <param name="reportByPercent">进度报告是否按照百分比报告</param>
  /// <returns>是否成功的结果对象</returns>
  protected OperateResult SendStreamToSocket(
    Socket socket,
    Stream stream,
    long receive,
    Action<long, long> report,
    bool reportByPercent)
  {
    byte[] numArray1 = new byte[this.fileCacheSize];
    long num1 = 0;
    long num2 = 0;
    stream.Position = 0L;
    while (num1 < receive)
    {
      OperateResult<int> socket1 = NetSupport.ReadStream(stream, numArray1);
      if (!socket1.IsSuccess)
      {
        socket?.Close();
        return (OperateResult) socket1;
      }
      num1 += (long) socket1.Content;
      byte[] numArray2 = new byte[socket1.Content];
      Array.Copy((Array) numArray1, 0, (Array) numArray2, 0, numArray2.Length);
      OperateResult socket2 = this.SendBytesAndCheckReceive(socket, socket1.Content, numArray2);
      if (!socket2.IsSuccess)
      {
        socket?.Close();
        return socket2;
      }
      if (reportByPercent)
      {
        long num3 = num1 * 100L / receive;
        if (num2 != num3)
        {
          num2 = num3;
          if (report != null)
            report(num1, receive);
        }
      }
      else if (report != null)
        report(num1, receive);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 从套接字中接收所有的数据然后写入到指定的流当中去，需要指定数据的长度，支持按照百分比进行进度报告<br />
  /// Receives all data from the socket and writes it to the specified stream. The length of the data needs to be specified, and progress reporting is supported in percentage.
  /// </summary>
  /// <param name="socket">套接字</param>
  /// <param name="stream">数据流</param>
  /// <param name="totalLength">所有数据的长度</param>
  /// <param name="report">进度报告</param>
  /// <param name="reportByPercent">进度报告是否按照百分比</param>
  /// <returns>是否成功的结果对象</returns>
  protected OperateResult WriteStreamFromSocket(
    Socket socket,
    Stream stream,
    long totalLength,
    Action<long, long> report,
    bool reportByPercent)
  {
    long num1 = 0;
    long num2 = 0;
    while (num1 < totalLength)
    {
      OperateResult<int, byte[]> contentFromSocket = this.ReceiveBytesContentFromSocket(socket, 60000);
      if (!contentFromSocket.IsSuccess)
        return (OperateResult) contentFromSocket;
      num1 += (long) contentFromSocket.Content1;
      OperateResult operateResult = NetSupport.WriteStream(stream, contentFromSocket.Content2);
      if (!operateResult.IsSuccess)
      {
        socket?.Close();
        return operateResult;
      }
      if (reportByPercent)
      {
        long num3 = num1 * 100L / totalLength;
        if (num2 != num3)
        {
          num2 = num3;
          if (report != null)
            report(num1, totalLength);
        }
      }
      else if (report != null)
        report(num1, totalLength);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.CreateSocketAndConnect(System.Net.IPEndPoint,System.Int32,System.Net.IPEndPoint)" />
  protected async Task<OperateResult<Socket>> CreateSocketAndConnectAsync(
    IPEndPoint endPoint,
    int timeOut,
    IPEndPoint local = null)
  {
    OperateResult<Socket> connect = await NetSupport.CreateSocketAndConnectAsync(endPoint, timeOut, local).ConfigureAwait(false);
    if (connect.IsSuccess)
    {
      this.connectErrorCount = 0;
      return connect;
    }
    if (this.connectErrorCount < 1000000000)
      ++this.connectErrorCount;
    return new OperateResult<Socket>(-this.connectErrorCount, connect.Message);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.CreateSocketAndConnect(System.String,System.Int32)" />
  protected async Task<OperateResult<Socket>> CreateSocketAndConnectAsync(
    string ipAddress,
    int port)
  {
    OperateResult<Socket> socketAndConnectAsync = await this.CreateSocketAndConnectAsync(new IPEndPoint(IPAddress.Parse(ipAddress), port), 10000).ConfigureAwait(false);
    return socketAndConnectAsync;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.CreateSocketAndConnect(System.String,System.Int32,System.Int32)" />
  protected async Task<OperateResult<Socket>> CreateSocketAndConnectAsync(
    string ipAddress,
    int port,
    int timeOut)
  {
    OperateResult<Socket> socketAndConnectAsync = await this.CreateSocketAndConnectAsync(new IPEndPoint(IPAddress.Parse(ipAddress), port), timeOut).ConfigureAwait(false);
    return socketAndConnectAsync;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Receive(System.Net.Sockets.Socket,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected async Task<OperateResult<byte[]>> ReceiveAsync(
    Socket socket,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
    {
      OperateResult<byte[]> operateResult = new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    }
    int bufferLength = length > 0 ? length : 2048 /*0x0800*/;
    byte[] buffer;
    try
    {
      buffer = new byte[bufferLength];
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<byte[]>($"Create byte[{bufferLength}] buffer failed: " + ex.Message);
    }
    OperateResult<int> receive = await this.ReceiveAsync(socket, buffer, 0, length, timeOut, reportProgress).ConfigureAwait(false);
    return receive.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(length > 0 ? buffer : buffer.SelectBegin<byte>(receive.Content)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) receive);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Receive(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected async Task<OperateResult<int>> ReceiveAsync(
    Socket socket,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(length);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
    {
      OperateResult<int> operateResult = new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    }
    HslTimeOut hslTimeOut = HslTimeOut.HandleTimeOutCheck(socket, timeOut);
    try
    {
      if (length > 0)
      {
        int alreadyCount = 0;
        do
        {
          int currentReceiveLength = length - alreadyCount > 16384 /*0x4000*/ ? 16384 /*0x4000*/ : length - alreadyCount;
          int count = await Task.Factory.FromAsync<int>(socket.BeginReceive(buffer, alreadyCount + offset, currentReceiveLength, SocketFlags.None, (AsyncCallback) null, (object) socket), new Func<IAsyncResult, int>(socket.EndReceive)).ConfigureAwait(false);
          alreadyCount += count;
          if (count > 0)
          {
            hslTimeOut.StartTime = DateTime.Now;
            Action<long, long> action = reportProgress;
            if (action != null)
              action((long) alreadyCount, (long) length);
          }
          else
            goto label_9;
        }
        while (alreadyCount < length);
        goto label_13;
label_9:
        throw new RemoteCloseException();
label_13:
        hslTimeOut.IsSuccessful = true;
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int count1 = await Task.Factory.FromAsync<int>(socket.BeginReceive(buffer, offset, buffer.Length - offset, SocketFlags.None, (AsyncCallback) null, (object) socket), new Func<IAsyncResult, int>(socket.EndReceive)).ConfigureAwait(false);
      if (count1 == 0)
        throw new RemoteCloseException();
      hslTimeOut.IsSuccessful = true;
      return OperateResult.CreateSuccessResult<int>(count1);
    }
    catch (RemoteCloseException ex)
    {
      socket?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      hslTimeOut.IsSuccessful = true;
      return new OperateResult<int>(-this.connectErrorCount, StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      socket?.Close();
      hslTimeOut.IsSuccessful = true;
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return !hslTimeOut.IsTimeout ? new OperateResult<int>(-this.connectErrorCount, "Socket Exception -> " + ex.Message) : new OperateResult<int>(-this.connectErrorCount, StringResources.Language.ReceiveDataTimeout + hslTimeOut.DelayTime.ToString());
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveAsync(System.Net.Sockets.Socket,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected async Task<OperateResult<byte[]>> ReceiveAsync(
    SslStream ssl,
    int length,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
    {
      OperateResult<byte[]> operateResult = new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    }
    int bufferLength = length > 0 ? length : 2048 /*0x0800*/;
    byte[] buffer;
    try
    {
      buffer = new byte[bufferLength];
    }
    catch (Exception ex)
    {
      ssl?.Close();
      return new OperateResult<byte[]>($"Create byte[{bufferLength}] buffer failed: " + ex.Message);
    }
    OperateResult<int> receive = await this.ReceiveAsync(ssl, buffer, 0, length, timeOut, reportProgress).ConfigureAwait(false);
    return receive.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(length > 0 ? buffer : buffer.SelectBegin<byte>(receive.Content)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) receive);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveAsync(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected async Task<OperateResult<int>> ReceiveAsync(
    SslStream ssl,
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<int>(length);
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
    {
      OperateResult<int> operateResult = new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    }
    try
    {
      if (length > 0)
      {
        int alreadyCount = 0;
        do
        {
          int currentReceiveLength = length - alreadyCount > 16384 /*0x4000*/ ? 16384 /*0x4000*/ : length - alreadyCount;
          int count = await ssl.ReadAsync(buffer, alreadyCount + offset, currentReceiveLength).ConfigureAwait(false);
          alreadyCount += count;
          if (count != 0)
          {
            Action<long, long> action = reportProgress;
            if (action != null)
              action((long) alreadyCount, (long) length);
          }
          else
            goto label_8;
        }
        while (alreadyCount < length);
        goto label_13;
label_8:
        throw new RemoteCloseException();
label_13:
        return OperateResult.CreateSuccessResult<int>(length);
      }
      int count1 = await ssl.ReadAsync(buffer, offset, buffer.Length - offset).ConfigureAwait(false);
      return count1 != 0 ? OperateResult.CreateSuccessResult<int>(count1) : throw new RemoteCloseException();
    }
    catch (RemoteCloseException ex)
    {
      ssl?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return new OperateResult<int>(-this.connectErrorCount, StringResources.Language.RemoteClosedConnection);
    }
    catch (Exception ex)
    {
      ssl?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return new OperateResult<int>(-this.connectErrorCount, "Socket Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveCommandLineFromSocket(System.Net.Sockets.Socket,System.Byte,System.Int32)" />
  protected async Task<OperateResult<byte[]>> ReceiveCommandLineFromSocketAsync(
    Socket socket,
    byte endCode,
    int timeout = 2147483647 /*0x7FFFFFFF*/)
  {
    List<byte> bufferArray = new List<byte>(128 /*0x80*/);
    try
    {
      DateTime st = DateTime.Now;
      bool bOK = false;
      while ((DateTime.Now - st).TotalMilliseconds < (double) timeout)
      {
        if (socket.Poll(timeout, SelectMode.SelectRead))
        {
          OperateResult<byte[]> headResult = await this.ReceiveAsync(socket, 1, timeout).ConfigureAwait(false);
          if (!headResult.IsSuccess)
            return headResult;
          bufferArray.AddRange((IEnumerable<byte>) headResult.Content);
          if ((int) headResult.Content[0] == (int) endCode)
          {
            bOK = true;
            break;
          }
          headResult = (OperateResult<byte[]>) null;
        }
      }
      return bOK ? OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray()) : new OperateResult<byte[]>(StringResources.Language.ReceiveDataTimeout);
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveCommandLineFromSocket(System.Net.Sockets.Socket,System.Byte,System.Byte,System.Int32)" />
  protected async Task<OperateResult<byte[]>> ReceiveCommandLineFromSocketAsync(
    Socket socket,
    byte endCode1,
    byte endCode2,
    int timeout = 60000)
  {
    List<byte> bufferArray = new List<byte>(128 /*0x80*/);
    try
    {
      DateTime st = DateTime.Now;
      bool bOK = false;
      while ((DateTime.Now - st).TotalMilliseconds < (double) timeout)
      {
        if (socket.Poll(timeout, SelectMode.SelectRead))
        {
          OperateResult<byte[]> headResult = await this.ReceiveAsync(socket, 1, timeout).ConfigureAwait(false);
          if (!headResult.IsSuccess)
            return headResult;
          bufferArray.AddRange((IEnumerable<byte>) headResult.Content);
          if ((int) headResult.Content[0] == (int) endCode2 && bufferArray.Count > 1 && (int) bufferArray[bufferArray.Count - 2] == (int) endCode1)
          {
            bOK = true;
            break;
          }
          headResult = (OperateResult<byte[]>) null;
        }
      }
      return bOK ? OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray()) : new OperateResult<byte[]>(StringResources.Language.ReceiveDataTimeout);
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Send(System.Net.Sockets.Socket,System.Byte[])" />
  protected async Task<OperateResult> SendAsync(Socket socket, byte[] data)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    OperateResult operateResult = await this.SendAsync(socket, data, 0, data.Length).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Send(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  protected async Task<OperateResult> SendAsync(Socket socket, byte[] data, int offset, int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult(StringResources.Language.AuthorizationFailed);
    int alreadyCount = 0;
    try
    {
      do
      {
        int count = await Task.Factory.FromAsync<int>(socket.BeginSend(data, offset, size - alreadyCount, SocketFlags.None, (AsyncCallback) null, (object) socket), new Func<IAsyncResult, int>(socket.EndSend)).ConfigureAwait(false);
        alreadyCount += count;
        offset += count;
      }
      while (alreadyCount < size);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      socket?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return (OperateResult) new OperateResult<byte[]>(-this.connectErrorCount, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Send(System.Net.Sockets.Socket,System.Byte[])" />
  protected async Task<OperateResult> SendAsync(SslStream ssl, byte[] data)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    OperateResult operateResult = await this.SendAsync(ssl, data, 0, data.Length).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.Send(System.Net.Sockets.Socket,System.Byte[],System.Int32,System.Int32)" />
  protected async Task<OperateResult> SendAsync(SslStream ssl, byte[] data, int offset, int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    try
    {
      await ssl.WriteAsync(data, offset, size).ConfigureAwait(false);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      ssl?.Close();
      if (this.connectErrorCount < 1000000000)
        ++this.connectErrorCount;
      return (OperateResult) new OperateResult<byte[]>(-this.connectErrorCount, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveByMessage(System.Net.Sockets.Socket,System.Int32,HslCommunication.Core.IMessage.INetMessage,System.Action{System.Int64,System.Int64})" />
  protected virtual async Task<OperateResult<byte[]>> ReceiveByMessageAsync(
    Socket socket,
    int timeOut,
    INetMessage netMessage,
    Action<long, long> reportProgress = null)
  {
    if (netMessage == null)
    {
      OperateResult<byte[]> byMessageAsync = await this.ReceiveAsync(socket, -1, timeOut).ConfigureAwait(false);
      return byMessageAsync;
    }
    if (netMessage.ProtocolHeadBytesLength < 0)
    {
      byte[] headCode = BitConverter.GetBytes(netMessage.ProtocolHeadBytesLength);
      int codeLength = (int) headCode[3] & 15;
      OperateResult<byte[]> receive = (OperateResult<byte[]>) null;
      ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable;
      switch (codeLength)
      {
        case 1:
          configuredTaskAwaitable = this.ReceiveCommandLineFromSocketAsync(socket, headCode[1], timeOut).ConfigureAwait(false);
          receive = await configuredTaskAwaitable;
          break;
        case 2:
          receive = await this.ReceiveCommandLineFromSocketAsync(socket, headCode[1], headCode[0], timeOut).ConfigureAwait(false);
          break;
      }
      if (receive == null)
        return new OperateResult<byte[]>("Receive by specified code failed, length check failed");
      if (!receive.IsSuccess)
        return receive;
      netMessage.HeadBytes = receive.Content;
      if (!(netMessage is SpecifiedCharacterMessage message) || message.EndLength == (byte) 0)
        return receive;
      configuredTaskAwaitable = this.ReceiveAsync(socket, (int) message.EndLength, timeOut).ConfigureAwait(false);
      OperateResult<byte[]> endResult = await configuredTaskAwaitable;
      if (!endResult.IsSuccess)
        return endResult;
      return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(receive.Content, endResult.Content));
    }
    OperateResult<byte[]> headResult = await this.ReceiveAsync(socket, netMessage.ProtocolHeadBytesLength, timeOut).ConfigureAwait(false);
    if (!headResult.IsSuccess)
      return headResult;
    int start = netMessage.PependedUselesByteLength(headResult.Content);
    int cycleCount = 0;
    while (start >= netMessage.ProtocolHeadBytesLength)
    {
      headResult = await this.ReceiveAsync(socket, netMessage.ProtocolHeadBytesLength, timeOut).ConfigureAwait(false);
      if (!headResult.IsSuccess)
        return headResult;
      start = netMessage.PependedUselesByteLength(headResult.Content);
      ++cycleCount;
      if (cycleCount > 10)
        break;
    }
    if (start > 0)
    {
      OperateResult<byte[]> head2Result = await this.ReceiveAsync(socket, start, timeOut).ConfigureAwait(false);
      if (!head2Result.IsSuccess)
        return head2Result;
      headResult.Content = SoftBasic.SpliceArray<byte>(headResult.Content.RemoveBegin<byte>(start), head2Result.Content);
      head2Result = (OperateResult<byte[]>) null;
    }
    netMessage.HeadBytes = headResult.Content;
    int contentLength = netMessage.GetContentLengthByHeadBytes();
    if (contentLength <= 0)
      return OperateResult.CreateSuccessResult<byte[]>(headResult.Content);
    byte[] buffer = new byte[netMessage.ProtocolHeadBytesLength + contentLength];
    headResult.Content.CopyTo((Array) buffer, 0);
    OperateResult<int> contentResult = await this.ReceiveAsync(socket, buffer, netMessage.ProtocolHeadBytesLength, contentLength, timeOut, reportProgress).ConfigureAwait(false);
    return contentResult.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(buffer) : OperateResult.CreateFailedResult<byte[]>((OperateResult) contentResult);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveLong(System.Net.Sockets.Socket)" />
  private async Task<OperateResult<long>> ReceiveLongAsync(Socket socket)
  {
    OperateResult<byte[]> read = await this.ReceiveAsync(socket, 8, -1).ConfigureAwait(false);
    OperateResult<long> longAsync = !read.IsSuccess ? OperateResult.CreateFailedResult<long>((OperateResult) read) : OperateResult.CreateSuccessResult<long>(BitConverter.ToInt64(read.Content, 0));
    read = (OperateResult<byte[]>) null;
    return longAsync;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendLong(System.Net.Sockets.Socket,System.Int64)" />
  private async Task<OperateResult> SendLongAsync(Socket socket, long value)
  {
    OperateResult operateResult = await this.SendAsync(socket, BitConverter.GetBytes(value)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendBaseAndCheckReceive(System.Net.Sockets.Socket,System.Int32,System.Int32,System.Byte[])" />
  protected async Task<OperateResult> SendBaseAndCheckReceiveAsync(
    Socket socket,
    int headCode,
    int customer,
    byte[] send)
  {
    send = HslProtocol.CommandBytes(headCode, customer, this.Token, send);
    OperateResult sendResult = await this.SendAsync(socket, send).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return sendResult;
    OperateResult<long> checkResult = await this.ReceiveLongAsync(socket).ConfigureAwait(false);
    if (!checkResult.IsSuccess || checkResult.Content == (long) send.Length)
      return (OperateResult) checkResult;
    socket?.Close();
    return new OperateResult(StringResources.Language.CommandLengthCheckFailed);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendBytesAndCheckReceive(System.Net.Sockets.Socket,System.Int32,System.Byte[])" />
  protected async Task<OperateResult> SendBytesAndCheckReceiveAsync(
    Socket socket,
    int customer,
    byte[] send)
  {
    OperateResult async = await this.SendBaseAndCheckReceiveAsync(socket, 1002, customer, send).ConfigureAwait(false);
    return async;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendStringAndCheckReceive(System.Net.Sockets.Socket,System.Int32,System.String)" />
  protected async Task<OperateResult> SendStringAndCheckReceiveAsync(
    Socket socket,
    int customer,
    string send)
  {
    byte[] data = string.IsNullOrEmpty(send) ? (byte[]) null : Encoding.Unicode.GetBytes(send);
    OperateResult async = await this.SendBaseAndCheckReceiveAsync(socket, 1001, customer, data).ConfigureAwait(false);
    data = (byte[]) null;
    return async;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendStringAndCheckReceive(System.Net.Sockets.Socket,System.Int32,System.String[])" />
  protected async Task<OperateResult> SendStringAndCheckReceiveAsync(
    Socket socket,
    int customer,
    string[] sends)
  {
    OperateResult async = await this.SendBaseAndCheckReceiveAsync(socket, 1005, customer, HslProtocol.PackStringArrayToByte(sends)).ConfigureAwait(false);
    return async;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendAccountAndCheckReceive(System.Net.Sockets.Socket,System.Int32,System.String,System.String)" />
  protected async Task<OperateResult> SendAccountAndCheckReceiveAsync(
    Socket socket,
    int customer,
    string name,
    string pwd)
  {
    OperateResult async = await this.SendBaseAndCheckReceiveAsync(socket, 5, customer, HslProtocol.PackStringArrayToByte(new string[2]
    {
      name,
      pwd
    })).ConfigureAwait(false);
    return async;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveAndCheckBytes(System.Net.Sockets.Socket,System.Int32)" />
  protected async Task<OperateResult<byte[], byte[]>> ReceiveAndCheckBytesAsync(
    Socket socket,
    int timeout)
  {
    OperateResult<byte[]> headResult = await this.ReceiveAsync(socket, 32 /*0x20*/, timeout).ConfigureAwait(false);
    if (!headResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], byte[]>((OperateResult) headResult);
    if (!this.CheckRemoteToken(headResult.Content))
    {
      socket?.Close();
      return new OperateResult<byte[], byte[]>(StringResources.Language.TokenCheckFailed);
    }
    int contentLength = BitConverter.ToInt32(headResult.Content, 28);
    OperateResult<byte[]> contentResult = await this.ReceiveAsync(socket, contentLength, timeout).ConfigureAwait(false);
    if (!contentResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], byte[]>((OperateResult) contentResult);
    OperateResult checkResult = await this.SendLongAsync(socket, (long) (32 /*0x20*/ + contentLength)).ConfigureAwait(false);
    if (!checkResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], byte[]>(checkResult);
    byte[] head = headResult.Content;
    byte[] content = contentResult.Content;
    content = HslProtocol.CommandAnalysis(head, content);
    return OperateResult.CreateSuccessResult<byte[], byte[]>(head, content);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveStringContentFromSocket(System.Net.Sockets.Socket,System.Int32)" />
  protected async Task<OperateResult<int, string>> ReceiveStringContentFromSocketAsync(
    Socket socket,
    int timeOut = 30000)
  {
    OperateResult<byte[], byte[]> receive = await this.ReceiveAndCheckBytesAsync(socket, timeOut).ConfigureAwait(false);
    if (!receive.IsSuccess)
      return OperateResult.CreateFailedResult<int, string>((OperateResult) receive);
    if (BitConverter.ToInt32(receive.Content1, 0) != 1001)
    {
      socket?.Close();
      return new OperateResult<int, string>(StringResources.Language.CommandHeadCodeCheckFailed);
    }
    if (receive.Content2 == null)
      receive.Content2 = new byte[0];
    return OperateResult.CreateSuccessResult<int, string>(BitConverter.ToInt32(receive.Content1, 4), Encoding.Unicode.GetString(receive.Content2));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveStringArrayContentFromSocket(System.Net.Sockets.Socket,System.Int32)" />
  protected async Task<OperateResult<int, string[]>> ReceiveStringArrayContentFromSocketAsync(
    Socket socket,
    int timeOut = 30000)
  {
    OperateResult<byte[], byte[]> receive = await this.ReceiveAndCheckBytesAsync(socket, timeOut).ConfigureAwait(false);
    if (!receive.IsSuccess)
      return OperateResult.CreateFailedResult<int, string[]>((OperateResult) receive);
    if (BitConverter.ToInt32(receive.Content1, 0) != 1005)
    {
      socket?.Close();
      return new OperateResult<int, string[]>(StringResources.Language.CommandHeadCodeCheckFailed);
    }
    if (receive.Content2 == null)
      receive.Content2 = new byte[4];
    return OperateResult.CreateSuccessResult<int, string[]>(BitConverter.ToInt32(receive.Content1, 4), HslProtocol.UnPackStringArrayFromByte(receive.Content2));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveBytesContentFromSocket(System.Net.Sockets.Socket,System.Int32)" />
  protected async Task<OperateResult<int, byte[]>> ReceiveBytesContentFromSocketAsync(
    Socket socket,
    int timeout = 30000)
  {
    OperateResult<byte[], byte[]> receive = await this.ReceiveAndCheckBytesAsync(socket, timeout).ConfigureAwait(false);
    if (!receive.IsSuccess)
      return OperateResult.CreateFailedResult<int, byte[]>((OperateResult) receive);
    if (BitConverter.ToInt32(receive.Content1, 0) == 1002)
      return OperateResult.CreateSuccessResult<int, byte[]>(BitConverter.ToInt32(receive.Content1, 4), receive.Content2);
    socket?.Close();
    return new OperateResult<int, byte[]>(StringResources.Language.CommandHeadCodeCheckFailed);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendStreamToSocket(System.Net.Sockets.Socket,System.IO.Stream,System.Int64,System.Action{System.Int64,System.Int64},System.Boolean)" />
  protected async Task<OperateResult> SendStreamToSocketAsync(
    Socket socket,
    Stream stream,
    long receive,
    Action<long, long> report,
    bool reportByPercent)
  {
    byte[] buffer = new byte[this.fileCacheSize];
    long SendTotal = 0;
    long percent = 0;
    stream.Position = 0L;
    while (SendTotal < receive)
    {
      OperateResult<int> read = await NetSupport.ReadStreamAsync(stream, buffer).ConfigureAwait(false);
      if (!read.IsSuccess)
      {
        socket?.Close();
        return (OperateResult) read;
      }
      SendTotal += (long) read.Content;
      byte[] newBuffer = new byte[read.Content];
      Array.Copy((Array) buffer, 0, (Array) newBuffer, 0, newBuffer.Length);
      OperateResult write = await this.SendBytesAndCheckReceiveAsync(socket, read.Content, newBuffer).ConfigureAwait(false);
      if (!write.IsSuccess)
      {
        socket?.Close();
        return write;
      }
      if (reportByPercent)
      {
        long percentCurrent = SendTotal * 100L / receive;
        if (percent != percentCurrent)
        {
          percent = percentCurrent;
          Action<long, long> action = report;
          if (action != null)
            action(SendTotal, receive);
        }
      }
      else
      {
        Action<long, long> action = report;
        if (action != null)
          action(SendTotal, receive);
      }
      read = (OperateResult<int>) null;
      newBuffer = (byte[]) null;
      write = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.WriteStreamFromSocket(System.Net.Sockets.Socket,System.IO.Stream,System.Int64,System.Action{System.Int64,System.Int64},System.Boolean)" />
  protected async Task<OperateResult> WriteStreamFromSocketAsync(
    Socket socket,
    Stream stream,
    long totalLength,
    Action<long, long> report,
    bool reportByPercent)
  {
    long count_receive = 0;
    long percent = 0;
    while (count_receive < totalLength)
    {
      OperateResult<int, byte[]> read = await this.ReceiveBytesContentFromSocketAsync(socket, 60000).ConfigureAwait(false);
      if (!read.IsSuccess)
        return (OperateResult) read;
      count_receive += (long) read.Content1;
      OperateResult write = await NetSupport.WriteStreamAsync(stream, read.Content2).ConfigureAwait(false);
      if (!write.IsSuccess)
      {
        socket?.Close();
        return write;
      }
      if (reportByPercent)
      {
        long percentCurrent = count_receive * 100L / totalLength;
        if (percent != percentCurrent)
        {
          percent = percentCurrent;
          Action<long, long> action = report;
          if (action != null)
            action(count_receive, totalLength);
        }
      }
      else
      {
        Action<long, long> action = report;
        if (action != null)
          action(count_receive, totalLength);
      }
      read = (OperateResult<int, byte[]>) null;
      write = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 接收一条完整的MQTT协议的报文信息，包含控制码和负载数据<br />
  /// Receive a message of a completed MQTT protocol, including control code and payload data
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="timeOut">超时时间</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <returns>结果数据内容</returns>
  protected OperateResult<byte, byte[]> ReceiveMqttMessage(
    Socket socket,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    return MqttHelper.ReceiveMqttMessage<Socket>(new Func<Socket, int, int, Action<long, long>, OperateResult<byte[]>>(this.Receive), socket, timeOut, reportProgress);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveMqttMessage(System.Net.Sockets.Socket,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected OperateResult<byte, byte[]> ReceiveMqttMessage(
    SslStream ssl,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    return MqttHelper.ReceiveMqttMessage<SslStream>(new Func<SslStream, int, int, Action<long, long>, OperateResult<byte[]>>(this.Receive), ssl, timeOut, reportProgress);
  }

  /// <summary>
  /// 使用MQTT协议从socket接收指定长度的字节数组，然后全部写入到流中，可以指定进度报告<br />
  /// Use the MQTT protocol to receive a byte array of specified length from the socket, and then write all of them to the stream, and you can specify a progress report
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="stream">数据流</param>
  /// <param name="fileSize">数据大小</param>
  /// <param name="timeOut">超时时间</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">取消的令牌操作信息</param>
  /// <returns>是否操作成功</returns>
  protected OperateResult ReceiveMqttStream(
    Socket socket,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    long num = 0;
    while (num < fileSize)
    {
      OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(socket, timeOut);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      if (mqttMessage.Content1 == (byte) 0)
      {
        socket?.Close();
        return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
      }
      if (aesCryptography != null)
      {
        try
        {
          mqttMessage.Content2 = aesCryptography.Decrypt(mqttMessage.Content2);
        }
        catch (Exception ex)
        {
          socket?.Close();
          return new OperateResult("AES Decrypt file stream failed: " + ex.Message);
        }
      }
      OperateResult mqttStream1 = NetSupport.WriteStream(stream, mqttMessage.Content2);
      if (!mqttStream1.IsSuccess)
        return mqttStream1;
      num += (long) mqttMessage.Content2.Length;
      byte[] payLoad = new byte[16 /*0x10*/];
      BitConverter.GetBytes(num).CopyTo((Array) payLoad, 0);
      BitConverter.GetBytes(fileSize).CopyTo((Array) payLoad, 8);
      if (cancelToken != null && cancelToken.IsCancelled)
      {
        OperateResult mqttStream2 = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content);
        if (!mqttStream2.IsSuccess)
        {
          socket?.Close();
          return mqttStream2;
        }
        socket?.Close();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      OperateResult mqttStream3 = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, payLoad).Content);
      if (!mqttStream3.IsSuccess)
        return mqttStream3;
      if (reportProgress != null)
        reportProgress(num, fileSize);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 使用MQTT协议将流中的数据读取到字节数组，然后都写入到socket里面，可以指定进度报告，主要用于将文件发送到网络。<br />
  /// Use the MQTT protocol to read the data in the stream into a byte array, and then write them all into the socket.
  /// You can specify a progress report, which is mainly used to send files to the network.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="stream">流</param>
  /// <param name="fileSize">总的数据大小</param>
  /// <param name="timeOut">超时信息</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">取消操作的令牌信息</param>
  /// <returns>是否操作成功</returns>
  protected OperateResult SendMqttStream(
    Socket socket,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    byte[] buffer = new byte[this.fileCacheSize];
    long num = 0;
    stream.Position = 0L;
    while (num < fileSize)
    {
      OperateResult<int> operateResult1 = NetSupport.ReadStream(stream, buffer);
      if (!operateResult1.IsSuccess)
      {
        socket?.Close();
        return (OperateResult) operateResult1;
      }
      num += (long) operateResult1.Content;
      if (cancelToken != null && cancelToken.IsCancelled)
      {
        OperateResult operateResult2 = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content);
        if (!operateResult2.IsSuccess)
        {
          socket?.Close();
          return operateResult2;
        }
        socket?.Close();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      OperateResult operateResult3 = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, buffer.SelectBegin<byte>(operateResult1.Content), aesCryptography).Content);
      if (!operateResult3.IsSuccess)
      {
        socket?.Close();
        return operateResult3;
      }
      OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(socket, timeOut);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      if (mqttMessage.Content1 == (byte) 0)
      {
        socket?.Close();
        return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
      }
      if (reportProgress != null)
        reportProgress(num, fileSize);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 使用MQTT协议将一个文件发送到网络上去，需要指定文件名，保存的文件名，可选指定文件描述信息，进度报告<br />
  /// To send a file to the network using the MQTT protocol, you need to specify the file name, the saved file name,
  /// optionally specify the file description information, and the progress report
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="filename">文件名称</param>
  /// <param name="servername">对方接收后保存的文件名</param>
  /// <param name="filetag">文件的描述信息</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">用户取消的令牌</param>
  /// <returns>是否操作成功</returns>
  protected OperateResult SendMqttFile(
    Socket socket,
    string filename,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    FileInfo fileInfo = new FileInfo(filename);
    if (!System.IO.File.Exists(filename))
    {
      OperateResult operateResult = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, Encoding.UTF8.GetBytes(StringResources.Language.FileNotExist)).Content);
      if (!operateResult.IsSuccess)
        return operateResult;
      socket?.Close();
      return new OperateResult(StringResources.Language.FileNotExist);
    }
    string[] data = new string[3]
    {
      servername,
      fileInfo.Length.ToString(),
      filetag
    };
    OperateResult operateResult1 = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(data)).Content);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(socket, 60000);
    if (!mqttMessage.IsSuccess)
      return (OperateResult) mqttMessage;
    if (mqttMessage.Content1 == (byte) 0)
    {
      socket?.Close();
      return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
    }
    try
    {
      OperateResult operateResult2 = new OperateResult();
      using (FileStream fileStream = new FileStream(filename, FileMode.Open, FileAccess.Read))
        operateResult2 = this.SendMqttStream(socket, (Stream) fileStream, fileInfo.Length, 60000, reportProgress, aesCryptography, cancelToken);
      return operateResult2;
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult("SendMqttStream Exception -> " + ex.Message);
    }
  }

  /// <summary>
  /// 使用MQTT协议将一个数据流发送到网络上去，需要保存的文件名，可选指定文件描述信息，进度报告<br />
  /// Use the MQTT protocol to send a data stream to the network, the file name that needs to be saved, optional file description information, progress report
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="stream">数据流</param>
  /// <param name="servername">对方接收后保存的文件名</param>
  /// <param name="filetag">文件的描述信息</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">用户取消的令牌信息</param>
  /// <returns>是否操作成功</returns>
  protected OperateResult SendMqttFile(
    Socket socket,
    Stream stream,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    string[] data = new string[3]
    {
      servername,
      stream.Length.ToString(),
      filetag
    };
    OperateResult operateResult = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(data)).Content);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(socket, 60000);
    if (!mqttMessage.IsSuccess)
      return (OperateResult) mqttMessage;
    if (mqttMessage.Content1 == (byte) 0)
    {
      socket?.Close();
      return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
    }
    try
    {
      return this.SendMqttStream(socket, stream, stream.Length, 60000, reportProgress, aesCryptography, cancelToken);
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult("SendMqttStream Exception -> " + ex.Message);
    }
  }

  /// <summary>
  /// 使用MQTT协议从网络接收字节数组，然后写入文件或流中，支持进度报告<br />
  /// Use MQTT protocol to receive byte array from the network, and then write it to file or stream, support progress report
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="source">文件名或是流</param>
  /// <param name="reportProgress">进度报告</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">用户取消的令牌信息</param>
  /// <returns>是否操作成功，如果成功，携带文件基本信息</returns>
  protected OperateResult<FileBaseInfo> ReceiveMqttFile(
    Socket socket,
    object source,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    OperateResult<byte, byte[]> mqttMessage = this.ReceiveMqttMessage(socket, 60000);
    if (!mqttMessage.IsSuccess)
      return OperateResult.CreateFailedResult<FileBaseInfo>((OperateResult) mqttMessage);
    if (mqttMessage.Content1 == (byte) 0)
    {
      socket?.Close();
      return new OperateResult<FileBaseInfo>(Encoding.UTF8.GetString(mqttMessage.Content2));
    }
    FileBaseInfo fileBaseInfo = new FileBaseInfo();
    string[] strArray = HslProtocol.UnPackStringArrayFromByte(mqttMessage.Content2);
    fileBaseInfo.Name = strArray[0];
    fileBaseInfo.Size = long.Parse(strArray[1]);
    fileBaseInfo.Tag = strArray[2];
    this.Send(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, (byte[]) null).Content);
    try
    {
      OperateResult result = (OperateResult) null;
      switch (source)
      {
        case string path:
          using (FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write))
            result = this.ReceiveMqttStream(socket, (Stream) fileStream, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken);
          if (!result.IsSuccess)
          {
            if (System.IO.File.Exists(path))
              System.IO.File.Delete(path);
            return OperateResult.CreateFailedResult<FileBaseInfo>(result);
          }
          break;
        case Stream stream:
          this.ReceiveMqttStream(socket, stream, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken);
          break;
        default:
          throw new Exception("Not Supported Type");
      }
      return OperateResult.CreateSuccessResult<FileBaseInfo>(fileBaseInfo);
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<FileBaseInfo>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveMqttMessage(System.Net.Sockets.Socket,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected async Task<OperateResult<byte, byte[]>> ReceiveMqttMessageAsync(
    Socket socket,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    OperateResult<byte, byte[]> mqttMessageAsync = await MqttHelper.ReceiveMqttMessageAsync<Socket>(new Func<Socket, int, int, Action<long, long>, Task<OperateResult<byte[]>>>(this.ReceiveAsync), socket, timeOut, reportProgress).ConfigureAwait(false);
    return mqttMessageAsync;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveMqttMessage(System.Net.Sockets.Socket,System.Int32,System.Action{System.Int64,System.Int64})" />
  protected async Task<OperateResult<byte, byte[]>> ReceiveMqttMessageAsync(
    SslStream ssl,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    OperateResult<byte, byte[]> mqttMessageAsync = await MqttHelper.ReceiveMqttMessageAsync<SslStream>(new Func<SslStream, int, int, Action<long, long>, Task<OperateResult<byte[]>>>(this.ReceiveAsync), ssl, timeOut, reportProgress).ConfigureAwait(false);
    return mqttMessageAsync;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveMqttStream(System.Net.Sockets.Socket,System.IO.Stream,System.Int64,System.Int32,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  protected async Task<OperateResult> ReceiveMqttStreamAsync(
    Socket socket,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    long already = 0;
    while (already < fileSize)
    {
      OperateResult<byte, byte[]> receive = await this.ReceiveMqttMessageAsync(socket, timeOut).ConfigureAwait(false);
      if (!receive.IsSuccess)
        return (OperateResult) receive;
      if (receive.Content1 == (byte) 0)
      {
        socket?.Close();
        return new OperateResult(Encoding.UTF8.GetString(receive.Content2));
      }
      if (aesCryptography != null)
      {
        try
        {
          receive.Content2 = aesCryptography.Decrypt(receive.Content2);
        }
        catch (Exception ex)
        {
          socket?.Close();
          return new OperateResult("AES Decrypt file stream failed: " + ex.Message);
        }
      }
      ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = NetSupport.WriteStreamAsync(stream, receive.Content2).ConfigureAwait(false);
      OperateResult write = await configuredTaskAwaitable;
      if (!write.IsSuccess)
        return write;
      already += (long) receive.Content2.Length;
      byte[] ack = new byte[16 /*0x10*/];
      BitConverter.GetBytes(already).CopyTo((Array) ack, 0);
      BitConverter.GetBytes(fileSize).CopyTo((Array) ack, 8);
      HslCancelToken hslCancelToken = cancelToken;
      if (hslCancelToken != null && hslCancelToken.IsCancelled)
      {
        OperateResult cancel = this.Send(socket, MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content);
        if (!cancel.IsSuccess)
        {
          socket?.Close();
          return cancel;
        }
        socket?.Close();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      configuredTaskAwaitable = this.SendAsync(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, ack).Content).ConfigureAwait(false);
      OperateResult send = await configuredTaskAwaitable;
      if (!send.IsSuccess)
        return send;
      Action<long, long> action = reportProgress;
      if (action != null)
        action(already, fileSize);
      receive = (OperateResult<byte, byte[]>) null;
      write = (OperateResult) null;
      ack = (byte[]) null;
      send = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendMqttStream(System.Net.Sockets.Socket,System.IO.Stream,System.Int64,System.Int32,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  protected async Task<OperateResult> SendMqttStreamAsync(
    Socket socket,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    byte[] buffer = new byte[this.fileCacheSize];
    long already = 0;
    stream.Position = 0L;
    while (already < fileSize)
    {
      OperateResult<int> read = await NetSupport.ReadStreamAsync(stream, buffer).ConfigureAwait(false);
      if (!read.IsSuccess)
      {
        socket?.Close();
        return (OperateResult) read;
      }
      HslCancelToken hslCancelToken = cancelToken;
      ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable;
      if (hslCancelToken != null && hslCancelToken.IsCancelled)
      {
        configuredTaskAwaitable = this.SendAsync(socket, MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content).ConfigureAwait(false);
        OperateResult cancel = await configuredTaskAwaitable;
        if (!cancel.IsSuccess)
        {
          socket?.Close();
          return cancel;
        }
        socket?.Close();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      already += (long) read.Content;
      configuredTaskAwaitable = this.SendAsync(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, buffer.SelectBegin<byte>(read.Content), aesCryptography).Content).ConfigureAwait(false);
      OperateResult write = await configuredTaskAwaitable;
      if (!write.IsSuccess)
      {
        socket?.Close();
        return write;
      }
      OperateResult<byte, byte[]> receive = await this.ReceiveMqttMessageAsync(socket, timeOut).ConfigureAwait(false);
      if (!receive.IsSuccess)
        return (OperateResult) receive;
      if (receive.Content1 == (byte) 0)
      {
        socket?.Close();
        return new OperateResult(Encoding.UTF8.GetString(receive.Content2));
      }
      Action<long, long> action = reportProgress;
      if (action != null)
        action(already, fileSize);
      read = (OperateResult<int>) null;
      write = (OperateResult) null;
      receive = (OperateResult<byte, byte[]>) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendMqttFile(System.Net.Sockets.Socket,System.String,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  protected async Task<OperateResult> SendMqttFileAsync(
    Socket socket,
    string filename,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    FileInfo info = new FileInfo(filename);
    if (!System.IO.File.Exists(filename))
    {
      OperateResult notFoundResult = await this.SendAsync(socket, MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, Encoding.UTF8.GetBytes(StringResources.Language.FileNotExist)).Content).ConfigureAwait(false);
      if (!notFoundResult.IsSuccess)
        return notFoundResult;
      socket?.Close();
      return new OperateResult(StringResources.Language.FileNotExist);
    }
    string[] array = new string[3]
    {
      servername,
      info.Length.ToString(),
      filetag
    };
    OperateResult sendResult = await this.SendAsync(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(array)).Content).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return sendResult;
    OperateResult<byte, byte[]> check = await this.ReceiveMqttMessageAsync(socket, 60000).ConfigureAwait(false);
    if (!check.IsSuccess)
      return (OperateResult) check;
    if (check.Content1 == (byte) 0)
    {
      socket?.Close();
      return new OperateResult(Encoding.UTF8.GetString(check.Content2));
    }
    try
    {
      OperateResult result = new OperateResult();
      using (FileStream fs = new FileStream(filename, FileMode.Open, FileAccess.Read))
        result = await this.SendMqttStreamAsync(socket, (Stream) fs, info.Length, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
      return result;
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult("SendMqttStreamAsync Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.SendMqttFile(System.Net.Sockets.Socket,System.IO.Stream,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  protected async Task<OperateResult> SendMqttFileAsync(
    Socket socket,
    Stream stream,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    string[] array = new string[3]
    {
      servername,
      stream.Length.ToString(),
      filetag
    };
    OperateResult sendResult = await this.SendAsync(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(array)).Content).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return sendResult;
    OperateResult<byte, byte[]> check = await this.ReceiveMqttMessageAsync(socket, 60000).ConfigureAwait(false);
    if (!check.IsSuccess)
      return (OperateResult) check;
    if (check.Content1 == (byte) 0)
    {
      socket?.Close();
      return new OperateResult(Encoding.UTF8.GetString(check.Content2));
    }
    try
    {
      OperateResult operateResult = await this.SendMqttStreamAsync(socket, stream, stream.Length, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
      return operateResult;
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult("SendMqttStreamAsync Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveMqttFile(System.Net.Sockets.Socket,System.Object,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  protected async Task<OperateResult<FileBaseInfo>> ReceiveMqttFileAsync(
    Socket socket,
    object source,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    OperateResult<byte, byte[]> receiveFileInfo = await this.ReceiveMqttMessageAsync(socket, 60000).ConfigureAwait(false);
    if (!receiveFileInfo.IsSuccess)
      return OperateResult.CreateFailedResult<FileBaseInfo>((OperateResult) receiveFileInfo);
    if (receiveFileInfo.Content1 == (byte) 0)
    {
      socket?.Close();
      return new OperateResult<FileBaseInfo>(Encoding.UTF8.GetString(receiveFileInfo.Content2));
    }
    FileBaseInfo fileBaseInfo = new FileBaseInfo();
    string[] array = HslProtocol.UnPackStringArrayFromByte(receiveFileInfo.Content2);
    if (array.Length < 3)
    {
      socket?.Close();
      return new OperateResult<FileBaseInfo>("FileBaseInfo Check failed: " + array.ToArrayString<string>());
    }
    fileBaseInfo.Name = array[0];
    fileBaseInfo.Size = long.Parse(array[1]);
    fileBaseInfo.Tag = array[2];
    OperateResult operateResult = await this.SendAsync(socket, MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, (byte[]) null).Content).ConfigureAwait(false);
    try
    {
      OperateResult write = (OperateResult) null;
      switch (source)
      {
        case string savename:
          using (FileStream fs = new FileStream(savename, FileMode.Create, FileAccess.Write))
            write = await this.ReceiveMqttStreamAsync(socket, (Stream) fs, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
          if (!write.IsSuccess)
          {
            if (System.IO.File.Exists(savename))
              System.IO.File.Delete(savename);
            return OperateResult.CreateFailedResult<FileBaseInfo>(write);
          }
          break;
        case Stream stream:
          write = await this.ReceiveMqttStreamAsync(socket, stream, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
          stream = (Stream) null;
          break;
        default:
          throw new Exception("Not Supported Type");
      }
      return OperateResult.CreateSuccessResult<FileBaseInfo>(fileBaseInfo);
    }
    catch (Exception ex)
    {
      socket?.Close();
      return new OperateResult<FileBaseInfo>(ex.Message);
    }
  }

  /// <summary>
  /// 接收一行基于redis协议的字符串的信息，需要指定固定的长度<br />
  /// Receive a line of information based on the redis protocol string, you need to specify a fixed length
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="length">字符串的长度</param>
  /// <returns>带有结果对象的数据信息</returns>
  protected OperateResult<byte[]> ReceiveRedisCommandString(Socket socket, int length)
  {
    List<byte> byteList = new List<byte>();
    OperateResult<byte[]> redisCommandString = this.Receive(socket, length);
    if (!redisCommandString.IsSuccess)
      return redisCommandString;
    byteList.AddRange((IEnumerable<byte>) redisCommandString.Content);
    OperateResult<byte[]> commandLineFromSocket = this.ReceiveCommandLineFromSocket(socket, (byte) 10);
    if (!commandLineFromSocket.IsSuccess)
      return commandLineFromSocket;
    byteList.AddRange((IEnumerable<byte>) commandLineFromSocket.Content);
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>
  /// 从网络接收一条完整的redis报文的消息<br />
  /// Receive a complete redis message from the network
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <returns>接收的结果对象</returns>
  protected OperateResult<byte[]> ReceiveRedisCommand(Socket socket)
  {
    List<byte> byteList = new List<byte>();
    OperateResult<byte[]> commandLineFromSocket = this.ReceiveCommandLineFromSocket(socket, (byte) 10);
    if (!commandLineFromSocket.IsSuccess)
      return commandLineFromSocket;
    byteList.AddRange((IEnumerable<byte>) commandLineFromSocket.Content);
    if (commandLineFromSocket.Content[0] == (byte) 43 || commandLineFromSocket.Content[0] == (byte) 45 || commandLineFromSocket.Content[0] == (byte) 58)
      return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    if (commandLineFromSocket.Content[0] == (byte) 36)
    {
      OperateResult<int> numberFromCommandLine = RedisHelper.GetNumberFromCommandLine(commandLineFromSocket.Content);
      if (!numberFromCommandLine.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) numberFromCommandLine);
      if (numberFromCommandLine.Content < 0)
        return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
      OperateResult<byte[]> redisCommandString = this.ReceiveRedisCommandString(socket, numberFromCommandLine.Content);
      if (!redisCommandString.IsSuccess)
        return redisCommandString;
      byteList.AddRange((IEnumerable<byte>) redisCommandString.Content);
      return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    if (commandLineFromSocket.Content[0] != (byte) 42)
      return new OperateResult<byte[]>("Not Supported HeadCode: " + commandLineFromSocket.Content[0].ToString());
    OperateResult<int> numberFromCommandLine1 = RedisHelper.GetNumberFromCommandLine(commandLineFromSocket.Content);
    if (!numberFromCommandLine1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) numberFromCommandLine1);
    for (int index = 0; index < numberFromCommandLine1.Content; ++index)
    {
      OperateResult<byte[]> redisCommand = this.ReceiveRedisCommand(socket);
      if (!redisCommand.IsSuccess)
        return redisCommand;
      byteList.AddRange((IEnumerable<byte>) redisCommand.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveRedisCommandString(System.Net.Sockets.Socket,System.Int32)" />
  protected async Task<OperateResult<byte[]>> ReceiveRedisCommandStringAsync(
    Socket socket,
    int length)
  {
    List<byte> bufferArray = new List<byte>();
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = this.ReceiveAsync(socket, length).ConfigureAwait(false);
    OperateResult<byte[]> receive = await configuredTaskAwaitable;
    if (!receive.IsSuccess)
      return receive;
    bufferArray.AddRange((IEnumerable<byte>) receive.Content);
    configuredTaskAwaitable = this.ReceiveCommandLineFromSocketAsync(socket, (byte) 10).ConfigureAwait(false);
    OperateResult<byte[]> commandTail = await configuredTaskAwaitable;
    if (!commandTail.IsSuccess)
      return commandTail;
    bufferArray.AddRange((IEnumerable<byte>) commandTail.Content);
    return OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveRedisCommand(System.Net.Sockets.Socket)" />
  protected async Task<OperateResult<byte[]>> ReceiveRedisCommandAsync(Socket socket)
  {
    List<byte> bufferArray = new List<byte>();
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = this.ReceiveCommandLineFromSocketAsync(socket, (byte) 10).ConfigureAwait(false);
    OperateResult<byte[]> readCommandLine = await configuredTaskAwaitable;
    if (!readCommandLine.IsSuccess)
      return readCommandLine;
    bufferArray.AddRange((IEnumerable<byte>) readCommandLine.Content);
    if (readCommandLine.Content[0] == (byte) 43 || readCommandLine.Content[0] == (byte) 45 || readCommandLine.Content[0] == (byte) 58)
      return OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray());
    if (readCommandLine.Content[0] == (byte) 36)
    {
      OperateResult<int> lengthResult = RedisHelper.GetNumberFromCommandLine(readCommandLine.Content);
      if (!lengthResult.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) lengthResult);
      if (lengthResult.Content < 0)
        return OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray());
      configuredTaskAwaitable = this.ReceiveRedisCommandStringAsync(socket, lengthResult.Content).ConfigureAwait(false);
      OperateResult<byte[]> receiveContent = await configuredTaskAwaitable;
      if (!receiveContent.IsSuccess)
        return receiveContent;
      bufferArray.AddRange((IEnumerable<byte>) receiveContent.Content);
      return OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray());
    }
    if (readCommandLine.Content[0] != (byte) 42)
      return new OperateResult<byte[]>("Not Supported HeadCode: " + readCommandLine.Content[0].ToString());
    OperateResult<int> lengthResult1 = RedisHelper.GetNumberFromCommandLine(readCommandLine.Content);
    if (!lengthResult1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) lengthResult1);
    for (int i = 0; i < lengthResult1.Content; ++i)
    {
      configuredTaskAwaitable = this.ReceiveRedisCommandAsync(socket).ConfigureAwait(false);
      OperateResult<byte[]> receiveCommand = await configuredTaskAwaitable;
      if (!receiveCommand.IsSuccess)
        return receiveCommand;
      bufferArray.AddRange((IEnumerable<byte>) receiveCommand.Content);
      receiveCommand = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray());
  }

  /// <summary>
  /// 接收一条hsl协议的数据信息，自动解析，解压，解码操作，获取最后的实际的数据，接收结果依次为暗号，用户码，负载数据<br />
  /// Receive a piece of hsl protocol data information, automatically parse, decompress, and decode operations to obtain the last actual data.
  /// The result is a opCode, user code, and payload data in order.
  /// </summary>
  /// <param name="socket">网络套接字</param>
  /// <returns>接收结果，依次为暗号，用户码，负载数据</returns>
  protected OperateResult<int, int, byte[]> ReceiveHslMessage(Socket socket)
  {
    OperateResult<byte[]> result1 = this.Receive(socket, 32 /*0x20*/, 10000);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int, int, byte[]>((OperateResult) result1);
    int int32 = BitConverter.ToInt32(result1.Content, result1.Content.Length - 4);
    OperateResult<byte[]> result2 = this.Receive(socket, int32);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<int, int, byte[]>((OperateResult) result2);
    byte[] numArray = HslProtocol.CommandAnalysis(result1.Content, result2.Content);
    return OperateResult.CreateSuccessResult<int, int, byte[]>(BitConverter.ToInt32(result1.Content, 0), BitConverter.ToInt32(result1.Content, 4), numArray);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkBase.ReceiveHslMessage(System.Net.Sockets.Socket)" />
  protected async Task<OperateResult<int, int, byte[]>> ReceiveHslMessageAsync(Socket socket)
  {
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = this.ReceiveAsync(socket, 32 /*0x20*/, 10000).ConfigureAwait(false);
    OperateResult<byte[]> receiveHead = await configuredTaskAwaitable;
    if (!receiveHead.IsSuccess)
      return OperateResult.CreateFailedResult<int, int, byte[]>((OperateResult) receiveHead);
    int receive_length = BitConverter.ToInt32(receiveHead.Content, receiveHead.Content.Length - 4);
    configuredTaskAwaitable = this.ReceiveAsync(socket, receive_length).ConfigureAwait(false);
    OperateResult<byte[]> receiveContent = await configuredTaskAwaitable;
    if (!receiveContent.IsSuccess)
      return OperateResult.CreateFailedResult<int, int, byte[]>((OperateResult) receiveContent);
    byte[] Content = HslProtocol.CommandAnalysis(receiveHead.Content, receiveContent.Content);
    int protocol = BitConverter.ToInt32(receiveHead.Content, 0);
    int customer = BitConverter.ToInt32(receiveHead.Content, 4);
    return OperateResult.CreateSuccessResult<int, int, byte[]>(protocol, customer, Content);
  }

  /// <summary>
  /// 删除一个指定的文件，如果文件不存在，直接返回 <c>True</c>，如果文件存在则直接删除，删除成功返回 <c>True</c>，如果发生了异常，返回<c>False</c><br />
  /// Delete a specified file, if the file does not exist, return <c>True</c> directly, if the file exists, delete it directly,
  /// if the deletion is successful, return <c>True</c>, if an exception occurs, return <c> False</c>
  /// </summary>
  /// <param name="fileName">完整的文件路径</param>
  /// <returns>是否删除成功</returns>
  protected bool DeleteFileByName(string fileName)
  {
    try
    {
      if (!System.IO.File.Exists(fileName))
        return true;
      System.IO.File.Delete(fileName);
      return true;
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), $"delete file [{fileName}] failed: ", ex);
      return false;
    }
  }

  /// <inheritdoc />
  public override string ToString() => nameof (NetworkBase);
}
