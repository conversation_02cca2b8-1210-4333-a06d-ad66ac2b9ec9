﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.ModBus.ModbusHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.ModBus;

/// <summary>
/// Modbus协议相关辅助类，关于Modbus消息的组装以及拆分<br />
/// Modbus protocol related auxiliary classes, about the assembly and splitting of Modbus messages
/// </summary>
internal class ModbusHelper
{
  public static OperateResult<byte[]> ExtraRtuResponseContent(
    byte[] send,
    byte[] response,
    bool crcCheck = true,
    int broadcastStation = -1)
  {
    if (broadcastStation >= 0 && (int) send[0] == broadcastStation)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (response == null || response.Length < 5)
      return new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataLengthTooShort}5 Content: {response.ToHexString(' ')}");
    byte[] modbusRtu = ((IEnumerable<byte>) response).ToArray<byte>();
    for (int index = 0; index < 2; ++index)
    {
      if (modbusRtu[1] == (byte) 1 || modbusRtu[1] == (byte) 2 || modbusRtu[1] == (byte) 3 || modbusRtu[1] == (byte) 4 || modbusRtu[1] == (byte) 23)
      {
        if (modbusRtu.Length > 5 + (int) modbusRtu[2])
          modbusRtu = modbusRtu.SelectBegin<byte>(5 + (int) modbusRtu[2]);
      }
      else if (modbusRtu[1] == (byte) 5 || modbusRtu[1] == (byte) 6 || modbusRtu[1] == (byte) 15 || modbusRtu[1] == (byte) 16 /*0x10*/)
      {
        if (modbusRtu.Length > 8)
          modbusRtu = modbusRtu.SelectBegin<byte>(8);
      }
      else if (modbusRtu[1] > (byte) 128 /*0x80*/ && modbusRtu.Length > 5)
        modbusRtu = modbusRtu.SelectBegin<byte>(5);
      if (crcCheck && !SoftCRC16.CheckCRC16(modbusRtu))
      {
        if (index != 0)
          return new OperateResult<byte[]>(int.MinValue, StringResources.Language.ModbusCRCCheckFailed + SoftBasic.ByteToHexString(response, ' '));
        modbusRtu = response.RemoveBegin<byte>(1);
      }
      else
        break;
    }
    if ((int) send[0] != (int) modbusRtu[0])
      return new OperateResult<byte[]>($"Station not match, request: {send[0]}, but response is {modbusRtu[0]}");
    if ((int) send[1] + 128 /*0x80*/ == (int) modbusRtu[1])
      return new OperateResult<byte[]>((int) modbusRtu[2], ModbusInfo.GetDescriptionByErrorCode(modbusRtu[2]));
    return (int) send[1] != (int) modbusRtu[1] ? new OperateResult<byte[]>((int) modbusRtu[1], "Receive Command Check Failed: ") : ModbusInfo.ExtractActualData(ModbusInfo.ExplodeRtuCommandToCore(modbusRtu));
  }

  public static OperateResult<byte[]> ExtraAsciiResponseContent(
    byte[] send,
    byte[] response,
    int broadcastStation = -1)
  {
    if (broadcastStation >= 0)
    {
      try
      {
        if (Convert.ToInt32(Encoding.ASCII.GetString(send, 1, 2), 16 /*0x10*/) == broadcastStation)
          return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      }
      catch
      {
      }
    }
    OperateResult<byte[]> core = ModbusInfo.TransAsciiPackCommandToCore(response);
    if (!core.IsSuccess)
      return core;
    if (core.Content.Length < 3)
      return new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataLengthTooShort} 3, Content: {core.Content.ToHexString(' ')}");
    return (int) send[1] + 128 /*0x80*/ == (int) core.Content[1] ? new OperateResult<byte[]>((int) core.Content[2], ModbusInfo.GetDescriptionByErrorCode(core.Content[2])) : ModbusInfo.ExtractActualData(core.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Read(System.String,System.UInt16)" />
  public static OperateResult<byte[]> Read(IModbus modbus, string address, ushort length)
  {
    if (ModbusHelper.CheckFileAddress(address))
    {
      int parameter1 = HslHelper.ExtractParameter(ref address, "file", 0);
      int parameter2 = HslHelper.ExtractParameter(ref address, "s", (int) modbus.Station);
      return ModbusHelper.ReadFile(modbus, (byte) parameter2, (ushort) parameter1, ushort.Parse(address), length);
    }
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 3);
    if (!modbusAddress.IsSuccess)
      return modbusAddress.ConvertFailed<byte[]>();
    OperateResult<byte[][]> result = ModbusInfo.BuildReadModbusCommand(modbus, modbusAddress.Content, (int) length, (byte) 3);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : modbus.ReadFromCoreServer((IEnumerable<byte[]>) result.Content);
  }

  private static OperateResult<byte[]> CreateReadFileResult(OperateResult<byte[]> read)
  {
    if (!read.IsSuccess)
      return read;
    if (read.Content.Length < 2)
      return new OperateResult<byte[]>(StringResources.Language.ReceiveDataLengthTooShort + "2");
    MemoryStream ms = new MemoryStream();
    int num;
    for (int index = 0; index < read.Content.Length; index += num + 1)
    {
      num = (int) read.Content[index];
      ms.Write(read.Content.SelectMiddle<byte>(index + 2, num - 1));
    }
    return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
  }

  private static bool CheckFileAddress(string address)
  {
    return Regex.IsMatch(address, "file=", RegexOptions.IgnoreCase);
  }

  /// <summary>
  /// 使用0x14功能码读取文件的数据内容，需要指定文件编号，地址，长度，返回读取的结果数据，读取的长度为任意长度，内部自动根据实际情况来切割操作。<br />
  /// </summary>
  /// <param name="modbus">Modbus设备通信对象</param>
  /// <param name="station">设备站号信息</param>
  /// <param name="fileNumber">文件号</param>
  /// <param name="address">起始地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>返回结果</returns>
  public static OperateResult<byte[]> ReadFile(
    IModbus modbus,
    byte station,
    ushort fileNumber,
    ushort address,
    ushort length)
  {
    OperateResult<byte[][]> result = ModbusInfo.BuildReadFileModbusCommand(fileNumber, address, length, station, modbus.AddressStartWithZero);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : ModbusHelper.CreateReadFileResult(modbus.ReadFromCoreServer((IEnumerable<byte[]>) result.Content));
  }

  /// <summary>
  /// 使用0x15功能码写入文件的数据内容，需要指定文件编号，地址，数据，返回写入的结果数据。写入的数据字节长度不能大于248个字节，如果想写入更多的字节，就需要切片写入操作<br />
  /// </summary>
  /// <param name="modbus">Modbus设备通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="fileNumber">文件号</param>
  /// <param name="address">起始地址</param>
  /// <param name="data">写入的数据信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteFile(
    IModbus modbus,
    byte station,
    ushort fileNumber,
    ushort address,
    byte[] data)
  {
    OperateResult<byte[]> result = ModbusInfo.BuildWriteFileModbusCommand(fileNumber, address, data, station, modbus.AddressStartWithZero);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : (OperateResult) modbus.ReadFromCoreServer(result.Content);
  }

  /// <summary>
  /// 使用0x17功能码来实现同时写入并读取数据的操作，使用一条报文来实现，需要指定读取的地址，长度，写入的地址，写入的数据信息，返回读取的结果数据。<br />
  /// Use 0x17 function code to write and read data at the same time, and use a message to implement it,
  /// you need to specify the read address, length, written address, written data information, and return the read result data.
  /// </summary>
  /// <param name="modbus">Modbus通信对象</param>
  /// <param name="readAddress">读取的地址信息</param>
  /// <param name="length">读取的长度信息</param>
  /// <param name="writeAddress">写入的地址信息</param>
  /// <param name="value">写入的字节数据信息</param>
  /// <returns>读取的结果对象</returns>
  public static OperateResult<byte[]> ReadWrite(
    IModbus modbus,
    string readAddress,
    ushort length,
    string writeAddress,
    byte[] value)
  {
    OperateResult<string> modbusAddress1 = modbus.TranslateToModbusAddress(readAddress, (byte) 23);
    if (!modbusAddress1.IsSuccess)
      return modbusAddress1.ConvertFailed<byte[]>();
    OperateResult<string> modbusAddress2 = modbus.TranslateToModbusAddress(writeAddress, (byte) 23);
    if (!modbusAddress2.IsSuccess)
      return modbusAddress2.ConvertFailed<byte[]>();
    OperateResult<byte[]> result = ModbusInfo.BuildReadWriteModbusCommand(modbusAddress1.Content, length, modbusAddress2.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 23);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : modbus.ReadFromCoreServer(result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.Read(HslCommunication.ModBus.IModbus,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IModbus modbus,
    string address,
    ushort length)
  {
    if (ModbusHelper.CheckFileAddress(address))
    {
      int fileNumber = HslHelper.ExtractParameter(ref address, "file", 0);
      int station = HslHelper.ExtractParameter(ref address, "s", (int) modbus.Station);
      OperateResult<byte[]> operateResult = await ModbusHelper.ReadFileAsync(modbus, (byte) station, (ushort) fileNumber, ushort.Parse(address), length).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 3);
    if (!modbusAddress.IsSuccess)
      return modbusAddress.ConvertFailed<byte[]>();
    OperateResult<byte[][]> command = ModbusInfo.BuildReadModbusCommand(modbus, modbusAddress.Content, (int) length, (byte) 3);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content).ConfigureAwait(false);
    return operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.ReadWrite(HslCommunication.ModBus.IModbus,System.String,System.UInt16,System.String,System.Byte[])" />
  public static async Task<OperateResult<byte[]>> ReadWriteAsync(
    IModbus modbus,
    string readAddress,
    ushort length,
    string writeAddress,
    byte[] value)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(readAddress, (byte) 23);
    if (!modbusAddress.IsSuccess)
      return modbusAddress.ConvertFailed<byte[]>();
    OperateResult<string> modbusAddress2 = modbus.TranslateToModbusAddress(writeAddress, (byte) 23);
    if (!modbusAddress2.IsSuccess)
      return modbusAddress2.ConvertFailed<byte[]>();
    OperateResult<byte[]> command = ModbusInfo.BuildReadWriteModbusCommand(modbusAddress.Content, length, modbusAddress2.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 23);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.ReadFile(HslCommunication.ModBus.IModbus,System.Byte,System.UInt16,System.UInt16,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadFileAsync(
    IModbus modbus,
    byte station,
    ushort fileNumber,
    ushort address,
    ushort length)
  {
    OperateResult<byte[][]> command = ModbusInfo.BuildReadFileModbusCommand(fileNumber, address, length, station, modbus.AddressStartWithZero);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> read = await modbus.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content).ConfigureAwait(false);
    return ModbusHelper.CreateReadFileResult(read);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.WriteFile(HslCommunication.ModBus.IModbus,System.Byte,System.UInt16,System.UInt16,System.Byte[])" />
  /// "/&gt;
  public static async Task<OperateResult> WriteFileAsync(
    IModbus modbus,
    byte station,
    ushort fileNumber,
    ushort address,
    byte[] data)
  {
    OperateResult<byte[]> command = ModbusInfo.BuildWriteFileModbusCommand(fileNumber, address, data, station, modbus.AddressStartWithZero);
    if (!command.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Byte[])" />
  public static OperateResult Write(IModbus modbus, string address, byte[] value)
  {
    if (ModbusHelper.CheckFileAddress(address))
    {
      int parameter1 = HslHelper.ExtractParameter(ref address, "file", 0);
      int parameter2 = HslHelper.ExtractParameter(ref address, "s", (int) modbus.Station);
      return ModbusHelper.WriteFile(modbus, (byte) parameter2, (ushort) parameter1, ushort.Parse(address), value);
    }
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 16 /*0x10*/);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> operateResult = ModbusInfo.BuildWriteWordModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 16 /*0x10*/);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) modbus.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.Write(HslCommunication.ModBus.IModbus,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(IModbus modbus, string address, byte[] value)
  {
    if (ModbusHelper.CheckFileAddress(address))
    {
      int fileNumber = HslHelper.ExtractParameter(ref address, "file", 0);
      int station = HslHelper.ExtractParameter(ref address, "s", (int) modbus.Station);
      OperateResult operateResult = await ModbusHelper.WriteFileAsync(modbus, (byte) station, (ushort) fileNumber, ushort.Parse(address), value).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 16 /*0x10*/);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> command = ModbusInfo.BuildWriteWordModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 16 /*0x10*/);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Int16)" />
  public static OperateResult Write(IModbus modbus, string address, short value)
  {
    if (ModbusHelper.CheckFileAddress(address))
      return modbus.Write(address, new short[1]{ value });
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 6);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> operateResult = ModbusInfo.BuildWriteWordModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 6, modbus.ByteTransform);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) modbus.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.Write(HslCommunication.ModBus.IModbus,System.String,System.Int16)" />
  public static async Task<OperateResult> WriteAsync(IModbus modbus, string address, short value)
  {
    if (ModbusHelper.CheckFileAddress(address))
    {
      OperateResult operateResult = await modbus.WriteAsync(address, new short[1]
      {
        value
      }).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 6);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> command = ModbusInfo.BuildWriteWordModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 6, modbus.ByteTransform);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.UInt16)" />
  public static OperateResult Write(IModbus modbus, string address, ushort value)
  {
    if (ModbusHelper.CheckFileAddress(address))
      return modbus.Write(address, new ushort[1]{ value });
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 6);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> operateResult = ModbusInfo.BuildWriteWordModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 6, modbus.ByteTransform);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) modbus.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.Write(HslCommunication.ModBus.IModbus,System.String,System.UInt16)" />
  public static async Task<OperateResult> WriteAsync(IModbus modbus, string address, ushort value)
  {
    if (ModbusHelper.CheckFileAddress(address))
    {
      OperateResult operateResult = await modbus.WriteAsync(address, new ushort[1]
      {
        value
      });
      return operateResult;
    }
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 6);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> command = ModbusInfo.BuildWriteWordModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 6, modbus.ByteTransform);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.WriteMask(System.String,System.UInt16,System.UInt16)" />
  public static OperateResult WriteMask(
    IModbus modbus,
    string address,
    ushort andMask,
    ushort orMask)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 22);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> operateResult = ModbusInfo.BuildWriteMaskModbusCommand(modbusAddress.Content, andMask, orMask, modbus.Station, modbus.AddressStartWithZero, (byte) 22);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) modbus.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.WriteMask(HslCommunication.ModBus.IModbus,System.String,System.UInt16,System.UInt16)" />
  public static async Task<OperateResult> WriteMaskAsync(
    IModbus modbus,
    string address,
    ushort andMask,
    ushort orMask)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 22);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    OperateResult<byte[]> command = ModbusInfo.BuildWriteMaskModbusCommand(modbusAddress.Content, andMask, orMask, modbus.Station, modbus.AddressStartWithZero, (byte) 22);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return (OperateResult) operateResult;
  }

  public static OperateResult<bool[]> ReadBoolHelper(
    IModbus modbus,
    string address,
    ushort length,
    byte function)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, function);
    if (!modbusAddress.IsSuccess)
      return modbusAddress.ConvertFailed<bool[]>();
    if (modbusAddress.Content.IndexOf('.') > 0)
    {
      string[] strArray = address.SplitDot();
      int int32;
      try
      {
        int32 = Convert.ToInt32(modbusAddress.Content.SplitDot()[1]);
      }
      catch (Exception ex)
      {
        return new OperateResult<bool[]>("Bit Index format wrong, " + ex.Message);
      }
      ushort length1 = (ushort) (((int) length + int32 + 15) / 16 /*0x10*/);
      OperateResult<byte[]> result = modbus.Read(strArray[0], length1);
      return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(SoftBasic.BytesReverseByWord(result.Content).ToBoolArray().SelectMiddle<bool>(int32, (int) length));
    }
    OperateResult<byte[][]> result1 = ModbusInfo.BuildReadModbusCommand(modbusAddress.Content, (int) length, modbus.Station, modbus.AddressStartWithZero, function);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    for (int index = 0; index < result1.Content.Length; ++index)
    {
      OperateResult<byte[]> result2 = modbus.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      int length2 = (int) result1.Content[index][4] * 256 /*0x0100*/ + (int) result1.Content[index][5];
      boolList.AddRange((IEnumerable<bool>) SoftBasic.ByteToBoolArray(result2.Content, length2));
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  internal static async Task<OperateResult<bool[]>> ReadBoolHelperAsync(
    IModbus modbus,
    string address,
    ushort length,
    byte function)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, function);
    if (!modbusAddress.IsSuccess)
      return modbusAddress.ConvertFailed<bool[]>();
    if (modbusAddress.Content.IndexOf('.') > 0)
    {
      string[] addressSplits = address.SplitDot();
      int bitIndex = 0;
      try
      {
        string[] modbusSplits = modbusAddress.Content.SplitDot();
        bitIndex = Convert.ToInt32(modbusSplits[1]);
        modbusSplits = (string[]) null;
      }
      catch (Exception ex)
      {
        return new OperateResult<bool[]>("Bit Index format wrong, " + ex.Message);
      }
      ushort len = (ushort) (((int) length + bitIndex + 15) / 16 /*0x10*/);
      OperateResult<byte[]> read = await modbus.ReadAsync(addressSplits[0], len).ConfigureAwait(false);
      return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(SoftBasic.BytesReverseByWord(read.Content).ToBoolArray().SelectMiddle<bool>(bitIndex, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    }
    OperateResult<byte[][]> command = ModbusInfo.BuildReadModbusCommand(modbusAddress.Content, (int) length, modbus.Station, modbus.AddressStartWithZero, function);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<bool> resultArray = new List<bool>();
    for (int i = 0; i < command.Content.Length; ++i)
    {
      OperateResult<byte[]> read = await modbus.ReadFromCoreServerAsync(command.Content[i]).ConfigureAwait(false);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      int bitLength = (int) command.Content[i][4] * 256 /*0x0100*/ + (int) command.Content[i][5];
      resultArray.AddRange((IEnumerable<bool>) SoftBasic.ByteToBoolArray(read.Content, bitLength));
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(resultArray.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Boolean[])" />
  public static OperateResult Write(IModbus modbus, string address, bool[] values)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 15);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    if (modbusAddress.Content.IndexOf('.') > 0)
      return ReadWriteNetHelper.WriteBoolWithWord((IReadWriteNet) modbus, address, values, reverseWord: true, bitStr: modbusAddress.Content.SplitDot()[1]);
    OperateResult<byte[]> operateResult = ModbusInfo.BuildWriteBoolModbusCommand(modbusAddress.Content, values, modbus.Station, modbus.AddressStartWithZero, (byte) 15);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) modbus.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.Write(HslCommunication.ModBus.IModbus,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(IModbus modbus, string address, bool[] values)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 15);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    if (modbusAddress.Content.IndexOf('.') > 0)
    {
      OperateResult operateResult = await ReadWriteNetHelper.WriteBoolWithWordAsync((IReadWriteNet) modbus, address, values, reverseWord: true, bitStr: modbusAddress.Content.SplitDot()[1]).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<byte[]> command = ModbusInfo.BuildWriteBoolModbusCommand(modbusAddress.Content, values, modbus.Station, modbus.AddressStartWithZero, (byte) 15);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Boolean)" />
  public static OperateResult Write(IModbus modbus, string address, bool value)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 5);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    if (address.IndexOf('.') > 0 && !modbus.EnableWriteMaskCode)
      return ModbusHelper.Write(modbus, address, new bool[1]
      {
        value
      });
    OperateResult<byte[]> operateResult1 = ModbusInfo.BuildWriteBoolModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 5);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult operateResult2 = (OperateResult) modbus.ReadFromCoreServer(operateResult1.Content);
    if (operateResult2.IsSuccess || address.IndexOf('.') <= 0 || operateResult2.ErrorCode != 1)
      return operateResult2;
    modbus.EnableWriteMaskCode = false;
    return ModbusHelper.Write(modbus, address, new bool[1]
    {
      value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.Write(HslCommunication.ModBus.IModbus,System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(IModbus modbus, string address, bool value)
  {
    OperateResult<string> modbusAddress = modbus.TranslateToModbusAddress(address, (byte) 5);
    if (!modbusAddress.IsSuccess)
      return (OperateResult) modbusAddress;
    if (address.IndexOf('.') > 0 && !modbus.EnableWriteMaskCode)
    {
      OperateResult operateResult = await ModbusHelper.WriteAsync(modbus, address, new bool[1]
      {
        value
      }).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<byte[]> command = ModbusInfo.BuildWriteBoolModbusCommand(modbusAddress.Content, value, modbus.Station, modbus.AddressStartWithZero, (byte) 5);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    OperateResult write = (OperateResult) operateResult1;
    operateResult1 = (OperateResult<byte[]>) null;
    if (write.IsSuccess || address.IndexOf('.') <= 0 || write.ErrorCode != 1)
      return write;
    modbus.EnableWriteMaskCode = false;
    OperateResult operateResult2 = await ModbusHelper.WriteAsync(modbus, address, new bool[1]
    {
      value
    }).ConfigureAwait(false);
    return operateResult2;
  }

  public static bool TransAddressToModbus(
    string station,
    string address,
    string[] code,
    int[] offset,
    Func<string, int> prase,
    out string newAddress)
  {
    newAddress = string.Empty;
    for (int index = 0; index < code.Length; ++index)
    {
      if (address.StartsWithAndNumber(code[index]))
      {
        newAddress = station + (prase(address.Substring(code[index].Length)) + offset[index]).ToString();
        return true;
      }
    }
    return false;
  }

  public static bool TransAddressToModbus(
    string station,
    string address,
    string[] code,
    int[] offset,
    out string newAddress)
  {
    return ModbusHelper.TransAddressToModbus(station, address, code, offset, new Func<string, int>(int.Parse), out newAddress);
  }

  public static bool TransPointAddressToModbus(
    string station,
    string address,
    string[] code,
    int[] offset,
    Func<string, int> prase,
    out string newAddress)
  {
    newAddress = string.Empty;
    int num = address.IndexOf('.');
    if (num > 0)
    {
      string str = address.Substring(num);
      address = address.Substring(0, num);
      if (ModbusHelper.TransAddressToModbus(station, address, code, offset, prase, out newAddress))
      {
        newAddress += str;
        return true;
      }
    }
    return false;
  }

  /// <summary>针对带有小数点的地址进行转换，例如 D100.0 转成 100.0</summary>
  /// <param name="station">站号信息</param>
  /// <param name="address">地址</param>
  /// <param name="code">地址类型</param>
  /// <param name="offset">起始偏移地址</param>
  /// <param name="newAddress">返回的新的地址</param>
  /// <returns>是否匹配当前的地址类型</returns>
  public static bool TransPointAddressToModbus(
    string station,
    string address,
    string[] code,
    int[] offset,
    out string newAddress)
  {
    return ModbusHelper.TransPointAddressToModbus(station, address, code, offset, new Func<string, int>(int.Parse), out newAddress);
  }
}
