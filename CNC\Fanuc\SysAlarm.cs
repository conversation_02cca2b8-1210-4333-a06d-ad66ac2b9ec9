﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.SysAlarm
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>当前机床的报警信息</summary>
public class SysAlarm
{
  /// <summary>当前报警的ID信息</summary>
  public int AlarmId { get; set; }

  /// <summary>当前的报警类型</summary>
  public short Type { get; set; }

  /// <summary>报警的轴信息</summary>
  public short Axis { get; set; }

  /// <summary>报警的消息</summary>
  public string Message { get; set; }
}
