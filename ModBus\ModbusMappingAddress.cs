﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.ModBus.ModbusMappingAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Address;
using System;

#nullable disable
namespace HslCommunication.ModBus;

/// <summary>Modbus的地址映射类</summary>
public class ModbusMappingAddress
{
  private static int ParseBitAddress(string address, int wordLength = 16 /*0x10*/)
  {
    int length = address.IndexOf('.');
    return length > 0 ? Convert.ToInt32(address.Substring(0, length)) * wordLength + HslHelper.CalculateBitStartIndex(address.Substring(length + 1)) : Convert.ToInt32(address) * wordLength;
  }

  /// <summary>
  /// 根据台达AS300的PLC的地址，解析出转换后的modbus协议信息，适用AS300系列，当前的地址仍然支持站号指定，例如s=2;D100<br />
  /// According to the PLC address of Delta AS300, the converted modbus protocol information is parsed,
  /// and it is applicable to AS300 series. The current address still supports station number designation, for example, s=2;D100
  /// </summary>
  /// <param name="address">台达plc的地址信息</param>
  /// <param name="modbusCode">原始的对应的modbus信息</param>
  /// <returns>还原后的modbus地址</returns>
  public static OperateResult<string> Delta_AS(string address, byte modbusCode)
  {
    try
    {
      string str = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        str = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWith("SM") || address.StartsWith("sm"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 16384 /*0x4000*/).ToString());
        if (address.StartsWith("HC") || address.StartsWith("hc"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 64512).ToString());
        if (address.StartsWith("S") || address.StartsWith("s"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 20480 /*0x5000*/).ToString());
        if (address.StartsWith("X") || address.StartsWith("x"))
          return OperateResult.CreateSuccessResult<string>($"{str}x=2;{(ModbusMappingAddress.ParseBitAddress(address.Substring(1)) + 24576 /*0x6000*/).ToString()}");
        if (address.StartsWith("Y") || address.StartsWith("y"))
          return OperateResult.CreateSuccessResult<string>(str + (ModbusMappingAddress.ParseBitAddress(address.Substring(1)) + 40960 /*0xA000*/).ToString());
        if (address.StartsWith("T") || address.StartsWith("t"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 57344 /*0xE000*/).ToString());
        if (address.StartsWith("C") || address.StartsWith("c"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 61440 /*0xF000*/).ToString());
        if (address.StartsWith("M") || address.StartsWith("m"))
          return OperateResult.CreateSuccessResult<string>(str + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWith("D") && address.Contains("."))
          return OperateResult.CreateSuccessResult<string>(str + address);
      }
      else
      {
        if (address.StartsWith("SR") || address.StartsWith("sr"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 49152 /*0xC000*/).ToString());
        if (address.StartsWith("HC") || address.StartsWith("hc"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 64512).ToString());
        if (address.StartsWith("D") || address.StartsWith("d"))
          return OperateResult.CreateSuccessResult<string>(str + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWith("X") || address.StartsWith("x"))
          return OperateResult.CreateSuccessResult<string>($"{str}x=4;{(Convert.ToInt32(address.Substring(1)) + 32768 /*0x8000*/).ToString()}");
        if (address.StartsWith("Y") || address.StartsWith("y"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 40960 /*0xA000*/).ToString());
        if (address.StartsWith("C") || address.StartsWith("c"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 61440 /*0xF000*/).ToString());
        if (address.StartsWith("T") || address.StartsWith("t"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 57344 /*0xE000*/).ToString());
        if (address.StartsWith("E") || address.StartsWith("e"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 65024).ToString());
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  /// <summary>
  /// 维控的Lx5v的Modbus地址转换方法，主要注入到Modbus类，即可以在Modbus里使用维控的地址进行通信
  /// </summary>
  /// <param name="address">维控plc的地址信息</param>
  /// <param name="modbusCode">原始的对应的modbus信息</param>
  /// <returns>还原后的modbus地址</returns>
  public static OperateResult<string> WeCon_Lx5v(string address, byte modbusCode)
  {
    try
    {
      string station = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        station = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWithAndNumber("T"))
          return OperateResult.CreateSuccessResult<string>(station + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWithAndNumber("C"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 1536 /*0x0600*/).ToString());
        if (address.StartsWithAndNumber("LC"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(2)) + 2560 /*0x0A00*/).ToString());
        if (address.StartsWithAndNumber("HSC"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(3)) + 3584 /*0x0E00*/).ToString());
        if (address.StartsWithAndNumber("M"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 4096 /*0x1000*/).ToString());
        if (address.StartsWithAndNumber("SM"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(2)) + 20480 /*0x5000*/).ToString());
        if (address.StartsWithAndNumber("S"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 49152 /*0xC000*/).ToString());
        if (address.StartsWithAndNumber("X"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1), 8) + 57344 /*0xE000*/).ToString());
        if (address.StartsWithAndNumber("Y"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1), 8) + 61440 /*0xF000*/).ToString());
        string newAddress;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[3]
        {
          "D",
          "SD",
          "R"
        }, new int[3]
        {
          4096 /*0x1000*/,
          20480 /*0x5000*/,
          32768 /*0x8000*/
        }, new Func<string, int>(int.Parse), out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
      }
      else
      {
        if (address.StartsWithAndNumber("T"))
          return OperateResult.CreateSuccessResult<string>(station + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWithAndNumber("C"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 1536 /*0x0600*/).ToString());
        if (address.StartsWithAndNumber("LC"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(2)) * 2 + 2560 /*0x0A00*/).ToString());
        if (address.StartsWithAndNumber("HSC"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(3)) * 2 + 3584 /*0x0E00*/).ToString());
        if (address.StartsWithAndNumber("D"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 4096 /*0x1000*/).ToString());
        if (address.StartsWithAndNumber("SD"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(2)) + 20480 /*0x5000*/).ToString());
        if (address.StartsWithAndNumber("R"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 32768 /*0x8000*/).ToString());
      }
      throw new Exception(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }

  /// <summary>英威腾 TS600 的modbus地址变换</summary>
  /// <param name="address">英威腾PLC的地址</param>
  /// <param name="modbusCode">默认的modbus功能码</param>
  /// <returns>还原后的modbus地址</returns>
  public static OperateResult<string> Invt_Ts(string address, byte modbusCode)
  {
    try
    {
      string station = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        station = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWithAndNumber("M"))
          return OperateResult.CreateSuccessResult<string>(station + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWithAndNumber("S"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 32768 /*0x8000*/).ToString());
        if (address.StartsWithAndNumber("X"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1), 8) + 40960 /*0xA000*/).ToString());
        if (address.StartsWithAndNumber("Y"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1), 8) + 45056 /*0xB000*/).ToString());
        if (address.StartsWithAndNumber("T"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 49152 /*0xC000*/).ToString());
        if (address.StartsWithAndNumber("C"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 49664).ToString());
        string newAddress;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[2]
        {
          "D",
          "R"
        }, new int[2]{ 0, 32768 /*0x8000*/ }, new Func<string, int>(int.Parse), out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
      }
      else
      {
        if (address.StartsWithAndNumber("T"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 57344 /*0xE000*/).ToString());
        if (address.StartsWithAndNumber("C"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 57856).ToString());
        if (address.StartsWithAndNumber("Z"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(2)) * 2 + 58368).ToString());
        if (address.StartsWithAndNumber("D"))
          return OperateResult.CreateSuccessResult<string>(station + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWithAndNumber("R"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 32768 /*0x8000*/).ToString());
        if (address.StartsWithAndNumber("M"))
          return OperateResult.CreateSuccessResult<string>($"{station}x=1;{Convert.ToInt32(address.Substring(1)).ToString()}");
      }
      throw new Exception(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }
}
