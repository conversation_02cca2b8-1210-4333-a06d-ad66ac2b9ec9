﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleySLCNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>
/// AllenBradley品牌的PLC，针对SLC系列的通信的实现，测试PLC为1747。<br />
/// AllenBradley brand PLC, for the realization of SLC series communication, the test PLC is 1747.
/// </summary>
/// <remarks>
/// 地址支持的举例：A9:0, N9:0, B9:0, F9:0, S:0, ST1:0, C9:0, I:0/10, O:0/1, R9:0, T9:0, L9:0
/// </remarks>
/// <example>
/// 地址格式如下：
/// <list type="table">
///   <listheader>
///     <term>地址代号</term>
///     <term>字操作</term>
///     <term>位操作</term>
///     <term>备注</term>
///   </listheader>
///   <item>
///     <term>A</term>
///     <term>A9:0</term>
///     <term>A9:0/1 或 A9:0.1</term>
///     <term>ASCII</term>
///   </item>
///   <item>
///     <term>B</term>
///     <term>B9:0</term>
///     <term>B9:0/1 或 B9:0.1</term>
///     <term>Bit</term>
///   </item>
///   <item>
///     <term>N</term>
///     <term>N9:0</term>
///     <term>N9:0/1 或 N9:0.1</term>
///     <term>Integer</term>
///   </item>
///   <item>
///     <term>F</term>
///     <term>F9:0</term>
///     <term>F9:0/1 或 F9:0.1</term>
///     <term>Floating point</term>
///   </item>
///   <item>
///     <term>S</term>
///     <term>S:0</term>
///     <term>S:0/1 或 S:0.1</term>
///     <term>Status  S:0 等同于 S2:0</term>
///   </item>
///   <item>
///     <term>ST</term>
///     <term>ST1:0</term>
///     <term></term>
///     <term>String</term>
///   </item>
///   <item>
///     <term>C</term>
///     <term>C9:0</term>
///     <term>C9:0/1 或 C9:0.1</term>
///     <term>Counter</term>
///   </item>
///   <item>
///     <term>I</term>
///     <term>I:0</term>
///     <term>I:0/1 或 I9:0.1</term>
///     <term>Input</term>
///   </item>
///   <item>
///     <term>O</term>
///     <term>O:0</term>
///     <term>O:0/1 或 O9:0.1</term>
///     <term>Output</term>
///   </item>
///   <item>
///     <term>R</term>
///     <term>R9:0</term>
///     <term>R9:0/1 或 R9:0.1</term>
///     <term>Control</term>
///   </item>
///   <item>
///     <term>T</term>
///     <term>T9:0</term>
///     <term>T9:0/1 或 T9:0.1</term>
///     <term>Timer</term>
///   </item>
///   <item>
///     <term>L</term>
///     <term>L9:0</term>
///     <term>L9:0/1 或 L9:0.1</term>
///     <term>long integer</term>
///   </item>
/// </list>
/// 感谢 seedee 的测试支持。
/// </example>
public class AllenBradleySLCNet : DeviceTcpNet
{
  /// <summary>
  /// Instantiate a communication object for a Allenbradley PLC protocol
  /// </summary>
  public AllenBradleySLCNet()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>
  /// Instantiate a communication object for a Allenbradley PLC protocol
  /// </summary>
  /// <param name="ipAddress">PLC IpAddress</param>
  /// <param name="port">PLC Port</param>
  public AllenBradleySLCNet(string ipAddress, int port = 44818)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new AllenBradleySLCMessage();

  /// <summary>
  /// The current session handle, which is determined by the PLC when communicating with the PLC handshake
  /// </summary>
  public uint SessionHandle { get; protected set; }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.CommunicationPipe, "01 01 00 00 00 00 00 00 00 00 00 00 00 04 00 05 00 00 00 00 00 00 00 00 00 00 00 00".ToHexBytes(), true, true);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    if (operateResult.Content.Length >= 8)
      this.SessionHandle = this.ByteTransform.TransUInt32(operateResult.Content, 4);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.CommunicationPipe, "01 01 00 00 00 00 00 00 00 00 00 00 00 04 00 05 00 00 00 00 00 00 00 00 00 00 00 00".ToHexBytes(), true, true).ConfigureAwait(false);
    if (!read.IsSuccess)
      return (OperateResult) read;
    if (read.Content.Length >= 8)
      this.SessionHandle = this.ByteTransform.TransUInt32(read.Content, 4);
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// Read data information, data length for read array length information
  /// </summary>
  /// <param name="address">Address format of the node</param>
  /// <param name="length">In the case of arrays, the length of the array </param>
  /// <returns>Result data with result object </returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult1 = AllenBradleySLCNet.BuildReadCommand(address, (int) length);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommand(operateResult1.Content));
    if (!operateResult2.IsSuccess)
      return operateResult2;
    OperateResult<byte[]> operateResult3 = AllenBradleySLCNet.ExtraActualContent(operateResult2.Content);
    return !operateResult3.IsSuccess ? operateResult3 : OperateResult.CreateSuccessResult<byte[]>(operateResult3.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = AllenBradleySLCNet.BuildWriteCommand(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommand(operateResult1.Content));
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult<byte[]> operateResult3 = AllenBradleySLCNet.ExtraActualContent(operateResult2.Content);
    return !operateResult3.IsSuccess ? (OperateResult) operateResult3 : (OperateResult) OperateResult.CreateSuccessResult<byte[]>(operateResult3.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    int bitIndex;
    address = AllenBradleySLCNet.AnalysisBitIndex(address, out bitIndex);
    OperateResult<byte[]> result = this.Read(address, (ushort) 1);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) result) : OperateResult.CreateSuccessResult<bool>(result.Content.ToBoolArray()[bitIndex]);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    OperateResult<byte[]> operateResult1 = AllenBradleySLCNet.BuildWriteCommand(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommand(operateResult1.Content));
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult<byte[]> operateResult3 = AllenBradleySLCNet.ExtraActualContent(operateResult2.Content);
    return !operateResult3.IsSuccess ? (OperateResult) operateResult3 : (OperateResult) OperateResult.CreateSuccessResult<byte[]>(operateResult3.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleySLCNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> command = AllenBradleySLCNet.BuildReadCommand(address, (int) length);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.PackCommand(command.Content));
    if (!read.IsSuccess)
      return read;
    OperateResult<byte[]> extra = AllenBradleySLCNet.ExtraActualContent(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(extra.Content) : extra;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> command = AllenBradleySLCNet.BuildWriteCommand(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.PackCommand(command.Content));
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult<byte[]> extra = AllenBradleySLCNet.ExtraActualContent(read.Content);
    return extra.IsSuccess ? (OperateResult) OperateResult.CreateSuccessResult<byte[]>(extra.Content) : (OperateResult) extra;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    int bitIndex;
    address = AllenBradleySLCNet.AnalysisBitIndex(address, out bitIndex);
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 1);
    OperateResult<bool> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<bool>(read.Content.ToBoolArray()[bitIndex]) : OperateResult.CreateFailedResult<bool>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult<byte[]> command = AllenBradleySLCNet.BuildWriteCommand(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.PackCommand(command.Content));
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult<byte[]> extra = AllenBradleySLCNet.ExtraActualContent(read.Content);
    return extra.IsSuccess ? (OperateResult) OperateResult.CreateSuccessResult<byte[]>(extra.Content) : (OperateResult) extra;
  }

  private byte[] PackCommand(byte[] coreCmd)
  {
    byte[] numArray = new byte[28 + coreCmd.Length];
    numArray[0] = (byte) 1;
    numArray[1] = (byte) 7;
    numArray[2] = (byte) (coreCmd.Length / 256 /*0x0100*/);
    numArray[3] = (byte) (coreCmd.Length % 256 /*0x0100*/);
    BitConverter.GetBytes(this.SessionHandle).CopyTo((Array) numArray, 4);
    coreCmd.CopyTo((Array) numArray, 28);
    return numArray;
  }

  /// <summary>分析地址数据信息里的位索引的信息</summary>
  /// <param name="address">数据地址</param>
  /// <param name="bitIndex">位索引</param>
  /// <returns>地址信息</returns>
  public static string AnalysisBitIndex(string address, out int bitIndex)
  {
    bitIndex = 0;
    int length = address.IndexOf('/');
    if (length < 0)
      length = address.IndexOf('.');
    if (length > 0)
    {
      bitIndex = int.Parse(address.Substring(length + 1));
      address = address.Substring(0, length);
    }
    return address;
  }

  /// <summary>构建读取的指令信息</summary>
  /// <param name="address">地址信息，举例：A9:0</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>是否成功</returns>
  public static OperateResult<byte[]> BuildReadCommand(string address, int length)
  {
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if (length < 2)
      length = 2;
    if (from.Content.DataCode == (byte) 142)
      from.Content.AddressStart /= 2;
    byte[] numArray = new byte[14]
    {
      (byte) 0,
      (byte) 5,
      (byte) 0,
      (byte) 0,
      (byte) 15,
      (byte) 0,
      (byte) 0,
      (byte) 1,
      (byte) 162,
      (byte) length,
      (byte) from.Content.DbBlock,
      from.Content.DataCode,
      (byte) 0,
      (byte) 0
    };
    BitConverter.GetBytes((ushort) from.Content.AddressStart).CopyTo((Array) numArray, 12);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>构建写入的报文内容，变成实际的数据</summary>
  /// <param name="address">地址信息</param>
  /// <param name="value">数据值</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<byte[]> BuildWriteCommand(string address, byte[] value)
  {
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if (from.Content.DataCode == (byte) 142)
      from.Content.AddressStart /= 2;
    byte[] numArray = new byte[18 + value.Length];
    numArray[0] = (byte) 0;
    numArray[1] = (byte) 5;
    numArray[2] = (byte) 0;
    numArray[3] = (byte) 0;
    numArray[4] = (byte) 15;
    numArray[5] = (byte) 0;
    numArray[6] = (byte) 0;
    numArray[7] = (byte) 1;
    numArray[8] = (byte) 171;
    numArray[9] = byte.MaxValue;
    numArray[10] = BitConverter.GetBytes(value.Length)[0];
    numArray[11] = BitConverter.GetBytes(value.Length)[1];
    numArray[12] = (byte) from.Content.DbBlock;
    numArray[13] = from.Content.DataCode;
    BitConverter.GetBytes((ushort) from.Content.AddressStart).CopyTo((Array) numArray, 14);
    numArray[16 /*0x10*/] = byte.MaxValue;
    numArray[17] = byte.MaxValue;
    value.CopyTo((Array) numArray, 18);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>构建写入的报文内容，变成实际的数据</summary>
  /// <param name="address">地址信息</param>
  /// <param name="value">数据值</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<byte[]> BuildWriteCommand(string address, bool value)
  {
    int bitIndex;
    address = AllenBradleySLCNet.AnalysisBitIndex(address, out bitIndex);
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if (from.Content.DataCode == (byte) 142)
      from.Content.AddressStart /= 2;
    int num = 1 << bitIndex;
    byte[] numArray = new byte[20]
    {
      (byte) 0,
      (byte) 5,
      (byte) 0,
      (byte) 0,
      (byte) 15,
      (byte) 0,
      (byte) 0,
      (byte) 1,
      (byte) 171,
      byte.MaxValue,
      (byte) 2,
      (byte) 0,
      (byte) from.Content.DbBlock,
      from.Content.DataCode,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0
    };
    BitConverter.GetBytes((ushort) from.Content.AddressStart).CopyTo((Array) numArray, 14);
    numArray[16 /*0x10*/] = BitConverter.GetBytes(num)[0];
    numArray[17] = BitConverter.GetBytes(num)[1];
    if (value)
    {
      numArray[18] = BitConverter.GetBytes(num)[0];
      numArray[19] = BitConverter.GetBytes(num)[1];
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>解析当前的实际报文内容，变成数据内容</summary>
  /// <param name="content">报文内容</param>
  /// <returns>是否成功</returns>
  public static OperateResult<byte[]> ExtraActualContent(byte[] content)
  {
    return content.Length < 36 ? new OperateResult<byte[]>(StringResources.Language.ReceiveDataLengthTooShort + content.ToHexString(' ')) : OperateResult.CreateSuccessResult<byte[]>(content.RemoveBegin<byte>(36));
  }
}
