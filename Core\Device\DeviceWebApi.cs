﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Device.DeviceWebApi
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Device;

/// <summary>基于WebApi接口的设备基类</summary>
public class DeviceWebApi : DeviceCommunication
{
  private NetworkWebApiBase webApi;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkWebApiBase.#ctor(System.String)" />
  public DeviceWebApi(string ipAddress)
    : this(ipAddress, 80 /*0x50*/, string.Empty, string.Empty)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkWebApiBase.#ctor(System.String,System.Int32)" />
  public DeviceWebApi(string ipAddress, int port)
    : this(ipAddress, port, string.Empty, string.Empty)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkWebApiBase.#ctor(System.String,System.Int32,System.String,System.String)" />
  public DeviceWebApi(string ipAddress, int port, string name, string password)
  {
    this.webApi = new NetworkWebApiBase(ipAddress, port, name, password);
    this.webApi.AddRequestHeadersAction = new Action<HttpContentHeaders>(this.AddRequestHeaders);
  }

  /// <summary>针对请求的头信息进行额外的处理</summary>
  /// <param name="headers">头信息</param>
  protected virtual void AddRequestHeaders(HttpContentHeaders headers)
  {
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.IpAddress" />
  public string IpAddress
  {
    get => this.webApi.IpAddress;
    set => this.webApi.IpAddress = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.Port" />
  public int Port
  {
    get => this.webApi.Port;
    set => this.webApi.Port = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.UserName" />
  public string UserName
  {
    get => this.webApi.UserName;
    set => this.webApi.UserName = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.Password" />
  public string Password
  {
    get => this.webApi.Password;
    set => this.webApi.Password = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.UseHttps" />
  public bool UseHttps
  {
    get => this.webApi.UseHttps;
    set => this.webApi.UseHttps = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.DefaultContentType" />
  public string DefaultContentType
  {
    get => this.webApi.DefaultContentType;
    set => this.webApi.DefaultContentType = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.UseEncodingISO" />
  public bool UseEncodingISO
  {
    get => this.webApi.UseEncodingISO;
    set => this.webApi.UseEncodingISO = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkWebApiBase.Client" />
  public HttpClient Client => this.webApi.Client;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkWebApiBase.Get(System.String)" />
  public OperateResult<string> Get(string rawUrl) => this.webApi.Get(rawUrl);

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkWebApiBase.Post(System.String,System.String)" />
  public OperateResult<string> Post(string rawUrl, string body) => this.webApi.Post(rawUrl, body);

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceWebApi.Get(System.String)" />
  public async Task<OperateResult<string>> GetAsync(string rawUrl)
  {
    OperateResult<string> async = await this.webApi.GetAsync(rawUrl);
    return async;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceWebApi.Post(System.String,System.String)" />
  public async Task<OperateResult<string>> PostAsync(string rawUrl, string body)
  {
    OperateResult<string> operateResult = await this.webApi.PostAsync(rawUrl, body);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"DeviceWebApi[{this.IpAddress}:{this.Port}]";
}
