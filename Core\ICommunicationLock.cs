﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.ICommunicationLock
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// 用于通信的锁的接口对象，定义了进入锁和离开锁的方法接口信息<br />
/// An interface object for a lock used for communication that defines interface information for the methods used to enter and exit the lock
/// </summary>
public interface ICommunicationLock : IDisposable
{
  /// <summary>
  /// 进入锁操作，指定超时时间，单位毫秒，并返回是否成功获得锁的标记。<br />
  /// Enters the lock operation, specifies the timeout period in milliseconds, and returns a flag as to whether the lock was successfully obtained.
  /// </summary>
  /// <param name="timeout">超时时间</param>
  /// <returns>是否成功的获得锁</returns>
  OperateResult EnterLock(int timeout);

  /// <summary>
  /// 离开锁操作<br />
  /// Lockaway operation
  /// </summary>
  void LeaveLock();
}
