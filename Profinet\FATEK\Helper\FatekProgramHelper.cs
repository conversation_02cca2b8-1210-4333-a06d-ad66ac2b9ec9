﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.FATEK.Helper;

/// <summary>FatekProgram相关的辅助方法，例如报文构建，核心读写支持</summary>
public class FatekProgramHelper
{
  /// <summary>计算指令的和校验码</summary>
  /// <param name="data">指令</param>
  /// <returns>校验之后的信息</returns>
  public static string CalculateAcc(string data)
  {
    byte[] bytes = Encoding.ASCII.GetBytes(data);
    int num = 0;
    for (int index = 0; index < bytes.Length; ++index)
      num += (int) bytes[index];
    return num.ToString("X4").Substring(2);
  }

  /// <summary>检查当前的串口的数据接收是否完整</summary>
  /// <param name="ms">数据流</param>
  /// <returns>是否数据接收完成</returns>
  public static bool CheckReceiveDataComplete(MemoryStream ms)
  {
    byte[] array = ms.ToArray();
    return array.Length >= 5 && array[array.Length - 1] == (byte) 3;
  }

  /// <summary>将Fatek的基本命令打包成可以发送PLC的电文消息</summary>
  /// <param name="station">PLC的站号信息</param>
  /// <param name="cmd">基本命令信息</param>
  /// <returns>发送PLC的电文消息</returns>
  public static byte[] PackFatekCommand(byte station, string cmd)
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append('\u0002');
    stringBuilder.Append(station.ToString("X2"));
    byte[] buffer = new byte[6 + cmd.Length];
    buffer[0] = (byte) 2;
    buffer[1] = SoftBasic.BuildAsciiBytesFrom(station)[0];
    buffer[2] = SoftBasic.BuildAsciiBytesFrom(station)[1];
    Encoding.ASCII.GetBytes(cmd).CopyTo((Array) buffer, 3);
    SoftLRC.CalculateAccAndFill(buffer, 0, 3);
    buffer[buffer.Length - 1] = (byte) 3;
    return buffer;
  }

  /// <summary>创建一条读取的指令信息，需要指定一些参数</summary>
  /// <param name="station">PLC的站号</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<List<byte[]>> BuildReadWordCommand(
    byte station,
    string address,
    ushort length)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return from.ConvertFailed<List<byte[]>>();
    List<byte[]> numArrayList = new List<byte[]>();
    int[] array = SoftBasic.SplitIntegerToArray((int) length, 64 /*0x40*/);
    for (int index = 0; index < array.Length; ++index)
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("46");
      stringBuilder.Append(array[index].ToString("X2"));
      if (from.Content.DataCode.StartsWith("X") || from.Content.DataCode.StartsWith("Y") || from.Content.DataCode.StartsWith("M") || from.Content.DataCode.StartsWith("S") || from.Content.DataCode.StartsWith("T") || from.Content.DataCode.StartsWith("C"))
        stringBuilder.Append("W");
      stringBuilder.Append(from.Content.ToString());
      numArrayList.Add(FatekProgramHelper.PackFatekCommand(station, stringBuilder.ToString()));
      if (from.Content.DataCode.StartsWith("X") || from.Content.DataCode.StartsWith("Y") || from.Content.DataCode.StartsWith("M") || from.Content.DataCode.StartsWith("S") || from.Content.DataCode.StartsWith("T") || from.Content.DataCode.StartsWith("C"))
        from.Content.AddressStart += array[index] * 16 /*0x10*/;
      else
        from.Content.AddressStart += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>创建一条读取的指令信息，需要指定一些参数</summary>
  /// <param name="station">PLC的站号</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<List<byte[]>> BuildReadBoolCommand(
    byte station,
    string address,
    ushort length)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return from.ConvertFailed<List<byte[]>>();
    List<byte[]> numArrayList = new List<byte[]>();
    int[] array = SoftBasic.SplitIntegerToArray((int) length, (int) byte.MaxValue);
    for (int index = 0; index < array.Length; ++index)
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("44");
      stringBuilder.Append(array[index].ToString("X2"));
      stringBuilder.Append(from.Content.ToString());
      numArrayList.Add(FatekProgramHelper.PackFatekCommand(station, stringBuilder.ToString()));
      from.Content.AddressStart += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>提取当前的结果数据信息，针对的是字单位的方式</summary>
  /// <param name="response">PLC返回的数据信息</param>
  /// <param name="length">读取的长度内容</param>
  /// <returns>结果数组</returns>
  public static byte[] ExtraResponse(byte[] response, ushort length)
  {
    byte[] numArray = new byte[(int) length * 2];
    for (int index = 0; index < numArray.Length / 2; ++index)
      BitConverter.GetBytes(Convert.ToUInt16(Encoding.ASCII.GetString(response, index * 4 + 6, 4), 16 /*0x10*/)).CopyTo((Array) numArray, index * 2);
    return numArray;
  }

  /// <summary>创建一条别入bool数据的指令信息，需要指定一些参数</summary>
  /// <param name="station">站号</param>
  /// <param name="address">地址</param>
  /// <param name="value">数组值</param>
  /// <returns>是否创建成功</returns>
  public static OperateResult<byte[]> BuildWriteBoolCommand(
    byte station,
    string address,
    bool[] value)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return from.ConvertFailed<byte[]>();
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append("45");
    stringBuilder.Append(value.Length.ToString("X2"));
    stringBuilder.Append(from.Content.ToString());
    for (int index = 0; index < value.Length; ++index)
      stringBuilder.Append(value[index] ? "1" : "0");
    return OperateResult.CreateSuccessResult<byte[]>(FatekProgramHelper.PackFatekCommand(station, stringBuilder.ToString()));
  }

  /// <summary>创建一条别入byte数据的指令信息，需要指定一些参数，按照字单位</summary>
  /// <param name="station">站号</param>
  /// <param name="address">地址</param>
  /// <param name="value">数组值</param>
  /// <returns>是否创建成功</returns>
  public static OperateResult<byte[]> BuildWriteByteCommand(
    byte station,
    string address,
    byte[] value)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return from.ConvertFailed<byte[]>();
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append("47");
    stringBuilder.Append((value.Length / 2).ToString("X2"));
    if (from.Content.DataCode.StartsWith("X") || from.Content.DataCode.StartsWith("Y") || from.Content.DataCode.StartsWith("M") || from.Content.DataCode.StartsWith("S") || from.Content.DataCode.StartsWith("T") || from.Content.DataCode.StartsWith("C"))
      stringBuilder.Append("W");
    stringBuilder.Append(from.Content.ToString());
    byte[] bytes = new byte[value.Length * 2];
    for (int index = 0; index < value.Length / 2; ++index)
      SoftBasic.BuildAsciiBytesFrom(BitConverter.ToUInt16(value, index * 2)).CopyTo((Array) bytes, 4 * index);
    stringBuilder.Append(Encoding.ASCII.GetString(bytes));
    return OperateResult.CreateSuccessResult<byte[]>(FatekProgramHelper.PackFatekCommand(station, stringBuilder.ToString()));
  }

  /// <summary>检查PLC反馈的报文是否正确，如果不正确，返回错误消息</summary>
  /// <param name="content">PLC反馈的报文信息</param>
  /// <returns>反馈的报文是否正确</returns>
  public static OperateResult CheckResponse(byte[] content)
  {
    try
    {
      if (content[0] != (byte) 2)
        return new OperateResult((int) content[0], "Write Faild:" + SoftBasic.ByteToHexString(content, ' '));
      return content[5] != (byte) 48 /*0x30*/ ? new OperateResult((int) content[5], FatekProgramHelper.GetErrorDescriptionFromCode((char) content[5])) : OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return new OperateResult($"CheckResponse failed: {ex.Message}{Environment.NewLine}Source: {content.ToHexString(' ')}");
    }
  }

  /// <summary>根据错误码获取到真实的文本信息</summary>
  /// <param name="code">错误码</param>
  /// <returns>错误的文本描述</returns>
  public static string GetErrorDescriptionFromCode(char code)
  {
    switch (code)
    {
      case '2':
        return StringResources.Language.FatekStatus02;
      case '3':
        return StringResources.Language.FatekStatus03;
      case '4':
        return StringResources.Language.FatekStatus04;
      case '5':
        return StringResources.Language.FatekStatus05;
      case '6':
        return StringResources.Language.FatekStatus06;
      case '7':
        return StringResources.Language.FatekStatus07;
      case '9':
        return StringResources.Language.FatekStatus09;
      case 'A':
        return StringResources.Language.FatekStatus10;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>
  /// 批量读取PLC的字节数据，以字为单位，支持读取X,Y,M,S,D,T,C,R,RT,RC具体的地址范围需要根据PLC型号来确认，地址可以携带站号信息，例如 s=2;D100<br />
  /// Read PLC byte data in batches, in word units. Supports reading X, Y, M, S, D, T, C, R, RT, RC.
  /// The specific address range needs to be confirmed according to the PLC model, The address can carry station number information, such as s=2;D100
  /// </summary>
  /// <param name="device">PLC通信的对象</param>
  /// <param name="station">设备的站点信息</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>读取结果信息</returns>
  public static OperateResult<byte[]> Read(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<List<byte[]>> result1 = FatekProgramHelper.BuildReadWordCommand(station, address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    int[] array = SoftBasic.SplitIntegerToArray((int) length, 64 /*0x40*/);
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = device.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      OperateResult operateResult = FatekProgramHelper.CheckResponse(result2.Content);
      if (!operateResult.IsSuccess)
        return operateResult.ConvertFailed<byte[]>();
      byteList.AddRange((IEnumerable<byte>) FatekProgramHelper.ExtraResponse(result2.Content, (ushort) array[index]));
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<List<byte[]>> command = FatekProgramHelper.BuildReadWordCommand(station, address, length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> content = new List<byte>();
    int[] splits = SoftBasic.SplitIntegerToArray((int) length, 64 /*0x40*/);
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      OperateResult check = FatekProgramHelper.CheckResponse(read.Content);
      if (!check.IsSuccess)
        return check.ConvertFailed<byte[]>();
      content.AddRange((IEnumerable<byte>) FatekProgramHelper.ExtraResponse(read.Content, (ushort) splits[i]));
      read = (OperateResult<byte[]>) null;
      check = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(content.ToArray());
  }

  /// <summary>
  /// 批量写入PLC的数据，以字为单位，也就是说最少2个字节信息，支持X,Y,M,S,D,T,C,R,RT,RC具体的地址范围需要根据PLC型号来确认，地址可以携带站号信息，例如 s=2;D100<br />
  /// The data written to the PLC in batches, in units of words, that is, at least 2 bytes of information,
  /// supporting X, Y, M, S, D, T, C, R, RT, and RC. The specific address range needs to be based on the PLC model To confirm, The address can carry station number information, such as s=2;D100
  /// </summary>
  /// <param name="device">PLC通信的对象</param>
  /// <param name="station">设备的站号信息</param>
  /// <param name="address">地址信息，举例，D100，R200，RC100，RT200</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteDevice device,
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> operateResult1 = FatekProgramHelper.BuildWriteByteCommand(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = device.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult operateResult3 = FatekProgramHelper.CheckResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> command = FatekProgramHelper.BuildWriteByteCommand(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = FatekProgramHelper.CheckResponse(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult() : check;
  }

  /// <summary>
  /// 批量读取bool类型数据，支持的类型为X,Y,M,S,T,C，具体的地址范围取决于PLC的类型，地址可以携带站号信息，例如 s=2;M100<br />
  /// Read bool data in batches. The supported types are X, Y, M, S, T, C. The specific address range depends on the type of PLC,
  /// The address can carry station number information, such as s=2;M100
  /// </summary>
  /// <param name="device">PLC通信对象</param>
  /// <param name="station">设备的站号信息</param>
  /// <param name="address">地址信息，比如X10，Y17，M100</param>
  /// <param name="length">读取的长度</param>
  /// <returns>读取结果信息</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<List<byte[]>> result1 = FatekProgramHelper.BuildReadBoolCommand(station, address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    int[] array = SoftBasic.SplitIntegerToArray((int) length, (int) byte.MaxValue);
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = device.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult operateResult = FatekProgramHelper.CheckResponse(result2.Content);
      if (!operateResult.IsSuccess)
        return operateResult.ConvertFailed<bool[]>();
      if (result2.Content.Length < 6 + array[index])
        return new OperateResult<bool[]>($"{StringResources.Language.ReceiveDataLengthTooShort} Source: {result2.Content.ToHexString(' ')}");
      boolList.AddRange(((IEnumerable<byte>) result2.Content.SelectMiddle<byte>(6, array[index])).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)));
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<List<byte[]>> command = FatekProgramHelper.BuildReadBoolCommand(station, address, length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<bool> content = new List<bool>();
    int[] splits = SoftBasic.SplitIntegerToArray((int) length, (int) byte.MaxValue);
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult check = FatekProgramHelper.CheckResponse(read.Content);
      if (!check.IsSuccess)
        return check.ConvertFailed<bool[]>();
      if (read.Content.Length < 6 + splits[i])
        return new OperateResult<bool[]>($"{StringResources.Language.ReceiveDataLengthTooShort} Source: {read.Content.ToHexString(' ')}");
      content.AddRange(((IEnumerable<byte>) read.Content.SelectMiddle<byte>(6, splits[i])).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)));
      read = (OperateResult<byte[]>) null;
      check = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(content.ToArray());
  }

  /// <summary>
  /// 批量写入bool类型的数组，支持的类型为X,Y,M,S,T,C，具体的地址范围取决于PLC的类型，地址可以携带站号信息，例如 s=2;M100<br />
  /// Write arrays of type bool in batches. The supported types are X, Y, M, S, T, C. The specific address range depends on the type of PLC,
  /// The address can carry station number information, such as s=2;M100
  /// </summary>
  /// <param name="device">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="value">数据信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteDevice device,
    byte station,
    string address,
    bool[] value)
  {
    OperateResult<byte[]> operateResult1 = FatekProgramHelper.BuildWriteBoolCommand(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = device.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult operateResult3 = FatekProgramHelper.CheckResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    bool[] value)
  {
    OperateResult<byte[]> command = FatekProgramHelper.BuildWriteBoolCommand(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = FatekProgramHelper.CheckResponse(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult() : check;
  }

  /// <summary>使PLC处于RUN的状态</summary>
  /// <param name="device">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <returns>是否操作成功</returns>
  public static OperateResult Run(IReadWriteDevice device, byte station)
  {
    return device.ReadFromCoreServer(FatekProgramHelper.PackFatekCommand(station, "411")).Then(new Func<byte[], OperateResult>(FatekProgramHelper.CheckResponse));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Run(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public static async Task<OperateResult> RunAsync(IReadWriteDevice device, byte station)
  {
    OperateResult<byte[]> operateResult = await device.ReadFromCoreServerAsync(FatekProgramHelper.PackFatekCommand(station, "411"));
    return operateResult.Then(new Func<byte[], OperateResult>(FatekProgramHelper.CheckResponse));
  }

  /// <summary>使PLC处于STOP状态</summary>
  /// <param name="device">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <returns>是否操作成功</returns>
  public static OperateResult Stop(IReadWriteDevice device, byte station)
  {
    return device.ReadFromCoreServer(FatekProgramHelper.PackFatekCommand(station, "410")).Then(new Func<byte[], OperateResult>(FatekProgramHelper.CheckResponse));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Stop(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public static async Task<OperateResult> StopAsync(IReadWriteDevice device, byte station)
  {
    OperateResult<byte[]> operateResult = await device.ReadFromCoreServerAsync(FatekProgramHelper.PackFatekCommand(station, "410"));
    return operateResult.Then(new Func<byte[], OperateResult>(FatekProgramHelper.CheckResponse));
  }

  /// <summary>
  /// 读取当前PLC的状态信息，返回一个bool数组，同时包含了几种电量信息，分别为 0: RUN/STOP, 1: BAT LOW/正常, 2: Ladder checksum error/正常, 3: 使用ROM PACK/未使用,
  /// 4: WDT Timeout/正常, 5: 设定ID/未设ID， 6： 紧急停机/正常<br />
  /// Read the status information of the current PLC and return a bool array, which also contains several power information, 0: RUN/STOP, 1: BAT LOW/normal,
  /// 2: Ladder checksum error/normal, 3: Use ROM PACK/ Not used, 4: WDT Timeout/Normal, 5: ID set/ID not set, 6: Emergency stop/Normal
  /// </summary>
  /// <param name="device">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <returns>状态结果信息</returns>
  public static OperateResult<bool[]> ReadStatus(IReadWriteDevice device, byte station)
  {
    OperateResult<byte[]> result = device.ReadFromCoreServer(FatekProgramHelper.PackFatekCommand(station, "40"));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    OperateResult operateResult = FatekProgramHelper.CheckResponse(result.Content);
    return !operateResult.IsSuccess ? operateResult.ConvertFailed<bool[]>() : OperateResult.CreateSuccessResult<bool[]>(Encoding.ASCII.GetString(result.Content, 6, 2).ToHexBytes().ToBoolArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Stop(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public static async Task<OperateResult<bool[]>> ReadStatusAsync(
    IReadWriteDevice device,
    byte station)
  {
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(FatekProgramHelper.PackFatekCommand(station, "40"));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    OperateResult check = FatekProgramHelper.CheckResponse(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(Encoding.ASCII.GetString(read.Content, 6, 2).ToHexBytes().ToBoolArray()) : check.ConvertFailed<bool[]>();
  }
}
