﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronCpuUnitStatus
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>欧姆龙Cpu的状态信息</summary>
public class OmronCpuUnitStatus
{
  /// <summary>实例化一个默认的对象</summary>
  public OmronCpuUnitStatus()
  {
  }

  /// <summary>从原始的字节数组来实例化一个</summary>
  /// <param name="data">原始的字节数据</param>
  public OmronCpuUnitStatus(byte[] data)
  {
    this.Status = data[0].GetBoolByIndex(0) ? "Run" : "Stop";
    this.BatteryStatus = data[0].GetBoolByIndex(2) ? "Present" : "No";
    this.CpuStatus = data[0].GetBoolByIndex(7) ? "Standby" : "Normal";
    this.Mode = data[1] == (byte) 0 ? "PROGRAM" : (data[1] == (byte) 2 ? "MONITOR" : (data[1] == (byte) 4 ? "RUN" : ""));
    this.ErrorCode = (int) data[8] * 256 /*0x0100*/ + (int) data[9];
    if (this.ErrorCode <= 0)
      return;
    this.ErrorMessage = Encoding.ASCII.GetString(data, 10, 16 /*0x10*/).TrimEnd(' ', char.MinValue);
  }

  /// <summary>Run 或是 Stop</summary>
  public string Status { get; set; }

  /// <summary>No 或是 Present</summary>
  public string BatteryStatus { get; set; }

  /// <summary>Normal 或是 Standby</summary>
  public string CpuStatus { get; set; }

  /// <summary>PROGRAM, MONITOR, RUN</summary>
  public string Mode { get; set; }

  /// <summary>
  /// Among errors that occur when the command is executed, the error code indicates the most serious. If there are no errors, it will be 0000 (hex)
  /// </summary>
  public int ErrorCode { get; set; }

  /// <summary>
  /// Indicates messages from execution of FAL(006) or FALS(007). If there is no error message,
  /// or if FAL(006) or FALS(007) are not being executed, 16 spaces( ASCII 20) will be returned.
  /// </summary>
  public string ErrorMessage { get; set; }

  /// <inheritdoc />
  public override string ToString() => $"OmronCpuUnitStatus[{this.Status}]";
}
