﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Keyence.KeyenceSR2000Serial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using HslCommunication.Serial;
using System;

#nullable disable
namespace HslCommunication.Profinet.Keyence;

/// <inheritdoc cref="T:HslCommunication.Profinet.Keyence.KeyenceSR2000SeriesTcp" />
public class KeyenceSR2000Serial : SerialBase, IKeyenceSR2000Series
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000SeriesTcp.#ctor" />
  public KeyenceSR2000Serial()
  {
    this.ReceiveTimeOut = 10000;
    this.SleepTime = 20;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.ReadBarcode(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult<string> ReadBarcode()
  {
    return KeyenceSR2000Helper.ReadBarcode(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.Reset(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult Reset()
  {
    return KeyenceSR2000Helper.Reset(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.OpenIndicator(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult OpenIndicator()
  {
    return KeyenceSR2000Helper.OpenIndicator(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.CloseIndicator(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult CloseIndicator()
  {
    return KeyenceSR2000Helper.CloseIndicator(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.ReadVersion(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult<string> ReadVersion()
  {
    return KeyenceSR2000Helper.ReadVersion(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.ReadCommandState(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult<string> ReadCommandState()
  {
    return KeyenceSR2000Helper.ReadCommandState(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.ReadErrorState(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult<string> ReadErrorState()
  {
    return KeyenceSR2000Helper.ReadErrorState(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.CheckInput(System.Int32,System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult<bool> CheckInput(int number)
  {
    return KeyenceSR2000Helper.CheckInput(number, new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.SetOutput(System.Int32,System.Boolean,System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult SetOutput(int number, bool value)
  {
    return KeyenceSR2000Helper.SetOutput(number, value, new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.ReadRecord(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult<int[]> ReadRecord()
  {
    return KeyenceSR2000Helper.ReadRecord(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.Lock(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult Lock()
  {
    return KeyenceSR2000Helper.Lock(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.UnLock(System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult UnLock()
  {
    return KeyenceSR2000Helper.UnLock(new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceSR2000Helper.ReadCustomer(System.String,System.Func{System.Byte[],HslCommunication.OperateResult{System.Byte[]}})" />
  [HslMqttApi]
  public OperateResult<string> ReadCustomer(string command)
  {
    return KeyenceSR2000Helper.ReadCustomer(command, new Func<byte[], OperateResult<byte[]>>(((BinaryCommunication) this).ReadFromCoreServer));
  }

  /// <inheritdoc />
  public override string ToString() => $"KeyenceSR2000Serial[{this.PortName}:{this.BaudRate}]";
}
