﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Yamatake.DigitronCPL
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Yamatake.Helper;
using HslCommunication.Reflection;

#nullable disable
namespace HslCommunication.Profinet.Yamatake;

/// <summary>日本山武的数字指示调节器，目前适配SDC40B</summary>
public class DigitronCPL : DeviceSerialPort
{
  /// <summary>实例化一个默认的对象</summary>
  public DigitronCPL()
  {
    this.Station = (byte) 1;
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.LogMsgFormatBinary = false;
    this.ReceiveEmptyDataCount = 5;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13, (byte) 10);
  }

  /// <summary>获取或设置当前的站号信息</summary>
  public byte Station { get; set; }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult1 = DigitronCPLHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station), address, length);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? operateResult2 : DigitronCPLHelper.ExtraActualResponse(operateResult2.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = DigitronCPLHelper.BuildWriteCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station), address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) DigitronCPLHelper.ExtraActualResponse(operateResult2.Content);
  }

  /// <inheritdoc />
  public override string ToString() => $"DigitronCPL[{this.PortName}:{this.BaudRate}]";
}
