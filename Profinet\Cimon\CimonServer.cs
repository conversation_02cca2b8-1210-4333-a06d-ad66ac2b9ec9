﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Cimon.CimonServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.Cimon.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Cimon;

/// <summary>Cimon的服务器端实现</summary>
public class CimonServer : DeviceServer
{
  private const int OffsetX = 0;
  private const int OffsetY = 1000;
  private const int OffsetM = 2000;
  private const int OffsetK = 3000;
  private const int OffsetL = 4000;
  private const int OffsetC = 5000;
  private const int OffsetT = 6000;
  private const int OffsetFlag = 7000;
  private const int OffsetD = 0;
  private const int OffsetF = 40000;
  private const int OffsetZ = 41000;
  private const int OffsetS = 42000;
  private SoftBuffer bufferBit;
  private SoftBuffer bufferWord;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private byte frameNo = 1;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public CimonServer()
  {
    this.bufferBit = new SoftBuffer(131072 /*0x020000*/)
    {
      IsBoolReverseByWord = true
    };
    this.bufferWord = new SoftBuffer(65536 /*0x010000*/);
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new ReverseWordTransform();
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Cimon.CimonHmiProtocol.FrameNo" />
  public byte FrameNo
  {
    get => this.frameNo;
    set => this.frameNo = value;
  }

  private OperateResult<byte[]> CreateReadWordResult(
    SoftBuffer softBuffer,
    int offset,
    int address,
    int length)
  {
    return OperateResult.CreateSuccessResult<byte[]>(softBuffer.GetBytes(address * 2 + offset * 2, length * 2));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    try
    {
      int address1 = int.Parse(address.Substring(1));
      if (address[0] == 'X')
        return this.CreateReadWordResult(this.bufferBit, 0, address1, (int) length);
      if (address[0] == 'Y')
        return this.CreateReadWordResult(this.bufferBit, 1000, address1, (int) length);
      if (address[0] == 'M')
        return this.CreateReadWordResult(this.bufferBit, 2000, address1, (int) length);
      if (address[0] == 'K')
        return this.CreateReadWordResult(this.bufferBit, 3000, address1, (int) length);
      if (address[0] == 'L')
        return this.CreateReadWordResult(this.bufferBit, 4000, address1, (int) length);
      if (address[0] == 'C')
        return this.CreateReadWordResult(this.bufferBit, 5000, address1, (int) length);
      if (address[0] == 'T')
        return this.CreateReadWordResult(this.bufferBit, 6000, address1, (int) length);
      if (address[0] == 'D')
        return this.CreateReadWordResult(this.bufferWord, 0, address1, (int) length);
      if (address[0] == 'F')
        return this.CreateReadWordResult(this.bufferWord, 40000, address1, (int) length);
      if (address[0] == 'Z')
        return this.CreateReadWordResult(this.bufferWord, 41000, address1, (int) length);
      return address[0] == 'S' ? this.CreateReadWordResult(this.bufferWord, 42000, address1, (int) length) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  private OperateResult CreateWriteWordResult(
    SoftBuffer softBuffer,
    int offset,
    int address,
    byte[] value)
  {
    softBuffer.SetBytes(value, address * 2 + offset * 2);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    try
    {
      int address1 = int.Parse(address.Substring(1));
      if (address[0] == 'X')
        return this.CreateWriteWordResult(this.bufferBit, 0, address1, value);
      if (address[0] == 'Y')
        return this.CreateWriteWordResult(this.bufferBit, 1000, address1, value);
      if (address[0] == 'M')
        return this.CreateWriteWordResult(this.bufferBit, 2000, address1, value);
      if (address[0] == 'K')
        return this.CreateWriteWordResult(this.bufferBit, 3000, address1, value);
      if (address[0] == 'L')
        return this.CreateWriteWordResult(this.bufferBit, 4000, address1, value);
      if (address[0] == 'C')
        return this.CreateWriteWordResult(this.bufferBit, 5000, address1, value);
      if (address[0] == 'T')
        return this.CreateWriteWordResult(this.bufferBit, 2000, address1, value);
      if (address[0] == 'D')
        return this.CreateWriteWordResult(this.bufferWord, 0, address1, value);
      if (address[0] == 'F')
        return this.CreateWriteWordResult(this.bufferWord, 40000, address1, value);
      if (address[0] == 'Z')
        return this.CreateWriteWordResult(this.bufferWord, 41000, address1, value);
      return address[0] == 'S' ? this.CreateWriteWordResult(this.bufferWord, 42000, address1, value) : new OperateResult(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<byte[]>(ex.Message);
    }
  }

  private int GetBitIndex(ref string address)
  {
    if (address.IndexOf('.') < 0)
    {
      int int32 = Convert.ToInt32(address.Substring(address.Length - 1), 16 /*0x10*/);
      address = address.Substring(0, address.Length - 1);
      return int32;
    }
    int int32_1 = Convert.ToInt32(address.Substring(address.IndexOf('.') + 1), 16 /*0x10*/);
    address = address.Substring(0, address.IndexOf('.'));
    return int32_1;
  }

  private OperateResult<bool[]> CreateReadBitResult(
    SoftBuffer softBuffer,
    int offset,
    int bitIndex,
    int length)
  {
    return OperateResult.CreateSuccessResult<bool[]>(softBuffer.GetBool(bitIndex + offset * 16 /*0x10*/, length));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    int bitIndex1 = this.GetBitIndex(ref address);
    try
    {
      int bitIndex2 = int.Parse(address.Substring(1)) * 16 /*0x10*/ + bitIndex1;
      if (address[0] == 'X')
        return this.CreateReadBitResult(this.bufferBit, 0, bitIndex2, (int) length);
      if (address[0] == 'Y')
        return this.CreateReadBitResult(this.bufferBit, 1000, bitIndex2, (int) length);
      if (address[0] == 'M')
        return this.CreateReadBitResult(this.bufferBit, 2000, bitIndex2, (int) length);
      if (address[0] == 'K')
        return this.CreateReadBitResult(this.bufferBit, 3000, bitIndex2, (int) length);
      if (address[0] == 'L')
        return this.CreateReadBitResult(this.bufferBit, 4000, bitIndex2, (int) length);
      if (address[0] == 'T')
        return this.CreateReadBitResult(this.bufferBit, 6000, bitIndex2, (int) length);
      if (address[0] == 'C')
        return this.CreateReadBitResult(this.bufferBit, 5000, bitIndex2, (int) length);
      return address[0] == 'F' ? this.CreateReadBitResult(this.bufferBit, 7000, bitIndex2, (int) length) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>(ex.Message);
    }
  }

  private OperateResult CreateWriteBitResult(
    SoftBuffer softBuffer,
    int offset,
    int bitIndex,
    bool[] value)
  {
    softBuffer.SetBool(value, bitIndex + offset * 16 /*0x10*/);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    int bitIndex1 = this.GetBitIndex(ref address);
    try
    {
      int bitIndex2 = int.Parse(address.Substring(1)) * 16 /*0x10*/ + bitIndex1;
      if (address[0] == 'X')
        return this.CreateWriteBitResult(this.bufferBit, 0, bitIndex2, value);
      if (address[0] == 'Y')
        return this.CreateWriteBitResult(this.bufferBit, 1000, bitIndex2, value);
      if (address[0] == 'M')
        return this.CreateWriteBitResult(this.bufferBit, 2000, bitIndex2, value);
      if (address[0] == 'K')
        return this.CreateWriteBitResult(this.bufferBit, 3000, bitIndex2, value);
      if (address[0] == 'L')
        return this.CreateWriteBitResult(this.bufferBit, 4000, bitIndex2, value);
      if (address[0] == 'T')
        return this.CreateWriteBitResult(this.bufferBit, 6000, bitIndex2, value);
      if (address[0] == 'C')
        return this.CreateWriteBitResult(this.bufferBit, 5000, bitIndex2, value);
      return address[0] == 'F' ? this.CreateWriteBitResult(this.bufferBit, 7000, bitIndex2, value) : new OperateResult(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive[10] == (byte) 82 || receive[10] == (byte) 114)
      return OperateResult.CreateSuccessResult<byte[]>(this.ReadByCommand(receive));
    return receive[10] == (byte) 87 || receive[10] == (byte) 119 ? OperateResult.CreateSuccessResult<byte[]>(this.WriteByMessage(receive)) : OperateResult.CreateSuccessResult<byte[]>(CimonHelper.PackErrorResponse(this.frameNo, 14));
  }

  private byte[] ReadByCommand(byte[] command)
  {
    int length = (int) command[21] * 256 /*0x0100*/ + (int) command[22];
    string str1 = Encoding.ASCII.GetString(command, 13, 1);
    string str2 = Encoding.ASCII.GetString(command, 15, 6);
    if (command[10] == (byte) 82)
    {
      List<byte> byteList = new List<byte>();
      byteList.AddRange((IEnumerable<byte>) new byte[9]);
      OperateResult<byte[]> operateResult = this.Read(str1 + str2, (ushort) length);
      if (!operateResult.IsSuccess)
        return CimonHelper.PackErrorResponse(this.frameNo, 3);
      byteList.AddRange((IEnumerable<byte>) operateResult.Content);
      return CimonHelper.PackEntireCommand(true, this.frameNo, command[10], byteList.ToArray());
    }
    if (command[10] != (byte) 114)
      return CimonHelper.PackErrorResponse(this.frameNo, 14);
    List<byte> byteList1 = new List<byte>();
    byteList1.AddRange((IEnumerable<byte>) new byte[9]);
    OperateResult<bool[]> operateResult1 = this.ReadBool(str1 + str2, (ushort) length);
    if (operateResult1.IsSuccess)
      byteList1.AddRange((IEnumerable<byte>) ((IEnumerable<bool>) operateResult1.Content).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>());
    return CimonHelper.PackEntireCommand(true, this.frameNo, command[10], byteList1.ToArray());
  }

  private byte[] WriteByMessage(byte[] command)
  {
    if (!this.EnableWrite)
      return (byte[]) null;
    string str1 = Encoding.ASCII.GetString(command, 14, 1);
    string str2 = Encoding.ASCII.GetString(command, 16 /*0x10*/, 6);
    int length = (int) command[22] * 256 /*0x0100*/ + (int) command[23];
    byte[] numArray = this.ByteTransform.TransByte(command, 24, length);
    if (command[10] == (byte) 87)
      return this.Write(str1 + str2, numArray).IsSuccess ? CimonHelper.PackEntireCommand(true, this.frameNo, command[10], new byte[9]) : CimonHelper.PackErrorResponse(this.frameNo, 3);
    if (command[10] != (byte) 119)
      return CimonHelper.PackErrorResponse(this.frameNo, 14);
    bool[] flagArray = new bool[numArray.Length];
    for (int index = 0; index < numArray.Length; ++index)
      flagArray[index] = numArray[index] > (byte) 0;
    return this.Write(str1 + str2, flagArray).IsSuccess ? CimonHelper.PackEntireCommand(true, this.frameNo, command[10], new byte[9]) : CimonHelper.PackErrorResponse(this.frameNo, 3);
  }

  /// <inheritdoc />
  public override string ToString() => $"CimonServer[{this.Port}]";
}
