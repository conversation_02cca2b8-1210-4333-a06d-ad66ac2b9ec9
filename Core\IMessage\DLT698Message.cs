﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.DLT698Message
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Instrument.DLT.Helper;
using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>DLT698的协议消息文本</summary>
public class DLT698Message : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 8;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    return (int) BitConverter.ToUInt16(this.HeadBytes, 1) + 2 - 8;
  }

  /// <inheritdoc />
  public override int PependedUselesByteLength(byte[] headByte)
  {
    return DLT645Helper.FindHeadCode68H(headByte);
  }
}
