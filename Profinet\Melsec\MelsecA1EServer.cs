﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecA1EServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱MC-A1E协议的虚拟服务器，支持M,X,Y,D,W的数据池读写操作，支持二进制及ASCII格式进行读写操作，需要在实例化的时候指定。<br />
/// The Mitsubishi MC-A1E protocol virtual server supports M, X, Y, D, W data pool read and write operations,
/// and supports binary and ASCII format read and write operations, which need to be specified during instantiation.
/// </summary>
/// <remarks>
/// 本三菱的虚拟PLC仅限商业授权用户使用，感谢支持。
/// 如果你没有可以测试的三菱PLC，想要测试自己开发的上位机软件，或是想要在本机实现虚拟PLC，然后进行IO的输入输出练习，都可以使用本类来实现，地址参考DEMO程序
/// </remarks>
/// <summary>
/// 实例化一个默认参数的mc协议的服务器<br />
/// Instantiate a mc protocol server with default parameters
/// </summary>
/// <param name="isBinary">是否是二进制，默认是二进制，否则是ASCII格式</param>
public class MelsecA1EServer(bool isBinary = true) : MelsecMcServer(isBinary)
{
  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) null;

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return this.IsBinary ? OperateResult.CreateSuccessResult<byte[]>(this.ReadFromMcCore(receive)) : OperateResult.CreateSuccessResult<byte[]>(this.ReadFromMcAsciiCore(receive));
  }

  private byte[] PackResponseCommand(byte[] mcCore, byte err, byte code, byte[] data)
  {
    byte[] numArray = new byte[2]
    {
      (byte) ((uint) mcCore[0] + 128U /*0x80*/),
      err
    };
    if (err > (byte) 0)
    {
      if (err != (byte) 91)
        return numArray;
      return SoftBasic.SpliceArray<byte>(numArray, new byte[1]
      {
        code
      });
    }
    if (data == null)
      return numArray;
    return SoftBasic.SpliceArray<byte>(numArray, data);
  }

  private byte[] PackResponseCommand(byte[] mcCore, byte err, byte code, bool[] data)
  {
    byte[] numArray = new byte[2]
    {
      (byte) ((uint) mcCore[0] + 128U /*0x80*/),
      err
    };
    if (err > (byte) 0)
    {
      if (err != (byte) 91)
        return numArray;
      return SoftBasic.SpliceArray<byte>(numArray, new byte[1]
      {
        code
      });
    }
    if (data == null)
      return numArray;
    return SoftBasic.SpliceArray<byte>(numArray, MelsecHelper.TransBoolArrayToByteData(data));
  }

  private string GetAddressFromDataCode(ushort dataCode, int address)
  {
    if ((int) dataCode == (int) MelsecA1EDataType.M.DataCode)
      return "M" + address.ToString();
    if ((int) dataCode == (int) MelsecA1EDataType.X.DataCode)
      return "X" + address.ToString("X");
    if ((int) dataCode == (int) MelsecA1EDataType.Y.DataCode)
      return "Y" + address.ToString("X");
    if ((int) dataCode == (int) MelsecA1EDataType.S.DataCode)
      return "S" + address.ToString();
    if ((int) dataCode == (int) MelsecA1EDataType.F.DataCode)
      return "F" + address.ToString();
    if ((int) dataCode == (int) MelsecA1EDataType.B.DataCode)
      return "B" + address.ToString("X");
    if ((int) dataCode == (int) MelsecA1EDataType.D.DataCode)
      return "D" + address.ToString();
    if ((int) dataCode == (int) MelsecA1EDataType.R.DataCode)
      return "R" + address.ToString();
    return (int) dataCode == (int) MelsecA1EDataType.W.DataCode ? "W" + address.ToString("X") : string.Empty;
  }

  /// <inheritdoc />
  protected override byte[] ReadFromMcCore(byte[] mcCore)
  {
    try
    {
      int int32 = BitConverter.ToInt32(mcCore, 4);
      ushort uint16 = BitConverter.ToUInt16(mcCore, 8);
      ushort length = BitConverter.ToUInt16(mcCore, 10);
      string addressFromDataCode = this.GetAddressFromDataCode(uint16, int32);
      if (mcCore[0] == (byte) 0)
      {
        if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode)
        {
          if (length == (ushort) 0)
            length = (ushort) 256 /*0x0100*/;
          if (length > (ushort) 256 /*0x0100*/)
            return this.PackResponseCommand(mcCore, (byte) 16 /*0x10*/, (byte) 0, new bool[0]);
          OperateResult<bool[]> operateResult = this.ReadBool(addressFromDataCode, length);
          return !operateResult.IsSuccess ? this.PackResponseCommand(mcCore, (byte) 16 /*0x10*/, (byte) 0, new bool[0]) : this.PackResponseCommand(mcCore, (byte) 0, (byte) 0, operateResult.Content);
        }
      }
      else if (mcCore[0] == (byte) 1)
      {
        if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode || (int) uint16 == (int) MelsecA1EDataType.D.DataCode || (int) uint16 == (int) MelsecA1EDataType.R.DataCode || (int) uint16 == (int) MelsecA1EDataType.W.DataCode)
        {
          if (length > (ushort) 64 /*0x40*/)
            return this.PackResponseCommand(mcCore, (byte) 16 /*0x10*/, (byte) 0, new byte[0]);
          OperateResult<byte[]> operateResult = this.Read(addressFromDataCode, length);
          return !operateResult.IsSuccess ? this.PackResponseCommand(mcCore, (byte) 16 /*0x10*/, (byte) 0, new byte[0]) : this.PackResponseCommand(mcCore, (byte) 0, (byte) 0, operateResult.Content);
        }
      }
      else if (mcCore[0] == (byte) 2)
      {
        bool[] boolData = MelsecHelper.TransByteArrayToBoolData(mcCore, 12, (int) length);
        if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode)
          return !this.Write(addressFromDataCode, boolData).IsSuccess ? this.PackResponseCommand(mcCore, (byte) 16 /*0x10*/, (byte) 0, new byte[0]) : this.PackResponseCommand(mcCore, (byte) 0, (byte) 0, new byte[0]);
      }
      else if (mcCore[0] == (byte) 3)
      {
        byte[] numArray = mcCore.RemoveBegin<byte>(12);
        if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode || (int) uint16 == (int) MelsecA1EDataType.D.DataCode || (int) uint16 == (int) MelsecA1EDataType.R.DataCode || (int) uint16 == (int) MelsecA1EDataType.W.DataCode)
          return !this.Write(addressFromDataCode, numArray).IsSuccess ? this.PackResponseCommand(mcCore, (byte) 16 /*0x10*/, (byte) 0, new byte[0]) : this.PackResponseCommand(mcCore, (byte) 0, (byte) 0, new byte[0]);
      }
      return (byte[]) null;
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), ex);
      return (byte[]) null;
    }
  }

  private byte[] PackAsciiResponseCommand(byte[] mcCore, byte[] data)
  {
    byte[] numArray = new byte[4]
    {
      (byte) ((uint) mcCore[0] + 8U),
      mcCore[1],
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/
    };
    if (data == null)
      return numArray;
    return SoftBasic.SpliceArray<byte>(numArray, MelsecHelper.TransByteArrayToAsciiByteArray(data));
  }

  private byte[] PackAsciiResponseCommand(byte[] mcCore, bool[] data)
  {
    byte[] numArray = new byte[4]
    {
      (byte) ((uint) mcCore[0] + 8U),
      mcCore[1],
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/
    };
    if (data == null)
      return numArray;
    if (data.Length % 2 == 1)
      data = SoftBasic.ArrayExpandToLength<bool>(data, data.Length + 1);
    return SoftBasic.SpliceArray<byte>(numArray, ((IEnumerable<bool>) data).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
  }

  /// <inheritdoc />
  protected override byte[] ReadFromMcAsciiCore(byte[] mcCore)
  {
    try
    {
      byte num = Convert.ToByte(Encoding.ASCII.GetString(mcCore, 0, 2), 16 /*0x10*/);
      int int32 = Convert.ToInt32(Encoding.ASCII.GetString(mcCore, 12, 8), 16 /*0x10*/);
      ushort uint16 = Convert.ToUInt16(Encoding.ASCII.GetString(mcCore, 8, 4), 16 /*0x10*/);
      ushort length = Convert.ToUInt16(Encoding.ASCII.GetString(mcCore, 20, 2), 16 /*0x10*/);
      string addressFromDataCode = this.GetAddressFromDataCode(uint16, int32);
      switch (num)
      {
        case 0:
          if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode)
          {
            if (length == (ushort) 0)
              length = (ushort) 256 /*0x0100*/;
            return this.PackAsciiResponseCommand(mcCore, this.ReadBool(addressFromDataCode, length).Content);
          }
          break;
        case 1:
          if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode || (int) uint16 == (int) MelsecA1EDataType.D.DataCode || (int) uint16 == (int) MelsecA1EDataType.R.DataCode || (int) uint16 == (int) MelsecA1EDataType.W.DataCode)
            return this.PackAsciiResponseCommand(mcCore, this.Read(addressFromDataCode, length).Content);
          break;
        case 2:
          bool[] array = ((IEnumerable<byte>) mcCore.SelectMiddle<byte>(24, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)).ToArray<bool>();
          if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode)
          {
            this.Write(addressFromDataCode, array);
            return this.PackAsciiResponseCommand(mcCore, new byte[0]);
          }
          break;
        case 3:
          byte[] byteArray = MelsecHelper.TransAsciiByteArrayToByteArray(mcCore.RemoveBegin<byte>(24));
          if ((int) uint16 == (int) MelsecA1EDataType.M.DataCode || (int) uint16 == (int) MelsecA1EDataType.X.DataCode || (int) uint16 == (int) MelsecA1EDataType.Y.DataCode || (int) uint16 == (int) MelsecA1EDataType.S.DataCode || (int) uint16 == (int) MelsecA1EDataType.F.DataCode || (int) uint16 == (int) MelsecA1EDataType.B.DataCode || (int) uint16 == (int) MelsecA1EDataType.D.DataCode || (int) uint16 == (int) MelsecA1EDataType.R.DataCode || (int) uint16 == (int) MelsecA1EDataType.W.DataCode)
          {
            this.Write(addressFromDataCode, byteArray);
            return this.PackAsciiResponseCommand(mcCore, new byte[0]);
          }
          break;
      }
      return (byte[]) null;
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), ex);
      return (byte[]) null;
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecA1EServer[{this.Port}]";
}
