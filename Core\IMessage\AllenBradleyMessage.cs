﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.AllenBradleyMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>用于和 AllenBradley PLC 交互的消息协议类</summary>
public class AllenBradleyMessage : NetMessageBase, INetMessage
{
  private bool contextCheck = false;

  /// <summary>
  /// 实例化一个默认的 <see cref="T:HslCommunication.Core.IMessage.AllenBradleyMessage" /> 对象
  /// </summary>
  public AllenBradleyMessage()
  {
  }

  /// <summary>
  /// Initializes a new instance of the <see cref="T:HslCommunication.Core.IMessage.AllenBradleyMessage" /> class.
  /// </summary>
  /// <param name="contextCheck">A value indicating whether the message should perform a context check. If <see langword="true" />, the message will
  /// validate its context before processing.</param>
  public AllenBradleyMessage(bool contextCheck) => this.contextCheck = contextCheck;

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 24;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => (int) BitConverter.ToUInt16(this.HeadBytes, 2);

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    if (receive == null || receive.Length < 24 || send == null || send.Length < 24)
      return 1;
    if (this.contextCheck)
    {
      for (int index = 12; index < 20; ++index)
      {
        if ((int) receive[index] != (int) send[index])
          return -1;
      }
    }
    return base.CheckMessageMatch(send, receive);
  }
}
