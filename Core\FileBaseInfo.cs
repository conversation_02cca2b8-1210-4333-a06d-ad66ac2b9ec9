﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.FileBaseInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>文件的基础信息</summary>
public class FileBaseInfo
{
  /// <summary>文件名称</summary>
  public string Name { get; set; }

  /// <summary>文件大小</summary>
  public long Size { get; set; }

  /// <summary>文件的标识，注释</summary>
  public string Tag { get; set; }

  /// <summary>文件上传人的名称</summary>
  public string Upload { get; set; }
}
