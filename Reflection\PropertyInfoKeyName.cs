﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.PropertyInfoKeyName
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Reflection;

#nullable disable
namespace HslCommunication.Reflection;

internal class PropertyInfoKeyName
{
  public PropertyInfoKeyName(PropertyInfo property, string key)
  {
    this.PropertyInfo = property;
    this.KeyName = key;
  }

  public PropertyInfoKeyName(PropertyInfo property, string key, string value)
  {
    this.PropertyInfo = property;
    this.KeyName = key;
    this.Value = value;
  }

  public PropertyInfo PropertyInfo { get; set; }

  public string KeyName { get; set; }

  public string Value { get; set; }
}
