﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT645Server
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Instrument.DLT.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>DLT645协议的虚拟服务器</summary>
public class DLT645Server : DeviceServer
{
  /// <summary>服务器的所有数据信息</summary>
  protected Dictionary<string, DLT645Server.DLTAddress> allDatas = new Dictionary<string, DLT645Server.DLTAddress>();
  private string station = "1";

  /// <summary>实例化一个默认的对象</summary>
  public DLT645Server() => this.CreateAddressTags();

  /// <summary>
  /// 创建一系列的地址标签<br />
  /// </summary>
  protected virtual void CreateAddressTags()
  {
    this.AddDltTag("02010100", "2311", 1);
    this.AddDltTag("02010200", "2312", 1);
    this.AddDltTag("02010300", "2313", 1);
    this.AddDltTag("02020100", "012345", 3);
    this.AddDltTag("02020200", "012346", 3);
    this.AddDltTag("02020300", "012347", 3);
    this.AddDltTag("02030000", "123456", 4);
    this.AddDltTag("02030100", "123457", 4);
    this.AddDltTag("02030200", "123458", 4);
    this.AddDltTag("02030300", "123459", 4);
    this.AddDltTag("02040000", "654321", 4);
    this.AddDltTag("02040100", "654322", 4);
    this.AddDltTag("02040200", "654323", 4);
    this.AddDltTag("02040300", "654324", 4);
    this.AddDltTag("02050000", "553322", 4);
    this.AddDltTag("02050100", "553323", 4);
    this.AddDltTag("02050200", "553324", 4);
    this.AddDltTag("02050300", "553325", 4);
    this.AddDltTag("02060000", "1236", 3);
    this.AddDltTag("02060100", "1237", 3);
    this.AddDltTag("02060200", "1238", 3);
    this.AddDltTag("02060300", "1239", 3);
    this.AddDltTag("02070100", "2345", 1);
    this.AddDltTag("02070200", "2346", 1);
    this.AddDltTag("02070300", "2347", 1);
    this.AddDltTag("02080001", "123789", 3);
    this.AddDltTag("02080002", "5012", 2);
    this.AddDltTag("02080003", "112233", 4);
    this.AddDltTag("02080004", "223344", 4);
    this.AddDltTag("02080005", "223355", 4);
    this.AddDltTag("02080006", "223366", 4);
    this.AddDltTag("02080007", "2312", 1);
    this.AddDltTag("02080008", "1221", 2);
    this.AddDltTag("02080009", "1223", 2);
    this.AddDltTag("0208000A", "11223344", 0);
    this.AddDltTag("00000000", "12345612", 2);
    this.AddDltTag("00010000", "12345613", 2);
    this.AddDltTag("00020000", "12345614", 2);
    this.AddDltTag("00030000", "12345615", 2);
    this.AddDltTag("04000101", "23110904", -1);
    this.AddDltTag("04000102", DateTime.Now.ToString("HH:mm:ss").Replace(":", ""), -1);
    this.AddDltTag("04000401", "112233665544", -1);
    this.AddDltTag("04000402", "552233665544", -1);
    this.AddDltAsciiTag("0400040B", "DLTServer ", -1);
    this.AddDltAsciiTag("0400040C", "2023111101 ", -1);
  }

  /// <summary>
  /// 新增一个DLT的Tag标签<br />
  /// </summary>
  /// <param name="address">地址名称</param>
  /// <param name="hex">数据内容</param>
  /// <param name="digtal"></param>
  protected void AddDltTag(string address, string hex, int digtal)
  {
    this.allDatas.Add(address, new DLT645Server.DLTAddress(address, hex.ToHexBytes(), digtal));
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645Server.AddDltTag(System.String,System.String,System.Int32)" />
  protected void AddDltTag(string address, byte[] data, int digtal)
  {
    this.allDatas.Add(address, new DLT645Server.DLTAddress(address, data, digtal));
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645Server.AddDltTag(System.String,System.String,System.Int32)" />
  protected void AddDltAsciiTag(string address, string ascii, int digtal)
  {
    this.allDatas.Add(address, new DLT645Server.DLTAddress(address, Encoding.ASCII.GetBytes(ascii), digtal));
  }

  /// <summary>获取当前的DLT645协议类型</summary>
  /// <returns>DLT类型信息</returns>
  protected virtual DLT645Type GetLT645Type() => DLT645Type.DLT2007;

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.DLT645.Station" />
  public virtual string Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <summary>
  /// 字符串的数据是否发生反转，默认为 <c>True</c>, 将根据高地位进行翻转操作<br />
  /// Whether the data of the string is reversed or not, the default is <c>True</c>, and the flip operation will be performed according to the high status
  /// </summary>
  public bool StringReverse { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.EnableCodeFE" />
  public bool EnableCodeFE { get; set; }

  /// <summary>
  /// 是否对客户端及主站的站号进行强制校验，只有两者站号匹配才进行返回数据，默认为false，对站号信息不校验操作<br />
  /// Whether to forcibly verify the station ids of the client and the master station. The data is returned only when the station ids of the client and master station match. The default value is false
  /// </summary>
  public bool StationMatch { get; set; } = false;

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDouble(System.String,System.UInt16)" />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    OperateResult<string[]> result = this.ReadStringArray(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<double[]>((OperateResult) result);
    try
    {
      return OperateResult.CreateSuccessResult<double[]>(((IEnumerable<string>) result.Content).Take<string>((int) length).Select<string, double>((Func<string, double>) (m => double.Parse(m))).ToArray<double>());
    }
    catch (Exception ex)
    {
      return new OperateResult<double[]>($"double.Parse failed: {ex.Message}{Environment.NewLine}Source: {result.Content.ToArrayString<string>()}");
    }
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromArray<string>(this.ReadStringArray(address));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double)" />
  [HslMqttApi("WriteDouble", "")]
  public override OperateResult Write(string address, double value)
  {
    OperateResult<string, byte[]> result = DLT645Helper.AnalysisBytesAddress(this.GetLT645Type(), address, this.Station);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string[]>((OperateResult) result);
    string hexString = ((IEnumerable<byte>) result.Content2).Reverse<byte>().ToArray<byte>().ToHexString();
    if (!this.allDatas.ContainsKey(hexString))
      return (OperateResult) new OperateResult<string[]>("None address data");
    value *= Math.Pow(10.0, (double) this.allDatas[hexString].Digtal);
    string str = Convert.ToInt32(value).ToString().PadLeft(this.allDatas[hexString].Buffer.Length * 2, '0');
    if (str.Length > this.allDatas[hexString].Buffer.Length * 2)
      str = str.Substring(str.Length - this.allDatas[hexString].Buffer.Length * 2);
    str.ToHexBytes().CopyTo((Array) this.allDatas[hexString].Buffer, 0);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String,System.Text.Encoding)" />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    OperateResult<string, byte[]> result = DLT645Helper.AnalysisBytesAddress(this.GetLT645Type(), address, this.Station);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string[]>((OperateResult) result);
    string hexString = ((IEnumerable<byte>) result.Content2).Reverse<byte>().ToArray<byte>().ToHexString();
    if (!this.allDatas.ContainsKey(hexString))
      return (OperateResult) new OperateResult<string[]>("None address data");
    byte[] numArray = value.PadLeft(this.allDatas[hexString].Buffer.Length * 2, '0').ToHexBytes();
    if (numArray.Length > this.allDatas[hexString].Buffer.Length)
      numArray = numArray.SelectBegin<byte>(this.allDatas[hexString].Buffer.Length);
    numArray.CopyTo((Array) this.allDatas[hexString].Buffer, 0);
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 根据指定的地址，读取DLT设备的字符串格式的数据信息<br />
  /// Read the DLT device data in string format based on the specified address
  /// </summary>
  /// <param name="address">地址信息</param>
  /// <returns>包含字符串结果数组的对象</returns>
  public virtual OperateResult<string[]> ReadStringArray(string address)
  {
    bool booleanParameter = HslHelper.ExtractBooleanParameter(ref address, "reverse", true);
    OperateResult<string, byte[]> result = DLT645Helper.AnalysisBytesAddress(this.GetLT645Type(), address, this.Station);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result);
    string hexString = ((IEnumerable<byte>) result.Content2).Reverse<byte>().ToArray<byte>().ToHexString();
    return this.allDatas.ContainsKey(hexString) ? DLTTransform.TransStringsFromDLt(this.GetLT645Type(), ((IEnumerable<byte>) this.allDatas[hexString].Buffer).Reverse<byte>().ToArray<byte>().EveryByteAdd(51), result.Content2, booleanParameter) : new OperateResult<string[]>("None address data");
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new DLT645Message();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length == 4 && receive[0] == (byte) 254 && receive[1] == (byte) 254 && receive[2] == (byte) 254 && receive[3] == (byte) 254)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (receive.Length < 5)
      return new OperateResult<byte[]>("Uknown message");
    int headCode68H = DLT645Helper.FindHeadCode68H(receive);
    if (headCode68H < 0)
      return new OperateResult<byte[]>("Uknown message: " + receive.ToHexString(' '));
    if (headCode68H > 0)
      receive = receive.RemoveBegin<byte>(headCode68H);
    return this.ReadFromDLTCore(receive);
  }

  private OperateResult CheckStationMatch(byte[] command)
  {
    if (this.CheckAddress(command, (byte) 170) || !this.StationMatch)
      return OperateResult.CreateSuccessResult();
    string hexString = command.SelectMiddle<byte>(1, 6).ReverseNew<byte>().ToHexString();
    try
    {
      if (Convert.ToInt32(hexString) == Convert.ToInt32(this.station))
        return OperateResult.CreateSuccessResult();
      return new OperateResult($"Station not match, need[{this.station}] actual[{hexString}]");
    }
    catch
    {
      return OperateResult.CreateSuccessResult();
    }
  }

  private bool CheckAddress(byte[] command, byte match)
  {
    for (int index = 1; index < 7; ++index)
    {
      if ((int) command[index] != (int) match)
        return false;
    }
    return true;
  }

  private OperateResult<byte[]> CreateResponseBack(
    byte err,
    byte[] command,
    byte[] data,
    bool withAddress)
  {
    if (err == (byte) 0 & withAddress)
      data = SoftBasic.SpliceArray<byte>(command.SelectMiddle<byte>(10, this.GetDataIdLength()).EveryByteAdd(-51), data);
    string address = this.station;
    if (!this.CheckAddress(command, (byte) 170))
      address = command.SelectMiddle<byte>(1, 6).ReverseNew<byte>().ToHexString();
    byte[] numArray;
    if (err > (byte) 0)
      numArray = DLT645Helper.BuildDlt645EntireCommand(address, (byte) ((uint) command[8] + 192U /*0xC0*/), new byte[1]
      {
        err
      }).Content;
    else
      numArray = DLT645Helper.BuildDlt645EntireCommand(address, (byte) ((uint) command[8] + 128U /*0x80*/), data).Content;
    if (this.EnableCodeFE)
      numArray = SoftBasic.SpliceArray<byte>(new byte[4]
      {
        (byte) 254,
        (byte) 254,
        (byte) 254,
        (byte) 254
      }, numArray);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  private bool IsCommandRead(byte command)
  {
    return this.GetLT645Type() == DLT645Type.DLT2007 ? command == (byte) 17 : command == (byte) 1;
  }

  private bool IsCommandWrite(byte command)
  {
    return this.GetLT645Type() == DLT645Type.DLT2007 ? command == (byte) 20 : command == (byte) 4;
  }

  private int GetDataIdLength() => this.GetLT645Type() == DLT645Type.DLT2007 ? 4 : 2;

  /// <summary>
  /// 从DLT协议中读取数据信息<br />
  /// </summary>
  /// <param name="receive">接收到的报文数据信息</param>
  /// <returns>返回的报文结果数据</returns>
  protected virtual OperateResult<byte[]> ReadFromDLTCore(byte[] receive)
  {
    int dataIdLength = this.GetDataIdLength();
    if (this.IsCommandRead(receive[8]))
    {
      OperateResult result = this.CheckStationMatch(receive);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result);
      string hexString = ((IEnumerable<byte>) receive.SelectMiddle<byte>(10, dataIdLength).EveryByteAdd(-51)).Reverse<byte>().ToArray<byte>().ToHexString();
      if (!this.allDatas.ContainsKey(hexString))
        return this.CreateResponseBack((byte) 2, receive, (byte[]) null, true);
      return this.allDatas[hexString].Digtal < 0 && !this.StringReverse ? this.CreateResponseBack((byte) 0, receive, ((IEnumerable<byte>) this.allDatas[hexString].Buffer).Reverse<byte>().ToArray<byte>(), true) : this.CreateResponseBack((byte) 0, receive, ((IEnumerable<byte>) this.allDatas[hexString].Buffer).Reverse<byte>().ToArray<byte>(), true);
    }
    if (receive[8] == (byte) 19)
      return this.CreateResponseBack((byte) 0, receive, this.station.PadLeft(12, '0').ToHexBytes(), false);
    if (this.IsCommandWrite(receive[8]))
    {
      OperateResult result = this.CheckStationMatch(receive);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result);
      string hexString = ((IEnumerable<byte>) receive.SelectMiddle<byte>(10, dataIdLength).EveryByteAdd(-51)).Reverse<byte>().ToArray<byte>().ToHexString();
      if (!this.allDatas.ContainsKey(hexString))
        return this.CreateResponseBack((byte) 2, receive, (byte[]) null, false);
      (this.GetLT645Type() == DLT645Type.DLT2007 ? (IEnumerable<byte>) receive.SelectMiddle<byte>(22, receive.Length - 24) : (IEnumerable<byte>) receive.SelectMiddle<byte>(12, receive.Length - 14)).Reverse<byte>().ToArray<byte>().EveryByteAdd(-51).CopyTo((Array) this.allDatas[hexString].Buffer, 0);
      return this.CreateResponseBack((byte) 0, receive, new byte[0], false);
    }
    if (receive[8] == (byte) 21)
    {
      OperateResult result = this.CheckStationMatch(receive);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result);
      string hexString = receive.SelectMiddle<byte>(10, 6).EveryByteAdd(-51).ReverseNew<byte>().ToHexString();
      this.LogNet?.WriteInfo(this.ToString(), $"Station change, {this.station} -> {hexString}");
      this.station = hexString;
      return this.CreateResponseBack((byte) 0, receive, new byte[0], false);
    }
    if (receive[8] == (byte) 8)
    {
      this.Write("04000101", ((IEnumerable<byte>) receive.SelectMiddle<byte>(13, 3).EveryByteAdd(-51)).Reverse<byte>().ToArray<byte>().ToHexString() + "00");
      this.Write("04000102", ((IEnumerable<byte>) receive.SelectMiddle<byte>(10, 3).EveryByteAdd(-51)).Reverse<byte>().ToArray<byte>().ToHexString());
      OperateResult<byte[]> operateResult = new OperateResult<byte[]>();
      operateResult.ErrorCode = int.MinValue;
      return operateResult;
    }
    if (receive[8] == (byte) 22)
    {
      this.LogNet?.WriteDebug(this.ToString(), "FreezeCommand: " + receive.SelectMiddle<byte>(10, 4).ToHexString(' '));
      if (receive[1] != (byte) 153 || receive[2] != (byte) 153 || receive[3] != (byte) 153 || receive[4] != (byte) 153 || receive[5] != (byte) 153 || receive[6] != (byte) 153)
        return this.CreateResponseBack((byte) 0, receive, new byte[0], false);
    }
    else if (receive[8] == (byte) 28)
      return this.CreateResponseBack((byte) 0, receive, new byte[0], false);
    return this.CreateResponseBack((byte) 2, receive, (byte[]) null, false);
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    MemoryStream ms = new MemoryStream();
    ms.Write(buffer.SelectBegin<byte>(receivedLength));
    return DLT645Helper.CheckReceiveDataComplete(ms);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (!disposing)
      ;
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"DLT645Server[{this.Port}]";

  /// <summary>DLT的地址信息</summary>
  protected class DLTAddress
  {
    /// <summary>实例化一个默认的对象</summary>
    public DLTAddress()
    {
    }

    /// <summary>指定地址，数据，小数位实例化对象</summary>
    /// <param name="address">地址</param>
    /// <param name="buffer">数据</param>
    /// <param name="digtal">小数位</param>
    public DLTAddress(string address, byte[] buffer, int digtal)
    {
      this.Address = address;
      this.Buffer = buffer;
      this.Digtal = digtal;
    }

    /// <summary>地址</summary>
    public string Address { get; set; }

    /// <summary>原始字节的数据</summary>
    public byte[] Buffer { get; set; }

    /// <summary>小数位，小于0则表示字符串</summary>
    public int Digtal { get; set; }
  }
}
