﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.FATEK.FatekProgramOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.FATEK.Helper;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.FATEK;

/// <summary>
/// 台湾永宏公司的编程口协议，此处是基于tcp的实现，地址信息请查阅api文档信息，地址可以携带站号信息，例如 s=2;D100<br />
/// The programming port protocol of Taiwan Yonghong company, here is the implementation based on TCP,
/// please refer to the API information for the address information, The address can carry station number information, such as s=2;D100
/// </summary>
/// <remarks>
/// 支持位访问：M,X,Y,S,T(触点),C(触点)，字访问：RT(当前值),RC(当前值)，D，R；具体参照API文档
/// </remarks>
public class FatekProgramOverTcp : DeviceTcpNet, IFatekProgram, IReadWriteNet
{
  private byte station = 1;

  /// <summary>
  /// 实例化默认的构造方法<br />
  /// Instantiate the default constructor
  /// </summary>
  public FatekProgramOverTcp()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.LogMsgFormatBinary = false;
  }

  /// <summary>
  /// 使用指定的ip地址和端口来实例化一个对象<br />
  /// Instantiate an object with the specified IP address and port
  /// </summary>
  /// <param name="ipAddress">设备的Ip地址</param>
  /// <param name="port">设备的端口号</param>
  public FatekProgramOverTcp(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 3);
  }

  /// <summary>
  /// PLC的站号信息，需要和实际的设置值一致，默认为1<br />
  /// The station number information of the PLC needs to be consistent with the actual setting value. The default is 1.
  /// </summary>
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return FatekProgramHelper.Read((IReadWriteDevice) this, this.station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return FatekProgramHelper.Write((IReadWriteDevice) this, this.station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await FatekProgramHelper.ReadAsync((IReadWriteDevice) this, this.station, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await FatekProgramHelper.WriteAsync((IReadWriteDevice) this, this.station, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return FatekProgramHelper.ReadBool((IReadWriteDevice) this, this.station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return FatekProgramHelper.Write((IReadWriteDevice) this, this.station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await FatekProgramHelper.ReadBoolAsync((IReadWriteDevice) this, this.station, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult operateResult = await FatekProgramHelper.WriteAsync((IReadWriteDevice) this, this.station, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Run(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult Run(byte station)
  {
    return FatekProgramHelper.Run((IReadWriteDevice) this, station);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.Run(System.Byte)" />
  [HslMqttApi("Run", "使PLC处于RUN状态")]
  public OperateResult Run() => this.Run(this.Station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Stop(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult Stop(byte station)
  {
    return FatekProgramHelper.Stop((IReadWriteDevice) this, station);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.Stop(System.Byte)" />
  [HslMqttApi("Stop", "使PLC处于STOP状态")]
  public OperateResult Stop() => this.Stop(this.Station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.ReadStatus(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult<bool[]> ReadStatus(byte station)
  {
    return FatekProgramHelper.ReadStatus((IReadWriteDevice) this, station);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.ReadStatus(System.Byte)" />
  [HslMqttApi("ReadStatus", "读取PLC基本的状态信息")]
  public OperateResult<bool[]> ReadStatus() => this.ReadStatus(this.Station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Run(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public async Task<OperateResult> RunAsync(byte station)
  {
    OperateResult operateResult = await FatekProgramHelper.RunAsync((IReadWriteDevice) this, station);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.RunAsync(System.Byte)" />
  public async Task<OperateResult> RunAsync()
  {
    OperateResult operateResult = await this.RunAsync(this.Station);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Stop(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public async Task<OperateResult> StopAsync(byte station)
  {
    OperateResult operateResult = await FatekProgramHelper.StopAsync((IReadWriteDevice) this, station);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.StopAsync(System.Byte)" />
  public async Task<OperateResult> StopAsync()
  {
    OperateResult operateResult = await this.StopAsync(this.Station);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.ReadStatus(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public async Task<OperateResult<bool[]>> ReadStatusAsync(byte station)
  {
    OperateResult<bool[]> operateResult = await FatekProgramHelper.ReadStatusAsync((IReadWriteDevice) this, station);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.ReadStatus(System.Byte)" />
  public async Task<OperateResult<bool[]>> ReadStatusAsync()
  {
    OperateResult<bool[]> operateResult = await this.ReadStatusAsync(this.Station);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"FatekProgramOverTcp[{this.IpAddress}:{this.Port}]";
}
