﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.MelsecA1EBinaryMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>三菱的A兼容1E帧协议解析规则</summary>
public class MelsecA1EBinaryMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 2;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    if (this.HeadBytes[1] == (byte) 91)
      return 2;
    if (this.HeadBytes[1] != (byte) 0)
      return 0;
    switch (this.HeadBytes[0])
    {
      case 128 /*0x80*/:
        return this.SendBytes[10] != (byte) 0 ? ((int) this.SendBytes[10] + 1) / 2 : 128 /*0x80*/;
      case 129:
        return (int) this.SendBytes[10] * 2;
      case 130:
      case 131:
        return 0;
      default:
        return 0;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes != null && (int) this.HeadBytes[0] - (int) this.SendBytes[0] == 128 /*0x80*/;
  }
}
