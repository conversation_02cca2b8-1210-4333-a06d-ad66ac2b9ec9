﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Keyence.KeyenceKvOld
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Keyence;

/// <summary>
/// 老型号的基恩士类，适用于 Kv-10xx, 16xx, 24xx, 40xx, kv-80, kv-300，其中 xx 表示 AR/AT/DR/DT<br />
/// Old Keenes class, suitable for Kv-10xx, 16xx, 24xx, 40xx, kv-80, kv-300, xx means AR/AT/DR/DT<br />
/// </summary>
public class KeyenceKvOld : DeviceTcpNet
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public KeyenceKvOld()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.ByteTransform.IsStringReverseByteWord = true;
    this.LogMsgFormatBinary = false;
  }

  /// <summary>
  /// 使用指定的ip地址和端口号来初始化对象<br />
  /// Initialize the object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">Ip地址数据</param>
  /// <param name="port">端口号</param>
  public KeyenceKvOld(string ipAddress, int port = 8501)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13, (byte) 10);
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.CommunicationPipe, KeyenceNanoHelper.GetConnectCmd((byte) 0, false), true, true);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult ExtraOnDisconnect()
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.CommunicationPipe, KeyenceNanoHelper.GetDisConnectCmd((byte) 0, false), true, true);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    OperateResult<byte[]> result = await this.ReadFromCoreServerAsync(this.CommunicationPipe, KeyenceNanoHelper.GetConnectCmd((byte) 0, false), true, true).ConfigureAwait(false);
    OperateResult operateResult = result.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) result;
    result = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> ExtraOnDisconnectAsync()
  {
    OperateResult<byte[]> result = await this.ReadFromCoreServerAsync(this.CommunicationPipe, KeyenceNanoHelper.GetDisConnectCmd((byte) 0, false), true, true).ConfigureAwait(false);
    OperateResult operateResult = result.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) result;
    result = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult1 = KeyenceKvOld.BuildReadWordCommand(address, false);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? operateResult1 : KeyenceKvOld.ExtraResponseContent(address, operateResult2.Content, true);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = KeyenceKvOld.BuildWriteWordCommand(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult1 : (OperateResult) KeyenceKvOld.ExtraResponseContent(address, operateResult2.Content, false);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceKvOld.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> build = KeyenceKvOld.BuildReadWordCommand(address, false);
    if (!build.IsSuccess)
      return build;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return read.IsSuccess ? KeyenceKvOld.ExtraResponseContent(address, read.Content, true) : build;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceKvOld.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> build = KeyenceKvOld.BuildWriteWordCommand(address, value);
    if (!build.IsSuccess)
      return (OperateResult) build;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return read.IsSuccess ? (OperateResult) KeyenceKvOld.ExtraResponseContent(address, read.Content, false) : (OperateResult) build;
  }

  private static bool CheckBoolOnWordAddress(string address)
  {
    return Regex.IsMatch(address, "^(DM|TM)[0-9]+\\.[0-9]+$", RegexOptions.IgnoreCase);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    if (KeyenceKvOld.CheckBoolOnWordAddress(address))
      return ByteTransformHelper.GetResultFromArray<bool>(HslHelper.ReadBool((IReadWriteNet) this, address, (ushort) 1));
    OperateResult<byte[]> result1 = KeyenceKvOld.BuildReadWordCommand(address, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) result2) : KeyenceKvOld.ExtraBoolContent(address, result2.Content, true);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    if (KeyenceKvOld.CheckBoolOnWordAddress(address))
      return HslHelper.WriteBool((IReadWriteNet) this, address, new bool[1]
      {
        value
      });
    OperateResult<byte[]> operateResult1 = KeyenceKvOld.BuildWriteBoolCommand(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) KeyenceKvOld.ExtraBoolContent(address, operateResult2.Content, false);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceKvOld.ReadBool(System.String)" />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    if (KeyenceKvOld.CheckBoolOnWordAddress(address))
    {
      OperateResult<bool[]> result = await HslHelper.ReadBoolAsync((IReadWriteNet) this, address, (ushort) 1).ConfigureAwait(false);
      return ByteTransformHelper.GetResultFromArray<bool>(result);
    }
    OperateResult<byte[]> build = KeyenceKvOld.BuildReadWordCommand(address, true);
    if (!build.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content);
    return read.IsSuccess ? KeyenceKvOld.ExtraBoolContent(address, read.Content, true) : OperateResult.CreateFailedResult<bool>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceKvOld.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    if (KeyenceKvOld.CheckBoolOnWordAddress(address))
    {
      OperateResult operateResult = await HslHelper.WriteBoolAsync((IReadWriteNet) this, address, new bool[1]
      {
        value
      }).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<byte[]> build = KeyenceKvOld.BuildWriteBoolCommand(address, value);
    if (!build.IsSuccess)
      return (OperateResult) build;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content);
    return read.IsSuccess ? (OperateResult) KeyenceKvOld.ExtraBoolContent(address, read.Content, false) : (OperateResult) read;
  }

  /// <inheritdoc />
  public override string ToString() => $"KeyenceKvOld<{this.CommunicationPipe}>";

  private static OperateResult<string> AnalysisAddress(string address, bool isBit)
  {
    if (isBit)
    {
      if (address.StartsWithAndNumber("CTH"))
        return OperateResult.CreateSuccessResult<string>(address);
      if (address.StartsWithAndNumber("CTC"))
        return OperateResult.CreateSuccessResult<string>(address);
      if (address.StartsWithAndNumber("C"))
        return OperateResult.CreateSuccessResult<string>(address);
      if (address.StartsWithAndNumber("T"))
        return OperateResult.CreateSuccessResult<string>(address);
      if (address.StartsWithAndNumber("R"))
      {
        address = address.Substring(1);
        int length = address.IndexOf(".");
        return length > 0 && length < address.Length - 1 ? OperateResult.CreateSuccessResult<string>(address.Substring(0, length) + address.Substring(length + 1).PadLeft(2, '0')) : OperateResult.CreateSuccessResult<string>(address);
      }
      return Regex.IsMatch(address, "^[0-9]+$") ? OperateResult.CreateSuccessResult<string>(address) : new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    if (address.StartsWithAndNumber("DM"))
      return OperateResult.CreateSuccessResult<string>(address);
    if (address.StartsWithAndNumber("CC"))
      return OperateResult.CreateSuccessResult<string>("C" + address.Substring(2));
    if (address.StartsWithAndNumber("CS"))
      return OperateResult.CreateSuccessResult<string>("C" + address.Substring(2));
    if (address.StartsWithAndNumber("CTH"))
      return OperateResult.CreateSuccessResult<string>(address);
    if (address.StartsWithAndNumber("TC"))
      return OperateResult.CreateSuccessResult<string>("T" + address.Substring(2));
    if (address.StartsWithAndNumber("TS"))
      return OperateResult.CreateSuccessResult<string>("T" + address.Substring(2));
    if (address.StartsWithAndNumber("AT"))
      return OperateResult.CreateSuccessResult<string>(address);
    return address.StartsWithAndNumber("TM") ? OperateResult.CreateSuccessResult<string>(address) : new OperateResult<string>(StringResources.Language.NotSupportedDataType);
  }

  private static OperateResult<byte[]> BuildReadWordCommand(string address, bool isBit)
  {
    OperateResult<string> result = KeyenceKvOld.AnalysisAddress(address, isBit);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append("RD");
    stringBuilder.Append(" ");
    stringBuilder.Append(result.Content);
    stringBuilder.Append("\r");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  private static OperateResult<byte[]> BuildWriteWordCommand(string address, byte[] value)
  {
    OperateResult<string> result = KeyenceKvOld.AnalysisAddress(address, false);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    StringBuilder stringBuilder = new StringBuilder();
    if (address.StartsWithAndNumber("CC"))
      stringBuilder.Append("WR");
    else if (address.StartsWithAndNumber("CS"))
      stringBuilder.Append("WS");
    else if (address.StartsWithAndNumber("CTH"))
      stringBuilder.Append("WR");
    else if (address.StartsWithAndNumber("TC"))
      stringBuilder.Append("WR");
    else if (address.StartsWithAndNumber("TS"))
      stringBuilder.Append("WS");
    else
      stringBuilder.Append("WR");
    stringBuilder.Append(" ");
    stringBuilder.Append(result.Content);
    stringBuilder.Append(" ");
    stringBuilder.Append(BitConverter.ToUInt16(value, 0));
    stringBuilder.Append("\r");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  private static OperateResult<byte[]> BuildWriteBoolCommand(string address, bool value)
  {
    OperateResult<string> result = KeyenceKvOld.AnalysisAddress(address, true);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(value ? "ST" : "RS");
    stringBuilder.Append(" ");
    stringBuilder.Append(result.Content);
    stringBuilder.Append("\r");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  private static OperateResult<bool> ExtraBoolContent(string address, byte[] ack, bool isRead)
  {
    if (ack.Length == 0)
      return new OperateResult<bool>(StringResources.Language.MelsecFxReceiveZero);
    if (ack[0] == (byte) 69)
      return new OperateResult<bool>(KeyenceNanoHelper.GetErrorText(Encoding.ASCII.GetString(ack)));
    if (!isRead)
    {
      string msg = Encoding.ASCII.GetString(ack);
      return msg.StartsWith("OK", StringComparison.OrdinalIgnoreCase) ? OperateResult.CreateSuccessResult<bool>(true) : new OperateResult<bool>(msg);
    }
    try
    {
      return OperateResult.CreateSuccessResult<bool>(ushort.Parse(Encoding.ASCII.GetString(ack).TrimEnd('\r', '\n').Split(',')[0]) > (ushort) 0);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool>($"{ex.Message} Ack: {ack.ToHexString(' ')}");
    }
  }

  private static OperateResult<byte[]> ExtraResponseContent(
    string address,
    byte[] ack,
    bool isRead)
  {
    if (ack.Length == 0)
      return new OperateResult<byte[]>(StringResources.Language.MelsecFxReceiveZero);
    if (ack[0] == (byte) 69)
      return new OperateResult<byte[]>(KeyenceNanoHelper.GetErrorText(Encoding.ASCII.GetString(ack)));
    if (!isRead)
    {
      string msg = Encoding.ASCII.GetString(ack);
      return msg.StartsWith("OK", StringComparison.OrdinalIgnoreCase) ? OperateResult.CreateSuccessResult<byte[]>(new byte[0]) : new OperateResult<byte[]>(msg);
    }
    try
    {
      string s = Encoding.ASCII.GetString(ack).TrimEnd('\r', '\n');
      if (address.StartsWithAndNumber("CC"))
        return OperateResult.CreateSuccessResult<byte[]>(BitConverter.GetBytes(ushort.Parse(s.Split(',')[1])));
      if (address.StartsWithAndNumber("CS"))
        return OperateResult.CreateSuccessResult<byte[]>(BitConverter.GetBytes(ushort.Parse(s.Split(',')[2])));
      if (address.StartsWithAndNumber("CTH"))
        return OperateResult.CreateSuccessResult<byte[]>(BitConverter.GetBytes(ushort.Parse(s.Split(',')[1])));
      if (address.StartsWithAndNumber("TC"))
        return OperateResult.CreateSuccessResult<byte[]>(BitConverter.GetBytes(ushort.Parse(s.Split(',')[1])));
      if (address.StartsWithAndNumber("TS"))
        return OperateResult.CreateSuccessResult<byte[]>(BitConverter.GetBytes(ushort.Parse(s.Split(',')[2])));
      if (!address.StartsWithAndNumber("AT"))
        return OperateResult.CreateSuccessResult<byte[]>(BitConverter.GetBytes(ushort.Parse(s)));
      return OperateResult.CreateSuccessResult<byte[]>(BitConverter.GetBytes(ushort.Parse(s.Split(',')[1])));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"{ex.Message} Ack: {ack.ToHexString(' ')}");
    }
  }
}
