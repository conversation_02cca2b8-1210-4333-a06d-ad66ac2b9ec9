﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.AppSession
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System.Net;
using System.Net.Sockets;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>
/// 当前的网络会话信息，还包含了一些客户端相关的基本的参数信息<br />
/// The current network session information also contains some basic parameter information related to the client
/// </summary>
public class AppSession : SessionBase
{
  /// <summary>
  /// UDP通信中的远程端<br />
  /// Remote side in UDP communication
  /// </summary>
  internal EndPoint UdpEndPoint = (EndPoint) null;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.SessionBase.#ctor" />
  public AppSession() => this.ClientUniqueID = SoftBasic.GetUniqueStringByGuidAndRandom();

  /// <inheritdoc cref="M:HslCommunication.Core.Net.SessionBase.#ctor(System.Net.Sockets.Socket)" />
  public AppSession(Socket socket)
    : base(socket)
  {
    this.ClientUniqueID = SoftBasic.GetUniqueStringByGuidAndRandom();
  }

  /// <summary>
  /// 远程对象的别名信息<br />
  /// Alias information for remote objects
  /// </summary>
  public string LoginAlias { get; set; }

  /// <summary>
  /// 客户端唯一的标识，在NetPushServer及客户端类里有使用<br />
  /// The unique identifier of the client, used in the NetPushServer and client classes
  /// </summary>
  public string ClientUniqueID { get; set; }

  /// <summary>
  /// 数据内容缓存<br />
  /// data content cache
  /// </summary>
  internal byte[] BytesBuffer { get; set; }

  /// <summary>
  /// 用于关键字分类使用<br />
  /// Used for keyword classification
  /// </summary>
  internal string KeyGroup { get; set; }

  /// <summary>
  /// 当前会话绑定的自定义的对象内容<br />
  /// The content of the custom object bound to the current session
  /// </summary>
  public object Tag { get; set; }

  /// <inheritdoc />
  public override bool Equals(object obj) => this == obj;

  /// <inheritdoc />
  public override string ToString()
  {
    return !string.IsNullOrEmpty(this.LoginAlias) ? $"AppSession[{this.IpEndPoint}] [{this.LoginAlias}]" : $"AppSession[{this.IpEndPoint}]";
  }

  /// <inheritdoc />
  public override int GetHashCode() => base.GetHashCode();
}
