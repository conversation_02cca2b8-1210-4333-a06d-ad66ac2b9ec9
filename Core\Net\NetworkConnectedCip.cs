﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.NetworkConnectedCip
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.AllenBradley;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>基于连接的CIP协议的基类</summary>
public class NetworkConnectedCip : DeviceTcpNet
{
  private SoftIncrementCount incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 3L, 2);
  private long openForwardId = 256 /*0x0100*/;
  private long context = 0;

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new AllenBradleyMessage();

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return AllenBradleyHelper.PackRequestHeader((ushort) 112 /*0x70*/, this.SessionHandle, AllenBradleyHelper.PackCommandSpecificData(this.GetOTConnectionIdService(), command));
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.RegisterSessionHandle(BitConverter.GetBytes(Interlocked.Increment(ref this.context))), true, false);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult operateResult2 = AllenBradleyHelper.CheckResponse(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    uint session = this.ByteTransform.TransUInt32(operateResult1.Content, 4);
    for (int index = 0; index < 10; ++index)
    {
      long num1 = Interlocked.Increment(ref this.openForwardId);
      ushort connectionID = index < 7 ? (ushort) index : (ushort) HslHelper.HslRandom.Next(7, 200);
      OperateResult<byte[]> operateResult3 = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, session, this.GetLargeForwardOpen(connectionID), this.ByteTransform.TransByte(num1)), true, false);
      if (!operateResult3.IsSuccess)
        return (OperateResult) operateResult3;
      try
      {
        if (operateResult3.Content.Length >= 46 && operateResult3.Content[42] > (byte) 0)
        {
          ushort num2 = this.ByteTransform.TransUInt16(operateResult3.Content, 44);
          if (num2 != (ushort) 256 /*0x0100*/ || index >= 9)
          {
            if (num2 == (ushort) 256 /*0x0100*/)
              return new OperateResult("Connection in use or duplicate Forward Open");
            return num2 == (ushort) 275 ? new OperateResult("Extended Status: Out of connections (0x0113)") : new OperateResult("Forward Open failed, Code: " + this.ByteTransform.TransUInt16(operateResult3.Content, 44).ToString());
          }
        }
        else
        {
          this.OTConnectionId = this.ByteTransform.TransUInt32(operateResult3.Content, 44);
          break;
        }
      }
      catch (Exception ex)
      {
        return new OperateResult($"{ex.Message}{Environment.NewLine}Source: {operateResult3.Content.ToHexString(' ')}");
      }
    }
    this.incrementCount.ResetCurrentValue();
    this.SessionHandle = session;
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult ExtraOnDisconnect()
  {
    if (this.CommunicationPipe == null)
      return OperateResult.CreateSuccessResult();
    byte[] largeForwardClose = this.GetLargeForwardClose();
    if (largeForwardClose != null)
    {
      OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, this.SessionHandle, largeForwardClose), true, false);
      if (!operateResult.IsSuccess)
        return (OperateResult) operateResult;
    }
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.UnRegisterSessionHandle(this.SessionHandle), true, false);
    return !operateResult1.IsSuccess ? (OperateResult) operateResult1 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.RegisterSessionHandle(BitConverter.GetBytes(Interlocked.Increment(ref this.context))), true, false).ConfigureAwait(false);
    OperateResult<byte[]> read1 = await configuredTaskAwaitable;
    if (!read1.IsSuccess)
      return (OperateResult) read1;
    OperateResult check = AllenBradleyHelper.CheckResponse(read1.Content);
    if (!check.IsSuccess)
      return check;
    uint sessionHandle = this.ByteTransform.TransUInt32(read1.Content, 4);
    for (int i = 0; i < 10; ++i)
    {
      long id = Interlocked.Increment(ref this.openForwardId);
      ushort tick = i < 7 ? (ushort) i : (ushort) HslHelper.HslRandom.Next(7, 200);
      configuredTaskAwaitable = this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, sessionHandle, this.GetLargeForwardOpen(tick), this.ByteTransform.TransByte(id)), true, false).ConfigureAwait(false);
      OperateResult<byte[]> read2 = await configuredTaskAwaitable;
      if (!read2.IsSuccess)
        return (OperateResult) read2;
      try
      {
        if (read2.Content.Length >= 46 && read2.Content[42] > (byte) 0)
        {
          ushort err = this.ByteTransform.TransUInt16(read2.Content, 44);
          if (err != (ushort) 256 /*0x0100*/ || i >= 9)
            return err != (ushort) 256 /*0x0100*/ ? (err != (ushort) 275 ? new OperateResult("Forward Open failed, Code: " + this.ByteTransform.TransUInt16(read2.Content, 44).ToString()) : new OperateResult("Extended Status: Out of connections (0x0113)")) : new OperateResult("Connection in use or duplicate Forward Open");
        }
        else
        {
          this.OTConnectionId = this.ByteTransform.TransUInt32(read2.Content, 44);
          break;
        }
      }
      catch (Exception ex)
      {
        return new OperateResult($"{ex.Message}{Environment.NewLine}Source: {read2.Content.ToHexString(' ')}");
      }
    }
    this.incrementCount.ResetCurrentValue();
    this.SessionHandle = sessionHandle;
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> ExtraOnDisconnectAsync()
  {
    if (this.CommunicationPipe == null)
      return OperateResult.CreateSuccessResult();
    byte[] forwardClose = this.GetLargeForwardClose();
    if (forwardClose != null)
    {
      OperateResult<byte[]> close = await this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, this.SessionHandle, forwardClose), true, false);
      if (!close.IsSuccess)
        return (OperateResult) close;
      close = (OperateResult<byte[]>) null;
    }
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.UnRegisterSessionHandle(this.SessionHandle), true, false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) read;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.SessionHandle" />
  public uint SessionHandle { get; protected set; }

  /// <summary>O -&gt; T Network Connection ID</summary>
  public uint OTConnectionId { get; set; } = 0;

  /// <summary>T -&gt; O Network Connection ID</summary>
  public uint TOConnectionId { get; set; } = 0;

  /// <summary>将多个的CIP命令打包成一个服务的命令</summary>
  /// <param name="cip">CIP命令列表</param>
  /// <returns>服务命令</returns>
  protected byte[] PackCommandService(params byte[][] cip)
  {
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 177);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    long currentValue = this.incrementCount.GetCurrentValue();
    memoryStream.WriteByte(BitConverter.GetBytes(currentValue)[0]);
    memoryStream.WriteByte(BitConverter.GetBytes(currentValue)[1]);
    if (cip.Length == 1)
    {
      memoryStream.Write(cip[0], 0, cip[0].Length);
    }
    else
    {
      memoryStream.Write(new byte[6]
      {
        (byte) 10,
        (byte) 2,
        (byte) 32 /*0x20*/,
        (byte) 2,
        (byte) 36,
        (byte) 1
      }, 0, 6);
      memoryStream.WriteByte(BitConverter.GetBytes(cip.Length)[0]);
      memoryStream.WriteByte(BitConverter.GetBytes(cip.Length)[1]);
      int num = 2 + cip.Length * 2;
      for (int index = 0; index < cip.Length; ++index)
      {
        memoryStream.WriteByte(BitConverter.GetBytes(num)[0]);
        memoryStream.WriteByte(BitConverter.GetBytes(num)[1]);
        num += cip[index].Length;
      }
      for (int index = 0; index < cip.Length; ++index)
        memoryStream.Write(cip[index], 0, cip[index].Length);
    }
    byte[] array = memoryStream.ToArray();
    memoryStream.Dispose();
    BitConverter.GetBytes((ushort) (array.Length - 4)).CopyTo((Array) array, 2);
    return array;
  }

  /// <summary>获取数据通信的前置打开命令，不同的PLC的信息不一样。</summary>
  /// <param name="connectionID">连接的ID信息</param>
  /// <returns>原始命令数据</returns>
  protected virtual byte[] GetLargeForwardOpen(ushort connectionID)
  {
    return "\r\n00 00 00 00 00 00 02 00 00 00 00 00 b2 00 34 00\r\n5b 02 20 06 24 01 0e 9c 02 00 00 80 01 00 fe 80\r\n02 00 1b 05 30 a7 2b 03 02 00 00 00 80 84 1e 00\r\ncc 07 00 42 80 84 1e 00 cc 07 00 42 a3 03 20 02\r\n24 01 2c 01".ToHexBytes();
  }

  /// <summary>获取数据通信的后置关闭命令，不同的PLC的信息不一样。</summary>
  /// <returns>原始命令数据</returns>
  protected virtual byte[] GetLargeForwardClose() => (byte[]) null;

  private byte[] GetOTConnectionIdService()
  {
    byte[] connectionIdService = new byte[8]
    {
      (byte) 161,
      (byte) 0,
      (byte) 4,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0
    };
    this.ByteTransform.TransByte(this.OTConnectionId).CopyTo((Array) connectionIdService, 4);
    return connectionIdService;
  }

  /// <summary>
  /// 从PLC反馈的数据解析出真实的数据内容，结果内容分别是原始字节数据，数据类型代码，是否有很多的数据<br />
  /// The real data content is parsed from the data fed back by the PLC. The result content is the original byte data,
  /// the data type code, and whether there is a lot of data.
  /// </summary>
  /// <param name="response">PLC的反馈数据</param>
  /// <param name="isRead">是否是返回的操作</param>
  /// <returns>带有结果标识的最终数据</returns>
  public static OperateResult<byte[], ushort, bool> ExtractActualData(byte[] response, bool isRead)
  {
    List<byte> byteList = new List<byte>();
    try
    {
      int startIndex1 = 42;
      bool flag = false;
      ushort num1 = 0;
      ushort uint16_1 = BitConverter.ToUInt16(response, startIndex1);
      if (BitConverter.ToInt32(response, 46) == 138)
      {
        int startIndex2 = 50;
        int uint16_2 = (int) BitConverter.ToUInt16(response, startIndex2);
        for (int index1 = 0; index1 < uint16_2; ++index1)
        {
          int num2 = (int) BitConverter.ToUInt16(response, startIndex2 + 2 + index1 * 2) + startIndex2;
          int num3 = index1 == uint16_2 - 1 ? response.Length : (int) BitConverter.ToUInt16(response, startIndex2 + 4 + index1 * 2) + startIndex2;
          ushort uint16_3 = BitConverter.ToUInt16(response, num2 + 2);
          switch (uint16_3)
          {
            case 0:
              if (isRead)
              {
                for (int index2 = num2 + 6; index2 < num3; ++index2)
                  byteList.Add(response[index2]);
                continue;
              }
              continue;
            case 4:
              OperateResult<byte[], ushort, bool> actualData1 = new OperateResult<byte[], ushort, bool>();
              actualData1.ErrorCode = (int) uint16_3;
              actualData1.Message = StringResources.Language.AllenBradley04;
              return actualData1;
            case 5:
              OperateResult<byte[], ushort, bool> actualData2 = new OperateResult<byte[], ushort, bool>();
              actualData2.ErrorCode = (int) uint16_3;
              actualData2.Message = StringResources.Language.AllenBradley05;
              return actualData2;
            case 6:
              if (response[startIndex2 + 2] == (byte) 210 || response[startIndex2 + 2] == (byte) 204)
              {
                OperateResult<byte[], ushort, bool> actualData3 = new OperateResult<byte[], ushort, bool>();
                actualData3.ErrorCode = (int) uint16_3;
                actualData3.Message = StringResources.Language.AllenBradley06;
                return actualData3;
              }
              goto case 0;
            case 10:
              OperateResult<byte[], ushort, bool> actualData4 = new OperateResult<byte[], ushort, bool>();
              actualData4.ErrorCode = (int) uint16_3;
              actualData4.Message = StringResources.Language.AllenBradley0A;
              return actualData4;
            case 12:
              OperateResult<byte[], ushort, bool> actualData5 = new OperateResult<byte[], ushort, bool>();
              actualData5.ErrorCode = (int) uint16_3;
              actualData5.Message = StringResources.Language.AllenBradley0C;
              return actualData5;
            case 19:
              OperateResult<byte[], ushort, bool> actualData6 = new OperateResult<byte[], ushort, bool>();
              actualData6.ErrorCode = (int) uint16_3;
              actualData6.Message = StringResources.Language.AllenBradley13;
              return actualData6;
            case 28:
              OperateResult<byte[], ushort, bool> actualData7 = new OperateResult<byte[], ushort, bool>();
              actualData7.ErrorCode = (int) uint16_3;
              actualData7.Message = StringResources.Language.AllenBradley1C;
              return actualData7;
            case 30:
              OperateResult<byte[], ushort, bool> actualData8 = new OperateResult<byte[], ushort, bool>();
              actualData8.ErrorCode = (int) uint16_3;
              actualData8.Message = StringResources.Language.AllenBradley1E;
              return actualData8;
            case 38:
              OperateResult<byte[], ushort, bool> actualData9 = new OperateResult<byte[], ushort, bool>();
              actualData9.ErrorCode = (int) uint16_3;
              actualData9.Message = StringResources.Language.AllenBradley26;
              return actualData9;
            default:
              OperateResult<byte[], ushort, bool> actualData10 = new OperateResult<byte[], ushort, bool>();
              actualData10.ErrorCode = (int) uint16_3;
              actualData10.Message = StringResources.Language.UnknownError;
              return actualData10;
          }
        }
      }
      else
      {
        byte num4 = response[startIndex1 + 6];
        switch (num4)
        {
          case 0:
            if (response[startIndex1 + 4] == (byte) 205 || response[startIndex1 + 4] == (byte) 211)
              return OperateResult.CreateSuccessResult<byte[], ushort, bool>(byteList.ToArray(), num1, flag);
            if (response[startIndex1 + 4] == (byte) 204 || response[startIndex1 + 4] == (byte) 210)
            {
              for (int index = startIndex1 + 10; index < startIndex1 + 2 + (int) uint16_1; ++index)
                byteList.Add(response[index]);
              num1 = BitConverter.ToUInt16(response, startIndex1 + 8);
            }
            else if (response[startIndex1 + 4] == (byte) 213)
            {
              for (int index = startIndex1 + 8; index < startIndex1 + 2 + (int) uint16_1; ++index)
                byteList.Add(response[index]);
            }
            else if (response[startIndex1 + 4] == (byte) 203)
            {
              if (response[58] > (byte) 0)
                return new OperateResult<byte[], ushort, bool>((int) response[58], $"{AllenBradleyDF1Serial.GetExtStatusDescription(response[58])}{Environment.NewLine}Source: {response.RemoveBegin<byte>(57).ToHexString(' ')}");
              return !isRead ? OperateResult.CreateSuccessResult<byte[], ushort, bool>(byteList.ToArray(), num1, flag) : OperateResult.CreateSuccessResult<byte[], ushort, bool>(response.RemoveBegin<byte>(61), num1, flag);
            }
            break;
          case 4:
            OperateResult<byte[], ushort, bool> actualData11 = new OperateResult<byte[], ushort, bool>();
            actualData11.ErrorCode = (int) num4;
            actualData11.Message = StringResources.Language.AllenBradley04;
            return actualData11;
          case 5:
            OperateResult<byte[], ushort, bool> actualData12 = new OperateResult<byte[], ushort, bool>();
            actualData12.ErrorCode = (int) num4;
            actualData12.Message = StringResources.Language.AllenBradley05;
            return actualData12;
          case 6:
            flag = true;
            goto case 0;
          case 10:
            OperateResult<byte[], ushort, bool> actualData13 = new OperateResult<byte[], ushort, bool>();
            actualData13.ErrorCode = (int) num4;
            actualData13.Message = StringResources.Language.AllenBradley0A;
            return actualData13;
          case 12:
            OperateResult<byte[], ushort, bool> actualData14 = new OperateResult<byte[], ushort, bool>();
            actualData14.ErrorCode = (int) num4;
            actualData14.Message = StringResources.Language.AllenBradley0C;
            return actualData14;
          case 19:
            OperateResult<byte[], ushort, bool> actualData15 = new OperateResult<byte[], ushort, bool>();
            actualData15.ErrorCode = (int) num4;
            actualData15.Message = StringResources.Language.AllenBradley13;
            return actualData15;
          case 28:
            OperateResult<byte[], ushort, bool> actualData16 = new OperateResult<byte[], ushort, bool>();
            actualData16.ErrorCode = (int) num4;
            actualData16.Message = StringResources.Language.AllenBradley1C;
            return actualData16;
          case 30:
            OperateResult<byte[], ushort, bool> actualData17 = new OperateResult<byte[], ushort, bool>();
            actualData17.ErrorCode = (int) num4;
            actualData17.Message = StringResources.Language.AllenBradley1E;
            return actualData17;
          case 38:
            OperateResult<byte[], ushort, bool> actualData18 = new OperateResult<byte[], ushort, bool>();
            actualData18.ErrorCode = (int) num4;
            actualData18.Message = StringResources.Language.AllenBradley26;
            return actualData18;
          default:
            OperateResult<byte[], ushort, bool> actualData19 = new OperateResult<byte[], ushort, bool>();
            actualData19.ErrorCode = (int) num4;
            actualData19.Message = StringResources.Language.UnknownError;
            return actualData19;
        }
      }
      return OperateResult.CreateSuccessResult<byte[], ushort, bool>(byteList.ToArray(), num1, flag);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[], ushort, bool>($"ExtractActualData failed: {ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
    }
  }
}
