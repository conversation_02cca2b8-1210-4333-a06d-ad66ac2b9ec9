﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.WebSocket.WebSocketHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Pipe;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.WebSocket;

/// <summary>websocket的相关辅助的方法</summary>
public class WebSocketHelper
{
  /// <summary>计算websocket返回得令牌</summary>
  /// <param name="webSocketKey">请求的令牌</param>
  /// <returns>返回的令牌</returns>
  public static string CalculateWebscoketSha1(string webSocketKey)
  {
    SHA1 shA1 = (SHA1) new SHA1CryptoServiceProvider();
    byte[] hash = shA1.ComputeHash(Encoding.UTF8.GetBytes(webSocketKey + "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"));
    shA1.Dispose();
    return Convert.ToBase64String(hash);
  }

  /// <summary>根据http网页的信息，计算出返回的安全令牌</summary>
  /// <param name="httpGet">网页信息</param>
  /// <returns>返回的安全令牌</returns>
  public static string GetSecKeyAccetp(string httpGet)
  {
    string webSocketKey = string.Empty;
    Match match = new Regex("Sec\\-WebSocket\\-Key:(.*?)\\r\\n").Match(httpGet);
    if (match.Success)
      webSocketKey = Regex.Replace(match.Value, "Sec\\-WebSocket\\-Key:(.*?)\\r\\n", "$1").Trim();
    return WebSocketHelper.CalculateWebscoketSha1(webSocketKey);
  }

  /// <summary>检测当前的反馈对象是否是标准的websocket请求</summary>
  /// <param name="httpGet">http的请求内容</param>
  /// <returns>是否验证成功</returns>
  public static OperateResult CheckWebSocketLegality(string httpGet)
  {
    return Regex.IsMatch(httpGet, "Connection:[ ]*Upgrade", RegexOptions.IgnoreCase) || Regex.IsMatch(httpGet, "Upgrade:[ ]*websocket", RegexOptions.IgnoreCase) ? OperateResult.CreateSuccessResult() : new OperateResult("Can't find Connection: Upgrade or Upgrade: websocket");
  }

  /// <summary>从当前的websocket的HTTP请求头里，分析出订阅的主题内容</summary>
  /// <param name="httpGet">http的请求内容</param>
  /// <returns>是否验证成功</returns>
  public static string[] GetWebSocketSubscribes(string httpGet)
  {
    Match match = new Regex("HslSubscribes:[^\\r\\n]+").Match(httpGet);
    if (!match.Success)
      return (string[]) null;
    return SoftBasic.UrlDecode(match.Value.Substring(14).Trim(), Encoding.UTF8).Split(new char[1]
    {
      ','
    }, StringSplitOptions.RemoveEmptyEntries);
  }

  /// <summary>从当前的Websocket的Url里，分析出订阅的主题内容</summary>
  /// <param name="url">URL内容，例如 ws://127.0.0.1:1883/HslSubscribes=A,B</param>
  /// <returns>消息主题</returns>
  public static string[] GetWebSocketSubscribesFromUrl(string url)
  {
    Match match = new Regex("HslSubscribes=[\\s\\S]+$").Match(url);
    if (!match.Success)
      return (string[]) null;
    return SoftBasic.UrlDecode(match.Value.Substring(14).Trim(), Encoding.UTF8).Split(new char[1]
    {
      ','
    }, StringSplitOptions.RemoveEmptyEntries);
  }

  /// <summary>获取初步握手的时候的完整返回的数据信息</summary>
  /// <param name="httpGet">请求的网页信息</param>
  /// <returns>完整的返回信息</returns>
  public static OperateResult<byte[]> GetResponse(string httpGet)
  {
    try
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("HTTP/1.1 101 Switching Protocols" + Environment.NewLine);
      stringBuilder.Append("Connection: Upgrade" + Environment.NewLine);
      stringBuilder.Append("Upgrade: websocket" + Environment.NewLine);
      stringBuilder.Append("Server:hsl websocket server" + Environment.NewLine);
      stringBuilder.Append("Access-Control-Allow-Credentials:true" + Environment.NewLine);
      stringBuilder.Append("Access-Control-Allow-Headers:content-type" + Environment.NewLine);
      stringBuilder.Append($"Sec-WebSocket-Accept: {WebSocketHelper.GetSecKeyAccetp(httpGet)}{Environment.NewLine}{Environment.NewLine}");
      return OperateResult.CreateSuccessResult<byte[]>(Encoding.UTF8.GetBytes(stringBuilder.ToString()));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>创建连接服务器的http请求，输入订阅的主题信息</summary>
  /// <param name="ipAddress">远程服务器的ip地址</param>
  /// <param name="port">远程服务器的端口号</param>
  /// <param name="url">参数信息</param>
  /// <param name="subscribes">通知hsl的服务器，需要订阅的topic信息</param>
  /// <param name="getCarryHostAndPort">GET命令是否携带 Host 信息。</param>
  /// <returns>报文信息</returns>
  public static byte[] BuildWsSubRequest(
    string ipAddress,
    int port,
    string url,
    string[] subscribes,
    bool getCarryHostAndPort)
  {
    StringBuilder stringBuilder = new StringBuilder();
    if (subscribes != null)
    {
      stringBuilder.Append("HslSubscribes: ");
      for (int index = 0; index < subscribes.Length; ++index)
      {
        stringBuilder.Append(subscribes[index]);
        if (index != subscribes.Length - 1)
          stringBuilder.Append(",");
      }
    }
    return WebSocketHelper.BuildWsRequest(ipAddress, port, url, stringBuilder.ToString(), getCarryHostAndPort);
  }

  /// <summary>创建连接服务器的http请求，采用问答的机制</summary>
  /// <param name="ipAddress">远程服务器的ip地址</param>
  /// <param name="port">远程服务器的端口号</param>
  /// <returns>报文信息</returns>
  public static byte[] BuildWsQARequest(string ipAddress, int port)
  {
    return WebSocketHelper.BuildWsRequest(ipAddress, port, string.Empty, "HslRequestAndAnswer: true", false);
  }

  /// <summary>根据额外的参数信息，创建新的websocket的请求信息</summary>
  /// <param name="ipAddress">ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="url">跟在端口号后面的额外的参数信息</param>
  /// <param name="extra">额外的参数信息</param>
  /// <param name="getCarryHostAndPort">GET命令是否携带 Host 信息。</param>
  /// <returns>报文信息</returns>
  public static byte[] BuildWsRequest(
    string ipAddress,
    int port,
    string url,
    string extra,
    bool getCarryHostAndPort)
  {
    if (!url.StartsWith("/"))
      url = "/" + url;
    StringBuilder stringBuilder = new StringBuilder();
    if (getCarryHostAndPort)
      stringBuilder.Append($"GET ws://{ipAddress}:{port}{url} HTTP/1.1");
    else
      stringBuilder.Append($"GET {url} HTTP/1.1");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append($"Host: {ipAddress}:{port}");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Connection: Upgrade");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Pragma: no-cache");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Cache-Control: no-cache");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Upgrade: websocket");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append($"Origin: http://{ipAddress}:{port}");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Sec-WebSocket-Version: 13");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3314.0 Safari/537.36 SE 2.X MetaSr 1.0");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Accept-Encoding: gzip, deflate, br");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Accept-Language: zh-CN,zh;q=0.9");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Sec-WebSocket-Key: ia36apzXapB4YVxRfVyTuw==");
    stringBuilder.Append(Environment.NewLine);
    stringBuilder.Append("Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits");
    stringBuilder.Append(Environment.NewLine);
    if (!string.IsNullOrEmpty(extra))
    {
      stringBuilder.Append(extra);
      stringBuilder.Append(Environment.NewLine);
    }
    stringBuilder.Append(Environment.NewLine);
    return Encoding.UTF8.GetBytes(stringBuilder.ToString());
  }

  /// <summary>将普通的文本信息转换成websocket的报文</summary>
  /// <param name="opCode">操作信息码</param>
  /// <param name="isMask">是否使用掩码</param>
  /// <param name="message">等待转换的数据信息</param>
  /// <returns>数据包</returns>
  public static byte[] WebScoketPackData(int opCode, bool isMask, string message)
  {
    return WebSocketHelper.WebScoketPackData(opCode, isMask, string.IsNullOrEmpty(message) ? new byte[0] : Encoding.UTF8.GetBytes(message));
  }

  /// <summary>将普通的文本信息转换成websocket的报文</summary>
  /// <param name="opCode">操作信息码</param>
  /// <param name="isMask">是否使用掩码</param>
  /// <param name="payload">等待转换的数据信息</param>
  /// <returns>数据包</returns>
  public static byte[] WebScoketPackData(int opCode, bool isMask, byte[] payload)
  {
    if (payload == null)
      payload = new byte[0];
    byte[] buffer1 = payload.CopyArray<byte>();
    MemoryStream memoryStream = new MemoryStream();
    byte[] buffer2 = new byte[4]
    {
      (byte) 155,
      (byte) 3,
      (byte) 161,
      (byte) 168
    };
    if (isMask)
    {
      HslHelper.HslRandom.NextBytes(buffer2);
      for (int index = 0; index < buffer1.Length; ++index)
        buffer1[index] = (byte) ((uint) buffer1[index] ^ (uint) buffer2[index % 4]);
    }
    memoryStream.WriteByte((byte) (128 /*0x80*/ | opCode));
    if (buffer1.Length < 126)
      memoryStream.WriteByte((byte) (buffer1.Length + (isMask ? 128 /*0x80*/ : 0)));
    else if (buffer1.Length <= (int) ushort.MaxValue)
    {
      memoryStream.WriteByte((byte) (126 + (isMask ? 128 /*0x80*/ : 0)));
      byte[] bytes = BitConverter.GetBytes((ushort) buffer1.Length);
      Array.Reverse((Array) bytes);
      memoryStream.Write(bytes, 0, bytes.Length);
    }
    else
    {
      memoryStream.WriteByte((byte) ((int) sbyte.MaxValue + (isMask ? 128 /*0x80*/ : 0)));
      byte[] bytes = BitConverter.GetBytes((ulong) buffer1.Length);
      Array.Reverse((Array) bytes);
      memoryStream.Write(bytes, 0, bytes.Length);
    }
    if (isMask)
      memoryStream.Write(buffer2, 0, buffer2.Length);
    memoryStream.Write(buffer1, 0, buffer1.Length);
    byte[] array = memoryStream.ToArray();
    memoryStream.Dispose();
    return array;
  }

  /// <summary>
  /// 从socket接收一条完整的websocket数据，返回<see cref="T:HslCommunication.WebSocket.WebSocketMessage" />的数据信息<br />
  /// Receive a complete websocket data from the socket, return the data information of the <see cref="T:HslCommunication.WebSocket.WebSocketMessage" />
  /// </summary>
  /// <param name="pipe">当前通信的管道信息</param>
  /// <returns>包含websocket消息的结果内容</returns>
  public static OperateResult<WebSocketMessage> ReceiveWebSocketPayload(CommunicationPipe pipe)
  {
    List<byte> byteList = new List<byte>();
    bool flag = false;
    int num1 = 0;
    int num2 = 0;
    OperateResult<WebSocketMessage, bool> webSocketPayload;
    while (true)
    {
      webSocketPayload = WebSocketHelper.ReceiveFrameWebSocketPayload(pipe);
      if (webSocketPayload.IsSuccess)
      {
        byteList.AddRange((IEnumerable<byte>) webSocketPayload.Content1.Payload);
        if (num2 == 0)
        {
          flag = webSocketPayload.Content1.HasMask;
          num1 = webSocketPayload.Content1.OpCode;
        }
        if (!webSocketPayload.Content2)
          ++num2;
        else
          goto label_5;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<WebSocketMessage>((OperateResult) webSocketPayload);
label_5:
    return OperateResult.CreateSuccessResult<WebSocketMessage>(new WebSocketMessage()
    {
      HasMask = flag,
      OpCode = num1,
      Payload = byteList.ToArray()
    });
  }

  /// <summary>
  /// 从socket接收一条<see cref="T:HslCommunication.WebSocket.WebSocketMessage" />片段数据，返回<see cref="T:HslCommunication.WebSocket.WebSocketMessage" />的数据信息和是否最后一条数据内容<br />
  /// Receive a piece of <see cref="T:HslCommunication.WebSocket.WebSocketMessage" /> fragment data from the socket, return the data information of <see cref="T:HslCommunication.WebSocket.WebSocketMessage" /> and whether the last data content
  /// </summary>
  /// <param name="pipe">当前通信的管道信息</param>
  /// <returns>包含websocket消息的结果内容</returns>
  private static OperateResult<WebSocketMessage, bool> ReceiveFrameWebSocketPayload(
    CommunicationPipe pipe)
  {
    OperateResult<byte[]> result1 = pipe.Receive(2, 5000);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) result1);
    bool flag1 = ((int) result1.Content[0] & 128 /*0x80*/) == 128 /*0x80*/;
    bool flag2 = ((int) result1.Content[1] & 128 /*0x80*/) == 128 /*0x80*/;
    int num = (int) result1.Content[0] & 15;
    byte[] numArray = (byte[]) null;
    int length = (int) result1.Content[1] & (int) sbyte.MaxValue;
    switch (length)
    {
      case 126:
        OperateResult<byte[]> result2 = pipe.Receive(2, 5000);
        if (!result2.IsSuccess)
          return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) result2);
        Array.Reverse((Array) result2.Content);
        length = (int) BitConverter.ToUInt16(result2.Content, 0);
        break;
      case (int) sbyte.MaxValue:
        OperateResult<byte[]> result3 = pipe.Receive(8, 5000);
        if (!result3.IsSuccess)
          return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) result3);
        Array.Reverse((Array) result3.Content);
        length = (int) BitConverter.ToUInt64(result3.Content, 0);
        break;
    }
    if (flag2)
    {
      OperateResult<byte[]> result4 = pipe.Receive(4, 5000);
      if (!result4.IsSuccess)
        return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) result4);
      numArray = result4.Content;
    }
    OperateResult<byte[]> result5 = pipe.Receive(length, 60000);
    if (!result5.IsSuccess)
      return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) result5);
    if (flag2)
    {
      for (int index = 0; index < result5.Content.Length; ++index)
        result5.Content[index] = (byte) ((uint) result5.Content[index] ^ (uint) numArray[index % 4]);
    }
    return OperateResult.CreateSuccessResult<WebSocketMessage, bool>(new WebSocketMessage()
    {
      HasMask = flag2,
      OpCode = num,
      Payload = result5.Content
    }, flag1);
  }

  /// <inheritdoc cref="M:HslCommunication.WebSocket.WebSocketHelper.ReceiveWebSocketPayload(HslCommunication.Core.Pipe.CommunicationPipe)" />
  public static async Task<OperateResult<WebSocketMessage>> ReceiveWebSocketPayloadAsync(
    CommunicationPipe pipe)
  {
    List<byte> data = new List<byte>();
    bool hasMask = false;
    int opCode = 0;
    int cycle = 0;
    OperateResult<WebSocketMessage, bool> read;
    while (true)
    {
      read = await WebSocketHelper.ReceiveFrameWebSocketPayloadAsync(pipe).ConfigureAwait(false);
      if (read.IsSuccess)
      {
        data.AddRange((IEnumerable<byte>) read.Content1.Payload);
        if (cycle == 0)
        {
          hasMask = read.Content1.HasMask;
          opCode = read.Content1.OpCode;
        }
        if (!read.Content2)
        {
          ++cycle;
          read = (OperateResult<WebSocketMessage, bool>) null;
        }
        else
          goto label_6;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<WebSocketMessage>((OperateResult) read);
label_6:
    return OperateResult.CreateSuccessResult<WebSocketMessage>(new WebSocketMessage()
    {
      HasMask = hasMask,
      OpCode = opCode,
      Payload = data.ToArray()
    });
  }

  /// <inheritdoc cref="M:HslCommunication.WebSocket.WebSocketHelper.ReceiveFrameWebSocketPayload(HslCommunication.Core.Pipe.CommunicationPipe)" />
  private static async Task<OperateResult<WebSocketMessage, bool>> ReceiveFrameWebSocketPayloadAsync(
    CommunicationPipe pipe)
  {
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = pipe.ReceiveAsync(2, 5000).ConfigureAwait(false);
    OperateResult<byte[]> head = await configuredTaskAwaitable;
    if (!head.IsSuccess)
      return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) head);
    bool isEof = ((int) head.Content[0] & 128 /*0x80*/) == 128 /*0x80*/;
    bool hasMask = ((int) head.Content[1] & 128 /*0x80*/) == 128 /*0x80*/;
    int opCode = (int) head.Content[0] & 15;
    byte[] mask = (byte[]) null;
    int length = (int) head.Content[1] & (int) sbyte.MaxValue;
    switch (length)
    {
      case 126:
        configuredTaskAwaitable = pipe.ReceiveAsync(2, 5000).ConfigureAwait(false);
        OperateResult<byte[]> extended1 = await configuredTaskAwaitable;
        if (!extended1.IsSuccess)
          return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) extended1);
        Array.Reverse((Array) extended1.Content);
        length = (int) BitConverter.ToUInt16(extended1.Content, 0);
        extended1 = (OperateResult<byte[]>) null;
        break;
      case (int) sbyte.MaxValue:
        configuredTaskAwaitable = pipe.ReceiveAsync(8, 5000).ConfigureAwait(false);
        OperateResult<byte[]> extended2 = await configuredTaskAwaitable;
        if (!extended2.IsSuccess)
          return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) extended2);
        Array.Reverse((Array) extended2.Content);
        length = (int) BitConverter.ToUInt64(extended2.Content, 0);
        extended2 = (OperateResult<byte[]>) null;
        break;
    }
    if (hasMask)
    {
      configuredTaskAwaitable = pipe.ReceiveAsync(4, 5000).ConfigureAwait(false);
      OperateResult<byte[]> maskResult = await configuredTaskAwaitable;
      if (!maskResult.IsSuccess)
        return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) maskResult);
      mask = maskResult.Content;
      maskResult = (OperateResult<byte[]>) null;
    }
    configuredTaskAwaitable = pipe.ReceiveAsync(length, 60000).ConfigureAwait(false);
    OperateResult<byte[]> payload = await configuredTaskAwaitable;
    if (!payload.IsSuccess)
      return OperateResult.CreateFailedResult<WebSocketMessage, bool>((OperateResult) payload);
    if (hasMask)
    {
      for (int i = 0; i < payload.Content.Length; ++i)
        payload.Content[i] = (byte) ((uint) payload.Content[i] ^ (uint) mask[i % 4]);
    }
    return OperateResult.CreateSuccessResult<WebSocketMessage, bool>(new WebSocketMessage()
    {
      HasMask = hasMask,
      OpCode = opCode,
      Payload = payload.Content
    }, isEof);
  }
}
