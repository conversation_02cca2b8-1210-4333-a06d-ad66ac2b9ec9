﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.StateObject
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Net.Sockets;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>网络中的异步对象</summary>
internal class StateObject : StateOneBase
{
  /// <summary>实例化一个对象</summary>
  public StateObject()
  {
  }

  /// <summary>实例化一个对象，指定接收或是发送的数据长度</summary>
  /// <param name="length">数据长度</param>
  public StateObject(int length)
  {
    this.DataLength = length;
    this.Buffer = new byte[length];
  }

  /// <summary>唯一的一串信息</summary>
  public string UniqueId { get; set; }

  /// <summary>网络套接字</summary>
  public Socket WorkSocket { get; set; }

  /// <summary>是否关闭了通道</summary>
  public bool IsClose { get; set; }

  /// <summary>清空旧的数据</summary>
  public void Clear()
  {
    this.IsError = false;
    this.IsClose = false;
    this.AlreadyDealLength = 0;
    this.Buffer = (byte[]) null;
  }
}
