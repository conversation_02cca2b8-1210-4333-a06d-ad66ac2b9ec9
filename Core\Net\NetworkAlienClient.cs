﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.NetworkAlienClient
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>
/// 异形客户端的基类，提供了基础的异形操作<br />
/// The base class of the profiled client provides the basic profiled operation
/// </summary>
public class NetworkAlienClient : CommunicationTcpServer, IDisposable
{
  private byte[] password;
  private List<string> trustOnline;
  private SimpleHybirdLock trustLock;
  private bool isResponseAck = true;
  private bool isCheckPwd = true;
  private bool disposedValue;
  private bool useRegistrationPackage = true;
  /// <summary>状态登录成功</summary>
  public const byte StatusOk = 0;
  /// <summary>重复登录</summary>
  public const byte StatusLoginRepeat = 1;
  /// <summary>禁止登录</summary>
  public const byte StatusLoginForbidden = 2;
  /// <summary>密码错误</summary>
  public const byte StatusPasswodWrong = 3;

  /// <summary>
  /// 默认的无参构造方法<br />
  /// The default parameterless constructor
  /// </summary>
  public NetworkAlienClient()
  {
    this.password = new byte[6];
    this.trustOnline = new List<string>();
    this.trustLock = new SimpleHybirdLock();
  }

  /// <summary>
  /// 当接收到了新的请求的时候执行的操作<br />
  /// An action performed when a new request is received
  /// </summary>
  /// <param name="pipeTcpNet">管道对象</param>
  /// <param name="endPoint">终结点</param>
  protected override async void ThreadPoolLogin(PipeTcpNet pipeTcpNet, IPEndPoint endPoint)
  {
    if (this.useRegistrationPackage)
    {
      OperateResult<byte[]> check = await pipeTcpNet.ReceiveMessageAsync((INetMessage) new AlienMessage(), (byte[]) null, false);
      if (!check.IsSuccess)
      {
        this.LogNet?.WriteDebug(this.ToString(), $"Initialize [{endPoint}] DTU failed: " + check.Message);
      }
      else
      {
        byte[] content = check.Content;
        if (content != null && content.Length < 22)
          pipeTcpNet.CloseCommunication();
        else if (check.Content[0] != (byte) 72)
        {
          pipeTcpNet.CloseCommunication();
        }
        else
        {
          string dtu = Encoding.ASCII.GetString(check.Content, 5, 11).Trim(char.MinValue, ' ');
          bool needAckResult = true;
          if (check.Content.Length >= 29 && (check.Content[28] == (byte) 1 || check.Content[28] == (byte) 49))
            needAckResult = false;
          bool isPasswrodRight = true;
          if (this.isCheckPwd)
          {
            for (int i = 0; i < this.password.Length; ++i)
            {
              if ((int) check.Content[16 /*0x10*/ + i] != (int) this.password[i])
              {
                isPasswrodRight = false;
                break;
              }
            }
          }
          if (!isPasswrodRight)
          {
            if (this.isResponseAck & needAckResult)
            {
              OperateResult send = pipeTcpNet.Send(this.GetResponse((byte) 3));
              if (send.IsSuccess)
                pipeTcpNet.CloseCommunication();
              send = (OperateResult) null;
            }
            else
              pipeTcpNet.CloseCommunication();
            this.LogNet?.WriteWarn(this.ToString(), $"[{endPoint}] DTU:{dtu} Login Password Wrong, Id:" + dtu);
          }
          else
          {
            PipeDtuNet pipeDtuNet = new PipeDtuNet(pipeTcpNet);
            pipeDtuNet.DTU = dtu;
            pipeDtuNet.DtuServer = this;
            pipeDtuNet.Pwd = check.Content.SelectMiddle<byte>(16 /*0x10*/, 6).ToHexString();
            if (check.Content.Length >= 28)
            {
              pipeDtuNet.DTUIpAddress = BitConverter.ToInt32(check.Content, 22);
              pipeDtuNet.DTUPort = BitConverter.ToUInt16(check.Content, 26);
            }
            if (!this.IsClientPermission(pipeDtuNet))
            {
              if (this.isResponseAck & needAckResult)
              {
                OperateResult send = pipeTcpNet.Send(this.GetResponse((byte) 2));
                if (send.IsSuccess)
                  pipeTcpNet.CloseCommunication();
                send = (OperateResult) null;
              }
              else
                pipeTcpNet.CloseCommunication();
              this.LogNet?.WriteWarn(this.ToString(), $"Initialize [{endPoint}] DTU:{dtu} Login Forbidden");
            }
            else
            {
              int status = this.IsClientOnline(pipeDtuNet);
              if (status != 0)
              {
                if (this.isResponseAck & needAckResult)
                {
                  OperateResult send = pipeTcpNet.Send(this.GetResponse((byte) 1));
                  if (send.IsSuccess)
                    pipeTcpNet.CloseCommunication();
                  send = (OperateResult) null;
                }
                else
                  pipeTcpNet.CloseCommunication();
                this.LogNet?.WriteDebug(this.ToString(), NetworkAlienClient.GetMsgFromCode($"Initialize [{endPoint}] DTU:{dtu}", status, "  Ack :" + (this.isResponseAck & needAckResult).ToString()));
              }
              else
              {
                if (this.isResponseAck & needAckResult)
                {
                  OperateResult send = pipeTcpNet.Send(this.GetResponse((byte) 0));
                  if (!send.IsSuccess)
                    return;
                  send = (OperateResult) null;
                }
                this.LogNet?.WriteDebug(this.ToString(), NetworkAlienClient.GetMsgFromCode($"Initialize [{endPoint}] DTU:{dtu}", status, "  Ack :" + (this.isResponseAck & needAckResult).ToString()));
                this.RaiseClientConnected(pipeDtuNet);
                check = (OperateResult<byte[]>) null;
                dtu = (string) null;
                pipeDtuNet = (PipeDtuNet) null;
              }
            }
          }
        }
      }
    }
    else
    {
      PipeDtuNet pipeDtuNet = new PipeDtuNet(pipeTcpNet);
      pipeDtuNet.DtuServer = this;
      this.RaiseClientConnected(pipeDtuNet);
      pipeDtuNet = (PipeDtuNet) null;
    }
  }

  private void RaiseClientConnected(PipeDtuNet pipeDtuNet)
  {
    this.LogNet?.WriteDebug(this.ToString(), $"Dtu Session<{pipeDtuNet}> Connected");
    NetworkAlienClient.OnClientConnectedDelegate onClientConnected = this.OnClientConnected;
    if (onClientConnected == null)
      return;
    onClientConnected(pipeDtuNet);
  }

  /// <summary>
  /// 在DTU设备发送了注册报文的时候，指示是否返回响应报文，用来通知DTU设备是否登录成功，默认为 <c>True</c><br />
  /// When a DTU sends a registration packet, it indicates whether to return a response packet to notify the DTU whether the DTU is logged in, The default is <c>True</c>
  /// </summary>
  public bool IsResponseAck
  {
    get => this.isResponseAck;
    set => this.isResponseAck = value;
  }

  /// <summary>
  /// 是否统一检查密码，如果每个会话需要自己检查密码，就需要设置为false<br />
  /// Whether to check the password uniformly, if each session needs to check the password by itself, it needs to be set to false
  /// </summary>
  public bool IsCheckPwd
  {
    get => this.isCheckPwd;
    set => this.isCheckPwd = value;
  }

  /// <summary>
  /// 是否启用注册包的功能，如果不启用，就不会进行注册包的验证，有连接上来就自动覆盖本地的会话信息，不是很安全<br />
  /// Whether to enable the registration package function. If not enabled, the registration package verification will not be performed,
  /// and the local session information will be automatically overwritten when a connection is made, which is not very secure
  /// </summary>
  public bool UseRegistrationPackage
  {
    get => this.useRegistrationPackage;
    set => this.useRegistrationPackage = value;
  }

  /// <summary>
  /// 当有服务器连接上来的时候触发<br />
  /// Triggered when a server is connected
  /// </summary>
  public event NetworkAlienClient.OnClientConnectedDelegate OnClientConnected = null;

  /// <summary>获取取返回的命令信息</summary>
  /// <param name="status">状态</param>
  /// <returns>回发的指令信息</returns>
  private byte[] GetResponse(byte status)
  {
    return new byte[6]
    {
      (byte) 72,
      (byte) 115,
      (byte) 110,
      (byte) 0,
      (byte) 1,
      status
    };
  }

  /// <summary>检测当前的DTU是否在线</summary>
  /// <param name="pipe">当前的会话信息</param>
  /// <returns>当前的会话是否在线</returns>
  public virtual int IsClientOnline(PipeDtuNet pipe) => 0;

  /// <summary>检测当前的dtu是否允许登录</summary>
  /// <param name="session">当前的会话信息</param>
  /// <returns>当前的id是否可允许登录</returns>
  private bool IsClientPermission(PipeDtuNet session)
  {
    bool flag = false;
    this.trustLock.Enter();
    if (this.trustOnline.Count == 0)
    {
      flag = true;
    }
    else
    {
      for (int index = 0; index < this.trustOnline.Count; ++index)
      {
        if (this.trustOnline[index] == session.DTU)
        {
          flag = true;
          break;
        }
      }
    }
    this.trustLock.Leave();
    return flag;
  }

  /// <summary>
  /// 设置密码，需要传入长度为6的字节数组<br />
  /// To set the password, you need to pass in an array of bytes of length 6
  /// </summary>
  /// <param name="password">密码信息</param>
  public void SetPassword(byte[] password)
  {
    if (password == null || password.Length != 6)
      return;
    password.CopyTo((Array) this.password, 0);
  }

  /// <summary>
  /// 设置可信任的客户端列表，传入一个DTU的列表信息<br />
  /// Set up the list of trusted clients, passing in the list information for a DTU
  /// </summary>
  /// <param name="clients">客户端列表</param>
  public void SetTrustClients(string[] clients)
  {
    this.trustOnline = new List<string>((IEnumerable<string>) clients);
  }

  /// <summary>释放当前的对象</summary>
  /// <param name="disposing"></param>
  protected virtual void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (disposing)
    {
      this.trustLock?.Dispose();
      this.OnClientConnected = (NetworkAlienClient.OnClientConnectedDelegate) null;
    }
    this.disposedValue = true;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    this.Dispose(true);
    GC.SuppressFinalize((object) this);
  }

  /// <inheritdoc />
  public override string ToString() => "NetworkAlienBase";

  /// <summary>获取错误的描述信息</summary>
  /// <param name="head">dtu信息</param>
  /// <param name="code">错误码</param>
  /// <param name="info">其他消息</param>
  /// <returns>错误信息</returns>
  public static string GetMsgFromCode(string head, int code, string info)
  {
    switch (code)
    {
      case 0:
        return $"{head}  Login Success{info}";
      case 1:
        return $"{head}  Login Repeat{info}";
      case 2:
        return $"{head}  Login Forbidden{info}";
      case 3:
        return $"{head}  Login Passwod Wrong{info}";
      default:
        return $"{head}  Login Unknow reason:{info}";
    }
  }

  /// <summary>
  /// 远程连接的客户端上线的委托事件<br />
  /// The delegate event for the client to which the remote connection goes online
  /// </summary>
  /// <param name="pipe">异形客户端的会话信息</param>
  public delegate void OnClientConnectedDelegate(PipeDtuNet pipe);
}
