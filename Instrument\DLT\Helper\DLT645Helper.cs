﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.Helper.DLT645Helper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.DLT.Helper;

/// <summary>DLT645相关的辅助类</summary>
public class DLT645Helper
{
  /// <summary>判断DLT645的报文是否是完整的</summary>
  /// <param name="ms">内存数据信息</param>
  /// <returns>是否完整的</returns>
  public static bool CheckReceiveDataComplete(MemoryStream ms)
  {
    byte[] array = ms.ToArray();
    if (array.Length < 10)
      return false;
    int headCode68H = DLT645Helper.FindHeadCode68H(array);
    return headCode68H >= 0 && array.Length >= headCode68H + 10 && (int) array[headCode68H + 9] + 12 + headCode68H == array.Length && array[array.Length - 1] == (byte) 22;
  }

  /// <summary>将地址解析成BCD码的地址，并且扩充到12位，不够的补0操作</summary>
  /// <param name="address">地址域信息</param>
  /// <returns>实际的结果</returns>
  public static OperateResult<byte[]> GetAddressByteFromString(string address)
  {
    if (address == null || address.Length == 0)
      return new OperateResult<byte[]>(StringResources.Language.DLTAddressCannotNull);
    if (address.Length > 12)
      return new OperateResult<byte[]>(StringResources.Language.DLTAddressCannotMoreThan12);
    if (!Regex.IsMatch(address, "^[0-9A-A]+$"))
      return new OperateResult<byte[]>(StringResources.Language.DLTAddressMatchFailed);
    if (address.Length < 12)
      address = address.PadLeft(12, '0');
    return OperateResult.CreateSuccessResult<byte[]>(((IEnumerable<byte>) address.ToHexBytes()).Reverse<byte>().ToArray<byte>());
  }

  /// <summary>将指定的地址信息，控制码信息，数据域信息打包成完整的报文命令</summary>
  /// <param name="address">地址域信息，地址域由6个字节构成，每字节2位BCD码，地址长度可达12位十进制数。地址域支持锁位寻址，即从若干低位起，剩余高位补AAH作为通配符进行读表操作</param>
  /// <param name="control">控制码信息</param>
  /// <param name="dataArea">数据域的内容</param>
  /// <returns>返回是否报文创建成功</returns>
  public static OperateResult<byte[]> BuildDlt645EntireCommand(
    string address,
    byte control,
    byte[] dataArea)
  {
    if (dataArea == null)
      dataArea = new byte[0];
    OperateResult<byte[]> addressByteFromString = DLT645Helper.GetAddressByteFromString(address);
    if (!addressByteFromString.IsSuccess)
      return addressByteFromString;
    byte[] numArray = new byte[12 + dataArea.Length];
    numArray[0] = (byte) 104;
    addressByteFromString.Content.CopyTo((Array) numArray, 1);
    numArray[7] = (byte) 104;
    numArray[8] = control;
    numArray[9] = (byte) dataArea.Length;
    if (dataArea.Length != 0)
    {
      dataArea.CopyTo((Array) numArray, 10);
      for (int index = 0; index < dataArea.Length; ++index)
        numArray[index + 10] += (byte) 51;
    }
    int num = 0;
    for (int index = 0; index < numArray.Length - 2; ++index)
      num += (int) numArray[index];
    numArray[numArray.Length - 2] = (byte) num;
    numArray[numArray.Length - 1] = (byte) 22;
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>检查设备返回的报文信息，是否校验码确认通过</summary>
  /// <param name="response">设备返回的报文</param>
  /// <param name="index">起始校验的索引</param>
  /// <returns>是否校验成功</returns>
  public static OperateResult CheckResponseCS(byte[] response, int index)
  {
    if (response.Length <= 2 + index)
      return new OperateResult("Receive length too short: " + response.ToHexString());
    int num1 = 0;
    for (int index1 = index; index1 < response.Length - 2; ++index1)
      num1 += (int) response[index1];
    int num2 = (int) (byte) num1;
    return num2 == (int) response[response.Length - 2] ? OperateResult.CreateSuccessResult() : new OperateResult($"CS check failed, need[{response[response.Length - 2]}] actual[{num2}]");
  }

  /// <summary>从用户输入的地址信息中解析出真实的地址及数据标识</summary>
  /// <param name="type">DLT的类型</param>
  /// <param name="address">用户输入的地址信息</param>
  /// <param name="defaultStation">默认的地址域</param>
  /// <param name="length">数据长度信息</param>
  /// <returns>解析结果信息</returns>
  public static OperateResult<string, byte[]> AnalysisBytesAddress(
    DLT645Type type,
    string address,
    string defaultStation,
    ushort length = 1)
  {
    try
    {
      string str = defaultStation;
      int index1 = 0;
      byte[] numArray;
      if (type == DLT645Type.DLT2007)
      {
        numArray = length == (ushort) 1 ? new byte[4] : new byte[5];
        if (length != (ushort) 1)
          numArray[4] = (byte) length;
      }
      else
      {
        numArray = length == (ushort) 1 ? new byte[2] : new byte[3];
        if (length != (ushort) 1)
        {
          numArray[0] = (byte) length;
          index1 = 1;
        }
      }
      if (address.IndexOf(';') > 0)
      {
        string[] strArray = address.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
        for (int index2 = 0; index2 < strArray.Length; ++index2)
        {
          if (strArray[index2].StartsWith("s="))
            str = strArray[index2].Substring(2);
          else
            ((IEnumerable<byte>) strArray[index2].ToHexBytes()).Reverse<byte>().ToArray<byte>().CopyTo((Array) numArray, index1);
        }
      }
      else
        ((IEnumerable<byte>) address.ToHexBytes()).Reverse<byte>().ToArray<byte>().CopyTo((Array) numArray, index1);
      return OperateResult.CreateSuccessResult<string, byte[]>(str, numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<string, byte[]>("Address prase wrong: " + ex.Message);
    }
  }

  /// <summary>从用户输入的地址信息中解析出真实的地址及数据标识</summary>
  /// <param name="address">用户输入的地址信息</param>
  /// <param name="defaultStation">默认的地址域</param>
  /// <returns>解析结果信息</returns>
  public static OperateResult<string, int> AnalysisIntegerAddress(
    string address,
    string defaultStation)
  {
    try
    {
      string str = defaultStation;
      int num = 0;
      if (address.IndexOf(';') > 0)
      {
        string[] strArray = address.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
        for (int index = 0; index < strArray.Length; ++index)
        {
          if (strArray[index].StartsWith("s="))
            str = strArray[index].Substring(2);
          else
            num = Convert.ToInt32(strArray[index]);
        }
      }
      else
        num = Convert.ToInt32(address);
      return OperateResult.CreateSuccessResult<string, int>(str, num);
    }
    catch (Exception ex)
    {
      return new OperateResult<string, int>(ex.Message);
    }
  }

  /// <summary>检查当前的DLT仪表设备反馈数据信息是否正确</summary>
  /// <param name="dlt">DLT通信设备</param>
  /// <param name="send">发送到DLT仪表的报文信息</param>
  /// <param name="response">从仪表反馈的数据信息</param>
  /// <returns>是否校验成功</returns>
  public static OperateResult CheckResponse(IDlt645 dlt, byte[] send, byte[] response)
  {
    if (response.Length < 9)
      return new OperateResult(StringResources.Language.ReceiveDataLengthTooShort);
    OperateResult operateResult1 = DLT645Helper.CheckResponseCS(response, 0);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult operateResult2 = DLT645Helper.CheckStation(send, response);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    if (((int) response[8] & 64 /*0x40*/) != 64 /*0x40*/)
      return OperateResult.CreateSuccessResult();
    if (response.Length < 11)
      return new OperateResult(StringResources.Language.ReceiveDataLengthTooShort);
    byte num = response[10];
    if (dlt.DLTType == DLT645Type.DLT2007)
    {
      if (num.GetBoolByIndex(0))
        return new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit0);
      if (num.GetBoolByIndex(1))
        return new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit1);
      if (num.GetBoolByIndex(2))
        return new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit2);
      if (num.GetBoolByIndex(3))
        return new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit3);
      if (num.GetBoolByIndex(4))
        return new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit4);
      if (num.GetBoolByIndex(5))
        return new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit5);
      if (num.GetBoolByIndex(6))
        return new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit6);
      return num.GetBoolByIndex(7) ? new OperateResult((int) num, StringResources.Language.DLTErrorInfoBit7) : new OperateResult((int) num, StringResources.Language.UnknownError);
    }
    if (num.GetBoolByIndex(0))
      return new OperateResult((int) num, StringResources.Language.DLT1997ErrorInfoBit0);
    if (num.GetBoolByIndex(1))
      return new OperateResult((int) num, StringResources.Language.DLT1997ErrorInfoBit1);
    if (num.GetBoolByIndex(2))
      return new OperateResult((int) num, StringResources.Language.DLT1997ErrorInfoBit2);
    if (num.GetBoolByIndex(4))
      return new OperateResult((int) num, StringResources.Language.DLT1997ErrorInfoBit4);
    if (num.GetBoolByIndex(5))
      return new OperateResult((int) num, StringResources.Language.DLT1997ErrorInfoBit5);
    return num.GetBoolByIndex(6) ? new OperateResult((int) num, StringResources.Language.DLT1997ErrorInfoBit6) : new OperateResult((int) num, StringResources.Language.UnknownError);
  }

  private static OperateResult CheckStation(byte[] send, byte[] response)
  {
    return send.Length < 8 || response.Length < 8 || send[1] == (byte) 170 && send[2] == (byte) 170 && send[3] == (byte) 170 && send[4] == (byte) 170 && send[5] == (byte) 170 && send[6] == (byte) 170 || send[1] == (byte) 153 && send[2] == (byte) 153 && send[3] == (byte) 153 && send[4] == (byte) 153 && send[5] == (byte) 153 && send[6] == (byte) 153 || (int) send[1] == (int) response[1] && (int) send[2] == (int) response[2] && (int) send[3] == (int) response[3] && (int) send[4] == (int) response[4] && (int) send[5] == (int) response[5] && (int) send[6] == (int) response[6] || (int) send[1] == (int) response[6] && (int) send[2] == (int) response[5] && (int) send[3] == (int) response[4] && (int) send[4] == (int) response[3] && (int) send[5] == (int) response[2] && (int) send[6] == (int) response[1] ? OperateResult.CreateSuccessResult() : new OperateResult($"Station check failed, need: {send.SelectMiddle<byte>(1, 6).ToHexString()} But Actual: {response.SelectMiddle<byte>(1, 6).ToHexString()}");
  }

  /// <summary>寻找0x68字节开头的位置信息</summary>
  /// <param name="buffer">缓存数据</param>
  /// <returns>如果有则为索引位置，如果没有则为空</returns>
  public static int FindHeadCode68H(byte[] buffer)
  {
    if (buffer == null)
      return -1;
    for (int headCode68H = 0; headCode68H < buffer.Length; ++headCode68H)
    {
      if (buffer[headCode68H] == (byte) 104)
        return headCode68H;
    }
    return -1;
  }

  private static OperateResult<byte[]> ReadWithAddress(
    IDlt645 dlt,
    string address,
    byte[] dataArea)
  {
    OperateResult<byte[]> operateResult1 = DLT645Helper.BuildDlt645EntireCommand(address, dlt.DLTType == DLT645Type.DLT2007 ? (byte) 17 : (byte) 1, dataArea);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    OperateResult result = DLT645Helper.CheckResponse(dlt, operateResult1.Content, operateResult2.Content);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result);
    try
    {
      return dlt.DLTType == DLT645Type.DLT2007 ? (operateResult2.Content.Length < 16 /*0x10*/ ? OperateResult.CreateSuccessResult<byte[]>(new byte[0]) : OperateResult.CreateSuccessResult<byte[]>(operateResult2.Content.SelectMiddle<byte>(14, operateResult2.Content.Length - 16 /*0x10*/))) : (operateResult2.Content.Length < 14 ? OperateResult.CreateSuccessResult<byte[]>(new byte[0]) : OperateResult.CreateSuccessResult<byte[]>(operateResult2.Content.SelectMiddle<byte>(12, operateResult2.Content.Length - 14)));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"ReadWithAddress failed: {ex.Message}{Environment.NewLine}Source: {operateResult2.Content.ToHexString(' ')}");
    }
  }

  /// <summary>
  /// 根据指定的数据标识来读取相关的原始数据信息，地址标识根据手册来，从高位到地位，例如 00-00-00-00，分割符可以任意特殊字符或是没有分隔符。<br />
  /// Read the relevant original data information according to the specified data identifier. The address identifier is based on the manual,
  /// from high to position, such as 00-00-00-00. The separator can be any special character or no separator.
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;00-00-00-00" 或是 "s=100000;00-00-02-00"，关于数据域信息，需要查找手册，例如:00-01-00-00 表示： (当前)正向有功总电能
  /// </remarks>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="address">数据标识，具体需要查找手册来对应</param>
  /// <param name="length">数据长度信息</param>
  /// <returns>结果信息</returns>
  public static OperateResult<byte[]> Read(IDlt645 dlt, string address, ushort length)
  {
    OperateResult<string, byte[]> result = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station, length);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : DLT645Helper.ReadWithAddress(dlt, result.Content1, result.Content2);
  }

  /// <summary>
  /// 读取指定地址的所有的字符串数据信息，一般来说，一个地址只有一个数据，但是少部分的地址存在多个数据，例如 01-01-00-00 正向有功总需求及发生时间<br />
  /// Read all the string data information of the specified address, in general, there is only one data for one address, but there are multiple data for a small number of addresses,
  /// such as 01-01-00-00 Forward active total demand and occurrence time
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;00-00-00-00" 或是 "s=100000;00-00-02-00"，关于数据域信息，需要查找手册，例如:00-01-00-00 表示： (当前)正向有功总电能<br />
  /// 地址也可以携带是否数据翻转的标记，例如 "reverse=false;00-00-00-00" 解析数据的时候就不发生反转的操作
  /// </remarks>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="address">数据标识，具体需要查找手册来对应</param>
  /// <returns>字符串数组信息</returns>
  public static OperateResult<string[]> ReadStringArray(IDlt645 dlt, string address)
  {
    bool booleanParameter = HslHelper.ExtractBooleanParameter(ref address, "reverse", true);
    OperateResult<string, byte[]> result1 = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = DLT645Helper.ReadWithAddress(dlt, result1.Content1, result1.Content2);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<string[]>((OperateResult) result2) : DLTTransform.TransStringsFromDLt(dlt.DLTType, result2.Content, result1.Content2, booleanParameter);
  }

  /// <summary>
  /// 读取指定地址的所有的double数据信息，一般来说，一个地址只有一个数据，但是少部分的地址存在多个数据，然后全部转换为double数据信息<br />
  /// Read all the double data information of the specified address, in general, an address has only one data, but a small number of addresses exist multiple data,
  /// and then all converted to double data information
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;00-00-00-00" 或是 "s=100000;00-00-02-00"，关于数据域信息，需要查找手册，例如:00-01-00-00 表示： (当前)正向有功总电能<br />
  /// 地址也可以携带是否数据翻转的标记，例如 "reverse=false;00-00-00-00" 解析数据的时候就不发生反转的操作
  /// </remarks>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="address">数据标识，具体需要查找手册来对应</param>
  /// <param name="length">读取的数据长度信息</param>
  public static OperateResult<double[]> ReadDouble(IDlt645 dlt, string address, ushort length)
  {
    OperateResult<string[]> result = DLT645Helper.ReadStringArray(dlt, address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<double[]>((OperateResult) result);
    try
    {
      return OperateResult.CreateSuccessResult<double[]>(((IEnumerable<string>) result.Content).Take<string>((int) length).Select<string, double>((Func<string, double>) (m => double.Parse(m))).ToArray<double>());
    }
    catch (Exception ex)
    {
      return new OperateResult<double[]>($"double.Parse failed: {ex.Message}{Environment.NewLine}Source: {result.Content.ToArrayString<string>()}");
    }
  }

  /// <summary>功能码1C的操作，主要用来控制跳闸（控制类型1A），合闸允许（控制类型1B）</summary>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="password">密钥信息</param>
  /// <param name="opCode">操作者代码</param>
  /// <param name="station">站号信息</param>
  /// <param name="controlType">控制类型</param>
  /// <param name="validTime">有效截止时间</param>
  /// <returns>是否操作成功</returns>
  public static OperateResult Function1C(
    IDlt645 dlt,
    string password,
    string opCode,
    string station,
    byte controlType,
    DateTime validTime)
  {
    byte[] numArray = new byte[8];
    numArray[0] = controlType;
    validTime.ToString("ss-mm-HH-dd-MM-yy").ToHexBytes().CopyTo((Array) numArray, 2);
    byte[] dataArea;
    if (dlt.DLTType == DLT645Type.DLT2007)
      dataArea = SoftBasic.SpliceArray<byte>(password.ToHexBytes(), opCode.ToHexBytes(), numArray);
    else
      dataArea = numArray;
    OperateResult<byte[]> operateResult1 = DLT645Helper.BuildDlt645EntireCommand(string.IsNullOrEmpty(station) ? dlt.Station : station, (byte) 28, dataArea);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : DLT645Helper.CheckResponse(dlt, operateResult1.Content, operateResult2.Content);
  }

  /// <summary>
  /// 根据指定的数据标识来写入相关的原始数据信息，地址标识根据手册来，从高位到地位，例如 00-00-00-00，分割符可以任意特殊字符或是没有分隔符。<br />
  /// Read the relevant original data information according to the specified data identifier. The address identifier is based on the manual,
  /// from high to position, such as 00-00-00-00. The separator can be any special character or no separator.
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;00-00-00-00" 或是 "s=100000;00-00-02-00"，关于数据域信息，需要查找手册，例如:00-01-00-00 表示： (当前)正向有功总电能<br />
  /// 注意：本命令必须与编程键配合使用
  /// </remarks>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="password">密钥信息</param>
  /// <param name="opCode">操作者代码</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">写入的数据值</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IDlt645 dlt,
    string password,
    string opCode,
    string address,
    byte[] value)
  {
    OperateResult<string, byte[]> result = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    byte[] dataArea;
    if (dlt.DLTType == DLT645Type.DLT2007)
      dataArea = SoftBasic.SpliceArray<byte>(result.Content2, password.ToHexBytes(), opCode.ToHexBytes(), value);
    else
      dataArea = SoftBasic.SpliceArray<byte>(result.Content2, value);
    OperateResult<byte[]> operateResult1 = DLT645Helper.BuildDlt645EntireCommand(result.Content1, dlt.DLTType == DLT645Type.DLT2007 ? (byte) 20 : (byte) 4, dataArea);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : DLT645Helper.CheckResponse(dlt, operateResult1.Content, operateResult2.Content);
  }

  /// <summary>
  /// 将指定的数据写入到仪表中，，地址标识根据手册来，从高位到地位，例如 00-00-00-00，分割符可以任意特殊字符或是没有分隔符。<br />
  /// Write the data to the gauge, address identification according to the manual, from high bit to position,
  /// such as 00-00-00-00, the separator can be any special character or no delimiter.
  /// </summary>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="password">密钥信息</param>
  /// <param name="opCode">操作者代码</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">写入的数据值</param>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult Write(
    IDlt645 dlt,
    string password,
    string opCode,
    string address,
    string[] value)
  {
    bool booleanParameter = HslHelper.ExtractBooleanParameter(ref address, "reverse", true);
    OperateResult<string, byte[]> result1 = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = DLTTransform.TransDltFromStrings(dlt.DLTType, value, result1.Content2, booleanParameter);
    return !result2.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result2) : DLT645Helper.Write(dlt, password, opCode, address, result2.Content);
  }

  /// <summary>
  /// 读取设备的通信地址，仅支持点对点通讯的情况，返回地址域数据，例如：149100007290<br />
  /// Read the communication address of the device, only support point-to-point communication, and return the address field data, for example: 149100007290
  /// </summary>
  /// <param name="dlt">DLT通信对象</param>
  /// <returns>设备的通信地址</returns>
  public static OperateResult<string> ReadAddress(IDlt645 dlt)
  {
    OperateResult<byte[]> result1 = DLT645Helper.BuildDlt645EntireCommand("AAAAAAAAAAAA", (byte) 19, (byte[]) null);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = dlt.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result2);
    OperateResult result3 = DLT645Helper.CheckResponse(dlt, result1.Content, result2.Content);
    if (!result3.IsSuccess)
      return OperateResult.CreateFailedResult<string>(result3);
    dlt.Station = ((IEnumerable<byte>) result2.Content.SelectMiddle<byte>(1, 6)).Reverse<byte>().ToArray<byte>().ToHexString();
    return OperateResult.CreateSuccessResult<string>(((IEnumerable<byte>) result2.Content.SelectMiddle<byte>(1, 6)).Reverse<byte>().ToArray<byte>().ToHexString());
  }

  /// <summary>
  /// 写入设备的地址域信息，仅支持点对点通讯的情况，需要指定地址域信息，例如：149100007290<br />
  /// Write the address domain information of the device, only support point-to-point communication,
  /// you need to specify the address domain information, for example: 149100007290
  /// </summary>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="address">等待写入的地址域</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteAddress(IDlt645 dlt, string address)
  {
    OperateResult<byte[]> addressByteFromString = DLT645Helper.GetAddressByteFromString(address);
    if (!addressByteFromString.IsSuccess)
      return (OperateResult) addressByteFromString;
    OperateResult<byte[]> operateResult1 = DLT645Helper.BuildDlt645EntireCommand("AAAAAAAAAAAA", dlt.DLTType == DLT645Type.DLT2007 ? (byte) 21 : (byte) 10, addressByteFromString.Content);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult operateResult3 = DLT645Helper.CheckResponse(dlt, operateResult1.Content, operateResult2.Content);
    if (!operateResult3.IsSuccess)
      return operateResult3;
    return SoftBasic.IsTwoBytesEquel(operateResult2.Content.SelectMiddle<byte>(1, 6), DLT645Helper.GetAddressByteFromString(address).Content) ? OperateResult.CreateSuccessResult() : new OperateResult(StringResources.Language.DLTErrorWriteReadCheckFailed);
  }

  /// <summary>
  /// 广播指定的时间，强制从站与主站时间同步，传入<see cref="T:System.DateTime" />时间对象，没有数据返回。<br />
  /// Broadcast the specified time, force the slave station to synchronize with the master station time,
  /// pass in the <see cref="T:System.DateTime" /> time object, and no data will be returned.
  /// </summary>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="dateTime">时间对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult BroadcastTime(IDlt645 dlt, DateTime dateTime)
  {
    string str = $"{dateTime.Second:D2}{dateTime.Minute:D2}{dateTime.Hour:D2}{dateTime.Day:D2}{dateTime.Month:D2}{dateTime.Year % 100:D2}";
    OperateResult<byte[]> operateResult = DLT645Helper.BuildDlt645EntireCommand("999999999999", dlt.DLTType == DLT645Type.DLT2007 ? (byte) 8 : (byte) 8, str.ToHexBytes());
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) dlt.ReadFromCoreServer(operateResult.Content, false);
  }

  /// <summary>
  /// 对设备发送冻结命令，默认点对点操作，地址域为 99999999999999 时为广播，数据域格式说明：MMDDhhmm(月日时分)，
  /// 99DDhhmm表示月为周期定时冻结，9999hhmm表示日为周期定时冻结，999999mm表示以小时为周期定时冻结，99999999表示瞬时冻结<br />
  /// Send a freeze command to the device, the default point-to-point operation, when the address field is 9999999999999,
  /// it is broadcast, and the data field format description: MMDDhhmm (month, day, hour and minute),
  /// 99DDhhmm means the month is the periodic fixed freeze, 9999hhmm means the day is the periodic periodic freeze,
  /// and 999999mm means the hour It is periodic timed freezing, 99999999 means instantaneous freezing
  /// </summary>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="dataArea">数据域信息</param>
  /// <returns>是否成功冻结</returns>
  public static OperateResult FreezeCommand(IDlt645 dlt, string dataArea)
  {
    OperateResult<string, byte[]> result = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, dataArea, dlt.Station);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    OperateResult<byte[]> operateResult1 = DLT645Helper.BuildDlt645EntireCommand(result.Content1, (byte) 22, result.Content2);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    if (result.Content1 == "999999999999")
      return (OperateResult) dlt.ReadFromCoreServer(operateResult1.Content, false);
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : DLT645Helper.CheckResponse(dlt, operateResult1.Content, operateResult2.Content);
  }

  private static OperateResult<byte[]> BuildChangeBaudRateCommand(
    IDlt645 dlt,
    string baudRate,
    out byte code)
  {
    code = (byte) 0;
    OperateResult<string, int> result = DLT645Helper.AnalysisIntegerAddress(baudRate, dlt.Station);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (dlt.DLTType == DLT645Type.DLT2007)
    {
      switch (result.Content2)
      {
        case 600:
          code = (byte) 2;
          break;
        case 1200:
          code = (byte) 4;
          break;
        case 2400:
          code = (byte) 8;
          break;
        case 4800:
          code = (byte) 16 /*0x10*/;
          break;
        case 9600:
          code = (byte) 32 /*0x20*/;
          break;
        case 19200:
          code = (byte) 64 /*0x40*/;
          break;
        default:
          return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      }
    }
    else
    {
      switch (result.Content2)
      {
        case 300:
          code = (byte) 2;
          break;
        case 600:
          code = (byte) 4;
          break;
        case 2400:
          code = (byte) 16 /*0x10*/;
          break;
        case 4800:
          code = (byte) 32 /*0x20*/;
          break;
        case 9600:
          code = (byte) 64 /*0x40*/;
          break;
        default:
          return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      }
    }
    return DLT645Helper.BuildDlt645EntireCommand(result.Content1, (byte) (dlt.DLTType == DLT645Type.DLT2007 ? 23 : 12), new byte[1]
    {
      code
    });
  }

  /// <summary>
  /// 更改通信速率，波特率可选 600,1200,2400,4800,9600,19200，其他值无效，可以携带地址域信息，s=1;9600 <br />
  /// Change the communication rate, the baud rate can be 600, 1200, 2400, 4800, 9600, 19200,
  /// other values are invalid, you can carry address domain information, s=1;9600
  /// </summary>
  /// <remarks>对于DLT1997来说，只支持 300, 600, 2400, 4800, 9600</remarks>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="baudRate">波特率的信息</param>
  /// <returns>是否更改成功</returns>
  public static OperateResult ChangeBaudRate(IDlt645 dlt, string baudRate)
  {
    byte code;
    OperateResult<byte[]> operateResult1 = DLT645Helper.BuildChangeBaudRateCommand(dlt, baudRate, out code);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult operateResult3 = DLT645Helper.CheckResponse(dlt, operateResult1.Content, operateResult2.Content);
    if (!operateResult3.IsSuccess)
      return operateResult3;
    return (int) operateResult2.Content[10] == (int) code ? OperateResult.CreateSuccessResult() : new OperateResult(StringResources.Language.DLTErrorWriteReadCheckFailed);
  }

  private static async Task<OperateResult<byte[]>> ReadWithAddressAsync(
    IDlt645 dlt,
    string address,
    byte[] dataArea)
  {
    OperateResult<byte[]> command = DLT645Helper.BuildDlt645EntireCommand(address, dlt.DLTType == DLT645Type.DLT2007 ? (byte) 17 : (byte) 1, dataArea);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await dlt.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return read;
    OperateResult check = DLT645Helper.CheckResponse(dlt, command.Content, read.Content);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(check);
    try
    {
      return dlt.DLTType != DLT645Type.DLT2007 ? (read.Content.Length >= 14 ? OperateResult.CreateSuccessResult<byte[]>(read.Content.SelectMiddle<byte>(12, read.Content.Length - 14)) : OperateResult.CreateSuccessResult<byte[]>(new byte[0])) : (read.Content.Length >= 16 /*0x10*/ ? OperateResult.CreateSuccessResult<byte[]>(read.Content.SelectMiddle<byte>(14, read.Content.Length - 16 /*0x10*/)) : OperateResult.CreateSuccessResult<byte[]>(new byte[0]));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"ReadWithAddress failed: {ex.Message}{Environment.NewLine}Source: {read.Content.ToHexString(' ')}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Read(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IDlt645 dlt,
    string address,
    ushort length)
  {
    OperateResult<string, byte[]> analysis = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station, length);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    OperateResult<byte[]> operateResult = await DLT645Helper.ReadWithAddressAsync(dlt, analysis.Content1, analysis.Content2);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.ReadDouble(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.UInt16)" />
  public static async Task<OperateResult<double[]>> ReadDoubleAsync(
    IDlt645 dlt,
    string address,
    ushort length)
  {
    OperateResult<string[]> read = await DLT645Helper.ReadStringArrayAsync(dlt, address);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<double[]>((OperateResult) read);
    try
    {
      return OperateResult.CreateSuccessResult<double[]>(((IEnumerable<string>) read.Content).Take<string>((int) length).Select<string, double>((Func<string, double>) (m => double.Parse(m))).ToArray<double>());
    }
    catch (Exception ex)
    {
      return new OperateResult<double[]>($"double.Parse failed: {ex.Message}{Environment.NewLine}Source: {read.Content.ToArrayString<string>()}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.ReadStringArray(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public static async Task<OperateResult<string[]>> ReadStringArrayAsync(
    IDlt645 dlt,
    string address)
  {
    bool reverse = HslHelper.ExtractBooleanParameter(ref address, "reverse", true);
    OperateResult<string, byte[]> analysis = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) analysis);
    OperateResult<byte[]> read = await DLT645Helper.ReadWithAddressAsync(dlt, analysis.Content1, analysis.Content2);
    return read.IsSuccess ? DLTTransform.TransStringsFromDLt(dlt.DLTType, read.Content, analysis.Content2, reverse) : OperateResult.CreateFailedResult<string[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IDlt645 dlt,
    string password,
    string opCode,
    string address,
    byte[] value)
  {
    OperateResult<string, byte[]> analysis = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    byte[] content = (byte[]) null;
    if (dlt.DLTType == DLT645Type.DLT2007)
      content = SoftBasic.SpliceArray<byte>(analysis.Content2, password.ToHexBytes(), opCode.ToHexBytes(), value);
    else
      content = SoftBasic.SpliceArray<byte>(analysis.Content2, value);
    OperateResult<byte[]> command = DLT645Helper.BuildDlt645EntireCommand(analysis.Content1, dlt.DLTType == DLT645Type.DLT2007 ? (byte) 20 : (byte) 4, content);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await dlt.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? DLT645Helper.CheckResponse(dlt, command.Content, read.Content) : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public static async Task<OperateResult> WriteAsync(
    IDlt645 dlt,
    string password,
    string opCode,
    string address,
    string[] value)
  {
    bool reverse = HslHelper.ExtractBooleanParameter(ref address, "reverse", true);
    OperateResult<string, byte[]> analysis = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, address, dlt.Station);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    OperateResult<byte[]> command = DLTTransform.TransDltFromStrings(dlt.DLTType, value, analysis.Content2, reverse);
    if (!command.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult operateResult = await DLT645Helper.WriteAsync(dlt, password, opCode, address, command.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.ReadAddress(HslCommunication.Instrument.DLT.Helper.IDlt645)" />
  public static async Task<OperateResult<string>> ReadAddressAsync(IDlt645 dlt)
  {
    OperateResult<byte[]> command = DLT645Helper.BuildDlt645EntireCommand("AAAAAAAAAAAA", (byte) 19, (byte[]) null);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) command);
    OperateResult<byte[]> read = await dlt.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    OperateResult check = DLT645Helper.CheckResponse(dlt, command.Content, read.Content);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<string>(check);
    dlt.Station = ((IEnumerable<byte>) read.Content.SelectMiddle<byte>(1, 6)).Reverse<byte>().ToArray<byte>().ToHexString();
    return OperateResult.CreateSuccessResult<string>(((IEnumerable<byte>) read.Content.SelectMiddle<byte>(1, 6)).Reverse<byte>().ToArray<byte>().ToHexString());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.WriteAddress(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public static async Task<OperateResult> WriteAddressAsync(IDlt645 dlt, string address)
  {
    OperateResult<byte[]> add = DLT645Helper.GetAddressByteFromString(address);
    if (!add.IsSuccess)
      return (OperateResult) add;
    OperateResult<byte[]> command = DLT645Helper.BuildDlt645EntireCommand("AAAAAAAAAAAA", dlt.DLTType == DLT645Type.DLT2007 ? (byte) 21 : (byte) 10, add.Content);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await dlt.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = DLT645Helper.CheckResponse(dlt, command.Content, read.Content);
    return check.IsSuccess ? (!SoftBasic.IsTwoBytesEquel(read.Content.SelectMiddle<byte>(1, 6), DLT645Helper.GetAddressByteFromString(address).Content) ? new OperateResult(StringResources.Language.DLTErrorWriteReadCheckFailed) : OperateResult.CreateSuccessResult()) : check;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.BroadcastTime(HslCommunication.Instrument.DLT.Helper.IDlt645,System.DateTime)" />
  public static async Task<OperateResult> BroadcastTimeAsync(
    IDlt645 dlt,
    DateTime dateTime,
    Func<byte[], bool, bool, Task<OperateResult<byte[]>>> func)
  {
    string hex = $"{dateTime.Second:D2}{dateTime.Minute:D2}{dateTime.Hour:D2}{dateTime.Day:D2}{dateTime.Month:D2}{dateTime.Year % 100:D2}";
    OperateResult<byte[]> command = DLT645Helper.BuildDlt645EntireCommand("999999999999", dlt.DLTType == DLT645Type.DLT2007 ? (byte) 8 : (byte) 8, hex.ToHexBytes());
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await func(command.Content, false, true);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.FreezeCommand(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public static async Task<OperateResult> FreezeCommandAsync(DLT645OverTcp dlt, string dataArea)
  {
    OperateResult<string, byte[]> analysis = DLT645Helper.AnalysisBytesAddress(dlt.DLTType, dataArea, dlt.Station);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    OperateResult<byte[]> command = DLT645Helper.BuildDlt645EntireCommand(analysis.Content1, (byte) 22, analysis.Content2);
    if (!command.IsSuccess)
      return (OperateResult) command;
    if (analysis.Content1 == "999999999999")
    {
      OperateResult<byte[]> operateResult = await dlt.ReadFromCoreServerAsync(command.Content, false, true);
      return (OperateResult) operateResult;
    }
    OperateResult<byte[]> read = await dlt.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? DLT645Helper.CheckResponse((IDlt645) dlt, command.Content, read.Content) : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.ChangeBaudRate(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public static async Task<OperateResult> ChangeBaudRateAsync(IDlt645 dlt, string baudRate)
  {
    byte code;
    OperateResult<byte[]> command = DLT645Helper.BuildChangeBaudRateCommand(dlt, baudRate, out code);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await dlt.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = DLT645Helper.CheckResponse(dlt, command.Content, read.Content);
    return check.IsSuccess ? ((int) read.Content[10] != (int) code ? new OperateResult(StringResources.Language.DLTErrorWriteReadCheckFailed) : OperateResult.CreateSuccessResult()) : check;
  }
}
