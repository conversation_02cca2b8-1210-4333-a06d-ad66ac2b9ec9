﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.Delixi.DTSU6606Serial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.ModBus;
using HslCommunication.Reflection;
using System;

#nullable disable
namespace HslCommunication.Instrument.Delixi;

/// <summary>DTSU6606型三相四线电子式电能表的Modbus-RTU通信协议</summary>
public class DTSU6606Serial : ModbusRtu
{
  /// <summary>
  /// 实例化一个Modbus-Rtu协议的客户端对象<br />
  /// Instantiate a client object of the Modbus-Rtu protocol
  /// </summary>
  public DTSU6606Serial()
  {
  }

  /// <summary>
  /// 指定客户端自己的站号来初始化<br />
  /// Specify the client's own station number to initialize
  /// </summary>
  /// <param name="station">客户端自身的站号</param>
  public DTSU6606Serial(byte station = 1)
    : base(station)
  {
  }

  /// <summary>
  /// 读取电表的电参数类，主要包含电压，电流，频率，有功功率，无功功率，视在功率，功率因数<br />
  /// Read the electrical parameters of the meter, including voltage, current, frequency, active power, reactive power, apparent power, and power factor
  /// </summary>
  /// <returns>包含是否成功的电表结果对象</returns>
  [HslMqttApi(ApiTopic = "ReadElectricalParameters", Description = "读取电表的电参数类，主要包含电压，电流，频率，有功功率，无功功率，视在功率，功率因数")]
  public OperateResult<ElectricalParameters> ReadElectricalParameters()
  {
    OperateResult<byte[]> operateResult = this.Read("768", (ushort) 23);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<ElectricalParameters>();
    try
    {
      return OperateResult.CreateSuccessResult<ElectricalParameters>(ElectricalParameters.ParseFromDelixi(operateResult.Content, this.ByteTransform));
    }
    catch (Exception ex)
    {
      return new OperateResult<ElectricalParameters>(ex.Message);
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"DTSU6606Serial[{this.PortName}]";
}
