﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.MegMeet.MegMeetHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.ModBus;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.MegMeet;

/// <summary>麦格米特PLC的辅助方法</summary>
public class MegMeetHelper
{
  internal static OperateResult<string> PraseMegMeetAddress(string address, byte modbusCode)
  {
    try
    {
      string station = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        station = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWithAndNumber("X"))
          return OperateResult.CreateSuccessResult<string>($"{station}x=2;{Convert.ToInt32(address.Substring(1), 8).ToString()}");
        if (address.StartsWithAndNumber("Y"))
          return OperateResult.CreateSuccessResult<string>(station + Convert.ToInt32(address.Substring(1), 8).ToString());
        if (address.StartsWithAndNumber("M"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 < 2048 /*0x0800*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 + 2000).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 2048 /*0x0800*/ + 12000).ToString());
        }
        if (address.StartsWithAndNumber("SM"))
        {
          int int32 = Convert.ToInt32(address.Substring(2));
          return int32 < 256 /*0x0100*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 + 4400).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 256 /*0x0100*/ + 30000).ToString());
        }
        if (address.StartsWithAndNumber("S"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 < 1024 /*0x0400*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 + 6000).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 1024 /*0x0400*/ + 31000).ToString());
        }
        if (address.StartsWithAndNumber("T"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 < 256 /*0x0100*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 + 8000).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 256 /*0x0100*/ + 11000).ToString());
        }
        if (address.StartsWithAndNumber("C"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 < 256 /*0x0100*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 + 9200).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 256 /*0x0100*/ + 10000).ToString());
        }
        string newAddress;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[2]
        {
          "D",
          "R"
        }, new int[2]{ 0, 13000 }, out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
      }
      else
      {
        if (address.StartsWithAndNumber("T"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 < 256 /*0x0100*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 + 9000).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 256 /*0x0100*/ + 11000).ToString());
        }
        if (address.StartsWithAndNumber("C"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          if (int32 < 200)
            return OperateResult.CreateSuccessResult<string>(station + (int32 + 9500).ToString());
          return int32 < 256 /*0x0100*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 * 2 - 200 + 9700).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 * 2 - 256 /*0x0100*/ + 10000).ToString());
        }
        if (address.StartsWithAndNumber("D"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return OperateResult.CreateSuccessResult<string>(station + int32.ToString());
        }
        if (address.StartsWithAndNumber("SD"))
        {
          int int32 = Convert.ToInt32(address.Substring(2));
          return int32 < 256 /*0x0100*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 + 8000).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 256 /*0x0100*/ + 12000).ToString());
        }
        if (address.StartsWithAndNumber("Z"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return OperateResult.CreateSuccessResult<string>(station + (int32 + 8500).ToString());
        }
        if (address.StartsWithAndNumber("R"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return OperateResult.CreateSuccessResult<string>(station + (int32 + 13000).ToString());
        }
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  private static List<CuttingAddress> GetBoolCuttingAddress()
  {
    return new List<CuttingAddress>()
    {
      new CuttingAddress("M", 2048 /*0x0800*/),
      new CuttingAddress("SM", 256 /*0x0100*/),
      new CuttingAddress("S", 1024 /*0x0400*/),
      new CuttingAddress("T", 256 /*0x0100*/),
      new CuttingAddress("C", 256 /*0x0100*/)
    };
  }

  private static List<CuttingAddress> GetWordCuttingAddress()
  {
    return new List<CuttingAddress>()
    {
      new CuttingAddress("SD", 256 /*0x0100*/),
      new CuttingAddress("T", 256 /*0x0100*/),
      new CuttingAddress("C", 256 /*0x0100*/)
    };
  }

  internal static OperateResult<bool[]> ReadBool(
    Func<string, ushort, OperateResult<bool[]>> readBoolFunc,
    string address,
    ushort length)
  {
    return HslHelper.ReadCuttingHelper<bool>(readBoolFunc, MegMeetHelper.GetBoolCuttingAddress(), address, length);
  }

  internal static async Task<OperateResult<bool[]>> ReadBoolAsync(
    Func<string, ushort, Task<OperateResult<bool[]>>> readBoolFunc,
    string address,
    ushort length)
  {
    OperateResult<bool[]> operateResult = await HslHelper.ReadCuttingAsyncHelper<bool>(readBoolFunc, MegMeetHelper.GetBoolCuttingAddress(), address, length);
    return operateResult;
  }

  internal static OperateResult<byte[]> Read(
    Func<string, ushort, OperateResult<byte[]>> readFunc,
    string address,
    ushort length)
  {
    return HslHelper.ReadCuttingHelper<byte>(readFunc, MegMeetHelper.GetWordCuttingAddress(), address, length);
  }

  internal static async Task<OperateResult<byte[]>> ReadAsync(
    Func<string, ushort, Task<OperateResult<byte[]>>> readFunc,
    string address,
    ushort length)
  {
    OperateResult<byte[]> operateResult = await HslHelper.ReadCuttingAsyncHelper<byte>(readFunc, MegMeetHelper.GetWordCuttingAddress(), address, length);
    return operateResult;
  }
}
