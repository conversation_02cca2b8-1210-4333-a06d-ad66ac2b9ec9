﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.TcpNetCommunication
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Net;
using System.Net.NetworkInformation;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>基于TCP、IP通信的类</summary>
public class TcpNetCommunication : BinaryCommunication
{
  private PipeTcpNet pipeTcpNet;
  private Lazy<Ping> ping = new Lazy<Ping>((Func<Ping>) (() => new Ping()));

  /// <summary>实例化一个默认的对象</summary>
  public TcpNetCommunication()
    : this("127.0.0.1", 5000)
  {
  }

  /// <summary>指定IP地址以及端口号信息来初始化对象</summary>
  /// <param name="ipAddress">IP地址信息，可以是IPv4, IPv6, 也可以是域名</param>
  /// <param name="port">设备方的端口号信息</param>
  public TcpNetCommunication(string ipAddress, int port)
  {
    this.pipeTcpNet = new PipeTcpNet();
    this.pipeTcpNet.IpAddress = ipAddress;
    this.pipeTcpNet.Port = port;
    this.CommunicationPipe = (CommunicationPipe) this.pipeTcpNet;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeTcpNet.ConnectTimeOut" />
  [HslMqttApi(HttpMethod = "GET", Description = "Gets or sets the timeout for the connection, in milliseconds")]
  public virtual int ConnectTimeOut
  {
    get
    {
      return this.CommunicationPipe is PipeTcpNet communicationPipe ? communicationPipe.ConnectTimeOut : this.pipeTcpNet.ConnectTimeOut;
    }
    set
    {
      PipeTcpNet communicationPipe;
      int num;
      if (value >= 0)
      {
        communicationPipe = this.CommunicationPipe as PipeTcpNet;
        num = communicationPipe != null ? 1 : 0;
      }
      else
        num = 0;
      if (num == 0)
        return;
      communicationPipe.ConnectTimeOut = value;
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeTcpNet.IpAddress" />
  [HslMqttApi(HttpMethod = "GET", Description = "Get or set the IP address of the remote server. If it is a local test, then it needs to be set to 127.0.0.1")]
  public virtual string IpAddress
  {
    get
    {
      return this.CommunicationPipe is PipeTcpNet communicationPipe ? communicationPipe.IpAddress : this.pipeTcpNet.IpAddress;
    }
    set
    {
      if (!(this.CommunicationPipe is PipeTcpNet communicationPipe))
        return;
      communicationPipe.IpAddress = value;
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeTcpNet.Port" />
  [HslMqttApi(HttpMethod = "GET", Description = "Gets or sets the port number of the server. The specific value depends on the configuration of the other party.")]
  public virtual int Port
  {
    get
    {
      return this.CommunicationPipe is PipeTcpNet communicationPipe ? communicationPipe.Port : this.pipeTcpNet.Port;
    }
    set
    {
      if (!(this.CommunicationPipe is PipeTcpNet communicationPipe))
        return;
      communicationPipe.Port = value;
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeTcpNet.LocalBinding" />
  public IPEndPoint LocalBinding
  {
    get
    {
      return this.CommunicationPipe is PipeTcpNet communicationPipe ? communicationPipe.LocalBinding : this.pipeTcpNet.LocalBinding;
    }
    set
    {
      if (!(this.CommunicationPipe is PipeTcpNet communicationPipe))
        return;
      communicationPipe.LocalBinding = value;
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeTcpNet.SocketKeepAliveTime" />
  public int SocketKeepAliveTime
  {
    get
    {
      return this.CommunicationPipe is PipeTcpNet communicationPipe ? communicationPipe.SocketKeepAliveTime : this.pipeTcpNet.SocketKeepAliveTime;
    }
    set
    {
      if (!(this.CommunicationPipe is PipeTcpNet communicationPipe))
        return;
      communicationPipe.SocketKeepAliveTime = value;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceTcpNet.IpAddressPing" />
  public IPStatus IpAddressPing() => this.ping.Value.Send(this.IpAddress).Status;

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceTcpNet.ConnectServer" />
  public OperateResult ConnectServer()
  {
    this.CommunicationPipe?.CloseCommunication();
    OperateResult<bool> operateResult = this.CommunicationPipe.OpenCommunication();
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    this.LogNet?.WriteDebug(this.ToString(), StringResources.Language.ConnectedSuccess);
    return operateResult.Content ? this.InitializationOnConnect() : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.TcpNetCommunication.ConnectServer" />
  public async Task<OperateResult> ConnectServerAsync()
  {
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = this.CommunicationPipe.CloseCommunicationAsync().ConfigureAwait(false);
    OperateResult operateResult1 = await configuredTaskAwaitable;
    OperateResult<bool> open = await this.CommunicationPipe.OpenCommunicationAsync().ConfigureAwait(false);
    if (!open.IsSuccess)
      return (OperateResult) open;
    this.LogNet?.WriteDebug(this.ToString(), StringResources.Language.ConnectedSuccess);
    if (!open.Content)
      return OperateResult.CreateSuccessResult();
    configuredTaskAwaitable = this.InitializationOnConnectAsync().ConfigureAwait(false);
    OperateResult operateResult = await configuredTaskAwaitable;
    return operateResult;
  }

  /// <summary>
  /// 手动断开与远程服务器的连接，如果当前是长连接模式，那么就会切换到短连接模式<br />
  /// Manually disconnect from the remote server, if it is currently in long connection mode, it will switch to short connection mode
  /// </summary>
  /// <returns>关闭连接，不需要查看IsSuccess属性查看</returns>
  /// <example>
  /// 直接关闭连接即可，基本上是不需要进行成功的判定
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkDoubleBase.cs" region="ConnectCloseExample" title="关闭连接结果" />
  /// </example>
  public OperateResult ConnectClose()
  {
    OperateResult operateResult = this.ExtraOnDisconnect();
    if (!operateResult.IsSuccess)
      return operateResult;
    this.LogNet?.WriteDebug(this.ToString(), StringResources.Language.Close);
    return this.CommunicationPipe.CloseCommunication();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.TcpNetCommunication.ConnectClose" />
  public async Task<OperateResult> ConnectCloseAsync()
  {
    OperateResult result = await this.ExtraOnDisconnectAsync().ConfigureAwait(false);
    if (!result.IsSuccess)
      return result;
    this.LogNet?.WriteDebug(this.ToString(), StringResources.Language.Close);
    OperateResult operateResult = await this.CommunicationPipe.CloseCommunicationAsync().ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"TcpNetCommunication<{this.CommunicationPipe}>";
}
