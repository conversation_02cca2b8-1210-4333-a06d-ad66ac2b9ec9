﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Keyence.KeyenceMcAsciiNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Address;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Profinet.Melsec.Helper;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Keyence;

/// <summary>
/// 基恩士PLC的数据通信类，使用QnA兼容3E帧的通信协议实现，使用ASCII的格式，地址格式需要进行转换成三菱的格式，详细参照备注说明<br />
/// Keyence PLC's data communication class is implemented using QnA compatible 3E frame communication protocol.
/// It uses ascii format. The address format needs to be converted to Mitsubishi format.
/// </summary>
/// <remarks>
/// <inheritdoc cref="T:HslCommunication.Profinet.Keyence.KeyenceMcNet" path="remarks" />
/// </remarks>
public class KeyenceMcAsciiNet : MelsecMcAsciiNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceMcNet.#ctor" />
  public KeyenceMcAsciiNet()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceMcNet.#ctor(System.String,System.Int32)" />
  public KeyenceMcAsciiNet(string ipAddress, int port)
    : base(ipAddress, port)
  {
  }

  /// <inheritdoc />
  public override OperateResult<McAddressData> McAnalysisAddress(
    string address,
    ushort length,
    bool isBit)
  {
    return McAddressData.ParseKeyenceFrom(address, length, isBit);
  }

  /// <inheritdoc />
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return KeyenceMcNet.CheckKeyenceBoolAddress(address) ? McHelper.ReadBool((IReadWriteMc) this, address, length, false) : base.ReadBool(address, length);
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, bool[] values)
  {
    return KeyenceMcNet.CheckKeyenceBoolAddress(address) ? McHelper.Write((IReadWriteMc) this, address, values, false) : base.Write(address, values);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (KeyenceMcNet.CheckKeyenceBoolAddress(address))
    {
      OperateResult<bool[]> operateResult = await McHelper.ReadBoolAsync((IReadWriteMc) this, address, length, false).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<bool[]> operateResult1 = await base.ReadBoolAsync(address, length).ConfigureAwait(false);
    return operateResult1;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool[] values)
  {
    if (KeyenceMcNet.CheckKeyenceBoolAddress(address))
    {
      OperateResult operateResult = await McHelper.WriteAsync((IReadWriteMc) this, address, values, false).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult operateResult1 = await base.WriteAsync(address, values).ConfigureAwait(false);
    return operateResult1;
  }

  /// <inheritdoc />
  public override string ToString() => $"KeyenceMcAsciiNet[{this.IpAddress}:{this.Port}]";
}
