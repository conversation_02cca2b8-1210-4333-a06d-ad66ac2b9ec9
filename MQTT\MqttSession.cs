﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttSession
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Pipe;
using HslCommunication.Core.Security;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// Mqtt的会话信息，包含了一些基本的信息内容，客户端的IP地址及端口，Client ID，用户名，活动时间，是否允许发布数据等等<br />
/// Mqtt's session information includes some basic information content, the client's IP address and port, Client ID, user name, activity time, whether it is allowed to publish data, etc.
/// </summary>
public class MqttSession : ISessionContext
{
  private object objLock = new object();

  /// <summary>
  /// 实例化一个对象，指定ip地址及端口，以及协议内容<br />
  /// Instantiate an object, specify ip address and port, and protocol content
  /// </summary>
  /// <param name="endPoint">远程客户端的IP地址</param>
  /// <param name="protocol">协议信息</param>
  public MqttSession(IPEndPoint endPoint, string protocol)
  {
    this.Topics = new List<string>();
    this.ActiveTime = DateTime.Now;
    this.OnlineTime = DateTime.Now;
    this.ActiveTimeSpan = TimeSpan.FromSeconds(1000000.0);
    this.EndPoint = endPoint;
    this.Protocol = protocol;
  }

  /// <summary>
  /// 远程的ip地址端口信息<br />
  /// Remote ip address port information
  /// </summary>
  public IPEndPoint EndPoint { get; set; }

  /// <summary>
  /// 当前接收的客户端ID信息<br />
  /// Client ID information currently received
  /// </summary>
  public string ClientId { get; set; }

  /// <summary>
  /// 当前客户端的激活时间<br />
  /// The activation time of the current client
  /// </summary>
  public DateTime ActiveTime { get; set; }

  /// <summary>
  /// 获取当前的客户端的上线时间<br />
  /// Get the online time of the current client
  /// </summary>
  public DateTime OnlineTime { get; private set; }

  /// <summary>
  /// 两次活动的最小时间间隔<br />
  /// Minimum time interval between two activities
  /// </summary>
  public TimeSpan ActiveTimeSpan { get; set; }

  /// <summary>当前客户端绑定的套接字对象</summary>
  internal PipeTcpNet MqttPipe { get; set; }

  /// <summary>
  /// 当前客户端订阅的所有的Topic信息<br />
  /// All Topic information subscribed by the current client
  /// </summary>
  private List<string> Topics { get; set; }

  /// <summary>
  /// 当前的用户名<br />
  /// Current username
  /// </summary>
  public string UserName { get; set; }

  /// <summary>
  /// 当前的协议信息，一般为 "MQTT"，如果是同步客户端那么是 "HUSL"，如果是文件客户端就是 "FILE"<br />
  /// The current protocol information, generally "MQTT", if it is a synchronous client then it is "HUSL", if it is a file client it is "FILE"
  /// </summary>
  public string Protocol { get; private set; }

  /// <summary>
  /// 遗嘱主题<br />
  /// will topic
  /// </summary>
  /// <remarks>当前的会话如果因为非正常原因下线的时候，服务器立即发布该客户端的遗嘱主题及数据信息</remarks>
  public string WillTopic { get; set; }

  /// <summary>
  /// 遗嘱的消息内容<br />
  /// The message content of the will
  /// </summary>
  /// <remarks>当前的会话如果因为非正常原因下线的时候，服务器立即发布该客户端的遗嘱主题及数据信息</remarks>
  public byte[] WillMessage { get; set; }

  /// <summary>
  /// 获取或设置当前的会话是否拥有开发者权限，在开发者权限下，可以遍历接口信息，默认只有admin账户支持开发者权限，如果自定义设置账户权限，则需要在会话登录的事件里，将本属性值设置为 True<br />
  /// Obtain or set whether the current session has developer permissions, under the developer permissions, you can traverse the interface information, by default,
  /// only the admin account supports developer permissions, if you customize the account permissions, you need to set the value of this property to True in the session login event
  /// </summary>
  public bool DeveloperPermissions { get; set; } = false;

  /// <summary>获取设置客户端的加密信息</summary>
  public bool IsAesCryptography => this.AesCryptography != null;

  /// <summary>获取或设置当前客户端的AES加密的对象信息</summary>
  internal AesCryptography AesCryptography { get; set; }

  /// <summary>
  /// 当前的会话信息关联的自定义信息<br />
  /// Custom information associated with the current session information
  /// </summary>
  public object Tag { get; set; }

  /// <summary>
  /// 获取或设置当前的MQTT客户端是否允许发布消息，默认为False，如果设置为True，就是禁止发布消息，服务器不会触发收到消息的事件。<br />
  /// Gets or sets whether the current MQTT client is allowed to publish messages, the default is False,
  /// if set to True, it is forbidden to publish messages, The server does not trigger the event of receiving a message.
  /// </summary>
  public bool ForbidPublishTopic { get; set; }

  /// <summary>
  /// 检查当前的会话对象里是否订阅了指定的主题内容<br />
  /// Check whether the specified topic content is subscribed in the current session object
  /// </summary>
  /// <param name="topic">主题信息</param>
  /// <param name="willcard">订阅的主题是否使用了通配符</param>
  /// <returns>如果订阅了，返回 True, 否则，返回 False</returns>
  public bool IsClientSubscribe(string topic, bool willcard)
  {
    bool flag = false;
    lock (this.objLock)
    {
      if (willcard)
      {
        for (int index = 0; index < this.Topics.Count; ++index)
        {
          if (MqttHelper.CheckMqttTopicWildcards(topic, this.Topics[index]))
          {
            flag = true;
            break;
          }
        }
      }
      else
        flag = this.Topics.Contains(topic);
    }
    return flag;
  }

  /// <summary>
  /// 获取当前客户端订阅的所有的Topic信息<br />
  /// Get all Topic information subscribed by the current client
  /// </summary>
  /// <returns>主题列表</returns>
  public string[] GetTopics()
  {
    string[] array;
    lock (this.objLock)
      array = this.Topics.ToArray();
    return array;
  }

  /// <summary>
  /// 当前的会话信息新增一个订阅的主题信息<br />
  /// The current session information adds a subscribed topic information
  /// </summary>
  /// <param name="topic">主题的信息</param>
  public void AddSubscribe(string topic)
  {
    lock (this.objLock)
    {
      if (this.Topics.Contains(topic))
        return;
      this.Topics.Add(topic);
    }
  }

  /// <summary>
  /// 当前的会话信息新增多个订阅的主题信息<br />
  /// The current session information adds multiple subscribed topic information
  /// </summary>
  /// <param name="topics">主题的信息</param>
  public void AddSubscribe(string[] topics)
  {
    if (topics == null)
      return;
    lock (this.objLock)
    {
      for (int index = 0; index < topics.Length; ++index)
      {
        if (!this.Topics.Contains(topics[index]))
          this.Topics.Add(topics[index]);
      }
    }
  }

  /// <summary>移除会话信息的一个订阅的主题</summary>
  /// <param name="topic">主题</param>
  public void RemoveSubscribe(string topic)
  {
    lock (this.objLock)
    {
      if (!this.Topics.Contains(topic))
        return;
      this.Topics.Remove(topic);
    }
  }

  /// <summary>
  /// 移除会话信息的一个订阅的主题<br />
  /// Remove a subscribed topic from session information
  /// </summary>
  /// <param name="topics">主题</param>
  public void RemoveSubscribe(string[] topics)
  {
    if (topics == null)
      return;
    lock (this.objLock)
    {
      for (int index = 0; index < topics.Length; ++index)
      {
        if (this.Topics.Contains(topics[index]))
          this.Topics.Remove(topics[index]);
      }
    }
  }

  /// <summary>
  /// 获取当前的会话信息，包含在线时间的信息<br />
  /// Get current session information, including online time information
  /// </summary>
  /// <returns>会话信息，包含在线时间</returns>
  public string GetSessionOnlineInfo()
  {
    StringBuilder stringBuilder = new StringBuilder(this.ToString());
    stringBuilder.Append($" [{SoftBasic.GetTimeSpanDescription(DateTime.Now - this.OnlineTime)}]");
    return stringBuilder.ToString();
  }

  /// <summary>
  /// 发送指定的MQTT命令到会话中去，可以指定头子节，变量头信息，数据负载，返回是否发送成功<br />
  /// Send the specified MQTT command to the session. You can specify the header section, variable header information, data load, and return whether the sending was successful
  /// </summary>
  /// <param name="head">头子节信息</param>
  /// <param name="variableHeader">数据头信息</param>
  /// <param name="payLoad">数据负载</param>
  /// <param name="aesCryptography">aes加密信息，如果有的话</param>
  /// <returns>是否发送成功</returns>
  public OperateResult SendMqttCommand(
    byte head,
    byte[] variableHeader,
    byte[] payLoad,
    AesCryptography aesCryptography = null)
  {
    OperateResult<byte[]> operateResult = MqttHelper.BuildMqttCommand(head, variableHeader, payLoad, aesCryptography);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    return this.MqttPipe == null ? new OperateResult("Pipe is Null") : this.MqttPipe.Send(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSession.SendMqttCommand(System.Byte,System.Byte[],System.Byte[],HslCommunication.Core.Security.AesCryptography)" />
  /// <param name="control">控制码</param>
  /// <param name="flags">标记</param>
  /// <param name="variableHeader">可变头的字节内容</param>
  /// <param name="payLoad">负载数据</param>
  /// <param name="aesCryptography">AES数据加密对象</param>
  public OperateResult SendMqttCommand(
    byte control,
    byte flags,
    byte[] variableHeader,
    byte[] payLoad,
    AesCryptography aesCryptography = null)
  {
    control <<= 4;
    return this.SendMqttCommand((byte) ((uint) control | (uint) flags), variableHeader, payLoad, aesCryptography);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSession.SendMqttCommand(System.Byte,System.Byte[],System.Byte[],HslCommunication.Core.Security.AesCryptography)" />
  public async Task<OperateResult> SendMqttCommandAsync(
    byte head,
    byte[] variableHeader,
    byte[] payLoad,
    AesCryptography aesCryptography = null)
  {
    OperateResult<byte[]> command = MqttHelper.BuildMqttCommand(head, variableHeader, payLoad, aesCryptography);
    if (!command.IsSuccess)
      return (OperateResult) command;
    if (this.MqttPipe == null)
      return new OperateResult("Pipe is Null");
    OperateResult operateResult = await this.MqttPipe.SendAsync(command.Content);
    return operateResult;
  }

  /// <summary>
  /// 获取当前会话的基本信息<br />
  /// Obtain basic information about the current session
  /// </summary>
  /// <returns>用于JSON发送的会话的基本信息</returns>
  public MqttSessionInfo GetSessionInfo()
  {
    return new MqttSessionInfo()
    {
      EndPoint = this.EndPoint.ToString(),
      ClientId = this.ClientId,
      ActiveTime = this.ActiveTime,
      OnlineTime = this.OnlineTime,
      Topics = this.Topics.ToArray(),
      UserName = this.UserName,
      Protocol = this.Protocol,
      WillTopic = this.WillTopic,
      DeveloperPermissions = this.DeveloperPermissions,
      IsAesCryptography = this.IsAesCryptography,
      ForbidPublishTopic = this.ForbidPublishTopic
    };
  }

  /// <inheritdoc />
  public override string ToString()
  {
    StringBuilder stringBuilder = new StringBuilder($"{this.Protocol} Session[IP:{this.EndPoint}]");
    if (!string.IsNullOrEmpty(this.ClientId))
      stringBuilder.Append($" [ID:{this.ClientId}]");
    if (!string.IsNullOrEmpty(this.UserName))
      stringBuilder.Append($" [Name:{this.UserName}]");
    if (this.IsAesCryptography)
      stringBuilder.Append("[RSA/AES]");
    return stringBuilder.ToString();
  }
}
