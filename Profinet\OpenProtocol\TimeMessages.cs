﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.TimeMessages
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>Time messages</summary>
public class TimeMessages
{
  private OpenProtocolNet openProtocol;

  /// <summary>指定Open通信类实例化一个对象</summary>
  /// <param name="openProtocol">开放协议的对象</param>
  public TimeMessages(OpenProtocolNet openProtocol) => this.openProtocol = openProtocol;

  /// <summary>Read time request.</summary>
  /// <returns>包含时间的结果对象</returns>
  public OperateResult<DateTime> ReadTimeUpload()
  {
    OperateResult<string> result = this.openProtocol.ReadCustomer(80 /*0x50*/, 1, -1, -1, (List<string>) null);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<DateTime>((OperateResult) result) : OperateResult.CreateSuccessResult<DateTime>(DateTime.ParseExact(result.Content.Substring(20, 19), "yyyy-MM-dd:HH:mm:ss", (IFormatProvider) null));
  }

  /// <summary>Set the time in the controller.</summary>
  /// <param name="dateTime">指定的时间</param>
  /// <returns>是否设置成功的结果对象</returns>
  public OperateResult SetTime(DateTime dateTime)
  {
    return (OperateResult) this.openProtocol.ReadCustomer(82, 1, -1, -1, new List<string>()
    {
      dateTime.ToString("yyyy-MM-dd:HH:mm:ss")
    });
  }
}
