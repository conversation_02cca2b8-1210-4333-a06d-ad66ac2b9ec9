﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Fuji.FujiSPBOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Fuji;

/// <summary>
/// 富士PLC的SPB协议，详细的地址信息见api文档说明，地址可以携带站号信息，例如：s=2;D100，PLC侧需要配置无BCC计算，包含0D0A结束码<br />
/// Fuji PLC's SPB protocol. For detailed address information, see the api documentation,
/// The address can carry station number information, for example: s=2;D100, PLC side needs to be configured with no BCC calculation, including 0D0A end code
/// </summary>
public class FujiSPBOverTcp : DeviceTcpNet
{
  private byte station = 1;

  /// <summary>
  /// 使用默认的构造方法实例化对象<br />
  /// Instantiate the object using the default constructor
  /// </summary>
  public FujiSPBOverTcp()
  {
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.SleepTime = 20;
  }

  /// <summary>
  /// 使用指定的ip地址和端口来实例化一个对象<br />
  /// Instantiate an object with the specified IP address and port
  /// </summary>
  /// <param name="ipAddress">设备的Ip地址</param>
  /// <param name="port">设备的端口号</param>
  public FujiSPBOverTcp(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new FujiSPBMessage();

  /// <summary>
  /// PLC的站号信息<br />
  /// PLC station number information
  /// </summary>
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return FujiSPBHelper.Read((IReadWriteDevice) this, this.station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return FujiSPBHelper.Write((IReadWriteDevice) this, this.station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return FujiSPBHelper.ReadBool((IReadWriteDevice) this, this.station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return FujiSPBHelper.Write((IReadWriteDevice) this, this.station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await FujiSPBHelper.ReadAsync((IReadWriteDevice) this, this.station, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await FujiSPBHelper.WriteAsync((IReadWriteDevice) this, this.station, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await FujiSPBHelper.ReadBoolAsync((IReadWriteDevice) this, this.station, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await FujiSPBHelper.WriteAsync((IReadWriteDevice) this, this.station, address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"FujiSPBOverTcp[{this.IpAddress}:{this.Port}]";
}
