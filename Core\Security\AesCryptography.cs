﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Security.AesCryptography
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Security.Cryptography;
using System.Text;

#nullable disable
namespace HslCommunication.Core.Security;

/// <summary>
/// 实例化一个AES加密解密的对象，默认 <see cref="F:System.Security.Cryptography.CipherMode.ECB" /> 模式的对象
/// </summary>
public class AesCryptography : ICryptography
{
  private ICryptoTransform encryptTransform;
  private ICryptoTransform decryptTransform;
  private RijndaelManaged rijndael;
  private string key;

  /// <summary>
  /// 使用指定的密钥实例化一个AES加密解密的对象，密钥由32位数字或字母组成，例如 ********************************
  /// </summary>
  /// <param name="key">密钥</param>
  /// <param name="mode">加密的模式，默认为 <see cref="F:System.Security.Cryptography.CipherMode.ECB" /></param>
  public AesCryptography(string key, CipherMode mode = CipherMode.ECB)
  {
    this.key = key;
    RijndaelManaged rijndaelManaged = new RijndaelManaged();
    rijndaelManaged.Key = Encoding.UTF8.GetBytes(key);
    rijndaelManaged.Mode = mode;
    rijndaelManaged.Padding = PaddingMode.PKCS7;
    this.rijndael = rijndaelManaged;
    this.encryptTransform = this.rijndael.CreateEncryptor();
    this.decryptTransform = this.rijndael.CreateDecryptor();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Encrypt(System.Byte[])" />
  public byte[] Encrypt(byte[] data)
  {
    return data == null ? (byte[]) null : this.encryptTransform.TransformFinalBlock(data, 0, data.Length);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Decrypt(System.Byte[])" />
  public byte[] Decrypt(byte[] data)
  {
    return data == null ? (byte[]) null : this.decryptTransform.TransformFinalBlock(data, 0, data.Length);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Encrypt(System.String)" />
  public string Encrypt(string data)
  {
    return Convert.ToBase64String(this.Encrypt(string.IsNullOrEmpty(data) ? new byte[0] : Encoding.UTF8.GetBytes(data)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Decrypt(System.String)" />
  public string Decrypt(string data)
  {
    return Encoding.UTF8.GetString(this.Decrypt(Convert.FromBase64String(data)));
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Security.ICryptography.Key" />
  public string Key => this.key;
}
