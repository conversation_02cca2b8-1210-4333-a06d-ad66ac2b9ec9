﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.LSCpuInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.LSIS;

/// <summary>
/// It is determined to be the XGK/I/R series through a reserved area
/// </summary>
public enum LSCpuInfo
{
  XGK = 1,
  XGI = 2,
  XGR = 3,
  XGB_MK = 4,
  XGB_IEC = 5,
  XGB = 6,
}
