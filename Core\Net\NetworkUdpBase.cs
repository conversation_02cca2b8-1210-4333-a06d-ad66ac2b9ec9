﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.NetworkUdpBase
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Pipe;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>
/// 基于Udp的应答式通信类<br />
/// Udp - based responsive communication class
/// </summary>
public class NetworkUdpBase : NetworkBase
{
  /// <inheritdoc cref="F:HslCommunication.Core.Net.NetworkDoubleBase.LogMsgFormatBinary" />
  protected bool LogMsgFormatBinary = true;
  private int connectErrorCount = 0;
  private PipeSocket pipeSocket;

  /// <summary>
  /// 实例化一个默认的方法<br />
  /// Instantiate a default method
  /// </summary>
  public NetworkUdpBase()
  {
    this.ReceiveTimeout = 5000;
    this.ConnectionId = SoftBasic.GetUniqueStringByGuidAndRandom();
    this.pipeSocket = new PipeSocket();
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.IpAddress" />
  public virtual string IpAddress
  {
    get => this.pipeSocket.IpAddress;
    set => this.pipeSocket.IpAddress = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.Port" />
  public virtual int Port
  {
    get => this.pipeSocket.Port;
    set => this.pipeSocket.Port = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.ReceiveTimeOut" />
  public int ReceiveTimeout { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.ConnectionId" />
  public string ConnectionId { get; set; }

  /// <summary>
  /// 获取或设置一次接收时的数据长度，默认2KB数据长度，特殊情况的时候需要调整<br />
  /// Gets or sets the length of data received at a time. The default length is 2KB
  /// </summary>
  public int ReceiveCacheLength { get; set; } = 2048 /*0x0800*/;

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.LocalBinding" />
  public IPEndPoint LocalBinding { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.PackCommandWithHeader(System.Byte[])" />
  public virtual byte[] PackCommandWithHeader(byte[] command) => command;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.UnpackResponseContent(System.Byte[],System.Byte[])" />
  public virtual OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return OperateResult.CreateSuccessResult<byte[]>(response);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkUdpBase.ReadFromCoreServer(System.Byte[],System.Boolean,System.Boolean)" />
  public virtual OperateResult<byte[]> ReadFromCoreServer(byte[] send)
  {
    return this.ReadFromCoreServer(send, true, true);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteDevice.ReadFromCoreServer(System.Collections.Generic.IEnumerable{System.Byte[]})" />
  public OperateResult<byte[]> ReadFromCoreServer(IEnumerable<byte[]> send)
  {
    return NetSupport.ReadFromCoreServer(send, new Func<byte[], OperateResult<byte[]>>(this.ReadFromCoreServer));
  }

  /// <summary>从UDP接收相关的数据信息，允许子类重写实现一些更加特殊的功能验证。</summary>
  /// <param name="socket">网络套接字</param>
  /// <param name="timeOut">超时时间</param>
  /// <param name="send">发送的报文信息</param>
  /// <returns>返回接收到的报文</returns>
  protected virtual byte[] ReceiveFromUdpSocket(Socket socket, int timeOut, byte[] send)
  {
    socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReceiveTimeout, this.ReceiveTimeout);
    EndPoint remoteEP = (EndPoint) new IPEndPoint(new IPEndPoint(IPAddress.Parse(this.IpAddress), this.Port).AddressFamily == AddressFamily.InterNetworkV6 ? IPAddress.IPv6Any : IPAddress.Any, 0);
    byte[] buffer = new byte[this.ReceiveCacheLength];
    int from = socket.ReceiveFrom(buffer, ref remoteEP);
    return buffer.SelectBegin<byte>(from);
  }

  /// <summary>
  /// 核心的数据交互读取，发数据发送到通道上去，然后从通道上接收返回的数据<br />
  /// The core data is read interactively, the data is sent to the serial port, and the returned data is received from the serial port
  /// </summary>
  /// <param name="send">完整的报文内容</param>
  /// <param name="hasResponseData">是否有等待的数据返回，默认为 true</param>
  /// <param name="usePackAndUnpack">是否需要对命令重新打包，在重写<see cref="M:HslCommunication.Core.Net.NetworkUdpBase.PackCommandWithHeader(System.Byte[])" />方法后才会有影响</param>
  /// <returns>是否成功的结果对象</returns>
  public virtual OperateResult<byte[]> ReadFromCoreServer(
    byte[] send,
    bool hasResponseData,
    bool usePackAndUnpack)
  {
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    byte[] numArray = usePackAndUnpack ? this.PackCommandWithHeader(send) : send;
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Send} : {(this.LogMsgFormatBinary ? SoftBasic.ByteToHexString(numArray) : Encoding.ASCII.GetString(numArray))}");
    if (this.pipeSocket.LockingTick > HslHelper.LockLimit)
      return new OperateResult<byte[]>(StringResources.Language.TooManyLock);
    this.pipeSocket.PipeLockEnter();
    try
    {
      IPEndPoint ipEndPoint = new IPEndPoint(IPAddress.Parse(this.IpAddress), this.Port);
      OperateResult<Socket> availableSocketAsync = this.GetAvailableSocketAsync(ipEndPoint);
      if (!availableSocketAsync.IsSuccess)
      {
        this.pipeSocket.PipeLockLeave();
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) availableSocketAsync);
      }
      availableSocketAsync.Content.SendTo(numArray, numArray.Length, SocketFlags.None, (EndPoint) ipEndPoint);
      if (this.ReceiveTimeout < 0)
      {
        this.pipeSocket.PipeLockLeave();
        return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      }
      if (!hasResponseData)
      {
        this.pipeSocket.PipeLockLeave();
        return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      }
      byte[] fromUdpSocket = this.ReceiveFromUdpSocket(availableSocketAsync.Content, this.ReceiveTimeout, numArray);
      this.pipeSocket.PipeLockLeave();
      this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Receive} : {(this.LogMsgFormatBinary ? SoftBasic.ByteToHexString(fromUdpSocket) : Encoding.ASCII.GetString(fromUdpSocket))}");
      this.connectErrorCount = 0;
      this.pipeSocket.IsSocketError = false;
      try
      {
        return usePackAndUnpack ? this.UnpackResponseContent(numArray, fromUdpSocket) : OperateResult.CreateSuccessResult<byte[]>(fromUdpSocket);
      }
      catch (Exception ex)
      {
        return new OperateResult<byte[]>("UnpackResponseContent failed: " + ex.Message);
      }
    }
    catch (Exception ex)
    {
      this.pipeSocket.ChangePorts();
      this.pipeSocket.IsSocketError = true;
      if (this.connectErrorCount < 100000000)
        ++this.connectErrorCount;
      this.pipeSocket.PipeLockLeave();
      return new OperateResult<byte[]>(-this.connectErrorCount, ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkUdpBase.ReadFromCoreServer(System.Byte[],System.Boolean,System.Boolean)" />
  public async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(byte[] value)
  {
    OperateResult<byte[]> operateResult = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.ReadFromCoreServer(value)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkUdpBase.ReadFromCoreServer(System.Collections.Generic.IEnumerable{System.Byte[]})" />
  public async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(IEnumerable<byte[]> send)
  {
    OperateResult<byte[]> operateResult = await NetSupport.ReadFromCoreServerAsync(send, new Func<byte[], Task<OperateResult<byte[]>>>(this.ReadFromCoreServerAsync));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.IpAddressPing" />
  public IPStatus IpAddressPing() => new Ping().Send(this.IpAddress).Status;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.SetPipeSocket(HslCommunication.Core.Pipe.PipeSocket)" />
  public void SetPipeSocket(PipeSocket pipeSocket)
  {
    if (this.pipeSocket == null)
      return;
    this.pipeSocket = pipeSocket;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.GetPipeSocket" />
  public PipeSocket GetPipeSocket() => this.pipeSocket;

  private OperateResult<Socket> GetAvailableSocketAsync(IPEndPoint endPoint)
  {
    if (!this.pipeSocket.IsConnectitonError())
      return OperateResult.CreateSuccessResult<Socket>(this.pipeSocket.Socket);
    OperateResult result;
    try
    {
      this.pipeSocket.Socket?.Close();
      Socket socket = new Socket(endPoint.AddressFamily, SocketType.Dgram, ProtocolType.Udp);
      if (this.LocalBinding != null)
        socket.Bind((EndPoint) this.LocalBinding);
      this.pipeSocket.Socket = socket;
      result = OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      this.pipeSocket.IsSocketError = true;
      result = new OperateResult(ex.Message);
    }
    if (!result.IsSuccess)
    {
      this.pipeSocket.IsSocketError = true;
      return OperateResult.CreateFailedResult<Socket>(result);
    }
    this.pipeSocket.IsSocketError = false;
    return OperateResult.CreateSuccessResult<Socket>(this.pipeSocket.Socket);
  }

  /// <inheritdoc />
  public override string ToString() => $"NetworkUdpBase[{this.IpAddress}:{this.Port}]";
}
