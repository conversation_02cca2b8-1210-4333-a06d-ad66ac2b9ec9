﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.FormPopup
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>一个用于消息弹出显示的类</summary>
public class FormPopup : Form
{
  private static List<FormPopup> FormsPopup = new List<FormPopup>();
  private Timer time = (Timer) null;
  private const int AW_HOR_POSITIVE = 1;
  private const int AW_HOR_NEGATIVE = 2;
  private const int AW_VER_POSITIVE = 4;
  private const int AW_VER_NEGATIVE = 8;
  private const int AW_CENTER = 16 /*0x10*/;
  private const int AW_HIDE = 65536 /*0x010000*/;
  private const int AW_ACTIVE = 131072 /*0x020000*/;
  private const int AW_SLIDE = 262144 /*0x040000*/;
  private const int AW_BLEND = 524288 /*0x080000*/;
  /// <summary>Required designer variable.</summary>
  private IContainer components = (IContainer) null;
  private Label label2;
  private Label label1;

  /// <summary>新增一个显示的弹出窗口</summary>
  /// <param name="form"></param>
  private static void AddNewForm(FormPopup form)
  {
    try
    {
      foreach (FormPopup formPopup in FormPopup.FormsPopup)
        formPopup.LocationUpMove();
      FormPopup.FormsPopup.Add(form);
    }
    catch (Exception ex)
    {
      Console.WriteLine(SoftBasic.GetExceptionMessage(ex));
    }
  }

  /// <summary>重置所有弹出窗口的位置</summary>
  private static void ResetLocation()
  {
    try
    {
      for (int index = 0; index < FormPopup.FormsPopup.Count; ++index)
        FormPopup.FormsPopup[index].LocationUpMove(FormPopup.FormsPopup.Count - 1 - index);
    }
    catch (Exception ex)
    {
      Console.WriteLine(SoftBasic.GetExceptionMessage(ex));
    }
  }

  /// <summary>实例化一个窗口信息弹出的对象</summary>
  public FormPopup() => this.InitializeComponent();

  /// <summary>实例化一个窗口信息弹出的对象</summary>
  /// <param name="infotext">需要显示的文本</param>
  public FormPopup(string infotext)
  {
    this.InitializeComponent();
    this.InfoText = infotext;
  }

  /// <summary>实例化一个窗口信息弹出的对象</summary>
  /// <param name="infotext">需要显示的文本</param>
  /// <param name="infocolor">文本的颜色</param>
  public FormPopup(string infotext, Color infocolor)
  {
    this.InitializeComponent();
    this.InfoText = infotext;
    this.InfoColor = infocolor;
  }

  /// <summary>实例化一个窗口信息弹出的对象</summary>
  /// <param name="infotext">需要显示的文本</param>
  /// <param name="infocolor">文本的颜色</param>
  /// <param name="existTime">指定窗口多少时间后消失，单位毫秒</param>
  public FormPopup(string infotext, Color infocolor, int existTime)
  {
    this.InitializeComponent();
    this.InfoText = infotext;
    this.InfoColor = infocolor;
    this.InfoExistTime = existTime;
  }

  private string InfoText { get; set; } = "This is a test message";

  private Color InfoColor { get; set; } = Color.DimGray;

  private int InfoExistTime { get; set; } = -1;

  private void FormPopup_Load(object sender, EventArgs e)
  {
    this.label1.Text = this.InfoText;
    this.label1.ForeColor = this.InfoColor;
    this.label2.Text = StringResources.Language.Close;
    FormPopup.AddNewForm(this);
    this.Location = new Point(Screen.PrimaryScreen.WorkingArea.Right - this.Width, Screen.PrimaryScreen.WorkingArea.Bottom - this.Height);
    FormPopup.AnimateWindow(this.Handle, 1000, 262152 /*0x040008*/);
    this.TopMost = true;
    if (this.InfoExistTime <= 100)
      return;
    this.time = new Timer();
    this.time.Interval = this.InfoExistTime;
    this.time.Tick += (EventHandler) ((_param1, _param2) =>
    {
      if (!this.IsHandleCreated)
        return;
      this.time.Dispose();
      FormPopup.AnimateWindow(this.Handle, 1000, 589824 /*0x090000*/);
      this.Close();
    });
    this.time.Start();
  }

  /// <summary>窗体的位置进行向上调整</summary>
  public void LocationUpMove()
  {
    Point location = this.Location;
    int x = location.X;
    location = this.Location;
    int y = location.Y - this.Height;
    this.Location = new Point(x, y);
  }

  /// <summary>窗体的位置进行向上调整</summary>
  public void LocationUpMove(int index)
  {
    this.Location = new Point(this.Location.X, Screen.PrimaryScreen.WorkingArea.Bottom - this.Height - index * this.Height);
  }

  private void FormPopup_Closing(object sender, FormClosingEventArgs e)
  {
    try
    {
      this.time.Enabled = false;
      FormPopup.FormsPopup.Remove(this);
      FormPopup.ResetLocation();
    }
    catch (Exception ex)
    {
      Console.WriteLine(SoftBasic.GetExceptionMessage(ex));
    }
  }

  [DllImport("user32")]
  private static extern bool AnimateWindow(IntPtr hwnd, int dwTime, int dwFlags);

  private void FormPopup_Paint(object sender, PaintEventArgs e)
  {
    Graphics graphics = e.Graphics;
    graphics.FillRectangle(Brushes.SkyBlue, new Rectangle(0, 0, this.Width - 1, 30));
    StringFormat format = new StringFormat()
    {
      Alignment = StringAlignment.Near,
      LineAlignment = StringAlignment.Center
    };
    graphics.DrawString(StringResources.Language.MessageTip, this.label2.Font, Brushes.DimGray, (RectangleF) new Rectangle(5, 0, this.Width - 1, 30), format);
    graphics.DrawRectangle(Pens.DimGray, 0, 0, this.Width - 1, this.Height - 1);
  }

  private void label2_Click(object sender, EventArgs e) => this.Close();

  private void label2_MouseEnter(object sender, EventArgs e)
  {
    SoftAnimation.BeginBackcolorAnimation((Control) this.label2, Color.Tomato, 100);
  }

  private void label2_MouseLeave(object sender, EventArgs e)
  {
    SoftAnimation.BeginBackcolorAnimation((Control) this.label2, Color.MistyRose, 100);
  }

  /// <summary>Clean up any resources being used.</summary>
  /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  /// <summary>
  /// Required method for Designer support - do not modify
  /// the contents of this method with the code editor.
  /// </summary>
  private void InitializeComponent()
  {
    this.label2 = new Label();
    this.label1 = new Label();
    this.SuspendLayout();
    this.label2.BackColor = Color.MistyRose;
    this.label2.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.label2.Location = new Point(287, 4);
    this.label2.Name = "label2";
    this.label2.Size = new Size(41, 21);
    this.label2.TabIndex = 1;
    this.label2.Text = "关闭";
    this.label2.TextAlign = ContentAlignment.MiddleCenter;
    this.label2.Click += new EventHandler(this.label2_Click);
    this.label2.MouseEnter += new EventHandler(this.label2_MouseEnter);
    this.label2.MouseLeave += new EventHandler(this.label2_MouseLeave);
    this.label1.Font = new Font("微软雅黑", 10.5f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.label1.ForeColor = Color.DimGray;
    this.label1.Location = new Point(12, 30);
    this.label1.Name = "label1";
    this.label1.Size = new Size(309, 119);
    this.label1.TabIndex = 2;
    this.label1.Text = "这是一条测试消息";
    this.label1.TextAlign = ContentAlignment.MiddleCenter;
    this.AutoScaleDimensions = new SizeF(10f, 21f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.ClientSize = new Size(333, 163);
    this.Controls.Add((Control) this.label1);
    this.Controls.Add((Control) this.label2);
    this.Font = new Font("微软雅黑", 12f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.FormBorderStyle = FormBorderStyle.None;
    this.Margin = new Padding(4, 5, 4, 5);
    this.MaximizeBox = false;
    this.MaximumSize = new Size(333, 163);
    this.MinimizeBox = false;
    this.MinimumSize = new Size(333, 163);
    this.Name = nameof (FormPopup);
    this.ShowIcon = false;
    this.ShowInTaskbar = false;
    this.Text = "消息";
    this.FormClosing += new FormClosingEventHandler(this.FormPopup_Closing);
    this.Load += new EventHandler(this.FormPopup_Load);
    this.Paint += new PaintEventHandler(this.FormPopup_Paint);
    this.ResumeLayout(false);
  }
}
