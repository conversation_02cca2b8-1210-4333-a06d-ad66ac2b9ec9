﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.RemoteConnectInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Net;
using System.Text;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>DTU远程连接的一些信息</summary>
public class RemoteConnectInfo
{
  private bool ack = false;
  private string dtuId = string.Empty;

  /// <summary>实例化一个远程连接信息对象，指定自定义的远程 DTU 注册包</summary>
  /// <param name="ipAddress">IP地址</param>
  /// <param name="port">端口号</param>
  /// <param name="dtu">dtu的注册包</param>
  public RemoteConnectInfo(string ipAddress, int port, byte[] dtu)
  {
    this.EndPoint = new IPEndPoint(IPAddress.Parse(HslHelper.GetIpAddressFromInput(ipAddress)), port);
    this.DtuBytes = dtu;
  }

  /// <summary>实例化一个远程连接信息对象，传入 IP，Port 来创建符合 HSL 标准的 DTU 注册包</summary>
  /// <param name="ipAddress">IP地址</param>
  /// <param name="port">端口号</param>
  /// <param name="dtuId">DTU信息</param>
  /// <param name="password">密码</param>
  /// <param name="needAckResult">是否需要返回注册结果</param>
  public RemoteConnectInfo(
    string ipAddress,
    int port,
    string dtuId,
    string password = "",
    bool needAckResult = true)
  {
    this.dtuId = dtuId;
    this.EndPoint = new IPEndPoint(IPAddress.Parse(HslHelper.GetIpAddressFromInput(ipAddress)), port);
    this.DtuBytes = this.CreateHslAlienMessage(dtuId, password, needAckResult);
    this.ack = needAckResult;
  }

  /// <summary>
  /// 远程端口号信息<br />
  /// Remote port number information
  /// </summary>
  public IPEndPoint EndPoint { get; set; }

  /// <summary>
  /// 会话信息<br />
  /// Session information
  /// </summary>
  public PipeSession Session { get; set; }

  /// <summary>
  /// DTU的注册包<br />
  /// Registration package for DTU
  /// </summary>
  public byte[] DtuBytes { get; set; }

  /// <summary>
  /// 是否需要返回注册结果的报文<br />
  /// Whether to return the registration result packet
  /// </summary>
  public bool NeedAckResult => this.ack;

  /// <summary>如果使用的是 HSL 格式的DTU消息的话，就是传入的 dtuid 信息</summary>
  public string DtuId => this.dtuId;

  /// <summary>
  /// 当前DTU会话的状态信息，如果需要关闭当前的会话并退出重连，设置本属性为 <see cref="F:HslCommunication.Core.Net.DtuStatus.Closed" /> 即可
  /// </summary>
  public DtuStatus Status { get; set; } = DtuStatus.Create;

  private byte[] CreateHslAlienMessage(string dtuId, string password, bool needAckResult)
  {
    if (dtuId.Length > 11)
      dtuId = dtuId.Substring(11);
    byte[] hslAlienMessage = new byte[30];
    hslAlienMessage[0] = (byte) 72;
    hslAlienMessage[1] = (byte) 83;
    hslAlienMessage[2] = (byte) 76;
    hslAlienMessage[3] = (byte) 0;
    hslAlienMessage[4] = (byte) 25;
    if (dtuId.Length > 11)
      dtuId = dtuId.Substring(0, 11);
    Encoding.ASCII.GetBytes(dtuId).CopyTo((Array) hslAlienMessage, 5);
    if (!string.IsNullOrEmpty(password))
    {
      if (password.Length > 6)
        password = password.Substring(6);
      Encoding.ASCII.GetBytes(password).CopyTo((Array) hslAlienMessage, 16 /*0x10*/);
    }
    if (!needAckResult)
      hslAlienMessage[28] = (byte) 1;
    return hslAlienMessage;
  }

  /// <summary>
  /// 关闭当前的 DTU 连接<br />
  /// Close the current DTU connection
  /// </summary>
  public void Close()
  {
    this.Status = DtuStatus.Closed;
    this.Session?.Close();
  }
}
