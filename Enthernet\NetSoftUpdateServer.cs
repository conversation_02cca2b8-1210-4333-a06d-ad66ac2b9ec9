﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.NetSoftUpdateServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.LogNet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;

#nullable disable
namespace HslCommunication.Enthernet;

/// <summary>
/// 用于服务器支持软件全自动更新升级的类<br />
/// Class for server support software full automatic update and upgrade
/// </summary>
/// <remarks>目前的更新机制是全部文件的更新，没有进行差异化的比较</remarks>
public sealed class NetSoftUpdateServer : NetworkServerBase
{
  private string m_FilePath = "C:\\HslCommunication";
  private string updateExeFileName;
  private List<AppSession> sessions = new List<AppSession>();
  private object lockSessions = new object();
  private object lockMd5 = new object();
  private Dictionary<string, FileInfoExtension> fileMd5 = new Dictionary<string, FileInfoExtension>();
  private long downloadSize = 0;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  /// <param name="updateExeFileName">更新程序的名称</param>
  public NetSoftUpdateServer(string updateExeFileName = "软件自动更新.exe")
  {
    this.updateExeFileName = updateExeFileName;
  }

  /// <summary>系统升级时客户端所在的目录，默认为C:\HslCommunication</summary>
  public string FileUpdatePath
  {
    get => this.m_FilePath;
    set => this.m_FilePath = value;
  }

  /// <summary>
  /// 获取当前在线的客户端数量信息，一般是正在下载中的会话客户端数量。<br />
  /// Get information about the number of currently online clients, generally the number of session clients that are being downloaded.
  /// </summary>
  public int OnlineSessions => this.sessions.Count;

  /// <summary>
  /// 获取当前处理的所有的字节信息，并且重置当前的字节数量<br />
  /// Get all byte information currently processed and reset the current number of bytes
  /// </summary>
  /// <remarks>
  /// 如果每秒调用一次本方法，就可以显示每秒的下载网速总和，如果每分钟调用一次本方法，就可以显示每分钟的下载网络总和<br />
  /// If this method is called once per second, the total download speed per second can be displayed. If this method is called once per minute, the total download network speed per minute can be displayed.
  /// </remarks>
  /// <returns>当前处理的字节数量</returns>
  public long GetDealSizeAndReset() => Interlocked.Exchange(ref this.downloadSize, 0L);

  private void RemoveAndCloseSession(AppSession session)
  {
    lock (this.lockSessions)
    {
      if (!this.sessions.Remove(session))
        return;
      session.WorkSocket?.Close();
    }
  }

  private string RemoveHeadPathChar(string path)
  {
    return path.StartsWith("\\") ? path.Substring(1) : path;
  }

  /// <inheritdoc />
  protected override async void ThreadPoolLogin(Socket socket, IPEndPoint endPoint)
  {
    string fileUpdatePath = this.FileUpdatePath;
    OperateResult<byte[]> receive = await this.ReceiveAsync(socket, 4, 10000);
    if (!receive.IsSuccess)
    {
      ILogNet logNet = this.LogNet;
      if (logNet == null)
      {
        fileUpdatePath = (string) null;
        receive = (OperateResult<byte[]>) null;
      }
      else
      {
        logNet.WriteError(this.ToString(), "Receive Failed: " + receive.Message);
        fileUpdatePath = (string) null;
        receive = (OperateResult<byte[]>) null;
      }
    }
    else
    {
      int protocol = BitConverter.ToInt32(receive.Content, 0);
      if (!Directory.Exists(fileUpdatePath) || protocol != 4097 && protocol != 4098 && protocol != 8193)
      {
        OperateResult operateResult = await this.SendAsync(socket, BitConverter.GetBytes(10000f));
        Socket socket1 = socket;
        if (socket1 == null)
        {
          fileUpdatePath = (string) null;
          receive = (OperateResult<byte[]>) null;
        }
        else
        {
          socket1.Close();
          fileUpdatePath = (string) null;
          receive = (OperateResult<byte[]>) null;
        }
      }
      else if (protocol == 8193)
      {
        List<string> files = NetSoftUpdateServer.GetAllFiles(fileUpdatePath, this.LogNet);
        AppSession session = new AppSession(socket);
        lock (this.lockSessions)
          this.sessions.Add(session);
        OperateResult operateResult = await this.SendAsync(socket, BitConverter.GetBytes(files.Count));
        foreach (string str in files)
        {
          string fileName = str;
          FileInfo finfo = new FileInfo(fileName);
          string fileShortName = finfo.FullName.Replace(fileUpdatePath, "");
          fileShortName = this.RemoveHeadPathChar(fileShortName);
          byte[] buffer = this.TranslateSourceData(new string[3]
          {
            fileShortName,
            finfo.Length.ToString(),
            this.GetMD5(finfo)
          });
          this.Send(socket, BitConverter.GetBytes(buffer.Length));
          this.Send(socket, buffer);
          OperateResult<byte[]> receiveCheck = await this.ReceiveAsync(socket, 4, 10000);
          if (!receiveCheck.IsSuccess)
          {
            this.RemoveAndCloseSession(session);
            fileUpdatePath = (string) null;
            receive = (OperateResult<byte[]>) null;
            return;
          }
          if (BitConverter.ToInt32(receiveCheck.Content, 0) != 1)
          {
            long alreadyReceived = 0;
            using (FileStream fs = new FileStream(fileName, FileMode.Open, FileAccess.Read))
            {
              buffer = new byte[40960 /*0xA000*/];
              long sended = 0;
              while (sended < fs.Length)
              {
                int count = await fs.ReadAsync(buffer, 0, buffer.Length);
                OperateResult sendFile = await this.SendAsync(socket, buffer, 0, count);
                if (!sendFile.IsSuccess)
                {
                  this.RemoveAndCloseSession(session);
                  fileUpdatePath = (string) null;
                  receive = (OperateResult<byte[]>) null;
                  return;
                }
                sended += (long) count;
                Interlocked.Add(ref this.downloadSize, (long) count);
                for (long atLeastReceive = sended - 10485760L /*0xA00000*/; alreadyReceived < atLeastReceive; alreadyReceived = (long) BitConverter.ToInt32(receiveCheck.Content, 0))
                {
                  receiveCheck = await this.ReceiveAsync(socket, 4);
                  if (!receiveCheck.IsSuccess)
                  {
                    this.RemoveAndCloseSession(session);
                    fileUpdatePath = (string) null;
                    receive = (OperateResult<byte[]>) null;
                    return;
                  }
                }
                sendFile = (OperateResult) null;
              }
            }
            while (true)
            {
              if (alreadyReceived < finfo.Length)
              {
                receiveCheck = await this.ReceiveAsync(socket, 4);
                if (receiveCheck.IsSuccess)
                  alreadyReceived = (long) BitConverter.ToInt32(receiveCheck.Content, 0);
                else
                  break;
              }
              else
                goto label_40;
            }
            this.RemoveAndCloseSession(session);
            fileUpdatePath = (string) null;
            receive = (OperateResult<byte[]>) null;
            return;
label_40:
            finfo = (FileInfo) null;
            fileShortName = (string) null;
            buffer = (byte[]) null;
            receiveCheck = (OperateResult<byte[]>) null;
            fileName = (string) null;
          }
        }
        this.RemoveAndCloseSession(session);
        files = (List<string>) null;
        session = (AppSession) null;
        fileUpdatePath = (string) null;
        receive = (OperateResult<byte[]>) null;
      }
      else
      {
        AppSession session = new AppSession(socket);
        session.Tag = (object) HslTimeOut.HandleTimeOutCheck(socket, 1800000);
        lock (this.lockSessions)
          this.sessions.Add(session);
        try
        {
          if (protocol == 4097)
            this.LogNet?.WriteInfo(this.ToString(), StringResources.Language.SystemInstallOperater + ((IPEndPoint) socket.RemoteEndPoint).Address.ToString());
          else
            this.LogNet?.WriteInfo(this.ToString(), StringResources.Language.SystemUpdateOperater + ((IPEndPoint) socket.RemoteEndPoint).Address.ToString());
          List<string> Files = NetSoftUpdateServer.GetAllFiles(fileUpdatePath, this.LogNet);
          for (int i = Files.Count - 1; i >= 0; --i)
          {
            FileInfo finfo = new FileInfo(Files[i]);
            if (finfo.Length > 200000000L)
              Files.RemoveAt(i);
            if (protocol == 4098 && finfo.Name == this.updateExeFileName)
              Files.RemoveAt(i);
            finfo = (FileInfo) null;
          }
          string[] files = Files.ToArray();
          socket.BeginReceive(new byte[4], 0, 4, SocketFlags.None, new AsyncCallback(this.ReceiveCallBack), (object) session);
          OperateResult operateResult = await this.SendAsync(socket, BitConverter.GetBytes(files.Length));
          for (int i = 0; i < files.Length; ++i)
          {
            FileInfo finfo = new FileInfo(files[i]);
            string fileName = finfo.FullName.Replace(fileUpdatePath, "");
            fileName = this.RemoveHeadPathChar(fileName);
            byte[] firstSend = this.GetFirstSendFileHead(fileName, (int) finfo.Length);
            OperateResult sendFirst = await this.SendAsync(socket, firstSend);
            if (!sendFirst.IsSuccess)
            {
              this.RemoveAndCloseSession(session);
              fileUpdatePath = (string) null;
              receive = (OperateResult<byte[]>) null;
              return;
            }
            HslHelper.ThreadSleep(10);
            using (FileStream fs = new FileStream(files[i], FileMode.Open, FileAccess.Read))
            {
              byte[] buffer = new byte[40960 /*0xA000*/];
              int sended = 0;
              while ((long) sended < fs.Length)
              {
                int count = await fs.ReadAsync(buffer, 0, buffer.Length);
                OperateResult sendFile = await this.SendAsync(socket, buffer, 0, count);
                if (!sendFile.IsSuccess)
                {
                  this.RemoveAndCloseSession(session);
                  fileUpdatePath = (string) null;
                  receive = (OperateResult<byte[]>) null;
                  return;
                }
                sended += count;
                Interlocked.Add(ref this.downloadSize, (long) count);
                sendFile = (OperateResult) null;
              }
              buffer = (byte[]) null;
            }
            HslHelper.ThreadSleep(20);
            finfo = (FileInfo) null;
            fileName = (string) null;
            firstSend = (byte[]) null;
            sendFirst = (OperateResult) null;
          }
          Files = (List<string>) null;
          files = (string[]) null;
        }
        catch (Exception ex)
        {
          if (session.Tag is HslTimeOut hslTimeOut)
            hslTimeOut.IsSuccessful = true;
          this.RemoveAndCloseSession(session);
          this.LogNet?.WriteException(this.ToString(), StringResources.Language.FileSendClientFailed, ex);
          hslTimeOut = (HslTimeOut) null;
        }
        session = (AppSession) null;
        fileUpdatePath = (string) null;
        receive = (OperateResult<byte[]>) null;
      }
    }
  }

  private void ReceiveCallBack(IAsyncResult ir)
  {
    if (!(ir.AsyncState is AppSession asyncState))
      return;
    try
    {
      asyncState.WorkSocket.EndReceive(ir);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), ex);
    }
    finally
    {
      if (asyncState.Tag is HslTimeOut tag)
        tag.IsSuccessful = true;
      this.RemoveAndCloseSession(asyncState);
    }
  }

  private byte[] GetFirstSendFileHead(string relativeFileName, int fileLength)
  {
    byte[] bytes = Encoding.Unicode.GetBytes(relativeFileName);
    byte[] destinationArray = new byte[8 + bytes.Length];
    Array.Copy((Array) BitConverter.GetBytes(destinationArray.Length), 0, (Array) destinationArray, 0, 4);
    Array.Copy((Array) BitConverter.GetBytes(fileLength), 0, (Array) destinationArray, 4, 4);
    Array.Copy((Array) bytes, 0, (Array) destinationArray, 8, bytes.Length);
    return destinationArray;
  }

  private byte[] TranslateSourceData(string[] parameters)
  {
    if (parameters == null)
      return new byte[0];
    MemoryStream memoryStream = new MemoryStream();
    foreach (string parameter in parameters)
    {
      byte[] buffer = string.IsNullOrEmpty(parameter) ? new byte[0] : Encoding.UTF8.GetBytes(parameter);
      memoryStream.Write(BitConverter.GetBytes(buffer.Length), 0, 4);
      if (buffer.Length != 0)
        memoryStream.Write(buffer, 0, buffer.Length);
    }
    return memoryStream.ToArray();
  }

  private string[] TranslateFromSourceData(byte[] source)
  {
    if (source == null)
      return new string[0];
    List<string> stringList = new List<string>();
    int startIndex = 0;
    while (startIndex < source.Length)
    {
      try
      {
        int int32 = BitConverter.ToInt32(source, startIndex);
        int index = startIndex + 4;
        string str = int32 > 0 ? Encoding.UTF8.GetString(source, index, int32) : string.Empty;
        startIndex = index + int32;
        stringList.Add(str);
      }
      catch
      {
        return stringList.ToArray();
      }
    }
    return stringList.ToArray();
  }

  private string GetMD5(FileInfo fileInfo)
  {
    lock (this.lockMd5)
    {
      if (this.fileMd5.ContainsKey(fileInfo.FullName))
      {
        if (fileInfo.LastWriteTime == this.fileMd5[fileInfo.FullName].ModifiTime)
          return this.fileMd5[fileInfo.FullName].MD5;
        this.fileMd5[fileInfo.FullName].MD5 = SoftBasic.CalculateFileMD5(fileInfo.FullName);
        return this.fileMd5[fileInfo.FullName].MD5;
      }
      FileInfoExtension fileInfoExtension = new FileInfoExtension();
      fileInfoExtension.FullName = fileInfo.FullName;
      fileInfoExtension.ModifiTime = fileInfo.LastWriteTime;
      fileInfoExtension.MD5 = SoftBasic.CalculateFileMD5(fileInfo.FullName);
      this.fileMd5.Add(fileInfoExtension.FullName, fileInfoExtension);
      return fileInfoExtension.MD5;
    }
  }

  /// <summary>
  /// 获取所有的文件信息，包括所有的子目录的文件信息<br />
  /// Get all file information, including file information of all subdirectories
  /// </summary>
  /// <param name="dircPath">目标路径</param>
  /// <param name="logNet">日志信息</param>
  /// <returns>文件名的列表</returns>
  public static List<string> GetAllFiles(string dircPath, ILogNet logNet)
  {
    List<string> allFiles = new List<string>();
    try
    {
      allFiles.AddRange((IEnumerable<string>) Directory.GetFiles(dircPath));
    }
    catch (Exception ex)
    {
      logNet?.WriteWarn(nameof (GetAllFiles), ex.Message);
    }
    foreach (string directory in Directory.GetDirectories(dircPath))
      allFiles.AddRange((IEnumerable<string>) NetSoftUpdateServer.GetAllFiles(directory, logNet));
    return allFiles;
  }

  /// <inheritdoc />
  public override string ToString() => $"NetSoftUpdateServer[{this.Port}]";
}
