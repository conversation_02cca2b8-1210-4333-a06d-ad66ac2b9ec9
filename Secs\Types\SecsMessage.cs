﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Types.SecsMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Secs.Helper;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Secs.Types;

/// <summary>Secs的消息类对象</summary>
public class SecsMessage
{
  /// <summary>实例化一个默认的对象</summary>
  public SecsMessage()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsMessage.#ctor(System.Byte[],System.Int32)" />
  public SecsMessage(byte[] message)
    : this(message, 0)
  {
  }

  /// <summary>通过原始的报文信息来实例化一个默认的对象</summary>
  /// <param name="message">原始的字节信息</param>
  /// <param name="startIndex">起始的偏移地址</param>
  public SecsMessage(byte[] message, int startIndex)
  {
    this.DeviceID = BitConverter.ToUInt16(new byte[2]
    {
      message[startIndex + 1],
      (byte) ((uint) message[startIndex] & (uint) sbyte.MaxValue)
    }, 0);
    this.R = ((int) message[startIndex] & 128 /*0x80*/) == 128 /*0x80*/;
    this.W = ((int) message[startIndex + 2] & 128 /*0x80*/) == 128 /*0x80*/;
    this.E = ((int) message[startIndex + 4] & 128 /*0x80*/) == 128 /*0x80*/;
    this.StreamNo = (byte) ((uint) message[startIndex + 2] & (uint) sbyte.MaxValue);
    this.FunctionNo = message[startIndex + 3];
    byte[] buffer = new byte[2]
    {
      (byte) ((uint) message[startIndex + 4] & (uint) sbyte.MaxValue),
      message[startIndex + 5]
    };
    this.BlockNo = (int) Secs2.SecsTransform.TransInt16(buffer, 0);
    this.MessageID = Secs2.SecsTransform.TransUInt32(message, startIndex + 6);
    this.Data = message.RemoveBegin<byte>(startIndex + 10);
  }

  /// <summary>设备的ID信息</summary>
  public ushort DeviceID { get; set; }

  /// <summary>R=false, Host → Equipment; R=true, Host ← Equipment</summary>
  public bool R { get; set; }

  /// <summary>W=false, 不必回复讯息；W=true, 必须回复讯息</summary>
  public bool W { get; set; }

  /// <summary>E=false, 尚有Block; E=true, 此为最后一个Block</summary>
  public bool E { get; set; }

  /// <summary>Stream功能码</summary>
  public byte StreamNo { get; set; }

  /// <summary>Function功能码</summary>
  public byte FunctionNo { get; set; }

  /// <summary>获取或设置区块号信息</summary>
  public int BlockNo { get; set; }

  /// <summary>获取或设置消息ID信息</summary>
  public uint MessageID { get; set; }

  /// <summary>消息数据对象</summary>
  public byte[] Data { get; set; }

  /// <summary>获取或设置用于字符串解析的编码信息</summary>
  public Encoding StringEncoding { get; set; } = Encoding.Default;

  /// <summary>获取当前消息的所有对象信息</summary>
  /// <returns>Secs数据对象</returns>
  public SecsValue GetItemValues() => Secs2.ExtraToSecsItemValue(this.Data, this.StringEncoding);

  /// <summary>使用指定的编码获取当前消息的所有对象信息</summary>
  /// <param name="encoding">自定义的编码信息</param>
  /// <returns>Secs数据对象</returns>
  public SecsValue GetItemValues(Encoding encoding)
  {
    return Secs2.ExtraToSecsItemValue(this.Data, encoding);
  }

  /// <inheritdoc />
  public override string ToString()
  {
    SecsValue itemValues = this.GetItemValues(this.StringEncoding);
    if (this.StreamNo == (byte) 0 && this.FunctionNo == (byte) 0)
      return $"S{this.StreamNo}F{this.FunctionNo}{(this.W ? (object) "W" : (object) string.Empty)} B{this.BlockNo}";
    if (itemValues == null)
      return $"S{this.StreamNo}F{this.FunctionNo}{(this.W ? (object) "W" : (object) string.Empty)}";
    return $"S{this.StreamNo}F{this.FunctionNo}{(this.W ? (object) "W" : (object) string.Empty)} {Environment.NewLine}{itemValues}";
  }
}
