﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.AsyncStateSend
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Net.Sockets;

#nullable disable
namespace HslCommunication.Core.Net;

internal class AsyncStateSend
{
  /// <summary>传输数据的对象</summary>
  internal Socket WorkSocket { get; set; }

  /// <summary>发送的数据内容</summary>
  internal byte[] Content { get; set; }

  /// <summary>已经发送长度</summary>
  internal int AlreadySendLength { get; set; }

  internal SimpleHybirdLock HybirdLockSend { get; set; }

  /// <summary>关键字</summary>
  internal string Key { get; set; }

  /// <summary>客户端的标识</summary>
  internal string ClientId { get; set; }
}
