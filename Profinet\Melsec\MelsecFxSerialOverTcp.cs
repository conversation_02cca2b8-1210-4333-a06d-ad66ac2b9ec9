﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Net;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱串口协议的网络版，如果使用的是 FX3U编程口(fx2n) -&gt; GOT1000(RS232)(或是GOT2000)  -&gt; 上位机(以太网) 的方式，那么就需要把<see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.UseGOT" />设置为 <c>True</c><br />
/// The network version of the Mitsubishi serial port protocol, if you use the FX3U programming port (fx2n) -&gt; GOT1000 (RS232) (or GOT2000) -&gt; host computer (Ethernet) method,
/// then you need to put <see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.UseGOT" /> is set to <c>True</c>
/// </summary>
/// <remarks>
/// 一般老旧的型号，例如FX2N之类的，需要将<see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.IsNewVersion" />设置为<c>False</c>，如果是FX3U新的型号，则需要将<see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.IsNewVersion" />设置为<c>True</c>
/// </remarks>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecFxSerial.cs" region="Usage" title="简单的使用" />
/// </example>
public class MelsecFxSerialOverTcp : DeviceTcpNet, IMelsecFxSerial, IReadWriteNet
{
  private List<string> inis = new List<string>()
  {
    "00 10 02 FF FF FC 01 10 03",
    "10025E000000FC00000000001212000000FFFF030000FF0300002D001C091A18000000000000000000FC000012120414000113960AC4E5D7010201000000023030453032303203364310033543",
    "10025E000000FC00000000001212000000FFFF030000FF0300002D001C091A18000000000000000000FC000012120414000113960AC4E5D7010202000000023030454341303203384510033833",
    "10025E000000FC00000000001212000000FFFF030000FF0300002D001C091A18000000000000000000FC000012120414000113960AC4E5D7010203000000023030453032303203364310033545",
    "10025E000000FC00000000001212000000FFFF030000FF0300002D001C091A18000000000000000000FC000012120414000113960AC4E5D7010204000000023030454341303203384510033835",
    "10025E000000FC00000000001212000000FFFF030000FF0300002F001C091A18000000000000000000FC000012120414000113960AC4E5D70102050000000245303138303030343003443510034342",
    "10025E000000FC00000000001212000000FFFF030000FF0300002F001C091A18000000000000000000FC000012120414000113960AC4E5D70102060000000245303138303430314303453910034535",
    "10025E000000FC00000000001212000000FFFF030000FF0300002F001C091A18000000000000000000FC000012120414000113960AC4E5D70102070000000245303030453030343003453110034436",
    "10025E000000FC00000000001212000000FFFF030000FF0300002F001C091A18000000000000000000FC000012120414000113960AC4E5D70102080000000245303030453430343003453510034446",
    "10025E000000FC00000000001212000000FFFF030000FF0300002F001C091A18000000000000000000FC000012120414000113960AC4E5D70102090000000245303030453830343003453910034538",
    "10025E000000FC00000000001212000000FFFF030000FF0300002F001C091A18000000000000000000FC000012120414000113960AC4E5D701020A0000000245303030454330343003463410034630"
  };
  private bool useGot = false;
  private SoftIncrementCount incrementCount;

  /// <summary>
  /// 实例化网络版的三菱的串口协议的通讯对象<br />
  /// Instantiate the communication object of Mitsubishi's serial protocol on the network
  /// </summary>
  public MelsecFxSerialOverTcp()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.IsNewVersion = true;
    this.ByteTransform.IsStringReverseByteWord = true;
    this.SleepTime = 20;
    this.incrementCount = new SoftIncrementCount((long) int.MaxValue, 1L);
    this.LogMsgFormatBinary = false;
  }

  /// <summary>
  /// 指定ip地址及端口号来实例化三菱的串口协议的通讯对象<br />
  /// Specify the IP address and port number to instantiate the communication object of Mitsubishi's serial protocol
  /// </summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  public MelsecFxSerialOverTcp(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <summary>获取或设置是否使用GOT连接三菱的PLC，当使用了GOT连接到</summary>
  public bool UseGOT
  {
    get => this.useGot;
    set => this.useGot = value;
  }

  private byte[] GetBytesSend(byte[] command)
  {
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < command.Length; ++index)
    {
      if (index < 2)
        byteList.Add(command[index]);
      else if (index < command.Length - 4)
      {
        if (command[index] == (byte) 16 /*0x10*/)
          byteList.Add(command[index]);
        byteList.Add(command[index]);
      }
      else
        byteList.Add(command[index]);
    }
    return byteList.ToArray();
  }

  private byte[] GetBytesReceive(byte[] response)
  {
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < response.Length; ++index)
    {
      if (index < 2)
        byteList.Add(response[index]);
      else if (index < response.Length - 4)
      {
        if (response[index] == (byte) 16 /*0x10*/ && response[index + 1] == (byte) 16 /*0x10*/)
        {
          byteList.Add(response[index]);
          ++index;
        }
        else
          byteList.Add(response[index]);
      }
      else
        byteList.Add(response[index]);
    }
    return byteList.ToArray();
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    if (!this.useGot)
      return base.PackCommandWithHeader(command);
    byte[] numArray = new byte[66 + command.Length];
    numArray[0] = (byte) 16 /*0x10*/;
    numArray[1] = (byte) 2;
    numArray[2] = (byte) 94;
    numArray[6] = (byte) 252;
    numArray[12] = (byte) 18;
    numArray[13] = (byte) 18;
    numArray[17] = byte.MaxValue;
    numArray[18] = byte.MaxValue;
    numArray[19] = (byte) 3;
    numArray[22] = byte.MaxValue;
    numArray[23] = (byte) 3;
    numArray[26] = BitConverter.GetBytes(34 + command.Length)[0];
    numArray[27] = BitConverter.GetBytes(34 + command.Length)[1];
    numArray[28] = (byte) 28;
    numArray[29] = (byte) 9;
    numArray[30] = (byte) 26;
    numArray[31 /*0x1F*/] = (byte) 24;
    numArray[41] = (byte) 252;
    numArray[44] = (byte) 18;
    numArray[45] = (byte) 18;
    numArray[46] = (byte) 4;
    numArray[47] = (byte) 20;
    numArray[49] = (byte) 1;
    numArray[50] = BitConverter.GetBytes(this.Port)[1];
    numArray[51] = BitConverter.GetBytes(this.Port)[0];
    numArray[52] = IPAddress.Parse(this.IpAddress).GetAddressBytes()[0];
    numArray[53] = IPAddress.Parse(this.IpAddress).GetAddressBytes()[1];
    numArray[54] = IPAddress.Parse(this.IpAddress).GetAddressBytes()[2];
    numArray[55] = IPAddress.Parse(this.IpAddress).GetAddressBytes()[3];
    numArray[56] = (byte) 1;
    numArray[57] = (byte) 2;
    BitConverter.GetBytes((int) this.incrementCount.GetCurrentValue()).CopyTo((Array) numArray, 58);
    command.CopyTo((Array) numArray, 62);
    numArray[numArray.Length - 4] = (byte) 16 /*0x10*/;
    numArray[numArray.Length - 3] = (byte) 3;
    MelsecHelper.FxCalculateCRC(numArray, 2, 4).CopyTo((Array) numArray, numArray.Length - 2);
    return this.GetBytesSend(numArray);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    if (!this.useGot)
      return base.UnpackResponseContent(send, response);
    if (response.Length > 68)
    {
      response = this.GetBytesReceive(response);
      int num = -1;
      for (int index = 0; index < response.Length - 4; ++index)
      {
        if (response[index] == (byte) 16 /*0x10*/ && response[index + 1] == (byte) 2)
        {
          num = index;
          break;
        }
      }
      if (num >= 0)
        return OperateResult.CreateSuccessResult<byte[]>(response.RemoveDouble<byte>(64 /*0x40*/ + num, 4));
    }
    return new OperateResult<byte[]>("Got failed: " + response.ToHexString(' ', 16 /*0x10*/));
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    if (this.useGot)
    {
      for (int index = 0; index < this.inis.Count; ++index)
      {
        OperateResult operateResult = (OperateResult) this.ReadFromCoreServer(this.CommunicationPipe, this.inis[index].ToHexBytes(), true, false);
        if (!operateResult.IsSuccess)
          return operateResult;
      }
    }
    return base.InitializationOnConnect();
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackAndUnpack = true)
  {
    OperateResult<byte[]> operateResult1 = base.ReadFromCoreServer(pipe, send, hasResponseData, usePackAndUnpack);
    if (!operateResult1.IsSuccess || operateResult1.Content == null || operateResult1.Content.Length > 2)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = base.ReadFromCoreServer(pipe, send, hasResponseData, usePackAndUnpack);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(operateResult1.Content, operateResult2.Content));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackAndUnpack = true)
  {
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = base.ReadFromCoreServerAsync(pipe, send, hasResponseData, usePackAndUnpack).ConfigureAwait(false);
    OperateResult<byte[]> read = await configuredTaskAwaitable;
    if (!read.IsSuccess || read.Content == null || read.Content.Length > 2)
      return read;
    configuredTaskAwaitable = base.ReadFromCoreServerAsync(pipe, send, hasResponseData, usePackAndUnpack).ConfigureAwait(false);
    OperateResult<byte[]> read2 = await configuredTaskAwaitable;
    if (!read2.IsSuccess)
      return read2;
    return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(read.Content, read2.Content));
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    if (this.useGot)
    {
      for (int i = 0; i < this.inis.Count; ++i)
      {
        OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.inis[i].ToHexBytes(), true, false).ConfigureAwait(false);
        OperateResult ini1 = (OperateResult) operateResult;
        operateResult = (OperateResult<byte[]>) null;
        if (!ini1.IsSuccess)
          return ini1;
        ini1 = (OperateResult) null;
      }
    }
    OperateResult operateResult1 = await base.InitializationOnConnectAsync();
    return operateResult1;
  }

  /// <summary>
  /// 当前的编程口协议是否为新版，默认为新版，如果无法读取，切换旧版再次尝试<br />
  /// Whether the current programming port protocol is the new version, the default is the new version,
  /// if it cannot be read, switch to the old version and try again
  /// </summary>
  public bool IsNewVersion { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.Read(HslCommunication.Core.IReadWriteDevice,System.String,System.UInt16,System.Boolean)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return MelsecFxSerialHelper.Read((IReadWriteDevice) this, address, length, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.Write(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte[],System.Boolean)" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return MelsecFxSerialHelper.Write((IReadWriteDevice) this, address, value, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await MelsecFxSerialHelper.ReadAsync((IReadWriteDevice) this, address, length, this.IsNewVersion);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await MelsecFxSerialHelper.WriteAsync((IReadWriteDevice) this, address, value, this.IsNewVersion);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.String,System.UInt16,System.Boolean)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return MelsecFxSerialHelper.ReadBool((IReadWriteDevice) this, address, length, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.Write(HslCommunication.Core.IReadWriteDevice,System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return MelsecFxSerialHelper.Write((IReadWriteDevice) this, address, value);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return MelsecFxSerialHelper.Write((IReadWriteDevice) this, address, value, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await MelsecFxSerialHelper.ReadBoolAsync((IReadWriteDevice) this, address, length, this.IsNewVersion);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await MelsecFxSerialHelper.WriteAsync((IReadWriteDevice) this, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ActivePlc(HslCommunication.Core.IReadWriteDevice)" />
  [HslMqttApi]
  public OperateResult ActivePlc() => MelsecFxSerialHelper.ActivePlc((IReadWriteDevice) this);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ActivePlc(HslCommunication.Core.IReadWriteDevice)" />
  public async Task<OperateResult> ActivePlcAsync()
  {
    OperateResult operateResult = await MelsecFxSerialHelper.ActivePlcAsync((IReadWriteDevice) this);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecFxSerialOverTcp[{this.IpAddress}:{this.Port}]";
}
