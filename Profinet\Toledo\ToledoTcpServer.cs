﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Toledo.ToledoTcpServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using System;

#nullable disable
namespace HslCommunication.Profinet.Toledo;

/// <summary>托利多电子秤的TCP服务器，启动服务器后，等待电子秤的数据连接。</summary>
public class ToledoTcpServer : CommunicationServer
{
  /// <summary>实例化一个默认的对象</summary>
  public ToledoTcpServer()
  {
    this.OnPipeMessageReceived += new CommunicationServer.PipeMessageReceived(this.ToledoTcpServer_OnPipeMessageReceived);
  }

  private void ToledoTcpServer_OnPipeMessageReceived(PipeSession session, byte[] buffer)
  {
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Receive} : {buffer.ToHexString(' ')}");
    ToledoStandardData toledoStandardData = (ToledoStandardData) null;
    try
    {
      toledoStandardData = new ToledoStandardData(buffer);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), "ToledoStandardData new failed: " + buffer.ToHexString(' '), ex);
    }
    if (toledoStandardData == null)
      return;
    ToledoTcpServer.ToledoStandardDataReceivedDelegate standardDataReceived = this.OnToledoStandardDataReceived;
    if (standardDataReceived == null)
      return;
    standardDataReceived((object) session, toledoStandardData);
  }

  /// <summary>获取或设置当前的报文否是含有校验的，默认为含有校验</summary>
  public bool HasChk { get; set; } = true;

  /// <summary>当接收到一条新的托利多的数据的时候触发</summary>
  public event ToledoTcpServer.ToledoStandardDataReceivedDelegate OnToledoStandardDataReceived;

  /// <inheritdoc />
  public override string ToString() => $"ToledoTcpServer[{this.Port}]";

  /// <summary>托利多数据接收时的委托</summary>
  /// <param name="sender">数据发送对象</param>
  /// <param name="toledoStandardData">数据对象</param>
  public delegate void ToledoStandardDataReceivedDelegate(
    object sender,
    ToledoStandardData toledoStandardData);
}
