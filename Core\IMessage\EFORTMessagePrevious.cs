﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.EFORTMessagePrevious
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>旧版的机器人的消息类对象，保留此类为了实现兼容</summary>
public class EFORTMessagePrevious : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 17;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    int lengthByHeadBytes = (int) BitConverter.ToInt16(this.HeadBytes, 15) - 17;
    if (lengthByHeadBytes < 0)
      lengthByHeadBytes = 0;
    return lengthByHeadBytes;
  }
}
