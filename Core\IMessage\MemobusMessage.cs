﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.MemobusMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>Memobus协议的消息定义</summary>
public class MemobusMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 12;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    int? length = this.HeadBytes?.Length;
    int protocolHeadBytesLength = this.ProtocolHeadBytesLength;
    if (!(length.GetValueOrDefault() >= protocolHeadBytesLength & length.HasValue))
      return 0;
    int lengthByHeadBytes = (int) BitConverter.ToUInt16(this.HeadBytes, 6) - 12;
    if (lengthByHeadBytes < 0)
      lengthByHeadBytes = 0;
    return lengthByHeadBytes;
  }
}
