﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Device.DeviceUdpNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Pipe;
using System.Net;
using System.Net.NetworkInformation;

#nullable disable
namespace HslCommunication.Core.Device;

/// <summary>基于UDP/IP管道的设备基类信息</summary>
public class DeviceUdpNet : DeviceCommunication
{
  private PipeUdpNet pipe;

  /// <summary>实例化一个默认的对象</summary>
  public DeviceUdpNet()
    : this("127.0.0.1", 5000)
  {
  }

  /// <summary>指定IP地址以及端口号信息来初始化对象</summary>
  /// <param name="ipAddress">IP地址信息，可以是IPv4, IPv6, 也可以是域名</param>
  /// <param name="port">设备方的端口号信息</param>
  public DeviceUdpNet(string ipAddress, int port)
  {
    PipeUdpNet pipeUdpNet = new PipeUdpNet();
    pipeUdpNet.IpAddress = ipAddress;
    pipeUdpNet.Port = port;
    this.CommunicationPipe = (CommunicationPipe) pipeUdpNet;
  }

  /// <inheritdoc />
  public override CommunicationPipe CommunicationPipe
  {
    get => base.CommunicationPipe;
    set
    {
      base.CommunicationPipe = value;
      if (!(value is PipeUdpNet pipeUdpNet))
        return;
      this.pipe = pipeUdpNet;
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceTcpNet.IpAddress" />
  public virtual string IpAddress
  {
    get => this.pipe.IpAddress;
    set => this.pipe.IpAddress = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceTcpNet.Port" />
  public virtual int Port
  {
    get => this.pipe.Port;
    set => this.pipe.Port = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeUdpNet.ReceiveCacheLength" />
  public int ReceiveCacheLength
  {
    get => this.pipe.ReceiveCacheLength;
    set => this.pipe.ReceiveCacheLength = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceTcpNet.LocalBinding" />
  public IPEndPoint LocalBinding
  {
    get => this.pipe.LocalBinding;
    set => this.pipe.LocalBinding = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceTcpNet.IpAddressPing" />
  public IPStatus IpAddressPing() => new Ping().Send(this.IpAddress).Status;

  /// <inheritdoc />
  public override string ToString()
  {
    return $"DeviceUdpNet<{this.ByteTransform}>{{{this.CommunicationPipe}}}";
  }
}
