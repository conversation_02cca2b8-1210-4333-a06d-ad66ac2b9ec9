﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.Helper.OmronHostLinkHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Omron.Helper;

/// <summary>欧姆龙的OmronHostLink相关辅助方法</summary>
public class OmronHostLinkHelper
{
  /// <summary>验证欧姆龙的Fins-TCP返回的数据是否正确的数据，如果正确的话，并返回所有的数据内容</summary>
  /// <param name="send">发送的报文信息</param>
  /// <param name="response">来自欧姆龙返回的数据内容</param>
  /// <returns>带有是否成功的结果对象</returns>
  public static OperateResult<byte[]> ResponseValidAnalysis(byte[] send, byte[] response)
  {
    if (response.Length < 27)
      return new OperateResult<byte[]>($"{StringResources.Language.OmronReceiveDataError} Source Data: {response.ToHexString(' ')}");
    try
    {
      string str1 = Encoding.ASCII.GetString(send, 14, 4);
      string str2 = Encoding.ASCII.GetString(response, 15, 4);
      if (str2 != str1)
        return new OperateResult<byte[]>($"Send Command [{str1}] not the same as receive command [{str2}] source:[{SoftBasic.GetAsciiStringRender(response)}]");
      int int32;
      try
      {
        int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, 19, 4), 16 /*0x10*/);
      }
      catch (Exception ex)
      {
        return new OperateResult<byte[]>($"Get error code failed: {ex.Message}{Environment.NewLine}Source Data: {SoftBasic.GetAsciiStringRender(response)}");
      }
      byte[] numArray1 = new byte[0];
      if (response.Length > 27)
        numArray1 = SoftBasic.HexStringToBytes(Encoding.ASCII.GetString(response, 23, response.Length - 27));
      if (int32 > 0)
      {
        OperateResult<byte[]> operateResult = new OperateResult<byte[]>();
        operateResult.ErrorCode = int32;
        operateResult.Content = numArray1;
        operateResult.Message = OmronHostLinkHelper.GetErrorText(int32);
        return operateResult;
      }
      if (Encoding.ASCII.GetString(response, 15, 4) == "0104")
      {
        byte[] numArray2 = numArray1.Length != 0 ? new byte[numArray1.Length * 2 / 3] : new byte[0];
        for (int index = 0; index < numArray1.Length / 3; ++index)
        {
          numArray2[index * 2] = numArray1[index * 3 + 1];
          numArray2[index * 2 + 1] = numArray1[index * 3 + 2];
        }
        numArray1 = numArray2;
      }
      return OperateResult.CreateSuccessResult<byte[]>(numArray1);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"ResponseValidAnalysis failed: {ex.Message} Source: {response.ToHexString(' ')}");
    }
  }

  /// <summary>根据错误信息获取当前的文本描述信息</summary>
  /// <param name="error">错误代号</param>
  /// <returns>文本消息</returns>
  public static string GetErrorText(int error)
  {
    switch (error)
    {
      case 1:
        return "Service was canceled.";
      case 257:
        return "Local node is not participating in the network.";
      case 258:
        return "Token does not arrive.";
      case 259:
        return "Send was not possible during the specified number of retries.";
      case 260:
        return "Cannot send because maximum number of event frames exceeded.";
      case 261:
        return "Node address setting error occurred.";
      case 262:
        return "The same node address has been set twice in the same network.";
      case 513:
        return "The destination node is not in the network.";
      case 514:
        return "There is no Unit with the specified unit address.";
      case 515:
        return "The third node does not exist.";
      case 516:
        return "The destination node is busy.";
      case 517:
        return "The message was destroyed by noise";
      case 769:
        return "An error occurred in the communications controller.";
      case 770:
        return "A CPU error occurred in the destination CPU Unit.";
      case 771:
        return "A response was not returned because an error occurred in the Board.";
      case 772:
        return "The unit number was set incorrectly";
      case 1025:
        return "The Unit/Board does not support the specified command code.";
      case 1026:
        return "The command cannot be executed because the model or version is incorrect";
      case 1281:
        return "The destination network or node address is not set in the routing tables.";
      case 1282:
        return "Relaying is not possible because there are no routing tables";
      case 1283:
        return "There is an error in the routing tables.";
      case 1284:
        return "An attempt was made to send to a network that was over 3 networks away";
      case 4097:
        return "The command is longer than the maximum permissible length.";
      case 4098:
        return "The command is shorter than the minimum permissible length.";
      case 4099:
        return "The designated number of elements differs from the number of write data items.";
      case 4100:
        return "An incorrect format was used.";
      case 4101:
        return "Either the relay table in the local node or the local network table in the relay node is incorrect.";
      case 4353:
        return "The specified word does not exist in the memory area or there is no EM Area.";
      case 4354:
        return "The access size specification is incorrect or an odd word address is specified.";
      case 4355:
        return "The start address in command process is beyond the accessible area";
      case 4356:
        return "The end address in command process is beyond the accessible area.";
      case 4358:
        return "FFFF hex was not specified.";
      case 4361:
        return "A large–small relationship in the elements in the command data is incorrect.";
      case 4363:
        return "The response format is longer than the maximum permissible length.";
      case 4364:
        return "There is an error in one of the parameter settings.";
      case 8194:
        return "The program area is protected.";
      case 8195:
        return "A table has not been registered.";
      case 8196:
        return "The search data does not exist.";
      case 8197:
        return "A non-existing program number has been specified.";
      case 8198:
        return "The file does not exist at the specified file device.";
      case 8199:
        return "A data being compared is not the same.";
      case 8449:
        return "The specified area is read-only.";
      case 8450:
        return "The program area is protected.";
      case 8451:
        return "The file cannot be created because the limit has been exceeded.";
      case 8453:
        return "A non-existing program number has been specified.";
      case 8454:
        return "The file does not exist at the specified file device.";
      case 8455:
        return "A file with the same name already exists in the specified file device.";
      case 8456:
        return "The change cannot be made because doing so would create a problem.";
      case 8705:
      case 8706:
      case 8712:
        return "The mode is incorrect.";
      case 8707:
        return "The PLC is in PROGRAM mode.";
      case 8708:
        return "The PLC is in DEBUG mode.";
      case 8709:
        return "The PLC is in MONITOR mode.";
      case 8710:
        return "The PLC is in RUN mode.";
      case 8711:
        return "The specified node is not the polling node.";
      case 8961:
        return "The specified memory does not exist as a file device.";
      case 8962:
        return "There is no file memory.";
      case 8963:
        return "There is no clock.";
      case 9217:
        return "The data link tables have not been registered or they contain an error.";
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>
  /// 将 fins 命令的报文打包成 HostLink 格式的报文信息，打包之后的结果可以直接发送给PLC<br />
  /// Pack the message of the fins command into the message information in the HostLink format, and the packaged result can be sent directly to the PLC
  /// </summary>
  /// <param name="hostLink">HostLink协议的plc通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="cmd">fins命令</param>
  /// <returns>可发送PLC的完整的报文信息</returns>
  public static byte[] PackCommand(IHostLink hostLink, byte station, byte[] cmd)
  {
    cmd = SoftBasic.BytesToAsciiBytes(cmd);
    byte[] numArray = new byte[18 + cmd.Length];
    numArray[0] = (byte) 64 /*0x40*/;
    numArray[1] = SoftBasic.BuildAsciiBytesFrom(station)[0];
    numArray[2] = SoftBasic.BuildAsciiBytesFrom(station)[1];
    numArray[3] = (byte) 70;
    numArray[4] = (byte) 65;
    numArray[5] = hostLink.ResponseWaitTime;
    numArray[6] = SoftBasic.BuildAsciiBytesFrom(hostLink.ICF)[0];
    numArray[7] = SoftBasic.BuildAsciiBytesFrom(hostLink.ICF)[1];
    numArray[8] = SoftBasic.BuildAsciiBytesFrom(hostLink.DA2)[0];
    numArray[9] = SoftBasic.BuildAsciiBytesFrom(hostLink.DA2)[1];
    numArray[10] = SoftBasic.BuildAsciiBytesFrom(hostLink.SA2)[0];
    numArray[11] = SoftBasic.BuildAsciiBytesFrom(hostLink.SA2)[1];
    numArray[12] = SoftBasic.BuildAsciiBytesFrom(hostLink.SID)[0];
    numArray[13] = SoftBasic.BuildAsciiBytesFrom(hostLink.SID)[1];
    numArray[numArray.Length - 2] = (byte) 42;
    numArray[numArray.Length - 1] = (byte) 13;
    cmd.CopyTo((Array) numArray, 14);
    int num = (int) numArray[0];
    for (int index = 1; index < numArray.Length - 4; ++index)
      num ^= (int) numArray[index];
    numArray[numArray.Length - 4] = SoftBasic.BuildAsciiBytesFrom((byte) num)[0];
    numArray[numArray.Length - 3] = SoftBasic.BuildAsciiBytesFrom((byte) num)[1];
    return numArray;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Read(System.String,System.UInt16)" />
  public static OperateResult<byte[]> Read(IHostLink hostLink, string address, ushort length)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    OperateResult<List<byte[]>> operateResult = OmronFinsNetHelper.BuildReadCommand(hostLink.PlcType, address, length, false, hostLink.ReadSplits);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<byte[]>();
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < operateResult.Content.Count; ++index)
    {
      OperateResult<byte[]> result = hostLink.ReadFromCoreServer(OmronHostLinkHelper.PackCommand(hostLink, parameter, operateResult.Content[index]));
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
      byteList.AddRange((IEnumerable<byte>) result.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.Read(HslCommunication.Profinet.Omron.Helper.IOmronFins,System.String[])" />
  /// <remarks>如果需要需要额外指定站号的话，在第一个地址里，使用 s=2;D100 这种携带地址的功能</remarks>
  public static OperateResult<byte[]> Read(IHostLink hostLink, string[] address)
  {
    byte station = hostLink.UnitNumber;
    if (address != null && address.Length != 0)
      station = (byte) HslHelper.ExtractParameter(ref address[0], "s", (int) hostLink.UnitNumber);
    OperateResult<List<byte[]>> operateResult = OmronFinsNetHelper.BuildReadCommand(address, hostLink.PlcType);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<byte[]>();
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < operateResult.Content.Count; ++index)
    {
      OperateResult<byte[]> result = hostLink.ReadFromCoreServer(OmronHostLinkHelper.PackCommand(hostLink, station, operateResult.Content[index]));
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
      byteList.AddRange((IEnumerable<byte>) result.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Byte[])" />
  public static OperateResult Write(IHostLink hostLink, string address, byte[] value)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    OperateResult<byte[]> operateResult1 = OmronFinsNetHelper.BuildWriteWordCommand(hostLink.PlcType, address, value, false);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = hostLink.ReadFromCoreServer(OmronHostLinkHelper.PackCommand(hostLink, parameter, operateResult1.Content));
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Read(System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IHostLink hostLink,
    string address,
    ushort length)
  {
    byte station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    OperateResult<List<byte[]>> command = OmronFinsNetHelper.BuildReadCommand(hostLink.PlcType, address, length, false, hostLink.ReadSplits);
    if (!command.IsSuccess)
      return command.ConvertFailed<byte[]>();
    List<byte> contentArray = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await hostLink.ReadFromCoreServerAsync(OmronHostLinkHelper.PackCommand(hostLink, station, command.Content[i]));
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      contentArray.AddRange((IEnumerable<byte>) read.Content);
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(contentArray.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IHostLink hostLink,
    string address,
    byte[] value)
  {
    byte station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    OperateResult<byte[]> command = OmronFinsNetHelper.BuildWriteWordCommand(hostLink.PlcType, address, value, false);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await hostLink.ReadFromCoreServerAsync(OmronHostLinkHelper.PackCommand(hostLink, station, command.Content));
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkHelper.Read(HslCommunication.Profinet.Omron.Helper.IHostLink,System.String[])" />
  public static async Task<OperateResult<byte[]>> ReadAsync(IHostLink hostLink, string[] address)
  {
    byte station = hostLink.UnitNumber;
    string[] strArray = address;
    if (strArray != null && strArray.Length != 0)
      station = (byte) HslHelper.ExtractParameter(ref address[0], "s", (int) hostLink.UnitNumber);
    OperateResult<List<byte[]>> command = OmronFinsNetHelper.BuildReadCommand(address, hostLink.PlcType);
    if (!command.IsSuccess)
      return command.ConvertFailed<byte[]>();
    List<byte> contentArray = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await hostLink.ReadFromCoreServerAsync(OmronHostLinkHelper.PackCommand(hostLink, station, command.Content[i]));
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      contentArray.AddRange((IEnumerable<byte>) read.Content);
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(contentArray.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.ReadBool(System.String,System.UInt16)" />
  public static OperateResult<bool[]> ReadBool(IHostLink hostLink, string address, ushort length)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
    {
      if (!address.Contains("."))
        address += ".0";
      string str = (int) parameter != (int) hostLink.UnitNumber ? $"s={parameter};" : string.Empty;
      return HslHelper.ReadBool((IReadWriteNet) hostLink, str + address, length, reverseByWord: true);
    }
    OperateResult<List<byte[]>> result1 = OmronFinsNetHelper.BuildReadCommand(hostLink.PlcType, address, length, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = hostLink.ReadFromCoreServer(OmronHostLinkHelper.PackCommand(hostLink, parameter, result1.Content[index]));
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      if (result2.Content.Length == 0)
        return new OperateResult<bool[]>("Data is empty.");
      boolList.AddRange(((IEnumerable<byte>) result2.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)));
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Boolean[])" />
  public static OperateResult Write(IHostLink hostLink, string address, bool[] values)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
      return new OperateResult("DR and IR address not support bit write");
    OperateResult<byte[]> operateResult1 = OmronFinsNetHelper.BuildWriteWordCommand(hostLink.PlcType, address, ((IEnumerable<bool>) values).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = hostLink.ReadFromCoreServer(OmronHostLinkHelper.PackCommand(hostLink, parameter, operateResult1.Content));
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.ReadBool(System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IHostLink hostLink,
    string address,
    ushort length)
  {
    byte station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
    {
      if (!address.Contains("."))
        address += ".0";
      string stationAddress = (int) station != (int) hostLink.UnitNumber ? $"s={station};" : string.Empty;
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) hostLink, stationAddress + address, length, reverseByWord: true);
      return operateResult;
    }
    OperateResult<List<byte[]>> command = OmronFinsNetHelper.BuildReadCommand(hostLink.PlcType, address, length, true);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<bool> contentArray = new List<bool>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await hostLink.ReadFromCoreServerAsync(OmronHostLinkHelper.PackCommand(hostLink, station, command.Content[i]));
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      contentArray.AddRange(((IEnumerable<byte>) read.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)));
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(contentArray.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IHostLink hostLink,
    string address,
    bool[] values)
  {
    byte station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) hostLink.UnitNumber);
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
      return new OperateResult("DR and IR address not support bit write");
    OperateResult<byte[]> command = OmronFinsNetHelper.BuildWriteWordCommand(hostLink.PlcType, address, ((IEnumerable<bool>) values).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), true);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await hostLink.ReadFromCoreServerAsync(OmronHostLinkHelper.PackCommand(hostLink, station, command.Content));
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) read;
  }
}
