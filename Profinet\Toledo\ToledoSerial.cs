﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Toledo.ToledoSerial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.LogNet;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO.Ports;

#nullable disable
namespace HslCommunication.Profinet.Toledo;

/// <summary>托利多电子秤的串口服务器对象</summary>
public class ToledoSerial
{
  private SerialPort serialPort;
  private ILogNet logNet;
  private int receiveTimeout = 5000;

  /// <summary>实例化一个默认的对象</summary>
  public ToledoSerial()
  {
    this.serialPort = new SerialPort();
    this.serialPort.RtsEnable = true;
    this.serialPort.DataReceived += new SerialDataReceivedEventHandler(this.SerialPort_DataReceived);
  }

  private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
  {
    List<byte> byteList = new List<byte>();
    byte[] numArray1 = new byte[1024 /*0x0400*/];
    int num = 0;
    while (true)
    {
      do
      {
        HslHelper.ThreadSleep(20);
        if (this.serialPort.BytesToRead < 1)
          ++num;
        else
          goto label_2;
      }
      while (num < 3);
      break;
label_2:
      num = 0;
      try
      {
        int length = this.serialPort.Read(numArray1, 0, Math.Min(this.serialPort.BytesToRead, numArray1.Length));
        byte[] numArray2 = new byte[length];
        Array.Copy((Array) numArray1, 0, (Array) numArray2, 0, length);
        byteList.AddRange((IEnumerable<byte>) numArray2);
        if (this.HasChk)
        {
          if (byteList.Count > 15 && byteList[byteList.Count - 2] == (byte) 13)
            break;
        }
        else if (byteList.Count <= 15 || byteList[byteList.Count - 1] != (byte) 13)
        {
          if (byteList.Count > 15 && byteList[byteList.Count - 2] == (byte) 13)
            break;
        }
        else
          break;
      }
      catch (Exception ex)
      {
        this.logNet?.WriteException(this.ToString(), nameof (SerialPort_DataReceived), ex);
        return;
      }
    }
    if (byteList.Count == 0)
      return;
    byte[] array = byteList.ToArray();
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Receive} : {array.ToHexString(' ')}");
    ToledoStandardData toledoStandardData = (ToledoStandardData) null;
    try
    {
      toledoStandardData = new ToledoStandardData(array);
    }
    catch (Exception ex)
    {
      this.logNet?.WriteException(this.ToString(), "ToledoStandardData new failed: " + array.ToHexString(' '), ex);
    }
    if (toledoStandardData == null)
      return;
    ToledoSerial.ToledoStandardDataReceivedDelegate standardDataReceived = this.OnToledoStandardDataReceived;
    if (standardDataReceived == null)
      return;
    standardDataReceived((object) this, toledoStandardData);
  }

  /// <summary>
  /// 初始化串口信息，9600波特率，8位数据位，1位停止位，无奇偶校验<br />
  /// Initial serial port information, 9600 baud rate, 8 data bits, 1 stop bit, no parity
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  public void SerialPortInni(string portName) => this.SerialPortInni(portName, 9600);

  /// <summary>
  /// 初始化串口信息，波特率，8位数据位，1位停止位，无奇偶校验<br />
  /// Initializes serial port information, baud rate, 8-bit data bit, 1-bit stop bit, no parity
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  /// <param name="baudRate">波特率</param>
  public void SerialPortInni(string portName, int baudRate)
  {
    this.SerialPortInni(portName, baudRate, 8, StopBits.One, Parity.None);
  }

  /// <summary>
  /// 初始化串口信息，波特率，数据位，停止位，奇偶校验需要全部自己来指定<br />
  /// Start serial port information, baud rate, data bit, stop bit, parity all need to be specified
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  /// <param name="baudRate">波特率</param>
  /// <param name="dataBits">数据位</param>
  /// <param name="stopBits">停止位</param>
  /// <param name="parity">奇偶校验</param>
  public void SerialPortInni(
    string portName,
    int baudRate,
    int dataBits,
    StopBits stopBits,
    Parity parity)
  {
    if (this.serialPort.IsOpen)
      return;
    this.serialPort.PortName = portName;
    this.serialPort.BaudRate = baudRate;
    this.serialPort.DataBits = dataBits;
    this.serialPort.StopBits = stopBits;
    this.serialPort.Parity = parity;
    this.PortName = this.serialPort.PortName;
    this.BaudRate = this.serialPort.BaudRate;
  }

  /// <summary>
  /// 根据自定义初始化方法进行初始化串口信息<br />
  /// Initialize the serial port information according to the custom initialization method
  /// </summary>
  /// <param name="initi">初始化的委托方法</param>
  public void SerialPortInni(Action<SerialPort> initi)
  {
    if (this.serialPort.IsOpen)
      return;
    this.serialPort.PortName = "COM5";
    this.serialPort.BaudRate = 9600;
    this.serialPort.DataBits = 8;
    this.serialPort.StopBits = StopBits.One;
    this.serialPort.Parity = Parity.None;
    initi(this.serialPort);
    this.PortName = this.serialPort.PortName;
    this.BaudRate = this.serialPort.BaudRate;
  }

  /// <summary>
  /// 打开一个新的串行端口连接<br />
  /// Open a new serial port connection
  /// </summary>
  public void Open()
  {
    if (this.serialPort.IsOpen)
      return;
    this.serialPort.Open();
  }

  /// <summary>
  /// 获取一个值，指示串口是否处于打开状态<br />
  /// Gets a value indicating whether the serial port is open
  /// </summary>
  /// <returns>是或否</returns>
  public bool IsOpen() => this.serialPort.IsOpen;

  /// <summary>
  /// 关闭当前的串口连接<br />
  /// Close the current serial connection
  /// </summary>
  public void Close()
  {
    if (!this.serialPort.IsOpen)
      return;
    this.serialPort.Close();
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkBase.LogNet" />
  public ILogNet LogNet
  {
    get => this.logNet;
    set => this.logNet = value;
  }

  /// <summary>
  /// 获取或设置一个值，该值指示在串行通信中是否启用请求发送 (RTS) 信号。<br />
  /// Gets or sets a value indicating whether the request sending (RTS) signal is enabled in serial communication.
  /// </summary>
  public bool RtsEnable
  {
    get => this.serialPort.RtsEnable;
    set => this.serialPort.RtsEnable = value;
  }

  /// <summary>
  /// 当前连接串口信息的端口号名称<br />
  /// The port name of the current connection serial port information
  /// </summary>
  public string PortName { get; private set; }

  /// <summary>
  /// 当前连接串口信息的波特率<br />
  /// Baud rate of current connection serial port information
  /// </summary>
  public int BaudRate { get; private set; }

  /// <summary>
  /// 接收数据的超时时间，默认5000ms<br />
  /// Timeout for receiving data, default is 5000ms
  /// </summary>
  [HslMqttApi(Description = "Timeout for receiving data, default is 5000ms")]
  public int ReceiveTimeout
  {
    get => this.receiveTimeout;
    set => this.receiveTimeout = value;
  }

  /// <summary>获取或设置当前的报文否是含有校验的，默认为含有校验</summary>
  public bool HasChk { get; set; } = false;

  /// <summary>当接收到一条新的托利多的数据的时候触发</summary>
  public event ToledoSerial.ToledoStandardDataReceivedDelegate OnToledoStandardDataReceived;

  /// <inheritdoc />
  public override string ToString() => base.ToString();

  /// <summary>托利多数据接收时的委托</summary>
  /// <param name="sender">数据发送对象</param>
  /// <param name="toledoStandardData">数据对象</param>
  public delegate void ToledoStandardDataReceivedDelegate(
    object sender,
    ToledoStandardData toledoStandardData);
}
