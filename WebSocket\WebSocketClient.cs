﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.WebSocket.WebSocketClient
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Pipe;
using HslCommunication.LogNet;
using System;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.WebSocket;

/// <summary>
/// websocket协议的客户端实现，支持从服务器订阅，发布数据内容信息，详细参考api文档信息<br />
/// Client implementation of the websocket protocol. It supports subscribing from the server and publishing data content information.
/// </summary>
/// <example>
/// 本客户端使用起来非常的方便，基本就是实例化，绑定一个数据接收的事件即可，如下所示
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\WebSocket\WebSocketClientSample.cs" region="Sample1" title="简单的实例化" />
/// 假设我们需要发数据给服务端，那么可以参考如下的方式
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\WebSocket\WebSocketClientSample.cs" region="Sample2" title="发送数据" />
/// 如果我们需要搭配服务器来做订阅推送的功能的话，写法上会稍微有点区别，按照下面的代码来写。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\WebSocket\WebSocketClientSample.cs" region="Sample3" title="订阅操作" />
/// 当网络发生异常的时候，我们需要这么来进行重新连接。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\WebSocket\WebSocketClientSample.cs" region="Sample4" title="异常重连" />
/// </example>
public class WebSocketClient : IDisposable
{
  private int isReConnectServer = 0;
  private string[] subcribeTopics;
  private bool closed = false;
  private string ipAddress = string.Empty;
  private int port = 1883;
  private int connectTimeOut = 10000;
  private Timer timerCheck;
  private string url = string.Empty;
  private bool disposedValue;
  private string host = string.Empty;
  private bool useSSL = false;
  private X509Certificate certificate = (X509Certificate) null;
  private PipeTcpNet communicationPipe;
  private AsyncCallback beginReceiveCallback = (AsyncCallback) null;

  /// <summary>
  /// 使用指定的ip，端口来实例化一个默认的对象<br />
  /// Use the specified ip and port to instantiate a default objects
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号信息</param>
  public WebSocketClient(string ipAddress, int port)
  {
    this.beginReceiveCallback = new AsyncCallback(this.ReceiveAsyncCallback);
    this.host = ipAddress;
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <summary>
  /// 使用指定的ip，端口，额外的url信息来实例化一个默认的对象<br />
  /// Use the specified ip, port, and additional url information to instantiate a default object
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号信息</param>
  /// <param name="url">额外的信息，比如 /A/B?C=123456</param>
  public WebSocketClient(string ipAddress, int port, string url)
  {
    this.beginReceiveCallback = new AsyncCallback(this.ReceiveAsyncCallback);
    this.host = ipAddress;
    this.IpAddress = ipAddress;
    this.Port = port;
    this.url = url;
  }

  /// <summary>
  /// 使用指定的url来实例化一个默认的对象，例如 ws://127.0.0.1:1883/A/B?C=123456 或是 ws://www.hslcommunication.cn:1883<br />
  /// Use the specified url to instantiate a default object, such as ws://127.0.0.1:1883/A/B?C=123456 or ws://www.hslcommunication.cn:1883s
  /// </summary>
  /// <param name="url">完整的ws地址</param>
  public WebSocketClient(string url)
  {
    this.beginReceiveCallback = new AsyncCallback(this.ReceiveAsyncCallback);
    if (url.StartsWith("ws://", StringComparison.OrdinalIgnoreCase))
    {
      this.port = 80 /*0x50*/;
      this.PraseHost(url.Substring(5));
    }
    else
    {
      if (!url.StartsWith("wss://", StringComparison.OrdinalIgnoreCase))
        throw new Exception("Url Must start with ws:// or wss://");
      this.port = 443;
      this.PraseHost(url.Substring(6));
      this.UseSSL(string.Empty);
    }
  }

  private void PraseHost(string url)
  {
    if (url.IndexOf('/') < 0)
    {
      this.PraseIPHost(url);
    }
    else
    {
      this.PraseIPHost(url.Substring(0, url.IndexOf('/')));
      this.url = url.Substring(url.IndexOf('/'));
    }
  }

  private void PraseIPHost(string url)
  {
    if (url.IndexOf(':') < 0)
    {
      this.host = url;
      this.IpAddress = url;
    }
    else
    {
      this.host = url.Substring(0, url.IndexOf(':'));
      this.IpAddress = this.host;
      this.Port = int.Parse(url.Substring(url.IndexOf(':') + 1));
    }
  }

  /// <summary>
  /// Mqtt服务器的ip地址<br />
  /// IP address of Mqtt server
  /// </summary>
  public string IpAddress
  {
    get => this.ipAddress;
    set => this.ipAddress = HslHelper.GetIpAddressFromInput(value);
  }

  /// <summary>
  /// 端口号。默认1883<br />
  /// The port number. Default 1883
  /// </summary>
  public int Port
  {
    get => this.port;
    set => this.port = value;
  }

  /// <summary>
  /// 获取或设置连接时候的 GET 命令后面是否协议Host及端口号信息，默认为 <c>False</c>，不携带。<br />
  /// Obtain or set the protocol host and port number information after the GET command when connecting, which is <c>False</c> by default and does not carry it.
  /// </summary>
  public bool GetCarryHostAndPort { get; set; } = false;

  /// <inheritdoc cref="P:HslCommunication.Core.Net.BinaryCommunication.LogNet" />
  public ILogNet LogNet { get; set; }

  /// <summary>
  /// 连接服务器，实例化客户端之后，至少要调用成功一次，如果返回失败，那些请过一段时间后重新调用本方法连接。<br />
  /// After connecting to the server, the client must be called at least once after instantiating the client.
  /// If the return fails, please call this method to connect again after a period of time.
  /// </summary>
  /// <returns>连接是否成功</returns>
  public OperateResult ConnectServer() => this.ConnectServer(this.subcribeTopics);

  /// <summary>
  /// 连接服务器，实例化客户端之后，至少要调用成功一次，如果返回失败，那些请过一段时间后重新调用本方法连接。<br />
  /// After connecting to the server, the client must be called at least once after instantiating the client.
  /// If the return fails, please call this method to connect again after a period of time.
  /// </summary>
  /// <param name="subscribes">订阅的消息</param>
  /// <returns>连接是否成功</returns>
  public OperateResult ConnectServer(string[] subscribes)
  {
    this.subcribeTopics = subscribes;
    this.communicationPipe?.CloseCommunication();
    if (!this.useSSL)
    {
      this.communicationPipe = new PipeTcpNet(this.ipAddress, this.port)
      {
        ConnectTimeOut = this.connectTimeOut
      };
    }
    else
    {
      PipeSslNet pipeSslNet = new PipeSslNet(this.host, this.port, false);
      pipeSslNet.ConnectTimeOut = this.connectTimeOut;
      pipeSslNet.Certificate = this.certificate;
      this.communicationPipe = (PipeTcpNet) pipeSslNet;
    }
    OperateResult operateResult1 = (OperateResult) this.communicationPipe.OpenCommunication();
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult operateResult2 = this.communicationPipe.Send(WebSocketHelper.BuildWsSubRequest(this.ipAddress, this.port, this.url, this.subcribeTopics, this.GetCarryHostAndPort));
    if (!operateResult2.IsSuccess)
      return operateResult2;
    OperateResult<byte[]> operateResult3 = this.communicationPipe.Receive(-1, 10000);
    if (!operateResult3.IsSuccess)
      return (OperateResult) operateResult3;
    try
    {
      this.communicationPipe.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) this.communicationPipe);
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
    this.closed = false;
    WebSocketClient.OnClientConnectedDelegate onClientConnected = this.OnClientConnected;
    if (onClientConnected != null)
      onClientConnected();
    this.timerCheck?.Dispose();
    this.timerCheck = new Timer(new TimerCallback(this.TimerCheckServer), (object) null, 2000, 30000);
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 关闭与Websocket服务器的连接。<br />
  /// Close the connection to the Mqtt server.
  /// </summary>
  public void ConnectClose()
  {
    if (this.closed)
      return;
    this.SendWebsocketBytes(WebSocketHelper.WebScoketPackData(8, true, "Closed"));
    this.closed = true;
    HslHelper.ThreadSleep(20);
    this.communicationPipe?.CloseCommunication();
  }

  /// <inheritdoc cref="M:HslCommunication.WebSocket.WebSocketClient.ConnectServer" />
  public async Task<OperateResult> ConnectServerAsync()
  {
    OperateResult operateResult = await this.ConnectServerAsync(this.subcribeTopics);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.WebSocket.WebSocketClient.ConnectServer(System.String[])" />
  public async Task<OperateResult> ConnectServerAsync(string[] subscribes)
  {
    this.subcribeTopics = subscribes;
    this.communicationPipe?.CloseCommunication();
    if (!this.useSSL)
    {
      this.communicationPipe = new PipeTcpNet(this.ipAddress, this.port)
      {
        ConnectTimeOut = this.connectTimeOut
      };
    }
    else
    {
      PipeSslNet pipeSslNet = new PipeSslNet(this.ipAddress, this.port, false);
      pipeSslNet.ConnectTimeOut = this.connectTimeOut;
      pipeSslNet.Certificate = this.certificate;
      this.communicationPipe = (PipeTcpNet) pipeSslNet;
    }
    OperateResult<bool> operateResult = await this.communicationPipe.OpenCommunicationAsync();
    OperateResult open = (OperateResult) operateResult;
    operateResult = (OperateResult<bool>) null;
    if (!open.IsSuccess)
      return open;
    byte[] command = WebSocketHelper.BuildWsSubRequest(this.ipAddress, this.port, this.url, this.subcribeTopics, this.GetCarryHostAndPort);
    OperateResult send = await this.communicationPipe.SendAsync(command).ConfigureAwait(false);
    if (!send.IsSuccess)
      return send;
    OperateResult<byte[]> rece = await this.communicationPipe.ReceiveAsync(-1, 10000).ConfigureAwait(false);
    if (!rece.IsSuccess)
      return (OperateResult) rece;
    try
    {
      this.communicationPipe.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) this.communicationPipe);
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
    this.closed = false;
    WebSocketClient.OnClientConnectedDelegate onClientConnected = this.OnClientConnected;
    if (onClientConnected != null)
      onClientConnected();
    this.timerCheck?.Dispose();
    this.timerCheck = new Timer(new TimerCallback(this.TimerCheckServer), (object) null, 2000, 30000);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.WebSocket.WebSocketClient.ConnectClose" />
  public async Task ConnectCloseAsync()
  {
    if (this.closed)
      return;
    OperateResult operateResult = await this.communicationPipe.SendAsync(WebSocketHelper.WebScoketPackData(8, true, "Closed")).ConfigureAwait(false);
    this.closed = true;
    HslHelper.ThreadSleep(20);
    this.communicationPipe?.CloseCommunication();
  }

  private void OnWebsocketNetworkError()
  {
    if (this.closed)
      return;
    if (Interlocked.CompareExchange(ref this.isReConnectServer, 1, 0) != 0)
      return;
    try
    {
      if (this.OnNetworkError == null)
      {
        this.LogNet?.WriteInfo(this.ToString(), "The network is abnormal, and the system is ready to automatically reconnect after 10 seconds.");
        while (true)
        {
          for (int index = 0; index < 10; ++index)
          {
            if (this.closed)
            {
              Interlocked.Exchange(ref this.isReConnectServer, 0);
              return;
            }
            HslHelper.ThreadSleep(1000);
            this.LogNet?.WriteInfo(this.ToString(), $"Wait for {10 - index} second to connect to the server ...");
          }
          if (!this.closed)
          {
            if (!this.ConnectServer().IsSuccess)
              this.LogNet?.WriteInfo(this.ToString(), "The connection failed. Prepare to reconnect after 10 seconds.");
            else
              goto label_14;
          }
          else
            break;
        }
        Interlocked.Exchange(ref this.isReConnectServer, 0);
        return;
label_14:
        this.LogNet?.WriteInfo(this.ToString(), "Successfully connected to the server!");
      }
      else
      {
        EventHandler onNetworkError = this.OnNetworkError;
        if (onNetworkError != null)
          onNetworkError((object) this, new EventArgs());
      }
      Interlocked.Exchange(ref this.isReConnectServer, 0);
    }
    catch
    {
      Interlocked.Exchange(ref this.isReConnectServer, 0);
      throw;
    }
  }

  private async void ReceiveAsyncCallback(IAsyncResult ar)
  {
    if (!(ar.AsyncState is PipeTcpNet pipeTcpNet))
    {
      pipeTcpNet = (PipeTcpNet) null;
    }
    else
    {
      Socket socket = pipeTcpNet.Socket;
      try
      {
        socket.EndReceive(ar);
      }
      catch (ObjectDisposedException ex)
      {
        socket?.Close();
        ILogNet logNet = this.LogNet;
        if (logNet == null)
        {
          pipeTcpNet = (PipeTcpNet) null;
          return;
        }
        logNet.WriteDebug(this.ToString(), "Closed");
        pipeTcpNet = (PipeTcpNet) null;
        return;
      }
      catch (Exception ex)
      {
        socket?.Close();
        this.LogNet?.WriteDebug(this.ToString(), "ReceiveCallback Failed:" + ex.Message);
        this.OnWebsocketNetworkError();
        pipeTcpNet = (PipeTcpNet) null;
        return;
      }
      if (this.closed)
      {
        ILogNet logNet = this.LogNet;
        if (logNet == null)
        {
          pipeTcpNet = (PipeTcpNet) null;
        }
        else
        {
          logNet.WriteDebug(this.ToString(), "Closed");
          pipeTcpNet = (PipeTcpNet) null;
        }
      }
      else
      {
        OperateResult<WebSocketMessage> read = (OperateResult<WebSocketMessage>) null;
        read = await WebSocketHelper.ReceiveWebSocketPayloadAsync((CommunicationPipe) pipeTcpNet).ConfigureAwait(false);
        if (!read.IsSuccess)
        {
          this.OnWebsocketNetworkError();
          pipeTcpNet = (PipeTcpNet) null;
        }
        else
        {
          if (read.Content.OpCode == 9)
          {
            this.SendWebsocketBytes(WebSocketHelper.WebScoketPackData(10, true, read.Content.Payload));
            this.LogNet?.WriteDebug(this.ToString(), read.Content.ToString());
          }
          else if (read.Content.OpCode == 10)
          {
            this.LogNet?.WriteDebug(this.ToString(), read.Content.ToString());
          }
          else
          {
            WebSocketClient.OnClientApplicationMessageReceiveDelegate applicationMessageReceive = this.OnClientApplicationMessageReceive;
            if (applicationMessageReceive != null)
              applicationMessageReceive(read.Content);
          }
          try
          {
            socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) pipeTcpNet);
          }
          catch (Exception ex)
          {
            socket?.Close();
            this.LogNet?.WriteDebug(this.ToString(), "BeginReceive Failed:" + ex.Message);
            this.OnWebsocketNetworkError();
          }
          socket = (Socket) null;
          read = (OperateResult<WebSocketMessage>) null;
          pipeTcpNet = (PipeTcpNet) null;
        }
      }
    }
  }

  private void TimerCheckServer(object obj)
  {
    if (this.communicationPipe == null)
      ;
  }

  /// <summary>
  /// 使用一个证书路径来初始化 SSL/TLS 通信<br />
  /// Use a certificate path to initialize SSL/TLS communication
  /// </summary>
  /// <param name="certificateFile">证书路径</param>
  public void UseSSL(string certificateFile)
  {
    this.useSSL = true;
    if (string.IsNullOrEmpty(certificateFile))
      return;
    this.certificate = X509Certificate.CreateFromCertFile(certificateFile);
  }

  /// <summary>
  /// 使用一个证书来初始化 SSL/TLS 通信<br />
  /// Use a certificate to initiate SSL/TLS communication
  /// </summary>
  /// <param name="certificate">证书路径</param>
  public void UseSSL(X509Certificate certificate)
  {
    this.useSSL = true;
    this.certificate = certificate;
  }

  /// <summary>
  /// 发送数据到WebSocket的服务器<br />
  /// Send data to WebSocket server
  /// </summary>
  /// <param name="message">消息</param>
  /// <returns>是否发送成功</returns>
  public OperateResult SendServer(string message)
  {
    return this.SendWebsocketBytes(WebSocketHelper.WebScoketPackData(1, true, message));
  }

  /// <summary>
  /// 发送数据到WebSocket的服务器，可以指定是否进行掩码操作<br />
  /// Send data to the WebSocket server, you can specify whether to perform a mask operation
  /// </summary>
  /// <param name="mask">是否进行掩码操作</param>
  /// <param name="message">消息</param>
  /// <returns>是否发送成功</returns>
  public OperateResult SendServer(bool mask, string message)
  {
    return this.SendWebsocketBytes(WebSocketHelper.WebScoketPackData(1, mask, message));
  }

  /// <summary>
  /// 发送自定义的命令到WebSocket服务器，可以指定操作码，是否掩码操作，原始字节数据<br />
  /// Send custom commands to the WebSocket server, you can specify the operation code, whether to mask operation, raw byte data
  /// </summary>
  /// <param name="opCode">操作码</param>
  /// <param name="mask">是否进行掩码操作</param>
  /// <param name="payload">原始字节数据</param>
  /// <returns>是否发送成功</returns>
  public OperateResult SendServer(int opCode, bool mask, byte[] payload)
  {
    return this.SendWebsocketBytes(WebSocketHelper.WebScoketPackData(opCode, mask, payload));
  }

  private OperateResult SendWebsocketBytes(byte[] data) => this.communicationPipe.Send(data);

  /// <summary>
  ///  websocket的消息收到时触发<br />
  ///  Triggered when a websocket message is received
  /// </summary>
  public event WebSocketClient.OnClientApplicationMessageReceiveDelegate OnClientApplicationMessageReceive;

  /// <summary>
  /// 当客户端连接成功触发事件，就算是重新连接服务器后，也是会触发的<br />
  /// The event is triggered when the client is connected successfully, even after reconnecting to the server.
  /// </summary>
  public event WebSocketClient.OnClientConnectedDelegate OnClientConnected;

  /// <summary>当网络发生异常的时候触发的事件，用户应该在事件里进行重连服务器</summary>
  public event EventHandler OnNetworkError;

  /// <summary>
  /// 获取或设置当前客户端的连接超时时间，默认10,000毫秒，单位ms<br />
  /// Gets or sets the connection timeout of the current client. The default is 10,000 milliseconds. The unit is ms.
  /// </summary>
  public int ConnectTimeOut
  {
    get => this.connectTimeOut;
    set => this.connectTimeOut = value;
  }

  /// <summary>
  /// 获取当前的客户端状态是否关闭了连接，当自己手动处理网络异常事件的时候，在重连之前就需要判断是否关闭了连接。<br />
  /// Obtain whether the current client status has closed the connection. When manually handling network abnormal events, you need to determine whether the connection is closed before reconnecting.
  /// </summary>
  public bool IsClosed => this.closed;

  /// <summary>释放当前的对象</summary>
  /// <param name="disposing"></param>
  protected virtual void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (disposing)
    {
      this.OnClientApplicationMessageReceive = (WebSocketClient.OnClientApplicationMessageReceiveDelegate) null;
      this.OnClientConnected = (WebSocketClient.OnClientConnectedDelegate) null;
      this.OnNetworkError = (EventHandler) null;
    }
    this.disposedValue = true;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    this.Dispose(true);
    GC.SuppressFinalize((object) this);
  }

  /// <inheritdoc />
  public override string ToString() => $"WebSocketClient[{this.ipAddress}:{this.port}]";

  /// <summary>
  /// websocket的消息收到委托<br />
  /// websocket message received delegate
  /// </summary>
  /// <param name="message">websocket的消息</param>
  public delegate void OnClientApplicationMessageReceiveDelegate(WebSocketMessage message);

  /// <summary>
  /// 连接服务器成功的委托<br />
  /// Connection server successfully delegated
  /// </summary>
  public delegate void OnClientConnectedDelegate();
}
