﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.S7PlusHelper.S7Object
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Collections.Generic;
using System.IO;

#nullable disable
namespace HslCommunication.Profinet.Siemens.S7PlusHelper;

/// <summary>西门子的数据对象信息</summary>
public class S7Object : IS7Object
{
  /// <summary>RelationId</summary>
  public uint RelationId { get; set; }

  /// <summary>第二个关联的信息</summary>
  public uint RelationId2 { get; set; }

  /// <summary>ClassId</summary>
  public uint ClassId { get; set; }

  /// <summary>ClassFlags</summary>
  public uint ClassFlags { get; set; }

  /// <summary>AttributeId</summary>
  public uint AttributeId { get; set; }

  /// <summary>名称信息</summary>
  public string Name { get; set; }

  /// <summary>数据对象关联的子对象信息</summary>
  public List<S7Object> SubObjects { get; set; }

  /// <summary>关联的节点信息</summary>
  public List<S7Tag> S7Tags { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.S7PlusHelper.IS7Object.WriteMessgae(System.IO.MemoryStream)" />
  public void WriteMessgae(MemoryStream ms)
  {
    S7Object.WriteUint32(ms, 0U);
    S7Object.WriteUint32(ms, this.RelationId);
    S7Object.WriteUint32(ms, 2U);
    S7Object.WriteUint32(ms, 2550U);
    S7Object.WriteUint32(ms, 1U);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.S7PlusHelper.IS7Object.GetNumberOfFields" />
  public int GetNumberOfFields() => 5;

  /// <inheritdoc />
  public override string ToString() => $"S7Object[{this.Name}]";

  private static int GetLongLength(ulong value)
  {
    ulong num = 128 /*0x80*/;
    int longLength = 0;
    while (true)
    {
      ++longLength;
      if (longLength < 9 && value >= num)
        num <<= 7;
      else
        break;
    }
    return longLength;
  }

  private static void WriteAutoLength(MemoryStream ms, int len, ulong value)
  {
    byte[] buffer = new byte[len];
    for (int index = len - 1; index >= 0; --index)
    {
      if (index == len - 1)
      {
        if (index == 8)
        {
          buffer[index] = (byte) value;
          value >>= 8;
        }
        else
        {
          buffer[index] = (byte) (value & (ulong) sbyte.MaxValue);
          value >>= 7;
        }
      }
      else
      {
        buffer[index] = (byte) (value | 128UL /*0x80*/);
        value >>= 7;
      }
    }
    ms.Write(buffer, 0, buffer.Length);
  }

  /// <summary>写入一个uint类型的数据，使用动态长度表示方式，写入到字节流里去</summary>
  /// <param name="ms">字节流</param>
  /// <param name="value">值信息</param>
  public static void WriteUint32(MemoryStream ms, uint value)
  {
    S7Object.WriteAutoLength(ms, S7Object.GetLongLength((ulong) value), (ulong) value);
  }

  /// <summary>写入一个ulong类型的数据，使用动态长度表示方式，写入到字节流里去</summary>
  /// <param name="ms">字节流</param>
  /// <param name="value">值信息</param>
  public static void WriteUint64(MemoryStream ms, ulong value)
  {
    S7Object.WriteAutoLength(ms, S7Object.GetLongLength(value), value);
  }

  /// <summary>从缓存中获取动态长度的uint类型数据</summary>
  /// <param name="buffer">缓存值</param>
  /// <param name="index">索引信息</param>
  /// <returns>结果值</returns>
  public static uint GetValueUint32(byte[] buffer, ref int index)
  {
    int valueUint32 = 0;
    int num = 0;
    for (int index1 = 0; index1 < 5; ++index1)
    {
      ++num;
      valueUint32 = (valueUint32 << 7) + (int) (byte) ((uint) buffer[index + index1] & (uint) sbyte.MaxValue);
      if (!buffer[index + index1].GetBoolByIndex(7))
        break;
    }
    index += num;
    return (uint) valueUint32;
  }

  /// <summary>从缓存中获取动态长度的ulong类型数据</summary>
  /// <param name="buffer">缓存值</param>
  /// <param name="index">索引信息</param>
  /// <returns>结果值</returns>
  public static ulong GetValueUint64(byte[] buffer, ref int index)
  {
    ulong valueUint64 = 0;
    int num = 0;
    for (int index1 = 0; index1 < 9; ++index1)
    {
      ++num;
      if (index1 == 8)
      {
        valueUint64 = (valueUint64 << 8) + (ulong) buffer[index + index1];
      }
      else
      {
        valueUint64 = (valueUint64 << 7) + (ulong) (byte) ((uint) buffer[index + index1] & (uint) sbyte.MaxValue);
        if (!buffer[index + index1].GetBoolByIndex(7))
          break;
      }
    }
    index += num;
    return valueUint64;
  }
}
