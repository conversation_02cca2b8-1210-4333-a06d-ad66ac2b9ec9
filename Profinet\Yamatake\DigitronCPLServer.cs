﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Yamatake.DigitronCPLServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.Yamatake.Helper;
using HslCommunication.Reflection;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Yamatake;

/// <summary>
/// 山武的数字指示调节器的虚拟设备，支持和HSL本身进行数据通信测试<br />
/// Yamatake’s digital indicating regulator is a virtual device that supports data communication testing with HSL itself
/// </summary>
public class DigitronCPLServer : DeviceServer
{
  private SoftBuffer softBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public DigitronCPLServer()
  {
    this.softBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.Station = (byte) 1;
    this.LogMsgFormatBinary = false;
  }

  /// <summary>
  /// 获取或设置当前虚拟仪表的站号信息，如果站号不一致，将不予访问<br />
  /// Get or set the station number information of the current virtual instrument. If the station number is inconsistent, it will not be accessed
  /// </summary>
  public byte Station { get; set; }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    try
    {
      return OperateResult.CreateSuccessResult<byte[]>(this.softBuffer.GetBytes((int) ushort.Parse(address) * 2, (int) length * 2));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Read Failed: " + ex.Message);
    }
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    try
    {
      ushort num = ushort.Parse(address);
      this.softBuffer.SetBytes(value, (int) num * 2);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return new OperateResult("Write Failed: " + ex.Message);
    }
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 10);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return OperateResult.CreateSuccessResult<byte[]>(this.ReadFromCore(session, receive));
  }

  private byte[] ReadFromCore(PipeSession session, byte[] command)
  {
    try
    {
      int num1 = 9;
      for (int index = 9; index < command.Length; ++index)
      {
        if (command[index] == (byte) 3)
        {
          num1 = index;
          break;
        }
      }
      byte num2 = Convert.ToByte(Encoding.ASCII.GetString(command, 1, 2), 16 /*0x10*/);
      if ((int) num2 != (int) this.Station)
      {
        this.LogNet?.WriteDebug(this.ToString(), $"<{session.Communication}> Station not same, need: {this.Station} but {num2}");
        return DigitronCPLHelper.PackResponseContent(this.Station, 40, (byte[]) null, (byte) 87);
      }
      string[] strArray = Encoding.ASCII.GetString(command, 9, num1 - 9).Split(new char[1]
      {
        ','
      }, StringSplitOptions.RemoveEmptyEntries);
      string str = Encoding.ASCII.GetString(command, 6, 2);
      int num3 = int.Parse(strArray[0].Substring(0, strArray[0].Length - 1));
      byte dataType = strArray[0].EndsWith("W") ? (byte) 87 : (byte) 83;
      if (num3 >= 65536 /*0x010000*/ || num3 < 0)
        return DigitronCPLHelper.PackResponseContent(this.Station, 42, (byte[]) null, dataType);
      switch (str)
      {
        case "RS":
          int num4 = int.Parse(strArray[1]);
          if (num3 + num4 > (int) ushort.MaxValue)
            return DigitronCPLHelper.PackResponseContent(this.Station, 42, (byte[]) null, dataType);
          return num4 > 16 /*0x10*/ ? DigitronCPLHelper.PackResponseContent(this.Station, 41, (byte[]) null, dataType) : DigitronCPLHelper.PackResponseContent(this.Station, 0, this.softBuffer.GetBytes(num3 * 2, num4 * 2), dataType);
        case "WS":
          if (!this.EnableWrite)
            return DigitronCPLHelper.PackResponseContent(this.Station, 46, (byte[]) null, dataType);
          if (strArray.Length > 17)
            return DigitronCPLHelper.PackResponseContent(this.Station, 41, (byte[]) null, dataType);
          byte[] data = new byte[(strArray.Length - 1) * 2];
          for (int index = 1; index < strArray.Length; ++index)
          {
            if (dataType == (byte) 87)
              BitConverter.GetBytes(short.Parse(strArray[index])).CopyTo((Array) data, index * 2 - 2);
            else
              BitConverter.GetBytes(ushort.Parse(strArray[index])).CopyTo((Array) data, index * 2 - 2);
          }
          this.softBuffer.SetBytes(data, num3 * 2);
          return DigitronCPLHelper.PackResponseContent(this.Station, 0, (byte[]) null, dataType);
        default:
          return DigitronCPLHelper.PackResponseContent(this.Station, 40, (byte[]) null, dataType);
      }
    }
    catch
    {
      return (byte[]) null;
    }
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength > 5 ? buffer[receivedLength - 2] == (byte) 13 && buffer[receivedLength - 1] == (byte) 10 : base.CheckSerialReceiveDataComplete(buffer, receivedLength);
  }

  /// <inheritdoc />
  public override string ToString() => $"DigitronCPLServer[{this.Port}]";
}
