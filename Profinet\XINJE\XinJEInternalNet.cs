﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.XINJE.XinJEInternalNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.ModBus;
using HslCommunication.Reflection;
using System.Collections.Generic;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.XINJE;

/// <summary>
/// 信捷内部的TCP信息，该协议是信捷基于modbus协议扩展而来，支持更多的地址类型，以及更广泛的地址范围。具体参考Demo界面信息。<br />
/// The TCP information inside Xinjie, which is extended by Xinjie based on the modbus protocol,
/// supports more address types and a wider range of addresses. For details, refer to the Demo UI.
/// </summary>
public class XinJEInternalNet : DeviceTcpNet
{
  private byte station = 1;
  private readonly SoftIncrementCount softIncrementCount;

  /// <summary>
  /// 实例化一个XINJE-Tcp协议的客户端对象<br />
  /// Instantiate a client object of the Modbus-Tcp protocol
  /// </summary>
  public XinJEInternalNet()
  {
    this.softIncrementCount = new SoftIncrementCount((long) ushort.MaxValue);
    this.WordLength = (ushort) 1;
    this.station = (byte) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
  }

  /// <summary>
  /// 指定服务器地址，端口号，客户端自己的站号来初始化<br />
  /// Specify the server address, port number, and client's own station number to initialize
  /// </summary>
  /// <param name="ipAddress">服务器的Ip地址</param>
  /// <param name="port">服务器的端口号</param>
  /// <param name="station">客户端自身的站号</param>
  public XinJEInternalNet(string ipAddress, int port = 502, byte station = 1)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
    this.station = station;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new ModbusTcpMessage();

  /// <summary>
  /// 获取或者重新修改服务器的默认站号信息，当然，你可以再读写的时候动态指定，参见备注<br />
  /// Get or modify the default station number information of the server. Of course, you can specify it dynamically when reading and writing, see note
  /// </summary>
  /// <remarks>
  /// 当你调用 ReadCoil("100") 时，对应的站号就是本属性的值，当你调用 ReadCoil("s=2;100") 时，就忽略本属性的值，读写寄存器的时候同理
  /// </remarks>
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.IByteTransform.DataFormat" />
  public DataFormat DataFormat
  {
    get => this.ByteTransform.DataFormat;
    set => this.ByteTransform.DataFormat = value;
  }

  /// <summary>
  /// 字符串数据是否按照字来反转，默认为False<br />
  /// Whether the string data is reversed according to words. The default is False.
  /// </summary>
  /// <remarks>字符串按照2个字节的排列进行颠倒，根据实际情况进行设置</remarks>
  public bool IsStringReverse
  {
    get => this.ByteTransform.IsStringReverseByteWord;
    set => this.ByteTransform.IsStringReverseByteWord = value;
  }

  /// <summary>
  /// 获取协议自增的消息号，你可以自定义modbus的消息号的规则，详细参见<see cref="T:HslCommunication.Profinet.XINJE.XinJEInternalNet" />说明，也可以查找<see cref="T:HslCommunication.BasicFramework.SoftIncrementCount" />说明。<br />
  /// Get the message number incremented by the modbus protocol. You can customize the rules of the message number of the modbus. For details, please refer to the description of <see cref="T:HslCommunication.ModBus.ModbusTcpNet" />, or you can find the description of <see cref="T:HslCommunication.BasicFramework.SoftIncrementCount" />
  /// </summary>
  public SoftIncrementCount MessageId => this.softIncrementCount;

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return ModbusInfo.PackCommandToTcp(command, (ushort) this.softIncrementCount.GetCurrentValue());
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return ModbusInfo.ExtractActualData(ModbusInfo.ExplodeTcpCommandToCore(response));
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址支持 D100, SD100, TD100, CD100, HD100, FD100, ETD100, HTD100, HCD100, HSD100, 各自的地址范围取决于实际PLC的范围，比如D的地址在XLH型号上可达 0~499999<br />
  /// Address support D100, SD100, TD100, CD100, HD100, FD100, ETD100, HTD100, HCD100, HSD100, the respective address range depends on the actual PLC range, for example, the address of D can reach 0~499999 on the XLH model
  /// </remarks>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<List<byte[]>> result = XinJEHelper.BuildReadCommand(this.Station, address, length, false);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : this.ReadFromCoreServer((IEnumerable<byte[]>) result.Content);
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址支持 M100, X100, Y100, SM100, T100, C100, HM100, HS100, HT100, HSC100 各自的地址范围取决于实际PLC的范围，比如M的地址在XLH型号上可达 0~199999<br />
  /// The address supports M100, X100, Y100, SM100, T100, C100, HM100, HS100, HT100, HSC100, the respective address range depends on the actual PLC range, for example, the address of M can reach 0~199999 on the XLH model
  /// </remarks>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<List<byte[]>> result1 = XinJEHelper.BuildReadCommand(this.Station, address, length, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer((IEnumerable<byte[]>) result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<bool[]>(result2.Content.ToBoolArray().SelectBegin<bool>((int) length));
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址支持 D100, SD100, TD100, CD100, HD100, FD100, ETD100, HTD100, HCD100, HSD100,  各自的地址范围取决于实际PLC的范围，比如D的地址在XLH型号上可达 0~499999<br />
  /// Address support D100, SD100, TD100, CD100, HD100, FD100, ETD100, HTD100, HCD100, HSD100,  the respective address range depends on the actual PLC range, for example, the address of D can reach 0~499999 on the XLH model
  /// </remarks>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = XinJEHelper.BuildWriteWordCommand(this.Station, address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址支持 M100, Y100, SM100, T100, C100, 各自的地址范围取决于实际PLC的范围，比如M的地址在XLH型号上可达 0~199999<br />
  /// The address supports M100, Y100, SM100, T100, C100, the respective address range depends on the actual PLC range, for example, the address of M can reach 0~199999 on the XLH model
  /// </remarks>
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<byte[]> operateResult = XinJEHelper.BuildWriteBoolCommand(this.Station, address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJEInternalNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<List<byte[]>> command = XinJEHelper.BuildReadCommand(this.Station, address, length, false);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJEInternalNet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<List<byte[]>> command = XinJEHelper.BuildReadCommand(this.Station, address, length, true);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray().SelectBegin<bool>((int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJEInternalNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> command = XinJEHelper.BuildWriteWordCommand(this.Station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJEInternalNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult<byte[]> command = XinJEHelper.BuildWriteBoolCommand(this.Station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"XinJEInternalNet[{this.IpAddress}:{this.Port}]";
}
