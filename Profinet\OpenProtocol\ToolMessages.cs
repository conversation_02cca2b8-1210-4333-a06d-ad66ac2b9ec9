﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.ToolMessages
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>工具类消息</summary>
public class ToolMessages
{
  private OpenProtocolNet openProtocol;

  /// <summary>指定Open通信来实例化一个工具类消息的对象</summary>
  /// <param name="openProtocol">工具类消息</param>
  public ToolMessages(OpenProtocolNet openProtocol) => this.openProtocol = openProtocol;

  /// <summary>
  /// A request for some of the data stored in the tool. The result of this command is the transmission of the tool data.
  /// </summary>
  /// <param name="revision">Revision</param>
  /// <returns>工具数据的结果对象</returns>
  public OperateResult<ToolData> ToolDataUpload(int revision = 1)
  {
    OperateResult<string> result = this.openProtocol.ReadCustomer(40, revision, -1, -1, (List<string>) null);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<ToolData>((OperateResult) result) : this.PraseMID0041(result.Content);
  }

  /// <summary>Disable tool.</summary>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult DisableTool()
  {
    return (OperateResult) this.openProtocol.ReadCustomer(42, 1, -1, -1, (List<string>) null);
  }

  /// <summary>Enable tool</summary>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult EnableTool()
  {
    return (OperateResult) this.openProtocol.ReadCustomer(43, 1, -1, -1, (List<string>) null);
  }

  /// <summary>
  /// This command is sent by the integrator in order to request the possibility to disconnect the tool from the controller.The command is rejected if the tool is currently used.
  /// </summary>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult DisconnectToolRequest()
  {
    return (OperateResult) this.openProtocol.ReadCustomer(44, 1, -1, -1, (List<string>) null);
  }

  /// <summary>
  /// This message is sent by the integrator in order to set the calibration value of the tool.
  /// </summary>
  /// <param name="calibrationValueUnit">The unit in which the calibration value is sent. 1=Nm, 2=Lbf.ft, 3=Lbf.In, 4=Kpm</param>
  /// <param name="calibrationValue">The calibration value</param>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult SetCalibrationValueRequest(int calibrationValueUnit, double calibrationValue)
  {
    return (OperateResult) this.openProtocol.ReadCustomer(45, 1, -1, -1, new List<string>()
    {
      "01" + calibrationValueUnit.ToString(),
      "02" + Convert.ToInt32(calibrationValue * 100.0).ToString("D6")
    });
  }

  private OperateResult<ToolData> PraseMID0041(string reply)
  {
    try
    {
      int int32 = Convert.ToInt32(reply.Substring(8, 3));
      ToolData toolData = new ToolData();
      toolData.ToolSerialNumber = reply.Substring(22, 14);
      toolData.ToolNumberOfTightening = Convert.ToUInt32(reply.Substring(38, 10));
      toolData.LastCalibrationDate = DateTime.ParseExact(reply.Substring(50, 19), "yyyy-MM-dd:HH:mm:ss", (IFormatProvider) null);
      toolData.ControllerSerialNumber = reply.Substring(71, 10);
      if (int32 > 1)
      {
        toolData.CalibrationValue = Convert.ToDouble(reply.Substring(83, 6)) / 100.0;
        toolData.LastServiceDate = DateTime.ParseExact(reply.Substring(91, 19), "yyyy-MM-dd:HH:mm:ss", (IFormatProvider) null);
        toolData.TighteningsSinceService = Convert.ToUInt32(reply.Substring(112 /*0x70*/, 10));
        toolData.ToolType = Convert.ToInt32(reply.Substring(124, 2));
        toolData.MotorSize = Convert.ToInt32(reply.Substring(128 /*0x80*/, 2));
        toolData.UseOpenEnd = reply[132] == '1';
        toolData.TighteningDirection = reply[133] == '1' ? "CCW" : "CW";
        toolData.MotorRotation = Convert.ToInt32(reply.Substring(134, 1));
        toolData.ControllerSoftwareVersion = reply.Substring(137, 19);
      }
      return OperateResult.CreateSuccessResult<ToolData>(toolData);
    }
    catch (Exception ex)
    {
      return new OperateResult<ToolData>($"MID0031 prase failed: {ex.Message}{Environment.NewLine}Source: {reply}");
    }
  }
}
