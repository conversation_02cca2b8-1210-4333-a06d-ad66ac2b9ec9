﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.GenerateMode
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>日志文件输出模式</summary>
public enum GenerateMode
{
  /// <summary>按每分钟生成日志文件</summary>
  ByEveryMinute = 1,
  /// <summary>按每个小时生成日志文件</summary>
  ByEveryHour = 2,
  /// <summary>按每天生成日志文件</summary>
  ByEveryDay = 3,
  /// <summary>按每个周生成日志文件</summary>
  ByEveryWeek = 4,
  /// <summary>按每个月生成日志文件</summary>
  ByEveryMonth = 5,
  /// <summary>按每季度生成日志文件</summary>
  ByEverySeason = 6,
  /// <summary>按每年生成日志文件</summary>
  ByEveryYear = 7,
}
