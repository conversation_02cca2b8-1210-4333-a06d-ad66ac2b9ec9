﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.FormAuthorize
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>用来测试版软件授权的窗口</summary>
public class FormAuthorize : Form
{
  private SoftAuthorize softAuthorize = (SoftAuthorize) null;
  private Func<string, string> Encrypt = (Func<string, string>) null;
  /// <summary>Required designer variable.</summary>
  private IContainer components = (IContainer) null;
  private TextBox textBox2;
  private TextBox textBox1;
  private Label label2;
  private Label label1;
  private Button userButton1;
  private LinkLabel linkLabel1;

  /// <summary>实例化授权注册窗口</summary>
  /// <param name="authorize"></param>
  /// <param name="aboutCode">提示关于怎么获取注册码的信息</param>
  /// <param name="encrypt">加密的方法</param>
  public FormAuthorize(SoftAuthorize authorize, string aboutCode, Func<string, string> encrypt)
  {
    this.InitializeComponent();
    this.softAuthorize = authorize;
    this.AboutCode = aboutCode;
    this.Encrypt = encrypt;
  }

  private void FormAuthorize_Load(object sender, EventArgs e)
  {
    this.textBox1.Text = this.softAuthorize.GetMachineCodeString();
  }

  private void userButton1_Click(object sender, EventArgs e)
  {
    if (this.softAuthorize.CheckAuthorize(this.textBox2.Text, this.Encrypt))
    {
      this.DialogResult = DialogResult.OK;
    }
    else
    {
      int num = (int) MessageBox.Show("注册码不正确");
    }
  }

  private string AboutCode { get; set; } = "";

  private void linkLabel1_Click(object sender, EventArgs e)
  {
    int num = (int) MessageBox.Show(this.AboutCode);
  }

  /// <summary>Clean up any resources being used.</summary>
  /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  /// <summary>
  /// Required method for Designer support - do not modify
  /// the contents of this method with the code editor.
  /// </summary>
  private void InitializeComponent()
  {
    this.textBox2 = new TextBox();
    this.textBox1 = new TextBox();
    this.label2 = new Label();
    this.label1 = new Label();
    this.linkLabel1 = new LinkLabel();
    this.userButton1 = new Button();
    this.SuspendLayout();
    this.textBox2.Font = new Font("宋体", 15f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.textBox2.Location = new Point(124, 78);
    this.textBox2.Name = "textBox2";
    this.textBox2.Size = new Size(292, 30);
    this.textBox2.TabIndex = 7;
    this.textBox1.Font = new Font("宋体", 15f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.textBox1.Location = new Point(124, 35);
    this.textBox1.Name = "textBox1";
    this.textBox1.ReadOnly = true;
    this.textBox1.Size = new Size(292, 30);
    this.textBox1.TabIndex = 6;
    this.label2.AutoSize = true;
    this.label2.Font = new Font("微软雅黑", 18f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.label2.Location = new Point(12, 77);
    this.label2.Name = "label2";
    this.label2.Size = new Size(110, 31 /*0x1F*/);
    this.label2.TabIndex = 5;
    this.label2.Text = "注册码：";
    this.label1.AutoSize = true;
    this.label1.Font = new Font("微软雅黑", 18f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.label1.Location = new Point(12, 34);
    this.label1.Name = "label1";
    this.label1.Size = new Size(110, 31 /*0x1F*/);
    this.label1.TabIndex = 4;
    this.label1.Text = "机器码：";
    this.linkLabel1.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.linkLabel1.Location = new Point(295, 174);
    this.linkLabel1.Name = "linkLabel1";
    this.linkLabel1.Size = new Size(137, 23);
    this.linkLabel1.TabIndex = 9;
    this.linkLabel1.TabStop = true;
    this.linkLabel1.Text = "关于注册码";
    this.linkLabel1.TextAlign = ContentAlignment.TopRight;
    this.linkLabel1.Click += new EventHandler(this.linkLabel1_Click);
    this.userButton1.BackColor = Color.Transparent;
    this.userButton1.Font = new Font("微软雅黑", 9f);
    this.userButton1.Location = new Point(135, 130);
    this.userButton1.Margin = new Padding(3, 4, 3, 4);
    this.userButton1.Name = "userButton1";
    this.userButton1.Size = new Size(166, 33);
    this.userButton1.TabIndex = 8;
    this.userButton1.Text = "注册";
    this.userButton1.Click += new EventHandler(this.userButton1_Click);
    this.AutoScaleDimensions = new SizeF(6f, 12f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.ClientSize = new Size(435, 193);
    this.Controls.Add((Control) this.linkLabel1);
    this.Controls.Add((Control) this.userButton1);
    this.Controls.Add((Control) this.textBox2);
    this.Controls.Add((Control) this.textBox1);
    this.Controls.Add((Control) this.label2);
    this.Controls.Add((Control) this.label1);
    this.MaximizeBox = false;
    this.MaximumSize = new Size(451, 232);
    this.MinimizeBox = false;
    this.MinimumSize = new Size(451, 232);
    this.Name = nameof (FormAuthorize);
    this.ShowIcon = false;
    this.ShowInTaskbar = false;
    this.StartPosition = FormStartPosition.CenterScreen;
    this.Text = "注册软件";
    this.TopMost = true;
    this.Load += new EventHandler(this.FormAuthorize_Load);
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
