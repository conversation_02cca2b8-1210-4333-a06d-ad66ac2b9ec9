﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Inovance.InovanceSerial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.ModBus;
using HslCommunication.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Inovance;

/// <summary>
/// 汇川的串口通信协议，A适用于AM400、 AM400_800、 AC800、H3U, XP, H5U 等系列底层走的是MODBUS-RTU协议，地址说明参见标记<br />
/// <PERSON><PERSON><PERSON>'s serial communication protocol is applicable to AM400, AM400_800, AC800 and other series. The bottom layer is MODBUS-RTU protocol. For the address description, please refer to the mark
/// </summary>
/// <remarks>
/// AM400_800 的元件有 Q 区，I 区，M 区这三种，分别都可以按位，按字节，按字和按双字进行访问，在本组件的条件下，仅支持按照位，字访问。<br />
/// 位地址支持 Q, I, M 地址类型，字地址支持 SM, SD，支持对字地址的位访问，例如 ReadBool("SD0.5");
/// H3U 系列控制器支持 M/SM/S/T/C/X/Y 等 bit 型变量（也称线圈） 的访问、 D/SD/R/T/C 等 word 型变量的访问；<br />
/// H5U 系列控制器支持 M/B/S/X/Y 等 bit 型变量（也称线圈） 的访问、 D/R 等 word 型变量的访问；内部 W 元件，不支持通信访问。<br />
/// </remarks>
public class InovanceSerial : ModbusRtu
{
  /// <summary>实例化一个默认的对象</summary>
  public InovanceSerial()
  {
    this.Series = InovanceSeries.AM;
    this.DataFormat = DataFormat.CDAB;
  }

  /// <summary>
  /// 指定服务器地址，端口号，客户端自己的站号来初始化<br />
  /// Specify the server address, port number, and client's own station number to initialize
  /// </summary>
  /// <param name="station">客户端自身的站号</param>
  public InovanceSerial(byte station = 1)
    : base(station)
  {
    this.Series = InovanceSeries.AM;
    this.DataFormat = DataFormat.CDAB;
  }

  /// <summary>
  /// 指定服务器地址，端口号，客户端自己的站号来初始化<br />
  /// Specify the server address, port number, and client's own station number to initialize
  /// </summary>
  /// <param name="series">PLC的系列选择</param>
  /// <param name="station">客户端自身的站号</param>
  public InovanceSerial(InovanceSeries series, byte station = 1)
    : base(station)
  {
    this.Series = series;
    this.Series = InovanceSeries.AM;
    this.DataFormat = DataFormat.CDAB;
  }

  /// <summary>获取或设置汇川的系列，默认为AM系列</summary>
  public InovanceSeries Series { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Inovance.InovanceHelper.ReadByte(HslCommunication.ModBus.IModbus,System.String)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return InovanceHelper.ReadByte((IModbus) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Inovance.InovanceHelper.ReadByte(HslCommunication.ModBus.IModbus,System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte> operateResult = await InovanceHelper.ReadByteAsync((IModbus) this, address);
    return operateResult;
  }

  /// <inheritdoc />
  public override OperateResult<string> TranslateToModbusAddress(string address, byte modbusCode)
  {
    return InovanceHelper.PraseInovanceAddress(this.Series, address, modbusCode);
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return this.Series == InovanceSeries.AM && Regex.IsMatch(address, "MB[0-9]*[13579]$", RegexOptions.IgnoreCase) ? InovanceHelper.ReadAMString((IModbus) this, address, length, encoding) : base.ReadString(address, length, encoding);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    if (this.Series == InovanceSeries.AM && Regex.IsMatch(address, "MB[0-9]*[13579]$", RegexOptions.IgnoreCase))
    {
      OperateResult<string> operateResult = await InovanceHelper.ReadAMStringAsync((IModbus) this, address, length, encoding);
      return operateResult;
    }
    OperateResult<string> operateResult1 = await base.ReadStringAsync(address, length, encoding);
    return operateResult1;
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"InovanceSerial<{this.Series}>[{this.PortName}:{this.BaudRate}]";
  }
}
