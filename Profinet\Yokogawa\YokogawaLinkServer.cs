﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Yokogawa.YokogawaLinkServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Yokogawa;

/// <summary>
/// 横河PLC的虚拟服务器，支持X,Y,I,E,M,T,C,L继电器类型的数据读写，支持D,B,F,R,V,Z,W,TN,CN寄存器类型的数据读写，可以用来测试横河PLC的二进制通信类型<br />
/// Yokogawa PLC's virtual server, supports X, Y, I, E, M, T, C, L relay type data read and write,
/// supports D, B, F, R, V, Z, W, TN, CN register types The data read and write can be used to test the binary communication type of Yokogawa PLC
/// </summary>
/// <remarks>
/// 其中的X继电器可以在服务器进行读写操作，但是远程的PLC只能进行读取，所有的数据读写的最大的范围按照协议进行了限制。
/// </remarks>
/// <example>
/// 你可以很快速并且简单的创建一个虚拟的横河服务器
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\YokogawaLinkServerSample.cs" region="UseExample1" title="简单的创建服务器" />
/// 当然如果需要高级的服务器，指定日志，限制客户端的IP地址，获取客户端发送的信息，在服务器初始化的时候就要参照下面的代码：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\YokogawaLinkServerSample.cs" region="UseExample4" title="定制服务器" />
/// 服务器创建好之后，我们就可以对服务器进行一些读写的操作了，下面的代码是基础的BCL类型的读写操作。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\YokogawaLinkServerSample.cs" region="ReadWriteExample" title="基础的读写示例" />
/// 高级的对于byte数组类型的数据进行批量化的读写操作如下：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\YokogawaLinkServerSample.cs" region="BytesReadWrite" title="字节的读写示例" />
/// 更高级操作请参见源代码。
/// </example>
public class YokogawaLinkServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer iBuffer;
  private SoftBuffer eBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer lBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer bBuffer;
  private SoftBuffer fBuffer;
  private SoftBuffer rBuffer;
  private SoftBuffer vBuffer;
  private SoftBuffer zBuffer;
  private SoftBuffer wBuffer;
  private SoftBuffer specialBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private IByteTransform transform;
  private bool isProgramStarted = false;

  /// <summary>
  /// 实例化一个横河PLC的服务器，支持X,Y,I,E,M,T,C,L继电器类型的数据读写，支持D,B,F,R,V,Z,W,TN,CN寄存器类型的数据读写<br />
  /// Instantiate a Yokogawa PLC server, support X, Y, I, E, M, T, C, L relay type data read and write,
  /// support D, B, F, R, V, Z, W, TN, CN Register type data reading and writing
  /// </summary>
  public YokogawaLinkServer()
  {
    this.xBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.yBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.iBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.eBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.lBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.bBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.fBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.vBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.zBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.wBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.specialBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.ByteTransform.DataFormat = DataFormat.CDAB;
    this.transform = (IByteTransform) new ReverseBytesTransform();
  }

  private OperateResult<SoftBuffer> GetDataAreaFromYokogawaAddress(
    YokogawaLinkAddress yokogawaAddress,
    bool isBit)
  {
    if (isBit)
    {
      switch (yokogawaAddress.DataCode)
      {
        case 5:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.eBuffer);
        case 9:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.iBuffer);
        case 12:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.lBuffer);
        case 13:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.mBuffer);
        case 24:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.xBuffer);
        case 25:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.yBuffer);
        default:
          return new OperateResult<SoftBuffer>(StringResources.Language.NotSupportedDataType);
      }
    }
    else
    {
      switch (yokogawaAddress.DataCode)
      {
        case 2:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.bBuffer);
        case 4:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.dBuffer);
        case 5:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.eBuffer);
        case 6:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.fBuffer);
        case 9:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.iBuffer);
        case 12:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.lBuffer);
        case 13:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.mBuffer);
        case 18:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.rBuffer);
        case 22:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.vBuffer);
        case 23:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.wBuffer);
        case 24:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.xBuffer);
        case 25:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.yBuffer);
        case 26:
          return OperateResult.CreateSuccessResult<SoftBuffer>(this.zBuffer);
        default:
          return new OperateResult<SoftBuffer>(StringResources.Language.NotSupportedDataType);
      }
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Yokogawa.YokogawaLinkTcp.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    if (address.StartsWith("Special:") || address.StartsWith("special:"))
    {
      address = address.Substring(8);
      HslHelper.ExtractParameter(ref address, "unit");
      HslHelper.ExtractParameter(ref address, "slot");
      try
      {
        return OperateResult.CreateSuccessResult<byte[]>(this.specialBuffer.GetBytes((int) ushort.Parse(address) * 2, (int) length * 2));
      }
      catch (Exception ex)
      {
        return new OperateResult<byte[]>("Address format wrong: " + ex.Message);
      }
    }
    else
    {
      OperateResult<YokogawaLinkAddress> from = YokogawaLinkAddress.ParseFrom(address, length);
      if (!from.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
      OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, false);
      if (!fromYokogawaAddress.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) fromYokogawaAddress);
      return from.Content.DataCode == 24 || from.Content.DataCode == 25 || from.Content.DataCode == 9 || from.Content.DataCode == 5 || from.Content.DataCode == 13 || from.Content.DataCode == 12 ? OperateResult.CreateSuccessResult<byte[]>(((IEnumerable<byte>) fromYokogawaAddress.Content.GetBytes(from.Content.AddressStart, (int) length * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray()) : OperateResult.CreateSuccessResult<byte[]>(fromYokogawaAddress.Content.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Yokogawa.YokogawaLinkTcp.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    if (address.StartsWith("Special:") || address.StartsWith("special:"))
    {
      address = address.Substring(8);
      HslHelper.ExtractParameter(ref address, "unit");
      HslHelper.ExtractParameter(ref address, "slot");
      try
      {
        this.specialBuffer.SetBytes(value, (int) ushort.Parse(address) * 2);
        return OperateResult.CreateSuccessResult();
      }
      catch (Exception ex)
      {
        return new OperateResult("Address format wrong: " + ex.Message);
      }
    }
    else
    {
      OperateResult<YokogawaLinkAddress> from = YokogawaLinkAddress.ParseFrom(address, (ushort) 0);
      if (!from.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
      OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, false);
      if (!fromYokogawaAddress.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) fromYokogawaAddress);
      if (from.Content.DataCode == 24 || from.Content.DataCode == 25 || from.Content.DataCode == 9 || from.Content.DataCode == 5 || from.Content.DataCode == 13 || from.Content.DataCode == 12)
        fromYokogawaAddress.Content.SetBytes(((IEnumerable<bool>) value.ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), from.Content.AddressStart);
      else
        fromYokogawaAddress.Content.SetBytes(value, from.Content.AddressStart * 2);
      return OperateResult.CreateSuccessResult();
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Yokogawa.YokogawaLinkTcp.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<YokogawaLinkAddress> from = YokogawaLinkAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, true);
    return !fromYokogawaAddress.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) fromYokogawaAddress) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) fromYokogawaAddress.Content.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Yokogawa.YokogawaLinkTcp.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<YokogawaLinkAddress> from = YokogawaLinkAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return (OperateResult) from;
    OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, true);
    if (!fromYokogawaAddress.IsSuccess)
      return (OperateResult) fromYokogawaAddress;
    fromYokogawaAddress.Content.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), from.Content.AddressStart);
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 如果未执行程序，则开始执行程序<br />
  /// Starts executing a program if it is not being executed
  /// </summary>
  [HslMqttApi(Description = "Starts executing a program if it is not being executed")]
  public void StartProgram() => this.isProgramStarted = true;

  /// <summary>
  /// 停止当前正在执行程序<br />
  /// Stops the executing program.
  /// </summary>
  [HslMqttApi(Description = "Stops the executing program.")]
  public void StopProgram() => this.isProgramStarted = false;

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new YokogawaLinkBinaryMessage();
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    byte[] numArray;
    if (receive[0] == (byte) 1)
      numArray = this.ReadBoolByCommand(receive);
    else if (receive[0] == (byte) 2)
      numArray = this.WriteBoolByCommand(receive);
    else if (receive[0] == (byte) 4)
      numArray = this.ReadRandomBoolByCommand(receive);
    else if (receive[0] == (byte) 5)
      numArray = this.WriteRandomBoolByCommand(receive);
    else if (receive[0] == (byte) 17)
      numArray = this.ReadWordByCommand(receive);
    else if (receive[0] == (byte) 18)
      numArray = this.WriteWordByCommand(receive);
    else if (receive[0] == (byte) 20)
      numArray = this.ReadRandomWordByCommand(receive);
    else if (receive[0] == (byte) 21)
      numArray = this.WriteRandomWordByCommand(receive);
    else if (receive[0] == (byte) 49)
      numArray = this.ReadSpecialModule(receive);
    else if (receive[0] == (byte) 50)
      numArray = this.WriteSpecialModule(receive);
    else if (receive[0] == (byte) 69)
      numArray = this.StartByCommand(receive);
    else if (receive[0] == (byte) 70)
    {
      numArray = this.StopByCommand(receive);
    }
    else
    {
      if (receive[0] == (byte) 97)
        throw new RemoteCloseException();
      numArray = receive[0] != (byte) 98 ? (receive[0] != (byte) 99 ? this.PackCommandBack(receive[0], (byte) 3, (byte[]) null) : this.ReadSystemDateTime(receive)) : this.ReadSystemByCommand(receive);
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  private byte[] ReadBoolByCommand(byte[] command)
  {
    int index = this.transform.TransInt32(command, 6);
    int length = (int) this.transform.TransUInt16(command, 10);
    if (index > (int) ushort.MaxValue || index < 0)
      return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
    if (length > 256 /*0x0100*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (index + length > (int) ushort.MaxValue)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    switch (command[5])
    {
      case 5:
        return this.PackCommandBack(command[0], (byte) 0, this.eBuffer.GetBytes(index, length));
      case 9:
        return this.PackCommandBack(command[0], (byte) 0, this.iBuffer.GetBytes(index, length));
      case 12:
        return this.PackCommandBack(command[0], (byte) 0, this.lBuffer.GetBytes(index, length));
      case 13:
        return this.PackCommandBack(command[0], (byte) 0, this.mBuffer.GetBytes(index, length));
      case 24:
        return this.PackCommandBack(command[0], (byte) 0, this.xBuffer.GetBytes(index, length));
      case 25:
        return this.PackCommandBack(command[0], (byte) 0, this.yBuffer.GetBytes(index, length));
      default:
        return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    }
  }

  private byte[] WriteBoolByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    int destIndex = this.transform.TransInt32(command, 6);
    int num = (int) this.transform.TransUInt16(command, 10);
    if (destIndex > (int) ushort.MaxValue || destIndex < 0)
      return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
    if (num > 256 /*0x0100*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (destIndex + num > (int) ushort.MaxValue)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (num != command.Length - 12)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    switch (command[5])
    {
      case 5:
        this.eBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 9:
        this.iBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 12:
        this.lBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 13:
        this.mBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 24:
        return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
      case 25:
        this.yBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      default:
        return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    }
  }

  private byte[] ReadRandomBoolByCommand(byte[] command)
  {
    int length = (int) this.transform.TransUInt16(command, 4);
    if (length > 32 /*0x20*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (length * 6 != command.Length - 6)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    byte[] result = new byte[length];
    for (int index1 = 0; index1 < length; ++index1)
    {
      int index2 = this.transform.TransInt32(command, 8 + 6 * index1);
      if (index2 > (int) ushort.MaxValue || index2 < 0)
        return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
      switch (command[7 + index1 * 6])
      {
        case 5:
          result[index1] = this.eBuffer.GetBytes(index2, 1)[0];
          break;
        case 9:
          result[index1] = this.iBuffer.GetBytes(index2, 1)[0];
          break;
        case 12:
          result[index1] = this.lBuffer.GetBytes(index2, 1)[0];
          break;
        case 13:
          result[index1] = this.mBuffer.GetBytes(index2, 1)[0];
          break;
        case 24:
          result[index1] = this.xBuffer.GetBytes(index2, 1)[0];
          break;
        case 25:
          result[index1] = this.yBuffer.GetBytes(index2, 1)[0];
          break;
        default:
          return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
      }
    }
    return this.PackCommandBack(command[0], (byte) 0, result);
  }

  private byte[] WriteRandomBoolByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    int num = (int) this.transform.TransUInt16(command, 4);
    if (num > 32 /*0x20*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (num * 8 - 1 != command.Length - 6)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    for (int index1 = 0; index1 < num; ++index1)
    {
      int index2 = this.transform.TransInt32(command, 8 + 8 * index1);
      if (index2 > (int) ushort.MaxValue || index2 < 0)
        return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
      switch (command[7 + index1 * 8])
      {
        case 5:
          this.eBuffer.SetValue(command[12 + 8 * index1], index2);
          break;
        case 9:
          this.iBuffer.SetValue(command[12 + 8 * index1], index2);
          break;
        case 12:
          this.lBuffer.SetValue(command[12 + 8 * index1], index2);
          break;
        case 13:
          this.mBuffer.SetValue(command[12 + 8 * index1], index2);
          break;
        case 24:
          return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
        case 25:
          this.yBuffer.SetValue(command[12 + 8 * index1], index2);
          break;
        default:
          return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
      }
    }
    return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
  }

  private byte[] ReadWordByCommand(byte[] command)
  {
    int index = this.transform.TransInt32(command, 6);
    int num = (int) this.transform.TransUInt16(command, 10);
    if (index > (int) ushort.MaxValue || index < 0)
      return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
    if (num > 64 /*0x40*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (index + num > (int) ushort.MaxValue)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    switch (command[5])
    {
      case 2:
        return this.PackCommandBack(command[0], (byte) 0, this.bBuffer.GetBytes(index * 2, num * 2));
      case 4:
        return this.PackCommandBack(command[0], (byte) 0, this.dBuffer.GetBytes(index * 2, num * 2));
      case 5:
        return this.PackCommandBack(command[0], (byte) 0, ((IEnumerable<byte>) this.eBuffer.GetBytes(index, num * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      case 6:
        return this.PackCommandBack(command[0], (byte) 0, this.fBuffer.GetBytes(index * 2, num * 2));
      case 9:
        return this.PackCommandBack(command[0], (byte) 0, ((IEnumerable<byte>) this.iBuffer.GetBytes(index, num * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      case 12:
        return this.PackCommandBack(command[0], (byte) 0, ((IEnumerable<byte>) this.lBuffer.GetBytes(index, num * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      case 13:
        return this.PackCommandBack(command[0], (byte) 0, ((IEnumerable<byte>) this.mBuffer.GetBytes(index, num * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      case 18:
        return this.PackCommandBack(command[0], (byte) 0, this.rBuffer.GetBytes(index * 2, num * 2));
      case 22:
        return this.PackCommandBack(command[0], (byte) 0, this.vBuffer.GetBytes(index * 2, num * 2));
      case 23:
        return this.PackCommandBack(command[0], (byte) 0, this.wBuffer.GetBytes(index * 2, num * 2));
      case 24:
        return this.PackCommandBack(command[0], (byte) 0, ((IEnumerable<byte>) this.xBuffer.GetBytes(index, num * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      case 25:
        return this.PackCommandBack(command[0], (byte) 0, ((IEnumerable<byte>) this.yBuffer.GetBytes(index, num * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      case 26:
        return this.PackCommandBack(command[0], (byte) 0, this.zBuffer.GetBytes(index * 2, num * 2));
      default:
        return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    }
  }

  private byte[] WriteWordByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    int destIndex = this.transform.TransInt32(command, 6);
    int num = (int) this.transform.TransUInt16(command, 10);
    if (destIndex > (int) ushort.MaxValue || destIndex < 0)
      return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
    if (num > 64 /*0x40*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (destIndex + num > (int) ushort.MaxValue)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (num * 2 != command.Length - 12)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    switch (command[5])
    {
      case 2:
        this.bBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex * 2);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 4:
        this.dBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex * 2);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 5:
        this.eBuffer.SetBytes(((IEnumerable<bool>) command.RemoveBegin<byte>(12).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 6:
        this.fBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex * 2);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 9:
        this.iBuffer.SetBytes(((IEnumerable<bool>) command.RemoveBegin<byte>(12).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 12:
        this.lBuffer.SetBytes(((IEnumerable<bool>) command.RemoveBegin<byte>(12).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 13:
        this.mBuffer.SetBytes(((IEnumerable<bool>) command.RemoveBegin<byte>(12).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 18:
        this.rBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex * 2);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 22:
        this.vBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex * 2);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 23:
        this.wBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex * 2);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 24:
        return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
      case 25:
        this.yBuffer.SetBytes(((IEnumerable<bool>) command.RemoveBegin<byte>(12).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      case 26:
        this.zBuffer.SetBytes(command.RemoveBegin<byte>(12), destIndex * 2);
        return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
      default:
        return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    }
  }

  private byte[] ReadRandomWordByCommand(byte[] command)
  {
    int num = (int) this.transform.TransUInt16(command, 4);
    if (num > 32 /*0x20*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (num * 6 != command.Length - 6)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    byte[] result = new byte[num * 2];
    for (int index1 = 0; index1 < num; ++index1)
    {
      int index2 = this.transform.TransInt32(command, 8 + 6 * index1);
      if (index2 > (int) ushort.MaxValue || index2 < 0)
        return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
      switch (command[7 + index1 * 6])
      {
        case 2:
          this.bBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) result, index1 * 2);
          break;
        case 4:
          this.dBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) result, index1 * 2);
          break;
        case 5:
          ((IEnumerable<byte>) this.eBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) result, index1 * 2);
          break;
        case 6:
          this.fBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) result, index1 * 2);
          break;
        case 9:
          ((IEnumerable<byte>) this.iBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) result, index1 * 2);
          break;
        case 12:
          ((IEnumerable<byte>) this.lBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) result, index1 * 2);
          break;
        case 13:
          ((IEnumerable<byte>) this.mBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) result, index1 * 2);
          break;
        case 18:
          this.rBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) result, index1 * 2);
          break;
        case 22:
          this.vBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) result, index1 * 2);
          break;
        case 23:
          this.wBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) result, index1 * 2);
          break;
        case 24:
          ((IEnumerable<byte>) this.xBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) result, index1 * 2);
          break;
        case 25:
          ((IEnumerable<byte>) this.yBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) result, index1 * 2);
          break;
        case 26:
          this.zBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) result, index1 * 2);
          break;
        default:
          return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
      }
    }
    return this.PackCommandBack(command[0], (byte) 0, result);
  }

  private byte[] WriteRandomWordByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    int num = (int) this.transform.TransUInt16(command, 4);
    if (num > 32 /*0x20*/)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    if (num * 8 != command.Length - 6)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    for (int index = 0; index < num; ++index)
    {
      int destIndex = this.transform.TransInt32(command, 8 + 8 * index);
      if (destIndex > (int) ushort.MaxValue || destIndex < 0)
        return this.PackCommandBack(command[0], (byte) 4, (byte[]) null);
      switch (command[7 + index * 8])
      {
        case 2:
          this.bBuffer.SetBytes(command.SelectMiddle<byte>(12 + 8 * index, 2), destIndex * 2);
          break;
        case 4:
          this.dBuffer.SetBytes(command.SelectMiddle<byte>(12 + 8 * index, 2), destIndex * 2);
          break;
        case 5:
          this.eBuffer.SetBytes(((IEnumerable<bool>) command.SelectMiddle<byte>(12 + 8 * index, 2).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
          break;
        case 6:
          this.fBuffer.SetBytes(command.SelectMiddle<byte>(12 + 8 * index, 2), destIndex * 2);
          break;
        case 9:
          this.iBuffer.SetBytes(((IEnumerable<bool>) command.SelectMiddle<byte>(12 + 8 * index, 2).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
          break;
        case 12:
          this.lBuffer.SetBytes(((IEnumerable<bool>) command.SelectMiddle<byte>(12 + 8 * index, 2).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
          break;
        case 13:
          this.mBuffer.SetBytes(((IEnumerable<bool>) command.SelectMiddle<byte>(12 + 8 * index, 2).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
          break;
        case 18:
          this.rBuffer.SetBytes(command.SelectMiddle<byte>(12 + 8 * index, 2), destIndex * 2);
          break;
        case 22:
          this.vBuffer.SetBytes(command.SelectMiddle<byte>(12 + 8 * index, 2), destIndex * 2);
          break;
        case 23:
          this.wBuffer.SetBytes(command.SelectMiddle<byte>(12 + 8 * index, 2), destIndex * 2);
          break;
        case 24:
          return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
        case 25:
          this.yBuffer.SetBytes(((IEnumerable<bool>) command.SelectMiddle<byte>(12 + 8 * index, 2).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
          break;
        case 26:
          this.zBuffer.SetBytes(command.SelectMiddle<byte>(12 + 8 * index, 2), destIndex * 2);
          break;
        default:
          return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
      }
    }
    return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
  }

  private byte[] StartByCommand(byte[] command)
  {
    this.isProgramStarted = true;
    return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
  }

  private byte[] StopByCommand(byte[] command)
  {
    this.isProgramStarted = false;
    return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
  }

  private byte[] ReadSystemByCommand(byte[] command)
  {
    if (command[5] == (byte) 1)
    {
      byte[] result = new byte[2]
      {
        (byte) 0,
        this.isProgramStarted ? (byte) 1 : (byte) 2
      };
      return this.PackCommandBack(command[0], (byte) 0, result);
    }
    if (command[5] != (byte) 2)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    byte[] result1 = new byte[28];
    Encoding.ASCII.GetBytes("F3SP38-6N").CopyTo((Array) result1, 0);
    Encoding.ASCII.GetBytes("12345").CopyTo((Array) result1, 16 /*0x10*/);
    result1[25] = (byte) 17;
    result1[26] = (byte) 2;
    result1[27] = (byte) 3;
    return this.PackCommandBack(command[0], (byte) 0, result1);
  }

  private byte[] ReadSystemDateTime(byte[] command)
  {
    byte[] result = new byte[16 /*0x10*/];
    DateTime now = DateTime.Now;
    result[0] = BitConverter.GetBytes(now.Year - 2000)[1];
    result[1] = BitConverter.GetBytes(now.Year - 2000)[0];
    result[2] = BitConverter.GetBytes(now.Month)[1];
    result[3] = BitConverter.GetBytes(now.Month)[0];
    result[4] = BitConverter.GetBytes(now.Day)[1];
    result[5] = BitConverter.GetBytes(now.Day)[0];
    result[6] = BitConverter.GetBytes(now.Hour)[1];
    result[7] = BitConverter.GetBytes(now.Hour)[0];
    result[8] = BitConverter.GetBytes(now.Minute)[1];
    result[9] = BitConverter.GetBytes(now.Minute)[0];
    result[10] = BitConverter.GetBytes(now.Second)[1];
    result[11] = BitConverter.GetBytes(now.Second)[0];
    uint totalSeconds = (uint) (now - new DateTime(now.Year, 1, 1)).TotalSeconds;
    result[12] = BitConverter.GetBytes(totalSeconds)[3];
    result[13] = BitConverter.GetBytes(totalSeconds)[2];
    result[14] = BitConverter.GetBytes(totalSeconds)[1];
    result[15] = BitConverter.GetBytes(totalSeconds)[0];
    return this.PackCommandBack(command[0], (byte) 0, result);
  }

  private byte[] ReadSpecialModule(byte[] command)
  {
    if (command[4] != (byte) 0 || command[5] != (byte) 1)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    ushort num1 = this.transform.TransUInt16(command, 6);
    ushort num2 = this.transform.TransUInt16(command, 8);
    return this.PackCommandBack(command[0], (byte) 0, this.specialBuffer.GetBytes((int) num1 * 2, (int) num2 * 2));
  }

  private byte[] WriteSpecialModule(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    if (command[4] != (byte) 0 || command[5] != (byte) 1)
      return this.PackCommandBack(command[0], (byte) 3, (byte[]) null);
    ushort num = this.transform.TransUInt16(command, 6);
    if ((int) this.transform.TransUInt16(command, 8) * 2 != command.Length - 10)
      return this.PackCommandBack(command[0], (byte) 5, (byte[]) null);
    this.specialBuffer.SetBytes(command.RemoveBegin<byte>(10), (int) num * 2);
    return this.PackCommandBack(command[0], (byte) 0, (byte[]) null);
  }

  private byte[] PackCommandBack(byte cmd, byte err, byte[] result)
  {
    if (result == null)
      result = new byte[0];
    byte[] numArray = new byte[4 + result.Length];
    numArray[0] = (byte) ((uint) cmd + 128U /*0x80*/);
    numArray[1] = err;
    numArray[2] = BitConverter.GetBytes(result.Length)[1];
    numArray[3] = BitConverter.GetBytes(result.Length)[0];
    result.CopyTo((Array) numArray, 4);
    return numArray;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 1310720 /*0x140000*/)
      throw new Exception("File is not correct");
    this.xBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.yBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.iBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.eBuffer.SetBytes(content, 196608 /*0x030000*/, 0, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 262144 /*0x040000*/, 0, 65536 /*0x010000*/);
    this.lBuffer.SetBytes(content, 327680 /*0x050000*/, 0, 65536 /*0x010000*/);
    this.dBuffer.SetBytes(content, 393216 /*0x060000*/, 0, 65536 /*0x010000*/);
    this.bBuffer.SetBytes(content, 524288 /*0x080000*/, 0, 65536 /*0x010000*/);
    this.fBuffer.SetBytes(content, 655360 /*0x0A0000*/, 0, 65536 /*0x010000*/);
    this.rBuffer.SetBytes(content, 786432 /*0x0C0000*/, 0, 65536 /*0x010000*/);
    this.vBuffer.SetBytes(content, 917504 /*0x0E0000*/, 0, 65536 /*0x010000*/);
    this.zBuffer.SetBytes(content, 1048576 /*0x100000*/, 0, 65536 /*0x010000*/);
    this.wBuffer.SetBytes(content, 1179648 /*0x120000*/, 0, 65536 /*0x010000*/);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[1310720 /*0x140000*/];
    Array.Copy((Array) this.xBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.yBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.iBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.eBuffer.GetBytes(), 0, (Array) destinationArray, 196608 /*0x030000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.mBuffer.GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.lBuffer.GetBytes(), 0, (Array) destinationArray, 327680 /*0x050000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dBuffer.GetBytes(), 0, (Array) destinationArray, 393216 /*0x060000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.bBuffer.GetBytes(), 0, (Array) destinationArray, 524288 /*0x080000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.fBuffer.GetBytes(), 0, (Array) destinationArray, 655360 /*0x0A0000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.rBuffer.GetBytes(), 0, (Array) destinationArray, 786432 /*0x0C0000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.vBuffer.GetBytes(), 0, (Array) destinationArray, 917504 /*0x0E0000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.zBuffer.GetBytes(), 0, (Array) destinationArray, 1048576 /*0x100000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.wBuffer.GetBytes(), 0, (Array) destinationArray, 1179648 /*0x120000*/, 65536 /*0x010000*/);
    return destinationArray;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.xBuffer?.Dispose();
      this.yBuffer?.Dispose();
      this.iBuffer?.Dispose();
      this.eBuffer?.Dispose();
      this.mBuffer?.Dispose();
      this.lBuffer?.Dispose();
      this.dBuffer?.Dispose();
      this.bBuffer?.Dispose();
      this.fBuffer?.Dispose();
      this.rBuffer?.Dispose();
      this.vBuffer?.Dispose();
      this.zBuffer?.Dispose();
      this.wBuffer?.Dispose();
      this.specialBuffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"YokogawaLinkServer[{this.Port}]";
}
