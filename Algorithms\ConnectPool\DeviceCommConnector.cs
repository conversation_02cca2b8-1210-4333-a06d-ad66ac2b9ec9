﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Algorithms.ConnectPool.DeviceCommConnector
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Device;
using System;

#nullable disable
namespace HslCommunication.Algorithms.ConnectPool;

/// <summary>
/// 基于设备通信的连接池信息<br />
/// Connection pool information based on device communication
/// </summary>
public class DeviceCommConnector : IConnector
{
  private DeviceCommunication device = (DeviceCommunication) null;

  /// <summary>
  /// 使用指定的设备来实例化一个对象<br />
  /// Use the specified device to instantiate an object
  /// </summary>
  /// <param name="device">指定的设备通信</param>
  public DeviceCommConnector(DeviceCommunication device) => this.device = device;

  /// <summary>
  /// 获取当前实际的设备通信对象<br />
  /// Gets the actual device communication object at present
  /// </summary>
  public DeviceCommunication Device => this.device;

  /// <inheritdoc cref="P:HslCommunication.Algorithms.ConnectPool.IConnector.IsConnectUsing" />
  public bool IsConnectUsing { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Algorithms.ConnectPool.IConnector.GuidToken" />
  public string GuidToken { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Algorithms.ConnectPool.IConnector.LastUseTime" />
  public DateTime LastUseTime { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Algorithms.ConnectPool.IConnector.Close" />
  public void Close() => this.device.CommunicationPipe.CloseCommunication();

  /// <inheritdoc cref="M:HslCommunication.Algorithms.ConnectPool.IConnector.Open" />
  public void Open()
  {
  }
}
