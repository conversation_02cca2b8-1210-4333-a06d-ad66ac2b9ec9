﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.YASKAWA.YRCRobotData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;

#nullable disable
namespace HslCommunication.Robot.YASKAWA;

/// <summary>安川机器人的数据信息，其中 Re只在YRC100中有效，没有外部轴的系统， 7-12外部轴的值设定为「0」</summary>
public class YRCRobotData
{
  /// <summary>实例化一个默认的对象</summary>
  public YRCRobotData()
  {
    this.SpeedPercent = "100.0%";
    this.Status = new bool[6];
  }

  /// <summary>指定类型及字符串数据信息来实例化一个对象</summary>
  /// <param name="type">类型信息</param>
  /// <param name="value">字符串数据</param>
  public YRCRobotData(YRCType type, string value)
    : this()
  {
    this.Parse(type, value);
  }

  /// <summary>动作速度（ 0.01 ～ 100.0％）</summary>
  /// <remarks>在读取时没有任何的含义，仅在写入数据是有效，默认为 100.0%</remarks>
  public string SpeedPercent { get; set; }

  /// <summary>参考系，0:基座坐标，1:机器人坐标，2-65分别表示用户坐标1-64</summary>
  public int Frame { get; set; }

  /// <summary>X 坐标值（ 单位mm、小数点第 3 位有效）</summary>
  public float X { get; set; }

  /// <summary>Y 坐标值（单位mm、小数点第 3 位有效）</summary>
  public float Y { get; set; }

  /// <summary>Z 坐标值（单位 mm、小数点第 3 位有效）</summary>
  public float Z { get; set; }

  /// <summary>手腕姿勢 Rx 坐标值（单位 °、小数点第 4 位有效）</summary>
  public float Rx { get; set; }

  /// <summary>手腕姿勢 Ry 坐标值（单位 °、 小数点第 4 位有效）</summary>
  public float Ry { get; set; }

  /// <summary>手腕姿勢 Rz 坐标值（单位 °、 小数点第 4 位有效）</summary>
  public float Rz { get; set; }

  /// <summary>肘角姿势 Re，仅在七轴机器人的情况下有效</summary>
  public float Re { get; set; }

  /// <summary>
  /// 形态数据，各个索引含义为 [0] 0:F lip,1:N o Flip [1] 0:上方肘，1:下方肘 [2] 0:正面,1:背面 [3] 0:R＜180, 1:R≥180 [4] 0:T＜180, 1:T≥180 [5] 0:S＜180, 1:S≥180
  /// </summary>
  public bool[] Status { get; set; }

  /// <summary>工具编号（ 0 ～ 63）</summary>
  public int ToolNumber { get; set; }

  /// <summary>第 7 轴脉冲数（ 行走轴时、 单位mm）</summary>
  public int Axis7PulseNumber { get; set; }

  /// <summary>第 8 轴脉冲数（ 行走轴时、 单位mm）</summary>
  public int Axis8PulseNumber { get; set; }

  /// <summary>第 9 轴脉冲数（行走轴时、 单位mm）</summary>
  public int Axis9PulseNumber { get; set; }

  /// <summary>第 10 轴脉冲数</summary>
  public int Axis10PulseNumber { get; set; }

  /// <summary>第 11 轴脉冲数</summary>
  public int Axis11PulseNumber { get; set; }

  /// <summary>第 12 轴脉冲数</summary>
  public int Axis12PulseNumber { get; set; }

  /// <summary>将数据转换为写入命令的字符换，需要指定是否七轴机器人的信息</summary>
  /// <param name="type">机器人的型号信息</param>
  /// <returns>写入的数据信息</returns>
  public string ToWriteString(YRCType type)
  {
    return type == YRCType.YRC100 ? $"{this.SpeedPercent},{this.Frame},{this.X},{this.Y},{this.Z},{this.Rx},{this.Ry},{this.Rz},{this.Re},{SoftBasic.BoolArrayToByte(this.Status)[0]}," + $"{this.ToolNumber},{this.Axis7PulseNumber},{this.Axis8PulseNumber},{this.Axis9PulseNumber},{this.Axis10PulseNumber},{this.Axis11PulseNumber},{this.Axis12PulseNumber}" : $"{this.SpeedPercent},{this.Frame},{this.X},{this.Y},{this.Z},{this.Rx},{this.Ry},{this.Rz},{SoftBasic.BoolArrayToByte(this.Status)[0]}," + $"{this.ToolNumber},{this.Axis7PulseNumber},{this.Axis8PulseNumber},{this.Axis9PulseNumber},{this.Axis10PulseNumber},{this.Axis11PulseNumber},{this.Axis12PulseNumber}";
  }

  /// <summary>从实际机器人读取到的数据解析出真实的机器人信息。</summary>
  /// <param name="type">机器人类型</param>
  /// <param name="value">值</param>
  public void Parse(YRCType type, string value)
  {
    string[] strArray1 = value.Split(new char[1]{ ',' }, StringSplitOptions.RemoveEmptyEntries);
    int num1 = 0;
    string[] strArray2 = strArray1;
    int index1 = num1;
    int num2 = index1 + 1;
    this.X = float.Parse(strArray2[index1]);
    string[] strArray3 = strArray1;
    int index2 = num2;
    int num3 = index2 + 1;
    this.Y = float.Parse(strArray3[index2]);
    string[] strArray4 = strArray1;
    int index3 = num3;
    int num4 = index3 + 1;
    this.Z = float.Parse(strArray4[index3]);
    string[] strArray5 = strArray1;
    int index4 = num4;
    int num5 = index4 + 1;
    this.Rx = float.Parse(strArray5[index4]);
    string[] strArray6 = strArray1;
    int index5 = num5;
    int num6 = index5 + 1;
    this.Ry = float.Parse(strArray6[index5]);
    string[] strArray7 = strArray1;
    int index6 = num6;
    int num7 = index6 + 1;
    this.Rz = float.Parse(strArray7[index6]);
    if (type == YRCType.YRC100)
      this.Re = float.Parse(strArray1[num7++]);
    byte[] InBytes = new byte[1];
    string[] strArray8 = strArray1;
    int index7 = num7;
    int num8 = index7 + 1;
    InBytes[0] = byte.Parse(strArray8[index7]);
    this.Status = InBytes.ToBoolArray().SelectBegin<bool>(6);
    string[] strArray9 = strArray1;
    int index8 = num8;
    int num9 = index8 + 1;
    this.ToolNumber = int.Parse(strArray9[index8]);
    if (strArray1.Length <= num9)
      return;
    string[] strArray10 = strArray1;
    int index9 = num9;
    int num10 = index9 + 1;
    this.Axis7PulseNumber = int.Parse(strArray10[index9]);
    string[] strArray11 = strArray1;
    int index10 = num10;
    int num11 = index10 + 1;
    this.Axis8PulseNumber = int.Parse(strArray11[index10]);
    string[] strArray12 = strArray1;
    int index11 = num11;
    int num12 = index11 + 1;
    this.Axis9PulseNumber = int.Parse(strArray12[index11]);
    string[] strArray13 = strArray1;
    int index12 = num12;
    int num13 = index12 + 1;
    this.Axis10PulseNumber = int.Parse(strArray13[index12]);
    string[] strArray14 = strArray1;
    int index13 = num13;
    int num14 = index13 + 1;
    this.Axis11PulseNumber = int.Parse(strArray14[index13]);
    string[] strArray15 = strArray1;
    int index14 = num14;
    int num15 = index14 + 1;
    this.Axis12PulseNumber = int.Parse(strArray15[index14]);
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"[{this.X},{this.Y},{this.Z},{this.Rx},{this.Ry},{this.Rz}]";
  }
}
