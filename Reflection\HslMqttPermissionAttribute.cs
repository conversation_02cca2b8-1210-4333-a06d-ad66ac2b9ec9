﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslMqttPermissionAttribute
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>
/// 可以指定方法的权限内容，可以限定MQTT会话的ClientID信息或是UserName内容<br />
/// 
/// </summary>
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public class HslMqttPermissionAttribute : Attribute
{
  /// <summary>ClientId的限定内容</summary>
  public string ClientID { get; set; }

  /// <summary>UserName的限定内容</summary>
  public string UserName { get; set; }

  /// <summary>检查当前的客户端ID是否通过</summary>
  /// <param name="clientID">ID信息</param>
  /// <returns>是否检测成功</returns>
  public virtual bool CheckClientID(string clientID)
  {
    return string.IsNullOrEmpty(this.ClientID) || this.ClientID == clientID;
  }

  /// <summary>检查当前的用户名是否通过</summary>
  /// <param name="name">用户名</param>
  /// <returns>是否检测成功</returns>
  public virtual bool CheckUserName(string name)
  {
    return string.IsNullOrEmpty(this.UserName) || this.UserName == name;
  }
}
