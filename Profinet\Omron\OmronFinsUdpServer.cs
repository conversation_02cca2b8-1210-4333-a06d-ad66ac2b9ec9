﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronFinsUdpServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <inheritdoc cref="T:HslCommunication.Profinet.Omron.OmronFinsServer" />
public class OmronFinsUdpServer : OmronFinsServer
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public OmronFinsUdpServer() => this.connectionInitialization = false;

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) null;

  /// <inheritdoc />
  protected override byte[] PackCommand(int status, byte[] finsCore, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    byte[] numArray = new byte[14 + data.Length];
    SoftBasic.HexStringToBytes("C0 00 02 00 00 00 00 00 00 00 00 00 00 00").CopyTo((Array) numArray, 0);
    if (data.Length != 0)
      data.CopyTo((Array) numArray, 14);
    numArray[10] = finsCore[0];
    numArray[11] = finsCore[1];
    numArray[12] = BitConverter.GetBytes(status)[1];
    numArray[13] = BitConverter.GetBytes(status)[0];
    return numArray;
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    byte[] numArray = this.ReadFromFinsCore(receive.RemoveBegin<byte>(10));
    if (numArray != null)
    {
      numArray[4] = receive[7];
      numArray[7] = receive[4];
      numArray[9] = receive[9];
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronFinsUdpServer[{this.Port}]";
}
