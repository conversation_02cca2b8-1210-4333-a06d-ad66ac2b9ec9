﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.FujiSPHAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>富士SPH地址类对象</summary>
public class FujiSPHAddress : DeviceAddressDataBase
{
  /// <summary>数据的类型代码</summary>
  public byte TypeCode { get; set; }

  /// <summary>当前地址的位索引信息</summary>
  public int BitIndex { get; set; }

  /// <summary>
  /// 从实际的Fuji的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual Fuji address
  /// </summary>
  /// <param name="address">富士的地址数据信息</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<FujiSPHAddress> ParseFrom(string address)
  {
    return FujiSPHAddress.ParseFrom(address, (ushort) 0);
  }

  /// <summary>
  /// 从实际的Fuji的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual Fuji address
  /// </summary>
  /// <param name="address">富士的地址数据信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<FujiSPHAddress> ParseFrom(string address, ushort length)
  {
    FujiSPHAddress fujiSphAddress = new FujiSPHAddress();
    try
    {
      switch (address[0])
      {
        case 'I':
        case 'Q':
        case 'i':
        case 'q':
          string[] strArray1 = address.SplitDot();
          fujiSphAddress.TypeCode = (byte) 1;
          fujiSphAddress.AddressStart = Convert.ToInt32(strArray1[0].Substring(1));
          if (strArray1.Length > 1)
          {
            fujiSphAddress.BitIndex = HslHelper.CalculateBitStartIndex(strArray1[1]);
            break;
          }
          break;
        case 'M':
        case 'm':
          string[] strArray2 = address.SplitDot();
          switch (int.Parse(strArray2[0].Substring(1)))
          {
            case 1:
              fujiSphAddress.TypeCode = (byte) 2;
              break;
            case 3:
              fujiSphAddress.TypeCode = (byte) 4;
              break;
            case 10:
              fujiSphAddress.TypeCode = (byte) 8;
              break;
            default:
              throw new Exception(StringResources.Language.NotSupportedDataType);
          }
          fujiSphAddress.AddressStart = Convert.ToInt32(strArray2[1]);
          if (strArray2.Length > 2)
          {
            fujiSphAddress.BitIndex = HslHelper.CalculateBitStartIndex(strArray2[2]);
            break;
          }
          break;
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<FujiSPHAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
    return OperateResult.CreateSuccessResult<FujiSPHAddress>(fujiSphAddress);
  }
}
