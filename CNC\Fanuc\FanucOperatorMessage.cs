﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.FanucOperatorMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System.Text;

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>Fanuc机床的操作信息</summary>
public class FanucOperatorMessage
{
  /// <summary>Number of operator's message</summary>
  public short Number { get; set; }

  /// <summary>Kind of operator's message</summary>
  public short Type { get; set; }

  /// <summary>Operator's message strings</summary>
  public string Data { get; set; }

  /// <summary>创建一个fanuc的操作消息对象</summary>
  /// <param name="byteTransform">数据变换对象</param>
  /// <param name="buffer">读取的数据缓存信息</param>
  /// <param name="encoding">解析的编码信息</param>
  /// <returns>fanuc设备的操作信息</returns>
  public static FanucOperatorMessage CreateMessage(
    IByteTransform byteTransform,
    byte[] buffer,
    Encoding encoding)
  {
    FanucOperatorMessage message = new FanucOperatorMessage();
    message.Number = byteTransform.TransInt16(buffer, 2);
    message.Type = byteTransform.TransInt16(buffer, 6);
    short count = byteTransform.TransInt16(buffer, 10);
    message.Data = (int) count + 12 > buffer.Length ? encoding.GetString(buffer, 12, buffer.Length - 12).TrimEnd(new char[1]) : encoding.GetString(buffer, 12, (int) count);
    return message;
  }
}
