﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT645With1997Server
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Instrument.DLT.Helper;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>DLT645-1997协议的服务端实现类，主要用于与DLT645-1997设备进行通信。</summary>
public class DLT645With1997Server : DLT645Server
{
  /// <inheritdoc />
  protected override void CreateAddressTags()
  {
    this.AddDltTag("9010", "12345678", 2);
    this.AddDltTag("9020", "12345679", 2);
    this.AddDltTag("9110", "12345680", 2);
    this.AddDltTag("9120", "12345681", 2);
    this.AddDltTag("9130", "12345682", 2);
    this.AddDltTag("9140", "12345683", 2);
    this.AddDltTag("9150", "12345684", 2);
    this.AddDltTag("9160", "12345685", 2);
    this.AddDltAsciiTag("B210", "07021756", -1);
    this.AddDltTag("B212", "1234", 0);
    this.AddDltTag("B214", "123456", 0);
    this.AddDltTag("B310", "4567", 0);
    this.AddDltTag("B311", "4568", 0);
    this.AddDltTag("B312", "4569", 0);
    this.AddDltTag("B313", "4570", 0);
    this.AddDltTag("B320", "123456", 0);
    this.AddDltTag("B321", "123457", 0);
    this.AddDltTag("B322", "123458", 0);
    this.AddDltTag("B323", "123459", 0);
    this.AddDltTag("B611", "0231", 1);
    this.AddDltTag("B612", "0232", 1);
    this.AddDltTag("B613", "0233", 1);
    this.AddDltTag("B621", "1234", 2);
    this.AddDltTag("B622", "1334", 2);
    this.AddDltTag("B623", "1434", 2);
    this.AddDltTag("B630", "234567", 4);
    this.AddDltTag("B631", "234568", 4);
    this.AddDltTag("B632", "234569", 4);
    this.AddDltTag("B633", "234570", 4);
    this.AddDltTag("B640", "1234", 2);
    this.AddDltTag("B641", "1235", 2);
    this.AddDltTag("B642", "1236", 2);
    this.AddDltTag("B643", "1237", 2);
    this.AddDltTag("B650", "2213", 3);
    this.AddDltTag("B651", "2214", 3);
    this.AddDltTag("B652", "2215", 3);
    this.AddDltTag("B653", "2216", 3);
    this.AddDltAsciiTag("C032", "123456654321", -1);
    this.AddDltAsciiTag("C033", "123457754321", -1);
    this.AddDltAsciiTag("C034", "112233445566", -1);
  }

  /// <inheritdoc />
  protected override DLT645Type GetLT645Type() => DLT645Type.DLT1997;

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromDLTCore(byte[] receive)
  {
    return base.ReadFromDLTCore(receive);
  }

  /// <inheritdoc />
  public override string ToString() => $"DLT645With1997Server[{this.Port}]";
}
