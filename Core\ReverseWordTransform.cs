﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.ReverseWordTransform
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// 按照字节错位的数据转换类<br />
/// Data conversion class according to byte misalignment
/// </summary>
public class ReverseWordTransform : RegularByteTransform
{
  /// <inheritdoc cref="M:HslCommunication.Core.RegularByteTransform.#ctor" />
  public ReverseWordTransform() => this.DataFormat = DataFormat.CDAB;

  /// <inheritdoc cref="M:HslCommunication.Core.RegularByteTransform.#ctor(HslCommunication.Core.DataFormat)" />
  public ReverseWordTransform(DataFormat dataFormat)
    : base(dataFormat)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IByteTransform.CreateByDateFormat(HslCommunication.Core.DataFormat)" />
  public override IByteTransform CreateByDateFormat(DataFormat dataFormat)
  {
    ReverseWordTransform byDateFormat = new ReverseWordTransform(dataFormat);
    byDateFormat.IsStringReverseByteWord = this.IsStringReverseByteWord;
    return (IByteTransform) byDateFormat;
  }

  /// <inheritdoc />
  public override string ToString() => $"ReverseWordTransform[{this.DataFormat}]";
}
