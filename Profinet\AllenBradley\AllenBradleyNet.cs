﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>
/// AB PLC的数据通信类，使用CIP协议实现，适用1756，1769等型号，支持使用标签的形式进行读写操作，支持标量数据，一维数组，二维数组，三维数组等等。如果是局部变量，那么使用 Program:MainProgram.[变量名]。<br />
/// The data communication class of AB PLC is implemented using the CIP protocol. It is suitable for 1756, 1769 and other models.
/// It supports reading and writing in the form of tags, scalar data, one-dimensional array, two-dimensional array,
/// three-dimensional array, and so on. If it is a local variable, use the Program:MainProgram.[Variable name].
/// </summary>
/// <remarks>
/// thanks 江阴-  ∮溪风-⊙_⌒ help test the dll
/// <br />
/// thanks 上海-null 测试了这个dll
/// <br />
/// <br />
/// 默认的地址就是PLC里的TAG名字，比如A，B，C；如果你需要读取的数据是一个数组，那么A就是默认的A[0]，如果想要读取偏移量为10的数据，那么地址为A[10]，
/// 多维数组同理，使用A[10,10,10]的操作。
/// <br />
/// <br />
/// 假设你读取的是局部变量，那么使用 Program:MainProgram.变量名<br />
/// 目前适用的系列为1756 ControlLogix, 1756 GuardLogix, 1769 CompactLogix, 1769 Compact GuardLogix, 1789SoftLogix, 5069 CompactLogix, 5069 Compact GuardLogix, Studio 5000 Logix Emulate
/// <br />
/// <br />
/// 如果你有个Bool数组要读取，变量名为 A, 那么读第0个位，可以通过 ReadBool("A")，但是第二个位需要使用<br />
/// ReadBoolArray("A[0]")   // 返回32个bool长度，0-31的索引，如果我想读取32-63的位索引，就需要 ReadBoolArray("A[1]") ，以此类推。
/// <br />
/// <br />
/// 地址可以携带站号信息，只要在前面加上slot=2;即可，这就是访问站号2的数据了，例如 slot=2;AAA，如果使用了自定义的消息路由，例如：[IP or Hostname],1,[Optional Routing Path],CPU Slot ************,1,[15,2,18,1],12<br />
/// 在实例化之后，连接PLC之前，需要调用如下代码 plc.MessageRouter = new MessageRouter( "*********.1.12" )
/// </remarks>
public class AllenBradleyNet : DeviceTcpNet, IReadWriteCip, IReadWriteNet
{
  private long contextId = 0;

  /// <summary>
  /// Instantiate a communication object for a Allenbradley PLC protocol
  /// </summary>
  public AllenBradleyNet()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>
  /// Instantiate a communication object for a Allenbradley PLC protocol
  /// </summary>
  /// <param name="ipAddress">PLC IpAddress</param>
  /// <param name="port">PLC Port</param>
  public AllenBradleyNet(string ipAddress, int port = 44818)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new AllenBradleyMessage(this.ContextCheck);
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    byte[] bytes = BitConverter.GetBytes(Interlocked.Increment(ref this.contextId));
    return AllenBradleyHelper.PackRequestHeader(this.CipCommand, this.SessionHandle, command, bytes);
  }

  /// <summary>
  /// The current session handle, which is determined by the PLC when communicating with the PLC handshake
  /// </summary>
  public uint SessionHandle { get; protected set; }

  /// <summary>
  /// Gets or sets the slot number information for the current plc, which should be set before connections
  /// </summary>
  public byte Slot { get; set; } = 0;

  /// <summary>port and slot information</summary>
  public byte[] PortSlot { get; set; }

  /// <summary>
  /// 获取或设置整个交互指令的控制码，默认为0x6F，通常不需要修改<br />
  /// Gets or sets the control code of the entire interactive instruction. The default is 0x6F, and usually does not need to be modified.
  /// </summary>
  public ushort CipCommand { get; set; } = 111;

  /// <summary>
  /// 获取或设置当前的通信的消息路由信息，可以实现一些复杂情况的通信，数据包含背板号，路由参数，slot，例如：*********.1.1<br />
  /// Get or set the message routing information of the current communication, which can realize some complicated communication.
  /// The data includes the backplane number, routing parameters, and slot, for example: *********.1.1
  /// </summary>
  public MessageRouter MessageRouter { get; set; }

  /// <summary>
  /// Gets or sets a value indicating whether the current context meets the required conditions.
  /// </summary>
  public bool ContextCheck { get; set; } = false;

  /// <summary>
  /// 获取或设置是否在读取数组的时候使用片段读取功能，默认是开启的。<br />
  /// Get or set whether to use the fragment reading function when reading the array. By default, it is enabled.
  /// </summary>
  public bool ReadArrayUseSegment { get; set; } = true;

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    Interlocked.Exchange(ref this.contextId, 0L);
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.RegisterSessionHandle(), true, false);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult operateResult2 = AllenBradleyHelper.CheckResponse(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    if (operateResult1.Content.Length >= 8)
      this.SessionHandle = BitConverter.ToUInt32(operateResult1.Content, 4);
    if (this.MessageRouter != null)
    {
      OperateResult<byte[]> operateResult3 = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, this.SessionHandle, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService(this.MessageRouter.GetRouterCIP()))), true, false);
      if (!operateResult3.IsSuccess)
        return (OperateResult) operateResult3;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult ExtraOnDisconnect()
  {
    if (this.CommunicationPipe != null)
    {
      OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.UnRegisterSessionHandle(this.SessionHandle), true, false);
      if (!operateResult.IsSuccess)
        return (OperateResult) operateResult;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    Interlocked.Exchange(ref this.contextId, 0L);
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.RegisterSessionHandle(), true, false).ConfigureAwait(false);
    OperateResult<byte[]> read = await configuredTaskAwaitable;
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = AllenBradleyHelper.CheckResponse(read.Content);
    if (!check.IsSuccess)
      return check;
    if (read.Content.Length >= 8)
      this.SessionHandle = BitConverter.ToUInt32(read.Content, 4);
    if (this.MessageRouter != null)
    {
      byte[] cip = this.MessageRouter.GetRouterCIP();
      configuredTaskAwaitable = this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, this.SessionHandle, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService(cip))), true, false).ConfigureAwait(false);
      OperateResult<byte[]> messageRouter = await configuredTaskAwaitable;
      if (!messageRouter.IsSuccess)
        return (OperateResult) messageRouter;
      cip = (byte[]) null;
      messageRouter = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> ExtraOnDisconnectAsync()
  {
    if (this.CommunicationPipe != null)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.UnRegisterSessionHandle(this.SessionHandle), true, false);
      if (!read.IsSuccess)
        return (OperateResult) read;
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 创建一个读取标签的报文指定，标签地址可以手动动态指定slot编号，例如 slot=2;AAA<br />
  /// Build a read command bytes, The label address can manually specify the slot number dynamically, for example slot=2;AAA
  /// </summary>
  /// <param name="address">the address of the tag name</param>
  /// <param name="length">Array information, if not arrays, is 1 </param>
  /// <returns>Message information that contains the result object </returns>
  public virtual OperateResult<byte[]> BuildReadCommand(string[] address, ushort[] length)
  {
    if (address == null || length == null)
      return new OperateResult<byte[]>("address or length is null");
    if (address.Length != length.Length)
      return new OperateResult<byte[]>("address and length is not same array");
    try
    {
      byte num = this.Slot;
      List<byte[]> numArrayList = new List<byte[]>();
      for (int index = 0; index < address.Length; ++index)
      {
        num = (byte) HslHelper.ExtractParameter(ref address[index], "slot", (int) this.Slot);
        numArrayList.Add(AllenBradleyHelper.PackRequsetRead(address[index], (int) length[index]));
      }
      byte[][] numArray = new byte[2][]{ new byte[4], null };
      byte[] portSlot = this.PortSlot;
      if (portSlot == null)
        portSlot = new byte[2]{ (byte) 1, num };
      numArray[1] = this.PackCommandService(portSlot, numArrayList.ToArray());
      return OperateResult.CreateSuccessResult<byte[]>(AllenBradleyHelper.PackCommandSpecificData(numArray));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address Wrong:" + ex.Message);
    }
  }

  /// <summary>
  /// 创建一个读取多标签的报文<br />
  /// Build a read command bytes
  /// </summary>
  /// <param name="address">The address of the tag name </param>
  /// <returns>Message information that contains the result object </returns>
  public OperateResult<byte[]> BuildReadCommand(string[] address)
  {
    if (address == null)
      return new OperateResult<byte[]>("address or length is null");
    ushort[] length = new ushort[address.Length];
    for (int index = 0; index < address.Length; ++index)
      length[index] = (ushort) 1;
    return this.BuildReadCommand(address, length);
  }

  /// <summary>Create a written message instruction</summary>
  /// <param name="address">The address of the tag name </param>
  /// <param name="typeCode">Data type</param>
  /// <param name="data">Source Data </param>
  /// <param name="length">In the case of arrays, the length of the array </param>
  /// <returns>Message information that contains the result object</returns>
  protected virtual OperateResult<List<byte[]>> BuildWriteCommand(
    string address,
    ushort typeCode,
    byte[] data,
    int length = 1)
  {
    try
    {
      byte parameter1 = (byte) HslHelper.ExtractParameter(ref address, "slot", (int) this.Slot);
      int parameter2 = HslHelper.ExtractParameter(ref address, "x", -1);
      if (parameter2 == 83 || parameter2 == 82)
      {
        int startIndex = 0;
        List<byte[]> numArrayList = SoftBasic.ArraySplitByLength<byte>(data, 474);
        for (int index = 0; index < numArrayList.Count; ++index)
        {
          byte[] numArray1 = AllenBradleyHelper.PackRequestWriteSegment(address, typeCode, numArrayList[index], startIndex, length);
          startIndex += numArrayList[index].Length;
          byte[][] numArray2 = new byte[2][]
          {
            new byte[4],
            null
          };
          byte[] portSlot = this.PortSlot;
          if (portSlot == null)
            portSlot = new byte[2]{ (byte) 1, parameter1 };
          numArray2[1] = this.PackCommandService(portSlot, numArray1);
          byte[] numArray3 = AllenBradleyHelper.PackCommandSpecificData(numArray2);
          numArrayList[index] = numArray3;
        }
        return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
      }
      byte[] numArray4 = AllenBradleyHelper.PackRequestWrite(address, typeCode, data, length, paddingTail: this.GetBoolWritePadding());
      byte[][] numArray5 = new byte[2][]
      {
        new byte[4],
        null
      };
      byte[] portSlot1 = this.PortSlot;
      if (portSlot1 == null)
        portSlot1 = new byte[2]{ (byte) 1, parameter1 };
      numArray5[1] = this.PackCommandService(portSlot1, numArray4);
      return OperateResult.CreateSuccessResult<List<byte[]>>(new List<byte[]>()
      {
        AllenBradleyHelper.PackCommandSpecificData(numArray5)
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<List<byte[]>>("Address Wrong:" + ex.Message);
    }
  }

  /// <summary>
  /// 当进行bool写入的时候，是否需要补齐到字节长度，默认不需要<br />
  /// When performing bool writing, whether it is necessary to complement to the byte length is not required by default
  /// </summary>
  /// <returns>是否需要字节对齐</returns>
  protected virtual bool GetBoolWritePadding() => false;

  /// <summary>Create a written message instruction</summary>
  /// <param name="address">The address of the tag name </param>
  /// <param name="data">Bool Data </param>
  /// <returns>Message information that contains the result object</returns>
  public OperateResult<byte[]> BuildWriteCommand(string address, bool data)
  {
    try
    {
      byte parameter = (byte) HslHelper.ExtractParameter(ref address, "slot", (int) this.Slot);
      byte[] numArray1 = AllenBradleyHelper.PackRequestWrite(address, data);
      byte[][] numArray2 = new byte[2][]
      {
        new byte[4],
        null
      };
      byte[] portSlot = this.PortSlot;
      if (portSlot == null)
        portSlot = new byte[2]{ (byte) 1, parameter };
      numArray2[1] = this.PackCommandService(portSlot, numArray1);
      return OperateResult.CreateSuccessResult<byte[]>(AllenBradleyHelper.PackCommandSpecificData(numArray2));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address Wrong:" + ex.Message);
    }
  }

  private OperateResult CheckResponse(byte[] response)
  {
    OperateResult operateResult = AllenBradleyHelper.CheckResponse(response);
    if (!operateResult.IsSuccess && operateResult.ErrorCode == 100)
      this.CommunicationPipe.RaisePipeError();
    return operateResult;
  }

  /// <summary>
  /// 读取指定地址的二进制数据内容，长度为地址长度，一般都是1，除非读取数组时，如果需要强制使用 片段读取功能码，则地址里携带 x=0x52; 或是 x=82; 则强制使用片段读取。<br />
  /// Read the binary data content of the specified address, the length is the address length, generally 1, unless the array is read, if you need to force the fragment reading function code,
  /// the address carries x=0x52; or x=82; then the fragment read is forced.
  /// </summary>
  /// <remarks>
  /// 使用片段读取的时候，可以读取一些数量量非常大的地址，例如一个结构体标签有100_000个字节长度的时候。<br />
  /// When using fragment reading, you can read a very large number of addresses, such as when a struct tag is 100_000 bytes long.
  /// </remarks>
  /// <param name="address">Address format of the node</param>
  /// <param name="length">In the case of arrays, the length of the array </param>
  /// <returns>Result data with result object </returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    HslHelper.ExtractParameter(ref address, "type", 0);
    int parameter = HslHelper.ExtractParameter(ref address, "x", -1);
    if (parameter == 82 || parameter == 83 || length > (ushort) 1 && this.ReadArrayUseSegment)
      return this.ReadSegment(address, 0, (int) length);
    return this.Read(new string[1]{ address }, new ushort[1]
    {
      length
    });
  }

  /// <summary>
  /// <b>[商业授权]</b> 批量读取多地址的数据信息，例如我可以读取两个标签的数据 "A","B[0]"，每个地址的数据长度为1，表示一个数据，最终读取返回的是一整个的字节数组，需要自行解析<br />
  /// <b>[Authorization]</b> Batch read data information of multiple addresses, for example, I can read the data of two tags "A", "B[0]", the data length of each address is 1,
  /// which means one data, and the final read returns a The entire byte array, which needs to be parsed by itself
  /// </summary>
  /// <param name="address">Name of the node </param>
  /// <returns>Result data with result object </returns>
  [HslMqttApi("ReadAddress", "")]
  public OperateResult<byte[]> Read(string[] address)
  {
    if (address == null)
      return new OperateResult<byte[]>("address can not be null");
    ushort[] length = new ushort[address.Length];
    for (int index = 0; index < length.Length; ++index)
      length[index] = (ushort) 1;
    return this.Read(address, length);
  }

  /// <summary>
  /// <b>[商业授权]</b> 批量读取多地址的数据信息，例如我可以读取两个标签的数据 "A","B[0]"， 长度为 [1, 5]，返回的是一整个的字节数组，需要自行解析<br />
  /// <b>[Authorization]</b> Read the data information of multiple addresses in batches. For example, I can read the data "A", "B[0]" of two tags,
  /// the length is [1, 5], and the return is an entire byte array, and I need to do it myself Parsing
  /// </summary>
  /// <param name="address">节点的名称 -&gt; Name of the node </param>
  /// <param name="length">如果是数组，就为数组长度 -&gt; In the case of arrays, the length of the array </param>
  /// <returns>带有结果对象的结果数据 -&gt; Result data with result object </returns>
  public OperateResult<byte[]> Read(string[] address, ushort[] length)
  {
    if (address != null && address.Length > 1 && !Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[], ushort, bool> result = this.ReadWithType(address, length);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(result.Content1);
  }

  private OperateResult<byte[], ushort, bool> ReadWithType(string[] address, ushort[] length)
  {
    OperateResult<byte[]> result1 = this.BuildReadCommand(address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) result2);
    OperateResult result3 = this.CheckResponse(result2.Content);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<byte[], ushort, bool>(result3) : AllenBradleyHelper.ExtractActualData(result2.Content, true);
  }

  /// <summary>
  /// Read Segment Data Array form plc, use address tag name
  /// </summary>
  /// <param name="address">Tag name in plc</param>
  /// <param name="startIndex">array start index, uint byte index</param>
  /// <param name="length">array length, data item length</param>
  /// <returns>Results Bytes</returns>
  [HslMqttApi("ReadSegment", "")]
  public OperateResult<byte[]> ReadSegment(string address, int startIndex, int length)
  {
    try
    {
      List<byte> byteList = new List<byte>();
      OperateResult<byte[]> operateResult;
      OperateResult<byte[], ushort, bool> actualData;
      do
      {
        operateResult = this.ReadCipFromServer(AllenBradleyHelper.PackRequestReadSegment(address, startIndex, length));
        if (operateResult.IsSuccess)
        {
          actualData = AllenBradleyHelper.ExtractActualData(operateResult.Content, true);
          if (actualData.IsSuccess)
          {
            startIndex += actualData.Content1.Length;
            byteList.AddRange((IEnumerable<byte>) actualData.Content1);
          }
          else
            goto label_4;
        }
        else
          goto label_2;
      }
      while (actualData.Content3);
      goto label_7;
label_2:
      return operateResult;
label_4:
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) actualData);
label_7:
      return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address Wrong:" + ex.Message);
    }
  }

  private OperateResult<byte[]> ReadByCips(params byte[][] cips)
  {
    OperateResult<byte[]> operateResult = this.ReadCipFromServer(cips);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte[], ushort, bool> actualData = AllenBradleyHelper.ExtractActualData(operateResult.Content, true);
    return !actualData.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) actualData) : OperateResult.CreateSuccessResult<byte[]>(actualData.Content1);
  }

  /// <summary>使用CIP报文和服务器进行核心的数据交换</summary>
  /// <param name="cips">Cip commands</param>
  /// <returns>Results Bytes</returns>
  public OperateResult<byte[]> ReadCipFromServer(params byte[][] cips)
  {
    byte[][] numArray = new byte[2][]{ new byte[4], null };
    byte[] portSlot = this.PortSlot;
    if (portSlot == null)
      portSlot = new byte[2]{ (byte) 1, this.Slot };
    numArray[1] = this.PackCommandService(portSlot, ((IEnumerable<byte[]>) cips).ToArray<byte[]>());
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(AllenBradleyHelper.PackCommandSpecificData(numArray));
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult result = this.CheckResponse(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult<byte[]>(operateResult.Content);
  }

  /// <summary>使用EIP报文和服务器进行核心的数据交换</summary>
  /// <param name="eip">eip commands</param>
  /// <returns>Results Bytes</returns>
  public OperateResult<byte[]> ReadEipFromServer(params byte[][] eip)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(AllenBradleyHelper.PackCommandSpecificData(eip));
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult result = this.CheckResponse(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult<byte[]>(operateResult.Content);
  }

  /// <summary>
  /// 读取单个的bool数据信息，如果读取的是单bool变量，就直接写变量名，如果是由int组成的bool数组的一个值，一律带"i="开头访问，例如"i=A[0]" <br />
  /// Read a single bool data information, if it is a single bool variable, write the variable name directly,
  /// if it is a value of a bool array composed of int, it is always accessed with "i=" at the beginning, for example, "i=A[0]"
  /// </summary>
  /// <param name="address">节点的名称 -&gt; Name of the node </param>
  /// <returns>带有结果对象的结果数据 -&gt; Result data with result info </returns>
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    if (address.StartsWith("i="))
      return ByteTransformHelper.GetResultFromArray<bool>(this.ReadBool(address, (ushort) 1));
    OperateResult<byte[]> result = this.Read(address, (ushort) 1);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) result) : OperateResult.CreateSuccessResult<bool>(this.ByteTransform.TransBool(result.Content, 0));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (address.StartsWith("i="))
    {
      address = address.Substring(2);
      int arrayIndex;
      address = AllenBradleyHelper.AnalysisArrayIndex(address, out arrayIndex);
      string str = arrayIndex / 32 /*0x20*/ == 0 ? "" : $"[{arrayIndex / 32 /*0x20*/}]";
      ushort occupyLength = (ushort) HslHelper.CalculateOccupyLength(arrayIndex, (int) length, 32 /*0x20*/);
      OperateResult<byte[]> result = this.Read(address + str, occupyLength);
      return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(result.Content.ToBoolArray().SelectMiddle<bool>(arrayIndex % 32 /*0x20*/, (int) length));
    }
    OperateResult<byte[]> result1 = this.Read(address, length);
    return !result1.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result1) : OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(result1.Content, (int) length));
  }

  /// <summary>
  /// 批量读取的bool数组信息，如果你有个Bool数组变量名为 A, 那么读第0个位，可以通过 ReadBool("A")，但是第二个位需要使用
  /// ReadBoolArray("A[0]")   // 返回32个bool长度，0-31的索引，如果我想读取32-63的位索引，就需要 ReadBoolArray("A[1]") ，以此类推。<br />
  /// For batch read bool array information, if you have a Bool array variable named A, then you can read the 0th bit through ReadBool("A"),
  /// but the second bit needs to use ReadBoolArray("A[0]" ) // Returns the length of 32 bools, the index is 0-31,
  /// if I want to read the bit index of 32-63, I need ReadBoolArray("A[1]"), and so on.
  /// </summary>
  /// <param name="address">节点的名称 -&gt; Name of the node </param>
  /// <returns>带有结果对象的结果数据 -&gt; Result data with result info </returns>
  [HslMqttApi("ReadBoolArrayAddress", "")]
  public OperateResult<bool[]> ReadBoolArray(string address)
  {
    OperateResult<byte[]> result = this.Read(address, (ushort) 1);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(result.Content.ToBoolArray());
  }

  /// <summary>
  /// 读取PLC的byte类型的数据<br />
  /// Read the byte type of PLC data
  /// </summary>
  /// <param name="address">节点的名称 -&gt; Name of the node </param>
  /// <returns>带有结果对象的结果数据 -&gt; Result data with result info </returns>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>
  /// 从PLC里读取一个指定标签名的原始数据信息及其数据类型信息<br />
  /// Read the original data information of a specified tag name and its data type information from the PLC
  /// </summary>
  /// <remarks>
  /// 数据类型的定义，可以参考 <see cref="T:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper" /> 的常量资源信息。
  /// </remarks>
  /// <param name="address">PLC的标签地址信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <returns>包含原始数据信息及数据类型的结果对象</returns>
  public OperateResult<ushort, byte[]> ReadTag(string address, ushort length = 1)
  {
    OperateResult<byte[], ushort, bool> result = this.ReadWithType(new string[1]
    {
      address
    }, new ushort[1]{ length });
    return !result.IsSuccess ? OperateResult.CreateFailedResult<ushort, byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<ushort, byte[]>(result.Content2, result.Content1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    HslHelper.ExtractParameter(ref address, "type", 0);
    int x = HslHelper.ExtractParameter(ref address, "x", -1);
    if (x == 82 || x == 83)
    {
      OperateResult<byte[]> operateResult = await this.ReadSegmentAsync(address, 0, (int) length).ConfigureAwait(false);
      return operateResult;
    }
    if (length > (ushort) 1 && this.ReadArrayUseSegment)
    {
      OperateResult<byte[]> operateResult = await this.ReadSegmentAsync(address, 0, (int) length).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<byte[]> operateResult1 = await this.ReadAsync(new string[1]
    {
      address
    }, new ushort[1]{ length }).ConfigureAwait(false);
    return operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Read(System.String[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address)
  {
    if (address == null)
      return new OperateResult<byte[]>("address can not be null");
    ushort[] length = new ushort[address.Length];
    for (int i = 0; i < length.Length; ++i)
      length[i] = (ushort) 1;
    OperateResult<byte[]> operateResult = await this.ReadAsync(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Read(System.String[],System.UInt16[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address, ushort[] length)
  {
    string[] strArray = address;
    if (strArray != null && strArray.Length > 1 && !Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(address, length).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content1) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  private async Task<OperateResult<byte[], ushort, bool>> ReadWithTypeAsync(
    string[] address,
    ushort[] length)
  {
    OperateResult<byte[]> command = this.BuildReadCommand(address, length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) command);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) read);
    OperateResult check = this.CheckResponse(read.Content);
    return check.IsSuccess ? AllenBradleyHelper.ExtractActualData(read.Content, true) : OperateResult.CreateFailedResult<byte[], ushort, bool>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadSegment(System.String,System.Int32,System.Int32)" />
  public async Task<OperateResult<byte[]>> ReadSegmentAsync(
    string address,
    int startIndex,
    int length)
  {
    try
    {
      List<byte> bytesContent = new List<byte>();
      OperateResult<byte[]> read;
      OperateResult<byte[], ushort, bool> analysis;
      while (true)
      {
        read = await this.ReadCipFromServerAsync(AllenBradleyHelper.PackRequestReadSegment(address, startIndex, length)).ConfigureAwait(false);
        if (read.IsSuccess)
        {
          analysis = AllenBradleyHelper.ExtractActualData(read.Content, true);
          if (analysis.IsSuccess)
          {
            startIndex += analysis.Content1.Length;
            bytesContent.AddRange((IEnumerable<byte>) analysis.Content1);
            if (analysis.Content3)
            {
              read = (OperateResult<byte[]>) null;
              analysis = (OperateResult<byte[], ushort, bool>) null;
            }
            else
              goto label_9;
          }
          else
            goto label_5;
        }
        else
          break;
      }
      return read;
label_5:
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
label_9:
      return OperateResult.CreateSuccessResult<byte[]>(bytesContent.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address Wrong:" + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadCipFromServer(System.Byte[][])" />
  public async Task<OperateResult<byte[]>> ReadCipFromServerAsync(params byte[][] cips)
  {
    byte[][] numArray = new byte[2][]{ new byte[4], null };
    byte[] portSlot = this.PortSlot;
    if (portSlot == null)
      portSlot = new byte[2]{ (byte) 1, this.Slot };
    numArray[1] = this.PackCommandService(portSlot, ((IEnumerable<byte[]>) cips).ToArray<byte[]>());
    byte[] commandSpecificData = AllenBradleyHelper.PackCommandSpecificData(numArray);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(commandSpecificData).ConfigureAwait(false);
    if (!read.IsSuccess)
      return read;
    OperateResult check = this.CheckResponse(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content) : OperateResult.CreateFailedResult<byte[]>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadEipFromServer(System.Byte[][])" />
  public async Task<OperateResult<byte[]>> ReadEipFromServerAsync(params byte[][] eip)
  {
    byte[] commandSpecificData = AllenBradleyHelper.PackCommandSpecificData(eip);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(commandSpecificData).ConfigureAwait(false);
    if (!read.IsSuccess)
      return read;
    OperateResult check = this.CheckResponse(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content) : OperateResult.CreateFailedResult<byte[]>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadBool(System.String)" />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    if (address.StartsWith("i="))
    {
      OperateResult<bool[]> result = await this.ReadBoolAsync(address, (ushort) 1).ConfigureAwait(false);
      return ByteTransformHelper.GetResultFromArray<bool>(result);
    }
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 1).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool>(this.ByteTransform.TransBool(read.Content, 0)) : OperateResult.CreateFailedResult<bool>((OperateResult) read);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (address.StartsWith("i="))
    {
      address = address.Substring(2);
      int bitIndex;
      address = AllenBradleyHelper.AnalysisArrayIndex(address, out bitIndex);
      string uintIndex = bitIndex / 32 /*0x20*/ == 0 ? "" : $"[{bitIndex / 32 /*0x20*/}]";
      ushort len = (ushort) HslHelper.CalculateOccupyLength(bitIndex, (int) length, 32 /*0x20*/);
      OperateResult<byte[]> read = await this.ReadAsync(address + uintIndex, len).ConfigureAwait(false);
      return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray().SelectMiddle<bool>(bitIndex % 32 /*0x20*/, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    }
    OperateResult<byte[]> read1 = await this.ReadAsync(address, length).ConfigureAwait(false);
    return read1.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(read1.Content, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadBoolArray(System.String)" />
  public async Task<OperateResult<bool[]>> ReadBoolArrayAsync(string address)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 1).ConfigureAwait(false);
    OperateResult<bool[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray()) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadTag(System.String,System.UInt16)" />
  public async Task<OperateResult<ushort, byte[]>> ReadTagAsync(string address, ushort length = 1)
  {
    OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(new string[1]
    {
      address
    }, new ushort[1]{ length }).ConfigureAwait(false);
    OperateResult<ushort, byte[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<ushort, byte[]>(read.Content2, read.Content1) : OperateResult.CreateFailedResult<ushort, byte[]>((OperateResult) read);
    read = (OperateResult<byte[], ushort, bool>) null;
    return operateResult;
  }

  /// <summary>
  /// 枚举当前的所有的变量名字，包含结构体信息，除去系统自带的名称数据信息<br />
  /// Enumerate all the current variable names, including structure information, except the name data information that comes with the system
  /// </summary>
  /// <returns>结果对象</returns>
  public OperateResult<AbTagItem[]> TagEnumerator()
  {
    List<AbTagItem> abTagItemList = new List<AbTagItem>();
    for (int index = 0; index < 2; ++index)
    {
      uint startInstance = 0;
      OperateResult<byte[]> result;
      OperateResult<byte[], ushort, bool> actualData;
      do
      {
        result = this.ReadCipFromServer(index == 0 ? AllenBradleyHelper.BuildEnumeratorCommand(startInstance) : AllenBradleyHelper.BuildEnumeratorProgrameMainCommand(startInstance));
        if (result.IsSuccess)
        {
          actualData = AllenBradleyHelper.ExtractActualData(result.Content, true);
          if (actualData.IsSuccess)
          {
            if (result.Content.Length >= 43 && BitConverter.ToUInt16(result.Content, 40) == (ushort) 213)
            {
              uint instance;
              abTagItemList.AddRange((IEnumerable<AbTagItem>) AbTagItem.PraseAbTagItems(result.Content, 44, index == 0, out instance));
              startInstance = instance + 1U;
            }
            else
              goto label_9;
          }
          else
            goto label_4;
        }
        else
          goto label_2;
      }
      while (actualData.Content3);
      continue;
label_2:
      return OperateResult.CreateFailedResult<AbTagItem[]>((OperateResult) result);
label_4:
      return index == 1 ? OperateResult.CreateSuccessResult<AbTagItem[]>(abTagItemList.ToArray()) : OperateResult.CreateFailedResult<AbTagItem[]>((OperateResult) actualData);
label_9:
      return new OperateResult<AbTagItem[]>($"{StringResources.Language.UnknownError} Source: {result.Content.ToHexString(' ')}");
    }
    return OperateResult.CreateSuccessResult<AbTagItem[]>(abTagItemList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.TagEnumerator" />
  public async Task<OperateResult<AbTagItem[]>> TagEnumeratorAsync()
  {
    List<AbTagItem> lists = new List<AbTagItem>();
label_12:
    for (int i = 0; i < 2; ++i)
    {
      uint instanceAddress = 0;
      OperateResult<byte[]> readCip;
      OperateResult<byte[], ushort, bool> analysis;
      while (true)
      {
        readCip = await this.ReadCipFromServerAsync(i == 0 ? AllenBradleyHelper.BuildEnumeratorCommand(instanceAddress) : AllenBradleyHelper.BuildEnumeratorProgrameMainCommand(instanceAddress));
        if (readCip.IsSuccess)
        {
          analysis = AllenBradleyHelper.ExtractActualData(readCip.Content, true);
          if (analysis.IsSuccess)
          {
            if (readCip.Content.Length >= 43 && BitConverter.ToUInt16(readCip.Content, 40) == (ushort) 213)
            {
              uint instance;
              lists.AddRange((IEnumerable<AbTagItem>) AbTagItem.PraseAbTagItems(readCip.Content, 44, i == 0, out instance));
              instanceAddress = instance + 1U;
              if (analysis.Content3)
              {
                readCip = (OperateResult<byte[]>) null;
                analysis = (OperateResult<byte[], ushort, bool>) null;
              }
              else
                goto label_12;
            }
            else
              goto label_8;
          }
          else
            goto label_5;
        }
        else
          break;
      }
      return OperateResult.CreateFailedResult<AbTagItem[]>((OperateResult) readCip);
label_5:
      return i != 1 ? OperateResult.CreateFailedResult<AbTagItem[]>((OperateResult) analysis) : OperateResult.CreateSuccessResult<AbTagItem[]>(lists.ToArray());
label_8:
      return new OperateResult<AbTagItem[]>($"{StringResources.Language.UnknownError} Source: {readCip.Content.ToHexString(' ')}");
    }
    return OperateResult.CreateSuccessResult<AbTagItem[]>(lists.ToArray());
  }

  private OperateResult<AbStructHandle> ReadTagStructHandle(AbTagItem structTag)
  {
    OperateResult<byte[]> result = this.ReadCipFromServer(AllenBradleyHelper.GetStructHandleCommand(structTag.SymbolType));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<AbStructHandle>((OperateResult) result);
    return result.Content.Length >= 43 && BitConverter.ToInt32(result.Content, 40) == 131 ? OperateResult.CreateSuccessResult<AbStructHandle>(new AbStructHandle(result.Content, 44)) : new OperateResult<AbStructHandle>($"{StringResources.Language.UnknownError} Source Data: {result.Content.ToHexString(' ')}");
  }

  /// <summary>
  /// 枚举结构体的方法，传入结构体的标签对象，返回结构体子属性标签列表信息，子属性有可能是标量数据，也可能是另一个结构体。<br />
  /// The method of enumerating the structure, passing in the tag object of the structure,
  /// and returning the tag list information of the sub-attributes of the structure. The sub-attributes may be scalar data or another structure.
  /// </summary>
  /// <param name="structTag">结构体的标签</param>
  /// <returns>是否成功</returns>
  public OperateResult<AbTagItem[]> StructTagEnumerator(AbTagItem structTag)
  {
    OperateResult<AbStructHandle> result1 = this.ReadTagStructHandle(structTag);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<AbTagItem[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadCipFromServer(AllenBradleyHelper.GetStructItemNameType(structTag.SymbolType, result1.Content, 0));
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<AbTagItem[]>((OperateResult) result2);
    return result2.Content.Length >= 43 && result2.Content[40] == (byte) 204 && result2.Content[41] == (byte) 0 && result2.Content[42] == (byte) 0 ? OperateResult.CreateSuccessResult<AbTagItem[]>(AbTagItem.PraseAbTagItemsFromStruct(result2.Content, 44, result1.Content).ToArray()) : new OperateResult<AbTagItem[]>($"{StringResources.Language.UnknownError} Status:{result2.Content[42].ToString()}");
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt16Array", "")]
  public override OperateResult<short[]> ReadInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<short[]>(this.Read(address, length), (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt16Array", "")]
  public override OperateResult<ushort[]> ReadUInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(this.Read(address, length), (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt32Array", "")]
  public override OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<int[]>(this.Read(address, length), (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt32Array", "")]
  public override OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<uint[]>(this.Read(address, length), (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadFloatArray", "")]
  public override OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<float[]>(this.Read(address, length), (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt64Array", "")]
  public override OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<long[]>(this.Read(address, length), (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt64Array", "")]
  public override OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(this.Read(address, length), (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<double[]>(this.Read(address, length), (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public OperateResult<string> ReadString(string address) => this.ReadString(address, (ushort) 1);

  /// <summary>
  /// 读取字符串数据，默认为<see cref="P:System.Text.Encoding.UTF8" />编码<br />
  /// Read string data, default is the <see cref="P:System.Text.Encoding.UTF8" /> encoding
  /// </summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>带有成功标识的string数据</returns>
  /// <example>
  /// 以下为三菱的连接对象示例，其他的设备读写情况参照下面的代码：
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkDeviceBase.cs" region="ReadString" title="String类型示例" />
  /// </example>
  [HslMqttApi("ReadString", "")]
  public override OperateResult<string> ReadString(string address, ushort length)
  {
    return this.ReadString(address, length, Encoding.UTF8);
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    HslHelper.ExtractParameter(ref address, "type", 0);
    return AllenBradleyHelper.ExtractActualString(this.ReadWithType(new string[1]
    {
      address
    }, new ushort[1]{ length }), this.ByteTransform, encoding);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice)" />
  [HslMqttApi(Description = "获取PLC的型号信息")]
  public OperateResult<string> ReadPlcType()
  {
    return AllenBradleyHelper.ReadPlcType((IReadWriteDevice) this);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<short[]>> ReadInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<short[]>(result, (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ushort[]>> ReadUInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(result, (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<int[]>(result, (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<uint[]>(result, (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<float[]>(result, (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<long[]>(result, (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(result, (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<double[]>(result, (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public async Task<OperateResult<string>> ReadStringAsync(string address)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, (ushort) 1);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadString(System.String,System.UInt16)" />
  public override async Task<OperateResult<string>> ReadStringAsync(string address, ushort length)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, length, Encoding.UTF8);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    HslHelper.ExtractParameter(ref address, "type", 0);
    OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(new string[1]
    {
      address
    }, new ushort[1]{ length });
    OperateResult<string> actualString = AllenBradleyHelper.ExtractActualString(read, this.ByteTransform, encoding);
    read = (OperateResult<byte[], ushort, bool>) null;
    return actualString;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice)" />
  public async Task<OperateResult<string>> ReadPlcTypeAsync()
  {
    OperateResult<string> operateResult = await AllenBradleyHelper.ReadPlcTypeAsync((IReadWriteDevice) this);
    return operateResult;
  }

  /// <summary>
  /// 当前写入字节数组使用数据类型 0xD1 写入，如果其他的字节类型需要调用 <see cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteTag(System.String,System.UInt16,System.Byte[],System.Int32)" /> 方法来实现。<br />
  /// The currently written byte array is written using the data type 0xD1. If other byte types need to be called <see cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteTag(System.String,System.UInt16,System.Byte[],System.Int32)" /> Method to achieve. <br />
  /// </summary>
  /// <param name="address">地址</param>
  /// <param name="value">值</param>
  /// <returns>写入结果值</returns>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return this.WriteTag(address, (ushort) 209, value, HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1);
  }

  /// <summary>
  /// 使用指定的类型写入指定的节点数据，类型信息参考API文档，地址支持协议类型代号信息，例如 "type=0xD1;A"<br />
  /// Use the specified type to write the specified node data. For type information, refer to the API documentation. The address supports protocol type code information, such as "type=0xD1;A"
  /// </summary>
  /// <remarks>
  /// 关于参数 length 的含义，表示的是地址长度，一般的标量数据都是 1，如果PLC有个标签是 A，数据类型为 byte[10]，那我们写入 3 个byte就是 WriteTag( "A[5]", 0xD1, new byte[]{1,2,3}, 3 );<br />
  /// Regarding the meaning of the parameter length, it represents the address length. The general scalar data is 1. If the PLC has a tag of A and the data type is byte[10], then we write 3 bytes as WriteTag( "A[5 ]", 0xD1, new byte[]{1,2,3}, 3 );
  /// </remarks>
  /// <param name="address">节点的名称 -&gt; Name of the node </param>
  /// <param name="typeCode">类型代码，详细参见<see cref="T:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper" />上的常用字段 -&gt;  Type code, see the commonly used Fields section on the <see cref="T:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper" /> in detail</param>
  /// <param name="value">实际的数据值 -&gt; The actual data value </param>
  /// <param name="length">如果节点是数组，就是数组长度 -&gt; If the node is an array, it is the array length </param>
  /// <returns>是否写入成功 -&gt; Whether to write successfully</returns>
  public virtual OperateResult WriteTag(string address, ushort typeCode, byte[] value, int length = 1)
  {
    typeCode = (ushort) HslHelper.ExtractParameter(ref address, "type", (int) typeCode);
    OperateResult<List<byte[]>> operateResult1 = this.BuildWriteCommand(address, typeCode, value, length);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    for (int index = 0; index < operateResult1.Content.Count; ++index)
    {
      OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content[index]);
      if (!operateResult2.IsSuccess)
        return (OperateResult) operateResult2;
      OperateResult result = this.CheckResponse(operateResult2.Content);
      if (!result.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>(result);
      OperateResult actualData = (OperateResult) AllenBradleyHelper.ExtractActualData(operateResult2.Content, false);
      if (!actualData.IsSuccess)
        return actualData;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 209, value, HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteTag(System.String,System.UInt16,System.Byte[],System.Int32)" />
  public virtual async Task<OperateResult> WriteTagAsync(
    string address,
    ushort typeCode,
    byte[] value,
    int length = 1)
  {
    typeCode = (ushort) HslHelper.ExtractParameter(ref address, "type", (int) typeCode);
    OperateResult<List<byte[]>> command = this.BuildWriteCommand(address, typeCode, value, length);
    if (!command.IsSuccess)
      return (OperateResult) command;
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content[i]).ConfigureAwait(false);
      if (!read.IsSuccess)
        return (OperateResult) read;
      OperateResult check = this.CheckResponse(read.Content);
      if (!check.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>(check);
      OperateResult extra = (OperateResult) AllenBradleyHelper.ExtractActualData(read.Content, false);
      if (!extra.IsSuccess)
        return extra;
      read = (OperateResult<byte[]>) null;
      check = (OperateResult) null;
      extra = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt16Array", "")]
  public override OperateResult Write(string address, short[] values)
  {
    return this.WriteTag(address, (ushort) 195, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt16Array", "")]
  public override OperateResult Write(string address, ushort[] values)
  {
    return this.WriteTag(address, (ushort) 199, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt32Array", "")]
  public override OperateResult Write(string address, int[] values)
  {
    return this.WriteTag(address, (ushort) 196, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt32Array", "")]
  public override OperateResult Write(string address, uint[] values)
  {
    return this.WriteTag(address, (ushort) 200, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteFloatArray", "")]
  public override OperateResult Write(string address, float[] values)
  {
    return this.WriteTag(address, (ushort) 202, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt64Array", "")]
  public override OperateResult Write(string address, long[] values)
  {
    return this.WriteTag(address, (ushort) 197, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt64Array", "")]
  public override OperateResult Write(string address, ulong[] values)
  {
    return this.WriteTag(address, (ushort) 201, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    return this.WriteTag(address, (ushort) 203, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    if (string.IsNullOrEmpty(value))
      value = string.Empty;
    ushort parameter = (ushort) HslHelper.ExtractParameter(ref address, "type", 194);
    if (parameter == (ushort) 218)
    {
      byte[] bytes = encoding.GetBytes(value);
      return this.WriteTag(address, parameter, SoftBasic.SpliceArray<byte>(new byte[1]
      {
        (byte) bytes.Length
      }, bytes), 1);
    }
    byte[] bytes1 = encoding.GetBytes(value);
    OperateResult operateResult = this.Write(address + ".LEN", bytes1.Length);
    if (!operateResult.IsSuccess)
      return operateResult;
    byte[] lengthEven = SoftBasic.ArrayExpandToLengthEven<byte>(bytes1);
    return this.WriteTag(address + ".DATA[0]", parameter, lengthEven, bytes1.Length);
  }

  /// <summary>
  /// 写入单个Bool的数据信息。如果读取的是单bool变量，就直接写变量名，如果是bool数组的一个值，一律带下标访问，例如a[0]<br />
  /// Write the data information of a single Bool. If the read is a single bool variable, write the variable name directly,
  /// if it is a value of the bool array, it will always be accessed with a subscript, such as a[0]
  /// </summary>
  /// <remarks>
  /// 如果写入的是类型代号 0xC1 的bool变量或是数组，直接使用标签名即可，比如：A,A[10]，如果写入的是类型代号0xD3的bool数组的值，则需要使用地址"i="开头，例如：i=A[10]<br />
  /// If you write a bool variable or array of type code 0xC1, you can use the tag name directly, such as: A,A[10],
  /// if you write the value of a bool array of type code 0xD3, you need to use the address" i=" at the beginning, for example: i=A[10]
  /// </remarks>
  /// <param name="address">标签的地址数据</param>
  /// <param name="value">bool数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    if (address.StartsWith("i=") && Regex.IsMatch(address, "\\[[0-9]+\\]$"))
    {
      OperateResult<byte[]> operateResult1 = this.BuildWriteCommand(address.Substring(2), value);
      if (!operateResult1.IsSuccess)
        return (OperateResult) operateResult1;
      OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return (OperateResult) operateResult2;
      OperateResult result = this.CheckResponse(operateResult2.Content);
      return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>(result) : (OperateResult) AllenBradleyHelper.ExtractActualData(operateResult2.Content, false);
    }
    string address1 = address;
    byte[] numArray;
    if (!value)
      numArray = new byte[2];
    else
      numArray = new byte[2]{ byte.MaxValue, byte.MaxValue };
    return this.WriteTag(address1, (ushort) 193, numArray, 1);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return this.WriteTag(address, (ushort) 193, ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1);
  }

  /// <summary>
  /// 写入Byte数据，返回是否写入成功，默认使用类型 0xC2, 如果PLC的变量类型不一样，则需要指定实际的变量类型，例如PLC的变量 A 是0xD1类型，那么地址需要携带类型信息，type=0xD1;A <br />
  /// Write Byte data and return whether the writing is successful. The default type is 0xC2. If the variable types of the PLC are different, you need to specify the actual variable type.
  /// For example, the variable A of the PLC is of type 0xD1, then the address needs to carry the type information, type= 0xD1;A
  /// </summary>
  /// <remarks>
  /// 如何确认PLC的变量的类型呢？可以在HslCommunicationDemo程序上测试知道，也可以直接调用 <see cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadWithType(System.String[],System.UInt16[])" /> 来知道类型信息。
  /// </remarks>
  /// <param name="address">标签的地址数据</param>
  /// <param name="value">Byte数据</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteByte", "")]
  public virtual OperateResult Write(string address, byte value)
  {
    return this.WriteTag(address, (ushort) 194, new byte[2]
    {
      value,
      (byte) 0
    }, 1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Int16[])" />
  public override async Task<OperateResult> WriteAsync(string address, short[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 195, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.UInt16[])" />
  public override async Task<OperateResult> WriteAsync(string address, ushort[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 199, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Int32[])" />
  public override async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 196, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.UInt32[])" />
  public override async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 200, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Single[])" />
  public override async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 202, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Int64[])" />
  public override async Task<OperateResult> WriteAsync(string address, long[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 197, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.UInt64[])" />
  public override async Task<OperateResult> WriteAsync(string address, ulong[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 201, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Double[])" />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 203, this.ByteTransform.TransByte(values), this.GetWriteValueLength(address, values.Length)).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    if (string.IsNullOrEmpty(value))
      value = string.Empty;
    ushort typeCode = (ushort) HslHelper.ExtractParameter(ref address, "type", 194);
    if (typeCode == (ushort) 218)
    {
      byte[] data = encoding.GetBytes(value);
      return this.WriteTag(address, typeCode, SoftBasic.SpliceArray<byte>(new byte[1]
      {
        (byte) data.Length
      }, data), 1);
    }
    byte[] data1 = encoding.GetBytes(value);
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = this.WriteAsync(address + ".LEN", data1.Length).ConfigureAwait(false);
    OperateResult write = await configuredTaskAwaitable;
    if (!write.IsSuccess)
      return write;
    byte[] buffer = SoftBasic.ArrayExpandToLengthEven<byte>(data1);
    configuredTaskAwaitable = this.WriteTagAsync(address + ".DATA[0]", typeCode, buffer, data1.Length).ConfigureAwait(false);
    OperateResult operateResult = await configuredTaskAwaitable;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    if (address.StartsWith("i=") && Regex.IsMatch(address, "\\[[0-9]+\\]$"))
    {
      OperateResult<byte[]> command = this.BuildWriteCommand(address.Substring(2), value);
      if (!command.IsSuccess)
        return (OperateResult) command;
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
      if (!read.IsSuccess)
        return (OperateResult) read;
      OperateResult check = this.CheckResponse(read.Content);
      return check.IsSuccess ? (OperateResult) AllenBradleyHelper.ExtractActualData(read.Content, false) : (OperateResult) OperateResult.CreateFailedResult<byte[]>(check);
    }
    string address1 = address;
    byte[] numArray;
    if (!value)
      numArray = new byte[2];
    else
      numArray = new byte[2]{ byte.MaxValue, byte.MaxValue };
    OperateResult operateResult = await this.WriteTagAsync(address1, (ushort) 193, numArray, 1).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 193, ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Byte)" />
  public virtual async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 194, new byte[2]
    {
      value,
      (byte) 0
    }, 1).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String)" />
  public OperateResult<DateTime> ReadDate(string address)
  {
    return AllenBradleyHelper.ReadDate((IReadWriteCip) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.DateTime)" />
  public OperateResult WriteDate(string address, DateTime date)
  {
    return AllenBradleyHelper.WriteDate((IReadWriteCip) this, address, date);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteDate(System.String,System.DateTime)" />
  public OperateResult WriteTimeAndDate(string address, DateTime date)
  {
    return AllenBradleyHelper.WriteTimeAndDate((IReadWriteCip) this, address, date);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadTime(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String)" />
  public OperateResult<TimeSpan> ReadTime(string address)
  {
    return AllenBradleyHelper.ReadTime((IReadWriteCip) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteTime(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.TimeSpan)" />
  public OperateResult WriteTime(string address, TimeSpan time)
  {
    return AllenBradleyHelper.WriteTime((IReadWriteCip) this, address, time);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteTimeOfDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.TimeSpan)" />
  public OperateResult WriteTimeOfDate(string address, TimeSpan timeOfDate)
  {
    return AllenBradleyHelper.WriteTimeOfDate((IReadWriteCip) this, address, timeOfDate);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadDate(System.String)" />
  public async Task<OperateResult<DateTime>> ReadDateAsync(string address)
  {
    OperateResult<DateTime> operateResult = await AllenBradleyHelper.ReadDateAsync((IReadWriteCip) this, address).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteDate(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteDateAsync(string address, DateTime date)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteDateAsync((IReadWriteCip) this, address, date).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteTimeAndDate(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteTimeAndDateAsync(string address, DateTime date)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteTimeAndDateAsync((IReadWriteCip) this, address, date).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadTime(System.String)" />
  public async Task<OperateResult<TimeSpan>> ReadTimeAsync(string address)
  {
    OperateResult<TimeSpan> operateResult = await AllenBradleyHelper.ReadTimeAsync((IReadWriteCip) this, address).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteTime(System.String,System.TimeSpan)" />
  public async Task<OperateResult> WriteTimeAsync(string address, TimeSpan time)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteTimeAsync((IReadWriteCip) this, address, time).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteTimeOfDate(System.String,System.TimeSpan)" />
  public async Task<OperateResult> WriteTimeOfDateAsync(string address, TimeSpan timeOfDate)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteTimeOfDateAsync((IReadWriteCip) this, address, timeOfDate).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.PackCommandService(System.Byte[],System.Byte[][])" />
  protected virtual byte[] PackCommandService(byte[] portSlot, params byte[][] cips)
  {
    if (this.MessageRouter != null)
      portSlot = this.MessageRouter.GetRouter();
    return AllenBradleyHelper.PackCommandService(portSlot, cips);
  }

  /// <summary>获取写入数据的长度信息，此处直接返回数组的长度信息</summary>
  /// <param name="address">地址信息</param>
  /// <param name="length">数组长度信息</param>
  /// <returns>实际的写入长度信息</returns>
  protected virtual int GetWriteValueLength(string address, int length) => length;

  /// <inheritdoc />
  public override string ToString() => $"AllenBradleyNet[{this.IpAddress}:{this.Port}]";
}
