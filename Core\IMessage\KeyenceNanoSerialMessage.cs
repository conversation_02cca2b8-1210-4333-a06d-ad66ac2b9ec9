﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.KeyenceNanoSerialMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.IO;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>基恩士上位链路协议的消息</summary>
public class KeyenceNanoSerialMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => -1;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => 0;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckReceiveDataComplete(System.Byte[],System.IO.MemoryStream)" />
  public override bool CheckReceiveDataComplete(byte[] send, MemoryStream ms)
  {
    byte[] array = ms.ToArray();
    return array.Length <= 2 || array[array.Length - 2] == (byte) 13 && array[array.Length - 1] == (byte) 10;
  }
}
