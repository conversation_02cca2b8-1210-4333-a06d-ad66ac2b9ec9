﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttRpcDevice
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// 基于MRPC实现的远程设备访问的接口，实现了和基础PLC一样的访问功能，适用的设备为 <see cref="T:HslCommunication.MQTT.MqttServer" /> 将PLC实际的通信对象注册为 RPC 接口服务。<br />
/// The interface for remote device access based on MRPC implements the same access function as the basic PLC. The applicable device is <see cref="T:HslCommunication.MQTT.MqttServer" /> to register the actual communication object of the PLC as an RPC interface service.
/// </summary>
/// <remarks>
/// 什么时候会用到本设备对象呢？如果你得PLC端口只有一个，只能支持一个连接，但是实际通信的客户端不止一个时，就可以使用本类实现一对多通信。或是你希望在最终通信的客户端和PLC之间做个隔离，并增加安全校验。详细的例子参考API文档。<br />
/// When will this device object be used? If you have only one PLC port and can only support one connection, but there are more than one client for actual communication, you can use this class to implement one-to-many communication.
/// Or you want to isolate the final communication client from the PLC and add security checks. For a detailed example, refer to the API documentation.
/// </remarks>
/// <example>
/// 如何和服务器进行配套调用使用呢？先在服务器端创建服务，并注册Api接口对象
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttRpcDeviceSample.cs" region="Server" title="服务器侧示例" />
/// 然后客户端就支持多连接了，客户端的代码如下
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttRpcDeviceSample.cs" region="Client" title="客户端侧示例" />
/// </example>
public class MqttRpcDevice : MqttSyncClient, IReadWriteDevice, IReadWriteNet
{
  private IByteTransform byteTransform = (IByteTransform) new RegularByteTransform();
  private string deviceTopic;

  /// <summary>
  /// 实例化一个MQTT的同步客户端<br />
  /// Instantiate an MQTT synchronization client
  /// </summary>
  /// <param name="options">连接的参数信息，可以指定IP地址，端口，账户名，密码，客户端ID信息</param>
  /// <param name="topic">设备关联的主题信息</param>
  public MqttRpcDevice(MqttConnectionOptions options, string topic = null)
    : base(options)
  {
    this.deviceTopic = topic;
  }

  /// <summary>
  /// 通过指定的ip地址及端口来实例化一个同步的MQTT客户端<br />
  /// Instantiate a synchronized MQTT client with the specified IP address and port
  /// </summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号信息</param>
  /// <param name="topic">设备关联的主题信息</param>
  public MqttRpcDevice(string ipAddress, int port, string topic = null)
    : base(ipAddress, port)
  {
    this.deviceTopic = topic;
  }

  private string GetTopic(string topic)
  {
    if (string.IsNullOrEmpty(this.deviceTopic))
      return topic;
    return this.deviceTopic.EndsWith("/") ? this.deviceTopic + topic : $"{this.deviceTopic}/{topic}";
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceCommunication.ByteTransform" />
  public IByteTransform ByteTransform
  {
    get => this.byteTransform;
    set => this.byteTransform = value;
  }

  /// <summary>
  /// 获取或设置当前的设备主题信息<br />
  /// Get or set the current device theme information.
  /// </summary>
  public string DeviceTopic
  {
    get => this.deviceTopic;
    set => this.deviceTopic = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public virtual OperateResult<byte[]> Read(string address, ushort length)
  {
    return this.ReadRpc<byte[]>(this.GetTopic("ReadByteArray"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public virtual OperateResult Write(string address, byte[] value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteByteArray"), (object) new
    {
      address = address,
      value = value.ToHexString()
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public virtual OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return this.ReadRpc<bool[]>(this.GetTopic("ReadBoolArray"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String)" />
  [HslMqttApi("ReadBool", "")]
  public virtual OperateResult<bool> ReadBool(string address)
  {
    return this.ReadRpc<bool>(this.GetTopic(nameof (ReadBool)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public virtual OperateResult Write(string address, bool[] value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteBoolArray"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public virtual OperateResult Write(string address, bool value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteBool"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String)" />
  public OperateResult<T> ReadCustomer<T>(string address) where T : IDataTransfer, new()
  {
    return ReadWriteNetHelper.ReadCustomer<T>((IReadWriteNet) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String,``0)" />
  public OperateResult<T> ReadCustomer<T>(string address, T obj) where T : IDataTransfer, new()
  {
    return ReadWriteNetHelper.ReadCustomer<T>((IReadWriteNet) this, address, obj);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteCustomer``1(System.String,``0)" />
  public OperateResult WriteCustomer<T>(string address, T data) where T : IDataTransfer, new()
  {
    return ReadWriteNetHelper.WriteCustomer<T>((IReadWriteNet) this, address, data);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read``1" />
  public virtual OperateResult<T> Read<T>() where T : class, new()
  {
    return HslReflectionHelper.Read<T>((IReadWriteNet) this);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write``1(``0)" />
  public virtual OperateResult Write<T>(T data) where T : class, new()
  {
    return HslReflectionHelper.Write<T>(data, (IReadWriteNet) this);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStruct``1(System.String,System.UInt16)" />
  public virtual OperateResult<T> ReadStruct<T>(string address, ushort length) where T : class, new()
  {
    return ReadWriteNetHelper.ReadStruct<T>((IReadWriteNet) this, address, length, this.ByteTransform);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16(System.String)" />
  [HslMqttApi("ReadInt16", "")]
  public OperateResult<short> ReadInt16(string address)
  {
    return this.ReadRpc<short>(this.GetTopic(nameof (ReadInt16)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt16Array", "")]
  public virtual OperateResult<short[]> ReadInt16(string address, ushort length)
  {
    return this.ReadRpc<short[]>(this.GetTopic("ReadInt16Array"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16(System.String)" />
  [HslMqttApi("ReadUInt16", "")]
  public OperateResult<ushort> ReadUInt16(string address)
  {
    return this.ReadRpc<ushort>(this.GetTopic(nameof (ReadUInt16)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt16Array", "")]
  public virtual OperateResult<ushort[]> ReadUInt16(string address, ushort length)
  {
    return this.ReadRpc<ushort[]>(this.GetTopic("ReadUInt16Array"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32(System.String)" />
  [HslMqttApi("ReadInt32", "")]
  public OperateResult<int> ReadInt32(string address)
  {
    return this.ReadRpc<int>(this.GetTopic(nameof (ReadInt32)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt32Array", "")]
  public virtual OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    return this.ReadRpc<int[]>(this.GetTopic("ReadInt32Array"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32(System.String)" />
  [HslMqttApi("ReadUInt32", "")]
  public OperateResult<uint> ReadUInt32(string address)
  {
    return this.ReadRpc<uint>(this.GetTopic(nameof (ReadUInt32)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt32Array", "")]
  public virtual OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    return this.ReadRpc<uint[]>(this.GetTopic("ReadUInt32Array"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloat(System.String)" />
  [HslMqttApi("ReadFloat", "")]
  public OperateResult<float> ReadFloat(string address)
  {
    return this.ReadRpc<float>(this.GetTopic(nameof (ReadFloat)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloat(System.String,System.UInt16)" />
  [HslMqttApi("ReadFloatArray", "")]
  public virtual OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    return this.ReadRpc<float[]>(this.GetTopic("ReadFloatArray"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64(System.String)" />
  [HslMqttApi("ReadInt64", "")]
  public OperateResult<long> ReadInt64(string address)
  {
    return this.ReadRpc<long>(this.GetTopic(nameof (ReadInt64)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt64Array", "")]
  public virtual OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    return this.ReadRpc<long[]>(this.GetTopic("ReadInt64Array"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64(System.String)" />
  [HslMqttApi("ReadUInt64", "")]
  public OperateResult<ulong> ReadUInt64(string address)
  {
    return this.ReadRpc<ulong>(this.GetTopic(nameof (ReadUInt64)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt64Array", "")]
  public virtual OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    return this.ReadRpc<ulong[]>(this.GetTopic("ReadUInt64Array"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDouble(System.String)" />
  [HslMqttApi("ReadDouble", "")]
  public OperateResult<double> ReadDouble(string address)
  {
    return this.ReadRpc<double>(this.GetTopic(nameof (ReadDouble)), (object) new
    {
      address = address
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDouble(System.String,System.UInt16)" />
  [HslMqttApi("ReadDoubleArray", "")]
  public virtual OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return this.ReadRpc<double[]>(this.GetTopic("ReadDoubleArray"), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadString(System.String,System.UInt16)" />
  [HslMqttApi("ReadString", "")]
  public virtual OperateResult<string> ReadString(string address, ushort length)
  {
    return this.ReadRpc<string>(this.GetTopic(nameof (ReadString)), (object) new
    {
      address = address,
      length = length
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadString(System.String,System.UInt16,System.Text.Encoding)" />
  public virtual OperateResult<string> ReadString(string address, ushort length, Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromBytes<string>(this.Read(address, length), (Func<byte[], string>) (m => this.ByteTransform.TransString(m, 0, m.Length, encoding)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int16[])" />
  [HslMqttApi("WriteInt16Array", "")]
  public virtual OperateResult Write(string address, short[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteInt16Array"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int16)" />
  [HslMqttApi("WriteInt16", "")]
  public virtual OperateResult Write(string address, short value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteInt16"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt16[])" />
  [HslMqttApi("WriteUInt16Array", "")]
  public virtual OperateResult Write(string address, ushort[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteUInt16Array"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt16)" />
  [HslMqttApi("WriteUInt16", "")]
  public virtual OperateResult Write(string address, ushort value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteUInt16"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int32[])" />
  [HslMqttApi("WriteInt32Array", "")]
  public virtual OperateResult Write(string address, int[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteInt32Array"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int32)" />
  [HslMqttApi("WriteInt32", "")]
  public OperateResult Write(string address, int value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteInt32"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt32[])" />
  [HslMqttApi("WriteUInt32Array", "")]
  public virtual OperateResult Write(string address, uint[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteUInt32Array"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt32)" />
  [HslMqttApi("WriteUInt32", "")]
  public OperateResult Write(string address, uint value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteUInt32"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Single[])" />
  [HslMqttApi("WriteFloatArray", "")]
  public virtual OperateResult Write(string address, float[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteFloatArray"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Single)" />
  [HslMqttApi("WriteFloat", "")]
  public OperateResult Write(string address, float value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteFloat"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int64[])" />
  [HslMqttApi("WriteInt64Array", "")]
  public virtual OperateResult Write(string address, long[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteInt64Array"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int64)" />
  [HslMqttApi("WriteInt64", "")]
  public OperateResult Write(string address, long value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteInt64"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt64[])" />
  [HslMqttApi("WriteUInt64Array", "")]
  public virtual OperateResult Write(string address, ulong[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteUInt64Array"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt64)" />
  [HslMqttApi("WriteUInt64", "")]
  public OperateResult Write(string address, ulong value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteUInt64"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double[])" />
  [HslMqttApi("WriteDoubleArray", "")]
  public virtual OperateResult Write(string address, double[] values)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteDoubleArray"), (object) new
    {
      address = address,
      values = values
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double)" />
  [HslMqttApi("WriteDouble", "")]
  public OperateResult Write(string address, double value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteDouble"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String)" />
  [HslMqttApi("WriteString", "")]
  public virtual OperateResult Write(string address, string value)
  {
    return (OperateResult) this.ReadRpc<string>(this.GetTopic("WriteString"), (object) new
    {
      address = address,
      value = value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String,System.Int32)" />
  public virtual OperateResult Write(string address, string value, int length)
  {
    return this.Write(address, value, length, Encoding.ASCII);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String,System.Text.Encoding)" />
  public virtual OperateResult Write(string address, string value, Encoding encoding)
  {
    byte[] numArray = this.ByteTransform.TransByte(value, encoding);
    return this.Write(address, numArray);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String,System.Int32,System.Text.Encoding)" />
  public virtual OperateResult Write(string address, string value, int length, Encoding encoding)
  {
    byte[] length1 = SoftBasic.ArrayExpandToLength<byte>(this.ByteTransform.TransByte(value, encoding), length);
    return this.Write(address, length1);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Boolean,System.Int32,System.Int32)" />
  [HslMqttApi("WaitBool", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    bool waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int16,System.Int32,System.Int32)" />
  [HslMqttApi("WaitInt16", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    short waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt16,System.Int32,System.Int32)" />
  [HslMqttApi("WaitUInt16", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    ushort waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int32,System.Int32,System.Int32)" />
  [HslMqttApi("WaitInt32", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    int waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt32,System.Int32,System.Int32)" />
  [HslMqttApi("WaitUInt32", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    uint waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int64,System.Int32,System.Int32)" />
  [HslMqttApi("WaitInt64", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    long waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt64,System.Int32,System.Int32)" />
  [HslMqttApi("WaitUInt64", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    ulong waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Boolean,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    bool waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int16,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    short waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt16,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    ushort waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int32,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    int waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt32,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    uint waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int64,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    long waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt64,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    ulong waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await this.ReadRpcAsync<byte[]>(this.GetTopic("ReadByteArray"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Byte[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteByteArray"), (object) new
    {
      address = address,
      value = value.ToHexString()
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBoolAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadRpcAsync<bool[]>(this.GetTopic("ReadBoolArray"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBoolAsync(System.String)" />
  public virtual async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<bool> operateResult = await this.ReadRpcAsync<bool>(this.GetTopic("ReadBool"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Boolean[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteBoolArray"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Boolean)" />
  public virtual async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteBool"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomerAsync``1(System.String)" />
  public async Task<OperateResult<T>> ReadCustomerAsync<T>(string address) where T : IDataTransfer, new()
  {
    OperateResult<T> operateResult = await ReadWriteNetHelper.ReadCustomerAsync<T>((IReadWriteNet) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomerAsync``1(System.String,``0)" />
  public async Task<OperateResult<T>> ReadCustomerAsync<T>(string address, T obj) where T : IDataTransfer, new()
  {
    OperateResult<T> operateResult = await ReadWriteNetHelper.ReadCustomerAsync<T>((IReadWriteNet) this, address, obj);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteCustomerAsync``1(System.String,``0)" />
  public async Task<OperateResult> WriteCustomerAsync<T>(string address, T data) where T : IDataTransfer, new()
  {
    OperateResult operateResult = await ReadWriteNetHelper.WriteCustomerAsync<T>((IReadWriteNet) this, address, data);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadAsync``1" />
  public virtual async Task<OperateResult<T>> ReadAsync<T>() where T : class, new()
  {
    OperateResult<T> operateResult = await HslReflectionHelper.ReadAsync<T>((IReadWriteNet) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync``1(``0)" />
  public virtual async Task<OperateResult> WriteAsync<T>(T data) where T : class, new()
  {
    OperateResult operateResult = await HslReflectionHelper.WriteAsync<T>(data, (IReadWriteNet) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStruct``1(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<T>> ReadStructAsync<T>(string address, ushort length) where T : class, new()
  {
    OperateResult<T> operateResult = await ReadWriteNetHelper.ReadStructAsync<T>((IReadWriteNet) this, address, length, this.ByteTransform);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16Async(System.String)" />
  public async Task<OperateResult<short>> ReadInt16Async(string address)
  {
    OperateResult<short> operateResult = await this.ReadRpcAsync<short>(this.GetTopic("ReadInt16"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<short[]>> ReadInt16Async(string address, ushort length)
  {
    OperateResult<short[]> operateResult = await this.ReadRpcAsync<short[]>(this.GetTopic("ReadInt16Array"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16Async(System.String)" />
  public async Task<OperateResult<ushort>> ReadUInt16Async(string address)
  {
    OperateResult<ushort> operateResult = await this.ReadRpcAsync<ushort>(this.GetTopic("ReadUInt16"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<ushort[]>> ReadUInt16Async(string address, ushort length)
  {
    OperateResult<ushort[]> operateResult = await this.ReadRpcAsync<ushort[]>(this.GetTopic("ReadUInt16Array"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32Async(System.String)" />
  public async Task<OperateResult<int>> ReadInt32Async(string address)
  {
    OperateResult<int> operateResult = await this.ReadRpcAsync<int>(this.GetTopic("ReadInt32"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    OperateResult<int[]> operateResult = await this.ReadRpcAsync<int[]>(this.GetTopic("ReadInt32Array"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32Async(System.String)" />
  public async Task<OperateResult<uint>> ReadUInt32Async(string address)
  {
    OperateResult<uint> operateResult = await this.ReadRpcAsync<uint>(this.GetTopic("ReadUInt32"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    OperateResult<uint[]> operateResult = await this.ReadRpcAsync<uint[]>(this.GetTopic("ReadUInt32Array"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloatAsync(System.String)" />
  public async Task<OperateResult<float>> ReadFloatAsync(string address)
  {
    OperateResult<float> operateResult = await this.ReadRpcAsync<float>(this.GetTopic("ReadFloat"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloatAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    OperateResult<float[]> operateResult = await this.ReadRpcAsync<float[]>(this.GetTopic("ReadFloatArray"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64Async(System.String)" />
  public async Task<OperateResult<long>> ReadInt64Async(string address)
  {
    OperateResult<long> operateResult = await this.ReadRpcAsync<long>(this.GetTopic("ReadInt64"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    OperateResult<long[]> operateResult = await this.ReadRpcAsync<long[]>(this.GetTopic("ReadInt64Array"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64Async(System.String)" />
  public async Task<OperateResult<ulong>> ReadUInt64Async(string address)
  {
    OperateResult<ulong> operateResult = await this.ReadRpcAsync<ulong>(this.GetTopic("ReadUInt64"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    OperateResult<ulong[]> operateResult = await this.ReadRpcAsync<ulong[]>(this.GetTopic("ReadUInt64Array"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDoubleAsync(System.String)" />
  public async Task<OperateResult<double>> ReadDoubleAsync(string address)
  {
    OperateResult<double> operateResult = await this.ReadRpcAsync<double>(this.GetTopic("ReadDouble"), (object) new
    {
      address = address
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDoubleAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<double[]> operateResult = await this.ReadRpcAsync<double[]>(this.GetTopic("ReadDoubleArray"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStringAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<string>> ReadStringAsync(string address, ushort length)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("ReadString"), (object) new
    {
      address = address,
      length = length
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStringAsync(System.String,System.UInt16,System.Text.Encoding)" />
  public virtual async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<string>(result, (Func<byte[], string>) (m => this.ByteTransform.TransString(m, 0, m.Length, encoding)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int16[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, short[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteInt16Array"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int16)" />
  public virtual async Task<OperateResult> WriteAsync(string address, short value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteInt16"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt16[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, ushort[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteUInt16Array"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult> WriteAsync(string address, ushort value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteUInt16"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int32[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteInt32Array"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int32)" />
  public async Task<OperateResult> WriteAsync(string address, int value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteInt32"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt32[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteUInt32Array"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt32)" />
  public async Task<OperateResult> WriteAsync(string address, uint value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteUInt32"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Single[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteFloatArray"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Single)" />
  public async Task<OperateResult> WriteAsync(string address, float value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteFloat"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int64[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, long[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteInt64Array"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int64)" />
  public async Task<OperateResult> WriteAsync(string address, long value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteInt64"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt64[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, ulong[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteUInt64Array"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt64)" />
  public async Task<OperateResult> WriteAsync(string address, ulong value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteUInt64"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Double[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteDoubleArray"), (object) new
    {
      address = address,
      values = values
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Double)" />
  public async Task<OperateResult> WriteAsync(string address, double value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteDouble"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String)" />
  public virtual async Task<OperateResult> WriteAsync(string address, string value)
  {
    OperateResult<string> operateResult = await this.ReadRpcAsync<string>(this.GetTopic("WriteString"), (object) new
    {
      address = address,
      value = value
    });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String,System.Text.Encoding)" />
  public virtual async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    byte[] temp = this.ByteTransform.TransByte(value, encoding);
    OperateResult operateResult = await this.WriteAsync(address, temp);
    temp = (byte[]) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String,System.Int32)" />
  public virtual async Task<OperateResult> WriteAsync(string address, string value, int length)
  {
    OperateResult operateResult = await this.WriteAsync(address, value, length, Encoding.ASCII);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String,System.Int32,System.Text.Encoding)" />
  public virtual async Task<OperateResult> WriteAsync(
    string address,
    string value,
    int length,
    Encoding encoding)
  {
    byte[] temp = this.ByteTransform.TransByte(value, encoding);
    temp = SoftBasic.ArrayExpandToLength<byte>(temp, length);
    OperateResult operateResult = await this.WriteAsync(address, temp);
    temp = (byte[]) null;
    return operateResult;
  }
}
