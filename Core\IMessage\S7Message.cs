﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.S7Message
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>西门子S7协议的消息解析规则</summary>
public class S7Message : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 4;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes != null && this.HeadBytes[0] == (byte) 3 && this.HeadBytes[1] == (byte) 0;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    byte[] headBytes = this.HeadBytes;
    if (headBytes == null || headBytes.Length < 4)
      return 0;
    int lengthByHeadBytes = (int) this.HeadBytes[2] * 256 /*0x0100*/ + (int) this.HeadBytes[3] - 4;
    if (lengthByHeadBytes < 0)
      lengthByHeadBytes = 0;
    return lengthByHeadBytes;
  }

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    if (receive == null || receive.Length < 14 || send == null || send.Length < 14 || receive[5] != (byte) 240 /*0xF0*/)
      return base.CheckMessageMatch(send, receive);
    return (int) send[11] == (int) receive[11] && (int) send[12] == (int) receive[12] ? 1 : -1;
  }
}
