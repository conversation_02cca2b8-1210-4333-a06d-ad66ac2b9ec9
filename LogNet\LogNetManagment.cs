﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.LogNetManagment
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>
/// 日志类的管理器，提供了基本的功能代码。<br />
/// The manager of the log class provides the basic function code.
/// </summary>
public class LogNetManagment
{
  /// <summary>日志文件的头标志</summary>
  public static string LogFileHeadString = "Logs_";

  internal static string GetDegreeDescription(HslMessageDegree degree)
  {
    switch (degree)
    {
      case HslMessageDegree.None:
        return StringResources.Language.LogNetAbandon;
      case HslMessageDegree.FATAL:
        return StringResources.Language.LogNetFatal;
      case HslMessageDegree.ERROR:
        return StringResources.Language.LogNetError;
      case HslMessageDegree.WARN:
        return StringResources.Language.LogNetWarn;
      case HslMessageDegree.INFO:
        return StringResources.Language.LogNetInfo;
      case HslMessageDegree.DEBUG:
        return StringResources.Language.LogNetDebug;
      default:
        return StringResources.Language.LogNetAbandon;
    }
  }

  /// <summary>
  /// 公开的一个静态变量，允许随意的设置<br />
  /// Public static variable, allowing arbitrary setting
  /// </summary>
  public static ILogNet LogNet { get; set; }

  /// <summary>
  /// 通过异常文本格式化成字符串用于保存或发送<br />
  /// Formatted as a string with exception text for saving or sending
  /// </summary>
  /// <param name="text">文本消息</param>
  /// <param name="ex">异常</param>
  /// <returns>异常最终信息</returns>
  public static string GetSaveStringFromException(string text, Exception ex)
  {
    StringBuilder stringBuilder = new StringBuilder(text);
    if (ex != null)
    {
      if (!string.IsNullOrEmpty(text))
        stringBuilder.Append(" : ");
      try
      {
        stringBuilder.Append(StringResources.Language.ExceptionMessage);
        stringBuilder.Append(ex.Message);
        stringBuilder.Append(Environment.NewLine);
        stringBuilder.Append(StringResources.Language.ExceptionSource);
        stringBuilder.Append(ex.Source);
        stringBuilder.Append(Environment.NewLine);
        stringBuilder.Append(StringResources.Language.ExceptionStackTrace);
        stringBuilder.Append(ex.StackTrace);
        stringBuilder.Append(Environment.NewLine);
        stringBuilder.Append(StringResources.Language.ExceptionType);
        stringBuilder.Append(ex.GetType().ToString());
        stringBuilder.Append(Environment.NewLine);
        stringBuilder.Append(StringResources.Language.ExceptionTargetSite);
        stringBuilder.Append(ex.TargetSite?.ToString());
      }
      catch
      {
      }
      stringBuilder.Append(Environment.NewLine);
      stringBuilder.Append("\u0002/=================================================[    Exception    ]================================================/");
    }
    try
    {
      return stringBuilder.ToString();
    }
    catch
    {
      return string.IsNullOrEmpty(text) ? ex.Message : $"{text}:{ex.Message}";
    }
  }
}
