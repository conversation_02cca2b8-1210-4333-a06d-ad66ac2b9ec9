﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.YASKAWA.MemobusUdpNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.YASKAWA.Helper;

#nullable disable
namespace HslCommunication.Profinet.YASKAWA;

/// <inheritdoc cref="T:HslCommunication.Profinet.YASKAWA.MemobusTcpNet" />
public class MemobusUdpNet : MemobusTcpNet, IMemobus, IReadWriteDevice, IReadWriteNet
{
  /// <summary>
  /// 实例化一个Memobus-Tcp协议的客户端对象<br />
  /// Instantiate a client object of the Memobus-Tcp protocol
  /// </summary>
  public MemobusUdpNet() => this.CommunicationPipe = (CommunicationPipe) new PipeUdpNet();

  /// <summary>
  /// 指定服务器地址，端口号，客户端自己的站号来初始化<br />
  /// Specify the server address, port number, and client's own station number to initialize
  /// </summary>
  /// <param name="ipAddress">服务器的Ip地址</param>
  /// <param name="port">服务器的端口号</param>
  public MemobusUdpNet(string ipAddress, int port = 502)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  public override string ToString() => $"MemobusUdpNet[{this.IpAddress}:{this.Port}]";
}
