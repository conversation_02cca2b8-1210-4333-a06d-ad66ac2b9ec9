﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronHostLinkOverTcp
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Omron.Helper;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// 欧姆龙的HostLink协议的实现，基于Tcp实现，地址支持示例 DM区:D100; CIO区:C100; Work区:W100; Holding区:H100; Auxiliary区: A100<br />
/// Implementation of Omron's HostLink protocol, based on tcp protocol, address support example DM area: D100; CIO area: C100; Work area: W100; Holding area: H100; Auxiliary area: A100
/// </summary>
/// <remarks>
/// 感谢 深圳～拾忆 的测试，地址可以携带站号信息，例如 s=2;D100
/// <br />
/// <note type="important">
/// 如果发现串口线和usb同时打开才能通信的情况，需要按照如下的操作：<br />
/// 串口线不是标准的串口线，电脑的串口线的235引脚分别接PLC的329引脚，45线短接，就可以通讯，感谢 深圳-小君(QQ932507362)提供的解决方案。
/// </note>
/// </remarks>
public class OmronHostLinkOverTcp : DeviceTcpNet, IHostLink, IReadWriteDevice, IReadWriteNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.#ctor" />
  public OmronHostLinkOverTcp()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronCipNet.#ctor(System.String,System.Int32)" />
  public OmronHostLinkOverTcp(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return OmronHostLinkHelper.ResponseValidAnalysis(send, response);
  }

  /// <summary>
  /// Specifies whether or not there are network relays. Set “80” (ASCII: 38,30)
  /// when sending an FINS command to a CPU Unit on a network.Set “00” (ASCII: 30,30)
  /// when sending to a CPU Unit connected directly to the host computer.
  /// </summary>
  public byte ICF { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronFinsNet.DA2" />
  public byte DA2 { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronFinsNet.SA2" />
  public byte SA2 { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronFinsNet.SID" />
  public byte SID { get; set; } = 0;

  /// <summary>
  /// The response wait time sets the time from when the CPU Unit receives a command block until it starts
  /// to return a response.It can be set from 0 to F in hexadecimal, in units of 10 ms.
  /// If F(15) is set, the response will begin to be returned 150 ms (15 × 10 ms) after the command block was received.
  /// </summary>
  public byte ResponseWaitTime { get; set; } = 48 /*0x30*/;

  /// <summary>
  /// PLC设备的站号信息<br />
  /// PLC device station number information
  /// </summary>
  public byte UnitNumber { get; set; }

  /// <summary>
  /// 进行字读取的时候对于超长的情况按照本属性进行切割，默认260。<br />
  /// When reading words, it is cut according to this attribute for the case of overlength. The default is 260.
  /// </summary>
  public int ReadSplits { get; set; } = 260;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.Helper.IOmronFins.PlcType" />
  public OmronPlcType PlcType { get; set; } = OmronPlcType.CSCJ;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return OmronHostLinkHelper.Read((IHostLink) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return OmronHostLinkHelper.Write((IHostLink) this, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkHelper.Read(HslCommunication.Profinet.Omron.Helper.IHostLink,System.String[])" />
  public OperateResult<byte[]> Read(string[] address)
  {
    return OmronHostLinkHelper.Read((IHostLink) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await OmronHostLinkHelper.ReadAsync((IHostLink) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await OmronHostLinkHelper.WriteAsync((IHostLink) this, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkHelper.Read(HslCommunication.Profinet.Omron.Helper.IHostLink,System.String[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address)
  {
    OperateResult<byte[]> operateResult = await OmronHostLinkHelper.ReadAsync((IHostLink) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return OmronHostLinkHelper.ReadBool((IHostLink) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    return OmronHostLinkHelper.Write((IHostLink) this, address, values);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await OmronHostLinkHelper.ReadBoolAsync((IHostLink) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] values)
  {
    OperateResult operateResult = await OmronHostLinkHelper.WriteAsync((IHostLink) this, address, values);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronHostLinkOverTcp[{this.IpAddress}:{this.Port}]";
}
