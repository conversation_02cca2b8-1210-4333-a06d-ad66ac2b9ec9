﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.NetSimplifyClient
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Enthernet;

/// <summary>同步访问数据的客户端类，用于向服务器请求一些确定的数据信息</summary>
/// <remarks>
/// 详细的使用说明，请参照博客<a href="http://www.cnblogs.com/dathlin/p/7697782.html">http://www.cnblogs.com/dathlin/p/7697782.html</a>
/// </remarks>
/// <example>
/// 此处贴上了Demo项目的服务器配置的示例代码
/// <code lang="cs" source="TestProject\HslCommunicationDemo\Hsl\FormSimplifyNet.cs" region="FormSimplifyNet" title="FormSimplifyNet示例" />
/// </example>
public class NetSimplifyClient : NetworkDoubleBase
{
  /// <summary>实例化一个客户端的对象，用于和服务器通信</summary>
  /// <param name="ipAddress">服务器的ip地址</param>
  /// <param name="port">服务器的端口号</param>
  public NetSimplifyClient(string ipAddress, int port)
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <summary>实例化一个客户端的对象，用于和服务器通信</summary>
  /// <param name="ipAddress">服务器的ip地址</param>
  /// <param name="port">服务器的端口号</param>
  public NetSimplifyClient(IPAddress ipAddress, int port)
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.IpAddress = ipAddress.ToString();
    this.Port = port;
  }

  /// <summary>实例化一个客户端对象，需要手动指定Ip地址和端口</summary>
  public NetSimplifyClient() => this.ByteTransform = (IByteTransform) new RegularByteTransform();

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new HslMessage();

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect(Socket socket)
  {
    return this.isUseAccountCertificate ? this.AccountCertificate(socket) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync(Socket socket)
  {
    if (!this.isUseAccountCertificate)
      return OperateResult.CreateSuccessResult();
    OperateResult operateResult = await this.AccountCertificateAsync(socket);
    return operateResult;
  }

  /// <summary>客户端向服务器进行请求，请求字符串数据，忽略了自定义消息反馈</summary>
  /// <param name="customer">用户的指令头</param>
  /// <param name="send">发送数据</param>
  /// <returns>带返回消息的结果对象</returns>
  public OperateResult<string> ReadFromServer(NetHandle customer, string send)
  {
    OperateResult<byte[]> result = this.ReadFromServerBase(HslProtocol.CommandBytes((int) customer, this.Token, send));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result) : OperateResult.CreateSuccessResult<string>(Encoding.Unicode.GetString(result.Content));
  }

  /// <summary>客户端向服务器进行请求，请求字符串数组，忽略了自定义消息反馈</summary>
  /// <param name="customer">用户的指令头</param>
  /// <param name="send">发送数据</param>
  /// <returns>带返回消息的结果对象</returns>
  public OperateResult<string[]> ReadFromServer(NetHandle customer, string[] send)
  {
    OperateResult<byte[]> result = this.ReadFromServerBase(HslProtocol.CommandBytes((int) customer, this.Token, send));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string[]>((OperateResult) result) : OperateResult.CreateSuccessResult<string[]>(HslProtocol.UnPackStringArrayFromByte(result.Content));
  }

  /// <summary>客户端向服务器进行请求，请求字节数据</summary>
  /// <param name="customer">用户的指令头</param>
  /// <param name="send">发送的字节内容</param>
  /// <returns>带返回消息的结果对象</returns>
  public OperateResult<byte[]> ReadFromServer(NetHandle customer, byte[] send)
  {
    return this.ReadFromServerBase(HslProtocol.CommandBytes((int) customer, this.Token, send));
  }

  /// <summary>客户端向服务器进行请求，请求字符串数据，并返回状态信息</summary>
  /// <param name="customer">用户的指令头</param>
  /// <param name="send">发送数据</param>
  /// <returns>带返回消息的结果对象</returns>
  public OperateResult<NetHandle, string> ReadCustomerFromServer(NetHandle customer, string send)
  {
    OperateResult<NetHandle, byte[]> result = this.ReadCustomerFromServerBase(HslProtocol.CommandBytes((int) customer, this.Token, send));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<NetHandle, string>((OperateResult) result) : OperateResult.CreateSuccessResult<NetHandle, string>(result.Content1, Encoding.Unicode.GetString(result.Content2));
  }

  /// <summary>客户端向服务器进行请求，请求字符串数据，并返回状态信息</summary>
  /// <param name="customer">用户的指令头</param>
  /// <param name="send">发送数据</param>
  /// <returns>带返回消息的结果对象</returns>
  public OperateResult<NetHandle, string[]> ReadCustomerFromServer(
    NetHandle customer,
    string[] send)
  {
    OperateResult<NetHandle, byte[]> result = this.ReadCustomerFromServerBase(HslProtocol.CommandBytes((int) customer, this.Token, send));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<NetHandle, string[]>((OperateResult) result) : OperateResult.CreateSuccessResult<NetHandle, string[]>(result.Content1, HslProtocol.UnPackStringArrayFromByte(result.Content2));
  }

  /// <summary>客户端向服务器进行请求，请求字符串数据，并返回状态信息</summary>
  /// <param name="customer">用户的指令头</param>
  /// <param name="send">发送数据</param>
  /// <returns>带返回消息的结果对象</returns>
  public OperateResult<NetHandle, byte[]> ReadCustomerFromServer(NetHandle customer, byte[] send)
  {
    return this.ReadCustomerFromServerBase(HslProtocol.CommandBytes((int) customer, this.Token, send));
  }

  /// <summary>需要发送的底层数据</summary>
  /// <param name="send">需要发送的底层数据</param>
  /// <returns>带返回消息的结果对象</returns>
  private OperateResult<byte[]> ReadFromServerBase(byte[] send)
  {
    OperateResult<NetHandle, byte[]> result = this.ReadCustomerFromServerBase(send);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(result.Content2);
  }

  /// <summary>需要发送的底层数据</summary>
  /// <param name="send">需要发送的底层数据</param>
  /// <returns>带返回消息的结果对象</returns>
  private OperateResult<NetHandle, byte[]> ReadCustomerFromServerBase(byte[] send)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(send);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<NetHandle, byte[]>((OperateResult) result) : HslProtocol.ExtractHslData(result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadFromServer(HslCommunication.NetHandle,System.String)" />
  public async Task<OperateResult<string>> ReadFromServerAsync(NetHandle customer, string send)
  {
    OperateResult<byte[]> read = await this.ReadFromServerBaseAsync(HslProtocol.CommandBytes((int) customer, this.Token, send));
    OperateResult<string> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<string>(Encoding.Unicode.GetString(read.Content)) : OperateResult.CreateFailedResult<string>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadFromServer(HslCommunication.NetHandle,System.String[])" />
  public async Task<OperateResult<string[]>> ReadFromServerAsync(NetHandle customer, string[] send)
  {
    OperateResult<byte[]> read = await this.ReadFromServerBaseAsync(HslProtocol.CommandBytes((int) customer, this.Token, send));
    OperateResult<string[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<string[]>(HslProtocol.UnPackStringArrayFromByte(read.Content)) : OperateResult.CreateFailedResult<string[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadFromServer(HslCommunication.NetHandle,System.Byte[])" />
  public async Task<OperateResult<byte[]>> ReadFromServerAsync(NetHandle customer, byte[] send)
  {
    OperateResult<byte[]> operateResult = await this.ReadFromServerBaseAsync(HslProtocol.CommandBytes((int) customer, this.Token, send));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadCustomerFromServer(HslCommunication.NetHandle,System.String)" />
  public async Task<OperateResult<NetHandle, string>> ReadCustomerFromServerAsync(
    NetHandle customer,
    string send)
  {
    OperateResult<NetHandle, byte[]> read = await this.ReadCustomerFromServerBaseAsync(HslProtocol.CommandBytes((int) customer, this.Token, send));
    OperateResult<NetHandle, string> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<NetHandle, string>(read.Content1, Encoding.Unicode.GetString(read.Content2)) : OperateResult.CreateFailedResult<NetHandle, string>((OperateResult) read);
    read = (OperateResult<NetHandle, byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadCustomerFromServer(HslCommunication.NetHandle,System.String[])" />
  public async Task<OperateResult<NetHandle, string[]>> ReadCustomerFromServerAsync(
    NetHandle customer,
    string[] send)
  {
    OperateResult<NetHandle, byte[]> read = await this.ReadCustomerFromServerBaseAsync(HslProtocol.CommandBytes((int) customer, this.Token, send));
    OperateResult<NetHandle, string[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<NetHandle, string[]>(read.Content1, HslProtocol.UnPackStringArrayFromByte(read.Content2)) : OperateResult.CreateFailedResult<NetHandle, string[]>((OperateResult) read);
    read = (OperateResult<NetHandle, byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadCustomerFromServer(HslCommunication.NetHandle,System.Byte[])" />
  public async Task<OperateResult<NetHandle, byte[]>> ReadCustomerFromServerAsync(
    NetHandle customer,
    byte[] send)
  {
    OperateResult<NetHandle, byte[]> operateResult = await this.ReadCustomerFromServerBaseAsync(HslProtocol.CommandBytes((int) customer, this.Token, send));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadFromServerBase(System.Byte[])" />
  private async Task<OperateResult<byte[]>> ReadFromServerBaseAsync(byte[] send)
  {
    OperateResult<NetHandle, byte[]> read = await this.ReadCustomerFromServerBaseAsync(send);
    OperateResult<byte[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content2) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
    read = (OperateResult<NetHandle, byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Enthernet.NetSimplifyClient.ReadCustomerFromServerBase(System.Byte[])" />
  private async Task<OperateResult<NetHandle, byte[]>> ReadCustomerFromServerBaseAsync(byte[] send)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(send);
    OperateResult<NetHandle, byte[]> operateResult = read.IsSuccess ? HslProtocol.ExtractHslData(read.Content) : OperateResult.CreateFailedResult<NetHandle, byte[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"NetSimplifyClient[{this.IpAddress}:{this.Port}]";
}
