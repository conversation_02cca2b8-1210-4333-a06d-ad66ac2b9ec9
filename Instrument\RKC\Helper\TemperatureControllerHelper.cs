﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.RKC.Helper.TemperatureControllerHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.RKC.Helper;

/// <summary>
/// RKC温度控制器的辅助类信息，提供了报文的生成，读写的实现的方法<br />
/// Auxiliary information of the RKC temperature controller provides a method for message generation and reading and writing
/// </summary>
public class TemperatureControllerHelper
{
  /// <summary>构建读取的报文命令，需要指定站号信息，数据地址</summary>
  /// <param name="station">站号信息</param>
  /// <param name="address">数据的地址</param>
  /// <returns>是否成功</returns>
  public static OperateResult<byte[]> BuildReadCommand(byte station, string address)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    if (station >= (byte) 100)
      return new OperateResult<byte[]>("Station must less than 100");
    try
    {
      byte[] numArray = new byte[4 + address.Length];
      numArray[0] = (byte) 4;
      Encoding.ASCII.GetBytes(station.ToString("D2")).CopyTo((Array) numArray, 1);
      Encoding.ASCII.GetBytes(address).CopyTo((Array) numArray, 3);
      numArray[numArray.Length - 1] = (byte) 5;
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>构建一个写入的报文信息</summary>
  /// <param name="station">站号信息</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">等待写入的值</param>
  /// <returns>是否成功的结果报文</returns>
  public static OperateResult<byte[]> BuildWriteCommand(byte station, string address, double value)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    if (station >= (byte) 100)
      return new OperateResult<byte[]>("Station must less than 100");
    if (value.ToString().Length > 6)
      return new OperateResult<byte[]>("The data consists of up to 6 characters");
    try
    {
      List<byte> byteList = new List<byte>(20);
      byteList.Add((byte) 4);
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(station.ToString("D2")));
      byteList.Add((byte) 2);
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(address));
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(value.ToString()));
      byteList.Add((byte) 3);
      int num = (int) byteList[4];
      for (int index = 5; index < byteList.Count; ++index)
        num ^= (int) byteList[index];
      byteList.Add((byte) num);
      return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>
  /// 从RKC设备读取Double类型的数据信息，地址示例：M1,M2,M3,AA,AB,B1,ER等，更详细的地址及具体含义需要参考API文档<br />
  /// Read Double type data information from RKC device. Examples of addresses: M1, M2, M3, AA, AB, B1, ER, etc.
  /// For more detailed addresses and specific meanings, please refer to the API documentation
  /// </summary>
  /// <param name="device">设备通信对象</param>
  /// <param name="station">表号信息，也叫站号信息</param>
  /// <param name="address">数据地址信息，地址示例：M1,M2,M3,AA,AB,B1,ER等</param>
  /// <returns>结果数据对象信息</returns>
  public static OperateResult<double> ReadDouble(
    IReadWriteDevice device,
    byte station,
    string address)
  {
    OperateResult<byte[]> result1 = TemperatureControllerHelper.BuildReadCommand(station, address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) result1);
    OperateResult<byte[]> result2 = device.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) result2);
    if (result2.Content[0] != (byte) 2)
      return new OperateResult<double>("STX check failed: " + result2.Content.ToHexString(' '));
    try
    {
      return OperateResult.CreateSuccessResult<double>(double.Parse(Encoding.ASCII.GetString(result2.Content, 3, result2.Content.Length - 5)));
    }
    catch (Exception ex)
    {
      return new OperateResult<double>($"{ex.Message}{Environment.NewLine}Source: {result2.Content.ToHexString(' ')}");
    }
  }

  /// <summary>
  /// 将Double类型的数据写入到RKC设备中去，地址示例：M1,M2,M3,AA,AB,B1,ER等，更详细的地址及具体含义需要参考API文档<br />
  /// Write Double type data to the RKC device. Examples of addresses: M1, M2, M3, AA, AB, B1, ER, etc.
  /// For more detailed addresses and specific meanings, please refer to the API documentation
  /// </summary>
  /// <param name="device">设备通信对象</param>
  /// <param name="station">表号信息，也叫站号信息</param>
  /// <param name="address">数据的地址信息，地址示例：M1,M2,M3,AA,AB,B1,ER等</param>
  /// <param name="value">等待写入的值</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteDevice device,
    byte station,
    string address,
    double value)
  {
    OperateResult<byte[]> operateResult1 = TemperatureControllerHelper.BuildWriteCommand(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = device.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    return operateResult2.Content[0] != (byte) 6 ? (OperateResult) new OperateResult<double>("ACK check failed: " + operateResult2.Content.ToHexString(' ')) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.RKC.Helper.TemperatureControllerHelper.ReadDouble(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String)" />
  public static async Task<OperateResult<double>> ReadDoubleAsync(
    IReadWriteDevice device,
    byte station,
    string address)
  {
    OperateResult<byte[]> build = TemperatureControllerHelper.BuildReadCommand(station, address);
    if (!build.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) build);
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(build.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) read);
    if (read.Content[0] != (byte) 2)
      return new OperateResult<double>("STX check failed: " + read.Content.ToHexString(' '));
    try
    {
      return OperateResult.CreateSuccessResult<double>(double.Parse(Encoding.ASCII.GetString(read.Content, 3, read.Content.Length - 5)));
    }
    catch (Exception ex)
    {
      return new OperateResult<double>($"{ex.Message}{Environment.NewLine}Source: {read.Content.ToHexString(' ')}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.RKC.Helper.TemperatureControllerHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Double)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    double value)
  {
    OperateResult<byte[]> build = TemperatureControllerHelper.BuildWriteCommand(station, address, value);
    if (!build.IsSuccess)
      return (OperateResult) build;
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(build.Content);
    return read.IsSuccess ? (read.Content[0] == (byte) 6 ? OperateResult.CreateSuccessResult() : (OperateResult) new OperateResult<double>("ACK check failed: " + read.Content.ToHexString(' '))) : (OperateResult) read;
  }
}
