﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.WebSocket.WebSocketMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Text;

#nullable disable
namespace HslCommunication.WebSocket;

/// <summary>
/// websocket 协议下的单个消息的数据对象<br />
/// Data object for a single message under the websocket protocol
/// </summary>
public class WebSocketMessage
{
  /// <summary>
  /// 是否存在掩码<br />
  /// Whether a mask exists
  /// </summary>
  public bool HasMask { get; set; }

  /// <summary>
  /// 当前的websocket的操作码<br />
  /// The current websocket opcode
  /// </summary>
  public int OpCode { get; set; }

  /// <summary>负载数据</summary>
  public byte[] Payload { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"OpCode[{this.OpCode}] HasMask[{this.HasMask}] Payload: {Encoding.UTF8.GetString(this.Payload)}";
  }
}
