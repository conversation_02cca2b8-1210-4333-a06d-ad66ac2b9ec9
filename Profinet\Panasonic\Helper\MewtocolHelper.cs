﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Panasonic.Helper;

/// <summary>Mewtocol协议的辅助类信息</summary>
public class MewtocolHelper
{
  private static bool CheckBoolOnWordAddress(string address)
  {
    return Regex.IsMatch(address, "^(s=[0-9]+;)?(D|LD|DT|F)[0-9]+\\.[0-9]+$", RegexOptions.IgnoreCase);
  }

  /// <summary>
  /// 读取单个的地址信息的bool值，地址举例：SR0.0  X0.0  Y0.0  R0.0  L0.0<br />
  /// Read the bool value of a single address, for example: SR0.0 X0.0 Y0.0 R0.0 L0.0
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">起始地址</param>
  /// <returns>读取结果对象</returns>
  public static OperateResult<bool> ReadBool(IReadWriteDevice plc, byte station, string address)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
      return ByteTransformHelper.GetResultFromArray<bool>(HslHelper.ReadBool((IReadWriteNet) plc, address, (ushort) 1));
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> result1 = PanasonicHelper.BuildReadOneCoil(station, address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) result1);
    OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) result2) : ByteTransformHelper.GetResultFromArray<bool>(PanasonicHelper.ExtraActualBool(result2.Content));
  }

  /// <summary>
  /// 批量读取松下PLC的位数据，按照字为单位，地址为 X0,X10,Y10，读取的长度为16的倍数<br />
  /// Read the bit data of Panasonic PLC in batches, the unit is word, the address is X0, X10, Y10, and the read length is a multiple of 16
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">起始地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>读取结果对象</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
      return HslHelper.ReadBool((IReadWriteNet) plc, address, length);
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<string, int> result1 = PanasonicHelper.AnalysisAddress(address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<List<byte[]>> result2 = PanasonicHelper.BuildReadCommand(station, address, length, true);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result2.Content.Count; ++index)
    {
      OperateResult<byte[]> result3 = plc.ReadFromCoreServer(result2.Content[index]);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result3);
      OperateResult<byte[]> result4 = PanasonicHelper.ExtraActualData(result3.Content);
      if (!result4.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result4);
      byteList.AddRange((IEnumerable<byte>) result4.Content);
    }
    return OperateResult.CreateSuccessResult<bool[]>(byteList.ToArray().ToBoolArray().SelectMiddle<bool>(result1.Content2 % 16 /*0x10*/, (int) length));
  }

  /// <summary>
  /// 批量读取松下PLC的位数据，传入一个读取的地址列表，地址支持X,Y,R,T,C,L, 举例：R1.0, X2.0, R3.A<br />
  /// Batch read the bit data of Panasonic PLC, pass in a read address list, the address supports X, Y, R, T, C, L, for example: R1.0, X2.0, R3.A
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">等待读取的地址列表，数组长度不限制</param>
  /// <returns>读取结果对象</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice plc,
    byte station,
    string[] address)
  {
    OperateResult<List<byte[]>> result1 = PanasonicHelper.BuildReadCoils(station, address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult<bool[]> result3 = PanasonicHelper.ExtraActualBool(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result3);
      boolList.AddRange((IEnumerable<bool>) result3.Content);
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <summary>
  /// 往指定的地址写入bool数据，地址举例：SR0.0  X0.0  Y0.0  R0.0  L0.0<br />
  /// Write bool data to the specified address. Example address: SR0.0 X0.0 Y0.0 R0.0 L0.0
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">起始地址</param>
  /// <param name="value">数据值信息</param>
  /// <returns>返回是否成功的结果对象</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    byte station,
    string address,
    bool value)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
      return HslHelper.WriteBool((IReadWriteNet) plc, address, new bool[1]
      {
        value
      });
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> operateResult1 = PanasonicHelper.BuildWriteOneCoil(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) PanasonicHelper.ExtraActualData(operateResult2.Content);
  }

  /// <summary>
  /// 往指定的地址写入 <see cref="T:System.Boolean" /> 数组，地址举例 X0.0  Y0.0  R0.0  L0.0，
  /// 起始的位地址必须为16的倍数，写入的 <see cref="T:System.Boolean" /> 数组长度也为16的倍数。<br />
  /// Write the <see cref="T:System.Boolean" /> array to the specified address, address example: SR0.0 X0.0 Y0.0 R0.0 L0.0,
  /// the starting bit address must be a multiple of 16. <see cref="T:System.Boolean" /> The length of the array is also a multiple of 16. <br />
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">起始地址</param>
  /// <param name="values">数据值信息</param>
  /// <returns>返回是否成功的结果对象</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    byte station,
    string address,
    bool[] values)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
      return HslHelper.WriteBool((IReadWriteNet) plc, address, values);
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<string, int> result = PanasonicHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    if (result.Content2 % 16 /*0x10*/ != 0)
      return new OperateResult(StringResources.Language.PanasonicAddressBitStartMulti16);
    if (values.Length % 16 /*0x10*/ != 0)
      return new OperateResult(StringResources.Language.PanasonicBoolLengthMulti16);
    byte[] values1 = SoftBasic.BoolArrayToByte(values);
    OperateResult<byte[]> operateResult1 = PanasonicHelper.BuildWriteCommand(station, address, values1);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) PanasonicHelper.ExtraActualData(operateResult2.Content);
  }

  /// <summary>
  /// 将Bool数组值写入到指定的离散地址里，一个地址对应一个bool值，地址数组长度和值数组长度必须相等，地址支持X,Y,R,T,C,L, 举例：R1.0, X2.0, R3.A<br />
  /// Write the Bool array value to the specified discrete address, one address corresponds to one bool value,
  /// the length of the address array and the length of the value array must be equal, the address supports X, Y, R, T, C, L, for example: R1.0, X2.0, R3.A
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">离散的地址列表</param>
  /// <param name="value">bool数组值</param>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    byte station,
    string[] address,
    bool[] value)
  {
    OperateResult<List<byte[]>> operateResult1 = PanasonicHelper.BuildWriteCoils(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    for (int index = 0; index < operateResult1.Content.Count; ++index)
    {
      OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content[index]);
      if (!operateResult2.IsSuccess)
        return (OperateResult) operateResult2;
      OperateResult operateResult3 = (OperateResult) PanasonicHelper.ExtraActualData(operateResult2.Content);
      if (!operateResult3.IsSuccess)
        return operateResult3;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 读取指定地址的原始数据，地址示例：D0  F0  K0  T0  C0, 地址支持携带站号的访问方式，例如：s=2;D100<br />
  /// Read the original data of the specified address, address example: D0 F0 K0 T0 C0, the address supports carrying station number information, for example: s=2;D100
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">起始地址</param>
  /// <param name="length">长度</param>
  /// <returns>原始的字节数据的信息</returns>
  public static OperateResult<byte[]> Read(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<List<byte[]>> result = PanasonicHelper.BuildReadCommand(station, address, length, false);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result.Content.Count; ++index)
    {
      OperateResult<byte[]> operateResult1 = plc.ReadFromCoreServer(result.Content[index]);
      if (!operateResult1.IsSuccess)
        return operateResult1;
      OperateResult<byte[]> operateResult2 = PanasonicHelper.ExtraActualData(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      byteList.AddRange((IEnumerable<byte>) operateResult2.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>
  /// 将数据写入到指定的地址里去，地址示例：D0  F0  K0  T0  C0, 地址支持携带站号的访问方式，例如：s=2;D100<br />
  /// Write data to the specified address, address example: D0 F0 K0 T0 C0, the address supports carrying station number information, for example: s=2;D100
  /// </summary>
  /// <param name="plc">PLC对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">起始地址</param>
  /// <param name="value">真实数据</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    byte station,
    string address,
    byte[] value)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> operateResult1 = PanasonicHelper.BuildWriteCommand(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) PanasonicHelper.ExtraActualData(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String)" />
  public static async Task<OperateResult<bool>> ReadBoolAsync(
    IReadWriteDevice plc,
    byte station,
    string address)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
    {
      OperateResult<bool[]> result = await HslHelper.ReadBoolAsync((IReadWriteNet) plc, address, (ushort) 1).ConfigureAwait(false);
      return ByteTransformHelper.GetResultFromArray<bool>(result);
    }
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> command = PanasonicHelper.BuildReadOneCoil(station, address);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) command);
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? ByteTransformHelper.GetResultFromArray<bool>(PanasonicHelper.ExtraActualBool(read.Content)) : OperateResult.CreateFailedResult<bool>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) plc, address, length).ConfigureAwait(false);
      return operateResult;
    }
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<string, int> analysis = PanasonicHelper.AnalysisAddress(address);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) analysis);
    OperateResult<List<byte[]>> command = PanasonicHelper.BuildReadCommand(station, address, length, true);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<byte> list = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]).ConfigureAwait(false);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult<byte[]> extra = PanasonicHelper.ExtraActualData(read.Content);
      if (!extra.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) extra);
      list.AddRange((IEnumerable<byte>) extra.Content);
      read = (OperateResult<byte[]>) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(list.ToArray().ToBoolArray().SelectMiddle<bool>(analysis.Content2 % 16 /*0x10*/, (int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String[])" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice plc,
    byte station,
    string[] address)
  {
    OperateResult<List<byte[]>> command = PanasonicHelper.BuildReadCoils(station, address);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<bool> list = new List<bool>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]).ConfigureAwait(false);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult<bool[]> extra = PanasonicHelper.ExtraActualBool(read.Content);
      if (!extra.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) extra);
      list.AddRange((IEnumerable<bool>) extra.Content);
      read = (OperateResult<byte[]>) null;
      extra = (OperateResult<bool[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(list.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    bool value)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
      return HslHelper.WriteBool((IReadWriteNet) plc, address, new bool[1]
      {
        value
      });
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> command = PanasonicHelper.BuildWriteOneCoil(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? (OperateResult) PanasonicHelper.ExtraActualData(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    bool[] values)
  {
    if (MewtocolHelper.CheckBoolOnWordAddress(address))
      return HslHelper.WriteBool((IReadWriteNet) plc, address, values);
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<string, int> analysis = PanasonicHelper.AnalysisAddress(address);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) analysis);
    if (analysis.Content2 % 16 /*0x10*/ != 0)
      return new OperateResult(StringResources.Language.PanasonicAddressBitStartMulti16);
    if (values.Length % 16 /*0x10*/ != 0)
      return new OperateResult(StringResources.Language.PanasonicBoolLengthMulti16);
    byte[] buffer = SoftBasic.BoolArrayToByte(values);
    OperateResult<byte[]> command = PanasonicHelper.BuildWriteCommand(station, address, buffer);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? (OperateResult) PanasonicHelper.ExtraActualData(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String[],System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    byte station,
    string[] address,
    bool[] value)
  {
    OperateResult<List<byte[]>> command = PanasonicHelper.BuildWriteCoils(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]).ConfigureAwait(false);
      if (!read.IsSuccess)
        return (OperateResult) read;
      OperateResult extra = (OperateResult) PanasonicHelper.ExtraActualData(read.Content);
      if (!extra.IsSuccess)
        return extra;
      read = (OperateResult<byte[]>) null;
      extra = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<List<byte[]>> command = PanasonicHelper.BuildReadCommand(station, address, length, false);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> list = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]).ConfigureAwait(false);
      if (!read.IsSuccess)
        return read;
      OperateResult<byte[]> extra = PanasonicHelper.ExtraActualData(read.Content);
      if (!extra.IsSuccess)
        return extra;
      list.AddRange((IEnumerable<byte>) extra.Content);
      read = (OperateResult<byte[]>) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(list.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    byte[] value)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> command = PanasonicHelper.BuildWriteCommand(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? (OperateResult) PanasonicHelper.ExtraActualData(read.Content) : (OperateResult) read;
  }

  private static OperateResult<string> GetPlcType(byte[] data)
  {
    try
    {
      string str = Encoding.ASCII.GetString(data, 0, 2);
      switch (str)
      {
        case "03":
          return OperateResult.CreateSuccessResult<string>("FP3");
        case "02":
          return OperateResult.CreateSuccessResult<string>("FP5");
        case "05":
          return OperateResult.CreateSuccessResult<string>("FP-E");
        default:
          return OperateResult.CreateSuccessResult<string>(str);
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<string>($"Get plctype failed : {ex.Message}{Environment.NewLine}Source: {data.ToHexString()}");
    }
  }

  /// <summary>
  /// 读取PLC的型号信息<br />
  /// Read the model information of the PLC
  /// </summary>
  /// <param name="plc">通信对象</param>
  /// <param name="station">站号信息</param>
  /// <returns>PLC型号</returns>
  public static OperateResult<string> ReadPlcType(IReadWriteDevice plc, byte station)
  {
    OperateResult<byte[]> result1 = PanasonicHelper.BuildReadPlcModel(station);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result2);
    OperateResult<byte[]> result3 = PanasonicHelper.ExtraActualData(result2.Content, false);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result3) : MewtocolHelper.GetPlcType(result3.Content);
  }
}
