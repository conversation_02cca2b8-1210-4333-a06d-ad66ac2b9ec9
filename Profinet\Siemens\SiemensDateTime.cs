﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensDateTime
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>
/// Contains the methods to convert between <see cref="T:System.DateTime" /> and S7 representation of datetime values.
/// </summary>
/// <remarks>
/// 这部分的代码参考了另一个s7的库，感谢原作者，此处贴出出处，遵循 MIT 协议
/// 
/// https://github.com/S7NetPlus/s7netplus
/// </remarks>
public class SiemensDateTime
{
  /// <summary>
  /// The minimum <see cref="T:System.DateTime" /> value supported by the specification.
  /// </summary>
  public static readonly DateTime SpecMinimumDateTime = new DateTime(1990, 1, 1);
  /// <summary>
  /// The maximum <see cref="T:System.DateTime" /> value supported by the specification.
  /// </summary>
  public static readonly DateTime SpecMaximumDateTime = new DateTime(2089, 12, 31 /*0x1F*/, 23, 59, 59, 999);

  /// <summary>
  /// Parses a <see cref="T:System.DateTime" /> value from bytes.
  /// </summary>
  /// <param name="bytes">Input bytes read from PLC.</param>
  /// <returns>A <see cref="T:System.DateTime" /> object representing the value read from PLC.</returns>
  /// <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the length of
  /// <paramref name="bytes" /> is not 8 or any value in <paramref name="bytes" />
  /// is outside the valid range of values.</exception>
  public static OperateResult<DateTime> FromByteArray(byte[] bytes)
  {
    try
    {
      return OperateResult.CreateSuccessResult<DateTime>(SiemensDateTime.FromByteArrayImpl((IList<byte>) bytes));
    }
    catch (Exception ex)
    {
      return new OperateResult<DateTime>("Prase DateTime failed: " + ex.Message);
    }
  }

  /// <summary>从西门子的原始字节数据中，提取出DTL格式的时间信息</summary>
  /// <param name="byteTransform">西门子的字节变换对象</param>
  /// <param name="buffer">原始字节数据</param>
  /// <param name="index">字节偏移索引</param>
  /// <returns>时间信息</returns>
  public static OperateResult<DateTime> GetDTLTime(
    IByteTransform byteTransform,
    byte[] buffer,
    int index)
  {
    try
    {
      return OperateResult.CreateSuccessResult<DateTime>(new DateTime((int) byteTransform.TransInt16(buffer, index), (int) buffer[index + 2], (int) buffer[index + 3], (int) buffer[index + 5], (int) buffer[index + 6], (int) buffer[index + 7], byteTransform.TransInt32(buffer, index + 8) / 1000 / 1000));
    }
    catch (Exception ex)
    {
      return new OperateResult<DateTime>("GetDTLTime failed: " + ex.Message);
    }
  }

  /// <summary>将时间数据转换为西门子的DTL格式的时间数据</summary>
  /// <param name="byteTransform">西门子的字节变换对象</param>
  /// <param name="dateTime">指定的时间信息</param>
  /// <returns>原始字节数据信息</returns>
  public static byte[] GetBytesFromDTLTime(IByteTransform byteTransform, DateTime dateTime)
  {
    byte[] bytesFromDtlTime = new byte[12];
    byteTransform.TransByte((short) dateTime.Year).CopyTo((Array) bytesFromDtlTime, 0);
    bytesFromDtlTime[2] = (byte) dateTime.Month;
    bytesFromDtlTime[3] = (byte) dateTime.Day;
    bytesFromDtlTime[4] = (byte) 5;
    bytesFromDtlTime[5] = (byte) dateTime.Hour;
    bytesFromDtlTime[6] = (byte) dateTime.Minute;
    bytesFromDtlTime[7] = (byte) dateTime.Second;
    byteTransform.TransByte(dateTime.Millisecond * 1000 * 1000).CopyTo((Array) bytesFromDtlTime, 8);
    return bytesFromDtlTime;
  }

  /// <summary>
  /// Parses an array of <see cref="T:System.DateTime" /> values from bytes.
  /// </summary>
  /// <param name="bytes">Input bytes read from PLC.</param>
  /// <returns>An array of <see cref="T:System.DateTime" /> objects representing the values read from PLC.</returns>
  /// <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the length of
  /// <paramref name="bytes" /> is not a multiple of 8 or any value in
  /// <paramref name="bytes" /> is outside the valid range of values.</exception>
  public static DateTime[] ToArray(byte[] bytes)
  {
    if (bytes.Length % 8 != 0)
      throw new ArgumentOutOfRangeException(nameof (bytes), (object) bytes.Length, $"Parsing an array of DateTime requires a multiple of 8 bytes of input data, input data is '{bytes.Length}' long.");
    int num = bytes.Length / 8;
    DateTime[] array = new DateTime[bytes.Length / 8];
    for (int index = 0; index < num; ++index)
      array[index] = SiemensDateTime.FromByteArrayImpl((IList<byte>) new ArraySegment<byte>(bytes, index * 8, 8).Array);
    return array;
  }

  private static DateTime FromByteArrayImpl(IList<byte> bytes)
  {
    int year = bytes.Count == 8 ? ByteToYear(bytes[0]) : throw new ArgumentOutOfRangeException(nameof (bytes), (object) bytes.Count, $"Parsing a DateTime requires exactly 8 bytes of input data, input data is {bytes.Count} bytes long.");
    int month = AssertRangeInclusive(DecodeBcd(bytes[1]), (byte) 1, (byte) 12, "month");
    int day = AssertRangeInclusive(DecodeBcd(bytes[2]), (byte) 1, (byte) 31 /*0x1F*/, "day of month");
    int hour = AssertRangeInclusive(DecodeBcd(bytes[3]), (byte) 0, (byte) 23, "hour");
    int minute = AssertRangeInclusive(DecodeBcd(bytes[4]), (byte) 0, (byte) 59, "minute");
    int second = AssertRangeInclusive(DecodeBcd(bytes[5]), (byte) 0, (byte) 59, "second");
    int num1 = AssertRangeInclusive(DecodeBcd(bytes[6]), (byte) 0, (byte) 99, "first two millisecond digits");
    int num2 = AssertRangeInclusive((int) bytes[7] >> 4, (byte) 0, (byte) 9, "third millisecond digit");
    AssertRangeInclusive((int) bytes[7] & 15, (byte) 1, (byte) 7, "day of week");
    return new DateTime(year, month, day, hour, minute, second, num1 * 10 + num2);

    static int DecodeBcd(byte input) => 10 * ((int) input >> 4) + ((int) input & 15);

    static int ByteToYear(byte bcdYear)
    {
      int num = DecodeBcd(bcdYear);
      if (num < 90)
        return num + 2000;
      if (num < 100)
        return num + 1900;
      throw new ArgumentOutOfRangeException(nameof (bcdYear), (object) bcdYear, $"Value '{num}' is higher than the maximum '99' of S7 date and time representation.");
    }

    static int AssertRangeInclusive(int input, byte min, byte max, string field)
    {
      if (input < (int) min)
        throw new ArgumentOutOfRangeException(nameof (input), (object) input, $"Value '{input}' is lower than the minimum '{min}' allowed for {field}.");
      return input <= (int) max ? input : throw new ArgumentOutOfRangeException(nameof (input), (object) input, $"Value '{input}' is higher than the maximum '{max}' allowed for {field}.");
    }
  }

  /// <summary>
  /// Converts a <see cref="T:System.DateTime" /> value to a byte array.
  /// </summary>
  /// <param name="dateTime">The DateTime value to convert.</param>
  /// <returns>A byte array containing the S7 date time representation of <paramref name="dateTime" />.</returns>
  /// <exception cref="T:System.ArgumentOutOfRangeException">Thrown when the value of
  /// <paramref name="dateTime" /> is before <see cref="P:SpecMinimumDateTime" />
  /// or after <see cref="P:SpecMaximumDateTime" />.</exception>
  public static byte[] ToByteArray(DateTime dateTime)
  {
    if (dateTime < SiemensDateTime.SpecMinimumDateTime)
      throw new ArgumentOutOfRangeException(nameof (dateTime), (object) dateTime, $"Date time '{dateTime}' is before the minimum '{SiemensDateTime.SpecMinimumDateTime}' supported in S7 date time representation.");
    if (dateTime > SiemensDateTime.SpecMaximumDateTime)
      throw new ArgumentOutOfRangeException(nameof (dateTime), (object) dateTime, $"Date time '{dateTime}' is after the maximum '{SiemensDateTime.SpecMaximumDateTime}' supported in S7 date time representation.");
    return new byte[8]
    {
      EncodeBcd((int) MapYear(dateTime.Year)),
      EncodeBcd(dateTime.Month),
      EncodeBcd(dateTime.Day),
      EncodeBcd(dateTime.Hour),
      EncodeBcd(dateTime.Minute),
      EncodeBcd(dateTime.Second),
      EncodeBcd(dateTime.Millisecond / 10),
      (byte) (dateTime.Millisecond % 10 << 4 | DayOfWeekToInt(dateTime.DayOfWeek))
    };

    static byte EncodeBcd(int value) => (byte) (value / 10 << 4 | value % 10);

    static byte MapYear(int year) => year < 2000 ? (byte) (year - 1900) : (byte) (year - 2000);

    static int DayOfWeekToInt(DayOfWeek dayOfWeek) => (int) (dayOfWeek + 1);
  }

  /// <summary>
  /// Converts an array of <see cref="T:System.DateTime" /> values to a byte array.
  /// </summary>
  /// <param name="dateTimes">The DateTime values to convert.</param>
  /// <returns>A byte array containing the S7 date time representations of <paramref name="dateTimes" />.</returns>
  /// <exception cref="T:System.ArgumentOutOfRangeException">Thrown when any value of
  /// <paramref name="dateTimes" /> is before <see cref="P:SpecMinimumDateTime" />
  /// or after <see cref="P:SpecMaximumDateTime" />.</exception>
  public static byte[] ToByteArray(DateTime[] dateTimes)
  {
    List<byte> byteList = new List<byte>(dateTimes.Length * 8);
    foreach (DateTime dateTime in dateTimes)
      byteList.AddRange((IEnumerable<byte>) SiemensDateTime.ToByteArray(dateTime));
    return byteList.ToArray();
  }
}
