﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.ToolInformation
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>刀具信息</summary>
public class ToolInformation
{
  /// <summary>实例化一个默认的对象</summary>
  public ToolInformation()
  {
  }

  /// <summary>指定内存数据来初始化对象</summary>
  /// <param name="content">机床返回的数据</param>
  /// <param name="byteTransform">字节变换规则</param>
  public ToolInformation(byte[] content, IByteTransform byteTransform)
  {
    this.Life = byteTransform.TransInt32(content, 26);
    this.Use = byteTransform.TransInt32(content, 34);
  }

  /// <summary>当前刀具的寿命</summary>
  public int Life { get; set; }

  /// <summary>当前刀具的使用次数</summary>
  public int Use { get; set; }
}
