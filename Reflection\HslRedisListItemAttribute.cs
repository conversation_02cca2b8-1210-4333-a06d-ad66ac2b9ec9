﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslRedisListItemAttribute
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>对应redis的一个列表信息的内容</summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class HslRedisListItemAttribute : Attribute
{
  /// <summary>列表键值的名称</summary>
  public string ListKey { get; set; }

  /// <summary>当前的位置的索引</summary>
  public long Index { get; set; }

  /// <summary>根据键名来读取写入当前的列表中的单个信息</summary>
  /// <param name="listKey">列表键名</param>
  /// <param name="index">当前的索引位置</param>
  public HslRedisListItemAttribute(string listKey, long index)
  {
    this.ListKey = listKey;
    this.Index = index;
  }
}
