﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttCredential
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// Mqtt协议的验证对象，包含用户名和密码<br />
/// Authentication object of Mqtt protocol, including username and password
/// </summary>
public class MqttCredential
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public MqttCredential()
  {
  }

  /// <summary>
  /// 实例化指定的用户名和密码的对象<br />
  /// Instantiates an object with the specified username and password
  /// </summary>
  /// <param name="name">用户名</param>
  /// <param name="pwd">密码</param>
  public MqttCredential(string name, string pwd)
  {
    this.UserName = name;
    this.Password = pwd;
  }

  /// <summary>
  /// 获取或设置用户名<br />
  /// Get or set username
  /// </summary>
  public string UserName { get; set; }

  /// <summary>
  /// 获取或设置密码<br />
  /// Get or set password
  /// </summary>
  public string Password { get; set; }

  /// <inheritdoc />
  public override string ToString() => this.UserName;
}
