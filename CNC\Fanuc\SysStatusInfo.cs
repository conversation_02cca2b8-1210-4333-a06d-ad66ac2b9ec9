﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.SysStatusInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>系统状态信息</summary>
public class SysStatusInfo
{
  /// <summary>dummy</summary>
  public short Dummy { get; set; }

  /// <summary>T/M mode</summary>
  public short TMMode { get; set; }

  /// <summary>selected automatic mode</summary>
  public CNCWorkMode WorkMode { get; set; }

  /// <summary>running status</summary>
  public CNCRunStatus RunStatus { get; set; }

  /// <summary>axis, dwell status</summary>
  public short Motion { get; set; }

  /// <summary>m, s, t, b status</summary>
  public short MSTB { get; set; }

  /// <summary>emergency stop status，为1就是急停，为0就是正常</summary>
  public short Emergency { get; set; }

  /// <summary>alarm status</summary>
  public short Alarm { get; set; }

  /// <summary>editting status</summary>
  public short Edit { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"Dummy: {this.Dummy}, TMMode:{this.TMMode}, WorkMode:{this.WorkMode}, RunStatus:{this.RunStatus}, " + $"Motion:{this.Motion}, MSTB:{this.MSTB}, Emergency:{this.Emergency}, Alarm:{this.Alarm}, Edit:{this.Edit}";
  }
}
