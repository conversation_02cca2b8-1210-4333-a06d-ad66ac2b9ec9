﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.MelsecFxLinksMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.IO;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>三菱的FxLinks的消息类</summary>
public class MelsecFxLinksMessage : NetMessageBase, INetMessage
{
  private int format = 1;
  private bool sumCheck = true;

  /// <summary>指定格式，及是否和校验实例化一个对象</summary>
  /// <param name="format">格式信息，1, 4</param>
  /// <param name="sumCheck">是否和校验</param>
  public MelsecFxLinksMessage(int format, bool sumCheck)
  {
    this.format = format;
    this.sumCheck = sumCheck;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => -1;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => 0;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckReceiveDataComplete(System.Byte[],System.IO.MemoryStream)" />
  public override bool CheckReceiveDataComplete(byte[] send, MemoryStream ms)
  {
    byte[] array = ms.ToArray();
    if (array.Length < 5)
      return false;
    if (this.format == 1)
    {
      if (array[0] == (byte) 21)
        return array.Length == 7;
      if (array[0] == (byte) 6)
        return array.Length == 5;
      if (array[0] != (byte) 2)
        return false;
      return this.sumCheck ? array[array.Length - 3] == (byte) 3 : array[array.Length - 1] == (byte) 3;
    }
    return this.format == 4 && array[array.Length - 1] == (byte) 10 && array[array.Length - 2] == (byte) 13;
  }
}
