﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.Helper.LSCpuHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.LSIS.Helper;

/// <summary>
/// <see cref="T:HslCommunication.Profinet.LSIS.LSCpu" />相关的辅助类，提供了一些辅助的静态方法信息
/// </summary>
public class LSCpuHelper
{
  private const string CpuTypes = "PMLKFTCDSQINUZR";

  /// <summary>
  /// 根据错误号，获取到真实的错误描述信息<br />
  /// According to the error number, get the real error description information
  /// </summary>
  /// <param name="err">错误号</param>
  /// <returns>真实的错误描述信息</returns>
  public static string GetErrorText(int err)
  {
    switch (err)
    {
      case 3:
        return StringResources.Language.LsisCnet0003;
      case 4:
        return StringResources.Language.LsisCnet0004;
      case 7:
        return StringResources.Language.LsisCnet0007;
      case 17:
        return StringResources.Language.LsisCnet0011;
      case 144 /*0x90*/:
        return StringResources.Language.LsisCnet0090;
      case 400:
        return StringResources.Language.LsisCnet0190;
      case 656:
        return StringResources.Language.LsisCnet0290;
      case 4402:
        return StringResources.Language.LsisCnet1132;
      case 4658:
        return StringResources.Language.LsisCnet1232;
      case 4660:
        return StringResources.Language.LsisCnet1234;
      case 4914:
        return StringResources.Language.LsisCnet1332;
      case 5170:
        return StringResources.Language.LsisCnet1432;
      case 28978:
        return StringResources.Language.LsisCnet7132;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.UnpackResponseContent(System.Byte[],System.Byte[])" />
  public static OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    try
    {
      if (response[0] == (byte) 6)
      {
        if (response[1] == (byte) 110 || response[1] == (byte) 111 || response[1] == (byte) 119)
          return OperateResult.CreateSuccessResult<byte[]>(response);
        string str1 = Encoding.ASCII.GetString(response);
        string str2 = str1.Substring(1, str1.Length - 2);
        return OperateResult.CreateSuccessResult<byte[]>(LSCpuHelper.GetBytesFromHex(str2.Substring(1, str2.Length - 3)));
      }
      if (response[0] != (byte) 21)
        return new OperateResult<byte[]>((int) response[0], "Source: " + SoftBasic.GetAsciiStringRender(response));
      int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, 6, 4), 16 /*0x10*/);
      return new OperateResult<byte[]>(int32, LSCpuHelper.GetErrorText(int32));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(1, $"Wrong:{ex.Message}{Environment.NewLine}Source: {response.ToHexString()}");
    }
  }

  /// <summary>GetBytesFromHex</summary>
  /// <param name="IP"></param>
  /// <returns></returns>
  public static byte[] GetBytesFromHex(string IP)
  {
    byte[] bytesFromHex = new byte[IP.Length / 2];
    for (int index = 0; index < bytesFromHex.Length; ++index)
      bytesFromHex[index] = Convert.ToByte(IP.Substring(index * 2, 2), 16 /*0x10*/);
    return bytesFromHex;
  }

  /// <summary>
  /// 从输入的地址里解析出真实的可以直接放到协议的地址信息，如果是X的地址，自动转换带小数点的表示方式到位地址，如果是其他类型地址，则一律统一转化为字节为单位的地址<br />
  /// The real address information that can be directly put into the protocol is parsed from the input address. If it is the address of X,
  /// it will automatically convert the representation with a decimal point to the address. If it is an address of other types, it will be uniformly converted into a unit of bytes. address
  /// </summary>
  /// <param name="address">输入的起始偏移地址</param>
  /// <param name="transBit">是否转换为bool地址</param>
  /// <returns>analysis result</returns>
  public static OperateResult<string> AnalysisAddress(string address, bool transBit = false)
  {
    StringBuilder stringBuilder = new StringBuilder();
    try
    {
      if (!"PMLKFTCDSQINUZR".Contains<char>(address[0]))
        return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
      stringBuilder.Append(address[0]);
      if (address[0] == 'M')
      {
        stringBuilder.Append("X");
        if (transBit & address.IndexOf('.') > 0)
        {
          int indexInformation = HslHelper.GetBitIndexInformation(ref address);
          stringBuilder.Append(address.Substring(2));
          stringBuilder.Append(indexInformation.ToString("X1"));
        }
        else
          stringBuilder.Append(address.Substring(2, address.Length - 2));
      }
      else
      {
        stringBuilder.Append("W");
        stringBuilder.Append(Convert.ToInt32(address.Substring(2, address.Length - 2)));
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
    return OperateResult.CreateSuccessResult<string>(stringBuilder.ToString());
  }

  /// <summary>往现有的命令数据中增加BCC的内容</summary>
  /// <param name="command">现有的命令</param>
  private static void AddBccTail(List<byte> command)
  {
    int num = 0;
    for (int index = 0; index < command.Count; ++index)
      num += (int) command[index];
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) num));
  }

  /// <summary>reading address  Type of ReadByte</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="length">read length</param>
  /// <returns>command bytes</returns>
  public static OperateResult<byte[]> BuildReadByteCommand(
    byte station,
    string address,
    ushort length)
  {
    List<byte> byteList = new List<byte>();
    byte num = 0;
    byteList.Clear();
    int memoryType = LSCpuHelper.GetMemoryType(address);
    int dataType = LSCpuHelper.GetDataType(address);
    int oct = LSCpuHelper.HexToOct(address.Substring(1, address.Length - 1));
    byteList.Add((byte) 2);
    byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(LSCpuHelper.sprintf("r%C", (object) LSCpuHelper.select_data_code(memoryType))));
    byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) 0:X4}"));
    byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) 0:X2}"));
    byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) LSCpuHelper.GetDataSize2(oct, (int) length, dataType):X2}"));
    for (int index = 1; index <= 10; ++index)
      num += byteList[index];
    byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{num:X2}"));
    byteList.Add((byte) 3);
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>build read command.</summary>
  /// <param name="station">station</param>
  /// <param name="address">start address</param>
  /// <param name="length">address length</param>
  /// <returns> command</returns>
  public static OperateResult<byte[]> BuildReadCommand(byte station, string address, ushort length)
  {
    return LSCpuHelper.BuildReadByteCommand(station, address, length);
  }

  /// <summary>write data to address  Type of ReadByte</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="value">source value</param>
  /// <returns>command bytes</returns>
  public static OperateResult<byte[]> BuildWriteByteCommand(
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<string> result = LSCpuHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    List<byte> byteList = new List<byte>();
    string str = new string(new char[60]);
    ushort num1 = 0;
    float[] destinationArray = new float[3];
    char[] sourceArray = new char[4];
    short int16 = BitConverter.ToInt16(value, 0);
    int dataType = LSCpuHelper.GetDataType(result.Content);
    int memoryType = LSCpuHelper.GetMemoryType(result.Content);
    int Staraddress = int.Parse(result.Content.Substring(2, result.Content.Length - 2));
    if (memoryType == 128 /*0x80*/)
    {
      Console.WriteLine("Memory Type Input Error. Memory Type = P, M, K, T, C, U, Z, S, L, N, D, R, ZR", (object) Staraddress);
    }
    else
    {
      byteList.Clear();
      byteList.Add((byte) 2);
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(LSCpuHelper.sprintf("w%C", (object) LSCpuHelper.select_data_code(memoryType))));
      if (str != null)
        byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{LSCpuHelper.GetDataSize((int) (ushort) Convert.ToInt32(Staraddress), 2):X2}{(ValueType) (byte) 0:X2}"));
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) 0:X2}"));
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) LSCpuHelper.GetDataSize2(Staraddress, 1, dataType):X2}"));
      int num2;
      switch (dataType)
      {
        case 1:
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) int16:X2}"));
          num2 = 13;
          break;
        case 3:
          short num3 = (short) ((ulong) int16 >> 16 /*0x10*/);
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (uint) int16:X2}{(ValueType) (byte) int16:X2}"));
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (uint) num3:X2}{(ulong) int16 >> 24:X2}"));
          num2 = 18;
          break;
        case 4:
          destinationArray[0] = (float) int16;
          Array.Copy((Array) sourceArray, (Array) destinationArray, 2);
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (uint) sourceArray[0]:X2}{(ValueType) (uint) sourceArray[1]:X2}"));
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (uint) sourceArray[2]:X2}{(ValueType) (uint) sourceArray[3]:X2}"));
          num2 = 18;
          break;
        case 5:
          destinationArray[0] = (float) int16;
          Array.Copy((Array) sourceArray, (Array) destinationArray, 2);
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (uint) sourceArray[3]:X2}{(ValueType) (uint) sourceArray[2]:X2}"));
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (uint) sourceArray[1]:X2}{(ValueType) (uint) sourceArray[0]:X2}"));
          num2 = 18;
          break;
        default:
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (sbyte) int16:X2}{(int) int16 >> 8:X2}"));
          num2 = 14;
          break;
      }
      for (int index = 1; index <= num2; ++index)
        num1 += (ushort) byteList[index];
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) num1:X2}"));
      byteList.Add((byte) 3);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>write data to address  Type of One</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="value">source value</param>
  /// <returns>command bytes</returns>
  public static OperateResult<byte[]> BuildWriteOneCommand(
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<string> result = LSCpuHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    List<byte> byteList = new List<byte>();
    string str1 = new string(new char[31 /*0x1F*/]);
    ushort num = 0;
    int memoryType = LSCpuHelper.GetMemoryType(result.Content);
    int oct = LSCpuHelper.HexToOct(result.Content.Substring(2, result.Content.Length - 2));
    string str2 = value[0].ToString("X2");
    if (memoryType == 128 /*0x80*/)
    {
      Console.WriteLine("Memory Type Input Error. Memory Type = P, M, K, T, C, U, Z, S, L, N, D, R, ZR", (object) address);
    }
    else
    {
      byteList.Clear();
      byteList.Add((byte) 2);
      if (str2 == "01")
        byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(LSCpuHelper.sprintf("o%C", (object) LSCpuHelper.select_data_code(memoryType))));
      else
        byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(LSCpuHelper.sprintf("n%C", (object) LSCpuHelper.select_data_code(memoryType))));
      if (memoryType != 1)
      {
        string str3 = $"{oct >> 4:X4}";
        byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{LSCpuHelper.GetDataSize((int) (ushort) Convert.ToInt32(str3), 2):X2}{(ValueType) (byte) 0:X2}"));
      }
      else
        byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (uint) (sbyte) ((int) (ushort) oct / 16 /*0x10*/):X2}{(int) (ushort) ((uint) (ushort) oct / 16U /*0x10*/) >> 16 /*0x10*/:X2}"));
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) 0:X2}"));
      short n = !(str2 != "00") ? (short) (-1.0 - Math.Pow(2.0, (double) (oct % 16 /*0x10*/))) : (short) Math.Pow(2.0, (double) (oct % 16 /*0x10*/));
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (sbyte) n:X2}{LSCpuHelper.HIBYTE(n):X2}"));
      for (int index = 1; index <= 12; ++index)
        num += (ushort) byteList[index];
      byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes($"{(ValueType) (byte) num:X2}"));
      byteList.Add((byte) 3);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>write data to address  Type of ReadByte</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="value">source value</param>
  /// <returns>command bytes</returns>
  public static OperateResult<byte[]> BuildWriteCommand(byte station, string address, byte[] value)
  {
    OperateResult<string> dataTypeToAddress = LSFastEnet.GetDataTypeToAddress(address);
    if (!dataTypeToAddress.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) dataTypeToAddress);
    switch (dataTypeToAddress.Content)
    {
      case "Bit":
        return LSCpuHelper.BuildWriteOneCommand(station, address, value);
      case "Word":
      case "DWord":
      case "LWord":
      case "Continuous":
        return LSCpuHelper.BuildWriteByteCommand(station, address, value);
      default:
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <summary>
  /// 从PLC的指定地址读取原始的字节数据信息，地址示例：MB100, MW100, MD100, 如果输入了M100等同于MB100<br />
  /// Read the original byte data information from the designated address of the PLC.
  /// Examples of addresses: MB100, MW100, MD100, if the input M100 is equivalent to MB100
  /// </summary>
  /// <remarks>
  /// 地址类型支持 P,M,L,K,F,T,C,D,R,I,Q,W, 支持携带站号的形式，例如 s=2;MW100
  /// </remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 M100, MB100, MW100, MD100</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  public static OperateResult<byte[]> Read(
    IReadWriteDevice plc,
    int station,
    string address,
    ushort length)
  {
    OperateResult<byte[]> operateResult = LSCpuHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "s", station), address, length);
    return !operateResult.IsSuccess ? operateResult : plc.ReadFromCoreServer(operateResult.Content);
  }

  /// <summary>
  /// 将原始数据写入到PLC的指定的地址里，地址示例：MB100, MW100, MD100, 如果输入了M100等同于MB100<br />
  /// Write the original data to the designated address of the PLC.
  /// Examples of addresses: MB100, MW100, MD100, if input M100 is equivalent to MB100
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 M100, MB100, MW100, MD100</param>
  /// <param name="value">等待写入的原始数据内容</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    int station,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> operateResult = LSCpuHelper.BuildWriteCommand((byte) HslHelper.ExtractParameter(ref address, "s", station), address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) plc.ReadFromCoreServer(operateResult.Content);
  }

  /// <summary>
  /// 从PLC的指定地址读取原始的位数据信息，地址示例：MB100.0, MW100.0<br />
  /// Read the original bool data information from the designated address of the PLC.
  /// Examples of addresses: MB100.0, MW100.0
  /// </summary>
  /// <remarks>
  /// 地址类型支持 P,M,L,K,F,T,C,D,R,I,Q,W, 支持携带站号的形式，例如 s=2;MB100.0
  /// </remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 MB100.0, MW100.0</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice plc,
    int station,
    string address,
    ushort length)
  {
    int indexInformation = HslHelper.GetBitIndexInformation(ref address);
    int occupyLength = HslHelper.CalculateOccupyLength(indexInformation, (int) length);
    OperateResult<byte[]> result = LSCpuHelper.Read(plc, station, address, (ushort) occupyLength);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(result.Content.ToBoolArray().SelectMiddle<bool>(indexInformation, (int) length));
  }

  /// <summary>
  /// 将bool数据写入到PLC的指定的地址里，地址示例：MX100, MX10A<br />
  /// Write the bool data to the designated address of the PLC. Examples of addresses: MX100, MX10A
  /// </summary>
  /// <remarks>
  /// 地址类型支持 P,M,L,K,F,T,C,D,R,I,Q,W, 支持携带站号的形式，例如 s=2;MX100
  /// </remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 MX100, MX10A</param>
  /// <param name="value">bool值信息</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  public static OperateResult Write(IReadWriteDevice plc, int station, string address, bool value)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    OperateResult<string> operateResult1 = LSCpuHelper.AnalysisAddress(address, true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = LSCpuHelper.BuildWriteOneCommand(parameter, operateResult1.Content, new byte[1]
    {
      value ? (byte) 1 : (byte) 0
    });
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) plc.ReadFromCoreServer(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice plc,
    int station,
    string address,
    ushort length)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    OperateResult<byte[]> command = LSCpuHelper.BuildReadCommand(stat, address, length);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> operateResult = await plc.ReadFromCoreServerAsync(command.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    int station,
    string address,
    byte[] value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    OperateResult<byte[]> command = LSCpuHelper.BuildWriteCommand(stat, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await plc.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice plc,
    int station,
    string address,
    ushort length)
  {
    int bitIndex = HslHelper.GetBitIndexInformation(ref address);
    int byteLength = HslHelper.CalculateOccupyLength(bitIndex, (int) length);
    OperateResult<byte[]> read = await LSCpuHelper.ReadAsync(plc, station, address, (ushort) byteLength);
    OperateResult<bool[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray().SelectMiddle<bool>(bitIndex, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    int station,
    string address,
    bool value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    OperateResult<string> analysis = LSCpuHelper.AnalysisAddress(address, true);
    if (!analysis.IsSuccess)
      return (OperateResult) analysis;
    OperateResult<byte[]> command = LSCpuHelper.BuildWriteOneCommand(stat, analysis.Content.Substring(1), new byte[1]
    {
      value ? (byte) 1 : (byte) 0
    });
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await plc.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  private static int GetMemoryType(string address)
  {
    switch (address[0])
    {
      case 'C':
        return 5;
      case 'D':
        return 11;
      case 'F':
        return 3;
      case 'K':
        return 2;
      case 'L':
        return 9;
      case 'M':
        return 1;
      case 'N':
        return 10;
      case 'P':
        return 0;
      case 'R':
        return 12;
      case 'S':
        return 8;
      case 'T':
        return 4;
      case 'U':
        return 6;
      case 'Z':
        return 7;
      default:
        return 128 /*0x80*/;
    }
  }

  private static int GetDataType(string address)
  {
    switch (address[1])
    {
      case 'D':
        return 4;
      case 'F':
        return 8;
      case 'W':
        return 2;
      case 'X':
        return 1;
      default:
        return 1;
    }
  }

  private static string sprintf(string input, params object[] inpVars)
  {
    int i = -1;
    input = Regex.Replace(input, "%.", (MatchEvaluator) (m => $"{{{(++i).ToString()}}}"));
    return string.Format(input, inpVars);
  }

  private static char select_data_code(int MemoryType)
  {
    char ch;
    switch (MemoryType)
    {
      case 0:
        ch = 'h';
        break;
      case 2:
        ch = 'k';
        break;
      case 3:
        ch = 'n';
        break;
      case 4:
        ch = 'd';
        break;
      case 5:
        ch = 'm';
        break;
      case 6:
        ch = 'q';
        break;
      case 7:
        ch = 'z';
        break;
      case 8:
        ch = 'o';
        break;
      case 9:
        ch = 'j';
        break;
      case 10:
        ch = 'p';
        break;
      case 11:
        ch = 'a';
        break;
      case 12:
        ch = 'r';
        break;
      case 13:
        ch = '{';
        break;
      default:
        ch = 'i';
        break;
    }
    return ch;
  }

  private static int GetDataSize(int address, int datatype)
  {
    if (datatype == 2)
      return 2 * address;
    return datatype > 2 && datatype <= 5 ? 4 * address : 10 * address / 8;
  }

  private static int GetDataSize2(int Staraddress, int DataSize, int datatype)
  {
    int num1;
    switch (datatype)
    {
      case 2:
        return 2 * DataSize;
      case 3:
      case 4:
        num1 = 1;
        break;
      default:
        num1 = datatype == 5 ? 1 : 0;
        break;
    }
    if (num1 != 0)
      return 4 * DataSize;
    int dataSize2 = 10 * DataSize / 8;
    int num2 = (8 - 10 * Staraddress % 8) % 8;
    if (num2 + 8 * dataSize2 < 10 * DataSize)
      ++dataSize2;
    if (num2 != 0)
      ++dataSize2;
    return dataSize2;
  }

  private static byte? HIBYTE(short n)
  {
    byte[] numArray = new byte[4]
    {
      (byte) ((int) n >> 24 & (int) byte.MaxValue),
      (byte) ((int) n >> 16 /*0x10*/ & (int) byte.MaxValue),
      (byte) ((int) n >> 8 & (int) byte.MaxValue),
      (byte) ((uint) n & (uint) byte.MaxValue)
    };
    if ((byte) ((int) n >> 8 & (int) byte.MaxValue) != byte.MaxValue && (byte) ((int) n >> 8 & (int) byte.MaxValue) > (byte) 0)
      return new byte?((byte) ((int) n >> 8 & (int) byte.MaxValue));
    return (byte) ((uint) n & (uint) byte.MaxValue) != byte.MaxValue && (byte) ((uint) n & (uint) byte.MaxValue) > (byte) 0 ? new byte?((byte) ((uint) n & (uint) byte.MaxValue)) : new byte?(numArray[3]);
  }

  private static int HexToOct(string hexNum)
  {
    Dictionary<char, int> dictionary = new Dictionary<char, int>()
    {
      {
        '0',
        0
      },
      {
        '1',
        1
      },
      {
        '2',
        2
      },
      {
        '3',
        3
      },
      {
        '4',
        4
      },
      {
        '5',
        5
      },
      {
        '6',
        6
      },
      {
        '7',
        7
      },
      {
        '8',
        8
      },
      {
        '9',
        9
      },
      {
        'A',
        10
      },
      {
        'B',
        11
      },
      {
        'C',
        12
      },
      {
        'D',
        13
      },
      {
        'E',
        14
      },
      {
        'F',
        15
      }
    };
    int oct = 0;
    foreach (char key in hexNum)
      oct = oct * 16 /*0x10*/ + dictionary[key];
    return oct;
  }
}
