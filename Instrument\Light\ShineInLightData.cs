﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.Light.ShineInLightData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Instrument.Light;

/// <summary>光源的数据信息</summary>
public class ShineInLightData
{
  /// <summary>实例化一个默认的对象</summary>
  public ShineInLightData()
  {
    this.Color = (byte) 4;
    this.LightDegree = (byte) 1;
    this.PulseWidth = (byte) 1;
  }

  /// <summary>使用指定的原始数据来获取当前的对象</summary>
  /// <param name="data">原始数据</param>
  public ShineInLightData(byte[] data)
    : this()
  {
    this.ParseFrom(data);
  }

  /// <summary>光源颜色信息，1:红色  2:绿色  3:蓝色  4:白色(默认)</summary>
  public byte Color { get; set; }

  /// <summary>光源的亮度信息，00-FF，值越大，亮度越大</summary>
  public byte Light { get; set; }

  /// <summary>光源的亮度等级，1-3</summary>
  public byte LightDegree { get; set; }

  /// <summary>
  /// 光源的工作模式，00:延时常亮  01:通道一频闪  02:通道二频闪  03:通道一二频闪  04:普通常亮  05:关闭
  /// </summary>
  public byte WorkMode { get; set; }

  /// <summary>控制器的地址选择位</summary>
  public byte Address { get; set; }

  /// <summary>脉冲宽度，01-14H</summary>
  public byte PulseWidth { get; set; }

  /// <summary>通道数据，01-08H的值</summary>
  public byte Channel { get; set; }

  /// <summary>获取原始的数据信息</summary>
  /// <returns>原始的字节信息</returns>
  public byte[] GetSourceData()
  {
    return new byte[7]
    {
      this.Color,
      this.Light,
      this.LightDegree,
      this.WorkMode,
      this.Address,
      this.PulseWidth,
      this.Channel
    };
  }

  /// <summary>从原始的信息解析光源的数据</summary>
  /// <param name="data">原始的数据信息</param>
  public void ParseFrom(byte[] data)
  {
    if (data != null && data.Length < 7)
      return;
    this.Color = data[0];
    this.Light = data[1];
    this.LightDegree = data[2];
    this.WorkMode = data[3];
    this.Address = data[4];
    this.PulseWidth = data[5];
    this.Channel = data[6];
  }

  /// <inheritdoc />
  public override string ToString() => $"ShineInLightData[{this.Color}]";
}
