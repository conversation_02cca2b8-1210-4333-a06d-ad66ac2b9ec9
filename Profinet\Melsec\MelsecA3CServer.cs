﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecA3CServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 基于MC协议的A3C格式虚拟服务器，可以模拟A3C格式的PLC，支持格式1，2，3，4，具体可以使用<see cref="P:HslCommunication.Profinet.Melsec.MelsecA3CServer.Format" />来设置，
/// 支持设置是否校验，同时支持网口或是串口的访问<br />
/// A3C format virtual server based on MC protocol can simulate A3C format PLC, support format 1, 2, 3, 4,
/// specifically you can use <see cref="P:HslCommunication.Profinet.Melsec.MelsecA3CServer.Format" /> to set, support whether to verify the setting,
/// and also support network Port or serial port access
/// </summary>
/// <remarks>可访问的地址支持M,X,Y,B,D,W,R,ZR地址，其中 M,X,Y,B 支持位访问</remarks>
public class MelsecA3CServer : MelsecMcServer
{
  /// <summary>实例化一个虚拟的A3C服务器</summary>
  public MelsecA3CServer()
    : base(false)
  {
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C.Station" />
  public byte Station { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C.SumCheck" />
  public bool SumCheck { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C.Format" />
  public int Format { get; set; } = 1;

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) null;

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length < 3)
      return new OperateResult<byte[]>("Uknown Data：" + receive.ToHexString(' '));
    OperateResult<byte[]> operateResult = this.ExtraMcCore(receive);
    return !operateResult.IsSuccess ? new OperateResult<byte[]>(operateResult.Message) : OperateResult.CreateSuccessResult<byte[]>(this.ReadFromMcAsciiCore(operateResult.Content));
  }

  private void SetSumCheck(byte[] command, int startLength, int endLength)
  {
    int num = 0;
    for (int index = startLength; index < command.Length - endLength; ++index)
      num += (int) command[index];
    byte[] numArray = SoftBasic.BuildAsciiBytesFrom((byte) num);
    command[command.Length - endLength] = numArray[0];
    command[command.Length - endLength + 1] = numArray[1];
  }

  private bool CalculatSumCheck(byte[] command, int startLength, int endLength)
  {
    int num = 0;
    for (int index = startLength; index < command.Length - endLength; ++index)
      num += (int) command[index];
    byte[] numArray = SoftBasic.BuildAsciiBytesFrom((byte) num);
    return (int) command[command.Length - endLength] == (int) numArray[0] && (int) command[command.Length - endLength + 1] == (int) numArray[1];
  }

  private OperateResult<byte[]> ExtraMcCore(byte[] command)
  {
    byte num = byte.Parse(Encoding.ASCII.GetString(command, this.Format == 2 ? 5 : 3, 2));
    if ((int) this.Station != (int) num)
      return new OperateResult<byte[]>($"Station Not Match, need: {this.Station}  but: {num}");
    if (this.Format == 1)
    {
      if (command[0] != (byte) 5)
        return new OperateResult<byte[]>("First Byte Must Start with 0x05");
      if (!this.SumCheck)
        return OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(11, command.Length - 11));
      return !this.CalculatSumCheck(command, 1, 2) ? new OperateResult<byte[]>("Sum Check Failed!") : OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(11, command.Length - 13));
    }
    if (this.Format == 2)
    {
      if (command[0] != (byte) 5)
        return new OperateResult<byte[]>("First Byte Must Start with 0x05");
      if (!this.SumCheck)
        return OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(13, command.Length - 13));
      return !this.CalculatSumCheck(command, 1, 2) ? new OperateResult<byte[]>("Sum Check Failed!") : OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(13, command.Length - 15));
    }
    if (this.Format == 3)
    {
      if (command[0] != (byte) 2)
        return new OperateResult<byte[]>("First Byte Must Start with 0x02");
      if (this.SumCheck)
      {
        if (command[command.Length - 3] != (byte) 3)
          return new OperateResult<byte[]>("The last three Byte Must be 0x03");
        return !this.CalculatSumCheck(command, 1, 2) ? new OperateResult<byte[]>("Sum Check Failed!") : OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(11, command.Length - 14));
      }
      return command[command.Length - 1] != (byte) 3 ? new OperateResult<byte[]>("The last Byte Must be 0x03") : OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(11, command.Length - 12));
    }
    if (this.Format != 4)
      return new OperateResult<byte[]>("Not Support Format:" + this.Format.ToString());
    if (command[0] != (byte) 5)
      return new OperateResult<byte[]>("First Byte Must Start with 0x05");
    if (command[command.Length - 1] != (byte) 10)
      return new OperateResult<byte[]>("The last Byte must be 0x0D,0x0A");
    if (command[command.Length - 2] != (byte) 13)
      return new OperateResult<byte[]>("The last Byte must be 0x0D,0x0A");
    if (!this.SumCheck)
      return OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(11, command.Length - 13));
    return !this.CalculatSumCheck(command, 1, 4) ? new OperateResult<byte[]>("Sum Check Failed!") : OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(11, command.Length - 15));
  }

  /// <inheritdoc />
  protected override byte[] PackCommand(ushort status, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    if (data.Length == 0)
    {
      if (this.Format == 1)
      {
        if (status == (ushort) 0)
        {
          byte[] bytes = Encoding.ASCII.GetBytes("\u0006F90000FF00");
          SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 3);
          return bytes;
        }
        byte[] bytes1 = Encoding.ASCII.GetBytes("\u0015F90000FF000000");
        SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes1, 3);
        SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes1, bytes1.Length - 4);
        return bytes1;
      }
      if (this.Format == 2)
      {
        if (status == (ushort) 0)
        {
          byte[] bytes = Encoding.ASCII.GetBytes("\u000600F90000FF00");
          SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 5);
          return bytes;
        }
        byte[] bytes2 = Encoding.ASCII.GetBytes("\u001500F90000FF000000");
        SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes2, 5);
        SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes2, bytes2.Length - 4);
        return bytes2;
      }
      if (this.Format == 3)
      {
        if (status == (ushort) 0)
        {
          byte[] bytes = Encoding.ASCII.GetBytes("\u0002F90000FF00QACK\u0003");
          SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 3);
          return bytes;
        }
        byte[] bytes3 = Encoding.ASCII.GetBytes("\u0002F90000FF00QNAK0000\u0003");
        SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes3, 3);
        SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes3, bytes3.Length - 5);
        return bytes3;
      }
      if (this.Format != 4)
        return (byte[]) null;
      if (status == (ushort) 0)
      {
        byte[] bytes = Encoding.ASCII.GetBytes("\u0006F90000FF00");
        SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 3);
        return bytes;
      }
      byte[] bytes4 = Encoding.ASCII.GetBytes("\u0015F90000FF000000\r\n");
      SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes4, 3);
      SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes4, bytes4.Length - 6);
      return bytes4;
    }
    if (this.Format == 1)
    {
      if (status > (ushort) 0)
      {
        byte[] bytes = Encoding.ASCII.GetBytes("\u0015F90000FF000000");
        SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 3);
        SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes, bytes.Length - 4);
        return bytes;
      }
      byte[] command = new byte[(this.SumCheck ? 14 : 12) + data.Length];
      Encoding.ASCII.GetBytes("\u0002F90000FF00").CopyTo((Array) command, 0);
      SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) command, 3);
      data.CopyTo((Array) command, 11);
      command[command.Length - (this.SumCheck ? 3 : 1)] = (byte) 3;
      if (this.SumCheck)
        this.SetSumCheck(command, 1, 2);
      return command;
    }
    if (this.Format == 2)
    {
      if (status > (ushort) 0)
      {
        byte[] bytes = Encoding.ASCII.GetBytes("\u001500F90000FF000000");
        SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 5);
        SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes, bytes.Length - 4);
        return bytes;
      }
      byte[] command = new byte[(this.SumCheck ? 16 /*0x10*/ : 14) + data.Length];
      Encoding.ASCII.GetBytes("\u000200F90000FF00").CopyTo((Array) command, 0);
      SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) command, 5);
      data.CopyTo((Array) command, 13);
      command[command.Length - (this.SumCheck ? 3 : 1)] = (byte) 3;
      if (this.SumCheck)
        this.SetSumCheck(command, 1, 2);
      return command;
    }
    if (this.Format == 3)
    {
      if (status > (ushort) 0)
      {
        byte[] bytes = Encoding.ASCII.GetBytes("\u0002F90000FF00QNAK0000\u0003");
        SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 3);
        SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes, bytes.Length - 5);
        return bytes;
      }
      byte[] command = new byte[(this.SumCheck ? 18 : 16 /*0x10*/) + data.Length];
      Encoding.ASCII.GetBytes("\u0002F90000FF00QACK").CopyTo((Array) command, 0);
      SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) command, 3);
      command[command.Length - (this.SumCheck ? 3 : 1)] = (byte) 3;
      data.CopyTo((Array) command, 15);
      if (this.SumCheck)
        this.SetSumCheck(command, 1, 2);
      return command;
    }
    if (this.Format != 4)
      return (byte[]) null;
    if (status > (ushort) 0)
    {
      byte[] bytes = Encoding.ASCII.GetBytes("\u0015F90000FF000000\r\n");
      SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 3);
      SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes, bytes.Length - 6);
      return bytes;
    }
    byte[] command1 = new byte[(this.SumCheck ? 16 /*0x10*/ : 14) + data.Length];
    Encoding.ASCII.GetBytes("\u0002F90000FF00").CopyTo((Array) command1, 0);
    SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) command1, 3);
    command1[command1.Length - (this.SumCheck ? 5 : 3)] = (byte) 3;
    data.CopyTo((Array) command1, 11);
    if (this.SumCheck)
      this.SetSumCheck(command1, 1, 4);
    command1[command1.Length - 2] = (byte) 13;
    command1[command1.Length - 1] = (byte) 10;
    return command1;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecA3CServer[{this.Port}]";
}
