﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.YASKAWA.Helper.IMemobus
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Profinet.YASKAWA.Helper;

/// <summary>Memobus协议的接口信息</summary>
public interface IMemobus : IReadWriteDevice, IReadWriteNet
{
  /// <summary>
  /// 获取或设置发送目标的CPU的编号信息，默认为 2<br />
  /// Get or set the CPU number information of the sending destination, the default is 2
  /// </summary>
  byte CpuTo { get; set; }

  /// <summary>
  /// 获取或设置发送源的CPU的编号信息，默认为 1<br />
  /// Get or set the number information of the sending source CPU, the default is 1
  /// </summary>
  byte CpuFrom { get; set; }
}
