﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.MessageRouter
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>
/// 自定义的消息路由类，可以实现CIP协议自定义的路由消息<br />
/// A custom message routing class that can implement custom routing messages of the CIP protocol
/// </summary>
public class MessageRouter
{
  private byte[] _router = new byte[6];

  /// <summary>
  /// 实例化一个默认的实例对象<br />
  /// instantiate a default instance object
  /// </summary>
  public MessageRouter()
  {
    this._router[0] = (byte) 1;
    new byte[4]{ (byte) 15, (byte) 2, (byte) 18, (byte) 1 }.CopyTo((Array) this._router, 1);
    this._router[5] = (byte) 12;
  }

  /// <summary>
  /// 指定路由来实例化一个对象，使用字符串的表示方式<br />
  /// Specify the route to instantiate an object, using the string representation
  /// </summary>
  /// <remarks>
  /// 路有消息支持两种格式，格式1：*********.1.12   格式2： *********.************.0<br />
  /// There are two formats for the channel message, format 1: *********.1.12 format 2: *********.************.0
  /// </remarks>
  /// <param name="router">路由信息</param>
  public MessageRouter(string router)
  {
    string[] strArray = router.Split(new char[1]{ '.' }, StringSplitOptions.RemoveEmptyEntries);
    if (strArray.Length <= 6)
    {
      if (strArray.Length != 0)
        this._router[0] = byte.Parse(strArray[0]);
      if (strArray.Length > 1)
        this._router[1] = byte.Parse(strArray[1]);
      if (strArray.Length > 2)
        this._router[2] = byte.Parse(strArray[2]);
      if (strArray.Length > 3)
        this._router[3] = byte.Parse(strArray[3]);
      if (strArray.Length > 4)
        this._router[4] = byte.Parse(strArray[4]);
      if (strArray.Length <= 5)
        return;
      this._router[5] = byte.Parse(strArray[5]);
    }
    else
    {
      if (strArray.Length != 9)
        return;
      string s = $"{strArray[3]}.{strArray[4]}.{strArray[5]}.{strArray[6]}";
      this._router = new byte[6 + s.Length];
      this._router[0] = byte.Parse(strArray[0]);
      this._router[1] = byte.Parse(strArray[1]);
      this._router[2] = (byte) (16U /*0x10*/ + (uint) byte.Parse(strArray[2]));
      this._router[3] = (byte) s.Length;
      Encoding.ASCII.GetBytes(s).CopyTo((Array) this._router, 4);
      this._router[this._router.Length - 2] = byte.Parse(strArray[7]);
      this._router[this._router.Length - 1] = byte.Parse(strArray[8]);
    }
  }

  /// <summary>
  /// 使用完全自定义的消息路由来初始化数据<br />
  /// Use fully custom message routing to initialize data
  /// </summary>
  /// <param name="router">完全自定义的路由消息</param>
  public MessageRouter(byte[] router) => this._router = router;

  /// <summary>获取路由信息</summary>
  /// <returns>路由消息的字节信息</returns>
  public byte[] GetRouter() => this._router;

  /// <summary>
  /// 获取用于发送的CIP路由报文信息<br />
  /// Get information about CIP routing packets for sending
  /// </summary>
  /// <returns>路由信息</returns>
  public byte[] GetRouterCIP()
  {
    byte[] numArray = this.GetRouter();
    if (numArray.Length % 2 == 1)
      numArray = SoftBasic.SpliceArray<byte>(numArray, new byte[1]);
    byte[] routerCip = new byte[46 + numArray.Length];
    "54022006240105f70200 00800100fe8002001b05 28a7fd03020000008084 1e00f44380841e00f443 a305".ToHexBytes().CopyTo((Array) routerCip, 0);
    numArray.CopyTo((Array) routerCip, 42);
    "********".ToHexBytes().CopyTo((Array) routerCip, 42 + numArray.Length);
    routerCip[41] = (byte) (numArray.Length / 2);
    return routerCip;
  }

  /// <summary>背板信息</summary>
  public byte Backplane
  {
    get => this._router[0];
    set => this._router[0] = value;
  }

  /// <summary>槽号信息</summary>
  public byte Slot
  {
    get => this._router[5];
    set => this._router[5] = value;
  }
}
