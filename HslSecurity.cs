﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.HslSecurity
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication;

internal class HslSecurity
{
  /// <summary>加密方法，只对当前的程序集开放</summary>
  /// <param name="enBytes">等待加密的数据</param>
  /// <returns>加密后的字节数据</returns>
  internal static byte[] ByteEncrypt(byte[] enBytes)
  {
    if (enBytes == null)
      return (byte[]) null;
    byte[] numArray = new byte[enBytes.Length];
    for (int index = 0; index < enBytes.Length; ++index)
      numArray[index] = (byte) ((uint) enBytes[index] ^ 181U);
    return numArray;
  }

  internal static void ByteEncrypt(byte[] enBytes, int offset, int count)
  {
    for (int index = offset; index < offset + count && index < enBytes.Length; ++index)
      enBytes[index] = (byte) ((uint) enBytes[index] ^ 181U);
  }

  /// <summary>解密方法，只对当前的程序集开放</summary>
  /// <param name="deBytes">等待解密的数据</param>
  /// <returns>解密后的字节数据</returns>
  internal static byte[] ByteDecrypt(byte[] deBytes) => HslSecurity.ByteEncrypt(deBytes);
}
