﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.SharpList`1
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>
/// 一个高效的数组管理类，用于高效控制固定长度的数组实现<br />
/// An efficient array management class for efficient control of fixed-length array implementations
/// </summary>
/// <typeparam name="T">泛型类型</typeparam>
public class SharpList<T>
{
  private T[] array;
  private int capacity = 2048 /*0x0800*/;
  private int count = 0;
  private int lastIndex = 0;
  private object objLock = new object();

  /// <summary>
  /// 实例化一个对象，需要指定数组的最大长度信息，以及是否从最后一个添加，之前的值为类型默认值<br />
  /// To instantiate an object, you need to specify the maximum length of the array and whether to add it from the last value before the type default
  /// </summary>
  /// <param name="count">数组的固定长度信息</param>
  /// <param name="appendLast">是否从最后一个索引开始添加</param>
  public SharpList(int count, bool appendLast = false)
  {
    if (count > (int) ushort.MaxValue)
      this.capacity = 8192 /*0x2000*/;
    else if (count > 8192 /*0x2000*/)
      this.capacity = 4096 /*0x1000*/;
    this.array = new T[this.capacity + count];
    this.count = count;
    if (!appendLast)
      return;
    this.lastIndex = count;
  }

  /// <summary>
  /// 获取数组设定时候的固定数量<br />
  /// Gets the fixed number of arrays when they are set
  /// </summary>
  public int Count => this.count;

  private void addItem(T value)
  {
    if (this.lastIndex >= this.capacity + this.count)
    {
      T[] destinationArray = new T[this.capacity + this.count];
      Array.Copy((Array) this.array, this.capacity, (Array) destinationArray, 0, this.count);
      this.array = destinationArray;
      this.lastIndex = this.count;
    }
    this.array[this.lastIndex++] = value;
  }

  /// <summary>
  /// 新增一个数据值<br />
  /// Add a data value
  /// </summary>
  /// <param name="value">数据值</param>
  public void Add(T value)
  {
    lock (this.objLock)
      this.addItem(value);
  }

  /// <summary>
  /// 将一个可以遍历的数据集合一次性批量的添加到当前的数据缓存中<br />
  /// Adds a traversable data set to the current data cache in one batch
  /// </summary>
  /// <param name="values">批量数据信息</param>
  public void Add(IEnumerable<T> values)
  {
    lock (this.objLock)
    {
      foreach (T obj in values)
        this.addItem(obj);
    }
  }

  /// <summary>
  /// 获取数据的浅拷贝数组对象<br />
  /// Get array value of data
  /// </summary>
  /// <returns>数组值</returns>
  public T[] ToArray()
  {
    T[] destinationArray = (T[]) null;
    lock (this.objLock)
    {
      if (this.lastIndex < this.count)
      {
        destinationArray = new T[this.lastIndex];
        Array.Copy((Array) this.array, 0, (Array) destinationArray, 0, this.lastIndex);
      }
      else
      {
        destinationArray = new T[this.count];
        Array.Copy((Array) this.array, this.lastIndex - this.count, (Array) destinationArray, 0, this.count);
      }
    }
    return destinationArray;
  }

  /// <summary>
  /// 获取或设置指定索引的位置的数据<br />
  /// Gets or sets the data at the specified index
  /// </summary>
  /// <param name="index">索引位置</param>
  /// <returns>当前索引的数据值</returns>
  public T this[int index]
  {
    get
    {
      if (index < 0)
        throw new IndexOutOfRangeException("Index must larger than zero");
      if (index >= this.count)
        throw new IndexOutOfRangeException("Index must smaller than array length");
      T obj = default (T);
      lock (this.objLock)
        obj = this.lastIndex >= this.count ? this.array[index + this.lastIndex - this.count] : this.array[index];
      return obj;
    }
    set
    {
      if (index < 0)
        throw new IndexOutOfRangeException("Index must larger than zero");
      if (index >= this.count)
        throw new IndexOutOfRangeException("Index must smaller than array length");
      lock (this.objLock)
      {
        if (this.lastIndex < this.count)
          this.array[index] = value;
        else
          this.array[index + this.lastIndex - this.count] = value;
      }
    }
  }

  /// <summary>
  /// 获取最后一个值，如果从来没有添加过，则引发异常<br />
  /// Gets the last value and throws an exception if it has never been added
  /// </summary>
  /// <returns>值信息</returns>
  public T LastValue()
  {
    T obj = default (T);
    lock (this.objLock)
    {
      if (this.lastIndex - 1 < this.count + this.capacity)
        obj = this.array[this.lastIndex - 1];
    }
    return obj;
  }

  /// <inheritdoc />
  public override string ToString() => $"SharpList<{typeof (T)}>[{this.capacity}]";
}
