﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.McType
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>MC协议的类型</summary>
public enum McType
{
  /// <summary>基于二进制的MC协议</summary>
  McBinary,
  /// <summary>基于ASCII格式的MC协议</summary>
  MCAscii,
  /// <summary>基于R系列的二进制的MC协议</summary>
  McRBinary,
  /// <summary>基于R系列的ASCII格式的MC协议</summary>
  McRAscii,
}
