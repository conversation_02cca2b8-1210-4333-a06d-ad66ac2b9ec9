﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.ModBus.ModBusMonitorAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.ModBus;

/// <summary>服务器端提供的数据监视服务</summary>
public class ModBusMonitorAddress
{
  /// <summary>本次数据监视的地址</summary>
  public ushort Address { get; set; }

  /// <summary>数据写入时触发的事件</summary>
  public event Action<ModBusMonitorAddress, short> OnWrite;

  /// <summary>数据改变时触发的事件</summary>
  public event Action<ModBusMonitorAddress, short, short> OnChange;

  /// <summary>强制设置触发事件</summary>
  /// <param name="value">数据值信息</param>
  public void SetValue(short value)
  {
    Action<ModBusMonitorAddress, short> onWrite = this.OnWrite;
    if (onWrite == null)
      return;
    onWrite(this, value);
  }

  /// <summary>强制设置触发值变更事件</summary>
  /// <param name="before">变更前的值</param>
  /// <param name="after">变更后的值</param>
  public void SetChangeValue(short before, short after)
  {
    if ((int) before == (int) after)
      return;
    Action<ModBusMonitorAddress, short, short> onChange = this.OnChange;
    if (onChange != null)
      onChange(this, before, after);
  }
}
