﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.DtuStatus
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>DTU的状态信息</summary>
public enum DtuStatus
{
  /// <summary>新创建的对象</summary>
  Create,
  /// <summary>连接中</summary>
  Connecting,
  /// <summary>已连接</summary>
  Connected,
  /// <summary>已关闭</summary>
  Closed,
}
