﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslRedisHashFieldAttribute
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>对应redis的一个哈希信息的内容</summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class HslRedisHashFieldAttribute : Attribute
{
  /// <summary>哈希键值的名称</summary>
  public string HaskKey { get; set; }

  /// <summary>当前的哈希域名称</summary>
  public string Field { get; set; }

  /// <summary>根据键名来读取写入当前的哈希的单个信息</summary>
  /// <param name="hashKey">哈希键名</param>
  /// <param name="filed">哈希域名称</param>
  public HslRedisHashFieldAttribute(string hashKey, string filed)
  {
    this.HaskKey = hashKey;
    this.Field = filed;
  }
}
