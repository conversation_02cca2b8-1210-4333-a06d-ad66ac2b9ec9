﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.Hyundai.HyundaiData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Robot.Hyundai;

/// <summary>Hyundai的数据类对象</summary>
public class HyundaiData
{
  /// <summary>实例化一个默认的对象</summary>
  public HyundaiData() => this.Data = new double[6];

  /// <summary>通过缓存对象实例化一个</summary>
  /// <param name="buffer"></param>
  public HyundaiData(byte[] buffer) => this.LoadBy(buffer);

  /// <summary>命令码，从控制器发数据到PC和PC到控制器，两者的命令不一样</summary>
  public char Command { get; set; }

  /// <summary>虚标记</summary>
  public string CharDummy { get; set; }

  /// <summary>状态码</summary>
  public int State { get; set; }

  /// <summary>标记数据，从PLC发送给机器人的数据，原封不动的返回</summary>
  public int Count { get; set; }

  /// <summary>虚标记</summary>
  public int IntDummy { get; set; }

  /// <summary>关节坐标数据，包含X,Y,Z,W,P,R，三个位置数据，三个角度数据。</summary>
  public double[] Data { get; set; }

  /// <summary>从字节数组的指定索引开始加载现在机器人的数据</summary>
  /// <param name="buffer">原始的字节数据</param>
  /// <param name="index">起始的索引信息</param>
  public void LoadBy(byte[] buffer, int index = 0)
  {
    this.Command = (char) buffer[index];
    this.CharDummy = Encoding.ASCII.GetString(buffer, index + 1, 3);
    this.State = BitConverter.ToInt32(buffer, index + 4);
    this.Count = BitConverter.ToInt32(buffer, index + 8);
    this.IntDummy = BitConverter.ToInt32(buffer, index + 12);
    this.Data = new double[6];
    for (int index1 = 0; index1 < this.Data.Length; ++index1)
      this.Data[index1] = index1 >= 3 ? BitConverter.ToDouble(buffer, index + 16 /*0x10*/ + 8 * index1) * 180.0 / Math.PI : BitConverter.ToDouble(buffer, index + 16 /*0x10*/ + 8 * index1) * 1000.0;
  }

  /// <summary>将现代机器人的数据转换为字节数组</summary>
  /// <returns>字节数组</returns>
  public byte[] ToBytes()
  {
    byte[] bytes = new byte[64 /*0x40*/];
    bytes[0] = (byte) this.Command;
    if (!string.IsNullOrEmpty(this.CharDummy))
      Encoding.ASCII.GetBytes(this.CharDummy).CopyTo((Array) bytes, 1);
    BitConverter.GetBytes(this.State).CopyTo((Array) bytes, 4);
    BitConverter.GetBytes(this.Count).CopyTo((Array) bytes, 8);
    BitConverter.GetBytes(this.IntDummy).CopyTo((Array) bytes, 12);
    for (int index = 0; index < this.Data.Length; ++index)
    {
      if (index < 3)
        BitConverter.GetBytes(this.Data[index] / 1000.0).CopyTo((Array) bytes, 16 /*0x10*/ + 8 * index);
      else
        BitConverter.GetBytes(this.Data[index] * Math.PI / 180.0).CopyTo((Array) bytes, 16 /*0x10*/ + 8 * index);
    }
    return bytes;
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"HyundaiData:Cmd[{this.Command},{this.CharDummy},{this.State},{this.Count},{this.IntDummy}] Data:{SoftBasic.ArrayFormat<double>(this.Data)}";
  }
}
