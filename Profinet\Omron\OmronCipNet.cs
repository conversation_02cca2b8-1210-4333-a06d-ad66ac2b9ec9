﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronCipNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.AllenBradley;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// 欧姆龙PLC的CIP协议的类，支持NJ,NX,NY系列PLC，支持tag名的方式读写数据，假设你读取的是局部变量，那么使用 Program:MainProgram.变量名<br />
/// Omron PLC's CIP protocol class, support NJ, NX, NY series PLC, support tag name read and write data, assuming you read local variables, then use Program: MainProgram. Variable name
/// </summary>
public class OmronCipNet : AllenBradleyNet
{
  /// <summary>
  /// Instantiate a communication object for a OmronCipNet PLC protocol
  /// </summary>
  public OmronCipNet()
  {
  }

  /// <summary>
  /// Specify the IP address and port to instantiate a communication object for a OmronCipNet PLC protocol
  /// </summary>
  /// <param name="ipAddress">PLC IpAddress</param>
  /// <param name="port">PLC Port</param>
  public OmronCipNet(string ipAddress, int port = 44818)
    : base(ipAddress, port)
  {
  }

  private bool CheckAddressArrayTag(string address) => Regex.IsMatch(address, "\\[[0-9]+\\]$");

  /// <inheritdoc />
  protected override bool GetBoolWritePadding() => true;

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return length > (ushort) 1 ? (this.CheckAddressArrayTag(address) ? this.Read(new string[1]
    {
      address
    }, new ushort[1]{ length }) : this.Read(new string[1]
    {
      address
    }, new ushort[1]{ (ushort) 1 })) : this.Read(new string[1]
    {
      address
    }, new ushort[1]{ length });
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (address.StartsWith("i="))
      return base.ReadBool(address, length);
    OperateResult<byte[]> result = this.Read(address, length);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    return this.CheckAddressArrayTag(address) ? OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) result.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).Take<bool>((int) length).ToArray<bool>()) : OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(result.Content, (int) length));
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> result = this.Read(address, length);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    try
    {
      int count = (int) this.ByteTransform.TransUInt16(result.Content, 0);
      return OperateResult.CreateSuccessResult<string>(encoding.GetString(result.Content, 2, count));
    }
    catch (Exception ex)
    {
      return new OperateResult<string>($"Parse failed: {ex.Message}{Environment.NewLine}Source: {result.Content.ToHexString(' ')}");
    }
  }

  /// <inheritdoc />
  protected override int GetWriteValueLength(string address, int length)
  {
    return !this.CheckAddressArrayTag(address) ? 1 : length;
  }

  /// <inheritdoc />
  public override OperateResult<T> ReadStruct<T>(string address, ushort length)
  {
    return ReadWriteNetHelper.ReadStruct<T>((IReadWriteNet) this, address, length, this.ByteTransform, 2);
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    if (string.IsNullOrEmpty(value))
      value = string.Empty;
    byte[] numArray = SoftBasic.SpliceArray<byte>(new byte[2], SoftBasic.ArrayExpandToLengthEven<byte>(encoding.GetBytes(value)));
    numArray[0] = BitConverter.GetBytes(numArray.Length - 2)[0];
    numArray[1] = BitConverter.GetBytes(numArray.Length - 2)[1];
    return this.WriteTag(address, (ushort) 208 /*0xD0*/, numArray, 1);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByte", "")]
  public override OperateResult Write(string address, byte value)
  {
    return this.WriteTag(address, (ushort) 209, new byte[1]
    {
      value
    }, 1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronCipNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    if (length > (ushort) 1)
    {
      if (this.CheckAddressArrayTag(address))
      {
        OperateResult<byte[]> operateResult = await this.ReadAsync(new string[1]
        {
          address
        }, new ushort[1]{ length });
        return operateResult;
      }
      OperateResult<byte[]> operateResult1 = await this.ReadAsync(new string[1]
      {
        address
      }, new ushort[1]{ (ushort) 1 });
      return operateResult1;
    }
    OperateResult<byte[]> operateResult2 = await this.ReadAsync(new string[1]
    {
      address
    }, new ushort[1]{ length });
    return operateResult2;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (address.StartsWith("i="))
    {
      OperateResult<bool[]> operateResult = await base.ReadBoolAsync(address, length);
      return operateResult;
    }
    OperateResult<byte[]> read = await this.ReadAsync(address, length);
    return read.IsSuccess ? (!this.CheckAddressArrayTag(address) ? OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(read.Content, (int) length)) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) read.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).Take<bool>((int) length).ToArray<bool>())) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, length);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    try
    {
      int strLen = (int) this.ByteTransform.TransUInt16(read.Content, 0);
      return OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content, 2, strLen));
    }
    catch (Exception ex)
    {
      return new OperateResult<string>($"Parse failed: {ex.Message}{Environment.NewLine}Source: {read.Content.ToHexString(' ')}");
    }
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    if (string.IsNullOrEmpty(value))
      value = string.Empty;
    byte[] data = SoftBasic.SpliceArray<byte>(new byte[2], SoftBasic.ArrayExpandToLengthEven<byte>(encoding.GetBytes(value)));
    data[0] = BitConverter.GetBytes(data.Length - 2)[0];
    data[1] = BitConverter.GetBytes(data.Length - 2)[1];
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 208 /*0xD0*/, data, 1);
    data = (byte[]) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronCipNet.Write(System.String,System.Byte)" />
  public override async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 209, new byte[1]
    {
      value
    }, 1);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronCipNet[{this.IpAddress}:{this.Port}]";
}
