﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyMicroCip
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>
/// AB PLC的cip通信实现类，适用Micro800系列控制系统<br />
/// AB PLC's cip communication implementation class, suitable for Micro800 series control system
/// </summary>
public class AllenBradleyMicroCip : AllenBradleyNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.#ctor" />
  public AllenBradleyMicroCip()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.#ctor(System.String,System.Int32)" />
  public AllenBradleyMicroCip(string ipAddress, int port = 44818)
    : base(ipAddress, port)
  {
  }

  /// <inheritdoc />
  protected override byte[] PackCommandService(byte[] portSlot, params byte[][] cips)
  {
    return AllenBradleyHelper.PackCleanCommandService(portSlot, cips);
  }

  /// <inheritdoc />
  public override string ToString() => $"AllenBradleyMicroCip[{this.IpAddress}:{this.Port}]";
}
