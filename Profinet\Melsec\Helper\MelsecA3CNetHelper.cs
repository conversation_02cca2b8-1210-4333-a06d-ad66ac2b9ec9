﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>MelsecA3CNet1协议通信的辅助类</summary>
public class MelsecA3CNetHelper
{
  /// <summary>将命令进行打包传送，可选站号及是否和校验机制</summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <param name="mcCommand">mc协议的命令</param>
  /// <param name="station">PLC的站号</param>
  /// <returns>最终的原始报文信息</returns>
  public static byte[] PackCommand(IReadWriteA3C plc, byte[] mcCommand, byte station = 0)
  {
    MemoryStream memoryStream = new MemoryStream();
    if (plc.Format != 3)
      memoryStream.WriteByte((byte) 5);
    else
      memoryStream.WriteByte((byte) 2);
    if (plc.Format == 2)
    {
      memoryStream.WriteByte((byte) 48 /*0x30*/);
      memoryStream.WriteByte((byte) 48 /*0x30*/);
    }
    memoryStream.WriteByte((byte) 70);
    memoryStream.WriteByte((byte) 57);
    memoryStream.WriteByte(SoftBasic.BuildAsciiBytesFrom(station)[0]);
    memoryStream.WriteByte(SoftBasic.BuildAsciiBytesFrom(station)[1]);
    memoryStream.WriteByte((byte) 48 /*0x30*/);
    memoryStream.WriteByte((byte) 48 /*0x30*/);
    memoryStream.WriteByte((byte) 70);
    memoryStream.WriteByte((byte) 70);
    memoryStream.WriteByte((byte) 48 /*0x30*/);
    memoryStream.WriteByte((byte) 48 /*0x30*/);
    memoryStream.Write(mcCommand, 0, mcCommand.Length);
    if (plc.Format == 3)
      memoryStream.WriteByte((byte) 3);
    if (plc.SumCheck)
    {
      byte[] array = memoryStream.ToArray();
      int num = 0;
      for (int index = 1; index < array.Length; ++index)
        num += (int) array[index];
      memoryStream.WriteByte(SoftBasic.BuildAsciiBytesFrom((byte) num)[0]);
      memoryStream.WriteByte(SoftBasic.BuildAsciiBytesFrom((byte) num)[1]);
    }
    if (plc.Format == 4)
    {
      memoryStream.WriteByte((byte) 13);
      memoryStream.WriteByte((byte) 10);
    }
    byte[] array1 = memoryStream.ToArray();
    memoryStream.Dispose();
    return array1;
  }

  private static int GetErrorCodeOrDataStartIndex(IReadWriteA3C plc)
  {
    int orDataStartIndex = 11;
    switch (plc.Format)
    {
      case 1:
        orDataStartIndex = 11;
        break;
      case 2:
        orDataStartIndex = 13;
        break;
      case 3:
        orDataStartIndex = 15;
        break;
      case 4:
        orDataStartIndex = 11;
        break;
    }
    return orDataStartIndex;
  }

  /// <summary>根据PLC返回的数据信息，获取到实际的数据内容</summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <param name="response">PLC返回的数据信息</param>
  /// <returns>带有是否成功的读取结果对象内容</returns>
  public static OperateResult<byte[]> ExtraReadActualResponse(IReadWriteA3C plc, byte[] response)
  {
    try
    {
      int orDataStartIndex = MelsecA3CNetHelper.GetErrorCodeOrDataStartIndex(plc);
      if (plc.Format == 1 || plc.Format == 2 || plc.Format == 4)
      {
        if (response[0] == (byte) 21)
        {
          int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, orDataStartIndex, 4), 16 /*0x10*/);
          return new OperateResult<byte[]>(int32, MelsecHelper.GetErrorDescription(int32));
        }
        if (response[0] != (byte) 2)
          return new OperateResult<byte[]>((int) response[0], "Read Faild:" + SoftBasic.GetAsciiStringRender(response));
      }
      else if (plc.Format == 3)
      {
        string str = Encoding.ASCII.GetString(response, 11, 4);
        if (str == "QNAK")
        {
          int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, orDataStartIndex, 4), 16 /*0x10*/);
          return new OperateResult<byte[]>(int32, MelsecHelper.GetErrorDescription(int32));
        }
        if (str != "QACK")
          return new OperateResult<byte[]>((int) response[0], "Read Faild:" + SoftBasic.GetAsciiStringRender(response));
      }
      int num = -1;
      for (int index = orDataStartIndex; index < response.Length; ++index)
      {
        if (response[index] == (byte) 3)
        {
          num = index;
          break;
        }
      }
      if (num == -1)
        num = response.Length;
      return OperateResult.CreateSuccessResult<byte[]>(response.SelectMiddle<byte>(orDataStartIndex, num - orDataStartIndex));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"ExtraReadActualResponse Wrong:{ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
    }
  }

  private static OperateResult CheckWriteResponse(IReadWriteA3C plc, byte[] response)
  {
    int orDataStartIndex = MelsecA3CNetHelper.GetErrorCodeOrDataStartIndex(plc);
    try
    {
      if (plc.Format == 1 || plc.Format == 2)
      {
        if (response[0] == (byte) 21)
        {
          int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, orDataStartIndex, 4), 16 /*0x10*/);
          return (OperateResult) new OperateResult<byte[]>(int32, MelsecHelper.GetErrorDescription(int32));
        }
        if (response[0] != (byte) 6)
          return (OperateResult) new OperateResult<byte[]>((int) response[0], "Write Faild:" + SoftBasic.GetAsciiStringRender(response));
      }
      else if (plc.Format == 3)
      {
        if (response[0] != (byte) 2)
          return (OperateResult) new OperateResult<byte[]>((int) response[0], "Write Faild:" + SoftBasic.GetAsciiStringRender(response));
        string str = Encoding.ASCII.GetString(response, 11, 4);
        if (str == "QNAK")
        {
          int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, orDataStartIndex, 4), 16 /*0x10*/);
          return (OperateResult) new OperateResult<byte[]>(int32, MelsecHelper.GetErrorDescription(int32));
        }
        if (str != "QACK")
          return (OperateResult) new OperateResult<byte[]>((int) response[0], "Write Faild:" + SoftBasic.GetAsciiStringRender(response));
      }
      else if (plc.Format == 4)
      {
        if (response[0] == (byte) 21)
        {
          int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, orDataStartIndex, 4), 16 /*0x10*/);
          return (OperateResult) new OperateResult<byte[]>(int32, MelsecHelper.GetErrorDescription(int32));
        }
        if (response[0] != (byte) 6)
          return (OperateResult) new OperateResult<byte[]>((int) response[0], "Write Faild:" + SoftBasic.GetAsciiStringRender(response));
      }
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<byte[]>($"CheckWriteResponse failed: {ex.Message}{Environment.NewLine}Content: {SoftBasic.GetAsciiStringRender(response)}");
    }
  }

  /// <summary>
  /// 批量读取PLC的数据，以字为单位，支持读取X,Y,M,S,D,T,C，具体的地址范围需要根据PLC型号来确认
  /// </summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>读取结果信息</returns>
  public static OperateResult<byte[]> Read(IReadWriteA3C plc, string address, ushort length)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, length, false);
    if (!melsecFrom.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) melsecFrom);
    List<byte> byteList = new List<byte>();
    ushort num1 = 0;
    while ((int) num1 < (int) length)
    {
      ushort num2 = (ushort) Math.Min((int) length - (int) num1, McHelper.GetReadWordLength(McType.MCAscii));
      melsecFrom.Content.Length = num2;
      byte[] mcCommand = McAsciiHelper.BuildAsciiReadMcCoreCommand(melsecFrom.Content, false);
      OperateResult<byte[]> operateResult1 = plc.ReadFromCoreServer(MelsecA3CNetHelper.PackCommand(plc, mcCommand, parameter));
      if (!operateResult1.IsSuccess)
        return operateResult1;
      OperateResult<byte[]> operateResult2 = MelsecA3CNetHelper.ExtraReadActualResponse(plc, operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      byteList.AddRange((IEnumerable<byte>) MelsecHelper.TransAsciiByteArrayToByteArray(operateResult2.Content));
      num1 += num2;
      if (melsecFrom.Content.McDataType.DataType == (byte) 0)
        melsecFrom.Content.AddressStart += (int) num2;
      else
        melsecFrom.Content.AddressStart += (int) num2 * 16 /*0x10*/;
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper.Read(HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteA3C plc,
    string address,
    ushort length)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> addressResult = McAddressData.ParseMelsecFrom(address, length, false);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
    List<byte> bytesContent = new List<byte>();
    ushort alreadyFinished = 0;
    while ((int) alreadyFinished < (int) length)
    {
      ushort readLength = (ushort) Math.Min((int) length - (int) alreadyFinished, McHelper.GetReadWordLength(McType.MCAscii));
      addressResult.Content.Length = readLength;
      byte[] command = McAsciiHelper.BuildAsciiReadMcCoreCommand(addressResult.Content, false);
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(MelsecA3CNetHelper.PackCommand(plc, command, stat));
      if (!read.IsSuccess)
        return read;
      OperateResult<byte[]> check = MelsecA3CNetHelper.ExtraReadActualResponse(plc, read.Content);
      if (!check.IsSuccess)
        return check;
      bytesContent.AddRange((IEnumerable<byte>) MelsecHelper.TransAsciiByteArrayToByteArray(check.Content));
      alreadyFinished += readLength;
      if (addressResult.Content.McDataType.DataType == (byte) 0)
        addressResult.Content.AddressStart += (int) readLength;
      else
        addressResult.Content.AddressStart += (int) readLength * 16 /*0x10*/;
      command = (byte[]) null;
      read = (OperateResult<byte[]>) null;
      check = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(bytesContent.ToArray());
  }

  /// <summary>
  /// 批量写入PLC的数据，以字为单位，也就是说最少2个字节信息，支持X,Y,M,S,D,T,C，具体的地址范围需要根据PLC型号来确认
  /// </summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(IReadWriteA3C plc, string address, byte[] value)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, (ushort) 0, false);
    if (!melsecFrom.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) melsecFrom);
    byte[] mcCommand = McAsciiHelper.BuildAsciiWriteWordCoreCommand(melsecFrom.Content, value);
    OperateResult<byte[]> operateResult = plc.ReadFromCoreServer(MelsecA3CNetHelper.PackCommand(plc, mcCommand, parameter));
    return !operateResult.IsSuccess ? (OperateResult) operateResult : MelsecA3CNetHelper.CheckWriteResponse(plc, operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper.Write(HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteA3C plc,
    string address,
    byte[] value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> addressResult = McAddressData.ParseMelsecFrom(address, (ushort) 0, false);
    if (!addressResult.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
    byte[] command = McAsciiHelper.BuildAsciiWriteWordCoreCommand(addressResult.Content, value);
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(MelsecA3CNetHelper.PackCommand(plc, command, stat));
    return read.IsSuccess ? MelsecA3CNetHelper.CheckWriteResponse(plc, read.Content) : (OperateResult) read;
  }

  /// <summary>批量读取bool类型数据，支持的类型为X,Y,S,T,C，具体的地址范围取决于PLC的类型</summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <param name="address">地址信息，比如X10,Y17，注意X，Y的地址是8进制的</param>
  /// <param name="length">读取的长度</param>
  /// <returns>读取结果信息</returns>
  public static OperateResult<bool[]> ReadBool(IReadWriteA3C plc, string address, ushort length)
  {
    if (address.IndexOf('.') > 0)
      return HslHelper.ReadBool((IReadWriteNet) plc, address, length);
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, length, true);
    if (!melsecFrom.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) melsecFrom);
    List<bool> boolList = new List<bool>();
    ushort num1 = 0;
    while ((int) num1 < (int) length)
    {
      ushort num2 = (ushort) Math.Min((int) length - (int) num1, McHelper.GetReadBoolLength(McType.MCAscii));
      melsecFrom.Content.Length = num2;
      byte[] mcCommand = McAsciiHelper.BuildAsciiReadMcCoreCommand(melsecFrom.Content, true);
      OperateResult<byte[]> result1 = plc.ReadFromCoreServer(MelsecA3CNetHelper.PackCommand(plc, mcCommand, parameter));
      if (!result1.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
      OperateResult<byte[]> result2 = MelsecA3CNetHelper.ExtraReadActualResponse(plc, result1.Content);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      boolList.AddRange((IEnumerable<bool>) ((IEnumerable<byte>) result2.Content).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)).ToArray<bool>());
      num1 += num2;
      melsecFrom.Content.AddressStart += (int) num2;
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper.ReadBool(HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteA3C plc,
    string address,
    ushort length)
  {
    if (address.IndexOf('.') > 0)
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) plc, address, length);
      return operateResult;
    }
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> addressResult = McAddressData.ParseMelsecFrom(address, length, true);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) addressResult);
    List<bool> boolContent = new List<bool>();
    ushort alreadyFinished = 0;
    while ((int) alreadyFinished < (int) length)
    {
      ushort readLength = (ushort) Math.Min((int) length - (int) alreadyFinished, McHelper.GetReadBoolLength(McType.MCAscii));
      addressResult.Content.Length = readLength;
      byte[] command = McAsciiHelper.BuildAsciiReadMcCoreCommand(addressResult.Content, true);
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(MelsecA3CNetHelper.PackCommand(plc, command, stat));
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult<byte[]> check = MelsecA3CNetHelper.ExtraReadActualResponse(plc, read.Content);
      if (!check.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) check);
      boolContent.AddRange((IEnumerable<bool>) ((IEnumerable<byte>) check.Content).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)).ToArray<bool>());
      alreadyFinished += readLength;
      addressResult.Content.AddressStart += (int) readLength;
      command = (byte[]) null;
      read = (OperateResult<byte[]>) null;
      check = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolContent.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA3CNetOverTcp.Write(System.String,System.Boolean[])" />
  public static OperateResult Write(IReadWriteA3C plc, string address, bool[] value)
  {
    if (plc.EnableWriteBitToWordRegister && address.Contains("."))
      return ReadWriteNetHelper.WriteBoolWithWord((IReadWriteNet) plc, address, value);
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, (ushort) 0, true);
    if (!melsecFrom.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) melsecFrom);
    byte[] mcCommand = McAsciiHelper.BuildAsciiWriteBitCoreCommand(melsecFrom.Content, value);
    OperateResult<byte[]> operateResult = plc.ReadFromCoreServer(MelsecA3CNetHelper.PackCommand(plc, mcCommand, parameter));
    return !operateResult.IsSuccess ? (OperateResult) operateResult : MelsecA3CNetHelper.CheckWriteResponse(plc, operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper.Write(HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteA3C plc,
    string address,
    bool[] value)
  {
    if (plc.EnableWriteBitToWordRegister && address.Contains("."))
    {
      OperateResult operateResult = await ReadWriteNetHelper.WriteBoolWithWordAsync((IReadWriteNet) plc, address, value).ConfigureAwait(false);
      return operateResult;
    }
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<McAddressData> addressResult = McAddressData.ParseMelsecFrom(address, (ushort) 0, true);
    if (!addressResult.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) addressResult);
    byte[] command = McAsciiHelper.BuildAsciiWriteBitCoreCommand(addressResult.Content, value);
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(MelsecA3CNetHelper.PackCommand(plc, command, stat));
    return read.IsSuccess ? MelsecA3CNetHelper.CheckWriteResponse(plc, read.Content) : (OperateResult) read;
  }

  /// <summary>远程Run操作</summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult RemoteRun(IReadWriteA3C plc)
  {
    OperateResult<byte[]> operateResult = plc.ReadFromCoreServer(MelsecA3CNetHelper.PackCommand(plc, Encoding.ASCII.GetBytes("1001000000010000"), plc.Station));
    return !operateResult.IsSuccess ? (OperateResult) operateResult : MelsecA3CNetHelper.CheckWriteResponse(plc, operateResult.Content);
  }

  /// <summary>远程Stop操作</summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult RemoteStop(IReadWriteA3C plc)
  {
    OperateResult<byte[]> operateResult = plc.ReadFromCoreServer(MelsecA3CNetHelper.PackCommand(plc, Encoding.ASCII.GetBytes("100200000001"), plc.Station));
    return !operateResult.IsSuccess ? (OperateResult) operateResult : MelsecA3CNetHelper.CheckWriteResponse(plc, operateResult.Content);
  }

  /// <summary>读取PLC的型号信息</summary>
  /// <param name="plc">PLC设备通信对象</param>
  /// <returns>返回型号的结果对象</returns>
  public static OperateResult<string> ReadPlcType(IReadWriteA3C plc)
  {
    OperateResult<byte[]> result1 = plc.ReadFromCoreServer(MelsecA3CNetHelper.PackCommand(plc, Encoding.ASCII.GetBytes("01010000"), plc.Station));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = MelsecA3CNetHelper.ExtraReadActualResponse(plc, result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result2) : OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(result2.Content, 0, 16 /*0x10*/).TrimEnd());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper.RemoteRun(HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C)" />
  public static async Task<OperateResult> RemoteRunAsync(IReadWriteA3C plc)
  {
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(MelsecA3CNetHelper.PackCommand(plc, Encoding.ASCII.GetBytes("1001000000010000"), plc.Station));
    OperateResult operateResult = read.IsSuccess ? MelsecA3CNetHelper.CheckWriteResponse(plc, read.Content) : (OperateResult) read;
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper.RemoteStop(HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C)" />
  public static async Task<OperateResult> RemoteStopAsync(IReadWriteA3C plc)
  {
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(MelsecA3CNetHelper.PackCommand(plc, Encoding.ASCII.GetBytes("100200000001"), plc.Station));
    OperateResult operateResult = read.IsSuccess ? MelsecA3CNetHelper.CheckWriteResponse(plc, read.Content) : (OperateResult) read;
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecA3CNetHelper.ReadPlcType(HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C)" />
  public static async Task<OperateResult<string>> ReadPlcTypeAsync(IReadWriteA3C plc)
  {
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(MelsecA3CNetHelper.PackCommand(plc, Encoding.ASCII.GetBytes("01010000"), plc.Station));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    OperateResult<byte[]> check = MelsecA3CNetHelper.ExtraReadActualResponse(plc, read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(check.Content, 0, 16 /*0x10*/).TrimEnd()) : OperateResult.CreateFailedResult<string>((OperateResult) check);
  }
}
