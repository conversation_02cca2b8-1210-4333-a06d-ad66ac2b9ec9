﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronHostLinkCModeServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>欧姆龙的HostLinkCMode协议的虚拟服务器</summary>
public class OmronHostLinkCModeServer : OmronFinsServer
{
  private byte operationMode = 1;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsServer.#ctor" />
  public OmronHostLinkCModeServer()
  {
    this.LogMsgFormatBinary = false;
    this.connectionInitialization = false;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.UnitNumber" />
  public byte UnitNumber { get; set; }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return receive.Length < 9 ? new OperateResult<byte[]>("Uknown Data：" + SoftBasic.GetAsciiStringRender(receive)) : OperateResult.CreateSuccessResult<byte[]>(this.ReadFromFinsCore(receive));
  }

  /// <inheritdoc />
  protected override byte[] ReadFromFinsCore(byte[] finsCore)
  {
    string str = Encoding.ASCII.GetString(finsCore, 3, 2);
    if (str == "RD" || str == "RR" || str == "RL" || str == "RH" || str == "RJ" || str == "RE")
    {
      SoftBuffer softBuffer;
      switch (str)
      {
        case "RD":
          softBuffer = this.dBuffer;
          break;
        case "RR":
          softBuffer = this.cioBuffer;
          break;
        case "RL":
          softBuffer = this.cioBuffer;
          break;
        case "RH":
          softBuffer = this.hBuffer;
          break;
        case "RJ":
          softBuffer = this.arBuffer;
          break;
        case "RE":
          softBuffer = this.emBuffer;
          break;
        default:
          return this.PackCommand(22, finsCore, (byte[]) null);
      }
      if (str == "RE")
      {
        int int32 = Convert.ToInt32(Encoding.ASCII.GetString(finsCore, 7, 4));
        ushort uint16 = Convert.ToUInt16(Encoding.ASCII.GetString(finsCore, 11, 4));
        byte[] bytes = softBuffer.GetBytes(int32 * 2, (int) uint16 * 2);
        return this.PackCommand(0, finsCore, bytes);
      }
      int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(finsCore, 5, 4));
      ushort uint16_1 = Convert.ToUInt16(Encoding.ASCII.GetString(finsCore, 9, 4));
      byte[] bytes1 = softBuffer.GetBytes(int32_1 * 2, (int) uint16_1 * 2);
      return this.PackCommand(0, finsCore, bytes1);
    }
    if (str == "WD" || str == "WR" || str == "WL" || str == "WH" || str == "WJ" || str == "WE")
    {
      SoftBuffer softBuffer;
      switch (str)
      {
        case "WD":
          softBuffer = this.dBuffer;
          break;
        case "WR":
          softBuffer = this.cioBuffer;
          break;
        case "WL":
          softBuffer = this.cioBuffer;
          break;
        case "WH":
          softBuffer = this.hBuffer;
          break;
        case "WJ":
          softBuffer = this.arBuffer;
          break;
        case "WE":
          softBuffer = this.emBuffer;
          break;
        default:
          return this.PackCommand(22, finsCore, (byte[]) null);
      }
      if (str == "WE")
      {
        int int32 = Convert.ToInt32(Encoding.ASCII.GetString(finsCore, 7, 4));
        byte[] hexBytes = Encoding.ASCII.GetString(finsCore, 11, finsCore.Length - 11).ToHexBytes();
        softBuffer.SetBytes(hexBytes, int32 * 2);
        return this.PackCommand(0, finsCore, (byte[]) null);
      }
      int int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(finsCore, 5, 4));
      byte[] hexBytes1 = Encoding.ASCII.GetString(finsCore, 9, finsCore.Length - 9).ToHexBytes();
      softBuffer.SetBytes(hexBytes1, int32_2 * 2);
      return this.PackCommand(0, finsCore, (byte[]) null);
    }
    switch (str)
    {
      case "MM":
        return this.PackCommand(0, finsCore, new byte[1]
        {
          (byte) 48 /*0x30*/
        });
      case "MS":
        return this.PackCommand(0, finsCore, new byte[2]
        {
          this.operationMode,
          (byte) 48 /*0x30*/
        });
      case "SC":
        byte num = Convert.ToByte(Encoding.ASCII.GetString(finsCore, 5, 2), 16 /*0x10*/);
        if (num >= (byte) 0 && num <= (byte) 2)
          this.operationMode = num;
        return this.PackCommand(0, finsCore, (byte[]) null);
      default:
        return this.PackCommand(22, finsCore, (byte[]) null);
    }
  }

  /// <inheritdoc />
  protected override byte[] PackCommand(int status, byte[] finsCore, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    data = SoftBasic.BytesToAsciiBytes(data);
    byte[] destinationArray = new byte[11 + data.Length];
    Encoding.ASCII.GetBytes("@0000").CopyTo((Array) destinationArray, 0);
    Encoding.ASCII.GetBytes(this.UnitNumber.ToString("X2")).CopyTo((Array) destinationArray, 1);
    Array.Copy((Array) finsCore, 3, (Array) destinationArray, 3, 2);
    Encoding.ASCII.GetBytes(status.ToString("X2")).CopyTo((Array) destinationArray, 5);
    if (data.Length != 0)
      data.CopyTo((Array) destinationArray, 7);
    int num = (int) destinationArray[0];
    for (int index = 1; index < destinationArray.Length - 4; ++index)
      num ^= (int) destinationArray[index];
    SoftBasic.BuildAsciiBytesFrom((byte) num).CopyTo((Array) destinationArray, destinationArray.Length - 4);
    destinationArray[destinationArray.Length - 2] = (byte) 42;
    destinationArray[destinationArray.Length - 1] = (byte) 13;
    return destinationArray;
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength > 1 && buffer[receivedLength - 1] == (byte) 13;
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronHostLinkCModeServer[{this.Port}]";
}
