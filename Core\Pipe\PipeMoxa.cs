﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeMoxa
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.IO;
using System.IO.Ports;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// 基于MOXA公司提供的串口驱动实现的管道类，目前仅支持windows平台，需要当前目录下存在 PCOMM.DLL 组件<br />
/// The pipe class based on the serial port driver provided by MOXA currently only supports the windows platform and requires the existence of the PCOMM.DLL component in the current directory
/// </summary>
public class PipeMoxa : CommunicationPipe, IDisposable
{
  private int portNumber = -1;
  private bool rts = false;
  private bool dtr = false;
  private bool isOpen = false;
  private int moxaBaudRate = 12;
  private int moxaDataBits = 3;
  private int moxaStopBits = 0;
  private int moxaParity = 0;
  private string moxaFormate = string.Empty;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public PipeMoxa()
  {
    this.SleepTime = 5;
    this.ReceiveEmptyDataCount = 4;
  }

  /// <summary>
  /// 根据串口的名称来实例化一个对象<br />
  /// Instantiate a default object
  /// </summary>
  /// <remarks>
  /// portName 支持格式化的方式，例如输入 COM3-9600-8-N-1，COM5-19200-7-E-2，其中奇偶校验的字母可选，N:无校验，O：奇校验，E:偶校验，停止位可选 0, 1, 2, 1.5 四种选项
  /// </remarks>
  public PipeMoxa(string portName)
  {
    this.SleepTime = 5;
    this.ReceiveEmptyDataCount = 4;
    this.SerialPortInni(portName);
  }

  /// <summary>
  /// 获取或设置一个值，该值指示在串行通信中是否启用请求发送 (RTS) 信号。<br />
  /// Gets or sets a value indicating whether the request sending (RTS) signal is enabled in serial communication.
  /// </summary>
  public bool RtsEnable
  {
    get => this.rts;
    set
    {
      this.rts = value;
      if (!this.isOpen)
        return;
      PCommHelper.sio_RTS(this.portNumber, value ? 1 : 0);
    }
  }

  /// <summary>
  /// 获取或设置一个值，该值指示在串行通信中是否启用数据终端就绪 (Drt) 信号。<br />
  /// Gets or sets a value that indicates whether the Data Terminal Ready (DRT) signal is enabled in serial communication.
  /// </summary>
  public bool DtrEnable
  {
    get => this.dtr;
    set
    {
      this.dtr = value;
      if (!this.isOpen)
        return;
      PCommHelper.sio_DTR(this.portNumber, value ? 1 : 0);
    }
  }

  /// <summary>从串口中至少接收的字节长度信息，默认为1个字节</summary>
  public int AtLeastReceiveLength { get; set; } = 1;

  /// <summary>
  /// 获取或设置连续接收空的数据次数，在数据接收完成时有效，每个单位消耗的时间为<see cref="P:HslCommunication.Core.Pipe.CommunicationPipe.SleepTime" />。<br />
  /// Obtain or set the number of consecutive times to receive empty data, which is valid when the data is received, and the time consumed by each unit is <see cref="P:HslCommunication.Core.Pipe.CommunicationPipe.SleepTime" />
  /// </summary>
  [HslMqttApi(Description = "Get or set the number of consecutive empty data receptions, which is valid when data reception is completed, default is 1")]
  public int ReceiveEmptyDataCount { get; set; } = 1;

  /// <summary>
  /// 是否在发送数据前清空缓冲数据，默认是false<br />
  /// Whether to empty the buffer before sending data, the default is false
  /// </summary>
  [HslMqttApi(Description = "Whether to empty the buffer before sending data, the default is false")]
  public bool IsClearCacheBeforeRead { get; set; }

  /// <summary>
  /// 初始化串口信息，9600波特率，8位数据位，1位停止位，无奇偶校验<br />
  /// Initial serial port information, 9600 baud rate, 8 data bits, 1 stop bit, no parity
  /// </summary>
  /// <remarks>
  /// portName 支持格式化的方式，例如输入 COM3-9600-8-N-1，COM5-19200-7-E-2，其中奇偶校验的字母可选，N:无校验，O：奇校验，E:偶校验，停止位可选 0, 1, 2, 1.5 四种选项
  /// </remarks>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  public void SerialPortInni(string portName)
  {
    if (portName.Contains("-"))
    {
      string portName1 = "COM1";
      int baudRate = 9600;
      int dataBits = 8;
      Parity parity = Parity.None;
      StopBits stopBits = StopBits.One;
      string[] strArray1 = portName.Split(new char[2]
      {
        '-',
        ';'
      }, StringSplitOptions.RemoveEmptyEntries);
      if (strArray1.Length == 0)
        return;
      int num1 = 0;
      if (!Regex.IsMatch(strArray1[0], "^[0-9]+$"))
      {
        portName1 = strArray1[0];
        num1 = 1;
      }
      if (num1 < strArray1.Length)
        baudRate = Convert.ToInt32(strArray1[num1++]);
      if (num1 < strArray1.Length)
        dataBits = Convert.ToInt32(strArray1[num1++]);
      if (num1 < strArray1.Length)
      {
        int num2;
        switch (strArray1[num1++].ToUpper())
        {
          case "N":
            num2 = 0;
            break;
          case "O":
            num2 = 1;
            break;
          case "E":
            num2 = 2;
            break;
          default:
            num2 = 4;
            break;
        }
        parity = (Parity) num2;
      }
      if (num1 < strArray1.Length)
      {
        string[] strArray2 = strArray1;
        int index = num1;
        int num3 = index + 1;
        int num4;
        switch (strArray2[index])
        {
          case "1":
            num4 = 1;
            break;
          case "2":
            num4 = 2;
            break;
          case "0":
            num4 = 0;
            break;
          default:
            num4 = 3;
            break;
        }
        stopBits = (StopBits) num4;
      }
      this.SerialPortInni(portName1, baudRate, dataBits, stopBits, parity);
    }
    else
      this.SerialPortInni(portName, 9600, 8, StopBits.One, Parity.None);
  }

  /// <summary>
  /// 初始化串口信息，波特率，数据位，停止位，奇偶校验需要全部自己来指定<br />
  /// Start serial port information, baud rate, data bit, stop bit, parity all need to be specified
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  /// <param name="baudRate">波特率</param>
  /// <param name="dataBits">数据位</param>
  /// <param name="stopBits">停止位</param>
  /// <param name="parity">奇偶校验</param>
  public void SerialPortInni(
    string portName,
    int baudRate,
    int dataBits,
    StopBits stopBits,
    Parity parity)
  {
    this.portNumber = !portName.StartsWith("COM", StringComparison.OrdinalIgnoreCase) ? Convert.ToInt32(portName) : Convert.ToInt32(portName.Substring(3));
    switch (baudRate)
    {
      case 50:
        this.moxaBaudRate = 0;
        break;
      case 75:
        this.moxaBaudRate = 1;
        break;
      case 110:
        this.moxaBaudRate = 2;
        break;
      case 134:
        this.moxaBaudRate = 3;
        break;
      case 150:
        this.moxaBaudRate = 4;
        break;
      case 300:
        this.moxaBaudRate = 5;
        break;
      case 600:
        this.moxaBaudRate = 6;
        break;
      case 1200:
        this.moxaBaudRate = 7;
        break;
      case 1800:
        this.moxaBaudRate = 8;
        break;
      case 2400:
        this.moxaBaudRate = 9;
        break;
      case 4800:
        this.moxaBaudRate = 10;
        break;
      case 7200:
        this.moxaBaudRate = 11;
        break;
      case 9600:
        this.moxaBaudRate = 12;
        break;
      case 19200:
        this.moxaBaudRate = 13;
        break;
      case 38400:
        this.moxaBaudRate = 14;
        break;
      case 57600:
        this.moxaBaudRate = 15;
        break;
      case 115200:
        this.moxaBaudRate = 16 /*0x10*/;
        break;
      case 230400:
        this.moxaBaudRate = 17;
        break;
      case 460800 /*0x070800*/:
        this.moxaBaudRate = 18;
        break;
      case 921600 /*0x0E1000*/:
        this.moxaBaudRate = 19;
        break;
    }
    switch (dataBits)
    {
      case 5:
        this.moxaDataBits = 0;
        break;
      case 6:
        this.moxaDataBits = 1;
        break;
      case 7:
        this.moxaDataBits = 2;
        break;
      case 8:
        this.moxaDataBits = 3;
        break;
    }
    switch (stopBits)
    {
      case StopBits.One:
        this.moxaStopBits = 0;
        break;
      case StopBits.Two:
        this.moxaStopBits = 4;
        break;
    }
    switch (parity)
    {
      case Parity.None:
        this.moxaParity = 0;
        break;
      case Parity.Odd:
        this.moxaParity = 8;
        break;
      case Parity.Even:
        this.moxaParity = 24;
        break;
      case Parity.Mark:
        this.moxaParity = 40;
        break;
      case Parity.Space:
        this.moxaParity = 48 /*0x30*/;
        break;
    }
    this.moxaFormate = HslHelper.ToFormatString(portName, baudRate, dataBits, parity, stopBits);
  }

  /// <summary>
  /// 获取一个值，指示串口是否处于打开状态<br />
  /// Gets a value indicating whether the serial port is open
  /// </summary>
  /// <returns>是或否</returns>
  public bool IsOpen() => this.isOpen;

  /// <summary>
  /// 清除串口缓冲区的数据，并返回该数据，如果缓冲区没有数据，返回的字节数组长度为0<br />
  /// The number sent clears the data in the serial port buffer and returns that data, or if there is no data in the buffer, the length of the byte array returned is 0
  /// </summary>
  /// <returns>是否操作成功的方法</returns>
  public OperateResult<byte[]> ClearSerialCache()
  {
    return this.SPReceived((INetMessage) null, (byte[]) null, false);
  }

  private string GetErrorText(int ret)
  {
    switch (ret)
    {
      case -12:
        return "Write timeout.";
      case -11:
        return "User abort blocked write.";
      case -9:
        return "The com port does not support this function.";
      case -8:
        return "Calling Win32 function failed Call GetLastError to get the error code.";
      case -7:
        return "Bad parameter.";
      case -6:
        return "Can't control the port because it is set as auto H/W flow control by sio_flowctrl.";
      case -5:
        return "No such port or port is occupied by other program.";
      case -4:
        return "No data to read.";
      case -2:
        return "The board is not the MOXA compatible intelligent board";
      case -1:
        return "Port number is invalid.";
      case 0:
        return "OK";
      default:
        return "Unkown";
    }
  }

  /// <inheritdoc />
  public override OperateResult<bool> OpenCommunication()
  {
    try
    {
      if (this.isOpen)
        return OperateResult.CreateSuccessResult<bool>(false);
      int num = PCommHelper.sio_open(this.portNumber);
      if (num != 0)
        return new OperateResult<bool>(num, this.GetErrorText(num));
      PCommHelper.sio_ioctl(this.portNumber, this.moxaBaudRate, this.moxaDataBits | this.moxaParity | this.moxaStopBits);
      if (this.RtsEnable)
        PCommHelper.sio_RTS(this.portNumber, 1);
      if (this.DtrEnable)
        PCommHelper.sio_DTR(this.portNumber, 1);
      this.ResetConnectErrorCount();
      this.isOpen = true;
      return OperateResult.CreateSuccessResult<bool>(true);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool>("OpenCommunication failed: " + ex.Message);
    }
  }

  /// <inheritdoc />
  public override OperateResult CloseCommunication()
  {
    if (this.isOpen)
    {
      try
      {
        PCommHelper.sio_close(this.portNumber);
        this.isOpen = false;
      }
      catch (Exception ex)
      {
        return new OperateResult(ex.Message);
      }
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult Send(byte[] data, int offset, int size)
  {
    if (data == null || data.Length == 0)
      return OperateResult.CreateSuccessResult();
    if (!Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    try
    {
      int num;
      if (offset == 0)
      {
        num = PCommHelper.sio_write(this.portNumber, data, data.Length);
      }
      else
      {
        byte[] buf = data.SelectMiddle<byte>(offset, size);
        num = PCommHelper.sio_write(this.portNumber, buf, buf.Length);
      }
      return num >= 0 ? OperateResult.CreateSuccessResult() : new OperateResult(num, this.GetErrorText(num));
    }
    catch (Exception ex)
    {
      return new OperateResult(-this.IncrConnectErrorCount(), ex.Message);
    }
  }

  /// <inheritdoc />
  public override OperateResult<int> Receive(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (!Authorization.nzugaydgwadawdibbas())
      return new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    try
    {
      if (length > 0)
      {
        int num = PCommHelper.sio_read(this.portNumber, ref buffer[offset], length);
        return num >= 0 ? OperateResult.CreateSuccessResult<int>(num) : new OperateResult<int>(num, this.GetErrorText(num));
      }
      int num1 = PCommHelper.sio_read(this.portNumber, ref buffer[offset], buffer.Length - offset);
      return num1 >= 0 ? OperateResult.CreateSuccessResult<int>(num1) : new OperateResult<int>(num1, this.GetErrorText(num1));
    }
    catch (Exception ex)
    {
      return new OperateResult<int>(-this.IncrConnectErrorCount(), ex.Message);
    }
  }

  /// <summary>
  /// 从串口接收一串字节数据信息，直到没有数据为止，如果参数awaitData为false, 第一轮接收没有数据则返回<br />
  /// Receives a string of bytes of data information from the serial port until there is no data, and returns if the parameter awaitData is false
  /// </summary>
  /// <param name="netMessage">定义的消息体对象</param>
  /// <param name="sendValue">等待发送的数据对象</param>
  /// <param name="awaitData">是否必须要等待数据返回</param>
  /// <param name="logMessage">用于消息记录的日志信息</param>
  /// <returns>结果数据对象</returns>
  private OperateResult<byte[]> SPReceived(
    INetMessage netMessage,
    byte[] sendValue,
    bool awaitData,
    Action<byte[]> logMessage = null)
  {
    if (!Authorization.nzugaydgwadawdibbas())
      return new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    byte[] buffer;
    MemoryStream ms;
    try
    {
      buffer = new byte[1024 /*0x0400*/];
      ms = new MemoryStream();
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
    DateTime now = DateTime.Now;
    int num1 = 0;
    int num2 = 0;
label_29:
    ++num2;
    if (num2 > 1 && this.SleepTime >= 0)
      HslHelper.ThreadSleep(this.SleepTime);
    try
    {
      TimeSpan timeSpan;
      if (PCommHelper.sio_iqueue(this.portNumber) < 1)
      {
        if (num2 != 1)
        {
          timeSpan = DateTime.Now - now;
          if (timeSpan.TotalMilliseconds > (double) this.ReceiveTimeOut)
            return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), $"Time out: {this.ReceiveTimeOut}, received: {ms.ToArray().ToHexString(' ')}");
          if (ms.Length >= (long) this.AtLeastReceiveLength)
          {
            ++num1;
            if (netMessage != null || num1 < this.ReceiveEmptyDataCount)
              goto label_29;
          }
          else if (awaitData)
            goto label_29;
        }
        else
          goto label_29;
      }
      else
      {
        num1 = 0;
        int num3 = PCommHelper.sio_read(this.portNumber, ref buffer[0], buffer.Length);
        if (num3 < 0)
          return new OperateResult<byte[]>(num3, this.GetErrorText(num3));
        if (num3 > 0)
        {
          ms.Write(buffer, 0, num3);
          if (logMessage != null)
            logMessage(buffer.SelectBegin<byte>(num3));
        }
        if (netMessage != null)
        {
          if (this.CheckMessageComplete(netMessage, sendValue, ref ms))
            goto label_30;
        }
        int num4;
        if (this.ReceiveTimeOut > 0)
        {
          timeSpan = DateTime.Now - now;
          num4 = timeSpan.TotalMilliseconds > (double) this.ReceiveTimeOut ? 1 : 0;
        }
        else
          num4 = 0;
        if (num4 != 0)
          return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), $"Time out: {this.ReceiveTimeOut}, received: {ms.ToArray().ToHexString(' ')}");
        goto label_29;
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), ex.Message);
    }
label_30:
    this.ResetConnectErrorCount();
    return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReceiveMessage(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    return this.UseServerActivePush ? base.ReceiveMessage(netMessage, sendValue, useActivePush, reportProgress) : this.SPReceived(netMessage, sendValue, true, logMessage);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    if (this.IsClearCacheBeforeRead)
      this.ClearSerialCache();
    OperateResult<byte[]> operateResult = this.ReadFromCoreServerHelper(netMessage, sendValue, hasResponseData, 0, logMessage);
    if (operateResult.IsSuccess)
      this.ResetConnectErrorCount();
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReceiveMessageAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    OperateResult<byte[]> messageAsync = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.SPReceived(netMessage, sendValue, true, logMessage))).ConfigureAwait(false);
    return messageAsync;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    OperateResult<byte[]> operateResult = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.ReadFromCoreServer(netMessage, sendValue, hasResponseData, logMessage))).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    base.Dispose(disposing);
    this.CloseCommunication();
  }

  /// <summary>
  /// 获取当前串口参数的信息，以格式化的字符串返回<br />
  /// Obtain the information of the current serial port parameters and return them as formatted strings
  /// </summary>
  /// <returns>串口参数</returns>
  public string ToFormatString() => this.moxaFormate;

  /// <inheritdoc />
  public override string ToString() => $"PipeMoxa[COM{this.portNumber}]";
}
