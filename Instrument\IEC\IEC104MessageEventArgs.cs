﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.IEC.IEC104MessageEventArgs
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Instrument.IEC;

/// <summary>IEC104的消息事件</summary>
public class IEC104MessageEventArgs : EventArgs
{
  /// <summary>指定asdu消息进行实例化一个对象</summary>
  /// <param name="asdu">asdu报文</param>
  public IEC104MessageEventArgs(byte[] asdu)
  {
    this.ASDU = asdu;
    if (asdu == null)
      return;
    this.TypeID = asdu[0];
    this.IsAddressContinuous = ((int) asdu[1] & 128 /*0x80*/) == 128 /*0x80*/;
    this.InfoObjectCount = (int) asdu[1] & (int) sbyte.MaxValue;
    this.TransmissionReason = (int) asdu[2] & 63 /*0x3F*/;
    this.StationAddress = (int) BitConverter.ToUInt16(asdu, 4);
    this.Body = asdu.RemoveBegin<byte>(6);
  }

  /// <summary>获取或设置当前的asdu信息</summary>
  public byte[] ASDU { get; }

  /// <summary>类型标识</summary>
  public byte TypeID { get; set; }

  /// <summary>地址是否连续</summary>
  public bool IsAddressContinuous { get; set; }

  /// <summary>信息对象个数</summary>
  public int InfoObjectCount { get; set; }

  /// <summary>传送原因</summary>
  public int TransmissionReason { get; set; }

  /// <summary>站地址</summary>
  public int StationAddress { get; set; }

  /// <summary>信息体</summary>
  public byte[] Body { get; set; }

  /// <summary>获取在传送原因的文本描述信息</summary>
  /// <returns>字符串值</returns>
  public string GetTransmissionReasonText()
  {
    switch (this.TransmissionReason)
    {
      case 1:
        return "周期循环";
      case 2:
        return "背景扫描";
      case 3:
        return "突发";
      case 4:
        return "初始化";
      case 5:
        return "请求或被请求";
      case 6:
        return "激活";
      case 7:
        return "激活确认";
      case 8:
        return "停止激活";
      case 9:
        return "停止激活确认";
      case 10:
        return "激活终止";
      case 11:
        return "远方命令引起的返送信息";
      case 12:
        return "当地命令引起的返送信息";
      case 20:
        return "响应站召唤";
      case 21:
        return "响应第1组召唤";
      case 44:
        return "未知的类型标识";
      case 45:
        return "未知的传送原因";
      case 46:
        return "未知的应用服务器数据单元公共地址";
      case 47:
        return "未知的信息对象地址";
      default:
        return this.TransmissionReason.ToString();
    }
  }

  /// <summary>获取当前类型的文本描述信息</summary>
  /// <returns>文本值</returns>
  public string GetTypeIDText()
  {
    switch (this.TypeID)
    {
      case 1:
        return "单点遥信";
      case 3:
        return "双点遥信";
      case 5:
        return "步位置信息";
      case 7:
        return "32比特串";
      case 9:
        return "归一化遥测值";
      case 11:
        return "标度化遥测值";
      case 13:
        return "单浮点遥测值";
      case 15:
        return "累计量";
      case 20:
        return "成组单点遥信";
      case 21:
        return "归一化遥测值";
      case 30:
        return "单点遥信带时标";
      case 31 /*0x1F*/:
        return "双点遥信带时标";
      case 32 /*0x20*/:
        return "步位置遥信带时标";
      case 33:
        return "32比特串带时标";
      case 34:
        return "归一化遥测值带时标";
      case 35:
        return "标度化遥测值带时标";
      case 36:
        return "短浮点遥测值带时标";
      case 37:
        return "累计量带时标";
      case 38:
        return "继电保护装置事件";
      case 39:
        return "继电保护装置成组启动事件";
      case 40:
        return "继电保护装置成组出口信息";
      case 45:
      case 58:
        return "单点遥控";
      case 46:
      case 59:
        return "双点遥控";
      case 47:
      case 60:
        return "升降遥控";
      case 48 /*0x30*/:
      case 61:
      case 136:
        return "归一化设置值";
      case 49:
      case 62:
        return "标度化设定值";
      case 50:
      case 63 /*0x3F*/:
        return "短浮点设定值";
      case 51:
      case 64 /*0x40*/:
        return "32比特串";
      case 70:
        return "初始化结束";
      case 100:
        return "总召唤";
      case 101:
        return "积累量召唤";
      case 102:
        return "读命令";
      case 103:
        return "时钟同步命令";
      case 105:
        return "复位进程命令";
      case 107:
        return "带时标的命令测试";
      default:
        return $"Unknown[{this.TypeID}]";
    }
  }

  /// <summary>获取当前的消息是否带时标信息</summary>
  /// <returns>是否携带时间标度信息</returns>
  public bool WithTimeInfo()
  {
    return this.TypeID >= (byte) 30 && (this.TypeID < (byte) 45 || this.TypeID >= (byte) 58 && this.TypeID < (byte) 70);
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"{this.GetTypeIDText()} [{(this.IsAddressContinuous ? (object) "Continuous" : (object) "Uncontinuous")}:{this.InfoObjectCount}] Reason[{this.GetTransmissionReasonText()}] Pub-Address[{this.StationAddress}]";
  }
}
