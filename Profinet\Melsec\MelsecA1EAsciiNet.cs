﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecA1EAsciiNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱PLC通讯协议，采用A兼容1E帧协议实现，使用ASCII码通讯，请根据实际型号来进行选取<br />
/// Mitsubishi PLC communication protocol, implemented using A compatible 1E frame protocol, using ascii code communication, please choose according to the actual model
/// </summary>
/// <remarks>
/// <inheritdoc cref="T:HslCommunication.Profinet.Melsec.MelsecA1ENet" path="remarks" />
/// </remarks>
public class MelsecA1EAsciiNet : DeviceTcpNet
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public MelsecA1EAsciiNet()
  {
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>
  /// 指定ip地址和端口来来实例化一个默认的对象<br />
  /// Specify the IP address and port to instantiate a default object
  /// </summary>
  /// <param name="ipAddress">PLC的Ip地址</param>
  /// <param name="port">PLC的端口</param>
  public MelsecA1EAsciiNet(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new MelsecA1EAsciiMessage();

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecA1ENet.PLCNumber" />
  public byte PLCNumber { get; set; } = byte.MaxValue;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1ENet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<List<byte[]>> result1 = MelsecA1EAsciiNet.BuildReadCommand(address, length, false, this.PLCNumber);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      OperateResult result3 = MelsecA1EAsciiNet.CheckResponseLegal(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result3);
      OperateResult<byte[]> actualData = MelsecA1EAsciiNet.ExtractActualData(result2.Content, false);
      if (!actualData.IsSuccess)
        return actualData;
      byteList.AddRange((IEnumerable<byte>) actualData.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1ENet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = MelsecA1EAsciiNet.BuildWriteWordCommand(address, value, this.PLCNumber);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult result = MelsecA1EAsciiNet.CheckResponseLegal(operateResult2.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1EAsciiNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<List<byte[]>> command = MelsecA1EAsciiNet.BuildReadCommand(address, length, false, this.PLCNumber);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> array = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      OperateResult check = MelsecA1EAsciiNet.CheckResponseLegal(read.Content);
      if (!check.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(check);
      OperateResult<byte[]> extra = MelsecA1EAsciiNet.ExtractActualData(read.Content, false);
      if (!extra.IsSuccess)
        return extra;
      array.AddRange((IEnumerable<byte>) extra.Content);
      read = (OperateResult<byte[]>) null;
      check = (OperateResult) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(array.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1EAsciiNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> command = MelsecA1EAsciiNet.BuildWriteWordCommand(address, value, this.PLCNumber);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = MelsecA1EAsciiNet.CheckResponseLegal(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1ENet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (address.IndexOf('.') > 0)
      return HslHelper.ReadBool((IReadWriteNet) this, address, length);
    OperateResult<List<byte[]>> result1 = MelsecA1EAsciiNet.BuildReadCommand(address, length, true, this.PLCNumber);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<byte> source = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult result3 = MelsecA1EAsciiNet.CheckResponseLegal(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(result3);
      OperateResult<byte[]> actualData = MelsecA1EAsciiNet.ExtractActualData(result2.Content, true);
      if (!actualData.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) actualData);
      source.AddRange((IEnumerable<byte>) actualData.Content);
    }
    return OperateResult.CreateSuccessResult<bool[]>(source.Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 1)).Take<bool>((int) length).ToArray<bool>());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1ENet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    OperateResult<byte[]> operateResult1 = MelsecA1EAsciiNet.BuildWriteBoolCommand(address, values, this.PLCNumber);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : MelsecA1EAsciiNet.CheckResponseLegal(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1EAsciiNet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (address.IndexOf('.') > 0)
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) this, address, length);
      return operateResult;
    }
    OperateResult<List<byte[]>> command = MelsecA1EAsciiNet.BuildReadCommand(address, length, true, this.PLCNumber);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<byte> array = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult check = MelsecA1EAsciiNet.CheckResponseLegal(read.Content);
      if (!check.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(check);
      OperateResult<byte[]> extract = MelsecA1EAsciiNet.ExtractActualData(read.Content, true);
      if (!extract.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) extract);
      array.AddRange((IEnumerable<byte>) extract.Content);
      read = (OperateResult<byte[]>) null;
      check = (OperateResult) null;
      extract = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(array.Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 1)).Take<bool>((int) length).ToArray<bool>());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1EAsciiNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] values)
  {
    OperateResult<byte[]> command = MelsecA1EAsciiNet.BuildWriteBoolCommand(address, values, this.PLCNumber);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? MelsecA1EAsciiNet.CheckResponseLegal(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecA1ENet[{this.IpAddress}:{this.Port}]";

  /// <summary>根据类型地址长度确认需要读取的指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">长度</param>
  /// <param name="isBit">指示是否按照位成批的读出</param>
  /// <param name="plcNumber">PLC编号</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<List<byte[]>> BuildReadCommand(
    string address,
    ushort length,
    bool isBit,
    byte plcNumber)
  {
    OperateResult<MelsecA1EDataType, int> result = MelsecHelper.McA1EAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) result);
    byte num1 = isBit ? (byte) 0 : (byte) 1;
    int[] array = SoftBasic.SplitIntegerToArray((int) length, isBit ? 256 /*0x0100*/ : 64 /*0x40*/);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      byte[] numArray = new byte[24]
      {
        SoftBasic.BuildAsciiBytesFrom(num1)[0],
        SoftBasic.BuildAsciiBytesFrom(num1)[1],
        SoftBasic.BuildAsciiBytesFrom(plcNumber)[0],
        SoftBasic.BuildAsciiBytesFrom(plcNumber)[1],
        (byte) 48 /*0x30*/,
        (byte) 48 /*0x30*/,
        (byte) 48 /*0x30*/,
        (byte) 65,
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[1])[0],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[1])[1],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[0])[0],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[0])[1],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[3])[0],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[3])[1],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[2])[0],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[2])[1],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[1])[0],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[1])[1],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[0])[0],
        SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[0])[1],
        (byte) 0,
        (byte) 0,
        (byte) 0,
        (byte) 0
      };
      int num2 = array[index];
      if (num2 == 256 /*0x0100*/)
        num2 = 0;
      numArray[20] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(num2 % 256 /*0x0100*/)[0])[0];
      numArray[21] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(num2 % 256 /*0x0100*/)[0])[1];
      numArray[22] = (byte) 48 /*0x30*/;
      numArray[23] = (byte) 48 /*0x30*/;
      numArrayList.Add(numArray);
      result.Content2 += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>根据类型地址以及需要写入的数据来生成指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="value">数据值</param>
  /// <param name="plcNumber">PLC编号</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<byte[]> BuildWriteWordCommand(
    string address,
    byte[] value,
    byte plcNumber)
  {
    OperateResult<MelsecA1EDataType, int> result = MelsecHelper.McA1EAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    value = MelsecHelper.TransByteArrayToAsciiByteArray(value);
    byte[] numArray = new byte[24 + value.Length];
    numArray[0] = (byte) 48 /*0x30*/;
    numArray[1] = (byte) 51;
    numArray[2] = SoftBasic.BuildAsciiBytesFrom(plcNumber)[0];
    numArray[3] = SoftBasic.BuildAsciiBytesFrom(plcNumber)[1];
    numArray[4] = (byte) 48 /*0x30*/;
    numArray[5] = (byte) 48 /*0x30*/;
    numArray[6] = (byte) 48 /*0x30*/;
    numArray[7] = (byte) 65;
    numArray[8] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[1])[0];
    numArray[9] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[1])[1];
    numArray[10] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[0])[0];
    numArray[11] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[0])[1];
    numArray[12] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[3])[0];
    numArray[13] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[3])[1];
    numArray[14] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[2])[0];
    numArray[15] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[2])[1];
    numArray[16 /*0x10*/] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[1])[0];
    numArray[17] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[1])[1];
    numArray[18] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[0])[0];
    numArray[19] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[0])[1];
    numArray[20] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(value.Length / 4)[0])[0];
    numArray[21] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(value.Length / 4)[0])[1];
    numArray[22] = (byte) 48 /*0x30*/;
    numArray[23] = (byte) 48 /*0x30*/;
    value.CopyTo((Array) numArray, 24);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>根据类型地址以及需要写入的数据来生成指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="value">数据值</param>
  /// <param name="plcNumber">PLC编号</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<byte[]> BuildWriteBoolCommand(
    string address,
    bool[] value,
    byte plcNumber)
  {
    OperateResult<MelsecA1EDataType, int> result = MelsecHelper.McA1EAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    byte[] numArray1 = ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>();
    if (numArray1.Length % 2 == 1)
      numArray1 = SoftBasic.SpliceArray<byte>(numArray1, new byte[1]
      {
        (byte) 48 /*0x30*/
      });
    byte[] numArray2 = new byte[24 + numArray1.Length];
    numArray2[0] = (byte) 48 /*0x30*/;
    numArray2[1] = (byte) 50;
    numArray2[2] = SoftBasic.BuildAsciiBytesFrom(plcNumber)[0];
    numArray2[3] = SoftBasic.BuildAsciiBytesFrom(plcNumber)[1];
    numArray2[4] = (byte) 48 /*0x30*/;
    numArray2[5] = (byte) 48 /*0x30*/;
    numArray2[6] = (byte) 48 /*0x30*/;
    numArray2[7] = (byte) 65;
    numArray2[8] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[1])[0];
    numArray2[9] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[1])[1];
    numArray2[10] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[0])[0];
    numArray2[11] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content1.DataCode)[0])[1];
    numArray2[12] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[3])[0];
    numArray2[13] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[3])[1];
    numArray2[14] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[2])[0];
    numArray2[15] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[2])[1];
    numArray2[16 /*0x10*/] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[1])[0];
    numArray2[17] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[1])[1];
    numArray2[18] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[0])[0];
    numArray2[19] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(result.Content2)[0])[1];
    numArray2[20] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(value.Length)[0])[0];
    numArray2[21] = SoftBasic.BuildAsciiBytesFrom(BitConverter.GetBytes(value.Length)[0])[1];
    numArray2[22] = (byte) 48 /*0x30*/;
    numArray2[23] = (byte) 48 /*0x30*/;
    numArray1.CopyTo((Array) numArray2, 24);
    return OperateResult.CreateSuccessResult<byte[]>(numArray2);
  }

  /// <summary>检测反馈的消息是否合法</summary>
  /// <param name="response">接收的报文</param>
  /// <returns>是否成功</returns>
  public static OperateResult CheckResponseLegal(byte[] response)
  {
    if (response.Length < 4)
      return new OperateResult(StringResources.Language.ReceiveDataLengthTooShort);
    if (response[2] == (byte) 48 /*0x30*/ && response[3] == (byte) 48 /*0x30*/)
      return OperateResult.CreateSuccessResult();
    return response[2] == (byte) 53 && response[3] == (byte) 66 ? new OperateResult(Convert.ToInt32(Encoding.ASCII.GetString(response, 4, 2), 16 /*0x10*/), StringResources.Language.MelsecPleaseReferToManualDocument) : new OperateResult(Convert.ToInt32(Encoding.ASCII.GetString(response, 2, 2), 16 /*0x10*/), StringResources.Language.MelsecPleaseReferToManualDocument);
  }

  /// <summary>从PLC反馈的数据中提取出实际的数据内容，需要传入反馈数据，是否位读取</summary>
  /// <param name="response">反馈的数据内容</param>
  /// <param name="isBit">是否位读取</param>
  /// <returns>解析后的结果对象</returns>
  public static OperateResult<byte[]> ExtractActualData(byte[] response, bool isBit)
  {
    return isBit ? OperateResult.CreateSuccessResult<byte[]>(((IEnumerable<byte>) response.RemoveBegin<byte>(4)).Select<byte, byte>((Func<byte, byte>) (m => m != (byte) 48 /*0x30*/ ? (byte) 1 : (byte) 0)).ToArray<byte>()) : OperateResult.CreateSuccessResult<byte[]>(MelsecHelper.TransAsciiByteArrayToByteArray(response.RemoveBegin<byte>(4)));
  }
}
