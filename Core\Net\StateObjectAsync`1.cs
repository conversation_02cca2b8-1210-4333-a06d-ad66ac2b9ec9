﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.StateObjectAsync`1
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>携带TaskCompletionSource属性的异步对象</summary>
/// <typeparam name="T">类型</typeparam>
internal class StateObjectAsync<T> : StateObject
{
  /// <summary>实例化一个对象</summary>
  public StateObjectAsync()
  {
  }

  /// <summary>实例化一个对象，指定接收或是发送的数据长度</summary>
  /// <param name="length">数据长度</param>
  public StateObjectAsync(int length)
    : base(length)
  {
  }

  public TaskCompletionSource<T> Tcs { get; set; }
}
