﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttSyncClient
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Core.Security;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// 基于MQTT协议的同步访问的客户端程序，支持以同步的方式访问服务器的数据信息，并及时的反馈结果，当服务器启动文件功能时，也支持文件的上传，下载，删除操作等。<br />
/// The client program based on MQTT protocol for synchronous access supports synchronous access to the server's data information and timely feedback of results,
/// When the server starts the file function, it also supports file upload, download, and delete operations.
/// </summary>
/// <remarks>
/// 在最新的V10.2.0及以上版本中，本客户端支持加密模式，启用加密模式后，就无法通过抓包的报文来分析出用户名密码，以及通信的数据细节，详细可以参考API文档。
/// </remarks>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test" title="简单的实例化" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test2" title="带用户名密码的实例化" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test3" title="连接示例" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test4" title="读取数据示例" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test5" title="带进度报告示例" />
/// 当MqttServer注册了远程RPC接口的时候，例如将一个plc对象注册是接口对象，或是自定义的接口内容
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test11" title="RPC接口读取" />
/// 服务器都有什么RPC接口呢？可以通过下面的方式知道
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test12" title="RPC接口列表" />
/// 关于加密模式，在不加密的情况下，用户名及密码，还有请求的数据信息会被第三方软件窃取，从而泄露一些关键的数据信息，如果使用了HslCommunicationV10.2.0版本以上创建的MQTTServer，
/// 那么可以在客户端使用加密模式，加密使用RSA+AES混合加密，密钥动态生成，在保证效率的同时，具有很高的安全性。客户端使用加密如下：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test13" title="加密举例" />
/// 下面演示文件部分的功能的接口方法，主要包含，上传，下载，删除，遍历操作
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test6" title="下载文件功能" />
/// 在实际的窗体界面开发中，会更加的复杂，有个按钮点击下载，还需要支持取消下载操作。可以参考如下的代码：<br />
/// <code lang="cs" source="TestProject\HslCommunicationDemo\MQTT\FormMqttFileClient.cs" region="Download Sample" title="带取消的下载功能" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test7" title="上传文件功能" />
/// 在实际的窗体界面开发中，会更加的复杂，有个按钮点击上传，还需要支持取消上传操作。可以参考如下的代码：<br />
/// <code lang="cs" source="TestProject\HslCommunicationDemo\MQTT\FormMqttFileClient.cs" region="Upload File" title="带取消的上传功能" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test8" title="删除文件功能" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test9" title="遍历指定目录的文件名功能" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttSyncClientSample.cs" region="Test10" title="遍历指定目录的所有子目录" />
/// 上述的两个遍历的方法，就可以遍历出服务器的所有目录和文件了，具体可以参考 Demo 的MQTT文件客户端的演示界面。
/// </example>
public class MqttSyncClient : TcpNetCommunication
{
  private SoftIncrementCount incrementCount;
  private MqttConnectionOptions connectionOptions;
  private Encoding stringEncoding = Encoding.UTF8;
  private RSACryptoServiceProvider cryptoServiceProvider = (RSACryptoServiceProvider) null;
  private AesCryptography aesCryptography = (AesCryptography) null;

  /// <summary>
  /// 实例化一个MQTT的同步客户端<br />
  /// Instantiate an MQTT synchronization client
  /// </summary>
  /// <param name="options">连接的参数信息，可以指定IP地址，端口，账户名，密码，客户端ID信息</param>
  public MqttSyncClient(MqttConnectionOptions options)
  {
    this.connectionOptions = options;
    this.IpAddress = options.IpAddress;
    this.Port = options.Port;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    this.ConnectTimeOut = options.ConnectTimeout;
    this.ReceiveTimeOut = 60000;
  }

  /// <summary>
  /// 通过指定的ip地址及端口来实例化一个同步的MQTT客户端<br />
  /// Instantiate a synchronized MQTT client with the specified IP address and port
  /// </summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号信息</param>
  public MqttSyncClient(string ipAddress, int port)
  {
    this.connectionOptions = new MqttConnectionOptions()
    {
      IpAddress = ipAddress,
      Port = port
    };
    this.IpAddress = ipAddress;
    this.Port = port;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    this.ReceiveTimeOut = 60000;
  }

  /// <summary>
  /// 通过指定的ip地址及端口来实例化一个同步的MQTT客户端<br />
  /// Instantiate a synchronized MQTT client with the specified IP address and port
  /// </summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号信息</param>
  public MqttSyncClient(IPAddress ipAddress, int port)
  {
    this.connectionOptions = new MqttConnectionOptions()
    {
      IpAddress = ipAddress.ToString(),
      Port = port
    };
    this.IpAddress = ipAddress.ToString();
    this.Port = port;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
  }

  private OperateResult InitializationMqttSocket(CommunicationPipe pipe, string protocol)
  {
    RSACryptoServiceProvider rsa = (RSACryptoServiceProvider) null;
    if (this.connectionOptions.UseRSAProvider)
    {
      this.cryptoServiceProvider = new RSACryptoServiceProvider();
      OperateResult operateResult = pipe.Send(MqttHelper.BuildMqttCommand(byte.MaxValue, (byte[]) null, HslSecurity.ByteEncrypt(this.cryptoServiceProvider.GetPEMPublicKey())).Content);
      if (!operateResult.IsSuccess)
        return operateResult;
      OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(pipe, this.ReceiveTimeOut);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      try
      {
        rsa = RSAHelper.CreateRsaProviderFromPublicKey(this.cryptoServiceProvider.DecryptLargeData(HslSecurity.ByteDecrypt(mqttMessage.Content2)));
      }
      catch (Exception ex)
      {
        pipe?.CloseCommunication();
        return new OperateResult("RSA check failed: " + ex.Message);
      }
    }
    OperateResult<byte[]> operateResult1 = MqttHelper.BuildConnectMqttCommand(this.connectionOptions, protocol, rsa);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult operateResult2 = pipe.Send(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    OperateResult<byte, byte[]> mqttMessage1 = MqttHelper.ReceiveMqttMessage(pipe, this.ReceiveTimeOut);
    if (!mqttMessage1.IsSuccess)
      return (OperateResult) mqttMessage1;
    OperateResult operateResult3 = MqttHelper.CheckConnectBack(mqttMessage1.Content1, mqttMessage1.Content2);
    if (!operateResult3.IsSuccess)
    {
      pipe?.CloseCommunication();
      return operateResult3;
    }
    if (this.connectionOptions.UseRSAProvider)
      this.aesCryptography = new AesCryptography(Encoding.UTF8.GetString(this.cryptoServiceProvider.Decrypt(mqttMessage1.Content2.RemoveBegin<byte>(2), false)));
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult operateResult = this.InitializationMqttSocket(this.CommunicationPipe, "HUSL");
    if (!operateResult.IsSuccess)
      return operateResult;
    this.incrementCount.ResetCurrentValue();
    return OperateResult.CreateSuccessResult();
  }

  private async Task<OperateResult> InitializationMqttSocketAsync(
    CommunicationPipe pipe,
    string protocol)
  {
    RSACryptoServiceProvider rsa = (RSACryptoServiceProvider) null;
    if (this.connectionOptions.UseRSAProvider)
    {
      this.cryptoServiceProvider = new RSACryptoServiceProvider();
      OperateResult sendKey = await pipe.SendAsync(MqttHelper.BuildMqttCommand(byte.MaxValue, (byte[]) null, HslSecurity.ByteEncrypt(this.cryptoServiceProvider.GetPEMPublicKey())).Content);
      if (!sendKey.IsSuccess)
        return sendKey;
      OperateResult<byte, byte[]> key = await MqttHelper.ReceiveMqttMessageAsync(pipe, this.ReceiveTimeOut);
      if (!key.IsSuccess)
        return (OperateResult) key;
      try
      {
        byte[] serverPublicToken = this.cryptoServiceProvider.DecryptLargeData(HslSecurity.ByteDecrypt(key.Content2));
        rsa = RSAHelper.CreateRsaProviderFromPublicKey(serverPublicToken);
        serverPublicToken = (byte[]) null;
      }
      catch (Exception ex)
      {
        pipe?.CloseCommunication();
        return new OperateResult("RSA check failed: " + ex.Message);
      }
      sendKey = (OperateResult) null;
      key = (OperateResult<byte, byte[]>) null;
    }
    OperateResult<byte[]> command = MqttHelper.BuildConnectMqttCommand(this.connectionOptions, protocol, rsa);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult send = await pipe.SendAsync(command.Content);
    if (!send.IsSuccess)
      return send;
    OperateResult<byte, byte[]> receive = await MqttHelper.ReceiveMqttMessageAsync(pipe, this.ReceiveTimeOut);
    if (!receive.IsSuccess)
      return (OperateResult) receive;
    OperateResult check = MqttHelper.CheckConnectBack(receive.Content1, receive.Content2);
    if (!check.IsSuccess)
    {
      pipe?.CloseCommunication();
      return check;
    }
    if (this.connectionOptions.UseRSAProvider)
    {
      string key = Encoding.UTF8.GetString(this.cryptoServiceProvider.Decrypt(receive.Content2.RemoveBegin<byte>(2), false));
      this.aesCryptography = new AesCryptography(key);
      key = (string) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    OperateResult ini = await this.InitializationMqttSocketAsync(this.CommunicationPipe, "HUSL");
    if (!ini.IsSuccess)
      return ini;
    this.incrementCount.ResetCurrentValue();
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackHeader = true)
  {
    OperateResult<byte, byte[]> result = this.ReadMqttFromCoreServer(pipe, send, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    return result.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(result.Content2) : OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
  }

  private OperateResult<byte, byte[]> ReadMqttFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    Action<long, long> sendProgress,
    Action<string, string> handleProgress,
    Action<long, long> receiveProgress)
  {
    OperateResult result = pipe.Send(send);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>(result);
    OperateResult<byte, byte[]> mqttMessage1;
    OperateResult<string, byte[]> data1;
    long int64_1;
    long int64_2;
    do
    {
      mqttMessage1 = MqttHelper.ReceiveMqttMessage(pipe, this.ReceiveTimeOut);
      if (mqttMessage1.IsSuccess)
      {
        data1 = MqttHelper.ExtraMqttReceiveData(mqttMessage1.Content1, mqttMessage1.Content2);
        if (data1.IsSuccess)
        {
          if (data1.Content2.Length == 16 /*0x10*/)
          {
            int64_1 = BitConverter.ToInt64(data1.Content2, 0);
            int64_2 = BitConverter.ToInt64(data1.Content2, 8);
            if (sendProgress != null)
              sendProgress(int64_1, int64_2);
          }
          else
            goto label_6;
        }
        else
          goto label_4;
      }
      else
        goto label_2;
    }
    while (int64_1 != int64_2);
    goto label_17;
label_2:
    return mqttMessage1;
label_4:
    return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) data1);
label_6:
    return new OperateResult<byte, byte[]>(StringResources.Language.ReceiveDataLengthTooShort);
label_17:
    OperateResult<byte, byte[]> mqttMessage2;
    while (true)
    {
      mqttMessage2 = MqttHelper.ReceiveMqttMessage(pipe, this.ReceiveTimeOut, receiveProgress);
      if (mqttMessage2.IsSuccess)
      {
        if ((int) mqttMessage2.Content1 >> 4 == 15)
        {
          OperateResult<string, byte[]> data2 = MqttHelper.ExtraMqttReceiveData(mqttMessage2.Content1, mqttMessage2.Content2);
          if (handleProgress != null)
            handleProgress(data2.Content1, Encoding.UTF8.GetString(data2.Content2));
        }
        else
          goto label_15;
      }
      else
        break;
    }
    return mqttMessage2;
label_15:
    return OperateResult.CreateSuccessResult<byte, byte[]>(mqttMessage2.Content1, mqttMessage2.Content2);
  }

  private OperateResult<byte[]> ReadMqttFromCoreServer(
    byte control,
    byte flags,
    byte[] variableHeader,
    byte[] payLoad,
    Action<long, long> sendProgress,
    Action<string, string> handleProgress,
    Action<long, long> receiveProgress)
  {
    OperateResult<byte[]> operateResult1 = new OperateResult<byte[]>();
    OperateResult result1 = this.CommunicationPipe.CommunicationLock.EnterLock(this.ReceiveTimeOut);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result1);
    try
    {
      OperateResult<bool> result2 = this.CommunicationPipe.OpenCommunication();
      if (!result2.IsSuccess)
      {
        this.CommunicationPipe.CommunicationLock.LeaveLock();
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      }
      if (result2.Content)
      {
        OperateResult result3 = this.InitializationOnConnect();
        if (!result3.IsSuccess)
        {
          this.CommunicationPipe.CommunicationLock.LeaveLock();
          return OperateResult.CreateFailedResult<byte[]>(result3);
        }
      }
      OperateResult<byte[]> result4 = MqttHelper.BuildMqttCommand(control, flags, variableHeader, payLoad, this.aesCryptography);
      if (!result4.IsSuccess)
      {
        this.CommunicationPipe.CommunicationLock.LeaveLock();
        operateResult1.CopyErrorFromOther<OperateResult<byte[]>>(result4);
        return operateResult1;
      }
      OperateResult<byte, byte[]> operateResult2 = this.ReadMqttFromCoreServer(this.CommunicationPipe, result4.Content, sendProgress, handleProgress, receiveProgress);
      if (operateResult2.IsSuccess)
      {
        if ((int) operateResult2.Content1 >> 4 == 0)
        {
          OperateResult<string, byte[]> data = MqttHelper.ExtraMqttReceiveData(operateResult2.Content1, operateResult2.Content2, this.aesCryptography);
          operateResult1.IsSuccess = false;
          operateResult1.ErrorCode = int.Parse(data.Content1);
          operateResult1.Message = Encoding.UTF8.GetString(data.Content2);
        }
        else
        {
          operateResult1.IsSuccess = operateResult2.IsSuccess;
          operateResult1.Content = operateResult2.Content2;
          operateResult1.Message = StringResources.Language.SuccessText;
        }
      }
      else
        operateResult1.CopyErrorFromOther<OperateResult<byte, byte[]>>(operateResult2);
      this.ExtraAfterReadFromCoreServer((OperateResult) operateResult2);
      this.CommunicationPipe.CommunicationLock.LeaveLock();
    }
    catch
    {
      this.CommunicationPipe.CommunicationLock.LeaveLock();
      throw;
    }
    if (!this.CommunicationPipe.IsPersistentConnection)
    {
      this.ExtraOnDisconnect();
      this.CommunicationPipe.CloseCommunication();
    }
    return operateResult1;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackHeader = true)
  {
    OperateResult<byte, byte[]> read = await this.ReadMqttFromCoreServerAsync(pipe, send, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null).ConfigureAwait(false);
    OperateResult<byte[]> operateResult = !read.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) read) : OperateResult.CreateSuccessResult<byte[]>(read.Content2);
    read = (OperateResult<byte, byte[]>) null;
    return operateResult;
  }

  private async Task<OperateResult<byte, byte[]>> ReadMqttFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    Action<long, long> sendProgress,
    Action<string, string> handleProgress,
    Action<long, long> receiveProgress)
  {
    OperateResult sendResult = await pipe.SendAsync(send).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>(sendResult);
    OperateResult<byte, byte[]> server_receive;
    OperateResult<string, byte[]> server_back;
    ConfiguredTaskAwaitable<OperateResult<byte, byte[]>> configuredTaskAwaitable;
    while (true)
    {
      configuredTaskAwaitable = MqttHelper.ReceiveMqttMessageAsync(pipe, this.ReceiveTimeOut).ConfigureAwait(false);
      server_receive = await configuredTaskAwaitable;
      if (server_receive.IsSuccess)
      {
        server_back = MqttHelper.ExtraMqttReceiveData(server_receive.Content1, server_receive.Content2);
        if (server_back.IsSuccess)
        {
          if (server_back.Content2.Length == 16 /*0x10*/)
          {
            long already = BitConverter.ToInt64(server_back.Content2, 0);
            long total = BitConverter.ToInt64(server_back.Content2, 8);
            Action<long, long> action = sendProgress;
            if (action != null)
              action(already, total);
            if (already != total)
            {
              server_receive = (OperateResult<byte, byte[]>) null;
              server_back = (OperateResult<string, byte[]>) null;
            }
            else
              goto label_21;
          }
          else
            goto label_8;
        }
        else
          goto label_6;
      }
      else
        break;
    }
    return server_receive;
label_6:
    return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) server_back);
label_8:
    return new OperateResult<byte, byte[]>(StringResources.Language.ReceiveDataLengthTooShort);
label_21:
    OperateResult<byte, byte[]> receive;
    while (true)
    {
      configuredTaskAwaitable = MqttHelper.ReceiveMqttMessageAsync(pipe, this.ReceiveTimeOut, receiveProgress).ConfigureAwait(false);
      receive = await configuredTaskAwaitable;
      if (receive.IsSuccess)
      {
        if ((int) receive.Content1 >> 4 == 15)
        {
          OperateResult<string, byte[]> extra = MqttHelper.ExtraMqttReceiveData(receive.Content1, receive.Content2);
          Action<string, string> action = handleProgress;
          if (action != null)
            action(extra.Content1, Encoding.UTF8.GetString(extra.Content2));
          extra = (OperateResult<string, byte[]>) null;
          receive = (OperateResult<byte, byte[]>) null;
        }
        else
          goto label_20;
      }
      else
        break;
    }
    return receive;
label_20:
    return OperateResult.CreateSuccessResult<byte, byte[]>(receive.Content1, receive.Content2);
  }

  private async Task<OperateResult<byte[]>> ReadMqttFromCoreServerAsync(
    byte control,
    byte flags,
    byte[] variableHeader,
    byte[] payLoad,
    Action<long, long> sendProgress,
    Action<string, string> handleProgress,
    Action<long, long> receiveProgress)
  {
    OperateResult<byte[]> result = new OperateResult<byte[]>();
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = Task.Run<OperateResult>((Func<OperateResult>) (() => this.CommunicationPipe.CommunicationLock.EnterLock(this.ReceiveTimeOut))).ConfigureAwait(false);
    OperateResult enter = await configuredTaskAwaitable;
    if (!enter.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(enter);
    try
    {
      OperateResult<bool> pipe = await this.CommunicationPipe.OpenCommunicationAsync().ConfigureAwait(false);
      if (!pipe.IsSuccess)
      {
        this.CommunicationPipe.CommunicationLock.LeaveLock();
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) pipe);
      }
      if (pipe.Content)
      {
        configuredTaskAwaitable = this.InitializationOnConnectAsync().ConfigureAwait(false);
        OperateResult ini = await configuredTaskAwaitable;
        if (!ini.IsSuccess)
        {
          this.CommunicationPipe.CommunicationLock.LeaveLock();
          return OperateResult.CreateFailedResult<byte[]>(ini);
        }
        ini = (OperateResult) null;
      }
      OperateResult<byte[]> command = MqttHelper.BuildMqttCommand(control, flags, variableHeader, payLoad, this.aesCryptography);
      if (!command.IsSuccess)
      {
        this.CommunicationPipe.CommunicationLock.LeaveLock();
        result.CopyErrorFromOther<OperateResult<byte[]>>(command);
        return result;
      }
      OperateResult<byte, byte[]> read = await this.ReadMqttFromCoreServerAsync(this.CommunicationPipe, command.Content, sendProgress, handleProgress, receiveProgress).ConfigureAwait(false);
      if (read.IsSuccess)
      {
        if ((int) read.Content1 >> 4 == 0)
        {
          OperateResult<string, byte[]> extra = MqttHelper.ExtraMqttReceiveData(read.Content1, read.Content2, this.aesCryptography);
          result.IsSuccess = false;
          result.ErrorCode = int.Parse(extra.Content1);
          result.Message = Encoding.UTF8.GetString(extra.Content2);
          extra = (OperateResult<string, byte[]>) null;
        }
        else
        {
          result.IsSuccess = read.IsSuccess;
          result.Content = read.Content2;
          result.Message = StringResources.Language.SuccessText;
        }
      }
      else
        result.CopyErrorFromOther<OperateResult<byte, byte[]>>(read);
      this.ExtraAfterReadFromCoreServer((OperateResult) read);
      this.CommunicationPipe.CommunicationLock.LeaveLock();
      pipe = (OperateResult<bool>) null;
      command = (OperateResult<byte[]>) null;
      read = (OperateResult<byte, byte[]>) null;
    }
    catch
    {
      this.CommunicationPipe.CommunicationLock.LeaveLock();
      throw;
    }
    if (!this.CommunicationPipe.IsPersistentConnection)
    {
      this.ExtraOnDisconnect();
      this.CommunicationPipe.CloseCommunication();
    }
    return result;
  }

  /// <summary>
  /// 从MQTT服务器同步读取数据，将payload发送到服务器，然后从服务器返回相关的数据，支持数据发送进度报告，服务器执行进度报告，接收数据进度报告操作<br />
  /// Synchronously read data from the MQTT server, send the payload to the server, and then return relevant data from the server,
  /// support data transmission progress report, the server executes the progress report, and receives the data progress report
  /// </summary>
  /// <remarks>
  /// 进度报告可以实现一个比较有意思的功能，可以用来数据的上传和下载，提供一个友好的进度条，因为网络的好坏通常是不确定的。
  /// </remarks>
  /// <param name="topic">主题信息</param>
  /// <param name="payload">负载数据</param>
  /// <param name="sendProgress">发送数据给服务器时的进度报告，第一个参数为已发送数据，第二个参数为总发送数据</param>
  /// <param name="handleProgress">服务器处理数据的进度报告，第一个参数Topic自定义，通常用来传送操作百分比，第二个参数自定义，通常用来表示服务器消息</param>
  /// <param name="receiveProgress">从服务器接收数据的进度报告，第一个参数为已接收数据，第二个参数为总接收数据</param>
  /// <returns>服务器返回的数据信息</returns>
  public OperateResult<string, byte[]> Read(
    string topic,
    byte[] payload,
    Action<long, long> sendProgress = null,
    Action<string, string> handleProgress = null,
    Action<long, long> receiveProgress = null)
  {
    OperateResult<byte[]> result = this.ReadMqttFromCoreServer((byte) 3, (byte) 0, MqttHelper.BuildSegCommandByString(topic), payload, sendProgress, handleProgress, receiveProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string, byte[]>((OperateResult) result) : MqttHelper.ExtraMqttReceiveData((byte) 3, result.Content, this.aesCryptography);
  }

  /// <summary>
  /// 从MQTT服务器同步读取数据，将指定编码的字符串payload发送到服务器，然后从服务器返回相关的数据，并转换为指定编码的字符串，支持数据发送进度报告，服务器执行进度报告，接收数据进度报告操作<br />
  /// Synchronously read data from the MQTT server, send the specified encoded string payload to the server,
  /// and then return the data from the server, and convert it to the specified encoded string,
  /// support data transmission progress report, the server executes the progress report, and receives the data progress report
  /// </summary>
  /// <param name="topic">主题信息</param>
  /// <param name="payload">负载数据</param>
  /// <param name="sendProgress">发送数据给服务器时的进度报告，第一个参数为已发送数据，第二个参数为总发送数据</param>
  /// <param name="handleProgress">服务器处理数据的进度报告，第一个参数Topic自定义，通常用来传送操作百分比，第二个参数自定义，通常用来表示服务器消息</param>
  /// <param name="receiveProgress">从服务器接收数据的进度报告，第一个参数为已接收数据，第二个参数为总接收数据</param>
  /// <returns>服务器返回的数据信息</returns>
  public OperateResult<string, string> ReadString(
    string topic,
    string payload,
    Action<long, long> sendProgress = null,
    Action<string, string> handleProgress = null,
    Action<long, long> receiveProgress = null)
  {
    OperateResult<string, byte[]> result = this.Read(topic, string.IsNullOrEmpty(payload) ? (byte[]) null : this.stringEncoding.GetBytes(payload), sendProgress, handleProgress, receiveProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string, string>((OperateResult) result) : OperateResult.CreateSuccessResult<string, string>(result.Content1, this.stringEncoding.GetString(result.Content2));
  }

  /// <summary>
  /// 读取MQTT服务器注册的RPC接口，忽略返回的Topic数据，直接将结果转换为泛型对象，如果JSON转换失败，将返回错误，参数传递主题和数据负载，
  /// 数据负载示例："{\"address\": \"100\",\"length\": 10}" 本质是一个字符串。<br />
  /// Read the RPC interface registered by the MQTT server, ignore the returned Topic data, and directly convert the result into a generic object.
  /// If the JSON conversion fails, an error will be returned. The parameter passes the topic and the data payload.
  /// The data payload example: "{\"address\ ": \"100\",\"length\": 10}" is essentially a string.
  /// </summary>
  /// <remarks>
  /// 关于类型对象，需要和服务器返回的类型一致，如果服务器返回了 <see cref="T:System.String" />, 这里也是 <see cref="T:System.String" />, 如果是自定义对象，客户端没有该类型，可以使用 <see cref="T:Newtonsoft.Json.Linq.JObject" />
  /// </remarks>
  /// <typeparam name="T">泛型对象，需要和返回的数据匹配，如果返回的是 int 数组，那么这里就是 int[]，务必和服务器侧定义的返回类型一致</typeparam>
  /// <param name="topic">主题信息，也是服务器的 RPC 接口信息</param>
  /// <param name="payload">传递的参数信息，示例："{\"address\": \"100\",\"length\": 10}" 本质是一个字符串。</param>
  /// <returns>服务器返回的数据信息</returns>
  public OperateResult<T> ReadRpc<T>(string topic, string payload)
  {
    OperateResult<string, string> operateResult = this.ReadString(topic, payload);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<T>();
    try
    {
      return OperateResult.CreateSuccessResult<T>(JsonConvert.DeserializeObject<T>(operateResult.Content2));
    }
    catch (Exception ex)
    {
      return new OperateResult<T>($"JSON failed: {ex.Message}{Environment.NewLine}Source Data: {operateResult.Content2}");
    }
  }

  /// <summary>
  /// 读取MQTT服务器注册的RPC接口，忽略返回的Topic数据，直接将结果转换为泛型对象，如果JSON转换失败，将返回错误，参数传递主题和数据负载，
  /// 数据负载示例：new { address = "", length = 0 } 本质是一个匿名对象。<br />
  /// Read the RPC interface registered by the MQTT server, ignore the returned Topic data, and directly convert the result into a generic object.
  /// If the JSON conversion fails, an error will be returned. The parameter passes the topic and the data payload.
  /// The data payload example: new { address = "", length = 0 } is essentially an anonymous object.
  /// </summary>
  /// <remarks>
  /// 关于类型对象，需要和服务器返回的类型一致，如果服务器返回了 <see cref="T:System.String" />, 这里也是 <see cref="T:System.String" />, 如果是自定义对象，客户端没有该类型，可以使用 <see cref="T:Newtonsoft.Json.Linq.JObject" />
  /// </remarks>
  /// <typeparam name="T">泛型对象，需要和返回的数据匹配，如果返回的是 int 数组，那么这里就是 int[]</typeparam>
  /// <param name="topic">主题信息，也是服务器的 RPC 接口信息</param>
  /// <param name="payload">传递的参数信息，示例：new { address = "", length = 0 } 本质是一个匿名对象。</param>
  /// <returns>服务器返回的数据信息</returns>
  public OperateResult<T> ReadRpc<T>(string topic, object payload)
  {
    return this.ReadRpc<T>(topic, payload == null ? "{}" : payload.ToJsonString());
  }

  /// <summary>
  /// 读取服务器的已经注册的API信息列表，将返回API的主题路径，注释信息，示例的传入的数据信息。<br />
  /// Read the registered API information list of the server, and return the API subject path, annotation information, and sample incoming data information.
  /// </summary>
  /// <returns>包含是否成功的api信息的列表</returns>
  public OperateResult<MqttRpcApiInfo[]> ReadRpcApis()
  {
    OperateResult<byte[]> result = this.ReadMqttFromCoreServer((byte) 8, (byte) 0, MqttHelper.BuildSegCommandByString(""), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<MqttRpcApiInfo[]>((OperateResult) result);
    OperateResult<string, byte[]> data = MqttHelper.ExtraMqttReceiveData((byte) 3, result.Content, this.aesCryptography);
    return !data.IsSuccess ? OperateResult.CreateFailedResult<MqttRpcApiInfo[]>((OperateResult) data) : OperateResult.CreateSuccessResult<MqttRpcApiInfo[]>(JArray.Parse(Encoding.UTF8.GetString(data.Content2)).ToObject<MqttRpcApiInfo[]>());
  }

  /// <summary>
  /// 读取服务器的指定的API接口的每天的调用次数，如果API接口不存在，或是还没有调用数据，则返回失败。<br />
  /// Read the number of calls per day of the designated API interface of the server.
  /// If the API interface does not exist or the data has not been called yet, it returns a failure.
  /// </summary>
  /// <remarks>如果api的参数为空字符串，就是请求所有的接口的调用的统计信息。</remarks>
  /// <param name="api">等待请求的API的接口信息，如果为空，就是请求所有的接口的调用的统计信息。</param>
  /// <returns>最近几日的连续的调用情况，例如[1,2,3]，表示前提调用1次，昨天调用2次，今天3次</returns>
  public OperateResult<long[]> ReadRpcApiLog(string api)
  {
    OperateResult<byte[]> result = this.ReadMqttFromCoreServer((byte) 6, (byte) 0, MqttHelper.BuildSegCommandByString(api), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<long[]>((OperateResult) result);
    OperateResult<string, byte[]> data = MqttHelper.ExtraMqttReceiveData((byte) 3, result.Content, this.aesCryptography);
    return !data.IsSuccess ? OperateResult.CreateFailedResult<long[]>((OperateResult) data) : OperateResult.CreateSuccessResult<long[]>(Encoding.UTF8.GetString(data.Content2).ToStringArray<long>());
  }

  /// <summary>
  /// 读取服务器的已经驻留的所有消息的主题列表<br />
  /// Read the topic list of all messages that have resided on the server
  /// </summary>
  /// <returns>消息列表对象</returns>
  public OperateResult<string[]> ReadRetainTopics()
  {
    OperateResult<byte[]> result = this.ReadMqttFromCoreServer((byte) 4, (byte) 0, MqttHelper.BuildSegCommandByString(""), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result);
    OperateResult<string, byte[]> data = MqttHelper.ExtraMqttReceiveData((byte) 3, result.Content, this.aesCryptography);
    return !data.IsSuccess ? OperateResult.CreateFailedResult<string[]>((OperateResult) data) : OperateResult.CreateSuccessResult<string[]>(HslProtocol.UnPackStringArrayFromByte(data.Content2));
  }

  /// <summary>
  /// 读取服务器的已经驻留的指定主题的消息内容<br />
  /// Read the topic list of all messages that have resided on the server
  /// </summary>
  /// <param name="topic">指定的主题消息</param>
  /// <param name="receiveProgress">结果进度报告</param>
  /// <returns>消息列表对象</returns>
  public OperateResult<MqttClientApplicationMessage> ReadTopicPayload(
    string topic,
    Action<long, long> receiveProgress = null)
  {
    OperateResult<byte[]> result = this.ReadMqttFromCoreServer((byte) 5, (byte) 0, MqttHelper.BuildSegCommandByString(topic), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, receiveProgress);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<MqttClientApplicationMessage>((OperateResult) result);
    OperateResult<string, byte[]> data = MqttHelper.ExtraMqttReceiveData((byte) 3, result.Content, this.aesCryptography);
    return !data.IsSuccess ? OperateResult.CreateFailedResult<MqttClientApplicationMessage>((OperateResult) data) : OperateResult.CreateSuccessResult<MqttClientApplicationMessage>(JObject.Parse(Encoding.UTF8.GetString(data.Content2)).ToObject<MqttClientApplicationMessage>());
  }

  /// <summary>读取服务器里当前的会话信息</summary>
  /// <returns>会话信息</returns>
  public OperateResult<MqttSessionInfo[]> ReadSessions()
  {
    OperateResult<byte[]> result = this.ReadMqttFromCoreServer((byte) 11, (byte) 0, MqttHelper.BuildSegCommandByString(""), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<MqttSessionInfo[]>((OperateResult) result);
    OperateResult<string, byte[]> data = MqttHelper.ExtraMqttReceiveData((byte) 3, result.Content, this.aesCryptography);
    return !data.IsSuccess ? OperateResult.CreateFailedResult<MqttSessionInfo[]>((OperateResult) data) : OperateResult.CreateSuccessResult<MqttSessionInfo[]>(JArray.Parse(Encoding.UTF8.GetString(data.Content2)).ToObject<MqttSessionInfo[]>());
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.Read(System.String,System.Byte[],System.Action{System.Int64,System.Int64},System.Action{System.String,System.String},System.Action{System.Int64,System.Int64})" />
  public async Task<OperateResult<string, byte[]>> ReadAsync(
    string topic,
    byte[] payload,
    Action<long, long> sendProgress = null,
    Action<string, string> handleProgress = null,
    Action<long, long> receiveProgress = null)
  {
    OperateResult<byte[]> read = await this.ReadMqttFromCoreServerAsync((byte) 3, (byte) 0, MqttHelper.BuildSegCommandByString(topic), payload, sendProgress, handleProgress, receiveProgress);
    OperateResult<string, byte[]> operateResult = read.IsSuccess ? MqttHelper.ExtraMqttReceiveData((byte) 3, read.Content, this.aesCryptography) : OperateResult.CreateFailedResult<string, byte[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.ReadString(System.String,System.String,System.Action{System.Int64,System.Int64},System.Action{System.String,System.String},System.Action{System.Int64,System.Int64})" />
  public async Task<OperateResult<string, string>> ReadStringAsync(
    string topic,
    string payload,
    Action<long, long> sendProgress = null,
    Action<string, string> handleProgress = null,
    Action<long, long> receiveProgress = null)
  {
    OperateResult<string, byte[]> read = await this.ReadAsync(topic, string.IsNullOrEmpty(payload) ? (byte[]) null : this.stringEncoding.GetBytes(payload), sendProgress, handleProgress, receiveProgress);
    OperateResult<string, string> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<string, string>(read.Content1, this.stringEncoding.GetString(read.Content2)) : OperateResult.CreateFailedResult<string, string>((OperateResult) read);
    read = (OperateResult<string, byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.ReadRpc``1(System.String,System.String)" />
  public async Task<OperateResult<T>> ReadRpcAsync<T>(string topic, string payload)
  {
    OperateResult<string, string> read = await this.ReadStringAsync(topic, payload);
    if (!read.IsSuccess)
      return read.ConvertFailed<T>();
    try
    {
      return OperateResult.CreateSuccessResult<T>(JsonConvert.DeserializeObject<T>(read.Content2));
    }
    catch (Exception ex)
    {
      return new OperateResult<T>($"JSON failed: {ex.Message}{Environment.NewLine}Source Data: {read.Content2}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.ReadRpc``1(System.String,System.Object)" />
  public async Task<OperateResult<T>> ReadRpcAsync<T>(string topic, object payload)
  {
    OperateResult<T> operateResult = await this.ReadRpcAsync<T>(topic, payload == null ? "{}" : payload.ToJsonString());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.ReadRpcApis" />
  public async Task<OperateResult<MqttRpcApiInfo[]>> ReadRpcApisAsync()
  {
    OperateResult<byte[]> read = await this.ReadMqttFromCoreServerAsync((byte) 8, (byte) 0, MqttHelper.BuildSegCommandByString(""), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<MqttRpcApiInfo[]>((OperateResult) read);
    OperateResult<string, byte[]> mqtt = MqttHelper.ExtraMqttReceiveData((byte) 3, read.Content, this.aesCryptography);
    return mqtt.IsSuccess ? OperateResult.CreateSuccessResult<MqttRpcApiInfo[]>(JArray.Parse(Encoding.UTF8.GetString(mqtt.Content2)).ToObject<MqttRpcApiInfo[]>()) : OperateResult.CreateFailedResult<MqttRpcApiInfo[]>((OperateResult) mqtt);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.ReadRpcApiLog(System.String)" />
  public async Task<OperateResult<long[]>> ReadRpcApiLogAsync(string api)
  {
    OperateResult<byte[]> read = await this.ReadMqttFromCoreServerAsync((byte) 6, (byte) 0, MqttHelper.BuildSegCommandByString(api), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<long[]>((OperateResult) read);
    OperateResult<string, byte[]> mqtt = MqttHelper.ExtraMqttReceiveData((byte) 3, read.Content, this.aesCryptography);
    if (!mqtt.IsSuccess)
      return OperateResult.CreateFailedResult<long[]>((OperateResult) mqtt);
    string content = Encoding.UTF8.GetString(mqtt.Content2);
    return OperateResult.CreateSuccessResult<long[]>(content.ToStringArray<long>());
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.ReadRetainTopics" />
  public async Task<OperateResult<string[]>> ReadRetainTopicsAsync()
  {
    OperateResult<byte[]> read = await this.ReadMqttFromCoreServerAsync((byte) 4, (byte) 0, MqttHelper.BuildSegCommandByString(""), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, (Action<long, long>) null);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) read);
    OperateResult<string, byte[]> mqtt = MqttHelper.ExtraMqttReceiveData((byte) 3, read.Content, this.aesCryptography);
    return mqtt.IsSuccess ? OperateResult.CreateSuccessResult<string[]>(HslProtocol.UnPackStringArrayFromByte(mqtt.Content2)) : OperateResult.CreateFailedResult<string[]>((OperateResult) mqtt);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.ReadTopicPayload(System.String,System.Action{System.Int64,System.Int64})" />
  public async Task<OperateResult<MqttClientApplicationMessage>> ReadTopicPayloadAsync(
    string topic,
    Action<long, long> receiveProgress = null)
  {
    OperateResult<byte[]> read = await this.ReadMqttFromCoreServerAsync((byte) 5, (byte) 0, MqttHelper.BuildSegCommandByString(topic), (byte[]) null, (Action<long, long>) null, (Action<string, string>) null, receiveProgress);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<MqttClientApplicationMessage>((OperateResult) read);
    OperateResult<string, byte[]> mqtt = MqttHelper.ExtraMqttReceiveData((byte) 3, read.Content, this.aesCryptography);
    return mqtt.IsSuccess ? OperateResult.CreateSuccessResult<MqttClientApplicationMessage>(JObject.Parse(Encoding.UTF8.GetString(mqtt.Content2)).ToObject<MqttClientApplicationMessage>()) : OperateResult.CreateFailedResult<MqttClientApplicationMessage>((OperateResult) mqtt);
  }

  private OperateResult<CommunicationPipe> ConnectMqttFileServer(
    byte opCode,
    string groups,
    string[] fileNames)
  {
    PipeTcpNet pipe = new PipeTcpNet(this.IpAddress, this.Port);
    pipe.ConnectTimeOut = this.ConnectTimeOut;
    OperateResult result1 = (OperateResult) pipe.OpenCommunication();
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(result1);
    OperateResult result2 = this.InitializationMqttSocket((CommunicationPipe) pipe, "FILE");
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(result2);
    PipeTcpNet pipeTcpNet = pipe;
    int head = (int) opCode;
    string[] data;
    if (!string.IsNullOrEmpty(groups))
      data = groups.Split(new char[2]{ '\\', '/' }, StringSplitOptions.RemoveEmptyEntries);
    else
      data = (string[]) null;
    byte[] payLoad = HslProtocol.PackStringArrayToByte(data);
    byte[] content = MqttHelper.BuildMqttCommand((byte) head, (byte[]) null, payLoad).Content;
    OperateResult result3 = pipeTcpNet.Send(content);
    if (!result3.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(result3);
    OperateResult result4 = pipe.Send(MqttHelper.BuildMqttCommand(opCode, (byte[]) null, HslProtocol.PackStringArrayToByte(fileNames)).Content);
    if (!result4.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(result4);
    OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage((CommunicationPipe) pipe, this.ReceiveTimeOut);
    if (!mqttMessage.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>((OperateResult) mqttMessage);
    if (mqttMessage.Content1 != (byte) 0)
      return OperateResult.CreateSuccessResult<CommunicationPipe>((CommunicationPipe) pipe);
    pipe.CloseCommunication();
    return new OperateResult<CommunicationPipe>(Encoding.UTF8.GetString(mqttMessage.Content2));
  }

  private OperateResult DownloadFileBase(
    string groups,
    string fileName,
    Action<long, long> processReport,
    object source,
    HslCancelToken cancelToken)
  {
    OperateResult<CommunicationPipe> operateResult = this.ConnectMqttFileServer((byte) 101, groups, new string[1]
    {
      fileName
    });
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult mqttFile = (OperateResult) MqttHelper.ReceiveMqttFile(operateResult.Content, source, processReport, this.aesCryptography, cancelToken);
    if (!mqttFile.IsSuccess)
      return mqttFile;
    operateResult.Content?.CloseCommunication();
    return OperateResult.CreateSuccessResult();
  }

  private OperateResult<T> OperateMqttFileContent<T>(
    byte opCode,
    string groups,
    string[] fileNames,
    Func<OperateResult<byte, byte[]>, T> trans)
  {
    OperateResult<CommunicationPipe> result = this.ConnectMqttFileServer(opCode, groups, fileNames);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) result);
    OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(result.Content, 60000);
    if (!mqttMessage.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) mqttMessage);
    result.Content?.CloseCommunication();
    if (mqttMessage.Content1 == (byte) 0)
      return new OperateResult<T>(mqttMessage.Content2 == null ? string.Empty : Encoding.UTF8.GetString(mqttMessage.Content2));
    try
    {
      return OperateResult.CreateSuccessResult<T>(trans(mqttMessage));
    }
    catch (Exception ex)
    {
      return new OperateResult<T>(ex.Message);
    }
  }

  /// <summary>
  /// [文件引擎] 从远程服务器下载一个文件到本地，需要指定文件类别，文件名，进度报告，本地保存的文件名<br />
  /// [File Engine] To download a file from a remote server to the local, you need to specify the file category, file name, progress report, and file name saved locally
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分 </param>
  /// <param name="fileName">文件名称，例如 123.txt</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数，如果不需要，传入NULL即可</param>
  /// <param name="fileSaveName">本地保存的文件名</param>
  /// <param name="cancelToken">取消下载操作的令牌</param>
  /// <returns>是否下载成功</returns>
  public OperateResult DownloadFile(
    string groups,
    string fileName,
    Action<long, long> processReport,
    string fileSaveName,
    HslCancelToken cancelToken = null)
  {
    return this.DownloadFileBase(groups, fileName, processReport, (object) fileSaveName, cancelToken);
  }

  /// <summary>
  /// [文件引擎] 从远程服务器下载一个文件到流中，需要指定文件类别，文件名，进度报告，本地保存的文件名<br />
  /// To download a file from a remote server to the stream, you need to specify the file category, file name, progress report, and file name saved locally
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分 </param>
  /// <param name="fileName">文件名称，例如 123.txt</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数</param>
  /// <param name="stream">数据流</param>
  /// <param name="cancelToken">取消下载操作的令牌</param>
  /// <returns>是否下载成功</returns>
  public OperateResult DownloadFile(
    string groups,
    string fileName,
    Action<long, long> processReport,
    Stream stream,
    HslCancelToken cancelToken = null)
  {
    return this.DownloadFileBase(groups, fileName, processReport, (object) stream, cancelToken);
  }

  /// <summary>
  /// [文件引擎] 从远程服务器下载一个文件，生成一个Bitmap图片对象，需要指定文件类别，文件名，进度报告，可用于用户头像的存储<br />
  /// [File Engine] Download a file from a remote server and generate a Bitmap image object. You need to specify the file category, file name, and progress report, which can be used to store the user's avatar
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="fileName">文件名称，例如 123.txt</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数</param>
  /// <param name="cancelToken">取消下载操作的令牌</param>
  /// <returns>如果下载成功，则携带图片资源对象</returns>
  public OperateResult<Bitmap> DownloadBitmap(
    string groups,
    string fileName,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    MemoryStream source = new MemoryStream();
    OperateResult result = this.DownloadFileBase(groups, fileName, processReport, (object) source, cancelToken);
    if (!result.IsSuccess)
    {
      source.Dispose();
      return OperateResult.CreateFailedResult<Bitmap>(result);
    }
    Bitmap bitmap = new Bitmap((Stream) source);
    source.Dispose();
    return OperateResult.CreateSuccessResult<Bitmap>(bitmap);
  }

  /// <summary>
  /// [文件引擎] 上传一个Bitmap图片对象到服务器指定的分类下面，需要指定分类信息，服务器保存的文件名，描述信息，支持进度报告<br />
  /// [File Engine] Upload a Bitmap image object to the category specified by the server, you need to specify the category information,
  /// the file name saved by the server, description information, and support for progress reports
  /// </summary>
  /// <param name="bitmap">图片对象</param>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="serverName">在服务器保存的文件名称</param>
  /// <param name="fileTag">文件的额外的描述信息</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数</param>
  /// <param name="cancelToken">取消上传操作的令牌</param>
  /// <returns>是否上传成功</returns>
  public OperateResult UploadFile(
    Bitmap bitmap,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    MemoryStream source = new MemoryStream();
    if (bitmap.RawFormat != null)
      bitmap.Save((Stream) source, bitmap.RawFormat);
    else
      bitmap.Save((Stream) source, ImageFormat.Bmp);
    OperateResult operateResult = this.UploadFileBase((object) source, groups, serverName, fileTag, processReport, cancelToken);
    source.Dispose();
    return operateResult;
  }

  /// <summary>
  /// [文件引擎] 上传文件给服务器，需要指定上传的数据内容，上传到服务器的分类信息，支持进度汇报功能。<br />
  /// [File Engine] To upload files to the server, you need to specify the content of the uploaded data,
  /// the classification information uploaded to the server, and support the progress report function.
  /// </summary>
  /// <param name="source">数据源，可以是文件名，也可以是数据流</param>
  /// <param name="serverName">在服务器保存的文件名，不包含驱动器路径</param>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="fileTag">文件的额外的描述信息</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数</param>
  /// <param name="cancelToken">用户取消的令牌信息</param>
  /// <returns>是否成功的结果对象</returns>
  private OperateResult UploadFileBase(
    object source,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken)
  {
    OperateResult<CommunicationPipe> operateResult1 = this.ConnectMqttFileServer((byte) 102, groups, new string[1]
    {
      serverName
    });
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    switch (source)
    {
      case string filename:
        OperateResult operateResult2 = MqttHelper.SendMqttFile(operateResult1.Content, filename, serverName, fileTag, processReport, this.aesCryptography, cancelToken);
        if (!operateResult2.IsSuccess)
          return operateResult2;
        break;
      case Stream stream:
        OperateResult operateResult3 = MqttHelper.SendMqttFile(operateResult1.Content, stream, serverName, fileTag, processReport, this.aesCryptography, cancelToken);
        if (!operateResult3.IsSuccess)
          return operateResult3;
        break;
      default:
        operateResult1.Content?.CloseCommunication();
        this.LogNet?.WriteError(this.ToString(), StringResources.Language.DataSourceFormatError);
        return new OperateResult(StringResources.Language.DataSourceFormatError);
    }
    OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(operateResult1.Content, 60000);
    if (!mqttMessage.IsSuccess)
      return (OperateResult) mqttMessage;
    operateResult1.Content?.CloseCommunication();
    return mqttMessage.Content1 != (byte) 0 ? OperateResult.CreateSuccessResult() : new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
  }

  /// <summary>
  /// [文件引擎] 上传文件给服务器，需要指定上传文件的路径信息，服务器保存的名字，以及上传到服务器的分类信息，支持进度汇报功能。<br />
  /// [File Engine] To upload a file to the server, you need to specify the path information of the uploaded file, the name saved by the server,
  /// and the classification information uploaded to the server to support the progress report function.
  /// </summary>
  /// <param name="fileName">文件名，需要指定完整的路径信息，文件必须存在，否则发送失败</param>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="serverName">服务器端保存的文件名</param>
  /// <param name="fileTag">文件的额外的描述信息</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数</param>
  /// <param name="cancelToken">取消上传操作的令牌</param>
  /// <returns>是否上传成功的结果对象</returns>
  public OperateResult UploadFile(
    string fileName,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    return !System.IO.File.Exists(fileName) ? new OperateResult(StringResources.Language.FileNotExist) : this.UploadFileBase((object) fileName, groups, serverName, fileTag, processReport, cancelToken);
  }

  /// <summary>
  /// [文件引擎] 上传文件给服务器，需要指定上传文件的路径信息(服务器保存的名称就是文件名)，以及上传到服务器的分类信息，支持进度汇报功能。<br />
  /// [File Engine] To upload a file to the server, you need to specify the path information of the uploaded file (the name saved by the server is the file name),
  /// as well as the classification information uploaded to the server, to support the progress report function.
  /// </summary>
  /// <param name="fileName">文件名，需要指定完整的路径信息，文件必须存在，否则发送失败</param>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="fileTag">文件的额外的描述信息</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数</param>
  /// <param name="cancelToken">取消上传操作的令牌</param>
  /// <returns>是否上传成功的结果对象</returns>
  public OperateResult UploadFile(
    string fileName,
    string groups,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    if (!System.IO.File.Exists(fileName))
      return new OperateResult(StringResources.Language.FileNotExist);
    FileInfo fileInfo = new FileInfo(fileName);
    return this.UploadFileBase((object) fileName, groups, fileInfo.Name, fileTag, processReport, cancelToken);
  }

  /// <summary>
  /// [文件引擎] 上传流给服务器，需要指定流，服务器保存的名字，以及上传到服务器的分类信息，支持进度汇报功能。<br />
  /// [File Engine] To upload a stream to the server, you need to specify the stream, the name saved by the server, and the classification information uploaded to the server to support the progress reporting function.
  /// </summary>
  /// <param name="stream">流</param>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="fileTag"></param>
  /// <param name="serverName">文件的额外的描述信息</param>
  /// <param name="processReport">进度报告，第一个参数是已完成字节数，第二个参数是总字节数</param>
  /// <param name="cancelToken">取消上传操作的令牌</param>
  /// <returns>是否上传成功的结果对象</returns>
  public OperateResult UploadFile(
    Stream stream,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    return this.UploadFileBase((object) stream, groups, serverName, fileTag, processReport, cancelToken);
  }

  private OperateResult<T[]> DownloadStringArrays<T>(
    byte protocol,
    string groups,
    string[] fileNames)
  {
    return this.OperateMqttFileContent<T[]>(protocol, groups, fileNames, (Func<OperateResult<byte, byte[]>, T[]>) (m => JArray.Parse(Encoding.UTF8.GetString(m.Content2)).ToObject<T[]>()));
  }

  /// <summary>
  /// [文件引擎] 下载指定分类信息的所有的文件描述信息，需要指定分类信息，例如：Files/Personal/Admin<br />
  /// [File Engine] To download all the file description information of the specified classification information,
  /// you need to specify the classification information, for example: Files/Personal/Admin
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <returns>当前分类下所有的文件描述信息</returns>
  public OperateResult<GroupFileItem[]> DownloadPathFileNames(string groups)
  {
    return this.DownloadStringArrays<GroupFileItem>((byte) 105, groups, (string[]) null);
  }

  /// <summary>
  /// 下载指定分类信息的全部子分类信息<br />
  /// Download all sub-category information of the specified category information
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <returns>当前分类下所有的子分类信息</returns>
  public OperateResult<string[]> DownloadPathFolders(string groups)
  {
    return this.DownloadStringArrays<string>((byte) 106, groups, (string[]) null);
  }

  /// <summary>
  /// [文件引擎] 请求服务器指定分类是否存在指定的文件名，需要指定分类信息，文件名<br />
  /// [File Engine] Request the server to specify whether the specified file name exists in the specified category, need to specify the category information, file name
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="fileName">文件名信息，例如 123.txt</param>
  /// <returns>Content为True表示存在，否则为不存在</returns>
  public OperateResult<bool> IsFileExists(string groups, string fileName)
  {
    return this.OperateMqttFileContent<bool>((byte) 107, groups, new string[1]
    {
      fileName
    }, (Func<OperateResult<byte, byte[]>, bool>) (m => m.Content1 == (byte) 1));
  }

  /// <summary>
  /// [文件引擎] 删除服务器的指定的文件名，需要指定分类信息，文件名<br />
  /// [File Engine] Delete the specified file name of the server, need to specify the classification information, file name
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="fileName">文件名信息</param>
  /// <returns>是否删除成功</returns>
  public OperateResult DeleteFile(string groups, string fileName)
  {
    return this.DeleteFile(groups, new string[1]{ fileName });
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DeleteFile(System.String,System.String)" />
  public OperateResult DeleteFile(string groups, string[] fileNames)
  {
    return (OperateResult) this.OperateMqttFileContent<byte>((byte) 103, groups, fileNames, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
  }

  /// <summary>
  /// [文件引擎] 删除服务器上指定的分类的所有的文件，不可逆操作，谨慎操作。<br />
  /// [File Engine] Delete the specified classification information and all files managed on the server, irreversible operation, and careful operation.
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <returns>是否删除成功</returns>
  public OperateResult DeleteFolderFiles(string groups)
  {
    return (OperateResult) this.OperateMqttFileContent<byte>((byte) 110, groups, (string[]) null, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
  }

  /// <summary>
  /// [文件引擎] 删除服务器上指定的分类信息及管理的所有的文件，包含所有的子分类信息，不可逆操作，谨慎操作。<br />
  /// [File Engine] Delete the specified classification information and all files managed on the server,
  /// including all sub-classification information, irreversible operation, and careful operation.
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <returns>是否删除成功</returns>
  public OperateResult DeleteFolder(string groups)
  {
    return (OperateResult) this.OperateMqttFileContent<byte>((byte) 104, groups, (string[]) null, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
  }

  /// <summary>
  /// [文件引擎] 重命名服务器上指定的路径信息，需要指定新的路径名称，当新的路径已经存在的时候，命名失败。<br />
  /// [File Engine] Renames the specified path information on the server, you need to specify a new path name, and when the new path already exists, the naming fails.
  /// </summary>
  /// <param name="groups">旧的路径信息</param>
  /// <param name="newName">新的路径名称</param>
  /// <returns>是否重命名成功</returns>
  public OperateResult RenameFolder(string groups, string newName)
  {
    return (OperateResult) this.OperateMqttFileContent<byte>((byte) 111, groups, new string[1]
    {
      newName
    }, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
  }

  /// <summary>
  /// [文件引擎] 获取服务器文件夹的指定目录的文件统计信息，包括文件数量，总大小，最后更新时间<br />
  /// [File Engine] Get the file statistics of the specified directory of the server folder, including the number of files, the total size, and the last update time
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <returns>服务器文件大小的结果对象，单位：字节数</returns>
  public OperateResult<GroupFileInfo> GetGroupFileInfo(string groups)
  {
    return this.OperateMqttFileContent<GroupFileInfo>((byte) 108, groups, (string[]) null, (Func<OperateResult<byte, byte[]>, GroupFileInfo>) (m => JObject.Parse(Encoding.UTF8.GetString(m.Content2)).ToObject<GroupFileInfo>()));
  }

  /// <summary>
  /// [文件引擎] 获取服务器文件夹的指定目录的所有子目录的文件信息，包括每个子目录的文件数量，总大小，最后更新时间<br />
  /// [File Engine] Get the file information of all subdirectories of the specified directory of the server folder, including the number of files in each subdirectory, the total size, and the last update time
  /// </summary>
  /// <param name="groups">文件的类别，例如 Files/Personal/Admin 按照斜杠来区分</param>
  /// <param name="withLastFileInfo">是否让服务器携带最新的文件信息返回</param>
  /// <returns>服务器文件大小的结果对象，单位：字节数</returns>
  public OperateResult<GroupFileInfo[]> GetSubGroupFileInfos(string groups, bool withLastFileInfo = false)
  {
    string groups1 = groups;
    string[] fileNames;
    if (!withLastFileInfo)
      fileNames = (string[]) null;
    else
      fileNames = new string[1]{ "1" };
    return this.OperateMqttFileContent<GroupFileInfo[]>((byte) 109, groups1, fileNames, (Func<OperateResult<byte, byte[]>, GroupFileInfo[]>) (m => JArray.Parse(Encoding.UTF8.GetString(m.Content2)).ToObject<GroupFileInfo[]>()));
  }

  private async Task<OperateResult<CommunicationPipe>> ConnectMqttFileServerAsync(
    byte opCode,
    string groups,
    string[] fileNames)
  {
    PipeTcpNet pipe = new PipeTcpNet(this.IpAddress, this.Port);
    pipe.ConnectTimeOut = this.ConnectTimeOut;
    OperateResult<bool> operateResult = await pipe.OpenCommunicationAsync();
    OperateResult open = (OperateResult) operateResult;
    operateResult = (OperateResult<bool>) null;
    if (!open.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(open);
    OperateResult ini = await this.InitializationMqttSocketAsync((CommunicationPipe) pipe, "FILE");
    if (!ini.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(ini);
    PipeTcpNet pipeTcpNet = pipe;
    int head = (int) opCode;
    string[] data;
    if (!string.IsNullOrEmpty(groups))
      data = groups.Split(new char[2]{ '\\', '/' }, StringSplitOptions.RemoveEmptyEntries);
    else
      data = (string[]) null;
    byte[] payLoad = HslProtocol.PackStringArrayToByte(data);
    byte[] content = MqttHelper.BuildMqttCommand((byte) head, (byte[]) null, payLoad).Content;
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = pipeTcpNet.SendAsync(content).ConfigureAwait(false);
    OperateResult sendClass = await configuredTaskAwaitable;
    if (!sendClass.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(sendClass);
    configuredTaskAwaitable = pipe.SendAsync(MqttHelper.BuildMqttCommand(opCode, (byte[]) null, HslProtocol.PackStringArrayToByte(fileNames)).Content).ConfigureAwait(false);
    OperateResult sendString = await configuredTaskAwaitable;
    if (!sendString.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>(sendString);
    OperateResult<byte, byte[]> legal = await MqttHelper.ReceiveMqttMessageAsync((CommunicationPipe) pipe, 60000).ConfigureAwait(false);
    if (!legal.IsSuccess)
      return OperateResult.CreateFailedResult<CommunicationPipe>((OperateResult) legal);
    if (legal.Content1 != (byte) 0)
      return OperateResult.CreateSuccessResult<CommunicationPipe>((CommunicationPipe) pipe);
    pipe?.CloseCommunication();
    return new OperateResult<CommunicationPipe>(Encoding.UTF8.GetString(legal.Content2));
  }

  private async Task<OperateResult> DownloadFileBaseAsync(
    string groups,
    string fileName,
    Action<long, long> processReport,
    object source,
    HslCancelToken cancelToken)
  {
    OperateResult<CommunicationPipe> socketResult = await this.ConnectMqttFileServerAsync((byte) 101, groups, new string[1]
    {
      fileName
    }).ConfigureAwait(false);
    if (!socketResult.IsSuccess)
      return (OperateResult) socketResult;
    OperateResult<FileBaseInfo> operateResult = await MqttHelper.ReceiveMqttFileAsync(socketResult.Content, source, processReport, this.aesCryptography, cancelToken).ConfigureAwait(false);
    OperateResult result = (OperateResult) operateResult;
    operateResult = (OperateResult<FileBaseInfo>) null;
    if (!result.IsSuccess)
      return result;
    socketResult.Content?.CloseCommunication();
    return OperateResult.CreateSuccessResult();
  }

  private async Task<OperateResult<T>> OperateMqttFileContentAsync<T>(
    byte opCode,
    string groups,
    string[] fileNames,
    Func<OperateResult<byte, byte[]>, T> trans)
  {
    OperateResult<CommunicationPipe> socketResult = await this.ConnectMqttFileServerAsync(opCode, groups, fileNames).ConfigureAwait(false);
    if (!socketResult.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) socketResult);
    OperateResult<byte, byte[]> receive = await MqttHelper.ReceiveMqttMessageAsync(socketResult.Content, 60000);
    if (!receive.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) receive);
    socketResult.Content?.CloseCommunication();
    if (receive.Content1 == (byte) 0)
      return new OperateResult<T>(receive.Content2 == null ? string.Empty : Encoding.UTF8.GetString(receive.Content2));
    try
    {
      return OperateResult.CreateSuccessResult<T>(trans(receive));
    }
    catch (Exception ex)
    {
      return new OperateResult<T>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DownloadFile(System.String,System.String,System.Action{System.Int64,System.Int64},System.String,HslCommunication.Core.HslCancelToken)" />
  public async Task<OperateResult> DownloadFileAsync(
    string groups,
    string fileName,
    Action<long, long> processReport,
    string fileSaveName,
    HslCancelToken cancelToken = null)
  {
    OperateResult operateResult = await this.DownloadFileBaseAsync(groups, fileName, processReport, (object) fileSaveName, cancelToken);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DownloadFile(System.String,System.String,System.Action{System.Int64,System.Int64},System.IO.Stream,HslCommunication.Core.HslCancelToken)" />
  public async Task<OperateResult> DownloadFileAsync(
    string groups,
    string fileName,
    Action<long, long> processReport,
    Stream stream,
    HslCancelToken cancelToken = null)
  {
    OperateResult operateResult = await this.DownloadFileBaseAsync(groups, fileName, processReport, (object) stream, cancelToken);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DownloadBitmap(System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.HslCancelToken)" />
  public async Task<OperateResult<Bitmap>> DownloadBitmapAsync(
    string groups,
    string fileName,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    MemoryStream stream = new MemoryStream();
    Bitmap bitmap = (Bitmap) null;
    OperateResult result = await this.DownloadFileBaseAsync(groups, fileName, processReport, (object) stream, cancelToken);
    if (!result.IsSuccess)
    {
      stream.Dispose();
      return OperateResult.CreateFailedResult<Bitmap>(result);
    }
    bitmap = new Bitmap((Stream) stream);
    stream.Dispose();
    return OperateResult.CreateSuccessResult<Bitmap>(bitmap);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.UploadFile(System.Drawing.Bitmap,System.String,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.HslCancelToken)" />
  public async Task<OperateResult> UploadFileAsync(
    Bitmap bitmap,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    MemoryStream stream = new MemoryStream();
    if (bitmap.RawFormat != null)
      bitmap.Save((Stream) stream, bitmap.RawFormat);
    else
      bitmap.Save((Stream) stream, ImageFormat.Bmp);
    OperateResult result = await this.UploadFileBaseAsync((object) stream, groups, serverName, fileTag, processReport, cancelToken);
    stream.Dispose();
    OperateResult operateResult = result;
    stream = (MemoryStream) null;
    result = (OperateResult) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.UploadFileBase(System.Object,System.String,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.HslCancelToken)" />
  private async Task<OperateResult> UploadFileBaseAsync(
    object source,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken)
  {
    OperateResult<CommunicationPipe> socketResult = await this.ConnectMqttFileServerAsync((byte) 102, groups, new string[1]
    {
      serverName
    }).ConfigureAwait(false);
    if (!socketResult.IsSuccess)
      return (OperateResult) socketResult;
    switch (source)
    {
      case string fileName:
        OperateResult result1 = await MqttHelper.SendMqttFileAsync(socketResult.Content, fileName, serverName, fileTag, processReport, this.aesCryptography, cancelToken).ConfigureAwait(false);
        if (!result1.IsSuccess)
          return result1;
        result1 = (OperateResult) null;
        break;
      case Stream stream:
        OperateResult result2 = await MqttHelper.SendMqttFileAsync(socketResult.Content, stream, serverName, fileTag, processReport, this.aesCryptography, cancelToken).ConfigureAwait(false);
        if (!result2.IsSuccess)
          return result2;
        result2 = (OperateResult) null;
        stream = (Stream) null;
        break;
      default:
        socketResult.Content?.CloseCommunication();
        this.LogNet?.WriteError(this.ToString(), StringResources.Language.DataSourceFormatError);
        return new OperateResult(StringResources.Language.DataSourceFormatError);
    }
    OperateResult<byte, byte[]> resultCheck = await MqttHelper.ReceiveMqttMessageAsync(socketResult.Content, 60000).ConfigureAwait(false);
    if (!resultCheck.IsSuccess)
      return (OperateResult) resultCheck;
    socketResult.Content?.CloseCommunication();
    return resultCheck.Content1 != (byte) 0 ? OperateResult.CreateSuccessResult() : new OperateResult(Encoding.UTF8.GetString(resultCheck.Content2));
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.UploadFile(System.String,System.String,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.HslCancelToken)" />
  public async Task<OperateResult> UploadFileAsync(
    string fileName,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    if (!System.IO.File.Exists(fileName))
      return new OperateResult(StringResources.Language.FileNotExist);
    OperateResult operateResult = await this.UploadFileBaseAsync((object) fileName, groups, serverName, fileTag, processReport, cancelToken);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.UploadFile(System.String,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.HslCancelToken)" />
  public async Task<OperateResult> UploadFileAsync(
    string fileName,
    string groups,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    if (!System.IO.File.Exists(fileName))
      return new OperateResult(StringResources.Language.FileNotExist);
    FileInfo fileInfo = new FileInfo(fileName);
    OperateResult operateResult = await this.UploadFileBaseAsync((object) fileName, groups, fileInfo.Name, fileTag, processReport, cancelToken);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.UploadFile(System.IO.Stream,System.String,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.HslCancelToken)" />
  public async Task<OperateResult> UploadFileAsync(
    Stream stream,
    string groups,
    string serverName,
    string fileTag,
    Action<long, long> processReport,
    HslCancelToken cancelToken = null)
  {
    OperateResult operateResult = await this.UploadFileBaseAsync((object) stream, groups, serverName, fileTag, processReport, cancelToken);
    return operateResult;
  }

  private async Task<OperateResult<T[]>> DownloadStringArraysAsync<T>(
    byte protocol,
    string groups,
    string[] fileNames)
  {
    OperateResult<T[]> operateResult = await this.OperateMqttFileContentAsync<T[]>(protocol, groups, fileNames, (Func<OperateResult<byte, byte[]>, T[]>) (m => JArray.Parse(Encoding.UTF8.GetString(m.Content2)).ToObject<T[]>()));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DownloadPathFileNames(System.String)" />
  public async Task<OperateResult<GroupFileItem[]>> DownloadPathFileNamesAsync(string groups)
  {
    OperateResult<GroupFileItem[]> operateResult = await this.DownloadStringArraysAsync<GroupFileItem>((byte) 105, groups, (string[]) null);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DownloadPathFolders(System.String)" />
  public async Task<OperateResult<string[]>> DownloadPathFoldersAsync(string groups)
  {
    OperateResult<string[]> operateResult = await this.DownloadStringArraysAsync<string>((byte) 106, groups, (string[]) null);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.IsFileExists(System.String,System.String)" />
  public async Task<OperateResult<bool>> IsFileExistsAsync(string groups, string fileName)
  {
    OperateResult<bool> operateResult = await this.OperateMqttFileContentAsync<bool>((byte) 107, groups, new string[1]
    {
      fileName
    }, (Func<OperateResult<byte, byte[]>, bool>) (m => m.Content1 == (byte) 1));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DeleteFile(System.String,System.String)" />
  public async Task<OperateResult> DeleteFileAsync(string groups, string fileName)
  {
    OperateResult operateResult = await this.DeleteFileAsync(groups, new string[1]
    {
      fileName
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DeleteFile(System.String,System.String[])" />
  public async Task<OperateResult> DeleteFileAsync(string groups, string[] fileNames)
  {
    OperateResult<byte> operateResult = await this.OperateMqttFileContentAsync<byte>((byte) 103, groups, fileNames, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DeleteFolderFiles(System.String)" />
  public async Task<OperateResult> DeleteFolderFilesAsync(string groups)
  {
    OperateResult<byte> operateResult = await this.OperateMqttFileContentAsync<byte>((byte) 110, groups, (string[]) null, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.DeleteFolder(System.String)" />
  public async Task<OperateResult> DeleteFolderAsync(string groups)
  {
    OperateResult<byte> operateResult = await this.OperateMqttFileContentAsync<byte>((byte) 104, groups, (string[]) null, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.RenameFolder(System.String,System.String)" />
  public async Task<OperateResult> RenameFolderAsync(string groups, string newName)
  {
    OperateResult<byte> operateResult = await this.OperateMqttFileContentAsync<byte>((byte) 111, groups, new string[1]
    {
      newName
    }, (Func<OperateResult<byte, byte[]>, byte>) (m => m.Content1));
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.GetGroupFileInfo(System.String)" />
  public async Task<OperateResult<GroupFileInfo>> GetGroupFileInfoAsync(string groups)
  {
    OperateResult<GroupFileInfo> groupFileInfoAsync = await this.OperateMqttFileContentAsync<GroupFileInfo>((byte) 108, groups, (string[]) null, (Func<OperateResult<byte, byte[]>, GroupFileInfo>) (m => JObject.Parse(Encoding.UTF8.GetString(m.Content2)).ToObject<GroupFileInfo>()));
    return groupFileInfoAsync;
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttSyncClient.GetSubGroupFileInfos(System.String,System.Boolean)" />
  public async Task<OperateResult<GroupFileInfo[]>> GetSubGroupFileInfosAsync(
    string groups,
    bool withLastFileInfo = false)
  {
    string groups1 = groups;
    string[] fileNames;
    if (!withLastFileInfo)
      fileNames = (string[]) null;
    else
      fileNames = new string[1]{ "1" };
    OperateResult<GroupFileInfo[]> groupFileInfosAsync = await this.OperateMqttFileContentAsync<GroupFileInfo[]>((byte) 109, groups1, fileNames, (Func<OperateResult<byte, byte[]>, GroupFileInfo[]>) (m => JArray.Parse(Encoding.UTF8.GetString(m.Content2)).ToObject<GroupFileInfo[]>()));
    return groupFileInfosAsync;
  }

  /// <summary>
  /// 获取或设置当前的连接信息，客户端将根据这个连接配置进行连接服务器，在连接之前需要设置相关的信息才有效。<br />
  /// To obtain or set the current connection information, the client will connect to the server according to this connection configuration.
  /// Before connecting, the relevant information needs to be set to be effective.
  /// </summary>
  public MqttConnectionOptions ConnectionOptions
  {
    get => this.connectionOptions;
    set => this.connectionOptions = value;
  }

  /// <summary>
  /// 获取或设置使用字符串访问的时候，使用的编码信息，默认为UT8编码<br />
  /// Get or set the encoding information used when accessing with a string, the default is UT8 encoding
  /// </summary>
  public Encoding StringEncoding
  {
    get => this.stringEncoding;
    set => this.stringEncoding = value;
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"MqttSyncClient[{this.connectionOptions.IpAddress}:{this.connectionOptions.Port}]";
  }
}
