﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.IEC.Helper.IECHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.IO;

#nullable disable
namespace HslCommunication.Instrument.IEC.Helper;

/// <summary>IEC协议的辅助类信息</summary>
public class IECHelper
{
  /// <summary>U帧协议里，启动的功能</summary>
  public const byte IEC104ControlStartDT = 7;
  /// <summary>U帧协议里，停止的功能</summary>
  public const byte IEC104ControlStopDT = 19;
  /// <summary>U帧协议里，测试的功能，主站和子站均可发出</summary>
  public const byte IEC104ControlTestFR = 67;

  /// <summary>将IEC104的报文打包成完整的IEC104标准的协议报文</summary>
  /// <param name="controlField1">控制域1</param>
  /// <param name="controlField2">控制域2</param>
  /// <param name="controlField3">控制域3</param>
  /// <param name="controlField4">控制域4</param>
  /// <param name="asdu">ASDU报文，包含类型标识，可变结构限定词，传送原因，应用服务器数据单元公共地址，信息体</param>
  /// <returns>完整的报文消息</returns>
  public static byte[] PackIEC104Message(
    byte controlField1,
    byte controlField2,
    byte controlField3,
    byte controlField4,
    byte[] asdu)
  {
    byte[] numArray = new byte[6 + (asdu == null ? 0 : asdu.Length)];
    numArray[0] = (byte) 104;
    numArray[1] = (byte) (numArray.Length - 2);
    numArray[2] = controlField1;
    numArray[3] = controlField2;
    numArray[4] = controlField3;
    numArray[5] = controlField4;
    if (asdu != null && asdu.Length != 0)
      asdu.CopyTo((Array) numArray, 6);
    return numArray;
  }

  internal static byte[] PackIEC104Message(ushort controlField1, ushort controlField2, byte[] asdu)
  {
    return IECHelper.PackIEC104Message(BitConverter.GetBytes(controlField1)[0], BitConverter.GetBytes(controlField1)[1], BitConverter.GetBytes(controlField2)[0], BitConverter.GetBytes(controlField2)[1], asdu);
  }

  /// <summary>根据给定的时间，获取绝对时标的报文数据信息</summary>
  /// <param name="dateTime">时间信息</param>
  /// <param name="valid">时标是否有效</param>
  /// <returns>可用于发送的绝对时标的报文</returns>
  public static byte[] GetAbsoluteTimeScale(DateTime dateTime, bool valid)
  {
    byte[] absoluteTimeScale = new byte[7]
    {
      BitConverter.GetBytes(dateTime.Millisecond + dateTime.Second * 1000)[0],
      BitConverter.GetBytes(dateTime.Millisecond + dateTime.Second * 1000)[1],
      BitConverter.GetBytes(dateTime.Minute)[0],
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0
    };
    if (!valid)
      absoluteTimeScale[2] = (byte) ((uint) absoluteTimeScale[2] | 128U /*0x80*/);
    absoluteTimeScale[3] = BitConverter.GetBytes(dateTime.Hour)[0];
    int num = 1;
    switch (dateTime.DayOfWeek)
    {
      case DayOfWeek.Sunday:
        num = 7;
        break;
      case DayOfWeek.Monday:
        num = 1;
        break;
      case DayOfWeek.Tuesday:
        num = 2;
        break;
      case DayOfWeek.Wednesday:
        num = 3;
        break;
      case DayOfWeek.Thursday:
        num = 4;
        break;
      case DayOfWeek.Friday:
        num = 5;
        break;
      case DayOfWeek.Saturday:
        num = 6;
        break;
    }
    absoluteTimeScale[4] = BitConverter.GetBytes(dateTime.Day + num * 32 /*0x20*/)[0];
    absoluteTimeScale[5] = BitConverter.GetBytes(dateTime.Month)[0];
    absoluteTimeScale[6] = BitConverter.GetBytes(dateTime.Year - 2000)[0];
    return absoluteTimeScale;
  }

  /// <summary>根据给定的绝对时标的原始内容，解析出实际的时间信息。</summary>
  /// <param name="source">原始字节</param>
  /// <param name="index">数据的偏移索引</param>
  /// <returns>时间信息</returns>
  public static DateTime PraseTimeFromAbsoluteTimeScale(byte[] source, int index)
  {
    int year = ((int) source[index + 6] & (int) sbyte.MaxValue) + 2000;
    int month = (int) source[index + 5] & 15;
    int day = (int) source[index + 4] & 31 /*0x1F*/;
    int hour = (int) source[index + 3] & 31 /*0x1F*/;
    int minute = (int) source[index + 2] & 63 /*0x3F*/;
    int uint16 = (int) BitConverter.ToUInt16(source, index);
    return new DateTime(year, month, day, hour, minute, uint16 / 1000, uint16 % 1000);
  }

  /// <summary>构建一个S帧协议的内容，需要传入接收需要信息</summary>
  /// <param name="receiveID">接收序号信息</param>
  /// <returns>S帧协议的报文信息</returns>
  public static byte[] BuildFrameSMessage(int receiveID)
  {
    receiveID *= 2;
    return IECHelper.PackIEC104Message((ushort) 1, (ushort) receiveID, (byte[]) null);
  }

  /// <summary>
  /// 构建一个U帧消息的报文信息，传入功能码，STARTDT: 0x07, STOPDT: 0x13; TESTFR: 0x43
  /// </summary>
  /// <param name="controlField">控制码信息</param>
  /// <returns>U帧的报文信息</returns>
  public static byte[] BuildFrameUMessage(byte controlField)
  {
    return IECHelper.PackIEC104Message(controlField, (byte) 0, (byte) 0, (byte) 0, (byte[]) null);
  }

  /// <summary>构建一个I帧消息的报文信息，传入相关的参数信息，返回完整的104消息报文</summary>
  /// <param name="sendID">发送的序列号</param>
  /// <param name="receiveID">接收的序列号</param>
  /// <param name="typeId">类型标识</param>
  /// <param name="variableStructureQualifier">可变结构限定词</param>
  /// <param name="reason">传送原因</param>
  /// <param name="station">应用服务数据单元公共地址</param>
  /// <param name="body">信息体，最大243个字节的长度</param>
  /// <returns>用于发送的104报文信息</returns>
  public static byte[] BuildFrameIMessage(
    int sendID,
    int receiveID,
    byte typeId,
    byte variableStructureQualifier,
    ushort reason,
    ushort station,
    byte[] body)
  {
    sendID *= 2;
    receiveID *= 2;
    byte[] asdu = new byte[6 + (body == null ? 0 : body.Length)];
    asdu[0] = typeId;
    asdu[1] = variableStructureQualifier;
    asdu[2] = BitConverter.GetBytes(reason)[0];
    asdu[3] = BitConverter.GetBytes(reason)[1];
    asdu[4] = BitConverter.GetBytes(station)[0];
    asdu[5] = BitConverter.GetBytes(station)[1];
    if (body != null && body.Length != 0)
      body.CopyTo((Array) asdu, 6);
    return IECHelper.PackIEC104Message((ushort) sendID, (ushort) receiveID, asdu);
  }

  /// <summary>构建写入IEC仪表的报文信息</summary>
  /// <param name="type">指令类型信息</param>
  /// <param name="reason">原因信息</param>
  /// <param name="station">公共单元地址</param>
  /// <param name="address">信息对象地址</param>
  /// <param name="value">值数据</param>
  /// <returns>发送仪表的报文</returns>
  public static byte[] BuildWriteIec(
    byte type,
    ushort reason,
    ushort station,
    ushort address,
    byte[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte(type);
    ms.WriteByte((byte) 1);
    ms.Write(BitConverter.GetBytes(reason));
    ms.Write(BitConverter.GetBytes(station));
    ms.Write(BitConverter.GetBytes(address));
    ms.WriteByte((byte) 0);
    ms.Write(value);
    if (type == (byte) 45 || type == (byte) 46 || type == (byte) 47 || type == (byte) 48 /*0x30*/)
      return ms.ToArray();
    ms.WriteByte((byte) 0);
    return ms.ToArray();
  }

  /// <summary>解析遥信值的方法</summary>
  /// <param name="message">IEC104的消息</param>
  /// <param name="trans">从实际的字节数据转换指定类型的方法</param>
  /// <param name="unitLength">数据类型的字节长度信息</param>
  /// <typeparam name="T">转换后的类型信息</typeparam>
  /// <returns>列表值</returns>
  public static List<IecValueObject<T>> ParseYaoCeValue<T>(
    IEC104MessageEventArgs message,
    Func<byte[], int, T> trans,
    int unitLength)
  {
    bool flag = message.TypeID >= (byte) 30;
    List<IecValueObject<T>> yaoCeValue = new List<IecValueObject<T>>();
    DateTime dateTime = DateTime.MinValue;
    int num1 = message.TypeID == (byte) 21 ? 3 : 4;
    if (message.IsAddressContinuous)
    {
      if (message.TypeID != (byte) 21)
        ++unitLength;
      if (flag && message.Body.Length >= message.InfoObjectCount * unitLength + 3 + 7)
        dateTime = IECHelper.PraseTimeFromAbsoluteTimeScale(message.Body, message.InfoObjectCount * unitLength + 3);
      ushort uint16 = BitConverter.ToUInt16(message.Body, 0);
      for (int index = 0; index < message.InfoObjectCount; ++index)
      {
        int num2 = unitLength * index + 3;
        if (num2 >= message.Body.Length)
          return yaoCeValue;
        IecValueObject<T> iecValueObject = new IecValueObject<T>();
        iecValueObject.Address = (int) uint16 + index;
        iecValueObject.Value = trans(message.Body, num2);
        if (message.TypeID != (byte) 21)
          iecValueObject.Quality = message.Body[num2 + unitLength - 1];
        if (flag)
          iecValueObject.Time = dateTime;
        yaoCeValue.Add(iecValueObject);
      }
    }
    else
    {
      if (flag && message.Body.Length >= message.InfoObjectCount * (num1 + unitLength) + 7)
        dateTime = IECHelper.PraseTimeFromAbsoluteTimeScale(message.Body, message.InfoObjectCount * (num1 + unitLength));
      for (int index = 0; index < message.InfoObjectCount; ++index)
      {
        int startIndex = (num1 + unitLength) * index;
        if (startIndex >= message.Body.Length)
          return yaoCeValue;
        IecValueObject<T> iecValueObject = new IecValueObject<T>();
        iecValueObject.Address = (int) BitConverter.ToUInt16(message.Body, startIndex);
        iecValueObject.Value = trans(message.Body, startIndex + 3);
        if (message.TypeID != (byte) 21)
          iecValueObject.Quality = message.Body[startIndex + 3 + unitLength];
        if (flag)
          iecValueObject.Time = dateTime;
        yaoCeValue.Add(iecValueObject);
      }
    }
    return yaoCeValue;
  }
}
