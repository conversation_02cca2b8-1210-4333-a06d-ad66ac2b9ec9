﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.WebSocket.WebSocketQANet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.WebSocket;

/// <summary>
/// WebSocket的问答机制的客户端，本客户端将会在请求头上追加 RequestAndAnswer: true，本客户端将会请求服务器的信息，然后等待服务器的返回<br />
/// Client of WebSocket Q &amp; A mechanism, this client will append RequestAndAnswer: true to the request header, this client will request the server information, and then wait for the server to return
/// </summary>
public class WebSocketQANet : TcpNetCommunication
{
  /// <summary>
  /// 根据指定的ip地址及端口号，实例化一个默认的对象<br />
  /// Instantiates a default object based on the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">远程服务器的ip地址</param>
  /// <param name="port">端口号信息</param>
  public WebSocketQANet(string ipAddress, int port)
  {
    this.IpAddress = HslHelper.GetIpAddressFromInput(ipAddress);
    this.Port = port;
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult operateResult1 = this.CommunicationPipe.Send(WebSocketHelper.BuildWsQARequest(this.IpAddress, this.Port));
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.CommunicationPipe.Receive(-1, 10000);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackHeader = true)
  {
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Send} : {SoftBasic.ByteToHexString(send, ' ')}");
    OperateResult result = pipe.Send(send);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result);
    if (this.ReceiveTimeOut < 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    OperateResult<WebSocketMessage> webSocketPayload = WebSocketHelper.ReceiveWebSocketPayload(pipe);
    if (!webSocketPayload.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) webSocketPayload);
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Receive} : OpCode[{webSocketPayload.Content.OpCode}] Mask[{webSocketPayload.Content.HasMask}] {SoftBasic.ByteToHexString(webSocketPayload.Content.Payload, ' ')}");
    return OperateResult.CreateSuccessResult<byte[]>(webSocketPayload.Content.Payload);
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    byte[] command = WebSocketHelper.BuildWsQARequest(this.IpAddress, this.Port);
    OperateResult send = await this.CommunicationPipe.SendAsync(command);
    if (!send.IsSuccess)
      return send;
    OperateResult<byte[]> rece = await this.CommunicationPipe.ReceiveAsync(-1, 10000);
    return rece.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) rece;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData = true,
    bool usePackHeader = true)
  {
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Send} : {SoftBasic.ByteToHexString(send, ' ')}");
    OperateResult sendResult = await pipe.SendAsync(send);
    if (!sendResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(sendResult);
    if (this.ReceiveTimeOut < 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    OperateResult<WebSocketMessage> read = await WebSocketHelper.ReceiveWebSocketPayloadAsync(pipe).ConfigureAwait(false);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
    this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Receive} : OpCode[{read.Content.OpCode}] Mask[{read.Content.HasMask}] {SoftBasic.ByteToHexString(read.Content.Payload, ' ')}");
    return OperateResult.CreateSuccessResult<byte[]>(read.Content.Payload);
  }

  /// <summary>
  /// 和websocket的服务器交互，将负载数据发送到服务器端，然后等待接收服务器的数据<br />
  /// Interact with the websocket server, send the load data to the server, and then wait to receive data from the server
  /// </summary>
  /// <param name="payload">数据负载</param>
  /// <returns>返回的结果数据</returns>
  public OperateResult<string> ReadFromServer(string payload)
  {
    return ByteTransformHelper.GetSuccessResultFromOther<string, byte[]>(this.ReadFromCoreServer(WebSocketHelper.WebScoketPackData(1, true, payload)), new Func<byte[], string>(Encoding.UTF8.GetString));
  }

  /// <inheritdoc cref="M:HslCommunication.WebSocket.WebSocketQANet.ReadFromServer(System.String)" />
  public async Task<OperateResult<string>> ReadFromServerAsync(string payload)
  {
    OperateResult<byte[]> result = await this.ReadFromCoreServerAsync(WebSocketHelper.WebScoketPackData(1, true, payload));
    return ByteTransformHelper.GetSuccessResultFromOther<string, byte[]>(result, new Func<byte[], string>(Encoding.UTF8.GetString));
  }

  /// <inheritdoc />
  public override string ToString() => $"WebSocketQANet[{this.IpAddress}:{this.Port}]";
}
