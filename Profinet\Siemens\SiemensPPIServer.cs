﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensPPIServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>PPIServer的虚拟服务器对象，支持的地址类型和S7虚拟服务器一致</summary>
public class SiemensPPIServer : SiemensS7Server
{
  /// <inheritdoc cref="P:HslCommunication.Profinet.Siemens.SiemensPPIOverTcp.Station" />
  public byte Station { get; set; }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new SiemensPPIServerMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    CommunicationPipe communication = session.Communication;
    if ((int) receive[4] != (int) this.Station)
      return new OperateResult<byte[]>($"Station not match, expect: {this.Station}, but actual: {receive[4]}");
    byte[] numArray = new byte[1]{ (byte) 229 };
    this.LogSendMessage(numArray, session);
    communication.Send(numArray);
    OperateResult<byte[]> message = communication.ReceiveMessage((INetMessage) new SpecifiedCharacterMessage((byte) 22), (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
    return !message.IsSuccess ? message : base.ReadFromCoreServer(session, receive);
  }

  /// <inheritdoc />
  protected override bool IsNeedShakeHands() => false;

  /// <inheritdoc />
  protected override byte[] PackReadBack(byte[] command, List<byte> content)
  {
    byte[] numArray = new byte[21 + content.Count + 2];
    SoftBasic.HexStringToBytes("68 1D 1D 68 00 02 08 32 03 00 00 00 00 00 02 00 0C 00 00 04 01").CopyTo((Array) numArray, 0);
    numArray[1] = (byte) (numArray.Length - 6);
    numArray[2] = (byte) (numArray.Length - 6);
    numArray[15] = (byte) (content.Count / 256 /*0x0100*/);
    numArray[16 /*0x10*/] = (byte) (content.Count % 256 /*0x0100*/);
    numArray[20] = command[18];
    content.CopyTo(numArray, 21);
    numArray[numArray.Length - 2] = (byte) SoftLRC.CalculateAcc(numArray, 4, 2);
    numArray[numArray.Length - 1] = (byte) 22;
    return numArray;
  }

  /// <inheritdoc />
  protected override byte[] PackWriteBack(byte[] packCommand, byte[] status)
  {
    byte[] buffer = new byte[23 + status.Length];
    SoftBasic.HexStringToBytes("68 12 12 68 00 02 08 32 03 00 00 00 01 00 02 00 01 00 00 05 01 04 00 16").CopyTo((Array) buffer, 0);
    buffer[20] = (byte) status.Length;
    status.CopyTo((Array) buffer, 21);
    buffer[buffer.Length - 2] = (byte) SoftLRC.CalculateAcc(buffer, 4, 2);
    buffer[buffer.Length - 1] = (byte) 22;
    return buffer;
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength == 6 && buffer[0] == (byte) 16 /*0x10*/ && buffer[1] == (byte) 2 && buffer[5] == (byte) 22 || receivedLength > 6 && buffer[0] == (byte) 104 && (int) buffer[1] + 6 == receivedLength && buffer[receivedLength - 1] == (byte) 22 || base.CheckSerialReceiveDataComplete(buffer, receivedLength);
  }

  /// <inheritdoc />
  public override string ToString() => $"SiemensPPIServer[{this.Port}]";
}
