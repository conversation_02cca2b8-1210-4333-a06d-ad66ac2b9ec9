﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Siemens.Helper;

internal class SiemensS7Helper
{
  /// <summary>读取BOOL时，根据S7协议的返回报文，正确提取出实际的数据内容</summary>
  /// <param name="content">PLC返回的原始字节信息</param>
  /// <returns>解析之后的结果对象</returns>
  internal static OperateResult<byte[]> AnalysisReadBit(byte[] content)
  {
    try
    {
      int length = 1;
      if (content.Length < 21 || content[20] != (byte) 1)
        return new OperateResult<byte[]>(StringResources.Language.SiemensDataLengthCheckFailed);
      byte[] numArray = new byte[length];
      if (22 < content.Length)
      {
        if (content[21] == byte.MaxValue && content[22] == (byte) 3)
        {
          numArray[0] = content[25];
        }
        else
        {
          if (content[21] == (byte) 5 && content[22] == (byte) 0)
            return new OperateResult<byte[]>((int) content[21], StringResources.Language.SiemensReadLengthOverPlcAssign);
          if (content[21] == (byte) 6 && content[22] == (byte) 0)
            return new OperateResult<byte[]>((int) content[21], StringResources.Language.SiemensError0006);
          return content[21] == (byte) 10 && content[22] == (byte) 0 ? new OperateResult<byte[]>((int) content[21], StringResources.Language.SiemensError000A) : new OperateResult<byte[]>((int) content[21], $"{StringResources.Language.UnknownError} Source: {content.ToHexString(' ')}");
        }
      }
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"AnalysisReadBit failed: {ex.Message}{Environment.NewLine} Msg:{SoftBasic.ByteToHexString(content, ' ')}");
    }
  }

  internal static List<S7AddressData[]> ArraySplitByLength(
    S7AddressData[] s7Addresses,
    int pduLength)
  {
    List<S7AddressData[]> s7AddressDataArrayList = new List<S7AddressData[]>();
    List<S7AddressData> s7AddressDataList = new List<S7AddressData>();
    int num = 0;
    for (int index = 0; index < s7Addresses.Length; ++index)
    {
      if (s7AddressDataList.Count >= 19 || num + (int) s7Addresses[index].Length + s7AddressDataList.Count * 4 >= pduLength)
      {
        if (s7AddressDataList.Count > 0)
        {
          s7AddressDataArrayList.Add(s7AddressDataList.ToArray());
          s7AddressDataList.Clear();
        }
        num = 0;
      }
      s7AddressDataList.Add(s7Addresses[index]);
      num += (int) s7Addresses[index].Length;
    }
    if (s7AddressDataList.Count > 0)
      s7AddressDataArrayList.Add(s7AddressDataList.ToArray());
    return s7AddressDataArrayList;
  }

  internal static S7AddressData[] SplitS7Address(S7AddressData s7Address, int pduLength)
  {
    List<S7AddressData> s7AddressDataList = new List<S7AddressData>();
    int num1 = 0;
    ushort num2;
    for (int length = (int) s7Address.Length; num1 < length; num1 += (int) num2)
    {
      num2 = (ushort) Math.Min(length - num1, pduLength);
      S7AddressData s7AddressData = new S7AddressData(s7Address);
      if (s7Address.DataCode == (byte) 31 /*0x1F*/ || s7Address.DataCode == (byte) 30)
        s7AddressData.AddressStart = s7Address.AddressStart + num1 / 2;
      else
        s7AddressData.AddressStart = s7Address.AddressStart + num1 * 8;
      s7AddressData.Length = num2;
      s7AddressDataList.Add(s7AddressData);
    }
    return s7AddressDataList.ToArray();
  }

  /// <summary>读取字数据时，根据S7协议返回的报文，解析出实际的原始字节数组信息</summary>
  /// <param name="content">PLC返回的原始字节数组</param>
  /// <returns>实际的结果数据对象</returns>
  internal static OperateResult<byte[]> AnalysisReadByte(byte[] content)
  {
    try
    {
      List<byte> byteList = new List<byte>();
      if (content.Length < 21)
        return new OperateResult<byte[]>($"{StringResources.Language.SiemensDataLengthCheckFailed} Msg: {SoftBasic.ByteToHexString(content, ' ')}");
      for (int index1 = 21; index1 < content.Length - 1; ++index1)
      {
        if (content[index1] == byte.MaxValue && content[index1 + 1] == (byte) 4)
        {
          int length = ((int) content[index1 + 2] * 256 /*0x0100*/ + (int) content[index1 + 3]) / 8;
          byteList.AddRange((IEnumerable<byte>) content.SelectMiddle<byte>(index1 + 4, length));
          index1 += length + 3;
        }
        else if (content[index1] == byte.MaxValue && content[index1 + 1] == (byte) 9)
        {
          int num = (int) content[index1 + 2] * 256 /*0x0100*/ + (int) content[index1 + 3];
          if (num % 3 == 0)
          {
            for (int index2 = 0; index2 < num / 3; ++index2)
              byteList.AddRange((IEnumerable<byte>) content.SelectMiddle<byte>(index1 + 5 + 3 * index2, 2));
          }
          else
          {
            for (int index3 = 0; index3 < num / 5; ++index3)
              byteList.AddRange((IEnumerable<byte>) content.SelectMiddle<byte>(index1 + 7 + 5 * index3, 2));
          }
          index1 += num + 4;
        }
        else
        {
          if (content[index1] == (byte) 5 && content[index1 + 1] == (byte) 0)
            return new OperateResult<byte[]>((int) content[index1], StringResources.Language.SiemensReadLengthOverPlcAssign);
          if (content[index1] == (byte) 6 && content[index1 + 1] == (byte) 0)
            return new OperateResult<byte[]>((int) content[index1], StringResources.Language.SiemensError0006);
          if (content[index1] == (byte) 10 && content[index1 + 1] == (byte) 0)
            return new OperateResult<byte[]>((int) content[index1], StringResources.Language.SiemensError000A);
        }
      }
      return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"AnalysisReadByte failed: {ex.Message}{Environment.NewLine} Msg:{SoftBasic.ByteToHexString(content, ' ')}");
    }
  }

  /// <summary>
  /// 读取西门子的地址的字符串信息，这个信息是和西门子绑定在一起，长度随西门子的信息动态变化的<br />
  /// Read the Siemens address string information. This information is bound to Siemens and its length changes dynamically with the Siemens information
  /// </summary>
  /// <remarks>
  /// 如果指定编码，一般<see cref="P:System.Text.Encoding.ASCII" />即可，中文需要 Encoding.GetEncoding("gb2312")
  /// </remarks>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="currentPlc">PLC的系列信息</param>
  /// <param name="address">数据地址，具体的格式需要参照类的说明文档</param>
  /// <param name="encoding">自定的编码信息，一般<see cref="P:System.Text.Encoding.ASCII" />即可，中文需要 Encoding.GetEncoding("gb2312")</param>
  /// <returns>带有是否成功的字符串结果类对象</returns>
  public static OperateResult<string> ReadString(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address,
    Encoding encoding)
  {
    if (currentPlc != SiemensPLCS.S200Smart)
    {
      OperateResult<byte[]> result1 = plc.Read(address, (ushort) 2);
      if (!result1.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) result1);
      if (result1.Content[0] == (byte) 0 || result1.Content[0] == byte.MaxValue)
        return new OperateResult<string>("Value in plc is not string type");
      OperateResult<byte[]> result2 = plc.Read(address, (ushort) (2U + (uint) result1.Content[1]));
      return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result2) : OperateResult.CreateSuccessResult<string>(encoding.GetString(result2.Content, 2, result2.Content.Length - 2));
    }
    OperateResult<byte[]> result3 = plc.Read(address, (ushort) 1);
    if (!result3.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result3);
    OperateResult<byte[]> result4 = plc.Read(address, (ushort) (1U + (uint) result3.Content[0]));
    return !result4.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result4) : OperateResult.CreateSuccessResult<string>(encoding.GetString(result4.Content, 1, result4.Content.Length - 1));
  }

  /// <summary>
  /// 将指定的字符串写入到西门子PLC里面去，将自动添加字符串长度信息，方便PLC识别字符串的内容。<br />
  /// Write the specified string into Siemens PLC, and the string length information will be automatically added, which is convenient for PLC to identify the content of the string.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="currentPlc">PLC的系列信息</param>
  /// <param name="address">数据地址，具体的格式需要参照类的说明文档</param>
  /// <param name="value">写入的字符串值</param>
  /// <param name="encoding">编码信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address,
    string value,
    Encoding encoding)
  {
    if (value == null)
      value = string.Empty;
    byte[] inBytes = encoding.GetBytes(value);
    if (encoding == Encoding.Unicode)
      inBytes = SoftBasic.BytesReverseByWord(inBytes);
    if (currentPlc != SiemensPLCS.S200Smart)
    {
      OperateResult<byte[]> operateResult = plc.Read(address, (ushort) 2);
      if (!operateResult.IsSuccess)
        return (OperateResult) operateResult;
      if (operateResult.Content[0] == byte.MaxValue)
        return (OperateResult) new OperateResult<string>("Value in plc is not string type");
      if (operateResult.Content[0] == (byte) 0)
        operateResult.Content[0] = (byte) 254;
      if (inBytes.Length > (int) operateResult.Content[0])
        return (OperateResult) new OperateResult<string>("String length is too long than plc defined");
      return plc.Write(address, SoftBasic.SpliceArray<byte>(new byte[2]
      {
        operateResult.Content[0],
        (byte) inBytes.Length
      }, inBytes));
    }
    return plc.Write(address, SoftBasic.SpliceArray<byte>(new byte[1]
    {
      (byte) inBytes.Length
    }, inBytes));
  }

  /// <summary>
  /// 读取西门子的地址的字符串信息，这个信息是和西门子绑定在一起，长度随西门子的信息动态变化的<br />
  /// Read the Siemens address string information. This information is bound to Siemens and its length changes dynamically with the Siemens information
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="currentPlc">PLC的系列信息</param>
  /// <param name="address">数据地址，具体的格式需要参照类的说明文档</param>
  /// <returns>带有是否成功的字符串结果类对象</returns>
  public static OperateResult<string> ReadWString(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address)
  {
    if (currentPlc != SiemensPLCS.S200Smart)
    {
      OperateResult<byte[]> result1 = plc.Read(address, (ushort) 4);
      if (!result1.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) result1);
      OperateResult<byte[]> result2 = plc.Read(address, (ushort) (4 + ((int) result1.Content[2] * 256 /*0x0100*/ + (int) result1.Content[3]) * 2));
      return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result2) : OperateResult.CreateSuccessResult<string>(Encoding.Unicode.GetString(SoftBasic.BytesReverseByWord(result2.Content.RemoveBegin<byte>(4))));
    }
    OperateResult<byte[]> result3 = plc.Read(address, (ushort) 1);
    if (!result3.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result3);
    OperateResult<byte[]> result4 = plc.Read(address, (ushort) (1 + (int) result3.Content[0] * 2));
    return !result4.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result4) : OperateResult.CreateSuccessResult<string>(Encoding.Unicode.GetString(result4.Content, 1, result4.Content.Length - 1));
  }

  /// <summary>
  /// 使用双字节编码的方式，将字符串以 Unicode 编码写入到PLC的地址里，可以使用中文。<br />
  /// Use the double-byte encoding method to write the character string to the address of the PLC in Unicode encoding. Chinese can be used.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="currentPlc">PLC的系列信息</param>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt; Starting address, formatted as I100,mM100,Q100,DB20.100</param>
  /// <param name="value">字符串的值</param>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult WriteWString(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address,
    string value)
  {
    if (currentPlc == SiemensPLCS.S200Smart)
      return plc.Write(address, value, Encoding.Unicode);
    if (value == null)
      value = string.Empty;
    byte[] numArray1 = Encoding.Unicode.GetBytes(value).ReverseByWord();
    OperateResult<byte[]> operateResult = plc.Read(address, (ushort) 4);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    int num = (int) operateResult.Content[0] * 256 /*0x0100*/ + (int) operateResult.Content[1];
    if (num == 0)
    {
      num = 254;
      operateResult.Content[1] = (byte) 254;
    }
    if (value.Length > num)
      return (OperateResult) new OperateResult<string>("String length is too long than plc defined");
    byte[] numArray2 = new byte[numArray1.Length + 4];
    numArray2[0] = operateResult.Content[0];
    numArray2[1] = operateResult.Content[1];
    numArray2[2] = BitConverter.GetBytes(value.Length)[1];
    numArray2[3] = BitConverter.GetBytes(value.Length)[0];
    numArray1.CopyTo((Array) numArray2, 4);
    return plc.Write(address, numArray2);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.ReadString(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.Text.Encoding)" />
  public static async Task<OperateResult<string>> ReadStringAsync(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address,
    Encoding encoding)
  {
    if (currentPlc != SiemensPLCS.S200Smart)
    {
      OperateResult<byte[]> read = await plc.ReadAsync(address, (ushort) 2);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) read);
      if (read.Content[0] == (byte) 0 || read.Content[0] == byte.MaxValue)
        return new OperateResult<string>("Value in plc is not string type");
      OperateResult<byte[]> readString = await plc.ReadAsync(address, (ushort) (2U + (uint) read.Content[1]));
      return readString.IsSuccess ? OperateResult.CreateSuccessResult<string>(encoding.GetString(readString.Content, 2, readString.Content.Length - 2)) : OperateResult.CreateFailedResult<string>((OperateResult) readString);
    }
    OperateResult<byte[]> read1 = await plc.ReadAsync(address, (ushort) 1);
    if (!read1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read1);
    OperateResult<byte[]> readString1 = await plc.ReadAsync(address, (ushort) (1U + (uint) read1.Content[0]));
    return readString1.IsSuccess ? OperateResult.CreateSuccessResult<string>(encoding.GetString(readString1.Content, 1, readString1.Content.Length - 1)) : OperateResult.CreateFailedResult<string>((OperateResult) readString1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.Write(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.String,System.Text.Encoding)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address,
    string value,
    Encoding encoding)
  {
    if (value == null)
      value = string.Empty;
    byte[] buffer = encoding.GetBytes(value);
    if (encoding == Encoding.Unicode)
      buffer = SoftBasic.BytesReverseByWord(buffer);
    if (currentPlc != SiemensPLCS.S200Smart)
    {
      OperateResult<byte[]> readLength = await plc.ReadAsync(address, (ushort) 2);
      if (!readLength.IsSuccess)
        return (OperateResult) readLength;
      if (readLength.Content[0] == byte.MaxValue)
        return (OperateResult) new OperateResult<string>("Value in plc is not string type");
      if (readLength.Content[0] == (byte) 0)
        readLength.Content[0] = (byte) 254;
      if (buffer.Length > (int) readLength.Content[0])
        return (OperateResult) new OperateResult<string>("String length is too long than plc defined");
      OperateResult operateResult = await plc.WriteAsync(address, SoftBasic.SpliceArray<byte>(new byte[2]
      {
        readLength.Content[0],
        (byte) buffer.Length
      }, buffer));
      return operateResult;
    }
    OperateResult operateResult1 = await plc.WriteAsync(address, SoftBasic.SpliceArray<byte>(new byte[1]
    {
      (byte) buffer.Length
    }, buffer));
    return operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.ReadWString(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String)" />
  public static async Task<OperateResult<string>> ReadWStringAsync(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address)
  {
    if (currentPlc != SiemensPLCS.S200Smart)
    {
      OperateResult<byte[]> read = await plc.ReadAsync(address, (ushort) 4);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) read);
      OperateResult<byte[]> readString = await plc.ReadAsync(address, (ushort) (4 + ((int) read.Content[2] * 256 /*0x0100*/ + (int) read.Content[3]) * 2));
      return readString.IsSuccess ? OperateResult.CreateSuccessResult<string>(Encoding.Unicode.GetString(SoftBasic.BytesReverseByWord(readString.Content.RemoveBegin<byte>(4)))) : OperateResult.CreateFailedResult<string>((OperateResult) readString);
    }
    OperateResult<byte[]> read1 = await plc.ReadAsync(address, (ushort) 1);
    if (!read1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read1);
    OperateResult<byte[]> readString1 = await plc.ReadAsync(address, (ushort) (1 + (int) read1.Content[0] * 2));
    return readString1.IsSuccess ? OperateResult.CreateSuccessResult<string>(Encoding.Unicode.GetString(readString1.Content, 1, readString1.Content.Length - 1)) : OperateResult.CreateFailedResult<string>((OperateResult) readString1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.WriteWString(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.String)" />
  public static async Task<OperateResult> WriteWStringAsync(
    IReadWriteNet plc,
    SiemensPLCS currentPlc,
    string address,
    string value)
  {
    if (currentPlc != SiemensPLCS.S200Smart)
    {
      if (value == null)
        value = string.Empty;
      byte[] buffer = Encoding.Unicode.GetBytes(value);
      buffer = SoftBasic.BytesReverseByWord(buffer);
      OperateResult<byte[]> readLength = await plc.ReadAsync(address, (ushort) 4);
      if (!readLength.IsSuccess)
        return (OperateResult) readLength;
      int defineLength = (int) readLength.Content[0] * 256 /*0x0100*/ + (int) readLength.Content[1];
      if (defineLength == 0)
      {
        defineLength = 254;
        readLength.Content[1] = (byte) 254;
      }
      if (value.Length > defineLength)
        return (OperateResult) new OperateResult<string>("String length is too long than plc defined");
      byte[] write = new byte[buffer.Length + 4];
      write[0] = readLength.Content[0];
      write[1] = readLength.Content[1];
      write[2] = BitConverter.GetBytes(value.Length)[1];
      write[3] = BitConverter.GetBytes(value.Length)[0];
      buffer.CopyTo((Array) write, 4);
      OperateResult operateResult = await plc.WriteAsync(address, write);
      return operateResult;
    }
    OperateResult operateResult1 = await plc.WriteAsync(address, value, Encoding.Unicode);
    return operateResult1;
  }
}
