﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.ModbusAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>
/// Modbus协议地址格式，可以携带站号，功能码，地址信息<br />
/// Modbus protocol address format, can carry station number, function code, address information
/// </summary>
public class ModbusAddress : DeviceAddressDataBase
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public ModbusAddress()
  {
    this.Station = -1;
    this.Function = -1;
    this.WriteFunction = -1;
    this.AddressStart = 0;
  }

  /// <summary>
  /// 实例化一个对象，使用指定的地址初始化<br />
  /// Instantiate an object, initialize with the specified address
  /// </summary>
  /// <param name="address">传入的地址信息，支持富地址，例如s=2;x=3;100</param>
  public ModbusAddress(string address)
  {
    this.Station = -1;
    this.Function = -1;
    this.WriteFunction = -1;
    this.AddressStart = 0;
    this.Parse(address, (ushort) 1);
  }

  /// <summary>
  /// 实例化一个对象，使用指定的地址及功能码初始化<br />
  /// Instantiate an object and initialize it with the specified address and function code
  /// </summary>
  /// <param name="address">传入的地址信息，支持富地址，例如s=2;x=3;100</param>
  /// <param name="function">默认的功能码信息</param>
  public ModbusAddress(string address, byte function)
  {
    this.Station = -1;
    this.WriteFunction = -1;
    this.Function = (int) function;
    this.AddressStart = 0;
    this.Parse(address, (ushort) 1);
  }

  /// <summary>
  /// 实例化一个对象，使用指定的地址，站号，功能码来初始化<br />
  /// Instantiate an object, use the specified address, station number, function code to initialize
  /// </summary>
  /// <param name="address">传入的地址信息，支持富地址，例如s=2;x=3;100</param>
  /// <param name="station">站号信息</param>
  /// <param name="function">默认的功能码信息</param>
  public ModbusAddress(string address, byte station, byte function)
  {
    this.WriteFunction = -1;
    this.Function = (int) function;
    this.Station = (int) station;
    this.AddressStart = 0;
    this.Parse(address, (ushort) 1);
  }

  /// <summary>
  /// 获取或设置当前地址的站号信息<br />
  /// Get or set the station number information of the current address
  /// </summary>
  public int Station { get; set; }

  /// <summary>
  /// 获取或设置当前地址携带的功能码<br />
  /// Get or set the function code carried by the current address
  /// </summary>
  public int Function { get; set; }

  /// <summary>
  /// 获取或设置当前地址在写入的情况下使用的功能码，用来扩展一些非常特殊的自定义服务器<br />
  /// </summary>
  public int WriteFunction { get; set; }

  /// <inheritdoc />
  public override void Parse(string address, ushort length)
  {
    this.Length = length;
    if (address.IndexOf(';') < 0)
    {
      this.AddressStart = (int) ushort.Parse(address);
    }
    else
    {
      string[] strArray = address.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
      for (int index = 0; index < strArray.Length; ++index)
      {
        if (strArray[index].StartsWith("s=", StringComparison.OrdinalIgnoreCase))
          this.Station = (int) byte.Parse(strArray[index].Substring(2));
        else if (strArray[index].StartsWith("x=", StringComparison.OrdinalIgnoreCase))
          this.Function = (int) byte.Parse(strArray[index].Substring(2));
        else if (strArray[index].StartsWith("w=", StringComparison.OrdinalIgnoreCase))
          this.WriteFunction = (int) byte.Parse(strArray[index].Substring(2));
        else
          this.AddressStart = (int) ushort.Parse(strArray[index]);
      }
    }
  }

  /// <summary>
  /// 地址偏移指定的位置，返回一个新的地址对象<br />
  /// The address is offset by the specified position and a new address object is returned
  /// </summary>
  /// <param name="value">数据值信息</param>
  /// <returns>新增后的地址信息</returns>
  public ModbusAddress AddressAdd(int value)
  {
    ModbusAddress modbusAddress = new ModbusAddress();
    modbusAddress.Station = this.Station;
    modbusAddress.Function = this.Function;
    modbusAddress.WriteFunction = this.WriteFunction;
    modbusAddress.AddressStart = this.AddressStart + value;
    return modbusAddress;
  }

  /// <summary>
  /// 地址偏移1，返回一个新的地址对象<br />
  /// The address is offset by 1 and a new address object is returned
  /// </summary>
  /// <returns>新增后的地址信息</returns>
  public ModbusAddress AddressAdd() => this.AddressAdd(1);

  /// <inheritdoc />
  public override string ToString()
  {
    StringBuilder stringBuilder = new StringBuilder();
    if (this.Station >= 0)
      stringBuilder.Append($"s={this.Station.ToString()};");
    if (this.Function == 2 || this.Function == 4 || this.Function > 6)
      stringBuilder.Append($"x={this.Function.ToString()};");
    if (this.WriteFunction > 0)
      stringBuilder.Append($"w={this.WriteFunction.ToString()};");
    stringBuilder.Append(this.AddressStart.ToString());
    return stringBuilder.ToString();
  }
}
