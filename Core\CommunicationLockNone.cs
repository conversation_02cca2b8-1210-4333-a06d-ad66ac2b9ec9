﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.CommunicationLockNone
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core;

/// <summary>用于通信的锁的基类</summary>
public class CommunicationLockNone : ICommunicationLock, IDisposable
{
  private bool disposedValue = false;

  /// <inheritdoc cref="M:HslCommunication.Core.ICommunicationLock.EnterLock(System.Int32)" />
  public virtual OperateResult EnterLock(int timeout) => OperateResult.CreateSuccessResult();

  /// <inheritdoc cref="M:HslCommunication.Core.ICommunicationLock.LeaveLock" />
  public virtual void LeaveLock()
  {
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  protected virtual void Dispose(bool disposing)
  {
    if (!disposing)
      ;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    if (this.disposedValue)
      return;
    this.Dispose(true);
    this.disposedValue = true;
  }
}
