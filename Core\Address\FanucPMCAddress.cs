﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.FanucPMCAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>Fanuc的PMC地址对象信息</summary>
public class FanucPMCAddress : DeviceAddressDataBase
{
  /// <summary>地址代号信息</summary>
  public int DataCode { get; set; }

  /// <summary>结束的地址值</summary>
  public int AddressEnd { get; set; }

  /// <summary>根据实际的地址信息，解析出PMC地址信息</summary>
  /// <param name="address">地址信息，例如 R0, G5</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>PMC地址对象</returns>
  public static OperateResult<FanucPMCAddress> ParseFrom(string address, ushort length)
  {
    FanucPMCAddress fanucPmcAddress = new FanucPMCAddress();
    try
    {
      switch (address[0])
      {
        case 'A':
        case 'a':
          fanucPmcAddress.DataCode = 4;
          break;
        case 'C':
        case 'c':
          fanucPmcAddress.DataCode = 8;
          break;
        case 'D':
        case 'd':
          fanucPmcAddress.DataCode = 9;
          break;
        case 'E':
        case 'e':
          fanucPmcAddress.DataCode = 12;
          break;
        case 'F':
        case 'f':
          fanucPmcAddress.DataCode = 1;
          break;
        case 'G':
        case 'g':
          fanucPmcAddress.DataCode = 0;
          break;
        case 'K':
        case 'k':
          fanucPmcAddress.DataCode = 7;
          break;
        case 'R':
        case 'r':
          fanucPmcAddress.DataCode = 5;
          break;
        case 'T':
        case 't':
          fanucPmcAddress.DataCode = 6;
          break;
        case 'X':
        case 'x':
          fanucPmcAddress.DataCode = 3;
          break;
        case 'Y':
        case 'y':
          fanucPmcAddress.DataCode = 2;
          break;
        default:
          return new OperateResult<FanucPMCAddress>(StringResources.Language.NotSupportedDataType);
      }
      fanucPmcAddress.AddressStart = Convert.ToInt32(address.Substring(1));
      fanucPmcAddress.AddressEnd = fanucPmcAddress.AddressStart + (int) length - 1;
      fanucPmcAddress.Length = length;
      if (fanucPmcAddress.AddressEnd < fanucPmcAddress.AddressStart)
        fanucPmcAddress.AddressEnd = fanucPmcAddress.AddressStart;
      return OperateResult.CreateSuccessResult<FanucPMCAddress>(fanucPmcAddress);
    }
    catch (Exception ex)
    {
      return new OperateResult<FanucPMCAddress>($"{StringResources.Language.NotSupportedDataType} : {ex.Message}");
    }
  }
}
