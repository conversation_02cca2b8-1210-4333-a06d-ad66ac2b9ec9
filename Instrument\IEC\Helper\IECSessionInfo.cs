﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.IEC.Helper.IECSessionInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Threading;

#nullable disable
namespace HslCommunication.Instrument.IEC.Helper;

/// <summary>IEC会话的额外信息，主要包含了这个会话的收发消息ID</summary>
public class IECSessionInfo
{
  private int sendMessageID = 0;
  private int recvMessageID = 0;

  /// <summary>获取发送的消息ID信息，并进行自增操作</summary>
  /// <returns></returns>
  public int GetSendMessageID()
  {
    int sendMessageId = this.sendMessageID;
    Interlocked.Increment(ref this.sendMessageID);
    return sendMessageId;
  }

  /// <summary>自增一个接收到的消息ID</summary>
  /// <returns></returns>
  public int IncrRecvMessageID()
  {
    int num = Interlocked.Increment(ref this.recvMessageID);
    if (this.recvMessageID > (int) short.MaxValue)
      this.recvMessageID = 0;
    return num;
  }

  /// <summary>当前接收的消息ID</summary>
  public int RecvMessageID => this.recvMessageID;
}
