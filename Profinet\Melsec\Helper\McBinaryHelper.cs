﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.McBinaryHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Address;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>三菱PLC，二进制的辅助类对象</summary>
public class McBinaryHelper
{
  /// <summary>将MC协议的核心报文打包成一个可以直接对PLC进行发送的原始报文</summary>
  /// <param name="mcCore">MC协议的核心报文</param>
  /// <param name="mc">MC接口的PLC信息</param>
  /// <returns>原始报文信息</returns>
  public static byte[] PackMcCommand(IReadWriteMc mc, byte[] mcCore)
  {
    byte[] numArray = new byte[11 + mcCore.Length];
    numArray[0] = (byte) 80 /*0x50*/;
    numArray[1] = (byte) 0;
    numArray[2] = mc.NetworkNumber;
    numArray[3] = mc.PLCNumber;
    numArray[4] = BitConverter.GetBytes(mc.TargetIOStation)[0];
    numArray[5] = BitConverter.GetBytes(mc.TargetIOStation)[1];
    numArray[6] = mc.NetworkStationNumber;
    numArray[7] = (byte) ((numArray.Length - 9) % 256 /*0x0100*/);
    numArray[8] = (byte) ((numArray.Length - 9) / 256 /*0x0100*/);
    numArray[9] = (byte) 10;
    numArray[10] = (byte) 0;
    mcCore.CopyTo((Array) numArray, 11);
    return numArray;
  }

  /// <summary>检查从MC返回的数据是否是合法的。</summary>
  /// <param name="content">数据内容</param>
  /// <returns>是否合法</returns>
  public static OperateResult CheckResponseContentHelper(byte[] content)
  {
    if (content == null || content.Length < 11)
      return new OperateResult($"{StringResources.Language.ReceiveDataLengthTooShort}11, Content: {content.ToHexString(' ')}");
    ushort uint16 = BitConverter.ToUInt16(content, 9);
    return uint16 > (ushort) 0 ? (OperateResult) new OperateResult<byte[]>((int) uint16, MelsecHelper.GetErrorDescription((int) uint16)) : OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 从三菱地址，是否位读取进行创建读取的MC的核心报文<br />
  /// From the Mitsubishi address, whether to read the core message of the MC for creating and reading
  /// </summary>
  /// <param name="isBit">是否进行了位读取操作</param>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildReadMcCoreCommand(McAddressData addressData, bool isBit)
  {
    return new byte[10]
    {
      (byte) 1,
      (byte) 4,
      isBit ? (byte) 1 : (byte) 0,
      (byte) 0,
      BitConverter.GetBytes(addressData.AddressStart)[0],
      BitConverter.GetBytes(addressData.AddressStart)[1],
      BitConverter.GetBytes(addressData.AddressStart)[2],
      (byte) addressData.McDataType.DataCode,
      (byte) ((uint) addressData.Length % 256U /*0x0100*/),
      (byte) ((uint) addressData.Length / 256U /*0x0100*/)
    };
  }

  /// <summary>以字为单位，创建数据写入的核心报文</summary>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <param name="value">实际的原始数据信息</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildWriteWordCoreCommand(McAddressData addressData, byte[] value)
  {
    if (value == null)
      value = new byte[0];
    byte[] numArray = new byte[10 + value.Length];
    numArray[0] = (byte) 1;
    numArray[1] = (byte) 20;
    numArray[2] = (byte) 0;
    numArray[3] = (byte) 0;
    numArray[4] = BitConverter.GetBytes(addressData.AddressStart)[0];
    numArray[5] = BitConverter.GetBytes(addressData.AddressStart)[1];
    numArray[6] = BitConverter.GetBytes(addressData.AddressStart)[2];
    numArray[7] = (byte) addressData.McDataType.DataCode;
    numArray[8] = (byte) (value.Length / 2 % 256 /*0x0100*/);
    numArray[9] = (byte) (value.Length / 2 / 256 /*0x0100*/);
    value.CopyTo((Array) numArray, 10);
    return numArray;
  }

  /// <summary>以位为单位，创建数据写入的核心报文</summary>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <param name="value">原始的bool数组数据</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildWriteBitCoreCommand(McAddressData addressData, bool[] value)
  {
    if (value == null)
      value = new bool[0];
    byte[] byteData = MelsecHelper.TransBoolArrayToByteData(value);
    byte[] numArray = new byte[10 + byteData.Length];
    numArray[0] = (byte) 1;
    numArray[1] = (byte) 20;
    numArray[2] = (byte) 1;
    numArray[3] = (byte) 0;
    numArray[4] = BitConverter.GetBytes(addressData.AddressStart)[0];
    numArray[5] = BitConverter.GetBytes(addressData.AddressStart)[1];
    numArray[6] = BitConverter.GetBytes(addressData.AddressStart)[2];
    numArray[7] = (byte) addressData.McDataType.DataCode;
    numArray[8] = (byte) (value.Length % 256 /*0x0100*/);
    numArray[9] = (byte) (value.Length / 256 /*0x0100*/);
    byteData.CopyTo((Array) numArray, 10);
    return numArray;
  }

  /// <summary>从三菱扩展地址，是否位读取进行创建读取的MC的核心报文</summary>
  /// <param name="isBit">是否进行了位读取操作</param>
  /// <param name="extend">扩展指定</param>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildReadMcCoreExtendCommand(
    McAddressData addressData,
    ushort extend,
    bool isBit)
  {
    return new byte[17]
    {
      (byte) 1,
      (byte) 4,
      isBit ? (byte) 129 : (byte) 128 /*0x80*/,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      BitConverter.GetBytes(addressData.AddressStart)[0],
      BitConverter.GetBytes(addressData.AddressStart)[1],
      BitConverter.GetBytes(addressData.AddressStart)[2],
      (byte) addressData.McDataType.DataCode,
      (byte) 0,
      (byte) 0,
      BitConverter.GetBytes(extend)[0],
      BitConverter.GetBytes(extend)[1],
      (byte) 249,
      (byte) ((uint) addressData.Length % 256U /*0x0100*/),
      (byte) ((uint) addressData.Length / 256U /*0x0100*/)
    };
  }

  /// <summary>按字为单位随机读取的指令创建</summary>
  /// <param name="address">地址数组</param>
  /// <returns>指令</returns>
  public static byte[] BuildReadRandomWordCommand(McAddressData[] address)
  {
    byte[] numArray = new byte[6 + address.Length * 4];
    numArray[0] = (byte) 3;
    numArray[1] = (byte) 4;
    numArray[2] = (byte) 0;
    numArray[3] = (byte) 0;
    numArray[4] = (byte) address.Length;
    numArray[5] = (byte) 0;
    for (int index = 0; index < address.Length; ++index)
    {
      numArray[index * 4 + 6] = BitConverter.GetBytes(address[index].AddressStart)[0];
      numArray[index * 4 + 7] = BitConverter.GetBytes(address[index].AddressStart)[1];
      numArray[index * 4 + 8] = BitConverter.GetBytes(address[index].AddressStart)[2];
      numArray[index * 4 + 9] = (byte) address[index].McDataType.DataCode;
    }
    return numArray;
  }

  /// <summary>随机读取的指令创建</summary>
  /// <param name="address">地址数组</param>
  /// <returns>指令</returns>
  public static byte[] BuildReadRandomCommand(McAddressData[] address)
  {
    byte[] numArray = new byte[6 + address.Length * 6];
    numArray[0] = (byte) 6;
    numArray[1] = (byte) 4;
    numArray[2] = (byte) 0;
    numArray[3] = (byte) 0;
    numArray[4] = (byte) address.Length;
    numArray[5] = (byte) 0;
    for (int index = 0; index < address.Length; ++index)
    {
      numArray[index * 6 + 6] = BitConverter.GetBytes(address[index].AddressStart)[0];
      numArray[index * 6 + 7] = BitConverter.GetBytes(address[index].AddressStart)[1];
      numArray[index * 6 + 8] = BitConverter.GetBytes(address[index].AddressStart)[2];
      numArray[index * 6 + 9] = (byte) address[index].McDataType.DataCode;
      numArray[index * 6 + 10] = (byte) ((uint) address[index].Length % 256U /*0x0100*/);
      numArray[index * 6 + 11] = (byte) ((uint) address[index].Length / 256U /*0x0100*/);
    }
    return numArray;
  }

  /// <summary>创建批量读取标签的报文数据信息</summary>
  /// <param name="tags">标签名</param>
  /// <param name="lengths">长度信息</param>
  /// <param name="isBit">是否位指定</param>
  /// <returns>报文数据信息</returns>
  public static byte[] BuildReadTag(string[] tags, ushort[] lengths, bool isBit = false)
  {
    if (tags.Length != lengths.Length)
      throw new Exception(StringResources.Language.TwoParametersLengthIsNotSame);
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 26);
    memoryStream.WriteByte((byte) 4);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte(BitConverter.GetBytes(tags.Length)[0]);
    memoryStream.WriteByte(BitConverter.GetBytes(tags.Length)[1]);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    for (int index = 0; index < tags.Length; ++index)
    {
      byte[] bytes = Encoding.Unicode.GetBytes(tags[index]);
      memoryStream.WriteByte(BitConverter.GetBytes(bytes.Length / 2)[0]);
      memoryStream.WriteByte(BitConverter.GetBytes(bytes.Length / 2)[1]);
      memoryStream.Write(bytes, 0, bytes.Length);
      if (isBit)
        memoryStream.WriteByte((byte) 0);
      else
        memoryStream.WriteByte((byte) 1);
      memoryStream.WriteByte((byte) 0);
      memoryStream.WriteByte(BitConverter.GetBytes((int) lengths[index] * 2)[0]);
      memoryStream.WriteByte(BitConverter.GetBytes((int) lengths[index] * 2)[1]);
    }
    byte[] array = memoryStream.ToArray();
    memoryStream.Dispose();
    return array;
  }

  /// <summary>创建写入标签的报文数据信息</summary>
  /// <param name="tag">标签名称</param>
  /// <param name="data">写入的数据信息</param>
  /// <returns>报文数据信息</returns>
  public static byte[] BuildWriteTag(string tag, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 26);
    ms.WriteByte((byte) 20);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 0);
    ms.WriteByte(BitConverter.GetBytes(1)[0]);
    ms.WriteByte(BitConverter.GetBytes(1)[1]);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 0);
    byte[] bytes = Encoding.Unicode.GetBytes(tag);
    ms.WriteByte(BitConverter.GetBytes(bytes.Length / 2)[0]);
    ms.WriteByte(BitConverter.GetBytes(bytes.Length / 2)[1]);
    ms.Write(bytes, 0, bytes.Length);
    ms.WriteByte((byte) 1);
    ms.WriteByte((byte) 0);
    ms.WriteByte(BitConverter.GetBytes(data.Length)[0]);
    ms.WriteByte(BitConverter.GetBytes(data.Length)[1]);
    ms.Write(data);
    return ms.ToArray();
  }

  /// <summary>读取本站缓冲寄存器的数据信息，需要指定寄存器的地址，和读取的长度</summary>
  /// <param name="address">寄存器的地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>结果内容</returns>
  public static OperateResult<byte[]> BuildReadMemoryCommand(string address, ushort length)
  {
    try
    {
      uint num = uint.Parse(address);
      return OperateResult.CreateSuccessResult<byte[]>(new byte[10]
      {
        (byte) 19,
        (byte) 6,
        (byte) 0,
        (byte) 0,
        BitConverter.GetBytes(num)[0],
        BitConverter.GetBytes(num)[1],
        BitConverter.GetBytes(num)[2],
        BitConverter.GetBytes(num)[3],
        (byte) ((uint) length % 256U /*0x0100*/),
        (byte) ((uint) length / 256U /*0x0100*/)
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>构建读取智能模块的命令，需要指定模块编号，起始地址，读取的长度，注意，该长度以字节为单位。</summary>
  /// <param name="module">模块编号</param>
  /// <param name="address">智能模块的起始地址</param>
  /// <param name="length">读取的字长度</param>
  /// <returns>报文的结果内容</returns>
  public static OperateResult<byte[]> BuildReadSmartModule(
    ushort module,
    string address,
    ushort length)
  {
    try
    {
      uint num = uint.Parse(address);
      return OperateResult.CreateSuccessResult<byte[]>(new byte[12]
      {
        (byte) 1,
        (byte) 6,
        (byte) 0,
        (byte) 0,
        BitConverter.GetBytes(num)[0],
        BitConverter.GetBytes(num)[1],
        BitConverter.GetBytes(num)[2],
        BitConverter.GetBytes(num)[3],
        (byte) ((uint) length % 256U /*0x0100*/),
        (byte) ((uint) length / 256U /*0x0100*/),
        BitConverter.GetBytes(module)[0],
        BitConverter.GetBytes(module)[1]
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>解析出标签读取的数据内容</summary>
  /// <param name="content">返回的数据信息</param>
  /// <returns>解析结果</returns>
  public static OperateResult<byte[]> ExtraTagData(byte[] content)
  {
    try
    {
      int uint16_1 = (int) BitConverter.ToUInt16(content, 0);
      int num = 2;
      List<byte> byteList = new List<byte>(20);
      for (int index = 0; index < uint16_1; ++index)
      {
        int uint16_2 = (int) BitConverter.ToUInt16(content, num + 2);
        byteList.AddRange((IEnumerable<byte>) SoftBasic.ArraySelectMiddle<byte>(content, num + 4, uint16_2));
        num += 4 + uint16_2;
      }
      return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"{ex.Message} Source:{SoftBasic.ByteToHexString(content, ' ')}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.ExtractActualData(System.Byte[],System.Boolean)" />
  public static byte[] ExtractActualDataHelper(byte[] response, bool isBit)
  {
    if (response == null || response.Length == 0 || !isBit)
      return response;
    byte[] actualDataHelper = new byte[response.Length * 2];
    for (int index = 0; index < response.Length; ++index)
    {
      if (((int) response[index] & 16 /*0x10*/) == 16 /*0x10*/)
        actualDataHelper[index * 2] = (byte) 1;
      if (((int) response[index] & 1) == 1)
        actualDataHelper[index * 2 + 1] = (byte) 1;
    }
    return actualDataHelper;
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取PLC的标签信息，需要传入标签的名称，读取的字长度，标签举例：A; label[1]; bbb[10,10,10]<br />
  /// <b>[Authorization]</b> To read the label information of the PLC, you need to pass in the name of the label,
  /// the length of the word read, and an example of the label: A; label [1]; bbb [10,10,10]
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <param name="tags">标签名</param>
  /// <param name="length">读取长度</param>
  /// <returns>是否成功</returns>
  /// <remarks>
  ///  不可以访问局部标签。<br />
  ///  不可以访问通过GX Works2设置的全局标签。<br />
  ///  为了访问全局标签，需要通过GX Works3的全局标签设置编辑器将“来自于外部设备的访问”的设置项目置为有效。(默认为无效。)<br />
  ///  以ASCII代码进行数据通信时，由于需要从UTF-16将标签名转换为ASCII代码，因此报文容量将增加
  /// </remarks>
  public static OperateResult<byte[]> ReadTags(IReadWriteMc mc, string[] tags, ushort[] length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] send = McBinaryHelper.BuildReadTag(tags, length);
    OperateResult<byte[]> result = mc.ReadFromCoreServer(send);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : McBinaryHelper.ExtraTagData(mc.ExtractActualData(result.Content, false));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" />
  public static async Task<OperateResult<byte[]>> ReadTagsAsync(
    IReadWriteMc mc,
    string[] tags,
    ushort[] length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] coreResult = McBinaryHelper.BuildReadTag(tags, length);
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    return read.IsSuccess ? McBinaryHelper.ExtraTagData(mc.ExtractActualData(read.Content, false)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" />
  public static OperateResult<bool[]> ReadBoolTag(IReadWriteMc mc, string tag, ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<bool[]>(StringResources.Language.InsufficientPrivileges);
    byte[] send = McBinaryHelper.BuildReadTag(new string[1]
    {
      tag
    }, new ushort[1]{ length }, true);
    OperateResult<byte[]> result1 = mc.ReadFromCoreServer(send);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = McBinaryHelper.ExtraTagData(mc.ExtractActualData(result1.Content, false));
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<bool[]>(result2.Content.ToBoolArray().SelectBegin<bool>((int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" />
  public static async Task<OperateResult<bool[]>> ReadBoolTagAsync(
    IReadWriteMc mc,
    string tag,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<bool[]>(StringResources.Language.InsufficientPrivileges);
    byte[] coreResult = McBinaryHelper.BuildReadTag(new string[1]
    {
      tag
    }, new ushort[1]{ length }, true);
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    OperateResult<byte[]> extra = McBinaryHelper.ExtraTagData(mc.ExtractActualData(read.Content, false));
    return extra.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(extra.Content.ToBoolArray().SelectBegin<bool>((int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) extra);
  }

  /// <summary>
  /// <b>[商业授权]</b> 写入PLC的标签数据，需要传入标签的名称，实际写入的字节数据信息，标签举例：A; label[1]; bbb[10,10,10]<br />
  /// <b>[Authorization]</b> To write PLC label data, you need to pass the name of the label, the actual written byte data information, label example: A; label[1];  BBB,10,10 [10]
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <param name="tag">标签名</param>
  /// <param name="data">写入的数据</param>
  /// <returns>是否写入成功</returns>
  /// <remarks>
  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" path="remarks" />
  /// </remarks>
  public static OperateResult WriteTag(IReadWriteMc mc, string tag, byte[] data)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] send = McBinaryHelper.BuildWriteTag(tag, data);
    return (OperateResult) mc.ReadFromCoreServer(send);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.WriteTag(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteTagAsync(IReadWriteMc mc, string tag, byte[] data)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] coreResult = McBinaryHelper.BuildWriteTag(tag, data);
    OperateResult<byte[]> operateResult = await mc.ReadFromCoreServerAsync(coreResult);
    return (OperateResult) operateResult;
  }
}
