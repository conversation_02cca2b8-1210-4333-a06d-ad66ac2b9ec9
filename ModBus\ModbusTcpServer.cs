﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.ModBus.ModbusTcpServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.ModBus;

/// <summary>
/// Modbus的虚拟服务器，同时支持Tcp，Rtu，Ascii的机制，支持线圈，离散输入，寄存器和输入寄存器的读写操作，同时支持掩码写入功能，可以用来当做系统的数据交换池<br />
/// Modbus virtual server supports Tcp and Rtu mechanisms at the same time, supports read and write operations of coils, discrete inputs, r
/// egisters and input registers, and supports mask write function, which can be used as a system data exchange pool
/// </summary>
/// <remarks>
/// 可以基于本类实现一个功能复杂的modbus服务器，支持Modbus-Tcp，启动串口后，还支持Modbus-Rtu和Modbus-ASCII，会根据报文进行动态的适配。<br />
/// 线圈，功能码对应01，05，15<br />
/// 离散输入，功能码对应02，服务器写入离散输入的地址使用 x=2;100<br />
/// 寄存器，功能码对应03，06，16<br />
/// 输入寄存器，功能码对应04，输入寄存器在服务器写入使用地址 x=4;100<br />
/// 掩码写入，功能码对应22，可以对字寄存器进行位操作<br />
/// 特别说明1: <see cref="P:HslCommunication.ModBus.ModbusTcpServer.StationDataIsolation" /> 属性如果设置为 True 的话，则服务器为每一个站号（0-255）都创建一个数据区，客户端使用站号作为区分可以写入不同的数据区，服务器也可以读取不同数据区的数据，例如 s=2;100<br />
/// 特别说明2: 如果多个modbus server使用485总线连接，那么属性 <see cref="P:HslCommunication.Core.Device.DeviceServer.ForceSerialReceiveOnce" /> 需要设置为 <c>True</c>
/// </remarks>
/// <example>
/// <list type="number">
/// <item>线圈，功能码对应01，05，15</item>
/// <item>离散输入，功能码对应02</item>
/// <item>寄存器，功能码对应03，06，16</item>
/// <item>输入寄存器，功能码对应04，输入寄存器在服务器端可以实现读写的操作</item>
/// <item>掩码写入，功能码对应22，可以对字寄存器进行位操作</item>
/// </list>
/// 读写的地址格式为富文本地址，具体请参照下面的示例代码。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Modbus\ModbusTcpServer.cs" region="ModbusTcpServerExample" title="ModbusTcpServer示例" />
/// </example>
public class ModbusTcpServer : DeviceServer
{
  private List<ModBusMonitorAddress> subscriptions;
  private SimpleHybirdLock subcriptionHybirdLock;
  private ModbusDataDict dictModbusDataPool;
  private byte station = 1;
  private bool stationDataIsolation = false;
  private IByteTransform byteTransformSelf = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
  private SoftBuffer fileBuffer = new SoftBuffer((int) ushort.MaxValue);

  /// <summary>实例化一个Modbus Tcp及Rtu的服务器，支持数据读写操作</summary>
  public ModbusTcpServer()
  {
    this.dictModbusDataPool = new ModbusDataDict();
    this.subscriptions = new List<ModBusMonitorAddress>();
    this.subcriptionHybirdLock = new SimpleHybirdLock();
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.WordLength = (ushort) 1;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.DataFormat" />
  public DataFormat DataFormat
  {
    get => this.ByteTransform.DataFormat;
    set => this.ByteTransform.DataFormat = value;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.IsStringReverse" />
  public bool IsStringReverse
  {
    get => this.ByteTransform.IsStringReverseByteWord;
    set => this.ByteTransform.IsStringReverseByteWord = value;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <summary>
  /// 获取或设置是否对站号进行检测，当服务器只有一个站号的时候，设置为<c>True</c>表示客户端请求站号和服务器不一致的时候，拒绝返回数据给客户端，反之，始终会返回数据给客户端。<br />
  /// When the server has only one station number, setting <c>True</c> means that when the client requests that the station number is inconsistent with the server,
  /// it refuses to return data to the client, and vice versa, it will always return data to the client.
  /// </summary>
  public bool StationCheck { get; set; } = true;

  /// <summary>
  /// 获取或设置当前的TCP服务器是否使用modbus-rtu报文进行通信，如果设置为 <c>True</c>，那么客户端需要使用 <see cref="T:HslCommunication.ModBus.ModbusRtuOverTcp" /><br />
  /// Get or set whether the current TCP server uses modbus-rtu messages for communication.
  /// If it is set to <c>True</c>, then the client needs to use <see cref="T:HslCommunication.ModBus.ModbusRtuOverTcp" />
  /// </summary>
  /// <remarks>
  /// 需要注意的是，本属性设置为<c>False</c>时，客户端使用<see cref="T:HslCommunication.ModBus.ModbusTcpNet" />，否则，使用<see cref="T:HslCommunication.ModBus.ModbusRtuOverTcp" />，不能混合使用
  /// </remarks>
  public bool UseModbusRtuOverTcp { get; set; }

  /// <summary>
  /// 获取或设置两次请求直接的延时时间，单位毫秒，默认是0，不发生延时，设置为20的话，可以有效防止有客户端疯狂进行请求而导致服务器的CPU占用率上升。<br />
  /// Get or set the direct delay time of two requests, in milliseconds, the default is 0, no delay occurs, if it is set to 20,
  /// it can effectively prevent the client from making crazy requests and causing the server's CPU usage to increase.
  /// </summary>
  public int RequestDelayTime { get; set; }

  /// <inheritdoc cref="P:HslCommunication.ModBus.IModbus.EnableWriteMaskCode" />
  public bool EnableWriteMaskCode { get; set; } = true;

  /// <summary>
  /// 获取或设置是否启动站点数据隔离功能，默认为 <c>False</c>，也即虚拟服务器模拟一个站点服务器，客户端使用正确的站号才能通信。
  /// 当设置为 <c>True</c> 时，虚拟服务器模式256个站点，无论客户端使用的什么站点，都能读取或是写入对应站点里去。服务器同时也可以访问任意站点自身的数据。<br />
  /// Get or set whether to enable the site data isolation function, the default is <c>False</c>, that is, the virtual server simulates a site server, and the client can communicate with the correct site number.
  /// When set to<c> True</c>, 256 sites in virtual server mode, no matter what site the client uses, can read or write to the corresponding site.The server can also access any site's own data.
  /// </summary>
  /// <remarks>当启动站号隔离之后，服务器访问自身的站号2的数据，地址写为 s=2;100</remarks>
  public bool StationDataIsolation
  {
    get => this.stationDataIsolation;
    set
    {
      this.stationDataIsolation = value;
      this.dictModbusDataPool.Set(value);
    }
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    return this.dictModbusDataPool.GetModbusPool(this.station).SaveToBytes();
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    this.dictModbusDataPool.GetModbusPool(this.station).LoadFromBytes(content, 0);
  }

  /// <inheritdoc cref="M:ModbusDataPool.ReadCoil(System.String)" />
  public bool ReadCoil(string address)
  {
    return this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).ReadCoil(address);
  }

  /// <inheritdoc cref="M:ModbusDataPool.ReadCoil(System.String,System.UInt16)" />
  public bool[] ReadCoil(string address, ushort length)
  {
    return this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).ReadCoil(address, length);
  }

  /// <inheritdoc cref="M:ModbusDataPool.WriteCoil(System.String,System.Boolean)" />
  public void WriteCoil(string address, bool data)
  {
    this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).WriteCoil(address, data);
  }

  /// <inheritdoc cref="M:ModbusDataPool.WriteCoil(System.String,System.Boolean[])" />
  public void WriteCoil(string address, bool[] data)
  {
    this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).WriteCoil(address, data);
  }

  /// <inheritdoc cref="M:ModbusDataPool.ReadDiscrete(System.String)" />
  public bool ReadDiscrete(string address)
  {
    return this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).ReadDiscrete(address);
  }

  /// <inheritdoc cref="M:ModbusDataPool.ReadDiscrete(System.String,System.UInt16)" />
  public bool[] ReadDiscrete(string address, ushort length)
  {
    return this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).ReadDiscrete(address, length);
  }

  /// <inheritdoc cref="M:ModbusDataPool.WriteDiscrete(System.String,System.Boolean)" />
  public void WriteDiscrete(string address, bool data)
  {
    this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).WriteDiscrete(address, data);
  }

  /// <inheritdoc cref="M:ModbusDataPool.WriteDiscrete(System.String,System.Boolean[])" />
  public void WriteDiscrete(string address, bool[] data)
  {
    this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).WriteDiscrete(address, data);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station);
    if (!address.StartsWith("file=", StringComparison.OrdinalIgnoreCase))
      return this.dictModbusDataPool.GetModbusPool(parameter).Read(address, length);
    HslHelper.ExtractParameter(ref address, "file", 0);
    return OperateResult.CreateSuccessResult<byte[]>(this.fileBuffer.GetBytes((int) ushort.Parse(address) * 2, (int) length * 2));
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station);
    if (!address.StartsWith("file=", StringComparison.OrdinalIgnoreCase))
      return this.dictModbusDataPool.GetModbusPool(parameter).Write(address, value);
    HslHelper.ExtractParameter(ref address, "file", 0);
    ushort num = ushort.Parse(address);
    this.fileBuffer.SetBytes(value, (int) num * 2);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).ReadBool(address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return this.dictModbusDataPool.GetModbusPool((byte) HslHelper.ExtractParameter(ref address, "s", (int) this.station)).Write(address, value);
  }

  /// <summary>写入寄存器数据，指定字节数据</summary>
  /// <param name="address">起始地址，示例："100"，如果是输入寄存器："x=4;100"</param>
  /// <param name="high">高位数据</param>
  /// <param name="low">地位数据</param>
  public void Write(string address, byte high, byte low)
  {
    this.Write(address, new byte[2]{ high, low });
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return !this.UseModbusRtuOverTcp ? (INetMessage) new ModbusTcpMessage() : (INetMessage) null;
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length < 3)
      return new OperateResult<byte[]>("Uknown Data：" + receive.ToHexString(' '));
    if (this.RequestDelayTime > 0)
      HslHelper.ThreadSleep(this.RequestDelayTime);
    if (session.Communication is PipeSerialPort)
    {
      byte[] numArray1 = receive;
      if (numArray1[0] == (byte) 58 && numArray1[1] >= (byte) 48 /*0x30*/ && numArray1[1] < (byte) 128 /*0x80*/)
      {
        OperateResult<byte[]> core = ModbusInfo.TransAsciiPackCommandToCore(numArray1);
        if (!core.IsSuccess)
          return core;
        byte[] content = core.Content;
        if (!this.CheckModbusMessageLegal(content))
          return new OperateResult<byte[]>("Unlegal Data：" + numArray1.ToHexString(' '));
        if (content[0] != (byte) 0 && content[0] != byte.MaxValue && !this.StationDataIsolation && this.StationCheck && (int) this.station != (int) content[0])
          return new OperateResult<byte[]>("Station not match Modbus-Ascii : " + SoftBasic.GetAsciiStringRender(numArray1));
        byte[] asciiPackCommand = ModbusInfo.TransModbusCoreToAsciiPackCommand(this.ReadFromModbusCore(content));
        return content[0] > (byte) 0 ? OperateResult.CreateSuccessResult<byte[]>(asciiPackCommand) : OperateResult.CreateSuccessResult<byte[]>((byte[]) null);
      }
      if (!SoftCRC16.CheckCRC16(numArray1))
        return new OperateResult<byte[]>("CRC Check Failed : " + numArray1.ToHexString(' '));
      byte[] numArray2 = numArray1.RemoveLast<byte>(2);
      if (!this.CheckModbusMessageLegal(numArray2))
        return new OperateResult<byte[]>("Unlegal Data：" + numArray1.ToHexString(' '));
      if (numArray2[0] != (byte) 0 && numArray2[0] != byte.MaxValue && !this.StationDataIsolation && this.StationCheck && (int) this.station != (int) numArray2[0])
        return new OperateResult<byte[]>("Station not match Modbus-rtu : " + numArray1.ToHexString(' '));
      byte[] rtu = ModbusInfo.PackCommandToRtu(this.ReadFromModbusCore(numArray2));
      return numArray2[0] > (byte) 0 ? OperateResult.CreateSuccessResult<byte[]>(rtu) : OperateResult.CreateSuccessResult<byte[]>((byte[]) null);
    }
    if (this.UseModbusRtuOverTcp)
    {
      if (receive[0] == (byte) 58 && receive[1] >= (byte) 48 /*0x30*/ && receive[1] < (byte) 128 /*0x80*/)
      {
        OperateResult<byte[]> core = ModbusInfo.TransAsciiPackCommandToCore(receive);
        if (!core.IsSuccess)
          return new OperateResult<byte[]>($"ASCII Check Failed: {core.Message} Source: {receive.ToHexString(' ')}");
        if (!this.CheckModbusMessageLegal(core.Content))
          return new OperateResult<byte[]>("Modbus Ascii message check failed ");
        if (core.Content[0] != (byte) 0 && !this.StationDataIsolation && this.StationCheck && (int) this.station != (int) core.Content[0])
          return new OperateResult<byte[]>($"Station not match Modbus-ascii, Need {this.station} actual {core.Content[0]}");
        byte[] asciiPackCommand = ModbusInfo.TransModbusCoreToAsciiPackCommand(this.ReadFromModbusCore(core.Content));
        return core.Content[0] > (byte) 0 ? OperateResult.CreateSuccessResult<byte[]>(asciiPackCommand) : OperateResult.CreateSuccessResult<byte[]>((byte[]) null);
      }
      if (!SoftCRC16.CheckCRC16(receive))
        return new OperateResult<byte[]>("CRC Check Failed: " + receive.ToHexString(' '));
      byte[] numArray = receive.RemoveLast<byte>(2);
      if (!this.CheckModbusMessageLegal(numArray))
        return new OperateResult<byte[]>("Modbus rtu message check failed ");
      if (numArray[0] != (byte) 0 && numArray[0] != byte.MaxValue && !this.StationDataIsolation && this.StationCheck && (int) this.station != (int) numArray[0])
        return new OperateResult<byte[]>($"Station not match Modbus-rtu, Need {this.station} actual {numArray[0]}");
      byte[] rtu = ModbusInfo.PackCommandToRtu(this.ReadFromModbusCore(numArray));
      return numArray[0] > (byte) 0 ? OperateResult.CreateSuccessResult<byte[]>(rtu) : OperateResult.CreateSuccessResult<byte[]>((byte[]) null);
    }
    if (!this.CheckModbusMessageLegal(receive.RemoveBegin<byte>(6)))
      return new OperateResult<byte[]>("Modbus message check failed");
    ushort id = (ushort) ((uint) receive[0] * 256U /*0x0100*/ + (uint) receive[1]);
    if (receive[6] != (byte) 0 && receive[6] != byte.MaxValue && !this.StationDataIsolation && this.StationCheck && (int) this.station != (int) receive[6])
      return new OperateResult<byte[]>("Station not match Modbus-tcp ");
    byte[] tcp = ModbusInfo.PackCommandToTcp(this.ReadFromModbusCore(receive.RemoveBegin<byte>(6)), id);
    return receive[6] == (byte) 0 ? OperateResult.CreateSuccessResult<byte[]>((byte[]) null) : OperateResult.CreateSuccessResult<byte[]>(tcp);
  }

  /// <summary>
  /// 创建特殊的功能标识，然后返回该信息<br />
  /// Create a special feature ID and return this information
  /// </summary>
  /// <param name="modbusCore">modbus核心报文</param>
  /// <param name="error">错误码</param>
  /// <returns>携带错误码的modbus报文</returns>
  private byte[] CreateExceptionBack(byte[] modbusCore, byte error)
  {
    return new byte[3]
    {
      modbusCore[0],
      (byte) ((uint) modbusCore[1] + 128U /*0x80*/),
      error
    };
  }

  /// <summary>
  /// 创建返回消息<br />
  /// Create return message
  /// </summary>
  /// <param name="modbusCore">modbus核心报文</param>
  /// <param name="content">返回的实际数据内容</param>
  /// <returns>携带内容的modbus报文</returns>
  private byte[] CreateReadBack(byte[] modbusCore, byte[] content)
  {
    return SoftBasic.SpliceArray<byte>(new byte[3]
    {
      modbusCore[0],
      modbusCore[1],
      (byte) content.Length
    }, content);
  }

  /// <summary>
  /// 创建写入成功的反馈信号<br />
  /// Create feedback signal for successful write
  /// </summary>
  /// <param name="modbus">modbus核心报文</param>
  /// <returns>携带成功写入的信息</returns>
  private byte[] CreateWriteBack(byte[] modbus) => modbus.SelectBegin<byte>(6);

  private byte[] ReadCoilBack(byte[] modbus, string addressHead)
  {
    try
    {
      ushort num = this.byteTransformSelf.TransUInt16(modbus, 2);
      ushort length = this.byteTransformSelf.TransUInt16(modbus, 4);
      if ((int) num + (int) length > 65536 /*0x010000*/)
        return this.CreateExceptionBack(modbus, (byte) 2);
      if (length > (ushort) 2040)
        return this.CreateExceptionBack(modbus, (byte) 3);
      bool[] content = this.dictModbusDataPool.GetModbusPool(modbus[0]).ReadBool(addressHead + num.ToString(), length).Content;
      return this.CreateReadBack(modbus, SoftBasic.BoolArrayToByte(content));
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpReadCoilException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] ReadRegisterBack(byte[] modbus, string addressHead)
  {
    try
    {
      ushort num = this.byteTransformSelf.TransUInt16(modbus, 2);
      ushort length = this.byteTransformSelf.TransUInt16(modbus, 4);
      if ((int) num + (int) length > 65536 /*0x010000*/)
        return this.CreateExceptionBack(modbus, (byte) 2);
      if (length > (ushort) sbyte.MaxValue)
        return this.CreateExceptionBack(modbus, (byte) 3);
      byte[] content = this.dictModbusDataPool.GetModbusPool(modbus[0]).Read(addressHead + num.ToString(), length).Content;
      return this.CreateReadBack(modbus, content);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpReadRegisterException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] ReadFileRecordBack(byte[] modbus)
  {
    try
    {
      int num1 = (modbus.Length - 3) / 7;
      MemoryStream ms = new MemoryStream();
      for (int index = 0; index < num1; ++index)
      {
        this.byteTransformSelf.TransUInt16(modbus, 4 + 7 * index);
        ushort num2 = this.byteTransformSelf.TransUInt16(modbus, 6 + 7 * index);
        ushort num3 = this.byteTransformSelf.TransUInt16(modbus, 8 + 7 * index);
        if ((int) num3 * 2 + 1 > (int) byte.MaxValue)
          return this.CreateExceptionBack(modbus, (byte) 3);
        ms.WriteByte((byte) ((int) num3 * 2 + 1));
        ms.WriteByte((byte) 6);
        ms.Write(this.fileBuffer.GetBytes((int) num2 * 2, (int) num3 * 2));
        if (ms.Length > (long) byte.MaxValue)
          return this.CreateExceptionBack(modbus, (byte) 3);
      }
      return this.CreateReadBack(modbus, ms.ToArray());
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), nameof (ReadFileRecordBack), ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] WriteFileRecordBack(byte[] modbus)
  {
    try
    {
      MemoryStream ms = new MemoryStream();
      ushort num1;
      for (int index = 3; index < modbus.Length; index += 7 + (int) num1 * 2)
      {
        ushort num2 = this.byteTransformSelf.TransUInt16(modbus, index + 1);
        ushort num3 = this.byteTransformSelf.TransUInt16(modbus, index + 3);
        num1 = this.byteTransformSelf.TransUInt16(modbus, index + 5);
        if (!this.Write($"file={num2};{num3}", modbus.SelectMiddle<byte>(index + 7, (int) num1 * 2)).IsSuccess)
          return this.CreateExceptionBack(modbus, (byte) 4);
        ms.Write(modbus.SelectMiddle<byte>(index, 7));
      }
      return this.CreateReadBack(modbus, ms.ToArray());
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), nameof (WriteFileRecordBack), ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] ReadWriteRegisterBack(byte[] modbus, string addressHead)
  {
    try
    {
      byte[] modbus1 = new byte[6]
      {
        modbus[0],
        (byte) 3,
        modbus[2],
        modbus[3],
        modbus[4],
        modbus[5]
      };
      byte[] numArray = this.ReadRegisterBack(modbus1, addressHead);
      if (numArray[1] > (byte) 128 /*0x80*/)
        return modbus1;
      byte[] modbus2 = SoftBasic.SpliceArray<byte>(new byte[2]
      {
        modbus[0],
        (byte) 16 /*0x10*/
      }, modbus.RemoveBegin<byte>(6));
      if (this.WriteRegisterBack(modbus2)[1] > (byte) 128 /*0x80*/)
        return modbus2;
      numArray[1] = modbus[1];
      return numArray;
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpFunctionCodeReadWriteException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] WriteOneCoilBack(byte[] modbus)
  {
    try
    {
      if (!this.EnableWrite)
        return this.CreateExceptionBack(modbus, (byte) 4);
      ushort num = this.byteTransformSelf.TransUInt16(modbus, 2);
      if (modbus[4] == byte.MaxValue && modbus[5] == (byte) 0)
        this.dictModbusDataPool.GetModbusPool(modbus[0]).Write(num.ToString(), new bool[1]
        {
          true
        });
      else if (modbus[4] == (byte) 0 && modbus[5] == (byte) 0)
        this.dictModbusDataPool.GetModbusPool(modbus[0]).Write(num.ToString(), new bool[1]);
      return this.CreateWriteBack(modbus);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpWriteCoilException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] WriteOneRegisterBack(byte[] modbus)
  {
    try
    {
      if (!this.EnableWrite)
        return this.CreateExceptionBack(modbus, (byte) 4);
      ushort address = this.byteTransformSelf.TransUInt16(modbus, 2);
      short content1 = this.ReadInt16(address.ToString()).Content;
      this.dictModbusDataPool.GetModbusPool(modbus[0]).Write(address.ToString(), new byte[2]
      {
        modbus[4],
        modbus[5]
      });
      short content2 = this.ReadInt16(address.ToString()).Content;
      this.OnRegisterBeforWrite(address, content1, content2);
      return this.CreateWriteBack(modbus);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpWriteRegisterException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] WriteCoilsBack(byte[] modbus)
  {
    try
    {
      if (!this.EnableWrite)
        return this.CreateExceptionBack(modbus, (byte) 4);
      ushort num = this.byteTransformSelf.TransUInt16(modbus, 2);
      ushort length = this.byteTransformSelf.TransUInt16(modbus, 4);
      if ((int) num + (int) length > 65536 /*0x010000*/)
        return this.CreateExceptionBack(modbus, (byte) 2);
      if (length > (ushort) 2040)
        return this.CreateExceptionBack(modbus, (byte) 3);
      this.dictModbusDataPool.GetModbusPool(modbus[0]).Write(num.ToString(), modbus.RemoveBegin<byte>(7).ToBoolArray((int) length));
      return this.CreateWriteBack(modbus);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpWriteCoilException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] WriteRegisterBack(byte[] modbus)
  {
    try
    {
      if (!this.EnableWrite)
        return this.CreateExceptionBack(modbus, (byte) 4);
      ushort num1 = this.byteTransformSelf.TransUInt16(modbus, 2);
      ushort length = this.byteTransformSelf.TransUInt16(modbus, 4);
      if ((int) num1 + (int) length > 65536 /*0x010000*/)
        return this.CreateExceptionBack(modbus, (byte) 2);
      if (length > (ushort) sbyte.MaxValue)
        return this.CreateExceptionBack(modbus, (byte) 3);
      byte[] content = this.dictModbusDataPool.GetModbusPool(modbus[0]).Read(num1.ToString(), length).Content;
      this.dictModbusDataPool.GetModbusPool(modbus[0]).Write(num1.ToString(), modbus.RemoveBegin<byte>(7));
      MonitorAddress[] monitorAddressArray = new MonitorAddress[(int) length];
      for (ushort index = 0; (int) index < (int) length; ++index)
      {
        short num2 = this.ByteTransform.TransInt16(content, (int) index * 2);
        short num3 = this.ByteTransform.TransInt16(modbus, (int) index * 2 + 7);
        monitorAddressArray[(int) index] = new MonitorAddress()
        {
          Address = (ushort) ((uint) num1 + (uint) index),
          ValueOrigin = num2,
          ValueNew = num3
        };
      }
      for (int index = 0; index < monitorAddressArray.Length; ++index)
        this.OnRegisterBeforWrite(monitorAddressArray[index].Address, monitorAddressArray[index].ValueOrigin, monitorAddressArray[index].ValueNew);
      return this.CreateWriteBack(modbus);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpWriteRegisterException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  private byte[] WriteMaskRegisterBack(byte[] modbus)
  {
    try
    {
      if (!this.EnableWrite)
        return this.CreateExceptionBack(modbus, (byte) 4);
      if (!this.EnableWriteMaskCode)
        return this.CreateExceptionBack(modbus, (byte) 1);
      ushort num1 = this.byteTransformSelf.TransUInt16(modbus, 2);
      int num2 = (int) this.ByteTransform.TransUInt16(modbus, 4);
      int num3 = (int) this.ByteTransform.TransUInt16(modbus, 6);
      int content = (int) this.ReadInt16($"s={modbus[0]};" + num1.ToString()).Content;
      short num4 = (short) (content & num2 | num3);
      this.Write($"s={modbus[0]};" + num1.ToString(), num4);
      MonitorAddress monitorAddress = new MonitorAddress()
      {
        Address = num1,
        ValueOrigin = (short) content,
        ValueNew = num4
      };
      this.OnRegisterBeforWrite(monitorAddress.Address, monitorAddress.ValueOrigin, monitorAddress.ValueNew);
      return modbus;
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), StringResources.Language.ModbusTcpWriteRegisterException, ex);
      return this.CreateExceptionBack(modbus, (byte) 4);
    }
  }

  /// <summary>
  /// 新增一个数据监视的任务，针对的是寄存器地址的数据<br />
  /// Added a data monitoring task for data at register addresses
  /// </summary>
  /// <param name="monitor">监视地址对象</param>
  public void AddSubcription(ModBusMonitorAddress monitor)
  {
    this.subcriptionHybirdLock.Enter();
    this.subscriptions.Add(monitor);
    this.subcriptionHybirdLock.Leave();
  }

  /// <summary>
  /// 移除一个数据监视的任务<br />
  /// Remove a data monitoring task
  /// </summary>
  /// <param name="monitor">监视地址对象</param>
  public void RemoveSubcrption(ModBusMonitorAddress monitor)
  {
    this.subcriptionHybirdLock.Enter();
    this.subscriptions.Remove(monitor);
    this.subcriptionHybirdLock.Leave();
  }

  /// <summary>
  /// 在数据变更后，进行触发是否产生订阅<br />
  /// Whether to generate a subscription after triggering data changes
  /// </summary>
  /// <param name="address">数据地址</param>
  /// <param name="before">修改之前的数</param>
  /// <param name="after">修改之后的数</param>
  private void OnRegisterBeforWrite(ushort address, short before, short after)
  {
    this.subcriptionHybirdLock.Enter();
    for (int index = 0; index < this.subscriptions.Count; ++index)
    {
      if ((int) this.subscriptions[index].Address == (int) address)
      {
        this.subscriptions[index].SetValue(after);
        if ((int) before != (int) after)
          this.subscriptions[index].SetChangeValue(before, after);
      }
    }
    this.subcriptionHybirdLock.Leave();
  }

  /// <summary>
  /// 检测当前的Modbus接收的指定是否是合法的<br />
  /// Check if the current Modbus datad designation is valid
  /// </summary>
  /// <param name="buffer">缓存数据</param>
  /// <returns>是否合格</returns>
  private bool CheckModbusMessageLegal(byte[] buffer)
  {
    bool flag;
    switch (buffer[1])
    {
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
        flag = buffer.Length == 6;
        break;
      case 15:
      case 16 /*0x10*/:
        flag = buffer.Length > 6 && (int) buffer[6] == buffer.Length - 7;
        break;
      case 20:
        flag = (int) buffer[2] + 3 == buffer.Length;
        break;
      case 21:
        flag = (int) buffer[2] + 3 == buffer.Length;
        break;
      case 22:
        flag = buffer.Length == 8;
        break;
      default:
        flag = true;
        break;
    }
    if (!flag)
      this.LogNet?.WriteError(this.ToString(), "Receive Nosense Modbus Msg : " + buffer.ToHexString(' '));
    return flag;
  }

  /// <summary>
  /// Modbus核心数据交互方法，允许重写自己来实现，报文只剩下核心的Modbus信息，去除了MPAB报头信息<br />
  /// The Modbus core data interaction method allows you to rewrite it to achieve the message.
  /// Only the core Modbus information is left in the message, and the MPAB header information is removed.
  /// </summary>
  /// <param name="modbusCore">核心的Modbus报文</param>
  /// <returns>进行数据交互之后的结果</returns>
  protected virtual byte[] ReadFromModbusCore(byte[] modbusCore)
  {
    byte[] numArray;
    switch (modbusCore[1])
    {
      case 1:
        numArray = this.ReadCoilBack(modbusCore, string.Empty);
        break;
      case 2:
        numArray = this.ReadCoilBack(modbusCore, "x=2;");
        break;
      case 3:
        numArray = this.ReadRegisterBack(modbusCore, string.Empty);
        break;
      case 4:
        numArray = this.ReadRegisterBack(modbusCore, "x=4;");
        break;
      case 5:
        numArray = this.WriteOneCoilBack(modbusCore);
        break;
      case 6:
        numArray = this.WriteOneRegisterBack(modbusCore);
        break;
      case 15:
        numArray = this.WriteCoilsBack(modbusCore);
        break;
      case 16 /*0x10*/:
        numArray = this.WriteRegisterBack(modbusCore);
        break;
      case 20:
        numArray = this.ReadFileRecordBack(modbusCore);
        break;
      case 21:
        numArray = this.WriteFileRecordBack(modbusCore);
        break;
      case 22:
        numArray = this.WriteMaskRegisterBack(modbusCore);
        break;
      case 23:
        numArray = this.ReadWriteRegisterBack(modbusCore, string.Empty);
        break;
      default:
        numArray = this.CreateExceptionBack(modbusCore, (byte) 1);
        break;
    }
    return numArray;
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int dataLength)
  {
    return dataLength > 5 && (ModbusInfo.CheckAsciiReceiveDataComplete(buffer, dataLength) || ModbusInfo.CheckServerRtuReceiveDataComplete(buffer.SelectBegin<byte>(dataLength)));
  }

  /// <inheritdoc />
  protected override string GetLogTextFromBinary(PipeSession session, byte[] content)
  {
    return session.Communication is PipeSerialPort ? (content[0] == (byte) 58 && content[1] >= (byte) 48 /*0x30*/ && content[1] < (byte) 128 /*0x80*/ ? "[Ascii] " + SoftBasic.GetAsciiStringRender(content) : "[Rtu] " + content.ToHexString(' ')) : (session.Communication is PipeTcpNet && content != null && content.Length > 2 && content[0] == (byte) 58 && content[1] >= (byte) 48 /*0x30*/ && content[1] < (byte) 128 /*0x80*/ ? "[Ascii] " + SoftBasic.GetAsciiStringRender(content) : base.GetLogTextFromBinary(session, content));
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.subcriptionHybirdLock?.Dispose();
      this.subscriptions?.Clear();
      this.dictModbusDataPool?.Dispose();
      GC.Collect();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt32Array", "")]
  public override OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<int[]>(this.Read(address, (ushort) ((int) length * (int) this.WordLength * 2)), (Func<byte[], int[]>) (m => transform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt32Array", "")]
  public override OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<uint[]>(this.Read(address, (ushort) ((int) length * (int) this.WordLength * 2)), (Func<byte[], uint[]>) (m => transform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloat(System.String,System.UInt16)" />
  [HslMqttApi("ReadFloatArray", "")]
  public override OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<float[]>(this.Read(address, (ushort) ((int) length * (int) this.WordLength * 2)), (Func<byte[], float[]>) (m => transform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt64Array", "")]
  public override OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<long[]>(this.Read(address, (ushort) ((int) length * (int) this.WordLength * 4)), (Func<byte[], long[]>) (m => transform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt64Array", "")]
  public override OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(this.Read(address, (ushort) ((int) length * (int) this.WordLength * 4)), (Func<byte[], ulong[]>) (m => transform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDouble(System.String,System.UInt16)" />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<double[]>(this.Read(address, (ushort) ((int) length * (int) this.WordLength * 4)), (Func<byte[], double[]>) (m => transform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int32[])" />
  [HslMqttApi("WriteInt32Array", "")]
  public override OperateResult Write(string address, int[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt32[])" />
  [HslMqttApi("WriteUInt32Array", "")]
  public override OperateResult Write(string address, uint[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Single[])" />
  [HslMqttApi("WriteFloatArray", "")]
  public override OperateResult Write(string address, float[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int64[])" />
  [HslMqttApi("WriteInt64Array", "")]
  public override OperateResult Write(string address, long[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt64[])" />
  [HslMqttApi("WriteUInt64Array", "")]
  public override OperateResult Write(string address, ulong[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double[])" />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) ((int) length * (int) this.WordLength * 2));
    return ByteTransformHelper.GetResultFromBytes<int[]>(result, (Func<byte[], int[]>) (m => transform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) ((int) length * (int) this.WordLength * 2));
    return ByteTransformHelper.GetResultFromBytes<uint[]>(result, (Func<byte[], uint[]>) (m => transform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloatAsync(System.String,System.UInt16)" />
  public override async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) ((int) length * (int) this.WordLength * 2));
    return ByteTransformHelper.GetResultFromBytes<float[]>(result, (Func<byte[], float[]>) (m => transform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) ((int) length * (int) this.WordLength * 4));
    return ByteTransformHelper.GetResultFromBytes<long[]>(result, (Func<byte[], long[]>) (m => transform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) ((int) length * (int) this.WordLength * 4));
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(result, (Func<byte[], ulong[]>) (m => transform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDoubleAsync(System.String,System.UInt16)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) ((int) length * (int) this.WordLength * 4));
    return ByteTransformHelper.GetResultFromBytes<double[]>(result, (Func<byte[], double[]>) (m => transform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int32[])" />
  public override async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt32[])" />
  public override async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Single[])" />
  public override async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int64[])" />
  public override async Task<OperateResult> WriteAsync(string address, long[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt64[])" />
  public override async Task<OperateResult> WriteAsync(string address, ulong[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Double[])" />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"ModbusTcpServer[{this.Port}]";
}
