﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.ReadWriteNetHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>读写网络的辅助类</summary>
public class ReadWriteNetHelper
{
  /// <summary>
  /// 写入位到字寄存器的功能，该功能先读取字寄存器的字数据，然后修改其中的位，再写入回去，可能存在脏数据的风险<br />
  /// The function of writing bit-to-word registers, which first reads the word data of the word register, then modifies the bits in it, and then writes back, which may be the risk of dirty data
  /// </summary>
  /// <remarks>
  /// 关于脏数据风险：从读取数据，修改位，再次写入数据时，大概需要经过3ms~10ms不等的时间，如果此期间内PLC修改了该字寄存器的其他位，再次写入数据时会恢复该点位的数据到读取时的初始值，可能引发设备故障，请谨慎开启此功能。
  /// </remarks>
  /// <param name="readWrite">通信对象信息</param>
  /// <param name="address">写入的地址信息，需要携带'.'号</param>
  /// <param name="values">写入的值信息</param>
  /// <param name="addLength">多少长度的bit位组成一个字地址信息</param>
  /// <param name="reverseWord">对原始数据是否按照字单位进行反转操作</param>
  /// <param name="bitStr">额外指定的位索引，如果为空，则使用<paramref name="address" />中的地址位偏移信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteBoolWithWord(
    IReadWriteNet readWrite,
    string address,
    bool[] values,
    int addLength = 16 /*0x10*/,
    bool reverseWord = false,
    string bitStr = null)
  {
    string[] strArray = address.SplitDot();
    int index = 0;
    try
    {
      if (string.IsNullOrEmpty(bitStr))
      {
        if (strArray.Length > 1)
          index = Convert.ToInt32(strArray[1]);
      }
      else
        index = Convert.ToInt32(bitStr);
    }
    catch (Exception ex)
    {
      return new OperateResult($"{address} Bit index input wrong: {ex.Message}");
    }
    ushort length = (ushort) ((index + values.Length + addLength - 1) / addLength);
    OperateResult<byte[]> result = readWrite.Read(strArray[0], length);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    bool[] array = reverseWord ? result.Content.ReverseByWord().ToBoolArray() : result.Content.ToBoolArray();
    if (index + values.Length <= array.Length)
      values.CopyTo((Array) array, index);
    return readWrite.Write(strArray[0], reverseWord ? array.ToByteArray().ReverseByWord() : array.ToByteArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.ReadWriteNetHelper.WriteBoolWithWord(HslCommunication.Core.IReadWriteNet,System.String,System.Boolean[],System.Int32,System.Boolean,System.String)" />
  public static async Task<OperateResult> WriteBoolWithWordAsync(
    IReadWriteNet readWrite,
    string address,
    bool[] values,
    int addLength = 16 /*0x10*/,
    bool reverseWord = false,
    string bitStr = null)
  {
    string[] adds = address.SplitDot();
    int bit = 0;
    try
    {
      if (string.IsNullOrEmpty(bitStr))
      {
        if (adds.Length > 1)
          bit = Convert.ToInt32(adds[1]);
      }
      else
        bit = Convert.ToInt32(bitStr);
    }
    catch (Exception ex)
    {
      return new OperateResult($"{address} Bit index input wrong: {ex.Message}");
    }
    ushort wordLength = (ushort) ((bit + values.Length + addLength - 1) / addLength);
    OperateResult<byte[]> read = await readWrite.ReadAsync(adds[0], wordLength);
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    bool[] array = reverseWord ? read.Content.ReverseByWord().ToBoolArray() : read.Content.ToBoolArray();
    if (bit + values.Length <= array.Length)
      values.CopyTo((Array) array, bit);
    OperateResult operateResult = await readWrite.WriteAsync(adds[0], reverseWord ? array.ToByteArray().ReverseByWord() : array.ToByteArray());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Boolean,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static OperateResult<TimeSpan> Wait(
    IReadWriteNet readWriteNet,
    string address,
    bool waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime now = DateTime.Now;
    OperateResult<bool> result;
    while (true)
    {
      result = readWriteNet.ReadBool(address);
      if (result.IsSuccess)
      {
        if (result.Content != waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - now).TotalMilliseconds <= (double) waitTimeout)
            HslHelper.ThreadSleep(readInterval);
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - now);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int16,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static OperateResult<TimeSpan> Wait(
    IReadWriteNet readWriteNet,
    string address,
    short waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime now = DateTime.Now;
    OperateResult<short> result;
    while (true)
    {
      result = readWriteNet.ReadInt16(address);
      if (result.IsSuccess)
      {
        if ((int) result.Content != (int) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - now).TotalMilliseconds <= (double) waitTimeout)
            HslHelper.ThreadSleep(readInterval);
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - now);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt16,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static OperateResult<TimeSpan> Wait(
    IReadWriteNet readWriteNet,
    string address,
    ushort waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime now = DateTime.Now;
    OperateResult<ushort> result;
    while (true)
    {
      result = readWriteNet.ReadUInt16(address);
      if (result.IsSuccess)
      {
        if ((int) result.Content != (int) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - now).TotalMilliseconds <= (double) waitTimeout)
            HslHelper.ThreadSleep(readInterval);
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - now);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int32,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static OperateResult<TimeSpan> Wait(
    IReadWriteNet readWriteNet,
    string address,
    int waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime now = DateTime.Now;
    OperateResult<int> result;
    while (true)
    {
      result = readWriteNet.ReadInt32(address);
      if (result.IsSuccess)
      {
        if (result.Content != waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - now).TotalMilliseconds <= (double) waitTimeout)
            HslHelper.ThreadSleep(readInterval);
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - now);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt32,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static OperateResult<TimeSpan> Wait(
    IReadWriteNet readWriteNet,
    string address,
    uint waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime now = DateTime.Now;
    OperateResult<uint> result;
    while (true)
    {
      result = readWriteNet.ReadUInt32(address);
      if (result.IsSuccess)
      {
        if ((int) result.Content != (int) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - now).TotalMilliseconds <= (double) waitTimeout)
            HslHelper.ThreadSleep(readInterval);
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - now);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int64,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static OperateResult<TimeSpan> Wait(
    IReadWriteNet readWriteNet,
    string address,
    long waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime now = DateTime.Now;
    OperateResult<long> result;
    while (true)
    {
      result = readWriteNet.ReadInt64(address);
      if (result.IsSuccess)
      {
        if (result.Content != waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - now).TotalMilliseconds <= (double) waitTimeout)
            HslHelper.ThreadSleep(readInterval);
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - now);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt64,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static OperateResult<TimeSpan> Wait(
    IReadWriteNet readWriteNet,
    string address,
    ulong waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime now = DateTime.Now;
    OperateResult<ulong> result;
    while (true)
    {
      result = readWriteNet.ReadUInt64(address);
      if (result.IsSuccess)
      {
        if ((long) result.Content != (long) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - now).TotalMilliseconds <= (double) waitTimeout)
            HslHelper.ThreadSleep(readInterval);
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - now);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String)" />
  public static OperateResult<T> ReadCustomer<T>(IReadWriteNet readWriteNet, string address) where T : IDataTransfer, new()
  {
    T obj = new T();
    return ReadWriteNetHelper.ReadCustomer<T>(readWriteNet, address, obj);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String,``0)" />
  public static OperateResult<T> ReadCustomer<T>(IReadWriteNet readWriteNet, string address, T obj) where T : IDataTransfer, new()
  {
    OperateResult<byte[]> result = readWriteNet.Read(address, obj.ReadCount);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) result);
    ref T local = ref obj;
    if ((object) default (T) == null)
    {
      T obj1 = local;
      local = ref obj1;
    }
    byte[] content = result.Content;
    local.ParseSource(content);
    return OperateResult.CreateSuccessResult<T>(obj);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteCustomer``1(System.String,``0)" />
  public static OperateResult WriteCustomer<T>(IReadWriteNet readWriteNet, string address, T data) where T : IDataTransfer, new()
  {
    return readWriteNet.Write(address, data.ToSource());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String)" />
  public static async Task<OperateResult<T>> ReadCustomerAsync<T>(
    IReadWriteNet readWriteNet,
    string address)
    where T : IDataTransfer, new()
  {
    T Content = new T();
    OperateResult<T> operateResult = await ReadWriteNetHelper.ReadCustomerAsync<T>(readWriteNet, address, Content);
    Content = default (T);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String,``0)" />
  public static async Task<OperateResult<T>> ReadCustomerAsync<T>(
    IReadWriteNet readWriteNet,
    string address,
    T obj)
    where T : IDataTransfer, new()
  {
    OperateResult<byte[]> read = await readWriteNet.ReadAsync(address, obj.ReadCount);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) read);
    ref T local = ref obj;
    if ((object) default (T) == null)
    {
      T obj1 = local;
      local = ref obj1;
    }
    byte[] content = read.Content;
    local.ParseSource(content);
    return OperateResult.CreateSuccessResult<T>(obj);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteCustomerAsync``1(System.String,``0)" />
  public static async Task<OperateResult> WriteCustomerAsync<T>(
    IReadWriteNet readWriteNet,
    string address,
    T data)
    where T : IDataTransfer, new()
  {
    OperateResult operateResult = await readWriteNet.WriteAsync(address, data.ToSource());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStruct``1(System.String,System.UInt16)" />
  public static OperateResult<T> ReadStruct<T>(
    IReadWriteNet readWriteNet,
    string address,
    ushort length,
    IByteTransform byteTransform,
    int startIndex = 0)
    where T : class, new()
  {
    OperateResult<byte[]> result = readWriteNet.Read(address, length);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) result);
    try
    {
      return OperateResult.CreateSuccessResult<T>(HslReflectionHelper.PraseStructContent<T>(result.Content, startIndex, byteTransform));
    }
    catch (Exception ex)
    {
      return new OperateResult<T>($"Prase struct faild: {ex.Message}{Environment.NewLine}Source Data: {result.Content.ToHexString(' ')}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.ReadWriteNetHelper.ReadStruct``1(HslCommunication.Core.IReadWriteNet,System.String,System.UInt16,HslCommunication.Core.IByteTransform,System.Int32)" />
  public static async Task<OperateResult<T>> ReadStructAsync<T>(
    IReadWriteNet readWriteNet,
    string address,
    ushort length,
    IByteTransform byteTransform,
    int startIndex = 0)
    where T : class, new()
  {
    OperateResult<byte[]> read = await readWriteNet.ReadAsync(address, length);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) read);
    try
    {
      return OperateResult.CreateSuccessResult<T>(HslReflectionHelper.PraseStructContent<T>(read.Content, startIndex, byteTransform));
    }
    catch (Exception ex)
    {
      return new OperateResult<T>($"Prase struct faild: {ex.Message}{Environment.NewLine}Source Data: {read.Content.ToHexString(' ')}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Boolean,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static async Task<OperateResult<TimeSpan>> WaitAsync(
    IReadWriteNet readWriteNet,
    string address,
    bool waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime start = DateTime.Now;
    OperateResult<bool> read;
    while (true)
    {
      read = await readWriteNet.ReadBoolAsync(address);
      if (read.IsSuccess)
      {
        if (read.Content != waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - start).TotalMilliseconds <= (double) waitTimeout)
          {
            await Task.Delay(readInterval);
            read = (OperateResult<bool>) null;
          }
          else
            goto label_6;
        }
        else
          goto label_4;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
label_4:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - start);
label_6:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int16,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static async Task<OperateResult<TimeSpan>> WaitAsync(
    IReadWriteNet readWriteNet,
    string address,
    short waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime start = DateTime.Now;
    OperateResult<short> read;
    while (true)
    {
      read = await readWriteNet.ReadInt16Async(address);
      if (read.IsSuccess)
      {
        if ((int) read.Content != (int) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - start).TotalMilliseconds <= (double) waitTimeout)
          {
            await Task.Delay(readInterval);
            read = (OperateResult<short>) null;
          }
          else
            goto label_6;
        }
        else
          goto label_4;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
label_4:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - start);
label_6:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt16,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static async Task<OperateResult<TimeSpan>> WaitAsync(
    IReadWriteNet readWriteNet,
    string address,
    ushort waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime start = DateTime.Now;
    OperateResult<ushort> read;
    while (true)
    {
      read = await readWriteNet.ReadUInt16Async(address);
      if (read.IsSuccess)
      {
        if ((int) read.Content != (int) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - start).TotalMilliseconds <= (double) waitTimeout)
          {
            await Task.Delay(readInterval);
            read = (OperateResult<ushort>) null;
          }
          else
            goto label_6;
        }
        else
          goto label_4;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
label_4:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - start);
label_6:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int32,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static async Task<OperateResult<TimeSpan>> WaitAsync(
    IReadWriteNet readWriteNet,
    string address,
    int waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime start = DateTime.Now;
    OperateResult<int> read;
    while (true)
    {
      read = await readWriteNet.ReadInt32Async(address);
      if (read.IsSuccess)
      {
        if (read.Content != waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - start).TotalMilliseconds <= (double) waitTimeout)
          {
            await Task.Delay(readInterval);
            read = (OperateResult<int>) null;
          }
          else
            goto label_6;
        }
        else
          goto label_4;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
label_4:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - start);
label_6:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt32,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static async Task<OperateResult<TimeSpan>> WaitAsync(
    IReadWriteNet readWriteNet,
    string address,
    uint waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime start = DateTime.Now;
    OperateResult<uint> read;
    while (true)
    {
      read = readWriteNet.ReadUInt32(address);
      if (read.IsSuccess)
      {
        if ((int) read.Content != (int) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - start).TotalMilliseconds <= (double) waitTimeout)
          {
            await Task.Delay(readInterval);
            read = (OperateResult<uint>) null;
          }
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - start);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int64,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static async Task<OperateResult<TimeSpan>> WaitAsync(
    IReadWriteNet readWriteNet,
    string address,
    long waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime start = DateTime.Now;
    OperateResult<long> read;
    while (true)
    {
      read = readWriteNet.ReadInt64(address);
      if (read.IsSuccess)
      {
        if (read.Content != waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - start).TotalMilliseconds <= (double) waitTimeout)
          {
            await Task.Delay(readInterval);
            read = (OperateResult<long>) null;
          }
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - start);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt64,System.Int32,System.Int32)" />
  /// <param name="readWriteNet">通信对象</param>
  public static async Task<OperateResult<TimeSpan>> WaitAsync(
    IReadWriteNet readWriteNet,
    string address,
    ulong waitValue,
    int readInterval,
    int waitTimeout)
  {
    DateTime start = DateTime.Now;
    OperateResult<ulong> read;
    while (true)
    {
      read = readWriteNet.ReadUInt64(address);
      if (read.IsSuccess)
      {
        if ((long) read.Content != (long) waitValue)
        {
          if (waitTimeout <= 0 || (DateTime.Now - start).TotalMilliseconds <= (double) waitTimeout)
          {
            await Task.Delay(readInterval);
            read = (OperateResult<ulong>) null;
          }
          else
            goto label_5;
        }
        else
          goto label_3;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
label_3:
    return OperateResult.CreateSuccessResult<TimeSpan>(DateTime.Now - start);
label_5:
    return new OperateResult<TimeSpan>(StringResources.Language.CheckDataTimeout + waitTimeout.ToString());
  }
}
