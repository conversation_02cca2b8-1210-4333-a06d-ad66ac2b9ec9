﻿// Decompiled with JetBrains decompiler
// Type: ModbusDataDict
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
internal class ModbusDataDict : IDisposable
{
  private Dictionary<int, ModbusDataPool> dictModbusDataPool;
  private bool stationDataIsolation = false;

  public ModbusDataDict()
  {
    this.dictModbusDataPool = new Dictionary<int, ModbusDataPool>();
    this.dictModbusDataPool.Add(0, new ModbusDataPool((byte) 0));
  }

  public void Set(bool stationDataIsolation)
  {
    if (this.stationDataIsolation == stationDataIsolation)
      return;
    this.stationDataIsolation = stationDataIsolation;
    if (this.stationDataIsolation)
    {
      this.dictModbusDataPool = new Dictionary<int, ModbusDataPool>();
      for (int index = 0; index < (int) byte.MaxValue; ++index)
        this.dictModbusDataPool.Add(index, new ModbusDataPool((byte) index));
    }
    else
    {
      this.dictModbusDataPool = new Dictionary<int, ModbusDataPool>();
      this.dictModbusDataPool.Add(0, new ModbusDataPool((byte) 0));
    }
  }

  public ModbusDataPool GetModbusPool(byte station)
  {
    return this.stationDataIsolation ? this.dictModbusDataPool[(int) station] : this.dictModbusDataPool.FirstOrDefault<KeyValuePair<int, ModbusDataPool>>().Value;
  }

  /// <inheritdoc />
  public void Dispose()
  {
    foreach (ModbusDataPool modbusDataPool in this.dictModbusDataPool.Values)
      modbusDataPool.Dispose();
    this.dictModbusDataPool.Clear();
  }
}
