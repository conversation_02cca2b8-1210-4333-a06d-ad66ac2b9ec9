﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.LSCnetOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.LSIS.Helper;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.LSIS;

/// <summary>
/// XGB Cnet I/F module supports Serial Port. On Tcp/ip implementation, The address can carry station number information, for example: s=2;D100
/// </summary>
/// <remarks>
/// XGB 主机的通道 0 仅支持 1:1 通信。 对于具有主从格式的 1:N 系统，在连接 XGL-C41A 模块的通道 1 或 XGB 主机中使用 RS-485 通信。 XGL-C41A 模块支持 RS-422/485 协议。
/// </remarks>
public class LSCnetOverTcp : DeviceTcpNet, IReadWriteDeviceStation, IReadWriteDevice, IReadWriteNet
{
  /// <summary>Instantiate a Default object</summary>
  public LSCnetOverTcp()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.SleepTime = 20;
  }

  /// <summary>Instantiate a Default object</summary>
  /// <param name="ipAddress">Ip Address</param>
  /// <param name="port">Ip port</param>
  public LSCnetOverTcp(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.IReadWriteDeviceStation.Station" />
  public byte Station { get; set; } = 5;

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return LSCnetHelper.UnpackResponseContent(send, response);
  }

  /// <summary>Read single byte value from plc</summary>
  /// <param name="address">Start address</param>
  /// <returns>result</returns>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>Write single byte value to plc</summary>
  /// <param name="address">Start address</param>
  /// <param name="value">value</param>
  /// <returns>Whether to write the successful</returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <summary>Read single byte value from plc</summary>
  /// <param name="address">Start address</param>
  /// <returns>read result</returns>
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 2);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <summary>Write single byte value to plc</summary>
  /// <param name="address">Start address</param>
  /// <param name="value">value</param>
  /// <returns>Whether to write the successful</returns>
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String)" />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    return LSCnetHelper.ReadBool((IReadWriteDevice) this, (int) this.Station, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.ReadBool(HslCommunication.Core.Net.IReadWriteDeviceStation,System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return LSCnetHelper.ReadBool((IReadWriteDeviceStation) this, address, length);
  }

  /// <summary>ReadCoil, same as ReadBool</summary>
  /// <param name="address">address, for example: MX100, PX100</param>
  /// <returns>Result</returns>
  public OperateResult<bool> ReadCoil(string address) => this.ReadBool(address);

  /// <summary>ReadCoil, same as ReadBool</summary>
  /// <param name="address">address, for example: MX100, PX100</param>
  /// <param name="length">array length</param>
  /// <returns>result</returns>
  public OperateResult<bool[]> ReadCoil(string address, ushort length)
  {
    return this.ReadBool(address, length);
  }

  /// <summary>WriteCoil</summary>
  /// <param name="address">Start Address</param>
  /// <param name="value">value for write</param>
  /// <returns>whether write is success</returns>
  public OperateResult WriteCoil(string address, bool value) => this.Write(address, value);

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return LSCnetHelper.Write((IReadWriteDevice) this, (int) this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.ReadBool(System.String)" />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<bool> operateResult = await LSCnetHelper.ReadBoolAsync((IReadWriteDevice) this, (int) this.Station, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await LSCnetHelper.ReadBoolAsync((IReadWriteDeviceStation) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.ReadCoil(System.String)" />
  public async Task<OperateResult<bool>> ReadCoilAsync(string address)
  {
    OperateResult<bool> operateResult = await this.ReadBoolAsync(address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.ReadCoil(System.String,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadCoilAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.WriteCoil(System.String,System.Boolean)" />
  public async Task<OperateResult> WriteCoilAsync(string address, bool value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await LSCnetHelper.WriteAsync((IReadWriteDevice) this, (int) this.Station, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Read(HslCommunication.Core.Net.IReadWriteDeviceStation,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return LSCnetHelper.Read((IReadWriteDeviceStation) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String[])" />
  public OperateResult<byte[]> Read(string[] address)
  {
    return LSCnetHelper.Read((IReadWriteDevice) this, (int) this.Station, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return LSCnetHelper.Write((IReadWriteDevice) this, (int) this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await LSCnetHelper.ReadAsync((IReadWriteDeviceStation) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.Read(System.String[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address)
  {
    OperateResult<byte[]> operateResult = await LSCnetHelper.ReadAsync((IReadWriteDevice) this, (int) this.Station, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await LSCnetHelper.WriteAsync((IReadWriteDevice) this, (int) this.Station, address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"LsCnetOverTcp[{this.IpAddress}:{this.Port}]";
}
