﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.ModBus.ModbusAscii
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;

#nullable disable
namespace HslCommunication.ModBus;

/// <summary>
/// Modbus-Ascii通讯协议的类库，基于rtu类库完善过来，支持标准的功能码，也支持扩展的功能码实现，地址采用富文本的形式，详细见备注说明<br />
/// The client communication class of Modbus-Ascii protocol is convenient for data interaction with the server. It supports standard function codes and also supports extended function codes.
/// The address is in rich text. For details, see the remarks.
/// </summary>
/// <remarks>
/// 本客户端支持的标准的modbus协议，Modbus-Tcp及Modbus-Udp内置的消息号会进行自增，地址支持富文本格式，具体参考示例代码。<br />
/// 读取线圈，输入线圈，寄存器，输入寄存器的方法中的读取长度对商业授权用户不限制，内部自动切割读取，结果合并。
/// </remarks>
/// <example>
/// 基本的用法请参照下面的代码示例，初始化部分的代码省略
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Modbus\ModbusAsciiExample.cs" region="Example" title="Modbus示例" />
/// 复杂的读取数据的代码示例如下：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Modbus\ModbusAsciiExample.cs" region="ReadExample" title="read示例" />
/// 写入数据的代码如下：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Modbus\ModbusAsciiExample.cs" region="WriteExample" title="write示例" />
/// </example>
public class ModbusAscii : ModbusRtu
{
  /// <summary>
  /// 实例化一个Modbus-ascii协议的客户端对象<br />
  /// Instantiate a client object of the Modbus-ascii protocol
  /// </summary>
  public ModbusAscii()
  {
    this.LogMsgFormatBinary = false;
    this.ReceiveEmptyDataCount = 5;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtu.#ctor(System.Byte)" />
  public ModbusAscii(byte station = 1)
    : base(station)
  {
    this.LogMsgFormatBinary = false;
    this.ReceiveEmptyDataCount = 5;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new ModbusAsciiMessage();

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return ModbusInfo.TransModbusCoreToAsciiPackCommand(command);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return ModbusHelper.ExtraAsciiResponseContent(send, response, this.BroadcastStation);
  }

  /// <inheritdoc />
  public override string ToString() => $"ModbusAscii[{this.PortName}:{this.BaudRate}]";
}
