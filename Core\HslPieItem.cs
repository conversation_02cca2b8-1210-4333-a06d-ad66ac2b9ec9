﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.HslPieItem
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Drawing;

#nullable disable
namespace HslCommunication.Core;

/// <summary>饼图的基本元素</summary>
public class HslPieItem
{
  /// <summary>实例化一个饼图基本元素的对象</summary>
  public HslPieItem() => this.Back = Color.DodgerBlue;

  /// <summary>名称</summary>
  public string Name { get; set; }

  /// <summary>值</summary>
  public int Value { get; set; }

  /// <summary>背景颜色</summary>
  public Color Back { get; set; }
}
