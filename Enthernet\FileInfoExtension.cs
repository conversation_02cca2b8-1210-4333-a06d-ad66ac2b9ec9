﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.FileInfoExtension
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Enthernet;

/// <summary>文件的扩展信息</summary>
public class FileInfoExtension
{
  /// <summary>文件的完整名称</summary>
  public string FullName { get; set; }

  /// <summary>文件的修改时间</summary>
  public DateTime ModifiTime { get; set; }

  /// <summary>文件的MD5码</summary>
  public string MD5 { get; set; }
}
