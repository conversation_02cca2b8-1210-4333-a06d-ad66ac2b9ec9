﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.MegMeet.MegMeetSerialOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.ModBus;
using HslCommunication.Reflection;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.MegMeet;

/// <summary>
/// 深圳麦格米特PLC的通信对象，基于ModbusRtu转以太网协议实现，适用机型为 MC80/MC100/MC200/MC280/MC200E，具体支持的地址及范围参见API文档：http://api.hslcommunication.cn<br />
/// The communication object of Shenzhen MegMeet PLC is based on the ModbusRtu over Ethernet protocol, and the applicable model is MC80/MC100/MC200/MC280/MC200E,
/// and the specific supported address and range are described in API document: http://api.hslcommunication.cn
/// </summary>
/// <remarks>
/// 位读写地址支持：X,Y,M,SM,S,T,C，字读写地址为：D,SD,Z,R,T,C，期中 C200以上使用int/uint类型进行读写操作
/// </remarks>
public class MegMeetSerialOverTcp : ModbusRtuOverTcp
{
  /// <summary>实例化一个默认的对象</summary>
  public MegMeetSerialOverTcp()
  {
  }

  /// <summary>通过指定站号，ip地址，端口号来实例化一个新的对象</summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="station">站号信息</param>
  public MegMeetSerialOverTcp(string ipAddress, int port = 502, byte station = 1)
    : base(ipAddress, port, station)
  {
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return MegMeetHelper.ReadBool(new Func<string, ushort, OperateResult<bool[]>>(((ModbusRtuOverTcp) this).ReadBool), address, length);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return MegMeetHelper.Read(new Func<string, ushort, OperateResult<byte[]>>(((ModbusRtuOverTcp) this).Read), address, length);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address1, ushort length1)
  {
    OperateResult<bool[]> operateResult = await MegMeetHelper.ReadBoolAsync((Func<string, ushort, Task<OperateResult<bool[]>>>) ([DebuggerHidden] (address2, length2) => base.ReadBoolAsync(address2, length2)), address1, length1);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address1, ushort length1)
  {
    OperateResult<byte[]> operateResult = await MegMeetHelper.ReadAsync((Func<string, ushort, Task<OperateResult<byte[]>>>) ([DebuggerHidden] (address2, length2) => base.ReadAsync(address2, length2)), address1, length1);
    return operateResult;
  }

  /// <inheritdoc />
  public override OperateResult<string> TranslateToModbusAddress(string address, byte modbusCode)
  {
    return MegMeetHelper.PraseMegMeetAddress(address, modbusCode);
  }

  /// <inheritdoc />
  public override string ToString() => $"MegMeetSerialOverTcp[{this.IpAddress}:{this.Port}]";
}
