﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.S7PlusHelper.OpenSslConnect
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Security;
using HslCommunication.LogNet;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Profinet.Siemens.S7PlusHelper;

/// <summary>OpenSsl的连接对象，控制当前的</summary>
public class OpenSslConnect : IDisposable
{
  private bool skip_bio_should_retry = false;
  private LinkedList<byte[]> m_pendingReadList = new LinkedList<byte[]>();
  private LinkedList<byte[]> m_pendingWriteList = new LinkedList<byte[]>();
  private bool m_readRequired;
  private readonly IntPtr m_ssl;
  private readonly IntPtr m_pBioIn;
  private readonly IntPtr m_pBioOut;
  private IntPtr m_ptr_ssl_method;
  private IntPtr m_ptr_ctx;
  private Func<byte[], OperateResult> sendDataFunc;
  private Action<byte[]> receiveByte;
  private bool disposedValue;

  /// <summary>指定收发数据实例化一个对象</summary>
  /// <param name="sendDataFunc">发送数据的委托方法</param>
  /// <param name="receiveByte">接收数据的委托方法</param>
  public OpenSslConnect(Func<byte[], OperateResult> sendDataFunc, Action<byte[]> receiveByte)
  {
    if (OpenSslNative.OPENSSL_init_ssl(0UL, IntPtr.Zero) != 1)
      throw new Exception("errOpenSSL");
    this.m_ptr_ssl_method = OpenSslNative.ExpectNonNull(OpenSslNative.TLS_client_method());
    this.m_ptr_ctx = OpenSslNative.ExpectNonNull(OpenSslNative.SSL_CTX_new(this.m_ptr_ssl_method));
    OpenSslNative.SSL_CTX_ctrl(this.m_ptr_ctx, 123, 772L, IntPtr.Zero);
    if (OpenSslNative.SSL_CTX_set_ciphersuites(this.m_ptr_ctx, "TLS_AES_256_GCM_SHA384:TLS_AES_128_GCM_SHA256") != 1)
      throw new Exception("errOpenSSL");
    this.m_readRequired = false;
    this.m_ssl = OpenSslNative.SSL_new(this.m_ptr_ctx);
    this.m_pBioIn = OpenSslNative.BIO_new(OpenSslNative.BIO_s_mem());
    this.m_pBioOut = OpenSslNative.BIO_new(OpenSslNative.BIO_s_mem());
    OpenSslNative.SSL_set_bio(this.m_ssl, this.m_pBioIn, this.m_pBioOut);
    this.sendDataFunc = sendDataFunc;
    this.receiveByte = receiveByte;
  }

  /// <summary>获取或设置当前的日志对象</summary>
  public ILogNet LogNet { get; set; }

  /// <summary>连接</summary>
  public void SSLConnect() => OpenSslNative.SSL_set_connect_state(this.m_ssl);

  /// <summary>将PDU数据写入到当前的信息中</summary>
  /// <param name="value">原始数据信息</param>
  public OperateResult Write(byte[] value)
  {
    return this.Write(new List<byte[]>() { value });
  }

  /// <summary>将PDU数据写入到当前的信息中</summary>
  /// <param name="values">等待发送的字节列表</param>
  public OperateResult Write(List<byte[]> values)
  {
    try
    {
      for (int index = 0; index < values.Count; ++index)
      {
        this.LogNet?.WriteDebug(this.ToString(), $"{StringResources.Language.Send} : {values[index].ToHexString(' ')}");
        this.m_pendingWriteList.AddLast(values[index]);
      }
      this.RunSSL();
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
  }

  /// <summary>收到了加密数据，写入到ssl里进行解密</summary>
  /// <param name="pData"></param>
  public void ReadCompleted(byte[] pData)
  {
    this.m_pendingReadList.AddLast(pData);
    this.RunSSL();
  }

  /// <summary>初始化方法</summary>
  public void SSLInit()
  {
    OpenSslNative.SSL_in_init(this.m_ssl);
    if (OpenSslNative.BIO_ctrl_pending(this.m_pBioOut) <= 0UL)
      return;
    this.SendPendingData();
  }

  /// <summary>运行当前的SSL方法</summary>
  private void RunSSL()
  {
    bool dataToWrite = false;
    bool dataToRead = false;
    this.GetPendingOperations(ref dataToRead, ref dataToWrite);
    while (!this.m_readRequired & dataToWrite | dataToRead)
    {
      if (OpenSslNative.SSL_in_init(this.m_ssl) == 0)
        ;
      if (dataToRead)
        this.PerformRead();
      if (!this.m_readRequired & dataToWrite)
        this.PerformWrite();
      if (OpenSslNative.BIO_ctrl_pending(this.m_pBioOut) > 0UL)
        this.SendPendingData();
      this.GetPendingOperations(ref dataToRead, ref dataToWrite);
    }
  }

  private void GetPendingOperations(ref bool dataToRead, ref bool dataToWrite)
  {
    dataToRead = this.m_pendingReadList.Count > 0;
    dataToWrite = this.m_pendingWriteList.Count > 0;
  }

  private void PerformRead()
  {
    byte[] nextBuffer = this.GetNextBuffer(this.m_pendingReadList);
    if (nextBuffer == null)
      return;
    this.DataToRead(nextBuffer, nextBuffer.Length);
  }

  private void PerformWrite()
  {
    byte[] nextBuffer = this.GetNextBuffer(this.m_pendingWriteList);
    if (nextBuffer == null)
      return;
    int write = this.DataToWrite(nextBuffer, nextBuffer.Length);
    if (write == 0)
      this.m_pendingWriteList.AddLast(nextBuffer);
    else if (write < nextBuffer.Length)
    {
      nextBuffer.RemoveBegin<byte>(write);
      this.m_pendingWriteList.AddLast(nextBuffer);
    }
  }

  private void DataToRead(byte[] pData, int dataLength)
  {
    this.m_readRequired = false;
    int sourceIndex = 0;
label_6:
    if (sourceIndex >= dataLength)
      return;
    byte[] numArray = new byte[dataLength - sourceIndex];
    Array.Copy((Array) pData, sourceIndex, (Array) numArray, 0, numArray.Length);
    sourceIndex += OpenSslNative.BIO_write(this.m_pBioIn, numArray, numArray.Length);
    int length;
    do
    {
      byte[] buf = new byte[4096 /*0x1000*/];
      length = OpenSslNative.SSL_read(this.m_ssl, buf, buf.Length);
      if (length > 0)
        this.receiveByte(buf.SelectBegin<byte>(length));
      if (length >= 0)
        ;
    }
    while (length > 0);
    goto label_6;
  }

  private void SendPendingData()
  {
    while (OpenSslNative.BIO_ctrl_pending(this.m_pBioOut) > 0UL)
    {
      byte[] array = new byte[4096 /*0x1000*/];
      int newSize = OpenSslNative.BIO_read(this.m_pBioOut, array, array.Length);
      if (newSize > 0)
      {
        Array.Resize<byte>(ref array, newSize);
        OperateResult operateResult = this.sendDataFunc(array);
        if (!operateResult.IsSuccess)
          this.LogNet?.WriteDebug("SendPendingData failed: " + operateResult.Message);
      }
      if (newSize <= 0)
      {
        if (!this.skip_bio_should_retry)
        {
          try
          {
            if (OpenSslNative.BIO_should_retry(this.m_pBioOut) == 0)
              ;
          }
          catch (EntryPointNotFoundException ex)
          {
            this.skip_bio_should_retry = true;
          }
        }
      }
    }
  }

  private int DataToWrite(byte[] pData, int dataLength)
  {
    int write = 0;
    int ret_code = OpenSslNative.SSL_write(this.m_ssl, pData, dataLength);
    if (ret_code < 0)
      this.LogNet?.WriteDebug("OpenSSL DataToWrite: Error = " + OpenSslNative.SSL_get_error(this.m_ssl, ret_code).ToString());
    else
      write = ret_code;
    if (OpenSslNative.SSL_want(this.m_ssl) == 3)
      this.m_readRequired = true;
    return write;
  }

  private void HandleError(int result)
  {
    if (result > 0)
      return;
    int error = OpenSslNative.SSL_get_error(this.m_ssl, result);
    if (error != 0)
      this.LogNet?.WriteDebug("OpenSSL HandleError: Error = " + error.ToString());
    switch (error)
    {
    }
  }

  private byte[] GetNextBuffer(LinkedList<byte[]> list)
  {
    byte[] nextBuffer = (byte[]) null;
    if (list.Count > 0)
    {
      nextBuffer = list.First.Value;
      list.RemoveFirst();
    }
    return nextBuffer;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  protected virtual void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (!disposing)
      ;
    OpenSslNative.SSL_free(this.m_ssl);
    this.disposedValue = true;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    this.Dispose(true);
    GC.SuppressFinalize((object) this);
  }
}
