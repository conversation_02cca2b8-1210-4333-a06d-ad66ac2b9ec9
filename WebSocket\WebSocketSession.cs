﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.WebSocket.WebSocketSession
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.MQTT;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;

#nullable disable
namespace HslCommunication.WebSocket;

/// <summary>websocket 的会话客户端</summary>
public class WebSocketSession : PipeSession
{
  private object objLock = new object();
  private NetworkStream networkStream = (NetworkStream) null;
  private SslStream ssl = (SslStream) null;
  private bool sslSecure = false;

  /// <summary>实例化一个默认的对象</summary>
  public WebSocketSession()
  {
    this.Topics = new List<string>();
    this.OnlineTime = DateTime.Now;
  }

  /// <summary>当前客户端订阅的所有的Topic信息</summary>
  public List<string> Topics { get; set; }

  /// <summary>远程的客户端的ip及端口信息</summary>
  public IPEndPoint Remote { get; set; }

  /// <summary>当前的会话是否是问答客户端，如果是问答客户端的话，数据的推送是无效的。</summary>
  public bool IsQASession { get; set; }

  /// <summary>客户端请求的url信息，可能携带一些参数信息</summary>
  public string Url { get; set; }

  /// <summary>检查当前的连接对象是否在</summary>
  /// <param name="topic">主题信息</param>
  /// <param name="willcard">是否启用通配符订阅操作</param>
  /// <returns>是否包含的结果信息</returns>
  public bool IsClientSubscribe(string topic, bool willcard)
  {
    bool flag = false;
    lock (this.objLock)
    {
      if (willcard)
      {
        for (int index = 0; index < this.Topics.Count; ++index)
        {
          if (MqttHelper.CheckMqttTopicWildcards(topic, this.Topics[index]))
          {
            flag = true;
            break;
          }
        }
      }
      else
        flag = this.Topics.Contains(topic);
    }
    return flag;
  }

  /// <summary>动态增加一个订阅的信息</summary>
  /// <param name="topic">订阅的主题</param>
  public void AddTopic(string topic)
  {
    lock (this.objLock)
    {
      if (this.Topics.Contains(topic))
        return;
      this.Topics.Add(topic);
    }
  }

  /// <summary>动态移除一个订阅的信息</summary>
  /// <param name="topic">订阅的主题</param>
  public bool RemoveTopic(string topic)
  {
    bool flag = false;
    lock (this.objLock)
      flag = this.Topics.Remove(topic);
    return flag;
  }

  private bool ValidateServerCertificate(
    object sender,
    X509Certificate certificate,
    X509Chain chain,
    SslPolicyErrors sslPolicyErrors)
  {
    return sslPolicyErrors == SslPolicyErrors.None || !this.sslSecure;
  }

  internal OperateResult<SslStream> CreateSslStream(bool createNew = false, X509Certificate cert = null)
  {
    if (!createNew)
      return OperateResult.CreateSuccessResult<SslStream>(this.ssl);
    this.networkStream?.Close();
    this.ssl?.Close();
    this.networkStream = new NetworkStream((this.Communication as PipeTcpNet).Socket, false);
    this.ssl = new SslStream((Stream) this.networkStream, false, new RemoteCertificateValidationCallback(this.ValidateServerCertificate), (LocalCertificateSelectionCallback) null);
    try
    {
      this.ssl.AuthenticateAsServer(cert, false, SslProtocols.Tls | SslProtocols.Tls11 | SslProtocols.Tls12, true);
      return OperateResult.CreateSuccessResult<SslStream>(this.ssl);
    }
    catch (Exception ex)
    {
      return new OperateResult<SslStream>(ex.Message);
    }
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"WebSocketSession[{this.Remote}][{SoftBasic.GetTimeSpanDescription(DateTime.Now - this.OnlineTime)}]";
  }
}
