﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.Light.ShineInLightSourceController
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Reflection;
using HslCommunication.Serial;
using System;
using System.IO.Ports;

#nullable disable
namespace HslCommunication.Instrument.Light;

/// <summary>昱行智造科技（深圳）有限公司的光源控制器，可以控制灯的亮暗，控制灯的颜色，通道等信息。</summary>
public class ShineInLightSourceController : SerialBase
{
  /// <summary>
  /// 初始化串口信息，波特率，8位数据位，1位停止位，偶校验<br />
  /// Initializes serial port information, baud rate, 8-bit data bit, 1-bit stop bit, even parity
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  /// <param name="baudRate">波特率</param>
  public override void SerialPortInni(string portName, int baudRate)
  {
    this.SerialPortInni(portName, baudRate, 8, StopBits.One, Parity.Even);
  }

  /// <summary>
  /// 初始化串口信息，57600波特率，8位数据位，1位停止位，偶校验<br />
  /// Initial serial port information, 57600 baud rate, 8 data bits, 1 stop bit, even parity
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  public override void SerialPortInni(string portName) => this.SerialPortInni(portName, 57600);

  /// <summary>
  /// 读取光源控制器的参数信息，需要传入通道号信息，读取到详细的内容参照<see cref="T:HslCommunication.Instrument.Light.ShineInLightData" />的值
  /// </summary>
  /// <param name="channel">读取的通道信息</param>
  /// <returns>读取的参数值</returns>
  [HslMqttApi(ApiTopic = "Read", Description = "读取光源控制器的参数信息，需要传入通道号信息，返回 ShineInLightData 对象")]
  public OperateResult<ShineInLightData> Read(byte channel)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(ShineInLightSourceController.BuildReadCommand(channel));
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<ShineInLightData>();
    OperateResult<byte[]> actualData = ShineInLightSourceController.ExtractActualData(operateResult.Content);
    return !actualData.IsSuccess ? actualData.ConvertFailed<ShineInLightData>() : OperateResult.CreateSuccessResult<ShineInLightData>(new ShineInLightData(actualData.Content));
  }

  /// <summary>将光源控制器的数据写入到设备，返回是否写入成功</summary>
  /// <param name="data">光源数据</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(ApiTopic = "Write", Description = "将光源控制器的数据写入到设备，返回是否写入成功")]
  public OperateResult Write(ShineInLightData data)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(ShineInLightSourceController.BuildWriteCommand(data));
    return !operateResult.IsSuccess ? (OperateResult) operateResult.ConvertFailed<ShineInLightData>() : (OperateResult) ShineInLightSourceController.ExtractActualData(operateResult.Content);
  }

  /// <inheritdoc />
  public override string ToString() => $"ShineInLightSourceController[{this.PortName}]";

  /// <summary>将命令和数据打包成用于发送的报文</summary>
  /// <param name="cmd">命令</param>
  /// <param name="data">命令数据</param>
  /// <returns>可用于发送的报文</returns>
  public static byte[] PackCommand(byte cmd, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    byte[] numArray = new byte[data.Length + 8];
    numArray[0] = (byte) 47;
    numArray[1] = (byte) 42;
    numArray[2] = (byte) 240 /*0xF0*/;
    numArray[3] = cmd;
    numArray[4] = (byte) (numArray.Length - 4);
    data.CopyTo((Array) numArray, 5);
    numArray[numArray.Length - 2] = (byte) 42;
    numArray[numArray.Length - 1] = (byte) 47;
    int num = (int) numArray[2];
    for (int index = 3; index < numArray.Length - 3; ++index)
      num ^= (int) numArray[index];
    numArray[numArray.Length - 3] = (byte) num;
    return numArray;
  }

  /// <summary>构建写入数据的报文命令</summary>
  /// <param name="shineInLightData">准备写入的数据</param>
  /// <returns>报文命令</returns>
  public static byte[] BuildWriteCommand(ShineInLightData shineInLightData)
  {
    return ShineInLightSourceController.PackCommand((byte) 1, shineInLightData.GetSourceData());
  }

  /// <summary>构建读取数据的报文命令</summary>
  /// <param name="channel">通道信息</param>
  /// <returns>构建读取的命令</returns>
  public static byte[] BuildReadCommand(byte channel)
  {
    return ShineInLightSourceController.PackCommand((byte) 2, new byte[1]
    {
      channel
    });
  }

  /// <summary>把服务器反馈的数据解析成实际的命令</summary>
  /// <param name="response">反馈的数据</param>
  /// <returns>结果内容</returns>
  public static OperateResult<byte[]> ExtractActualData(byte[] response)
  {
    if (response.Length < 9)
      return new OperateResult<byte[]>("Receive Data is too short; source:" + response.ToHexString(' '));
    if (response[0] != (byte) 47 || response[1] != (byte) 42 || response[response.Length - 2] != (byte) 42 || response[response.Length - 1] != (byte) 47)
      return new OperateResult<byte[]>("Receive Data not start with /* or end with */; source:" + response.ToHexString());
    return response[3] == (byte) 1 ? (response[5] == (byte) 170 ? OperateResult.CreateSuccessResult<byte[]>(new byte[0]) : new OperateResult<byte[]>((int) response[5], "set not success")) : OperateResult.CreateSuccessResult<byte[]>(response.SelectMiddle<byte>(5, response.Length - 8));
  }
}
