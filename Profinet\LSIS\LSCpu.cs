﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.LSCpu
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Profinet.LSIS.Helper;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.LSIS;

/// <summary>
/// XGB CPU I/F module supports Serial Port. The address can carry station number information, for example: s=2;D100
/// </summary>
/// <remarks>
/// XGB 主机的通道 0 仅支持 1:1 通信。 对于具有主从格式的 1:N 系统，在连接 XGL-C41A 模块的通道 1 或 XGB 主机中使用 RS-485 通信。 XGL-C41A 模块支持 RS-422/485 协议。
/// </remarks>
public class LSCpu : DeviceSerialPort
{
  /// <summary>Instantiate a Default object</summary>
  public LSCpu()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 2;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return LSCpuHelper.UnpackResponseContent(send, response);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.LSIS.LSCnet.Station" />
  public byte Station { get; set; } = 5;

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.ReadByte(System.String)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.Write(System.String,System.Byte)" />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return LSCpuHelper.ReadBool((IReadWriteDevice) this, (int) this.Station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.ReadCoil(System.String)" />
  public OperateResult<bool> ReadCoil(string address) => this.ReadBool(address);

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.ReadCoil(System.String,System.UInt16)" />
  public OperateResult<bool[]> ReadCoil(string address, ushort length)
  {
    return this.ReadBool(address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.WriteCoil(System.String,System.Boolean)" />
  public OperateResult WriteCoil(string address, bool value) => this.Write(address, value);

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return LSCpuHelper.Write((IReadWriteDevice) this, (int) this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCpu.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await LSCpuHelper.ReadBoolAsync((IReadWriteDevice) this, (int) this.Station, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCpu.ReadCoil(System.String)" />
  public async Task<OperateResult<bool>> ReadCoilAsync(string address)
  {
    OperateResult<bool> operateResult = await this.ReadBoolAsync(address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCpu.ReadCoil(System.String,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadCoilAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCpu.WriteCoil(System.String,System.Boolean)" />
  public async Task<OperateResult> WriteCoilAsync(string address, bool value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCpu.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await LSCpuHelper.WriteAsync((IReadWriteDevice) this, (int) this.Station, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return LSCpuHelper.Read((IReadWriteDevice) this, (int) this.Station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCpuHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return LSCpuHelper.Write((IReadWriteDevice) this, (int) this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCpu.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await LSCpuHelper.ReadAsync((IReadWriteDevice) this, (int) this.Station, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCpu.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await LSCpuHelper.WriteAsync((IReadWriteDevice) this, (int) this.Station, address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"LSCpu[{this.PortName}:{this.BaudRate}]";
}
