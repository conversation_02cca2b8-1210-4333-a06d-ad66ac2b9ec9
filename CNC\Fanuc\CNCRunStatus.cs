﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.CNCRunStatus
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>CNC的运行状态</summary>
public enum CNCRunStatus
{
  /// <summary>重置</summary>
  RESET,
  /// <summary>停止</summary>
  STOP,
  /// <summary>等待</summary>
  HOLD,
  /// <summary>启动</summary>
  START,
  /// <summary>MSTR</summary>
  MSTR,
}
