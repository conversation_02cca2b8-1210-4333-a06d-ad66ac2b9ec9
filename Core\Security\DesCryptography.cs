﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Security.DesCryptography
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Security.Cryptography;
using System.Text;

#nullable disable
namespace HslCommunication.Core.Security;

/// <summary>DES加密解密的对象</summary>
public class DesCryptography : ICryptography
{
  private ICryptoTransform encryptTransform;
  private ICryptoTransform decryptTransform;
  private DESCryptoServiceProvider des;
  private string key;

  /// <summary>使用指定的密钥来实例化一个加密对象，该密钥右8位的字符和数字组成，例如 12345678</summary>
  /// <param name="key">密钥</param>
  public DesCryptography(string key)
  {
    this.key = key;
    this.des = new DESCryptoServiceProvider();
    this.des.Key = Encoding.ASCII.GetBytes(key);
    this.des.IV = Encoding.ASCII.GetBytes(key);
    this.encryptTransform = this.des.CreateEncryptor();
    this.decryptTransform = this.des.CreateDecryptor();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Encrypt(System.Byte[])" />
  public byte[] Encrypt(byte[] data)
  {
    return data == null ? (byte[]) null : this.encryptTransform.TransformFinalBlock(data, 0, data.Length);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Decrypt(System.Byte[])" />
  public byte[] Decrypt(byte[] data)
  {
    return data == null ? (byte[]) null : this.decryptTransform.TransformFinalBlock(data, 0, data.Length);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Encrypt(System.String)" />
  public string Encrypt(string data)
  {
    return Convert.ToBase64String(this.Encrypt(string.IsNullOrEmpty(data) ? new byte[0] : Encoding.UTF8.GetBytes(data)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Security.ICryptography.Decrypt(System.String)" />
  public string Decrypt(string data)
  {
    return Encoding.UTF8.GetString(this.Decrypt(Convert.FromBase64String(data)));
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Security.ICryptography.Key" />
  public string Key => this.key;
}
