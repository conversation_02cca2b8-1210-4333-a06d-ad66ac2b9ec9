﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.GraphDirection
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>图形的方向</summary>
public enum GraphDirection
{
  /// <summary>向上</summary>
  Upward = 1,
  /// <summary>向下</summary>
  Downward = 2,
  /// <summary>向左</summary>
  Ledtward = 3,
  /// <summary>向右</summary>
  Rightward = 4,
}
