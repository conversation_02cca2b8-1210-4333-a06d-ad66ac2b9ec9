﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Geniitek.VibrationSensorLongMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;

#nullable disable
namespace HslCommunication.Profinet.Geniitek;

/// <summary>完整的数据报文信息</summary>
public class VibrationSensorLongMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 12;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes != null && this.HeadBytes[0] == (byte) 170 && this.HeadBytes[1] == (byte) 85 && this.HeadBytes[2] == (byte) 127 /*0x7F*/;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    return (int) this.HeadBytes[10] * 256 /*0x0100*/ + (int) this.HeadBytes[11] + 4;
  }
}
