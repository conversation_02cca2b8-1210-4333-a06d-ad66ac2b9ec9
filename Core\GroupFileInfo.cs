﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.GroupFileInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;

#nullable disable
namespace HslCommunication.Core;

/// <summary>文件服务器的目录管理类的基本信息</summary>
public class GroupFileInfo
{
  /// <summary>实例化一个默认的对象</summary>
  public GroupFileInfo() => this.LastModifyTime = DateTime.Now;

  /// <summary>文件目录的名称信息</summary>
  public string PathName { get; set; }

  /// <summary>获取或设置文件的总大小</summary>
  public long FileTotalSize { get; set; }

  /// <summary>获取或设置文件的总数量</summary>
  public int FileCount { get; set; }

  /// <summary>获取或设置最后一次文件更新的时间，如果不存在文件，则为理论最小值</summary>
  public DateTime LastModifyTime { get; set; }

  /// <summary>获取或设置最后一次更新的文件的基本信息，如果该目录不存在文件，则本值为空</summary>
  public GroupFileItem LastModifyFile { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    return string.Format("Count: {0} TotalSize: {1} [{2}] ModifyTime:{3:yyyy-MM-dd HH:mm:ss}", (object) this.FileCount, (object) this.FileTotalSize, (object) SoftBasic.GetSizeDescription(this.FileTotalSize), (object) this.LastModifyTime);
  }
}
