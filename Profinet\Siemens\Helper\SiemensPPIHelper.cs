﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using System;
using System.IO;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Siemens.Helper;

/// <summary>西门子PPI协议的辅助类对象</summary>
public class SiemensPPIHelper
{
  /// <summary>
  /// 解析数据地址，解析出地址类型，起始地址，DB块的地址<br />
  /// Parse data address, parse out address type, start address, db block address
  /// </summary>
  /// <param name="address">起始地址，例如M100，I0，Q0，V100 -&gt;
  /// Start address, such as M100,I0,Q0,V100</param>
  /// <returns>解析数据地址，解析出地址类型，起始地址，DB块的地址 -&gt;
  /// Parse data address, parse out address type, start address, db block address</returns>
  public static OperateResult<S7AddressData> AnalysisAddress(string address)
  {
    S7AddressData s7AddressData = new S7AddressData();
    try
    {
      s7AddressData.DbBlock = (ushort) 0;
      if (address.StartsWith("SYS"))
      {
        s7AddressData.DataCode = (byte) 3;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(3));
      }
      else if (address.StartsWith("AI"))
      {
        s7AddressData.DataCode = (byte) 6;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(2));
      }
      else if (address.StartsWith("AQ"))
      {
        s7AddressData.DataCode = (byte) 7;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(2));
      }
      else if (address[0] == 'T')
      {
        s7AddressData.DataCode = (byte) 31 /*0x1F*/;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(1));
      }
      else if (address[0] == 'C')
      {
        s7AddressData.DataCode = (byte) 30;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(1));
      }
      else if (address.StartsWith("SM"))
      {
        s7AddressData.DataCode = (byte) 5;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(2));
      }
      else if (address[0] == 'S')
      {
        s7AddressData.DataCode = (byte) 4;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(1));
      }
      else if (address[0] == 'I')
      {
        s7AddressData.DataCode = (byte) 129;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(1));
      }
      else if (address[0] == 'Q')
      {
        s7AddressData.DataCode = (byte) 130;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(1));
      }
      else if (address[0] == 'M')
      {
        s7AddressData.DataCode = (byte) 131;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(1));
      }
      else if (address[0] == 'D' || address.StartsWith("DB"))
      {
        s7AddressData.DataCode = (byte) 132;
        string[] strArray = address.Split('.');
        s7AddressData.DbBlock = address[1] != 'B' ? Convert.ToUInt16(strArray[0].Substring(1)) : Convert.ToUInt16(strArray[0].Substring(2));
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(address.IndexOf('.') + 1));
      }
      else
      {
        if (address[0] != 'V')
          return new OperateResult<S7AddressData>(StringResources.Language.NotSupportedDataType);
        s7AddressData.DataCode = (byte) 132;
        s7AddressData.DbBlock = (ushort) 1;
        s7AddressData.AddressStart = S7AddressData.CalculateAddressStarted(address.Substring(1));
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<S7AddressData>(ex.Message);
    }
    return OperateResult.CreateSuccessResult<S7AddressData>(s7AddressData);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.BuildReadCommand(System.Byte,HslCommunication.Core.Address.S7AddressData,System.UInt16,System.Boolean)" />
  public static OperateResult<byte[]> BuildReadCommand(
    byte station,
    string address,
    ushort length,
    bool isBit)
  {
    OperateResult<S7AddressData> result = SiemensPPIHelper.AnalysisAddress(address);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : SiemensPPIHelper.BuildReadCommand(station, result.Content, length, isBit);
  }

  /// <summary>
  /// 生成一个读取字数据指令头的通用方法<br />
  /// A general method for generating a command header to read a Word data
  /// </summary>
  /// <param name="station">设备的站号信息 -&gt; Station number information for the device</param>
  /// <param name="address">起始地址，例如M100，I0，Q0，V100 -&gt;
  /// Start address, such as M100,I0,Q0,V100</param>
  /// <param name="length">读取数据长度 -&gt; Read Data length</param>
  /// <param name="isBit">是否为位读取</param>
  /// <returns>包含结果对象的报文 -&gt; Message containing the result object</returns>
  public static OperateResult<byte[]> BuildReadCommand(
    byte station,
    S7AddressData address,
    ushort length,
    bool isBit)
  {
    byte[] numArray = new byte[33];
    numArray[0] = (byte) 104;
    numArray[1] = BitConverter.GetBytes(numArray.Length - 6)[0];
    numArray[2] = BitConverter.GetBytes(numArray.Length - 6)[0];
    numArray[3] = (byte) 104;
    numArray[4] = station;
    numArray[5] = (byte) 0;
    numArray[6] = (byte) 108;
    numArray[7] = (byte) 50;
    numArray[8] = (byte) 1;
    numArray[9] = (byte) 0;
    numArray[10] = (byte) 0;
    numArray[11] = (byte) 0;
    numArray[12] = (byte) 0;
    numArray[13] = (byte) 0;
    numArray[14] = (byte) 14;
    numArray[15] = (byte) 0;
    numArray[16 /*0x10*/] = (byte) 0;
    numArray[17] = (byte) 4;
    numArray[18] = (byte) 1;
    numArray[19] = (byte) 18;
    numArray[20] = (byte) 10;
    numArray[21] = (byte) 16 /*0x10*/;
    numArray[22] = isBit ? (byte) 1 : (byte) 2;
    numArray[23] = (byte) 0;
    numArray[24] = BitConverter.GetBytes(length)[0];
    numArray[25] = BitConverter.GetBytes(length)[1];
    numArray[26] = (byte) address.DbBlock;
    numArray[27] = address.DataCode;
    numArray[28] = BitConverter.GetBytes(address.AddressStart)[2];
    numArray[29] = BitConverter.GetBytes(address.AddressStart)[1];
    numArray[30] = BitConverter.GetBytes(address.AddressStart)[0];
    int num = 0;
    for (int index = 4; index < 31 /*0x1F*/; ++index)
      num += (int) numArray[index];
    numArray[31 /*0x1F*/] = BitConverter.GetBytes(num)[0];
    numArray[32 /*0x20*/] = (byte) 22;
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>生成一个写入PLC数据信息的报文内容</summary>
  /// <param name="station">PLC的站号</param>
  /// <param name="address">地址</param>
  /// <param name="values">数据值</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult<byte[]> BuildWriteCommand(
    byte station,
    string address,
    byte[] values)
  {
    OperateResult<S7AddressData> result = SiemensPPIHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    int length = values.Length;
    byte[] numArray = new byte[37 + values.Length];
    numArray[0] = (byte) 104;
    numArray[1] = BitConverter.GetBytes(numArray.Length - 6)[0];
    numArray[2] = BitConverter.GetBytes(numArray.Length - 6)[0];
    numArray[3] = (byte) 104;
    numArray[4] = station;
    numArray[5] = (byte) 0;
    numArray[6] = (byte) 124;
    numArray[7] = (byte) 50;
    numArray[8] = (byte) 1;
    numArray[9] = (byte) 0;
    numArray[10] = (byte) 0;
    numArray[11] = (byte) 0;
    numArray[12] = (byte) 0;
    numArray[13] = (byte) 0;
    numArray[14] = (byte) 14;
    numArray[15] = (byte) 0;
    numArray[16 /*0x10*/] = (byte) (values.Length + 4);
    numArray[17] = (byte) 5;
    numArray[18] = (byte) 1;
    numArray[19] = (byte) 18;
    numArray[20] = (byte) 10;
    numArray[21] = (byte) 16 /*0x10*/;
    numArray[22] = (byte) 2;
    numArray[23] = (byte) 0;
    numArray[24] = BitConverter.GetBytes(length)[0];
    numArray[25] = BitConverter.GetBytes(length)[1];
    numArray[26] = (byte) result.Content.DbBlock;
    numArray[27] = result.Content.DataCode;
    numArray[28] = BitConverter.GetBytes(result.Content.AddressStart)[2];
    numArray[29] = BitConverter.GetBytes(result.Content.AddressStart)[1];
    numArray[30] = BitConverter.GetBytes(result.Content.AddressStart)[0];
    numArray[31 /*0x1F*/] = (byte) 0;
    numArray[32 /*0x20*/] = (byte) 4;
    numArray[33] = BitConverter.GetBytes(length * 8)[1];
    numArray[34] = BitConverter.GetBytes(length * 8)[0];
    values.CopyTo((Array) numArray, 35);
    int num = 0;
    for (int index = 4; index < numArray.Length - 2; ++index)
      num += (int) numArray[index];
    numArray[numArray.Length - 2] = BitConverter.GetBytes(num)[0];
    numArray[numArray.Length - 1] = (byte) 22;
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>
  /// 根据错误代号信息，获取到指定的文本信息<br />
  /// According to the error code information, get the specified text information
  /// </summary>
  /// <param name="code">错误状态信息</param>
  /// <returns>消息文本</returns>
  public static string GetMsgFromStatus(byte code)
  {
    switch (code)
    {
      case 1:
        return "Hardware fault";
      case 3:
        return "Illegal object access";
      case 5:
        return "Invalid address(incorrent variable address)";
      case 6:
        return "Data type is not supported";
      case 10:
        return "Object does not exist or length error";
      case byte.MaxValue:
        return "No error";
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>根据错误信息，获取到文本信息</summary>
  /// <param name="errorClass">错误类型</param>
  /// <param name="errorCode">错误代码</param>
  /// <returns>错误信息</returns>
  public static string GetMsgFromStatus(byte errorClass, byte errorCode)
  {
    if (errorClass == (byte) 128 /*0x80*/ && errorCode == (byte) 1)
      return "Switch in wrong position for requested operation";
    if (errorClass == (byte) 129 && errorCode == (byte) 4)
      return "Miscellaneous structure error in command.  Command is not supportedby CPU";
    if (errorClass == (byte) 132 && errorCode == (byte) 4)
      return "CPU is busy processing an upload or download CPU cannot process command because of system fault condition";
    if (errorClass == (byte) 133 && errorCode == (byte) 0)
      return "Length fields are not correct or do not agree with the amount of data received";
    int num;
    switch (errorClass)
    {
      case 210:
        return "Error in upload or download command";
      case 214:
        return "Protection error(password)";
      case 220:
        num = errorCode == (byte) 1 ? 1 : 0;
        break;
      default:
        num = 0;
        break;
    }
    return num != 0 ? "Error in time-of-day clock data" : StringResources.Language.UnknownError;
  }

  /// <summary>创建写入PLC的bool类型数据报文指令</summary>
  /// <param name="station">PLC的站号信息</param>
  /// <param name="address">地址信息</param>
  /// <param name="values">bool[]数据值</param>
  /// <returns>带有成功标识的结果对象</returns>
  public static OperateResult<byte[]> BuildWriteCommand(
    byte station,
    string address,
    bool[] values)
  {
    OperateResult<S7AddressData> result = SiemensPPIHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    byte[] numArray1 = SoftBasic.BoolArrayToByte(values);
    byte[] numArray2 = new byte[37 + numArray1.Length];
    numArray2[0] = (byte) 104;
    numArray2[1] = BitConverter.GetBytes(numArray2.Length - 6)[0];
    numArray2[2] = BitConverter.GetBytes(numArray2.Length - 6)[0];
    numArray2[3] = (byte) 104;
    numArray2[4] = station;
    numArray2[5] = (byte) 0;
    numArray2[6] = (byte) 124;
    numArray2[7] = (byte) 50;
    numArray2[8] = (byte) 1;
    numArray2[9] = (byte) 0;
    numArray2[10] = (byte) 0;
    numArray2[11] = (byte) 0;
    numArray2[12] = (byte) 0;
    numArray2[13] = (byte) 0;
    numArray2[14] = (byte) 14;
    numArray2[15] = (byte) 0;
    numArray2[16 /*0x10*/] = (byte) 5;
    numArray2[17] = (byte) 5;
    numArray2[18] = (byte) 1;
    numArray2[19] = (byte) 18;
    numArray2[20] = (byte) 10;
    numArray2[21] = (byte) 16 /*0x10*/;
    numArray2[22] = (byte) 1;
    numArray2[23] = (byte) 0;
    numArray2[24] = BitConverter.GetBytes(values.Length)[0];
    numArray2[25] = BitConverter.GetBytes(values.Length)[1];
    numArray2[26] = (byte) result.Content.DbBlock;
    numArray2[27] = result.Content.DataCode;
    numArray2[28] = BitConverter.GetBytes(result.Content.AddressStart)[2];
    numArray2[29] = BitConverter.GetBytes(result.Content.AddressStart)[1];
    numArray2[30] = BitConverter.GetBytes(result.Content.AddressStart)[0];
    numArray2[31 /*0x1F*/] = (byte) 0;
    numArray2[32 /*0x20*/] = (byte) 3;
    numArray2[33] = BitConverter.GetBytes(values.Length)[1];
    numArray2[34] = BitConverter.GetBytes(values.Length)[0];
    numArray1.CopyTo((Array) numArray2, 35);
    int num = 0;
    for (int index = 4; index < numArray2.Length - 2; ++index)
      num += (int) numArray2[index];
    numArray2[numArray2.Length - 2] = BitConverter.GetBytes(num)[0];
    numArray2[numArray2.Length - 1] = (byte) 22;
    return OperateResult.CreateSuccessResult<byte[]>(numArray2);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.NetMessageBase.CheckReceiveDataComplete(System.Byte[],System.IO.MemoryStream)" />
  public static bool CheckReceiveDataComplete(MemoryStream ms)
  {
    byte[] array = ms.ToArray();
    return array.Length != 0 && (array.Length == 1 && array[0] == (byte) 229 || array.Length > 6 && array[0] == (byte) 104 && (int) array[1] + 6 == array.Length && array[array.Length - 1] == (byte) 22 || array.Length > 6 && array[0] == (byte) 3 && (int) array[3] == array.Length);
  }

  /// <summary>检查西门子PLC的返回的数据和合法性，对反馈的数据进行初步的校验</summary>
  /// <param name="content">服务器返回的原始的数据内容</param>
  /// <returns>是否校验成功</returns>
  public static OperateResult CheckResponse(byte[] content)
  {
    if (content.Length < 21)
      return new OperateResult(10000, "Failed, data too short:" + SoftBasic.ByteToHexString(content, ' '));
    if (content[17] != (byte) 0 || content[18] > (byte) 0)
      return new OperateResult((int) content[19], SiemensPPIHelper.GetMsgFromStatus(content[18], content[19]));
    if (content.Length < 22)
      return new OperateResult(10000, "Failed, data too short:" + SoftBasic.ByteToHexString(content, ' '));
    return content[21] != byte.MaxValue ? new OperateResult((int) content[21], SiemensPPIHelper.GetMsgFromStatus(content[21])) : OperateResult.CreateSuccessResult();
  }

  /// <summary>根据站号信息获取命令二次确认的报文信息</summary>
  /// <param name="station">站号信息</param>
  /// <returns>二次命令确认的报文</returns>
  public static byte[] GetExecuteConfirm(byte station)
  {
    byte[] executeConfirm = new byte[6]
    {
      (byte) 16 /*0x10*/,
      station,
      (byte) 0,
      (byte) 92,
      (byte) 94,
      (byte) 22
    };
    int num = 0;
    for (int index = 1; index < 4; ++index)
      num += (int) executeConfirm[index];
    executeConfirm[4] = (byte) num;
    return executeConfirm;
  }

  private static OperateResult<byte[]> Read(
    IReadWriteDevice plc,
    S7AddressData address,
    ushort length,
    byte station,
    object communicationLock)
  {
    OperateResult<byte[]> operateResult1 = SiemensPPIHelper.BuildReadCommand(station, address, length, false);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    lock (communicationLock)
    {
      OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      if (operateResult2.Content == null || operateResult2.Content.Length == 0 || operateResult2.Content[0] != (byte) 229)
        return new OperateResult<byte[]>("PLC Receive Check Failed:" + SoftBasic.ByteToHexString(operateResult2.Content, ' '));
      OperateResult<byte[]> operateResult3 = plc.ReadFromCoreServer(SiemensPPIHelper.GetExecuteConfirm(station));
      if (!operateResult3.IsSuccess)
        return operateResult3;
      OperateResult result = SiemensPPIHelper.CheckResponse(operateResult3.Content);
      return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : SiemensS7Helper.AnalysisReadByte(operateResult3.Content);
    }
  }

  /// <summary>
  /// 从西门子的PLC中读取数据信息，地址为"M100","AI100","I0","Q0","V100","S100"等<br />
  /// Read data information from Siemens PLC with addresses "M100", "AI100", "I0", "Q0", "V100", "S100", etc.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="address">西门子的地址数据信息</param>
  /// <param name="length">数据长度</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="communicationLock">当前的同通信锁</param>
  /// <returns>带返回结果的结果对象</returns>
  public static OperateResult<byte[]> Read(
    IReadWriteDevice plc,
    string address,
    ushort length,
    byte station,
    object communicationLock)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<S7AddressData> result = SiemensPPIHelper.AnalysisAddress(address);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : SiemensPPIHelper.Read(plc, result.Content, length, parameter, communicationLock);
  }

  /// <summary>
  /// 从西门子的PLC中读取bool数据信息，地址为"M100.0","AI100.1","I0.3","Q0.6","V100.4","S100"等<br />
  /// Read bool data information from Siemens PLC, the addresses are "M100.0", "AI100.1", "I0.3", "Q0.6", "V100.4", "S100", etc.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="address">西门子的地址数据信息</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="communicationLock">当前的同通信锁</param>
  /// <returns>带返回结果的结果对象</returns>
  public static OperateResult<bool> ReadBool(
    IReadWriteDevice plc,
    string address,
    byte station,
    object communicationLock)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> result1 = SiemensPPIHelper.BuildReadCommand(parameter, address, (ushort) 1, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) result1);
    lock (communicationLock)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool>((OperateResult) result2);
      if (result2.Content == null || result2.Content.Length == 0 || result2.Content[0] != (byte) 229)
        return new OperateResult<bool>("PLC Receive Check Failed:" + SoftBasic.ByteToHexString(result2.Content, ' '));
      OperateResult<byte[]> result3 = plc.ReadFromCoreServer(SiemensPPIHelper.GetExecuteConfirm(parameter));
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool>((OperateResult) result3);
      OperateResult result4 = SiemensPPIHelper.CheckResponse(result3.Content);
      if (!result4.IsSuccess)
        return OperateResult.CreateFailedResult<bool>(result4);
      OperateResult<byte[]> result5 = SiemensS7Helper.AnalysisReadBit(result3.Content);
      return !result5.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) result5) : OperateResult.CreateSuccessResult<bool>(result5.Content.ToBoolArray()[0]);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice plc,
    string address,
    ushort length,
    byte station,
    object communicationLock)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<S7AddressData> result1 = SiemensPPIHelper.AnalysisAddress(address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    int newStart;
    ushort byteLength;
    int offset;
    HslHelper.CalculateStartBitIndexAndLength(result1.Content.AddressStart, length, out newStart, out byteLength, out offset);
    result1.Content.AddressStart = newStart;
    result1.Content.Length = byteLength;
    OperateResult<byte[]> result2 = SiemensPPIHelper.Read(plc, result1.Content, result1.Content.Length, parameter, communicationLock);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<bool[]>(result2.Content.ToBoolArray().SelectMiddle<bool>(offset, (int) length));
  }

  /// <summary>
  /// 将字节数据写入到西门子PLC中，地址为"M100.0","AI100.1","I0.3","Q0.6","V100.4","S100"等<br />
  /// Write byte data to Siemens PLC with addresses "M100.0", "AI100.1", "I0.3", "Q0.6", "V100.4", "S100", etc.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="address">西门子的地址数据信息</param>
  /// <param name="value">数据长度</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="communicationLock">当前的同通信锁</param>
  /// <returns>带返回结果的结果对象</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    string address,
    byte[] value,
    byte station,
    object communicationLock)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> operateResult1 = SiemensPPIHelper.BuildWriteCommand(parameter, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    lock (communicationLock)
    {
      OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return (OperateResult) operateResult2;
      if (operateResult2.Content == null || operateResult2.Content.Length == 0 || operateResult2.Content[0] != (byte) 229)
        return (OperateResult) new OperateResult<byte[]>("PLC Receive Check Failed:" + SoftBasic.ByteToHexString(operateResult2.Content, ' '));
      OperateResult<byte[]> operateResult3 = plc.ReadFromCoreServer(SiemensPPIHelper.GetExecuteConfirm(parameter));
      if (!operateResult3.IsSuccess)
        return (OperateResult) operateResult3;
      OperateResult operateResult4 = SiemensPPIHelper.CheckResponse(operateResult3.Content);
      return !operateResult4.IsSuccess ? operateResult4 : OperateResult.CreateSuccessResult();
    }
  }

  /// <summary>
  /// 将bool数据写入到西门子PLC中，地址为"M100.0","AI100.1","I0.3","Q0.6","V100.4","S100"等<br />
  /// Write the bool data to Siemens PLC with the addresses "M100.0", "AI100.1", "I0.3", "Q0.6", "V100.4", "S100", etc.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="address">西门子的地址数据信息</param>
  /// <param name="value">数据长度</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="communicationLock">当前的同通信锁</param>
  /// <returns>带返回结果的结果对象</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    string address,
    bool[] value,
    byte station,
    object communicationLock)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> operateResult1 = SiemensPPIHelper.BuildWriteCommand(parameter, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    lock (communicationLock)
    {
      OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return (OperateResult) operateResult2;
      if (operateResult2.Content == null || operateResult2.Content.Length == 0 || operateResult2.Content[0] != (byte) 229)
        return (OperateResult) new OperateResult<byte[]>("PLC Receive Check Failed:" + SoftBasic.ByteToHexString(operateResult2.Content, ' '));
      OperateResult<byte[]> operateResult3 = plc.ReadFromCoreServer(SiemensPPIHelper.GetExecuteConfirm(parameter));
      if (!operateResult3.IsSuccess)
        return (OperateResult) operateResult3;
      OperateResult operateResult4 = SiemensPPIHelper.CheckResponse(operateResult3.Content);
      return !operateResult4.IsSuccess ? operateResult4 : OperateResult.CreateSuccessResult();
    }
  }

  /// <summary>
  /// 启动西门子PLC为RUN模式，参数信息可以携带站号信息 "s=2;", 注意，分号是必须的。<br />
  /// Start Siemens PLC in RUN mode, parameter information can carry station number information "s=2;", note that the semicolon is required.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="parameter">额外的参数信息，例如可以携带站号信息 "s=2;", 注意，分号是必须的。</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="communicationLock">当前的同通信锁</param>
  /// <returns>是否启动成功</returns>
  public static OperateResult Start(
    IReadWriteDevice plc,
    string parameter,
    byte station,
    object communicationLock)
  {
    byte parameter1 = (byte) HslHelper.ExtractParameter(ref parameter, "s", (int) station);
    byte[] send = new byte[39]
    {
      (byte) 104,
      (byte) 33,
      (byte) 33,
      (byte) 104,
      station,
      (byte) 0,
      (byte) 108,
      (byte) 50,
      (byte) 1,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 20,
      (byte) 0,
      (byte) 0,
      (byte) 40,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 253,
      (byte) 0,
      (byte) 0,
      (byte) 9,
      (byte) 80 /*0x50*/,
      (byte) 95,
      (byte) 80 /*0x50*/,
      (byte) 82,
      (byte) 79,
      (byte) 71,
      (byte) 82,
      (byte) 65,
      (byte) 77,
      (byte) 170,
      (byte) 22
    };
    lock (communicationLock)
    {
      OperateResult<byte[]> operateResult1 = plc.ReadFromCoreServer(send);
      if (!operateResult1.IsSuccess)
        return (OperateResult) operateResult1;
      if (operateResult1.Content == null || operateResult1.Content.Length == 0 || operateResult1.Content[0] != (byte) 229)
        return (OperateResult) new OperateResult<byte[]>("PLC Receive Check Failed:" + SoftBasic.ByteToHexString(operateResult1.Content, ' '));
      OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(SiemensPPIHelper.GetExecuteConfirm(parameter1));
      return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
    }
  }

  /// <summary>
  /// 停止西门子PLC，切换为Stop模式，参数信息可以携带站号信息 "s=2;", 注意，分号是必须的。<br />
  /// Stop Siemens PLC and switch to Stop mode, parameter information can carry station number information "s=2;", note that the semicolon is required.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="parameter">额外的参数信息，例如可以携带站号信息 "s=2;", 注意，分号是必须的。</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="communicationLock">当前的同通信锁</param>
  /// <returns>是否停止成功</returns>
  public static OperateResult Stop(
    IReadWriteDevice plc,
    string parameter,
    byte station,
    object communicationLock)
  {
    byte parameter1 = (byte) HslHelper.ExtractParameter(ref parameter, "s", (int) station);
    byte[] send = new byte[35]
    {
      (byte) 104,
      (byte) 29,
      (byte) 29,
      (byte) 104,
      station,
      (byte) 0,
      (byte) 108,
      (byte) 50,
      (byte) 1,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 16 /*0x10*/,
      (byte) 0,
      (byte) 0,
      (byte) 41,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 9,
      (byte) 80 /*0x50*/,
      (byte) 95,
      (byte) 80 /*0x50*/,
      (byte) 82,
      (byte) 79,
      (byte) 71,
      (byte) 82,
      (byte) 65,
      (byte) 77,
      (byte) 170,
      (byte) 22
    };
    lock (communicationLock)
    {
      OperateResult<byte[]> operateResult1 = plc.ReadFromCoreServer(send);
      if (!operateResult1.IsSuccess)
        return (OperateResult) operateResult1;
      if (operateResult1.Content == null || operateResult1.Content.Length == 0 || operateResult1.Content[0] != (byte) 229)
        return (OperateResult) new OperateResult<byte[]>("PLC Receive Check Failed:" + SoftBasic.ByteToHexString(operateResult1.Content, ' '));
      OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(SiemensPPIHelper.GetExecuteConfirm(parameter1));
      return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
    }
  }

  /// <summary>
  /// 读取西门子PLC的型号信息，参数信息可以携带站号信息 "s=2;", 注意，分号是必须的。<br />
  /// Read the model information of Siemens PLC, the parameter information can carry the station number information "s=2;", note that the semicolon is required.
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="parameter">额外的参数信息，例如可以携带站号信息 "s=2;", 注意，分号是必须的。</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="communicationLock">当前的同通信锁</param>
  /// <returns>包含是否成功的结果对象</returns>
  public static OperateResult<string> ReadPlcType(
    IReadWriteDevice plc,
    string parameter,
    byte station,
    object communicationLock)
  {
    byte parameter1 = (byte) HslHelper.ExtractParameter(ref parameter, "s", (int) station);
    OperateResult<byte[]> result1 = SiemensPPIHelper.BuildReadCommand(parameter1, "SYS0", (ushort) 20, false);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    lock (communicationLock)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) result2);
      if (result2.Content == null || result2.Content.Length == 0 || result2.Content[0] != (byte) 229)
        return new OperateResult<string>("PLC Receive Check Failed:" + SoftBasic.ByteToHexString(result2.Content, ' '));
      OperateResult<byte[]> result3 = plc.ReadFromCoreServer(SiemensPPIHelper.GetExecuteConfirm(parameter1));
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) result3);
      try
      {
        byte[] numArray = new byte[20];
        if (result3.Content[21] == byte.MaxValue && result3.Content[22] == (byte) 4)
          Array.Copy((Array) result3.Content, 25, (Array) numArray, 0, 20);
        return OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(numArray));
      }
      catch (Exception ex)
      {
        return new OperateResult<string>($"Get plc type failed: {ex.Message}{Environment.NewLine}Content: {result3.Content.ToHexString(' ')}");
      }
    }
  }
}
