﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.ExceptionArgs
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>异常消息基类</summary>
[Serializable]
public abstract class ExceptionArgs
{
  /// <summary>携带的额外的消息类对象</summary>
  public virtual string Message => string.Empty;
}
