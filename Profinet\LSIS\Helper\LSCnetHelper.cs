﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.Helper.LSCnetHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.LSIS.Helper;

/// <summary>Cnet的辅助类</summary>
public class LSCnetHelper
{
  private const string CnetTypes = "PMLKFTCDSQINUZR";

  /// <summary>
  /// 根据错误号，获取到真实的错误描述信息<br />
  /// According to the error number, get the real error description information
  /// </summary>
  /// <param name="err">错误号</param>
  /// <returns>真实的错误描述信息</returns>
  public static string GetErrorText(int err)
  {
    switch (err)
    {
      case 3:
        return StringResources.Language.LsisCnet0003;
      case 4:
        return StringResources.Language.LsisCnet0004;
      case 7:
        return StringResources.Language.LsisCnet0007;
      case 17:
        return StringResources.Language.LsisCnet0011;
      case 144 /*0x90*/:
        return StringResources.Language.LsisCnet0090;
      case 400:
        return StringResources.Language.LsisCnet0190;
      case 656:
        return StringResources.Language.LsisCnet0290;
      case 4402:
        return StringResources.Language.LsisCnet1132;
      case 4658:
        return StringResources.Language.LsisCnet1232;
      case 4660:
        return StringResources.Language.LsisCnet1234;
      case 4914:
        return StringResources.Language.LsisCnet1332;
      case 5170:
        return StringResources.Language.LsisCnet1432;
      case 28978:
        return StringResources.Language.LsisCnet7132;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.UnpackResponseContent(System.Byte[],System.Byte[])" />
  public static OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    try
    {
      if (response[0] == (byte) 6)
      {
        if (response[3] == (byte) 87 || response[3] == (byte) 119)
          return OperateResult.CreateSuccessResult<byte[]>(response);
        string str = Encoding.ASCII.GetString(response, 4, 2);
        switch (str)
        {
          case "SS":
            int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(response, 6, 2), 16 /*0x10*/);
            int index1 = 8;
            List<byte> byteList = new List<byte>();
            for (int index2 = 0; index2 < int32_1; ++index2)
            {
              int int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(response, index1, 2), 16 /*0x10*/);
              byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetString(response, index1 + 2, int32_2 * 2).ToHexBytes());
              index1 += 2 + int32_2 * 2;
            }
            return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
          case "SB":
            int int32_3 = Convert.ToInt32(Encoding.ASCII.GetString(response, 8, 2), 16 /*0x10*/);
            return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetString(response, 10, int32_3 * 2).ToHexBytes());
          default:
            return new OperateResult<byte[]>(1, $"Command Wrong:{str}{Environment.NewLine}Source: {response.ToHexString()}");
        }
      }
      else
      {
        if (response[0] != (byte) 21)
          return new OperateResult<byte[]>((int) response[0], "Source: " + SoftBasic.GetAsciiStringRender(response));
        int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, 6, 4), 16 /*0x10*/);
        return new OperateResult<byte[]>(int32, LSCnetHelper.GetErrorText(int32));
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(1, $"Wrong:{ex.Message}{Environment.NewLine}Source: {response.ToHexString()}");
    }
  }

  /// <summary>
  /// 
  /// </summary>
  /// <param name="Address"></param>
  /// <param name="IsWrite"></param>
  /// <returns></returns>
  public static string GetAddressOfU_Q_I(string Address, bool IsWrite = false)
  {
    string[] strArray = Address.Split('.');
    object obj1 = (object) 0;
    object obj2;
    if (strArray.Length >= 3)
    {
      int int32 = Convert.ToInt32(strArray[2].Last<char>().ToString(), 16 /*0x10*/);
      obj2 = !(LSFastEnet.IsHex(strArray[2]) & IsWrite) ? (object) ((int.Parse(strArray[0]) * 32 /*0x20*/ + int.Parse(strArray[1])) * 10 + int32) : (object) ((int.Parse(strArray[0]) * 32 /*0x20*/ + int.Parse(strArray[1])).ToString() + strArray[2]);
    }
    else
      obj2 = (object) (int.Parse(strArray[0]) * 32 /*0x20*/ + int.Parse(strArray[1]));
    return $"{obj2}";
  }

  /// <summary>
  /// 从输入的地址里解析出真实的可以直接放到协议的地址信息，如果是X的地址，自动转换带小数点的表示方式到位地址，如果是其他类型地址，则一律统一转化为字节为单位的地址<br />
  /// The real address information that can be directly put into the protocol is parsed from the input address. If it is the address of X,
  /// it will automatically convert the representation with a decimal point to the address. If it is an address of other types, it will be uniformly converted into a unit of bytes. address
  /// </summary>
  /// <param name="address">输入的起始偏移地址</param>
  /// <param name="IsWrite">是否转换为bool地址</param>
  /// <returns>analysis result</returns>
  public static OperateResult<string> AnalysisAddress(string address, bool IsWrite = false)
  {
    bool flag = false;
    StringBuilder stringBuilder = new StringBuilder();
    try
    {
      if (!"PMLKFTCDSQINUZR".Contains<char>(address[0]))
        return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
      stringBuilder.Append("%");
      stringBuilder.Append(address[0]);
      if (address.IndexOf('.') > 0)
        flag = true;
      if (address[1] == 'X')
      {
        stringBuilder.Append("X");
        if (flag)
        {
          if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
          {
            int indexInformation = HslHelper.GetBitIndexInformation(ref address);
            stringBuilder.Append(address.Substring(2));
            stringBuilder.Append(indexInformation.ToString("X1"));
          }
          else
            stringBuilder.Append(LSCnetHelper.GetAddressOfU_Q_I(address.Substring(2), IsWrite));
        }
        else
          stringBuilder.Append(address.Substring(2));
      }
      else
      {
        int num1 = 0;
        int num2 = 0;
        switch (address[1])
        {
          case 'B':
            stringBuilder.Append(flag ? "X" : "B");
            if (flag)
            {
              if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
              {
                num2 = HslHelper.GetBitIndexInformation(ref address);
                stringBuilder.Append(address.Substring(2));
                stringBuilder.Append(num2.ToString("X1"));
                break;
              }
              stringBuilder.Append(LSCnetHelper.GetAddressOfU_Q_I(address.Substring(2)));
              break;
            }
            if (address[0] == 'I' || address[0] == 'Q')
            {
              stringBuilder.Append(address.Substring(2));
              break;
            }
            num1 = Convert.ToInt32(address.Substring(2)) * 2;
            break;
          case 'D':
            stringBuilder.Append(flag ? "X" : "D");
            if (flag)
            {
              if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
              {
                num2 = HslHelper.GetBitIndexInformation(ref address);
                stringBuilder.Append(address.Substring(2));
                stringBuilder.Append(num2.ToString("X1"));
                break;
              }
              stringBuilder.Append(LSCnetHelper.GetAddressOfU_Q_I(address.Substring(2)));
              break;
            }
            if (address[0] == 'I' || address[0] == 'Q')
            {
              stringBuilder.Append(address.Substring(2));
              break;
            }
            num1 = Convert.ToInt32(address.Substring(2)) * 4;
            break;
          case 'L':
            stringBuilder.Append(flag ? "X" : "L");
            if (flag)
            {
              if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
              {
                num2 = HslHelper.GetBitIndexInformation(ref address);
                stringBuilder.Append(address.Substring(2));
                stringBuilder.Append(num2.ToString("X1"));
                break;
              }
              stringBuilder.Append(LSCnetHelper.GetAddressOfU_Q_I(address.Substring(2)));
              break;
            }
            if (address[0] == 'I' || address[0] == 'Q')
            {
              stringBuilder.Append(address.Substring(2));
              break;
            }
            num1 = Convert.ToInt32(address.Substring(2)) * 8;
            break;
          case 'W':
            stringBuilder.Append(flag ? "X" : "W");
            if (flag)
            {
              if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
              {
                num2 = HslHelper.GetBitIndexInformation(ref address);
                stringBuilder.Append(address.Substring(2));
                stringBuilder.Append(num2.ToString("X1"));
                break;
              }
              stringBuilder.Append(LSCnetHelper.GetAddressOfU_Q_I(address.Substring(2)));
              break;
            }
            if (address[0] == 'I' || address[0] == 'Q')
            {
              stringBuilder.Append(address.Substring(2));
              break;
            }
            num1 = Convert.ToInt32(address.Substring(2)) * 2;
            break;
          default:
            stringBuilder.Append(flag ? "X" : "B");
            if (flag)
            {
              if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
              {
                num2 = HslHelper.GetBitIndexInformation(ref address);
                stringBuilder.Append(address.Substring(1));
                stringBuilder.Append(num2.ToString("X1"));
                break;
              }
              stringBuilder.Append(LSCnetHelper.GetAddressOfU_Q_I(address.Substring(1)));
              break;
            }
            if (address[0] == 'I' || address[0] == 'Q')
            {
              stringBuilder.Append(address.Substring(1));
              break;
            }
            num1 = Convert.ToInt32(address.Substring(1)) * 2;
            break;
        }
        if (flag)
        {
          stringBuilder.Append(num1 / 2);
          stringBuilder.Append(num2.ToString("X1"));
        }
        else if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
          stringBuilder.Append(num1);
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
    return OperateResult.CreateSuccessResult<string>(stringBuilder.ToString());
  }

  /// <summary>往现有的命令数据中增加BCC的内容</summary>
  /// <param name="command">现有的命令</param>
  private static void AddBccTail(List<byte> command)
  {
    int num = 0;
    for (int index = 0; index < command.Count; ++index)
      num += (int) command[index];
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) num));
  }

  /// <summary>reading address  Type of ReadByte</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="length">read length</param>
  /// <returns>command bytes</returns>
  public static OperateResult<List<byte[]>> BuildReadByteCommand(
    byte station,
    string address,
    ushort length)
  {
    OperateResult<LsisCnetAddress> from = LsisCnetAddress.ParseFrom(address, length, false);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from);
    List<byte[]> numArrayList = new List<byte[]>();
    int[] array = SoftBasic.SplitIntegerToArray((int) length, 254);
    for (int index = 0; index < array.Length; ++index)
    {
      List<byte> command = new List<byte>();
      command.Add((byte) 5);
      command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom(station));
      command.Add((byte) 114);
      command.Add((byte) 83);
      command.Add((byte) 66);
      string addressCommand = from.Content.GetAddressCommand();
      command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) addressCommand.Length));
      command.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(addressCommand));
      command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) array[index]));
      command.Add((byte) 4);
      LSCnetHelper.AddBccTail(command);
      numArrayList.Add(command.ToArray());
      from.Content.AddressStart += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.BuildReadIndividualCommand(System.Byte,System.String[])" />
  public static OperateResult<byte[]> BuildReadIndividualCommand(byte station, string address)
  {
    return LSCnetHelper.BuildReadIndividualCommand(station, new string[1]
    {
      address
    });
  }

  /// <summary>Multi reading address Type of Read Individual</summary>
  /// <param name="station">plc station</param>
  /// <param name="addresses">address, for example: MX100, PX100</param>
  /// <returns></returns>
  public static OperateResult<byte[]> BuildReadIndividualCommand(byte station, string[] addresses)
  {
    List<byte> command = new List<byte>();
    command.Add((byte) 5);
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom(station));
    command.Add((byte) 114);
    command.Add((byte) 83);
    command.Add((byte) 83);
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) addresses.Length));
    if (addresses.Length > 1)
    {
      foreach (string address in addresses)
      {
        string s = address.StartsWith("%") ? address : "%" + address;
        command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) s.Length));
        command.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(s));
      }
    }
    else
    {
      foreach (string address in addresses)
      {
        OperateResult<string> result = LSCnetHelper.AnalysisAddress(address);
        if (!result.IsSuccess)
          return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
        command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) result.Content.Length));
        command.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(result.Content));
      }
    }
    command.Add((byte) 4);
    LSCnetHelper.AddBccTail(command);
    return OperateResult.CreateSuccessResult<byte[]>(command.ToArray());
  }

  /// <summary>write data to address  Type of ReadByte</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="value">source value</param>
  /// <returns>command bytes</returns>
  public static OperateResult<byte[]> BuildWriteByteCommand(
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<LsisCnetAddress> from = LsisCnetAddress.ParseFrom(address, (ushort) 0, false);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    List<byte> command = new List<byte>();
    command.Add((byte) 5);
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom(station));
    command.Add((byte) 119);
    command.Add((byte) 83);
    command.Add((byte) 66);
    string addressCommand = from.Content.GetAddressCommand();
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) addressCommand.Length));
    command.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(addressCommand));
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) value.Length));
    command.AddRange((IEnumerable<byte>) SoftBasic.BytesToAsciiBytes(value));
    command.Add((byte) 4);
    LSCnetHelper.AddBccTail(command);
    return OperateResult.CreateSuccessResult<byte[]>(command.ToArray());
  }

  /// <summary>write data to address  Type of One</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="value">source value</param>
  /// <returns>command bytes</returns>
  public static OperateResult<byte[]> BuildWriteOneCommand(
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<string> result = LSCnetHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    List<byte> command = new List<byte>();
    command.Add((byte) 5);
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom(station));
    command.Add((byte) 119);
    command.Add((byte) 83);
    command.Add((byte) 83);
    command.Add((byte) 48 /*0x30*/);
    command.Add((byte) 49);
    command.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) result.Content.Length));
    command.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(result.Content));
    command.AddRange((IEnumerable<byte>) SoftBasic.BytesToAsciiBytes(value));
    command.Add((byte) 4);
    LSCnetHelper.AddBccTail(command);
    return OperateResult.CreateSuccessResult<byte[]>(command.ToArray());
  }

  /// <summary>write data to address  Type of ReadByte</summary>
  /// <param name="station">plc station</param>
  /// <param name="address">address, for example: M100, D100, DW100</param>
  /// <param name="value">source value</param>
  /// <returns>command bytes</returns>
  public static OperateResult<byte[]> BuildWriteCommand(byte station, string address, byte[] value)
  {
    OperateResult<string> dataTypeToAddress = LSFastEnet.GetDataTypeToAddress(address);
    if (!dataTypeToAddress.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) dataTypeToAddress);
    switch (dataTypeToAddress.Content)
    {
      case "Bit":
        return LSCnetHelper.BuildWriteOneCommand(station, address, value);
      case "Word":
      case "DWord":
      case "LWord":
      case "Continuous":
        return LSCnetHelper.BuildWriteByteCommand(station, address, value);
      default:
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <summary>
  /// 从PLC的指定地址读取原始的字节数据信息，地址示例：MB100, MW100, MD100, 如果输入了M100等同于MB100<br />
  /// Read the original byte data information from the designated address of the PLC.
  /// Examples of addresses: MB100, MW100, MD100, if the input M100 is equivalent to MB100
  /// </summary>
  /// <remarks>
  /// 地址类型支持 P,M,L,K,F,T,C,D,R,I,Q,W, 支持携带站号的形式，例如 s=2;MW100
  /// </remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">PLC的地址信息，例如 M100, MB100, MW100, MD100</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  public static OperateResult<byte[]> Read(
    IReadWriteDeviceStation plc,
    string address,
    ushort length)
  {
    OperateResult<List<byte[]>> result = LSCnetHelper.BuildReadByteCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station), address, length);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : plc.ReadFromCoreServer((IEnumerable<byte[]>) result.Content);
  }

  /// <summary>
  /// 从PLC设备读取多个地址的数据信息，返回连续的字节数组，需要按照实际情况进行按顺序解析。<br />
  /// Read the data information of multiple addresses from the PLC device and return a continuous byte array, which needs to be parsed in order according to the actual situation.
  /// </summary>
  /// <remarks>按照每16个地址长度进行自动的切割，支持任意的多的长度地址</remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 M100, MB100, MW100, MD100</param>
  /// <returns>结果对象数据</returns>
  public static OperateResult<byte[]> Read(IReadWriteDevice plc, int station, string[] address)
  {
    List<string[]> strArrayList = SoftBasic.ArraySplitByLength<string>(address, 16 /*0x10*/);
    List<byte> byteList = new List<byte>(32 /*0x20*/);
    for (int index = 0; index < strArrayList.Count; ++index)
    {
      OperateResult<byte[]> operateResult1 = LSCnetHelper.BuildReadIndividualCommand((byte) station, strArrayList[index]);
      if (!operateResult1.IsSuccess)
        return operateResult1;
      OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      byteList.AddRange((IEnumerable<byte>) operateResult2.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>
  /// 将原始数据写入到PLC的指定的地址里，地址示例：MB100, MW100, MD100, 如果输入了M100等同于MB100<br />
  /// Write the original data to the designated address of the PLC.
  /// Examples of addresses: MB100, MW100, MD100, if input M100 is equivalent to MB100
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 M100, MB100, MW100, MD100</param>
  /// <param name="value">等待写入的原始数据内容</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    int station,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> operateResult = LSCnetHelper.BuildWriteByteCommand((byte) HslHelper.ExtractParameter(ref address, "s", station), address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) plc.ReadFromCoreServer(operateResult.Content);
  }

  /// <summary>
  /// 从PLC的指定地址读取原始的位数据信息，地址示例：MX100, MX10A<br />
  /// Read the original bool data information from the designated address of the PLC.
  /// Examples of addresses: MX100, MX10A
  /// </summary>
  /// <remarks>
  /// 地址类型支持 P,M,L,K,F,T,C,D,R,I,Q,W, 支持携带站号的形式，例如 s=2;MX100
  /// </remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 MX100, MX10A</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  public static OperateResult<bool> ReadBool(IReadWriteDevice plc, int station, string address)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    int indexInformation = HslHelper.GetBitIndexInformation(ref address);
    OperateResult<byte[]> result1 = LSCnetHelper.BuildReadIndividualCommand(parameter, address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) result1);
    OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) result2) : OperateResult.CreateSuccessResult<bool>(result2.Content.ToBoolArray()[indexInformation]);
  }

  /// <summary>
  /// 从PLC的指定地址读取原始的位数据信息，地址示例：MB100.0, MW100.0<br />
  /// Read the original bool data information from the designated address of the PLC.
  /// Examples of addresses: MB100.0, MW100.0
  /// </summary>
  /// <remarks>
  /// 地址类型支持 P,M,L,K,F,T,C,D,R,I,Q,W, 支持携带站号的形式，例如 s=2;MB100.0
  /// </remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">PLC的地址信息，例如 MB100.0, MW100.0</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDeviceStation plc,
    string address,
    ushort length)
  {
    return HslHelper.ReadBool((IReadWriteNet) plc, address, length, 8);
  }

  /// <summary>
  /// 将bool数据写入到PLC的指定的地址里，地址示例：MX100, MX10A<br />
  /// Write the bool data to the designated address of the PLC. Examples of addresses: MX100, MX10A
  /// </summary>
  /// <remarks>
  /// 地址类型支持 P,M,L,K,F,T,C,D,R,I,Q,W, 支持携带站号的形式，例如 s=2;MX100
  /// </remarks>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址信息，例如 MX100, MX10A</param>
  /// <param name="value">bool值信息</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  public static OperateResult Write(IReadWriteDevice plc, int station, string address, bool value)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    OperateResult<string> operateResult1 = LSCnetHelper.AnalysisAddress(address, true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = LSCnetHelper.BuildWriteOneCommand(parameter, operateResult1.Content.Substring(1), new byte[1]
    {
      value ? (byte) 1 : (byte) 0
    });
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) plc.ReadFromCoreServer(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Read(HslCommunication.Core.Net.IReadWriteDeviceStation,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDeviceStation plc,
    string address,
    ushort length)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<List<byte[]>> command = LSCnetHelper.BuildReadByteCommand(stat, address, length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult = await plc.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String[])" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice plc,
    int station,
    string[] address)
  {
    List<string[]> list = SoftBasic.ArraySplitByLength<string>(address, 16 /*0x10*/);
    List<byte> result = new List<byte>(32 /*0x20*/);
    for (int i = 0; i < list.Count; ++i)
    {
      OperateResult<byte[]> command = LSCnetHelper.BuildReadIndividualCommand((byte) station, list[i]);
      if (!command.IsSuccess)
        return command;
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
      if (!read.IsSuccess)
        return read;
      result.AddRange((IEnumerable<byte>) read.Content);
      command = (OperateResult<byte[]>) null;
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(result.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    int station,
    string address,
    byte[] value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    OperateResult<byte[]> command = LSCnetHelper.BuildWriteByteCommand(stat, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await plc.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String)" />
  public static async Task<OperateResult<bool>> ReadBoolAsync(
    IReadWriteDevice plc,
    int station,
    string address)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    int bitIndex = HslHelper.GetBitIndexInformation(ref address);
    OperateResult<byte[]> command = LSCnetHelper.BuildReadIndividualCommand(stat, address);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) command);
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool>(read.Content.ToBoolArray()[bitIndex]) : OperateResult.CreateFailedResult<bool>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.ReadBool(HslCommunication.Core.Net.IReadWriteDeviceStation,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDeviceStation plc,
    string address,
    ushort length)
  {
    OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) plc, address, length, 8);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    int station,
    string address,
    bool value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", station);
    OperateResult<string> analysis = LSCnetHelper.AnalysisAddress(address, true);
    if (!analysis.IsSuccess)
      return (OperateResult) analysis;
    OperateResult<byte[]> command = LSCnetHelper.BuildWriteOneCommand(stat, analysis.Content.Substring(1), new byte[1]
    {
      value ? (byte) 1 : (byte) 0
    });
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await plc.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }
}
