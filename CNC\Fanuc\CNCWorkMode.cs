﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.CNCWorkMode
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>设备的工作模式</summary>
public enum CNCWorkMode
{
  /// <summary>手动输入</summary>
  MDI = 0,
  /// <summary>自动循环</summary>
  AUTO = 1,
  /// <summary>程序编辑</summary>
  EDIT = 3,
  /// <summary>×100</summary>
  HANDLE = 4,
  /// <summary>连续进给</summary>
  JOG = 5,
  /// <summary>???</summary>
  TeachInJOG = 6,
  /// <summary>示教</summary>
  TeachInHandle = 7,
  /// <summary>???</summary>
  INCfeed = 8,
  /// <summary>机床回零</summary>
  REFerence = 9,
  /// <summary>???</summary>
  ReMoTe = 10, // 0x0000000A
}
