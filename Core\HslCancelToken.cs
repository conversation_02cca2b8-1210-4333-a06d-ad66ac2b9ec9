﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.HslCancelToken
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// 取消操作的令牌<br />
/// Token to cancel the operation
/// </summary>
public class HslCancelToken
{
  /// <summary>
  /// 是否取消的操作<br />
  /// Whether to cancel the operation
  /// </summary>
  public bool IsCancelled { get; set; }
}
