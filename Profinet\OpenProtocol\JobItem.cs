﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.JobItem
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>JobItem</summary>
public class JobItem
{
  /// <summary>实例化一个默认的对象</summary>
  public JobItem()
  {
  }

  /// <summary>指定原始数据实例化一个对象信息</summary>
  /// <param name="data">等待分析的原始数据，例如：15:011:0:22</param>
  public JobItem(string data)
  {
    if (data.Length == 12)
      data = data.Substring(0, 11);
    string[] strArray = data.Split(new char[1]{ ':' }, StringSplitOptions.RemoveEmptyEntries);
    this.ChannelID = Convert.ToInt32(strArray[0]);
    this.TypeID = Convert.ToInt32(strArray[1]);
    this.AutoValue = Convert.ToInt32(strArray[2]);
    this.BatchSize = Convert.ToInt32(strArray[3]);
  }

  /// <summary>Channel-ID</summary>
  public int ChannelID { get; set; }

  /// <summary>Type-ID</summary>
  public int TypeID { get; set; }

  /// <summary>AutoValue</summary>
  public int AutoValue { get; set; }

  /// <summary>BatchSize</summary>
  public int BatchSize { get; set; }
}
