﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.Helper.DLT645Type
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Instrument.DLT.Helper;

/// <summary>DLT645的类型</summary>
public enum DLT645Type
{
  /// <summary>DLT645/1997 版本</summary>
  DLT1997,
  /// <summary>DLT645/2007 版本</summary>
  DLT2007,
}
