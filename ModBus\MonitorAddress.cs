﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.ModBus.MonitorAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.ModBus;

/// <summary>监视使用的数据缓存</summary>
internal struct MonitorAddress
{
  /// <summary>地址</summary>
  public ushort Address;
  /// <summary>原有的值</summary>
  public short ValueOrigin;
  /// <summary>新的值</summary>
  public short ValueNew;
}
