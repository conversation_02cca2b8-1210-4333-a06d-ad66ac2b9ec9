﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.XINJE.XinJESeries
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.XINJE;

/// <summary>信捷PLC的不同系列的枚举</summary>
public enum XinJESeries
{
  /// <summary>XC系列</summary>
  XC,
  /// <summary>XD系列</summary>
  XD,
  /// <summary>XL系列</summary>
  XL,
}
