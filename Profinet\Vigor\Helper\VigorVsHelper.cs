﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Vigor.Helper.VigorVsHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Address;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Vigor.Helper;

/// <summary>丰炜PLC的辅助类对象</summary>
public class VigorVsHelper
{
  internal static byte[] PackCommand(byte[] command, byte code = 2)
  {
    if (command == null)
      command = new byte[0];
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 16 /*0x10*/);
    memoryStream.WriteByte(code);
    int num = 0;
    for (int index = 0; index < command.Length; ++index)
    {
      num += (int) command[index];
      memoryStream.WriteByte(command[index]);
      if (command[index] == (byte) 16 /*0x10*/)
        memoryStream.WriteByte(command[index]);
    }
    memoryStream.WriteByte((byte) 16 /*0x10*/);
    memoryStream.WriteByte((byte) 3);
    byte[] bytes = Encoding.ASCII.GetBytes((num % 256 /*0x0100*/).ToString("X2"));
    memoryStream.WriteByte(bytes[0]);
    memoryStream.WriteByte(bytes[1]);
    return memoryStream.ToArray();
  }

  internal static byte[] UnPackCommand(byte[] command)
  {
    if (command == null)
      command = new byte[0];
    MemoryStream memoryStream = new MemoryStream();
    for (int index = 0; index < command.Length; ++index)
    {
      memoryStream.WriteByte(command[index]);
      if (command[index] == (byte) 16 /*0x10*/ && index + 1 < command.Length && command[index + 1] == (byte) 16 /*0x10*/)
        ++index;
    }
    return memoryStream.ToArray();
  }

  internal static bool CheckReceiveDataComplete(byte[] buffer, int length)
  {
    int num = 0;
    if (length < 10)
      return false;
    for (int index = 0; index < length; ++index)
    {
      if (buffer[index] == (byte) 16 /*0x10*/ && index + 1 < length)
      {
        if (buffer[index + 1] == (byte) 16 /*0x10*/)
          ++index;
        else if (buffer[index + 1] == (byte) 3)
        {
          num = index;
          break;
        }
      }
    }
    return num == length - 4;
  }

  private static byte[] GetBytesAddress(int address)
  {
    string str = address.ToString("D6");
    if (str.Length > 6)
      str = str.Substring(6);
    return str.ToHexBytes();
  }

  /// <summary>
  /// 构建读取的报文命令，对于字地址，单次最多读取64字节，支持D,SD,R,T,C的数据读取，对于位地址，最多读取1024位，支持X,Y,M,SM,S,TS(定时器触点),TC（定时器线圈）,CS(计数器触点),CC（计数器线圈）<br />
  /// Construct a read message command. For word addresses, up to 64 bytes can be read at a time, and data reading of D, SD, R, T, and C is supported. For bit addresses,
  /// up to 1024 bits are read, and X, Y are supported. , M, SM, S, TS (timer contact), TC (timer coil), CS (counter contact), CC (counter coil)
  /// </summary>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的数据地址</param>
  /// <param name="length">读取的长度</param>
  /// <param name="isBool">是否进行位读取</param>
  /// <returns>完整的读取的报文信息</returns>
  public static OperateResult<List<byte[]>> BuildReadCommand(
    byte station,
    string address,
    ushort length,
    bool isBool)
  {
    OperateResult<VigorAddress> from = VigorAddress.ParseFrom(address, length, isBool);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from);
    int[] array = SoftBasic.SplitIntegerToArray((int) length, isBool ? 1024 /*0x0400*/ : (from.Content.DataCode == (byte) 173 ? 32 /*0x20*/ : 64 /*0x40*/));
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      byte[] bytesAddress = VigorVsHelper.GetBytesAddress(from.Content.AddressStart);
      byte[] command = new byte[10]
      {
        station,
        (byte) 7,
        (byte) 0,
        isBool ? (byte) 33 : (byte) 32 /*0x20*/,
        from.Content.DataCode,
        bytesAddress[2],
        bytesAddress[1],
        bytesAddress[0],
        BitConverter.GetBytes(array[index])[0],
        BitConverter.GetBytes(array[index])[1]
      };
      numArrayList.Add(VigorVsHelper.PackCommand(command));
      from.Content.AddressStart += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>
  /// 构建以字单位写入的报文，单次最多写入64个word，地址支持 D,SD,R,T,C，对于C200~C255,是属于32位的计数器<br />
  /// Construct a message written in word units, and write up to 64 words in a single time. The address supports D, SD, R, T, C. For C200~C255, it is a 32-bit counter
  /// </summary>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址</param>
  /// <param name="value">写入的原始数据</param>
  /// <returns>写入命令的完整报文</returns>
  public static OperateResult<byte[]> BuildWriteWordCommand(
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<VigorAddress> from = VigorAddress.ParseFrom(address, (ushort) 1, false);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    byte[] bytesAddress = VigorVsHelper.GetBytesAddress(from.Content.AddressStart);
    byte[] command = new byte[10 + value.Length];
    command[0] = station;
    command[1] = BitConverter.GetBytes(7 + value.Length)[0];
    command[2] = BitConverter.GetBytes(7 + value.Length)[1];
    command[3] = (byte) 40;
    command[4] = from.Content.DataCode;
    command[5] = bytesAddress[2];
    command[6] = bytesAddress[1];
    command[7] = bytesAddress[0];
    if (from.Content.DataCode == (byte) 173)
    {
      command[8] = BitConverter.GetBytes(value.Length / 4)[0];
      command[9] = BitConverter.GetBytes(value.Length / 4)[1];
    }
    else
    {
      command[8] = BitConverter.GetBytes(value.Length / 2)[0];
      command[9] = BitConverter.GetBytes(value.Length / 2)[1];
    }
    value.CopyTo((Array) command, 10);
    return OperateResult.CreateSuccessResult<byte[]>(VigorVsHelper.PackCommand(command));
  }

  /// <summary>
  /// 构建以位单位写入的报文，单次最多写入1024bit，支持X,Y,M,SM,S,TS(定时器触点),TC（定时器线圈）,CS(计数器触点),CC（计数器线圈）
  /// </summary>
  /// <param name="station">站号信息</param>
  /// <param name="address">PLC的地址</param>
  /// <param name="value">等待写入的bool数组</param>
  /// <returns>写入位数据的完整报文信息</returns>
  public static OperateResult<byte[]> BuildWriteBoolCommand(
    byte station,
    string address,
    bool[] value)
  {
    OperateResult<VigorAddress> from = VigorAddress.ParseFrom(address, (ushort) 1, true);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    byte[] bytesAddress = VigorVsHelper.GetBytesAddress(from.Content.AddressStart);
    byte[] byteArray = value.ToByteArray();
    byte[] command = new byte[10 + byteArray.Length];
    command[0] = station;
    command[1] = BitConverter.GetBytes(7 + byteArray.Length)[0];
    command[2] = BitConverter.GetBytes(7 + byteArray.Length)[1];
    command[3] = (byte) 41;
    command[4] = from.Content.DataCode;
    command[5] = bytesAddress[2];
    command[6] = bytesAddress[1];
    command[7] = bytesAddress[0];
    command[8] = BitConverter.GetBytes(value.Length)[0];
    command[9] = BitConverter.GetBytes(value.Length)[1];
    byteArray.CopyTo((Array) command, 10);
    return OperateResult.CreateSuccessResult<byte[]>(VigorVsHelper.PackCommand(command));
  }

  /// <summary>检查从PLC返回的报文是否正确，以及提取出正确的结果数据</summary>
  /// <param name="response">PLC返回的报文</param>
  /// <returns>提取的结果数据内容</returns>
  public static OperateResult<byte[]> CheckResponseContent(byte[] response)
  {
    response = VigorVsHelper.UnPackCommand(response);
    if (response.Length < 6)
      return new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataLengthTooShort} Source: {response.ToHexString(' ')}");
    if (response[5] > (byte) 0)
      return new OperateResult<byte[]>((int) response[5], $"{VigorVsHelper.GetErrorText(response[5])} Source: {response.ToHexString(' ')}");
    try
    {
      int uint16 = (int) BitConverter.ToUInt16(response, 3);
      if (uint16 + 9 != response.Length)
        return new OperateResult<byte[]>((int) response[5], "Length check failed, Source: " + response.ToHexString(' '));
      return uint16 == 1 ? OperateResult.CreateSuccessResult<byte[]>(new byte[0]) : OperateResult.CreateSuccessResult<byte[]>(response.SelectMiddle<byte>(6, uint16 - 1));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"CheckResponseContent failed: {ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
    }
  }

  internal static string GetErrorText(byte status)
  {
    switch (status)
    {
      case 2:
        return StringResources.Language.Vigor02;
      case 4:
        return StringResources.Language.Vigor04;
      case 6:
        return StringResources.Language.Vigor06;
      case 8:
        return StringResources.Language.Vigor08;
      case 49:
        return StringResources.Language.Vigor31;
      default:
        return StringResources.Language.UnknownError;
    }
  }
}
