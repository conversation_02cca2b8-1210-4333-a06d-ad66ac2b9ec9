﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.VersionInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>版本信息类，用于展示版本发布信息</summary>
public sealed class VersionInfo
{
  /// <summary>版本的发行日期</summary>
  public DateTime ReleaseDate { get; set; } = DateTime.Now;

  /// <summary>版本的更新细节</summary>
  public StringBuilder UpdateDetails { get; set; } = new StringBuilder();

  /// <summary>版本号</summary>
  public SystemVersion VersionNum { get; set; } = new SystemVersion(1, 0, 0);

  /// <inheritdoc />
  public override string ToString() => this.VersionNum.ToString();
}
