﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Helper.Secs2
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Secs.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Secs.Helper;

/// <summary>Secs2相关的规则</summary>
public class Secs2
{
  /// <summary>列表的类型信息</summary>
  public const int TypeList = 0;
  /// <summary>ASCII字符串的信息</summary>
  public const int TypeASCII = 64 /*0x40*/;
  /// <summary>有符号的1个字节长度的整型</summary>
  public const int TypeSByte = 100;
  /// <summary>无符号的1个字节长度的整型</summary>
  public const int TypeByte = 164;
  /// <summary>有符号的2个字节长度的整型</summary>
  public const int TypeInt16 = 104;
  /// <summary>无符号的2个字节长度的整型</summary>
  public const int TypeUInt16 = 168;
  /// <summary>有符号的4个字节长度的整型</summary>
  public const int TypeInt32 = 112 /*0x70*/;
  /// <summary>无符号的4个字节长度的整型</summary>
  public const int TypeUInt32 = 176 /*0xB0*/;
  /// <summary>有符号的8个字节长度的整型</summary>
  public const int TypeInt64 = 96 /*0x60*/;
  /// <summary>无符号的8个字节长度的整型</summary>
  public const int TypeUInt64 = 160 /*0xA0*/;
  /// <summary>单浮点精度的类型</summary>
  public const int TypeSingle = 144 /*0x90*/;
  /// <summary>双浮点精度的类型</summary>
  public const int TypeDouble = 128 /*0x80*/;
  /// <summary>Bool值信息</summary>
  public const int TypeBool = 36;
  /// <summary>二进制的数据信息</summary>
  public const int TypeBinary = 32 /*0x20*/;
  /// <summary>JIS8类型的数据</summary>
  public const int TypeJIS8 = 68;
  /// <summary>SECS的字节顺序信息</summary>
  public static IByteTransform SecsTransform = (IByteTransform) new ReverseBytesTransform();

  private static int GetMaxLength(byte[] buffer, int index, int length)
  {
    return index + length <= buffer.Length ? length : buffer.Length - index;
  }

  internal static SecsValue ExtraToSecsItemValue(
    IByteTransform byteTransform,
    byte[] buffer,
    ref int index,
    Encoding encoding)
  {
    if (index >= buffer.Length)
      return new SecsValue();
    int num1 = (int) buffer[index] & 3;
    int num2 = (int) buffer[index] & 252;
    int length = 0;
    switch (num1)
    {
      case 1:
        length = (int) buffer[index + 1];
        index += 2;
        break;
      case 2:
        length = (int) buffer[index + 1] * 256 /*0x0100*/ + (int) buffer[index + 2];
        index += 3;
        break;
      case 3:
        length = (int) buffer[index + 1] * 65536 /*0x010000*/ + (int) buffer[index + 2] * 256 /*0x0100*/ + (int) buffer[index + 3];
        index += 4;
        break;
      default:
        ++index;
        break;
    }
    if (num2 == 0)
    {
      SecsValue[] secsValueArray = new SecsValue[length];
      for (int index1 = 0; index1 < secsValueArray.Length; ++index1)
        secsValueArray[index1] = Secs2.ExtraToSecsItemValue(byteTransform, buffer, ref index, encoding);
      return new SecsValue(SecsItemType.List, (object) secsValueArray);
    }
    int index2 = index;
    index += length;
    switch (num2)
    {
      case 32 /*0x20*/:
        return new SecsValue(SecsItemType.Binary, (object) buffer.SelectMiddle<byte>(index2, length));
      case 36:
        return new SecsValue(SecsItemType.Bool, length == 1 ? (object) (buffer[index2] > (byte) 0) : (object) ((IEnumerable<byte>) buffer.SelectMiddle<byte>(index2, length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
      case 64 /*0x40*/:
        return new SecsValue(SecsItemType.ASCII, (object) encoding.GetString(buffer, index2, Secs2.GetMaxLength(buffer, index2, length)), length);
      case 68:
        return new SecsValue(SecsItemType.JIS8, (object) buffer.SelectMiddle<byte>(index2, length));
      case 96 /*0x60*/:
        return new SecsValue(SecsItemType.Int64, length == 8 ? (object) byteTransform.TransInt64(buffer, index2) : (object) byteTransform.TransInt64(buffer, index2, length / 8));
      case 100:
        return new SecsValue(SecsItemType.SByte, length == 1 ? (object) (sbyte) buffer[index2] : (object) ((IEnumerable<byte>) buffer.SelectMiddle<byte>(index2, length)).Select<byte, sbyte>((Func<byte, sbyte>) (m => (sbyte) m)).ToArray<sbyte>());
      case 104:
        return new SecsValue(SecsItemType.Int16, length == 2 ? (object) byteTransform.TransInt16(buffer, index2) : (object) byteTransform.TransInt16(buffer, index2, length / 2));
      case 112 /*0x70*/:
        return new SecsValue(SecsItemType.Int32, length == 4 ? (object) byteTransform.TransInt32(buffer, index2) : (object) byteTransform.TransInt32(buffer, index2, length / 4));
      case 128 /*0x80*/:
        return new SecsValue(SecsItemType.Double, length == 8 ? (object) byteTransform.TransDouble(buffer, index2) : (object) byteTransform.TransDouble(buffer, index2, length / 8));
      case 144 /*0x90*/:
        return new SecsValue(SecsItemType.Single, length == 4 ? (object) byteTransform.TransSingle(buffer, index2) : (object) byteTransform.TransSingle(buffer, index2, length / 4));
      case 160 /*0xA0*/:
        return new SecsValue(SecsItemType.UInt64, length == 8 ? (object) byteTransform.TransUInt64(buffer, index2) : (object) byteTransform.TransUInt64(buffer, index2, length / 8));
      case 164:
        return new SecsValue(SecsItemType.Byte, length == 1 ? (object) buffer[index2] : (object) buffer.SelectMiddle<byte>(index2, length));
      case 168:
        return new SecsValue(SecsItemType.UInt16, length == 2 ? (object) byteTransform.TransUInt16(buffer, index2) : (object) byteTransform.TransUInt16(buffer, index2, length / 2));
      case 176 /*0xB0*/:
        return new SecsValue(SecsItemType.UInt32, length == 4 ? (object) byteTransform.TransUInt32(buffer, index2) : (object) byteTransform.TransUInt32(buffer, index2, length / 4));
      default:
        return (SecsValue) null;
    }
  }

  internal static int GetTypeCodeFrom(SecsItemType type)
  {
    switch (type)
    {
      case SecsItemType.Bool:
        return 36;
      case SecsItemType.Binary:
        return 32 /*0x20*/;
      case SecsItemType.ASCII:
        return 64 /*0x40*/;
      case SecsItemType.JIS8:
        return 68;
      case SecsItemType.SByte:
        return 100;
      case SecsItemType.Byte:
        return 164;
      case SecsItemType.Int16:
        return 104;
      case SecsItemType.UInt16:
        return 168;
      case SecsItemType.Int32:
        return 112 /*0x70*/;
      case SecsItemType.UInt32:
        return 176 /*0xB0*/;
      case SecsItemType.Int64:
        return 96 /*0x60*/;
      case SecsItemType.UInt64:
        return 160 /*0xA0*/;
      case SecsItemType.Single:
        return 144 /*0x90*/;
      case SecsItemType.Double:
        return 128 /*0x80*/;
      default:
        return 0;
    }
  }

  internal static void AddCodeSource(List<byte> bytes, SecsItemType type, int length)
  {
    int typeCodeFrom = Secs2.GetTypeCodeFrom(type);
    if (length < 256 /*0x0100*/)
    {
      bytes.Add((byte) (typeCodeFrom | 1));
      bytes.Add((byte) length);
    }
    else if (length < 65536 /*0x010000*/)
    {
      byte[] bytes1 = BitConverter.GetBytes(length);
      bytes.Add((byte) (typeCodeFrom | 2));
      bytes.Add(bytes1[1]);
      bytes.Add(bytes1[0]);
    }
    else
    {
      byte[] bytes2 = BitConverter.GetBytes(length);
      bytes.Add((byte) (typeCodeFrom | 3));
      bytes.Add(bytes2[2]);
      bytes.Add(bytes2[1]);
      bytes.Add(bytes2[0]);
    }
  }

  internal static void AddCodeAndValueSource(List<byte> bytes, SecsValue value, Encoding encoding)
  {
    if (value == null)
      return;
    if (value.ItemType == SecsItemType.List)
    {
      int length = !(value.Value is IEnumerable<SecsValue> source) ? 0 : source.Count<SecsValue>();
      Secs2.AddCodeSource(bytes, value.ItemType, length);
    }
    else
    {
      byte[] collection;
      switch (value.ItemType)
      {
        case SecsItemType.Bool:
          byte[] numArray1;
          if (!(value.Value.GetType() == typeof (bool)))
            numArray1 = ((IEnumerable<bool>) (bool[]) value.Value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : byte.MaxValue)).ToArray<byte>();
          else if (!(bool) value.Value)
            numArray1 = new byte[1];
          else
            numArray1 = new byte[1]{ byte.MaxValue };
          collection = numArray1;
          break;
        case SecsItemType.Binary:
          collection = (byte[]) value.Value;
          break;
        case SecsItemType.ASCII:
          collection = encoding.GetBytes(value.Value.ToString());
          break;
        case SecsItemType.JIS8:
          collection = (byte[]) value.Value;
          break;
        case SecsItemType.SByte:
          byte[] numArray2;
          if (!(value.Value.GetType() == typeof (sbyte)))
            numArray2 = ((IEnumerable<sbyte>) (sbyte[]) value.Value).Select<sbyte, byte>((Func<sbyte, byte>) (m => (byte) m)).ToArray<byte>();
          else
            numArray2 = new byte[1]{ (byte) value.Value };
          collection = numArray2;
          break;
        case SecsItemType.Byte:
          byte[] numArray3;
          if (!(value.Value.GetType() == typeof (byte)))
            numArray3 = (byte[]) value.Value;
          else
            numArray3 = new byte[1]{ (byte) value.Value };
          collection = numArray3;
          break;
        case SecsItemType.Int16:
          collection = value.Value.GetType() == typeof (short) ? Secs2.SecsTransform.TransByte((short) value.Value) : Secs2.SecsTransform.TransByte((short[]) value.Value);
          break;
        case SecsItemType.UInt16:
          collection = value.Value.GetType() == typeof (ushort) ? Secs2.SecsTransform.TransByte((ushort) value.Value) : Secs2.SecsTransform.TransByte((ushort[]) value.Value);
          break;
        case SecsItemType.Int32:
          collection = value.Value.GetType() == typeof (int) ? Secs2.SecsTransform.TransByte((int) value.Value) : Secs2.SecsTransform.TransByte((int[]) value.Value);
          break;
        case SecsItemType.UInt32:
          collection = value.Value.GetType() == typeof (uint) ? Secs2.SecsTransform.TransByte((uint) value.Value) : Secs2.SecsTransform.TransByte((uint[]) value.Value);
          break;
        case SecsItemType.Int64:
          collection = value.Value.GetType() == typeof (long) ? Secs2.SecsTransform.TransByte((long) value.Value) : Secs2.SecsTransform.TransByte((long[]) value.Value);
          break;
        case SecsItemType.UInt64:
          collection = value.Value.GetType() == typeof (ulong) ? Secs2.SecsTransform.TransByte((ulong) value.Value) : Secs2.SecsTransform.TransByte((ulong[]) value.Value);
          break;
        case SecsItemType.Single:
          collection = value.Value.GetType() == typeof (float) ? Secs2.SecsTransform.TransByte((float) value.Value) : Secs2.SecsTransform.TransByte((float[]) value.Value);
          break;
        case SecsItemType.Double:
          collection = value.Value.GetType() == typeof (double) ? Secs2.SecsTransform.TransByte((double) value.Value) : Secs2.SecsTransform.TransByte((double[]) value.Value);
          break;
        case SecsItemType.None:
          collection = new byte[0];
          break;
        default:
          collection = (byte[]) value.Value;
          break;
      }
      Secs2.AddCodeSource(bytes, value.ItemType, collection.Length);
      bytes.AddRange((IEnumerable<byte>) collection);
    }
  }

  /// <summary>将返回的数据内容解析为实际的字符串信息，根据secsⅡ 协议定义的规则解析出实际的数据信息</summary>
  /// <param name="buffer">原始的字节数据内容</param>
  /// <param name="encoding">字符串对象的编码信息</param>
  /// <returns>字符串消息</returns>
  public static SecsValue ExtraToSecsItemValue(byte[] buffer, Encoding encoding)
  {
    if (buffer == null)
      return (SecsValue) null;
    int index = 0;
    return Secs2.ExtraToSecsItemValue(Secs2.SecsTransform, buffer, ref index, encoding);
  }
}
