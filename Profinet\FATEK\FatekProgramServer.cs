﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.FATEK.FatekProgramServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.FATEK;

/// <summary>
/// 永宏编程口协议的虚拟PLC，可以用来和<see cref="T:HslCommunication.Profinet.FATEK.FatekProgram" />及<see cref="T:HslCommunication.Profinet.FATEK.FatekProgramOverTcp" />类做通信测试，支持简单数据的读写操作。<br />
/// The virtual PLC of Yonghong programming port protocol can be used to communicate with <see cref="T:HslCommunication.Profinet.FATEK.FatekProgram" /> and <see cref="T:HslCommunication.Profinet.FATEK.FatekProgramOverTcp" /> class,
/// and support simple data read and write operations.
/// </summary>
public class FatekProgramServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer sBuffer;
  private SoftBuffer tBuffer;
  private SoftBuffer cBuffer;
  private SoftBuffer tmrBuffer;
  private SoftBuffer ctrBuffer;
  private SoftBuffer hrBuffer;
  private SoftBuffer drBuffer;
  private byte station = 1;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private bool run = false;

  /// <summary>
  /// 实例化一个基于Programe协议的虚拟的永宏PLC对象，可以用来和<see cref="T:HslCommunication.Profinet.FATEK.FatekProgram" />进行通信测试。
  /// </summary>
  public FatekProgramServer()
  {
    this.xBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.yBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.sBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.tBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.cBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.tmrBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ctrBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.hrBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.drBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.LogMsgFormatBinary = false;
    this.WordLength = (ushort) 1;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.FATEK.FatekProgram.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] bytes = new byte[917504 /*0x0E0000*/];
    this.xBuffer.GetBytes().CopyTo((Array) bytes, 0);
    this.yBuffer.GetBytes().CopyTo((Array) bytes, 65536 /*0x010000*/);
    this.mBuffer.GetBytes().CopyTo((Array) bytes, 131072 /*0x020000*/);
    this.sBuffer.GetBytes().CopyTo((Array) bytes, 196608 /*0x030000*/);
    this.tBuffer.GetBytes().CopyTo((Array) bytes, 262144 /*0x040000*/);
    this.cBuffer.GetBytes().CopyTo((Array) bytes, 327680 /*0x050000*/);
    this.tmrBuffer.GetBytes().CopyTo((Array) bytes, 393216 /*0x060000*/);
    this.ctrBuffer.GetBytes().CopyTo((Array) bytes, 524288 /*0x080000*/);
    this.hrBuffer.GetBytes().CopyTo((Array) bytes, 655360 /*0x0A0000*/);
    this.drBuffer.GetBytes().CopyTo((Array) bytes, 786432 /*0x0C0000*/);
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 917504 /*0x0E0000*/)
      throw new Exception("File is not correct");
    this.xBuffer.SetBytes(content, 0, 65536 /*0x010000*/);
    this.yBuffer.SetBytes(content, 65536 /*0x010000*/, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 131072 /*0x020000*/, 65536 /*0x010000*/);
    this.sBuffer.SetBytes(content, 196608 /*0x030000*/, 65536 /*0x010000*/);
    this.tBuffer.SetBytes(content, 262144 /*0x040000*/, 65536 /*0x010000*/);
    this.cBuffer.SetBytes(content, 327680 /*0x050000*/, 65536 /*0x010000*/);
    this.tmrBuffer.SetBytes(content, 393216 /*0x060000*/, 131072 /*0x020000*/);
    this.ctrBuffer.SetBytes(content, 524288 /*0x080000*/, 131072 /*0x020000*/);
    this.hrBuffer.SetBytes(content, 655360 /*0x0A0000*/, 131072 /*0x020000*/);
    this.drBuffer.SetBytes(content, 786432 /*0x0C0000*/, 131072 /*0x020000*/);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return from.ConvertFailed<byte[]>();
    if (from.Content.DataCode == "D")
      return OperateResult.CreateSuccessResult<byte[]>(this.drBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
    if (from.Content.DataCode == "R")
      return OperateResult.CreateSuccessResult<byte[]>(this.hrBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
    if (from.Content.DataCode == "RT")
      return OperateResult.CreateSuccessResult<byte[]>(this.tmrBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
    if (from.Content.DataCode == "CT")
      return OperateResult.CreateSuccessResult<byte[]>(this.ctrBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
    if (from.Content.DataCode == "X")
      return OperateResult.CreateSuccessResult<byte[]>(this.xBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.DataCode == "Y")
      return OperateResult.CreateSuccessResult<byte[]>(this.yBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.DataCode == "M")
      return OperateResult.CreateSuccessResult<byte[]>(this.mBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.DataCode == "S")
      return OperateResult.CreateSuccessResult<byte[]>(this.sBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.DataCode == "T")
      return OperateResult.CreateSuccessResult<byte[]>(this.tBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    return from.Content.DataCode == "C" ? OperateResult.CreateSuccessResult<byte[]>(this.cBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray()) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return (OperateResult) from.ConvertFailed<byte[]>();
    if (from.Content.DataCode == "D")
      this.drBuffer.SetBytes(value, from.Content.AddressStart * 2);
    else if (from.Content.DataCode == "R")
      this.hrBuffer.SetBytes(value, from.Content.AddressStart * 2);
    else if (from.Content.DataCode == "RT")
      this.tmrBuffer.SetBytes(value, from.Content.AddressStart * 2);
    else if (from.Content.DataCode == "CT")
      this.ctrBuffer.SetBytes(value, from.Content.AddressStart * 2);
    else if (from.Content.DataCode == "X")
      this.xBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.DataCode == "Y")
      this.yBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.DataCode == "M")
      this.mBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.DataCode == "S")
      this.sBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.DataCode == "T")
    {
      this.tBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    }
    else
    {
      if (!(from.Content.DataCode == "C"))
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      this.cBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return from.ConvertFailed<bool[]>();
    if (from.Content.DataCode == "X")
      return OperateResult.CreateSuccessResult<bool[]>(this.xBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.DataCode == "Y")
      return OperateResult.CreateSuccessResult<bool[]>(this.yBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.DataCode == "M")
      return OperateResult.CreateSuccessResult<bool[]>(this.mBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.DataCode == "S")
      return OperateResult.CreateSuccessResult<bool[]>(this.sBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.DataCode == "T")
      return OperateResult.CreateSuccessResult<bool[]>(this.tBuffer.GetBool(from.Content.AddressStart, (int) length));
    return from.Content.DataCode == "C" ? OperateResult.CreateSuccessResult<bool[]>(this.cBuffer.GetBool(from.Content.AddressStart, (int) length)) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<FatekProgramAddress> from = FatekProgramAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return (OperateResult) from.ConvertFailed<byte[]>();
    if (from.Content.DataCode == "X")
      this.xBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.DataCode == "Y")
      this.yBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.DataCode == "M")
      this.mBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.DataCode == "S")
      this.sBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.DataCode == "T")
    {
      this.tBuffer.SetBool(value, from.Content.AddressStart);
    }
    else
    {
      if (!(from.Content.DataCode == "C"))
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      this.cBuffer.SetBool(value, from.Content.AddressStart);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 3);
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength >= 5 && buffer[receivedLength - 1] == (byte) 3;
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    byte num = Convert.ToByte(Encoding.ASCII.GetString(receive, 1, 2), 16 /*0x10*/);
    if ((int) num != (int) this.Station)
      return new OperateResult<byte[]>($"Station is not match, need [{this.station}] but actual is [{num}]");
    switch (Encoding.ASCII.GetString(receive, 3, 2))
    {
      case "44":
        return OperateResult.CreateSuccessResult<byte[]>(this.ReadBoolByMessage(receive));
      case "45":
        return OperateResult.CreateSuccessResult<byte[]>(this.WriteBoolByMessage(receive));
      case "46":
        return OperateResult.CreateSuccessResult<byte[]>(this.ReadWordByMessage(receive));
      case "47":
        return OperateResult.CreateSuccessResult<byte[]>(this.WriteWordByMessage(receive));
      case "41":
        this.run = receive[5] == (byte) 49;
        return OperateResult.CreateSuccessResult<byte[]>(this.PackResponseBack(receive, (byte) 48 /*0x30*/, (byte[]) null));
      case "40":
        return OperateResult.CreateSuccessResult<byte[]>(this.PackResponseBack(receive, (byte) 48 /*0x30*/, Encoding.ASCII.GetBytes(new byte[3]
        {
          this.run ? (byte) 1 : (byte) 0,
          (byte) 0,
          (byte) 0
        }.ToHexString())));
      default:
        return OperateResult.CreateSuccessResult<byte[]>(this.PackResponseBack(receive, (byte) 52, (byte[]) null));
    }
  }

  private byte[] PackResponseBack(byte[] receive, byte err, byte[] value)
  {
    if (value == null)
      value = new byte[0];
    byte[] buffer = new byte[9 + value.Length];
    buffer[0] = (byte) 2;
    buffer[1] = receive[1];
    buffer[2] = receive[2];
    buffer[3] = receive[3];
    buffer[4] = receive[4];
    buffer[5] = err;
    value.CopyTo((Array) buffer, 6);
    SoftLRC.CalculateAccAndFill(buffer, 0, 3);
    buffer[buffer.Length - 1] = (byte) 3;
    return buffer;
  }

  private SoftBuffer GetBoolBuffer(char code)
  {
    switch (code)
    {
      case 'C':
        return this.cBuffer;
      case 'M':
        return this.mBuffer;
      case 'S':
        return this.sBuffer;
      case 'T':
        return this.tBuffer;
      case 'X':
        return this.xBuffer;
      case 'Y':
        return this.yBuffer;
      default:
        return (SoftBuffer) null;
    }
  }

  private SoftBuffer GetWordBuffer(byte[] receive, out int address)
  {
    if (Encoding.ASCII.GetString(receive, 7, 2) == "RT")
    {
      address = Convert.ToInt32(Encoding.ASCII.GetString(receive, 9, 4));
      return this.tmrBuffer;
    }
    if (Encoding.ASCII.GetString(receive, 7, 2) == "RC")
    {
      address = Convert.ToInt32(Encoding.ASCII.GetString(receive, 9, 4));
      return this.ctrBuffer;
    }
    if (Encoding.ASCII.GetString(receive, 7, 1) == "D")
    {
      address = Convert.ToInt32(Encoding.ASCII.GetString(receive, 8, 5));
      return this.drBuffer;
    }
    if (Encoding.ASCII.GetString(receive, 7, 1) == "R")
    {
      address = Convert.ToInt32(Encoding.ASCII.GetString(receive, 8, 5));
      return this.hrBuffer;
    }
    address = 0;
    return (SoftBuffer) null;
  }

  private byte[] ReadBoolByMessage(byte[] receive)
  {
    int length = Convert.ToInt32(Encoding.ASCII.GetString(receive, 5, 2), 16 /*0x10*/);
    if (length == 0)
      length = 256 /*0x0100*/;
    int int32 = Convert.ToInt32(Encoding.ASCII.GetString(receive, 8, 4), 10);
    SoftBuffer boolBuffer = this.GetBoolBuffer((char) receive[7]);
    return boolBuffer == null ? this.PackResponseBack(receive, (byte) 52, (byte[]) null) : this.PackResponseBack(receive, (byte) 48 /*0x30*/, ((IEnumerable<bool>) boolBuffer.GetBool(int32, length)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
  }

  private byte[] WriteBoolByMessage(byte[] receive)
  {
    int length = Convert.ToInt32(Encoding.ASCII.GetString(receive, 5, 2), 16 /*0x10*/);
    if (length == 0)
      length = 256 /*0x0100*/;
    int int32 = Convert.ToInt32(Encoding.ASCII.GetString(receive, 8, 4), 10);
    SoftBuffer boolBuffer = this.GetBoolBuffer((char) receive[7]);
    if (boolBuffer == null)
      return this.PackResponseBack(receive, (byte) 52, (byte[]) null);
    bool[] array = ((IEnumerable<byte>) receive.SelectMiddle<byte>(12, length)).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)).ToArray<bool>();
    boolBuffer.SetBool(array, int32);
    return this.PackResponseBack(receive, (byte) 48 /*0x30*/, (byte[]) null);
  }

  private byte[] ReadWordByMessage(byte[] receive)
  {
    int int32 = Convert.ToInt32(Encoding.ASCII.GetString(receive, 5, 2), 16 /*0x10*/);
    if (int32 > 64 /*0x40*/)
      return this.PackResponseBack(receive, (byte) 50, (byte[]) null);
    int address;
    SoftBuffer wordBuffer = this.GetWordBuffer(receive, out address);
    return wordBuffer == null ? this.PackResponseBack(receive, (byte) 52, (byte[]) null) : this.PackResponseBack(receive, (byte) 48 /*0x30*/, Encoding.ASCII.GetBytes(wordBuffer.GetBytes(address * 2, int32 * 2).ToHexString()));
  }

  private byte[] WriteWordByMessage(byte[] receive)
  {
    int int32 = Convert.ToInt32(Encoding.ASCII.GetString(receive, 5, 2), 16 /*0x10*/);
    if (int32 > 64 /*0x40*/)
      return this.PackResponseBack(receive, (byte) 50, (byte[]) null);
    int address;
    SoftBuffer wordBuffer = this.GetWordBuffer(receive, out address);
    if (wordBuffer == null)
      return this.PackResponseBack(receive, (byte) 52, (byte[]) null);
    byte[] hexBytes = Encoding.ASCII.GetString(receive, 13, int32 * 4).ToHexBytes();
    wordBuffer.SetBytes(hexBytes, address * 2);
    return this.PackResponseBack(receive, (byte) 48 /*0x30*/, (byte[]) null);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.xBuffer.Dispose();
      this.yBuffer.Dispose();
      this.mBuffer.Dispose();
      this.sBuffer.Dispose();
      this.tBuffer.Dispose();
      this.cBuffer.Dispose();
      this.tmrBuffer.Dispose();
      this.ctrBuffer.Dispose();
      this.hrBuffer.Dispose();
      this.drBuffer.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"FatekProgramServer[{this.Port}]";
}
