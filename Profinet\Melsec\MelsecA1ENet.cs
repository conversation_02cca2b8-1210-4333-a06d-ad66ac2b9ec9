﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecA1ENet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱PLC通讯协议，采用A兼容1E帧协议实现，使用二进制码通讯，请根据实际型号来进行选取<br />
/// Mitsubishi PLC communication protocol, implemented using A compatible 1E frame protocol, using binary code communication, please choose according to the actual model
/// </summary>
/// <remarks>
/// 本类适用于的PLC列表
/// <list type="number">
/// <item>FX3U(C) PLC   测试人sandy_liao</item>
/// </list>
/// <note type="important">本通讯类由CKernal推送，感谢</note>
/// </remarks>
public class MelsecA1ENet : DeviceTcpNet
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public MelsecA1ENet()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>
  /// 指定ip地址和端口来来实例化一个默认的对象<br />
  /// Specify the IP address and port to instantiate a default object
  /// </summary>
  /// <param name="ipAddress">PLC的Ip地址</param>
  /// <param name="port">PLC的端口</param>
  public MelsecA1ENet(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new MelsecA1EBinaryMessage();

  /// <summary>
  /// PLC编号，默认为0xFF<br />
  /// PLC number, default is 0xFF
  /// </summary>
  public byte PLCNumber { get; set; } = byte.MaxValue;

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<List<byte[]>> result1 = MelsecA1ENet.BuildReadCommand(address, length, false, this.PLCNumber);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      OperateResult result3 = MelsecA1ENet.CheckResponseLegal(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result3);
      OperateResult<byte[]> actualData = MelsecA1ENet.ExtractActualData(result2.Content, false);
      if (!actualData.IsSuccess)
        return actualData;
      byteList.AddRange((IEnumerable<byte>) actualData.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = MelsecA1ENet.BuildWriteWordCommand(address, value, this.PLCNumber);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult result = MelsecA1ENet.CheckResponseLegal(operateResult2.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<List<byte[]>> command = MelsecA1ENet.BuildReadCommand(address, length, false, this.PLCNumber);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> array = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      OperateResult check = MelsecA1ENet.CheckResponseLegal(read.Content);
      if (!check.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(check);
      OperateResult<byte[]> extra = MelsecA1ENet.ExtractActualData(read.Content, false);
      if (!extra.IsSuccess)
        return extra;
      array.AddRange((IEnumerable<byte>) extra.Content);
      read = (OperateResult<byte[]>) null;
      check = (OperateResult) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(array.ToArray());
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> command = MelsecA1ENet.BuildWriteWordCommand(address, value, this.PLCNumber);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = MelsecA1ENet.CheckResponseLegal(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>(check);
  }

  /// <summary>
  /// 批量读取<see cref="T:System.Boolean" />数组信息，需要指定地址和长度，地址示例M100，S100，B1A，如果是X,Y, X017就是8进制地址，Y10就是16进制地址。<br />
  /// Batch read <see cref="T:System.Boolean" /> array information, need to specify the address and length, return <see cref="T:System.Boolean" /> array.
  /// Examples of addresses M100, S100, B1A, if it is X, Y, X017 is an octal address, Y10 is a hexadecimal address.
  /// </summary>
  /// <remarks>
  /// 根据协议的规范，最多读取256长度的bool数组信息，如果需要读取更长的bool信息，需要按字为单位进行读取的操作。
  /// </remarks>
  /// <param name="address">数据地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>带有成功标识的byte[]数组</returns>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (address.IndexOf('.') > 0)
      return HslHelper.ReadBool((IReadWriteNet) this, address, length);
    OperateResult<List<byte[]>> result1 = MelsecA1ENet.BuildReadCommand(address, length, true, this.PLCNumber);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<byte> source = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult result3 = MelsecA1ENet.CheckResponseLegal(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(result3);
      OperateResult<byte[]> actualData = MelsecA1ENet.ExtractActualData(result2.Content, true);
      if (!actualData.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) actualData);
      source.AddRange((IEnumerable<byte>) actualData.Content);
    }
    return OperateResult.CreateSuccessResult<bool[]>(source.Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 1)).Take<bool>((int) length).ToArray<bool>());
  }

  /// <summary>
  /// 批量写入<see cref="T:System.Boolean" />数组数据，返回是否成功，地址示例M100，S100，B1A，如果是X,Y, X017就是8进制地址，Y10就是16进制地址。<br />
  /// Batch write <see cref="T:System.Boolean" /> array data, return whether the write was successful.
  /// Examples of addresses M100, S100, B1A, if it is X, Y, X017 is an octal address, Y10 is a hexadecimal address.
  /// </summary>
  /// <param name="address">起始地址</param>
  /// <param name="value">写入值</param>
  /// <returns>带有成功标识的结果类对象</returns>
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<byte[]> operateResult1 = MelsecA1ENet.BuildWriteBoolCommand(address, value, this.PLCNumber);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : MelsecA1ENet.CheckResponseLegal(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1ENet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (address.IndexOf('.') > 0)
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) this, address, length);
      return operateResult;
    }
    OperateResult<List<byte[]>> command = MelsecA1ENet.BuildReadCommand(address, length, true, this.PLCNumber);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<byte> array = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult check = MelsecA1ENet.CheckResponseLegal(read.Content);
      if (!check.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(check);
      OperateResult<byte[]> extract = MelsecA1ENet.ExtractActualData(read.Content, true);
      if (!extract.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) extract);
      array.AddRange((IEnumerable<byte>) extract.Content);
      read = (OperateResult<byte[]>) null;
      check = (OperateResult) null;
      extract = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(array.Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 1)).Take<bool>((int) length).ToArray<bool>());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecA1ENet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] values)
  {
    OperateResult<byte[]> command = MelsecA1ENet.BuildWriteBoolCommand(address, values, this.PLCNumber);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? MelsecA1ENet.CheckResponseLegal(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecA1ENet[{this.IpAddress}:{this.Port}]";

  /// <summary>根据类型地址长度确认需要读取的指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">长度</param>
  /// <param name="isBit">指示是否按照位成批的读出</param>
  /// <param name="plcNumber">PLC编号</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<List<byte[]>> BuildReadCommand(
    string address,
    ushort length,
    bool isBit,
    byte plcNumber)
  {
    OperateResult<MelsecA1EDataType, int> result = MelsecHelper.McA1EAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) result);
    byte num1 = isBit ? (byte) 0 : (byte) 1;
    int[] array = SoftBasic.SplitIntegerToArray((int) length, isBit ? 256 /*0x0100*/ : 64 /*0x40*/);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      byte[] numArray = new byte[12]
      {
        num1,
        plcNumber,
        (byte) 10,
        (byte) 0,
        BitConverter.GetBytes(result.Content2)[0],
        BitConverter.GetBytes(result.Content2)[1],
        BitConverter.GetBytes(result.Content2)[2],
        BitConverter.GetBytes(result.Content2)[3],
        BitConverter.GetBytes(result.Content1.DataCode)[0],
        BitConverter.GetBytes(result.Content1.DataCode)[1],
        (byte) 0,
        (byte) 0
      };
      int num2 = array[index];
      if (num2 == 256 /*0x0100*/)
        num2 = 0;
      numArray[10] = BitConverter.GetBytes(num2)[0];
      numArray[11] = BitConverter.GetBytes(num2)[1];
      numArrayList.Add(numArray);
      result.Content2 += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>根据类型地址以及需要写入的数据来生成指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="value">数据值</param>
  /// <param name="plcNumber">PLC编号</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<byte[]> BuildWriteWordCommand(
    string address,
    byte[] value,
    byte plcNumber)
  {
    OperateResult<MelsecA1EDataType, int> result = MelsecHelper.McA1EAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    byte[] destinationArray = new byte[12 + value.Length];
    destinationArray[0] = (byte) 3;
    destinationArray[1] = plcNumber;
    destinationArray[2] = (byte) 10;
    destinationArray[3] = (byte) 0;
    destinationArray[4] = BitConverter.GetBytes(result.Content2)[0];
    destinationArray[5] = BitConverter.GetBytes(result.Content2)[1];
    destinationArray[6] = BitConverter.GetBytes(result.Content2)[2];
    destinationArray[7] = BitConverter.GetBytes(result.Content2)[3];
    destinationArray[8] = BitConverter.GetBytes(result.Content1.DataCode)[0];
    destinationArray[9] = BitConverter.GetBytes(result.Content1.DataCode)[1];
    destinationArray[10] = BitConverter.GetBytes(value.Length / 2)[0];
    destinationArray[11] = BitConverter.GetBytes(value.Length / 2)[1];
    Array.Copy((Array) value, 0, (Array) destinationArray, 12, value.Length);
    return OperateResult.CreateSuccessResult<byte[]>(destinationArray);
  }

  /// <summary>根据类型地址以及需要写入的数据来生成指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="value">数据值</param>
  /// <param name="plcNumber">PLC编号</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<byte[]> BuildWriteBoolCommand(
    string address,
    bool[] value,
    byte plcNumber)
  {
    OperateResult<MelsecA1EDataType, int> result = MelsecHelper.McA1EAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    byte[] byteData = MelsecHelper.TransBoolArrayToByteData(value);
    byte[] destinationArray = new byte[12 + byteData.Length];
    destinationArray[0] = (byte) 2;
    destinationArray[1] = plcNumber;
    destinationArray[2] = (byte) 10;
    destinationArray[3] = (byte) 0;
    destinationArray[4] = BitConverter.GetBytes(result.Content2)[0];
    destinationArray[5] = BitConverter.GetBytes(result.Content2)[1];
    destinationArray[6] = BitConverter.GetBytes(result.Content2)[2];
    destinationArray[7] = BitConverter.GetBytes(result.Content2)[3];
    destinationArray[8] = BitConverter.GetBytes(result.Content1.DataCode)[0];
    destinationArray[9] = BitConverter.GetBytes(result.Content1.DataCode)[1];
    destinationArray[10] = BitConverter.GetBytes(value.Length)[0];
    destinationArray[11] = BitConverter.GetBytes(value.Length)[1];
    Array.Copy((Array) byteData, 0, (Array) destinationArray, 12, byteData.Length);
    return OperateResult.CreateSuccessResult<byte[]>(destinationArray);
  }

  /// <summary>检测反馈的消息是否合法</summary>
  /// <param name="response">接收的报文</param>
  /// <returns>是否成功</returns>
  public static OperateResult CheckResponseLegal(byte[] response)
  {
    if (response.Length < 2)
      return new OperateResult(StringResources.Language.ReceiveDataLengthTooShort);
    if (response[1] == (byte) 0)
      return OperateResult.CreateSuccessResult();
    return response[1] == (byte) 91 ? new OperateResult((int) response[2], StringResources.Language.MelsecPleaseReferToManualDocument) : new OperateResult((int) response[1], StringResources.Language.MelsecPleaseReferToManualDocument);
  }

  /// <summary>从PLC反馈的数据中提取出实际的数据内容，需要传入反馈数据，是否位读取</summary>
  /// <param name="response">反馈的数据内容</param>
  /// <param name="isBit">是否位读取</param>
  /// <returns>解析后的结果对象</returns>
  public static OperateResult<byte[]> ExtractActualData(byte[] response, bool isBit)
  {
    if (!isBit)
      return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(2));
    byte[] numArray = new byte[(response.Length - 2) * 2];
    for (int index = 2; index < response.Length; ++index)
    {
      if (((int) response[index] & 16 /*0x10*/) == 16 /*0x10*/)
        numArray[(index - 2) * 2] = (byte) 1;
      if (((int) response[index] & 1) == 1)
        numArray[(index - 2) * 2 + 1] = (byte) 1;
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }
}
