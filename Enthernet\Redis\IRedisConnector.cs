﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.Redis.IRedisConnector
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Algorithms.ConnectPool;
using System;

#nullable disable
namespace HslCommunication.Enthernet.Redis;

/// <summary>
/// 关于Redis实现的接口<see cref="T:HslCommunication.Algorithms.ConnectPool.IConnector" />，从而实现了数据连接池的操作信息
/// </summary>
public class IRedisConnector : IConnector
{
  /// <inheritdoc cref="P:HslCommunication.Algorithms.ConnectPool.IConnector.IsConnectUsing" />
  public bool IsConnectUsing { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Algorithms.ConnectPool.IConnector.GuidToken" />
  public string GuidToken { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Algorithms.ConnectPool.IConnector.LastUseTime" />
  public DateTime LastUseTime { get; set; }

  /// <summary>Redis的连接对象</summary>
  public RedisClient Redis { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Algorithms.ConnectPool.IConnector.Close" />
  public void Close() => this.Redis?.ConnectClose();

  /// <inheritdoc cref="M:HslCommunication.Algorithms.ConnectPool.IConnector.Open" />
  public void Open() => this.Redis?.SetPersistentConnection();
}
