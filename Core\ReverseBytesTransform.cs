﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.ReverseBytesTransform
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// 大端顺序的字节的转换类，字节的顺序和C#的原生字节的顺序是完全相反的，高字节在前，低字节在后。<br />
/// In the reverse byte order conversion class, the byte order is completely opposite to the native byte order of C#,
/// with the high byte first and the low byte following.
/// </summary>
/// <remarks>适用西门子PLC的S7协议的数据转换</remarks>
public class ReverseBytesTransform : RegularByteTransform
{
  /// <inheritdoc cref="M:HslCommunication.Core.RegularByteTransform.#ctor" />
  public ReverseBytesTransform() => this.DataFormat = DataFormat.ABCD;

  /// <inheritdoc cref="M:HslCommunication.Core.RegularByteTransform.#ctor(HslCommunication.Core.DataFormat)" />
  public ReverseBytesTransform(DataFormat dataFormat)
    : base(dataFormat)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IByteTransform.CreateByDateFormat(HslCommunication.Core.DataFormat)" />
  public override IByteTransform CreateByDateFormat(DataFormat dataFormat)
  {
    ReverseBytesTransform byDateFormat = new ReverseBytesTransform(dataFormat);
    byDateFormat.IsStringReverseByteWord = this.IsStringReverseByteWord;
    return (IByteTransform) byDateFormat;
  }

  /// <inheritdoc />
  public override string ToString() => $"ReverseBytesTransform[{this.DataFormat}]";
}
