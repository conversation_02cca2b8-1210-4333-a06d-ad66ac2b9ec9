﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeSocket
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Net;
using System.Net.Sockets;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// 基于网络通信的管道信息，可以设置额外的一些参数信息，例如连接超时时间，读取超时时间等等。<br />
/// Based on the pipe information of network communication, some additional parameter information can be set, such as connection timeout time, read timeout time and so on.
/// </summary>
public class PipeSocket : PipeBase, IDisposable
{
  private string ipAddress = "127.0.0.1";
  private int[] _port = new int[1]{ 2000 };
  private int indexPort = -1;
  private Socket socket;
  private int receiveTimeOut = 5000;
  private int connectTimeOut = 10000;
  private int sleepTime = 0;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public PipeSocket()
  {
  }

  /// <summary>
  /// 通过指定的IP地址和端口号来实例化一个对象<br />
  /// Instantiate an object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号</param>
  public PipeSocket(string ipAddress, int port)
  {
    this.ipAddress = ipAddress;
    this._port = new int[1]{ port };
  }

  /// <summary>
  /// 获取当前的连接状态是否发生了异常，如果发生了异常，返回 False<br />
  /// Gets whether an exception has occurred in the current connection state, and returns False if an exception has occurred
  /// </summary>
  /// <returns>如果有异常，返回 True, 否则返回 False</returns>
  public bool IsConnectitonError() => this.IsSocketError || this.socket == null;

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.LocalBinding" />
  public IPEndPoint LocalBinding { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.IpAddress" />
  public string IpAddress
  {
    get => this.ipAddress;
    set => this.ipAddress = HslHelper.GetIpAddressFromInput(value);
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.Port" />
  public int Port
  {
    get
    {
      if (this._port.Length == 1)
        return this._port[0];
      int index = this.indexPort;
      if (index < 0 || index >= this._port.Length)
        index = 0;
      return this._port[index];
    }
    set
    {
      if (this._port.Length == 1)
      {
        this._port[0] = value;
      }
      else
      {
        int index = this.indexPort;
        if (index < 0 || index >= this._port.Length)
          index = 0;
        this._port[index] = value;
      }
    }
  }

  /// <summary>
  /// 指示长连接的套接字是否处于错误的状态<br />
  /// Indicates if the long-connected socket is in the wrong state
  /// </summary>
  public bool IsSocketError { get; set; }

  /// <summary>
  /// 获取或设置当前的客户端用于服务器连接的套接字。<br />
  /// Gets or sets the socket currently used by the client for server connection.
  /// </summary>
  public Socket Socket
  {
    get => this.socket;
    set => this.socket = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.ReceiveTimeOut" />
  public int ConnectTimeOut
  {
    get => this.connectTimeOut;
    set => this.connectTimeOut = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.ReceiveTimeOut" />
  public int ReceiveTimeOut
  {
    get => this.receiveTimeOut;
    set => this.receiveTimeOut = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.SleepTime" />
  public int SleepTime
  {
    get => this.sleepTime;
    set => this.sleepTime = value;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public override void Dispose()
  {
    base.Dispose();
    this.socket?.Close();
  }

  /// <summary>
  /// 设置多个可选的端口号信息，例如在三菱的PLC里，支持配置多个端口号，当一个网络发生异常时，立即切换端口号连接读写，提升系统的稳定性<br />
  /// Set multiple optional port number information. For example, in Mitsubishi PLC, it supports to configure multiple port numbers.
  /// When an abnormality occurs in a network, the port number is immediately switched to connect to read and write to improve the stability of the system.
  /// </summary>
  /// <param name="ports">端口号数组信息</param>
  public void SetMultiPorts(int[] ports)
  {
    if (ports == null || ports.Length == 0)
      return;
    this._port = ports;
    this.indexPort = -1;
  }

  /// <summary>
  /// 获取当前的远程连接信息，如果端口号设置了可选的数组，那么每次获取对象就会发生端口号切换的操作。<br />
  /// Get the current remote connection information. If the port number is set to an optional array, the port number switching operation will occur every time the object is obtained.
  /// </summary>
  /// <returns>远程连接的对象</returns>
  public IPEndPoint GetConnectIPEndPoint()
  {
    if (this._port.Length == 1)
      return new IPEndPoint(IPAddress.Parse(this.IpAddress), this._port[0]);
    this.ChangePorts();
    int port = this._port[this.indexPort];
    return new IPEndPoint(IPAddress.Parse(this.IpAddress), port);
  }

  /// <summary>
  /// 变更当前的端口号信息，如果设置了多个端口号的话，就切换其他可用的端口<br />
  /// Change the current port number information, and if multiple port numbers are set, switch to other available ports
  /// </summary>
  public void ChangePorts()
  {
    if (this._port.Length == 1)
      return;
    if (this.indexPort < this._port.Length - 1)
      ++this.indexPort;
    else
      this.indexPort = 0;
  }

  /// <inheritdoc />
  public override string ToString() => $"PipeSocket[{this.ipAddress}:{this.Port}]";
}
