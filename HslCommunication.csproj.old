<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{EA76C07F-FE54-4D61-BCB9-B98DBC67B04C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AssemblyName>HslCommunication</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <ApplicationVersion>12.3.3.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
    <RootNamespace>HslCommunication</RootNamespace>
    <LangVersion>latest</LangVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json">
      <HintPath>lib\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ModbusDataDict.cs" />
    <Compile Include="ModbusDataPool.cs" />
    <Compile Include="Authorization.cs" />
    <Compile Include="NetHandle.cs" />
    <Compile Include="HslProtocol.cs" />
    <Compile Include="HslSecurity.cs" />
    <Compile Include="HslExtension.cs" />
    <Compile Include="HslTimeOut.cs" />
    <Compile Include="IDataTransfer.cs" />
    <Compile Include="OperateResult.cs" />
    <Compile Include="OperateResult`1.cs" />
    <Compile Include="OperateResult`2.cs" />
    <Compile Include="OperateResult`3.cs" />
    <Compile Include="OperateResult`4.cs" />
    <Compile Include="OperateResult`5.cs" />
    <Compile Include="OperateResult`6.cs" />
    <Compile Include="OperateResult`7.cs" />
    <Compile Include="OperateResult`8.cs" />
    <Compile Include="OperateResult`9.cs" />
    <Compile Include="OperateResult`10.cs" />
    <Compile Include="NamespaceDoc.cs" />
    <Compile Include="StringResources.cs" />
    <Compile Include="WebSocket\WebSocketClient.cs" />
    <Compile Include="WebSocket\WebSocketHelper.cs" />
    <Compile Include="WebSocket\WebSocketMessage.cs" />
    <Compile Include="WebSocket\WebSocketQANet.cs" />
    <Compile Include="WebSocket\WebSocketServer.cs" />
    <Compile Include="WebSocket\WebSocketSession.cs" />
    <Compile Include="WebSocket\WSOpCode.cs" />
    <Compile Include="Serial\CheckType.cs" />
    <Compile Include="Serial\SoftCRC16.cs" />
    <Compile Include="Serial\SerialBase.cs" />
    <Compile Include="Serial\SoftLRC.cs" />
    <Compile Include="Secs\Gem.cs" />
    <Compile Include="Secs\SecsGemSerial.cs" />
    <Compile Include="Secs\SecsHsms.cs" />
    <Compile Include="Secs\SecsHsmsServer.cs" />
    <Compile Include="Secs\Types\ISecs.cs" />
    <Compile Include="Secs\Types\OnlineData.cs" />
    <Compile Include="Secs\Types\SecsItemType.cs" />
    <Compile Include="Secs\Types\SecsMessage.cs" />
    <Compile Include="Secs\Types\SecsMessageExtension.cs" />
    <Compile Include="Secs\Types\SecsValue.cs" />
    <Compile Include="Secs\Types\TypeHelper.cs" />
    <Compile Include="Secs\Types\VariableName.cs" />
    <Compile Include="Secs\Message\SecsHsmsMessage.cs" />
    <Compile Include="Secs\Helper\Secs1.cs" />
    <Compile Include="Secs\Helper\Secs2.cs" />
    <Compile Include="Robot\YASKAWA\YRC1000TcpNet.cs" />
    <Compile Include="Robot\YASKAWA\YRCAlarmItem.cs" />
    <Compile Include="Robot\YASKAWA\YRCHighEthernet.cs" />
    <Compile Include="Robot\YASKAWA\YRCRobotData.cs" />
    <Compile Include="Robot\YASKAWA\YRCType.cs" />
    <Compile Include="Robot\YASKAWA\Helper\YRCHelper.cs" />
    <Compile Include="Robot\YASKAWA\Helper\YRCHighEthernetHelper.cs" />
    <Compile Include="Robot\YAMAHA\YamahaRCX.cs" />
    <Compile Include="Robot\KUKA\KukaAvarProxyNet.cs" />
    <Compile Include="Robot\KUKA\KukaTcpNet.cs" />
    <Compile Include="Robot\Hyundai\HyundaiData.cs" />
    <Compile Include="Robot\Hyundai\HyundaiUdpNet.cs" />
    <Compile Include="Robot\FANUC\FanucAlarm.cs" />
    <Compile Include="Robot\FANUC\FanucData.cs" />
    <Compile Include="Robot\FANUC\FanucHelper.cs" />
    <Compile Include="Robot\FANUC\FanucInterfaceNet.cs" />
    <Compile Include="Robot\FANUC\FanucPose.cs" />
    <Compile Include="Robot\FANUC\FanucRobotServer.cs" />
    <Compile Include="Robot\FANUC\FanucTask.cs" />
    <Compile Include="Robot\Estun\EstunData.cs" />
    <Compile Include="Robot\Estun\EstunTcpNet.cs" />
    <Compile Include="Robot\EFORT\EfortData.cs" />
    <Compile Include="Robot\EFORT\ER7BC10.cs" />
    <Compile Include="Robot\EFORT\ER7BC10Previous.cs" />
    <Compile Include="Robot\ABB\ABBWebApiClient.cs" />
    <Compile Include="Robot\ABB\ABBWebApiServer.cs" />
    <Compile Include="Properties\Resources.cs" />
    <Compile Include="Profinet\Yokogawa\YokogawaLinkHelper.cs" />
    <Compile Include="Profinet\Yokogawa\YokogawaLinkServer.cs" />
    <Compile Include="Profinet\Yokogawa\YokogawaLinkTcp.cs" />
    <Compile Include="Profinet\Yokogawa\YokogawaSystemInfo.cs" />
    <Compile Include="Profinet\YASKAWA\MemobusTcpNet.cs" />
    <Compile Include="Profinet\YASKAWA\MemobusTcpServer.cs" />
    <Compile Include="Profinet\YASKAWA\MemobusUdpNet.cs" />
    <Compile Include="Profinet\YASKAWA\Helper\IMemobus.cs" />
    <Compile Include="Profinet\YASKAWA\Helper\MemobusHelper.cs" />
    <Compile Include="Profinet\Yamatake\DigitronCPL.cs" />
    <Compile Include="Profinet\Yamatake\DigitronCPLOverTcp.cs" />
    <Compile Include="Profinet\Yamatake\DigitronCPLServer.cs" />
    <Compile Include="Profinet\Yamatake\Helper\DigitronCPLHelper.cs" />
    <Compile Include="Profinet\XINJE\XinJEHelper.cs" />
    <Compile Include="Profinet\XINJE\XinJEInternalNet.cs" />
    <Compile Include="Profinet\XINJE\XinJEServer.cs" />
    <Compile Include="Profinet\XINJE\XinJESerialOverTcp.cs" />
    <Compile Include="Profinet\XINJE\XinJESeries.cs" />
    <Compile Include="Profinet\XINJE\XinJESerial.cs" />
    <Compile Include="Profinet\XINJE\XinJETcpNet.cs" />
    <Compile Include="Profinet\Vigor\VigorSerial.cs" />
    <Compile Include="Profinet\Vigor\VigorSerialOverTcp.cs" />
    <Compile Include="Profinet\Vigor\VigorServer.cs" />
    <Compile Include="Profinet\Vigor\Helper\VigorHelper.cs" />
    <Compile Include="Profinet\Vigor\Helper\VigorVsHelper.cs" />
    <Compile Include="Profinet\Turck\ReaderNet.cs" />
    <Compile Include="Profinet\Turck\ReaderServer.cs" />
    <Compile Include="Profinet\Toyota\ToyoPuc.cs" />
    <Compile Include="Profinet\Toyota\ToyoPucServer.cs" />
    <Compile Include="Profinet\Toledo\ToledoSerial.cs" />
    <Compile Include="Profinet\Toledo\ToledoStandardData.cs" />
    <Compile Include="Profinet\Toledo\ToledoTcpServer.cs" />
    <Compile Include="Profinet\Special\EcFanMachine.cs" />
    <Compile Include="Profinet\Special\EcFanData.cs" />
    <Compile Include="Profinet\Special\EuFunMessage.cs" />
    <Compile Include="Profinet\Siemens\SiemensDateTime.cs" />
    <Compile Include="Profinet\Siemens\SiemensFetchWriteNet.cs" />
    <Compile Include="Profinet\Siemens\SiemensFetchWriteServer.cs" />
    <Compile Include="Profinet\Siemens\SiemensMPI.cs" />
    <Compile Include="Profinet\Siemens\SiemensPLCS.cs" />
    <Compile Include="Profinet\Siemens\SiemensPPI.cs" />
    <Compile Include="Profinet\Siemens\SiemensPPIOverTcp.cs" />
    <Compile Include="Profinet\Siemens\SiemensPPIServer.cs" />
    <Compile Include="Profinet\Siemens\SiemensS7Net.cs" />
    <Compile Include="Profinet\Siemens\SiemensS7Plus.cs" />
    <Compile Include="Profinet\Siemens\S7PlusStream.cs" />
    <Compile Include="Profinet\Siemens\SiemensS7Server.cs" />
    <Compile Include="Profinet\Siemens\SiemensWebApi.cs" />
    <Compile Include="Profinet\Siemens\S7PlusHelper\OpenSslConnect.cs" />
    <Compile Include="Profinet\Siemens\S7PlusHelper\S7Object.cs" />
    <Compile Include="Profinet\Siemens\S7PlusHelper\IS7Object.cs" />
    <Compile Include="Profinet\Siemens\S7PlusHelper\S7Tag.cs" />
    <Compile Include="Profinet\Siemens\S7PlusHelper\S7Value.cs" />
    <Compile Include="Profinet\Siemens\Helper\ISiemensPPI.cs" />
    <Compile Include="Profinet\Siemens\Helper\SiemensPPIHelper.cs" />
    <Compile Include="Profinet\Siemens\Helper\SiemensS7Helper.cs" />
    <Compile Include="Profinet\Sick\SickIcrTcpServer.cs" />
    <Compile Include="Profinet\Panasonic\PanasonicHelper.cs" />
    <Compile Include="Profinet\Panasonic\PanasonicMcNet.cs" />
    <Compile Include="Profinet\Panasonic\PanasonicMewtocol.cs" />
    <Compile Include="Profinet\Panasonic\PanasonicMewtocolOverTcp.cs" />
    <Compile Include="Profinet\Panasonic\PanasonicMewtocolServer.cs" />
    <Compile Include="Profinet\Panasonic\Helper\MewtocolHelper.cs" />
    <Compile Include="Profinet\OpenProtocol\AlarmMessages.cs" />
    <Compile Include="Profinet\OpenProtocol\JobMessage.cs" />
    <Compile Include="Profinet\OpenProtocol\JobData.cs" />
    <Compile Include="Profinet\OpenProtocol\JobItem.cs" />
    <Compile Include="Profinet\OpenProtocol\OpenEventArgs.cs" />
    <Compile Include="Profinet\OpenProtocol\OpenProtocolNet.cs" />
    <Compile Include="Profinet\OpenProtocol\OpenProtocolServer.cs" />
    <Compile Include="Profinet\OpenProtocol\OpenProtocolSession.cs" />
    <Compile Include="Profinet\OpenProtocol\ParameterSetMessages.cs" />
    <Compile Include="Profinet\OpenProtocol\ParameterSetData.cs" />
    <Compile Include="Profinet\OpenProtocol\TighteningResultMessages.cs" />
    <Compile Include="Profinet\OpenProtocol\TimeMessages.cs" />
    <Compile Include="Profinet\OpenProtocol\ToolMessages.cs" />
    <Compile Include="Profinet\OpenProtocol\ToolData.cs" />
    <Compile Include="Profinet\Omron\OmronCipNet.cs" />
    <Compile Include="Profinet\Omron\OmronCipServer.cs" />
    <Compile Include="Profinet\Omron\OmronConnectedCipNet.cs" />
    <Compile Include="Profinet\Omron\OmronCpuUnitData.cs" />
    <Compile Include="Profinet\Omron\OmronCpuUnitStatus.cs" />
    <Compile Include="Profinet\Omron\OmronFinsDataType.cs" />
    <Compile Include="Profinet\Omron\OmronFinsNet.cs" />
    <Compile Include="Profinet\Omron\OmronFinsNetHelper.cs" />
    <Compile Include="Profinet\Omron\OmronFinsServer.cs" />
    <Compile Include="Profinet\Omron\OmronFinsUdp.cs" />
    <Compile Include="Profinet\Omron\OmronFinsUdpServer.cs" />
    <Compile Include="Profinet\Omron\OmronHostLink.cs" />
    <Compile Include="Profinet\Omron\OmronHostLinkCMode.cs" />
    <Compile Include="Profinet\Omron\OmronHostLinkCModeOverTcp.cs" />
    <Compile Include="Profinet\Omron\OmronHostLinkCModeServer.cs" />
    <Compile Include="Profinet\Omron\OmronHostLinkOverTcp.cs" />
    <Compile Include="Profinet\Omron\OmronHostLinkServer.cs" />
    <Compile Include="Profinet\Omron\OmronPlcType.cs" />
    <Compile Include="Profinet\Omron\Helper\IHostLink.cs" />
    <Compile Include="Profinet\Omron\Helper\IHostLinkCMode.cs" />
    <Compile Include="Profinet\Omron\Helper\IOmronFins.cs" />
    <Compile Include="Profinet\Omron\Helper\OmronHostLinkCModeHelper.cs" />
    <Compile Include="Profinet\Omron\Helper\OmronHostLinkHelper.cs" />
    <Compile Include="Profinet\Melsec\MelsecA1EAsciiNet.cs" />
    <Compile Include="Profinet\Melsec\MelsecA1EDataType.cs" />
    <Compile Include="Profinet\Melsec\MelsecA1ENet.cs" />
    <Compile Include="Profinet\Melsec\MelsecA3CNet.cs" />
    <Compile Include="Profinet\Melsec\MelsecA3CNetOverTcp.cs" />
    <Compile Include="Profinet\Melsec\MelsecA3CServer.cs" />
    <Compile Include="Profinet\Melsec\MelsecCipNet.cs" />
    <Compile Include="Profinet\Melsec\MelsecFxLinks.cs" />
    <Compile Include="Profinet\Melsec\MelsecFxLinksOverTcp.cs" />
    <Compile Include="Profinet\Melsec\MelsecFxLinksServer.cs" />
    <Compile Include="Profinet\Melsec\MelsecFxSerialOverTcp.cs" />
    <Compile Include="Profinet\Melsec\MelsecFxSerialServer.cs" />
    <Compile Include="Profinet\Melsec\MelsecHelper.cs" />
    <Compile Include="Profinet\Melsec\MelsecMcAsciiUdp.cs" />
    <Compile Include="Profinet\Melsec\MelsecMcAsciiNet.cs" />
    <Compile Include="Profinet\Melsec\MelsecMcDataType.cs" />
    <Compile Include="Profinet\Melsec\MelsecMcRNet.cs" />
    <Compile Include="Profinet\Melsec\MelsecA1EServer.cs" />
    <Compile Include="Profinet\Melsec\MelsecMcUdp.cs" />
    <Compile Include="Profinet\Melsec\MelsecMcNet.cs" />
    <Compile Include="Profinet\Melsec\MelsecMcServer.cs" />
    <Compile Include="Profinet\Melsec\MelsecFxSerial.cs" />
    <Compile Include="Profinet\Melsec\NamespaceDoc.cs" />
    <Compile Include="Profinet\Melsec\Helper\IMelsecFxSerial.cs" />
    <Compile Include="Profinet\Melsec\Helper\IReadWriteA3C.cs" />
    <Compile Include="Profinet\Melsec\Helper\IReadWriteFxLinks.cs" />
    <Compile Include="Profinet\Melsec\Helper\IReadWriteMc.cs" />
    <Compile Include="Profinet\Melsec\Helper\McType.cs" />
    <Compile Include="Profinet\Melsec\Helper\McAsciiHelper.cs" />
    <Compile Include="Profinet\Melsec\Helper\McBinaryHelper.cs" />
    <Compile Include="Profinet\Melsec\Helper\McHelper.cs" />
    <Compile Include="Profinet\Melsec\Helper\MelsecA3CNetHelper.cs" />
    <Compile Include="Profinet\Melsec\Helper\MelsecFxLinksHelper.cs" />
    <Compile Include="Profinet\Melsec\Helper\MelsecFxSerialHelper.cs" />
    <Compile Include="Profinet\MegMeet\MegMeetHelper.cs" />
    <Compile Include="Profinet\MegMeet\MegMeetSerial.cs" />
    <Compile Include="Profinet\MegMeet\MegMeetSerialOverTcp.cs" />
    <Compile Include="Profinet\MegMeet\MegMeetTcpNet.cs" />
    <Compile Include="Profinet\LSIS\LSCnet.cs" />
    <Compile Include="Profinet\LSIS\LSCnetOverTcp.cs" />
    <Compile Include="Profinet\LSIS\LSCpu.cs" />
    <Compile Include="Profinet\LSIS\LSCpuInfo.cs" />
    <Compile Include="Profinet\LSIS\LSCpuStatus.cs" />
    <Compile Include="Profinet\LSIS\LSFastEnet.cs" />
    <Compile Include="Profinet\LSIS\LSisServer.cs" />
    <Compile Include="Profinet\LSIS\Helper\LSCnetHelper.cs" />
    <Compile Include="Profinet\LSIS\Helper\LSCpuHelper.cs" />
    <Compile Include="Profinet\Knx\KnxCode.cs" />
    <Compile Include="Profinet\Knx\KnxUdp.cs" />
    <Compile Include="Profinet\Keyence\KeyenceDLEN1.cs" />
    <Compile Include="Profinet\Keyence\IKeyenceSR2000Series.cs" />
    <Compile Include="Profinet\Keyence\KeyenceDataType.cs" />
    <Compile Include="Profinet\Keyence\KeyenceKvOld.cs" />
    <Compile Include="Profinet\Keyence\KeyenceMcAsciiNet.cs" />
    <Compile Include="Profinet\Keyence\KeyenceMcNet.cs" />
    <Compile Include="Profinet\Keyence\KeyenceNanoHelper.cs" />
    <Compile Include="Profinet\Keyence\KeyenceNanoSerial.cs" />
    <Compile Include="Profinet\Keyence\KeyenceNanoSerialOverTcp.cs" />
    <Compile Include="Profinet\Keyence\KeyenceNanoServer.cs" />
    <Compile Include="Profinet\Keyence\KeyencePLCS.cs" />
    <Compile Include="Profinet\Keyence\KeyenceSR2000Helper.cs" />
    <Compile Include="Profinet\Keyence\KeyenceSR2000Serial.cs" />
    <Compile Include="Profinet\Keyence\KeyenceSR2000SeriesTcp.cs" />
    <Compile Include="Profinet\Inovance\InovanceHelper.cs" />
    <Compile Include="Profinet\Inovance\InovanceSerial.cs" />
    <Compile Include="Profinet\Inovance\InovanceSerialOverTcp.cs" />
    <Compile Include="Profinet\Inovance\InovanceSeries.cs" />
    <Compile Include="Profinet\Inovance\InovanceTcpNet.cs" />
    <Compile Include="Profinet\IDCard\IdentityCard.cs" />
    <Compile Include="Profinet\IDCard\SAMSerial.cs" />
    <Compile Include="Profinet\IDCard\SAMTcpNet.cs" />
    <Compile Include="Profinet\GE\GeHelper.cs" />
    <Compile Include="Profinet\GE\GeSRTPNet.cs" />
    <Compile Include="Profinet\GE\GeSRTPServer.cs" />
    <Compile Include="Profinet\Geniitek\VibrationSensorActualValue.cs" />
    <Compile Include="Profinet\Geniitek\VibrationSensorClient.cs" />
    <Compile Include="Profinet\Geniitek\VibrationSensorLongMessage.cs" />
    <Compile Include="Profinet\Geniitek\VibrationSensorPeekValue.cs" />
    <Compile Include="Profinet\Geniitek\VibrationSensorShortMessage.cs" />
    <Compile Include="Profinet\Fuji\FujiCommandSettingType.cs" />
    <Compile Include="Profinet\Fuji\FujiCommandSettingTypeServer.cs" />
    <Compile Include="Profinet\Fuji\FujiSPB.cs" />
    <Compile Include="Profinet\Fuji\FujiSPBHelper.cs" />
    <Compile Include="Profinet\Fuji\FujiSPHNet.cs" />
    <Compile Include="Profinet\Fuji\FujiSPBOverTcp.cs" />
    <Compile Include="Profinet\Fuji\FujiSPBServer.cs" />
    <Compile Include="Profinet\Fuji\FujiSPHServer.cs" />
    <Compile Include="Profinet\Freedom\FreedomSerial.cs" />
    <Compile Include="Profinet\Freedom\FreedomTcpNet.cs" />
    <Compile Include="Profinet\Freedom\FreedomUdpNet.cs" />
    <Compile Include="Profinet\FATEK\FatekProgram.cs" />
    <Compile Include="Profinet\FATEK\FatekProgramOverTcp.cs" />
    <Compile Include="Profinet\FATEK\FatekProgramServer.cs" />
    <Compile Include="Profinet\FATEK\Helper\FatekProgramHelper.cs" />
    <Compile Include="Profinet\FATEK\Helper\IFatekProgram.cs" />
    <Compile Include="Profinet\Delta\DeltaSerialAscii.cs" />
    <Compile Include="Profinet\Delta\DeltaSerialAsciiOverTcp.cs" />
    <Compile Include="Profinet\Delta\DeltaSerial.cs" />
    <Compile Include="Profinet\Delta\DeltaSerialOverTcp.cs" />
    <Compile Include="Profinet\Delta\DeltaTcpNet.cs" />
    <Compile Include="Profinet\Delta\DeltaSeries.cs" />
    <Compile Include="Profinet\Delta\IDelta.cs" />
    <Compile Include="Profinet\Delta\Helper\DeltaASHelper.cs" />
    <Compile Include="Profinet\Delta\Helper\DeltaDvpHelper.cs" />
    <Compile Include="Profinet\Delta\Helper\DeltaHelper.cs" />
    <Compile Include="Profinet\Cimon\CimonHmiProtocol.cs" />
    <Compile Include="Profinet\Cimon\CimonServer.cs" />
    <Compile Include="Profinet\Cimon\Helper\CimonHelper.cs" />
    <Compile Include="Profinet\Beckhoff\AdsDeviceInfo.cs" />
    <Compile Include="Profinet\Beckhoff\AmsTcpHeaderFlags.cs" />
    <Compile Include="Profinet\Beckhoff\BeckhoffAdsNet.cs" />
    <Compile Include="Profinet\Beckhoff\BeckhoffAdsServer.cs" />
    <Compile Include="Profinet\Beckhoff\BeckhoffCommandId.cs" />
    <Compile Include="Profinet\Beckhoff\Helper\AdsHelper.cs" />
    <Compile Include="Profinet\Beckhoff\Helper\AdsTagItem.cs" />
    <Compile Include="Profinet\AllenBradley\AbStructHandle.cs" />
    <Compile Include="Profinet\AllenBradley\AbTagItem.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyConnectedCipNet.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyDF1Serial.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyHelper.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyItemValue.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyMicroCip.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyNet.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyPcccNet.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyPcccServer.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleyServer.cs" />
    <Compile Include="Profinet\AllenBradley\CipSessionTag.cs" />
    <Compile Include="Profinet\AllenBradley\AllenBradleySLCNet.cs" />
    <Compile Include="Profinet\AllenBradley\IReadWriteCip.cs" />
    <Compile Include="Profinet\AllenBradley\MessageRouter.cs" />
    <Compile Include="MQTT\IMqttSyncConnector.cs" />
    <Compile Include="MQTT\MqttApplicationMessage.cs" />
    <Compile Include="MQTT\MqttClient.cs" />
    <Compile Include="MQTT\SubscribeTopic.cs" />
    <Compile Include="MQTT\MqttClientApplicationMessage.cs" />
    <Compile Include="MQTT\MqttConnectionOptions.cs" />
    <Compile Include="MQTT\MqttControlMessage.cs" />
    <Compile Include="MQTT\MqttCredential.cs" />
    <Compile Include="MQTT\MqttHelper.cs" />
    <Compile Include="MQTT\MqttPublishMessage.cs" />
    <Compile Include="MQTT\MqttQualityOfServiceLevel.cs" />
    <Compile Include="MQTT\MqttRpcDevice.cs" />
    <Compile Include="MQTT\MqttServer.cs" />
    <Compile Include="MQTT\MqttSession.cs" />
    <Compile Include="MQTT\MqttSessionInfo.cs" />
    <Compile Include="MQTT\MqttSubscribeMessage.cs" />
    <Compile Include="MQTT\MqttSyncClient.cs" />
    <Compile Include="MQTT\MqttSyncClientPool.cs" />
    <Compile Include="MQTT\MqttRpcApiInfo.cs" />
    <Compile Include="ModBus\IModbus.cs" />
    <Compile Include="ModBus\ModbusMappingAddress.cs" />
    <Compile Include="ModBus\ModbusAscii.cs" />
    <Compile Include="ModBus\ModbusAsciiOverTcp.cs" />
    <Compile Include="ModBus\ModbusHelper.cs" />
    <Compile Include="ModBus\ModbusInfo.cs" />
    <Compile Include="ModBus\ModBusMonitorAddress.cs" />
    <Compile Include="ModBus\ModbusRtu.cs" />
    <Compile Include="ModBus\ModbusRtuOverTcp.cs" />
    <Compile Include="ModBus\ModbusTcpNet.cs" />
    <Compile Include="ModBus\ModbusTcpServer.cs" />
    <Compile Include="ModBus\ModbusUdpNet.cs" />
    <Compile Include="ModBus\MonitorAddress.cs" />
    <Compile Include="LogNet\ILogNet.cs" />
    <Compile Include="LogNet\LogNetBase.cs" />
    <Compile Include="LogNet\LogPathBase.cs" />
    <Compile Include="LogNet\LogStatisticsBase`1.cs" />
    <Compile Include="LogNet\HslEventArgs.cs" />
    <Compile Include="LogNet\LogNetException.cs" />
    <Compile Include="LogNet\LogSaveMode.cs" />
    <Compile Include="LogNet\GenerateMode.cs" />
    <Compile Include="LogNet\HslMessageDegree.cs" />
    <Compile Include="LogNet\HslMessageItem.cs" />
    <Compile Include="LogNet\LogNetManagment.cs" />
    <Compile Include="LogNet\LogNetDateTime.cs" />
    <Compile Include="LogNet\LogNetFileSize.cs" />
    <Compile Include="LogNet\LogNetSingle.cs" />
    <Compile Include="LogNet\LogStatistics.cs" />
    <Compile Include="LogNet\LogStatisticsDict.cs" />
    <Compile Include="LogNet\LogValueLimit.cs" />
    <Compile Include="LogNet\LogValueLimitDict.cs" />
    <Compile Include="LogNet\FormLogNetView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LogNet\LogNetAnalysisControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Language\DefaultLanguage.cs" />
    <Compile Include="Language\English.cs" />
    <Compile Include="Instrument\Temperature\DAM3601.cs" />
    <Compile Include="Instrument\RKC\TemperatureController.cs" />
    <Compile Include="Instrument\RKC\TemperatureControllerOverTcp.cs" />
    <Compile Include="Instrument\RKC\TemperatureControllerServer.cs" />
    <Compile Include="Instrument\RKC\Helper\TemperatureControllerHelper.cs" />
    <Compile Include="Instrument\Light\ShineInLightSourceController.cs" />
    <Compile Include="Instrument\Light\ShineInLightData.cs" />
    <Compile Include="Instrument\IEC\IEC104.cs" />
    <Compile Include="Instrument\IEC\IEC104MessageEventArgs.cs" />
    <Compile Include="Instrument\IEC\IEC104Server.cs" />
    <Compile Include="Instrument\IEC\IecValueServerObject`1.cs" />
    <Compile Include="Instrument\IEC\IecValueObject`1.cs" />
    <Compile Include="Instrument\IEC\Helper\IECHelper.cs" />
    <Compile Include="Instrument\IEC\Helper\IECSessionInfo.cs" />
    <Compile Include="Instrument\DLT\DLT645.cs" />
    <Compile Include="Instrument\DLT\DLT645OverTcp.cs" />
    <Compile Include="Instrument\DLT\DLT645Server.cs" />
    <Compile Include="Instrument\DLT\DLT645With1997.cs" />
    <Compile Include="Instrument\DLT\DLT645With1997OverTcp.cs" />
    <Compile Include="Instrument\DLT\DLT645With1997Server.cs" />
    <Compile Include="Instrument\DLT\DLT698.cs" />
    <Compile Include="Instrument\DLT\DLT698OverTcp.cs" />
    <Compile Include="Instrument\DLT\DLT698Server.cs" />
    <Compile Include="Instrument\DLT\DLT698TcpNet.cs" />
    <Compile Include="Instrument\DLT\DLTControl.cs" />
    <Compile Include="Instrument\DLT\DLTTransform.cs" />
    <Compile Include="Instrument\DLT\Helper\DLT645Helper.cs" />
    <Compile Include="Instrument\DLT\Helper\DLT645Type.cs" />
    <Compile Include="Instrument\DLT\Helper\DLT698FcsHelper.cs" />
    <Compile Include="Instrument\DLT\Helper\DLT698Helper.cs" />
    <Compile Include="Instrument\DLT\Helper\IDlt645.cs" />
    <Compile Include="Instrument\DLT\Helper\IDlt698.cs" />
    <Compile Include="Instrument\Delixi\DTSU6606Serial.cs" />
    <Compile Include="Instrument\Delixi\ElectricalParameters.cs" />
    <Compile Include="Instrument\CJT\CJT188.cs" />
    <Compile Include="Instrument\CJT\CJT188OverTcp.cs" />
    <Compile Include="Instrument\CJT\CjtFlowRate.cs" />
    <Compile Include="Instrument\CJT\Helper\CJT188Helper.cs" />
    <Compile Include="Instrument\CJT\Helper\CJTControl.cs" />
    <Compile Include="Instrument\CJT\Helper\ICjt188.cs" />
    <Compile Include="Enthernet\NetComplexClient.cs" />
    <Compile Include="Enthernet\NetComplexServer.cs" />
    <Compile Include="Enthernet\AdvancedFileServer.cs" />
    <Compile Include="Enthernet\FileClientBase.cs" />
    <Compile Include="Enthernet\IntegrationFileClient.cs" />
    <Compile Include="Enthernet\UltimateFileServer.cs" />
    <Compile Include="Enthernet\HttpApiCalledInfo.cs" />
    <Compile Include="Enthernet\HttpServer.cs" />
    <Compile Include="Enthernet\HttpUploadFile.cs" />
    <Compile Include="Enthernet\NetPlainSocket.cs" />
    <Compile Include="Enthernet\AppPushSession.cs" />
    <Compile Include="Enthernet\NetPushClient.cs" />
    <Compile Include="Enthernet\NetPushServer.cs" />
    <Compile Include="Enthernet\PushGroupClient.cs" />
    <Compile Include="Enthernet\NetSimplifyClient.cs" />
    <Compile Include="Enthernet\NetSimplifyServer.cs" />
    <Compile Include="Enthernet\FileInfoExtension.cs" />
    <Compile Include="Enthernet\NetSoftUpdateServer.cs" />
    <Compile Include="Enthernet\ForwardSession.cs" />
    <Compile Include="Enthernet\TcpForward.cs" />
    <Compile Include="Enthernet\NetUdpClient.cs" />
    <Compile Include="Enthernet\NetUdpServer.cs" />
    <Compile Include="Enthernet\Tcp\DebugRemoteServer.cs" />
    <Compile Include="Enthernet\Redis\IRedisConnector.cs" />
    <Compile Include="Enthernet\Redis\RedisClientPool.cs" />
    <Compile Include="Enthernet\Redis\RedisHelper.cs" />
    <Compile Include="Enthernet\Redis\RedisSubscribe.cs" />
    <Compile Include="Enthernet\Redis\RedisClient.cs" />
    <Compile Include="DTU\DTUServer.cs" />
    <Compile Include="DTU\DTUSettingType.cs" />
    <Compile Include="DCS\DcsNanJingAuto.cs" />
    <Compile Include="Reflection\HslDeviceAddressAttribute.cs" />
    <Compile Include="Reflection\HslAddressProperty.cs" />
    <Compile Include="Reflection\HslMqttApiAttribute.cs" />
    <Compile Include="Reflection\HslMqttPermissionAttribute.cs" />
    <Compile Include="Reflection\HslRedisKeyAttribute.cs" />
    <Compile Include="Reflection\PropertyInfoKeyName.cs" />
    <Compile Include="Reflection\PropertyInfoHashKeyName.cs" />
    <Compile Include="Reflection\HslRedisListItemAttribute.cs" />
    <Compile Include="Reflection\HslRedisListAttribute.cs" />
    <Compile Include="Reflection\HslRedisHashFieldAttribute.cs" />
    <Compile Include="Reflection\HslReflectionHelper.cs" />
    <Compile Include="Reflection\HslStructAttribute.cs" />
    <Compile Include="Core\GroupFileInfo.cs" />
    <Compile Include="Core\MqttFileOperateInfo.cs" />
    <Compile Include="Core\MqttFileMonitor.cs" />
    <Compile Include="Core\MqttFileMonitorItem.cs" />
    <Compile Include="Core\IReadWriteDevice.cs" />
    <Compile Include="Core\IReadWriteNet.cs" />
    <Compile Include="Core\NetSupport.cs" />
    <Compile Include="Core\CommunicationLockNone.cs" />
    <Compile Include="Core\CommunicationLockSimple.cs" />
    <Compile Include="Core\ICommunicationLock.cs" />
    <Compile Include="Core\CoordinationStatus.cs" />
    <Compile Include="Core\AsyncCoordinator.cs" />
    <Compile Include="Core\HslAsyncCoordinator.cs" />
    <Compile Include="Core\HslReadWriteLock.cs" />
    <Compile Include="Core\SimpleHybirdLock.cs" />
    <Compile Include="Core\SoftMultiTask`1.cs" />
    <Compile Include="Core\Singleton.cs" />
    <Compile Include="Core\AdvancedHybirdLock.cs" />
    <Compile Include="Core\RegularByteTransform.cs" />
    <Compile Include="Core\ByteTransformHelper.cs" />
    <Compile Include="Core\DataFormat.cs" />
    <Compile Include="Core\IByteTransform.cs" />
    <Compile Include="Core\ReverseBytesTransform.cs" />
    <Compile Include="Core\ReverseWordTransform.cs" />
    <Compile Include="Core\AsciiControl.cs" />
    <Compile Include="Core\HslCancelToken.cs" />
    <Compile Include="Core\HslHelper.cs" />
    <Compile Include="Core\CuttingAddress.cs" />
    <Compile Include="Core\HslPieItem.cs" />
    <Compile Include="Core\ISessionContext.cs" />
    <Compile Include="Core\RemoteCloseException.cs" />
    <Compile Include="Core\SessionContext.cs" />
    <Compile Include="Core\ValueLimit.cs" />
    <Compile Include="Core\FileBaseInfo.cs" />
    <Compile Include="Core\FileGroupInfo.cs" />
    <Compile Include="Core\FileServerInfo.cs" />
    <Compile Include="Core\FileMarkId.cs" />
    <Compile Include="Core\GroupFileContainer.cs" />
    <Compile Include="Core\GroupFileItem.cs" />
    <Compile Include="Core\Types\CertificateDegree.cs" />
    <Compile Include="Core\Security\AesCryptography.cs" />
    <Compile Include="Core\Security\DesCryptography.cs" />
    <Compile Include="Core\Security\HslCertificate.cs" />
    <Compile Include="Core\Security\ICryptography.cs" />
    <Compile Include="Core\Security\OpenSslNative.cs" />
    <Compile Include="Core\Security\RSAHelper.cs" />
    <Compile Include="Core\Pipe\CommunicationPipe.cs" />
    <Compile Include="Core\Pipe\PipeBase.cs" />
    <Compile Include="Core\Pipe\PipeDebugRemote.cs" />
    <Compile Include="Core\Pipe\PipeDtuNet.cs" />
    <Compile Include="Core\Pipe\PipeMoxa.cs" />
    <Compile Include="Core\Pipe\PCommHelper.cs" />
    <Compile Include="Core\Pipe\PipeMqttClient.cs" />
    <Compile Include="Core\Pipe\PipeSerial.cs" />
    <Compile Include="Core\Pipe\PipeSerialPort.cs" />
    <Compile Include="Core\Pipe\PipeSocket.cs" />
    <Compile Include="Core\Pipe\PipeSslNet.cs" />
    <Compile Include="Core\Pipe\PipeTcpNet.cs" />
    <Compile Include="Core\Pipe\PipeUdpNet.cs" />
    <Compile Include="Core\Net\IReadWriteDeviceStation.cs" />
    <Compile Include="Core\Net\IRobotNet.cs" />
    <Compile Include="Core\Net\BinaryCommunication.cs" />
    <Compile Include="Core\Net\CommunicationServer.cs" />
    <Compile Include="Core\Net\RemoteConnectInfo.cs" />
    <Compile Include="Core\Net\DtuStatus.cs" />
    <Compile Include="Core\Net\CommunicationTcpServer.cs" />
    <Compile Include="Core\Net\NetworkAlienClient.cs" />
    <Compile Include="Core\Net\NetworkAuthenticationServerBase.cs" />
    <Compile Include="Core\Net\NetworkBase.cs" />
    <Compile Include="Core\Net\NetworkConnectedCip.cs" />
    <Compile Include="Core\Net\NetworkDoubleBase.cs" />
    <Compile Include="Core\Net\NetworkFileServerBase.cs" />
    <Compile Include="Core\Net\NetworkServerBase.cs" />
    <Compile Include="Core\Net\NetworkUdpBase.cs" />
    <Compile Include="Core\Net\NetworkWebApiBase.cs" />
    <Compile Include="Core\Net\NetworkWebApiRobotBase.cs" />
    <Compile Include="Core\Net\NetworkXBase.cs" />
    <Compile Include="Core\Net\TcpNetCommunication.cs" />
    <Compile Include="Core\Net\ReadWriteNetHelper.cs" />
    <Compile Include="Core\Net\AlienSession.cs" />
    <Compile Include="Core\Net\AppSession.cs" />
    <Compile Include="Core\Net\AsyncStateSend.cs" />
    <Compile Include="Core\Net\FileStateObject.cs" />
    <Compile Include="Core\Net\PipeSession.cs" />
    <Compile Include="Core\Net\SessionBase.cs" />
    <Compile Include="Core\Net\StateObject.cs" />
    <Compile Include="Core\Net\StateObjectAsync`1.cs" />
    <Compile Include="Core\Net\StateOneBase.cs" />
    <Compile Include="Core\IMessage\AdsNetMessage.cs" />
    <Compile Include="Core\IMessage\AlienMessage.cs" />
    <Compile Include="Core\IMessage\AllenBradleyMessage.cs" />
    <Compile Include="Core\IMessage\AllenBradleySLCMessage.cs" />
    <Compile Include="Core\IMessage\CJT188Message.cs" />
    <Compile Include="Core\IMessage\DcsNanJingAutoMessage.cs" />
    <Compile Include="Core\IMessage\DLT645Message.cs" />
    <Compile Include="Core\IMessage\DLT698Message.cs" />
    <Compile Include="Core\IMessage\EFORTMessage.cs" />
    <Compile Include="Core\IMessage\EFORTMessagePrevious.cs" />
    <Compile Include="Core\IMessage\FanucRobotMessage.cs" />
    <Compile Include="Core\IMessage\FatekProgramMessage.cs" />
    <Compile Include="Core\IMessage\FetchWriteMessage.cs" />
    <Compile Include="Core\IMessage\FinsMessage.cs" />
    <Compile Include="Core\IMessage\FinsUdpMessage.cs" />
    <Compile Include="Core\IMessage\FujiCommandSettingTypeMessage.cs" />
    <Compile Include="Core\IMessage\FujiSPBMessage.cs" />
    <Compile Include="Core\IMessage\FujiSPHMessage.cs" />
    <Compile Include="Core\IMessage\GeSRTPMessage.cs" />
    <Compile Include="Core\IMessage\HslMessage.cs" />
    <Compile Include="Core\IMessage\IEC104Message.cs" />
    <Compile Include="Core\IMessage\INetMessage.cs" />
    <Compile Include="Core\IMessage\KeyenceNanoSerialMessage.cs" />
    <Compile Include="Core\IMessage\KukaVarProxyMessage.cs" />
    <Compile Include="Core\IMessage\LsisFastEnetMessage.cs" />
    <Compile Include="Core\IMessage\MelsecA1EAsciiMessage.cs" />
    <Compile Include="Core\IMessage\MelsecA1EBinaryMessage.cs" />
    <Compile Include="Core\IMessage\MelsecFxLinksMessage.cs" />
    <Compile Include="Core\IMessage\MelsecFxSerialMessage.cs" />
    <Compile Include="Core\IMessage\MelsecQnA3EAsciiMessage.cs" />
    <Compile Include="Core\IMessage\MelsecQnA3EBinaryMessage.cs" />
    <Compile Include="Core\IMessage\MemobusMessage.cs" />
    <Compile Include="Core\IMessage\ModbusAsciiMessage.cs" />
    <Compile Include="Core\IMessage\ModbusRtuMessage.cs" />
    <Compile Include="Core\IMessage\ModbusTcpMessage.cs" />
    <Compile Include="Core\IMessage\NetMessageBase.cs" />
    <Compile Include="Core\IMessage\OpenProtocolMessage.cs" />
    <Compile Include="Core\IMessage\RkcTemperatureMessage.cs" />
    <Compile Include="Core\IMessage\S7Message.cs" />
    <Compile Include="Core\IMessage\SAMMessage.cs" />
    <Compile Include="Core\IMessage\SiemensPPIMessage.cs" />
    <Compile Include="Core\IMessage\SiemensPPIServerMessage.cs" />
    <Compile Include="Core\IMessage\SpecifiedCharacterMessage.cs" />
    <Compile Include="Core\IMessage\ToyoPucMessage.cs" />
    <Compile Include="Core\IMessage\TurckReaderMessage.cs" />
    <Compile Include="Core\IMessage\VigorSerialMessage.cs" />
    <Compile Include="Core\IMessage\YokogawaLinkBinaryMessage.cs" />
    <Compile Include="Core\Device\DeviceCommunication.cs" />
    <Compile Include="Core\Device\DeviceSerialPort.cs" />
    <Compile Include="Core\Device\DeviceServer.cs" />
    <Compile Include="Core\Device\DeviceTcpNet.cs" />
    <Compile Include="Core\Device\DeviceUdpNet.cs" />
    <Compile Include="Core\Device\DeviceWebApi.cs" />
    <Compile Include="Core\Address\AllenBradleySLCAddress.cs" />
    <Compile Include="Core\Address\CnetAddressData.cs" />
    <Compile Include="Core\Address\DeviceAddressDataBase.cs" />
    <Compile Include="Core\Address\FanucPMCAddress.cs" />
    <Compile Include="Core\Address\FatekProgramAddress.cs" />
    <Compile Include="Core\Address\FujiCommandSettingTypeAddress.cs" />
    <Compile Include="Core\Address\FujiSPBAddress.cs" />
    <Compile Include="Core\Address\FujiSPHAddress.cs" />
    <Compile Include="Core\Address\GeSRTPAddress.cs" />
    <Compile Include="Core\Address\KeyenceNanoAddress.cs" />
    <Compile Include="Core\Address\LsisCnetAddress.cs" />
    <Compile Include="Core\Address\McAddressData.cs" />
    <Compile Include="Core\Address\MelsecFxLinksAddress.cs" />
    <Compile Include="Core\Address\MemobusAddress.cs" />
    <Compile Include="Core\Address\OmronFinsAddress.cs" />
    <Compile Include="Core\Address\S7AddressData.cs" />
    <Compile Include="Core\Address\ToyoPucAddress.cs" />
    <Compile Include="Core\Address\VigorAddress.cs" />
    <Compile Include="Core\Address\XinJEAddress.cs" />
    <Compile Include="Core\Address\YokogawaLinkAddress.cs" />
    <Compile Include="Core\Address\ModbusAddress.cs" />
    <Compile Include="CNC\Fanuc\CNCFanucSeriesMessage.cs" />
    <Compile Include="CNC\Fanuc\CNCRunStatus.cs" />
    <Compile Include="CNC\Fanuc\CNCWorkMode.cs" />
    <Compile Include="CNC\Fanuc\CutterInfo.cs" />
    <Compile Include="CNC\Fanuc\FanucSysInfo.cs" />
    <Compile Include="CNC\Fanuc\FanucOperatorMessage.cs" />
    <Compile Include="CNC\Fanuc\FileDirInfo.cs" />
    <Compile Include="CNC\Fanuc\ToolInformation.cs" />
    <Compile Include="CNC\Fanuc\FanucSeries0i.cs" />
    <Compile Include="CNC\Fanuc\SysAlarm.cs" />
    <Compile Include="CNC\Fanuc\SysAllCoors.cs" />
    <Compile Include="CNC\Fanuc\SysStatusInfo.cs" />
    <Compile Include="BasicFramework\SharpList`1.cs" />
    <Compile Include="BasicFramework\FormAuthorize.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BasicFramework\FormPopup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BasicFramework\SoftAnimation.cs" />
    <Compile Include="BasicFramework\SoftAuthorize.cs" />
    <Compile Include="BasicFramework\ISoftFileSaveBase.cs" />
    <Compile Include="BasicFramework\SoftFileSaveBase.cs" />
    <Compile Include="BasicFramework\SoftBasic.cs" />
    <Compile Include="BasicFramework\UrlDecoder.cs" />
    <Compile Include="BasicFramework\SoftBuffer.cs" />
    <Compile Include="BasicFramework\SoftCacheArrayBase.cs" />
    <Compile Include="BasicFramework\SoftCacheArrayLong.cs" />
    <Compile Include="BasicFramework\SoftCacheArrayInt.cs" />
    <Compile Include="BasicFramework\Exception`1.cs" />
    <Compile Include="BasicFramework\ExceptionArgs.cs" />
    <Compile Include="BasicFramework\SoftMail.cs" />
    <Compile Include="BasicFramework\SoftMsgQueue`1.cs" />
    <Compile Include="BasicFramework\MessageBoard.cs" />
    <Compile Include="BasicFramework\SoftNumericalOrder.cs" />
    <Compile Include="BasicFramework\SoftIncrementCount.cs" />
    <Compile Include="BasicFramework\GraphDirection.cs" />
    <Compile Include="BasicFramework\Paintdata.cs" />
    <Compile Include="BasicFramework\GraphicRender.cs" />
    <Compile Include="BasicFramework\SoftPainting.cs" />
    <Compile Include="BasicFramework\SoftSecurity.cs" />
    <Compile Include="BasicFramework\SoftSqlOperate.cs" />
    <Compile Include="BasicFramework\ISqlDataType.cs" />
    <Compile Include="BasicFramework\SystemVersion.cs" />
    <Compile Include="BasicFramework\VersionInfo.cs" />
    <Compile Include="BasicFramework\SoftZipped.cs" />
    <Compile Include="Algorithms\PID\PIDHelper.cs" />
    <Compile Include="Algorithms\PID\PidMode.cs" />
    <Compile Include="Algorithms\Fourier\FFTFilter.cs" />
    <Compile Include="Algorithms\Fourier\FFTHelper.cs" />
    <Compile Include="Algorithms\ConnectPool\DeviceCommConnector.cs" />
    <Compile Include="Algorithms\ConnectPool\ConnectPool`1.cs" />
    <Compile Include="Algorithms\ConnectPool\IConnector.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="BasicFramework\FormAuthorize.resx" />
    <EmbeddedResource Include="BasicFramework\FormPopup.resx" />
    <EmbeddedResource Include="LogNet\FormLogNetView.resx" />
    <EmbeddedResource Include="LogNet\LogNetAnalysisControl.resx" />
    <EmbeddedResource Include="Properties\Resources.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>