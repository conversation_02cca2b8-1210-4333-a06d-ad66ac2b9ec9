﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.MelsecFxLinksAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>三菱的FxLinks协议信息</summary>
public class MelsecFxLinksAddress : DeviceAddressDataBase
{
  /// <summary>当前的地址类型信息</summary>
  public string TypeCode { get; set; }

  /// <inheritdoc />
  public override void Parse(string address, ushort length) => base.Parse(address, length);

  /// <inheritdoc />
  public override string ToString()
  {
    switch (this.TypeCode)
    {
      case "X":
      case "Y":
        return this.TypeCode + Convert.ToString(this.AddressStart, 8).PadLeft(this.AddressStart >= 10000 ? 6 : 4, '0');
      default:
        return this.TypeCode + this.AddressStart.ToString("D" + ((this.AddressStart >= 10000 ? 7 : 5) - this.TypeCode.Length).ToString());
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Address.MelsecFxLinksAddress.Parse(System.String,System.UInt16)" />
  public static OperateResult<MelsecFxLinksAddress> ParseFrom(string address)
  {
    return MelsecFxLinksAddress.ParseFrom(address, (ushort) 0);
  }

  /// <summary>从三菱FxLinks协议里面解析出实际的地址信息</summary>
  /// <param name="address">三菱的地址信息</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>解析结果信息</returns>
  public static OperateResult<MelsecFxLinksAddress> ParseFrom(string address, ushort length)
  {
    MelsecFxLinksAddress melsecFxLinksAddress = new MelsecFxLinksAddress();
    melsecFxLinksAddress.Length = length;
    try
    {
      switch (address[0])
      {
        case 'C':
        case 'c':
          if (address[1] == 'S' || address[1] == 's')
          {
            melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(2), 10);
            melsecFxLinksAddress.TypeCode = "CS";
            break;
          }
          melsecFxLinksAddress.AddressStart = address[1] == 'N' || address[1] == 'n' ? (int) Convert.ToUInt16(address.Substring(2), 10) : throw new Exception(StringResources.Language.NotSupportedDataType);
          melsecFxLinksAddress.TypeCode = "CN";
          break;
        case 'D':
        case 'd':
          melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          melsecFxLinksAddress.TypeCode = "D";
          break;
        case 'M':
        case 'm':
          melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          melsecFxLinksAddress.TypeCode = "M";
          break;
        case 'R':
        case 'r':
          melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          melsecFxLinksAddress.TypeCode = "R";
          break;
        case 'S':
        case 's':
          melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          melsecFxLinksAddress.TypeCode = "S";
          break;
        case 'T':
        case 't':
          if (address[1] == 'S' || address[1] == 's')
          {
            melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(2), 10);
            melsecFxLinksAddress.TypeCode = "TS";
            break;
          }
          melsecFxLinksAddress.AddressStart = address[1] == 'N' || address[1] == 'n' ? (int) Convert.ToUInt16(address.Substring(2), 10) : throw new Exception(StringResources.Language.NotSupportedDataType);
          melsecFxLinksAddress.TypeCode = "TN";
          break;
        case 'X':
        case 'x':
          melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 8);
          melsecFxLinksAddress.TypeCode = "X";
          break;
        case 'Y':
        case 'y':
          melsecFxLinksAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 8);
          melsecFxLinksAddress.TypeCode = "Y";
          break;
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
      return OperateResult.CreateSuccessResult<MelsecFxLinksAddress>(melsecFxLinksAddress);
    }
    catch (Exception ex)
    {
      return new OperateResult<MelsecFxLinksAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }
}
