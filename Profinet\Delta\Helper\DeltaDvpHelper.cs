﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Delta.Helper.DeltaDvpHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.ModBus;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Delta.Helper;

/// <summary>
/// 台达PLC的相关的帮助类，公共的地址解析的方法。<br />
/// Delta PLC related help classes, public address resolution methods.
/// </summary>
public class DeltaDvpHelper
{
  private static int TransDAdressToModbusAddress(string address)
  {
    int int32 = Convert.ToInt32(address);
    return int32 >= 4096 /*0x1000*/ ? int32 - 4096 /*0x1000*/ + 36864 /*0x9000*/ : int32 + 4096 /*0x1000*/;
  }

  /// <summary>
  /// 根据台达PLC的地址，解析出转换后的modbus协议信息，适用DVP系列，当前的地址仍然支持站号指定，例如s=2;D100<br />
  /// According to the address of Delta PLC, the converted modbus protocol information is parsed out, applicable to DVP series,
  /// the current address still supports station number designation, such as s=2;D100
  /// </summary>
  /// <param name="address">台达plc的地址信息</param>
  /// <param name="modbusCode">原始的对应的modbus信息</param>
  /// <returns>还原后的modbus地址</returns>
  public static OperateResult<string> ParseDeltaDvpAddress(string address, byte modbusCode)
  {
    try
    {
      string station = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        station = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWithAndNumber("S"))
          return OperateResult.CreateSuccessResult<string>(station + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWithAndNumber("X"))
          return OperateResult.CreateSuccessResult<string>($"{station}x=2;{(Convert.ToInt32(address.Substring(1), 8) + 1024 /*0x0400*/).ToString()}");
        if (address.StartsWithAndNumber("Y"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1), 8) + 1280 /*0x0500*/).ToString());
        if (address.StartsWithAndNumber("T"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 1536 /*0x0600*/).ToString());
        if (address.StartsWithAndNumber("C"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 3584 /*0x0E00*/).ToString());
        if (address.StartsWithAndNumber("M"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 >= 1536 /*0x0600*/ ? OperateResult.CreateSuccessResult<string>(station + (int32 - 1536 /*0x0600*/ + 45056 /*0xB000*/).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 + 2048 /*0x0800*/).ToString());
        }
        string newAddress;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[1]
        {
          "D"
        }, new int[1], new Func<string, int>(DeltaDvpHelper.TransDAdressToModbusAddress), out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
      }
      else
      {
        if (address.StartsWithAndNumber("D"))
          return OperateResult.CreateSuccessResult<string>(station + DeltaDvpHelper.TransDAdressToModbusAddress(address.Substring(1)).ToString());
        if (address.StartsWithAndNumber("C"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 >= 200 ? OperateResult.CreateSuccessResult<string>(station + (int32 - 200 + 3784).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 + 3584 /*0x0E00*/).ToString());
        }
        if (address.StartsWithAndNumber("T"))
          return OperateResult.CreateSuccessResult<string>(station + (Convert.ToInt32(address.Substring(1)) + 1536 /*0x0600*/).ToString());
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  /// <summary>读取台达PLC的bool变量，重写了读M地址时，跨区域读1536地址时，将会分割多次读取</summary>
  /// <param name="readBoolFunc">底层基础的读取方法</param>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>读取的结果</returns>
  public static OperateResult<bool[]> ReadBool(
    Func<string, ushort, OperateResult<bool[]>> readBoolFunc,
    string address,
    ushort length)
  {
    string str = string.Empty;
    OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
    if (parameter.IsSuccess)
      str = $"s={parameter.Content};";
    int result;
    if (!address.StartsWith("M") || !int.TryParse(address.Substring(1), out result) || result >= 1536 /*0x0600*/ || result + (int) length <= 1536 /*0x0600*/)
      return readBoolFunc(address, length);
    ushort num1 = (ushort) (1536 /*0x0600*/ - result);
    ushort num2 = (ushort) ((uint) length - (uint) num1);
    OperateResult<bool[]> operateResult1 = readBoolFunc(str + address, num1);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<bool[]> operateResult2 = readBoolFunc(str + "M1536", num2);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    return OperateResult.CreateSuccessResult<bool[]>(SoftBasic.SpliceArray<bool>(operateResult1.Content, operateResult2.Content));
  }

  /// <summary>写入台达PLC的bool数据，当发现是M类型的数据，并且地址出现跨1536时，进行切割写入操作</summary>
  /// <param name="writeBoolFunc">底层的写入操作方法</param>
  /// <param name="address">PLC的起始地址信息</param>
  /// <param name="value">等待写入的数据信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    Func<string, bool[], OperateResult> writeBoolFunc,
    string address,
    bool[] value)
  {
    string str = string.Empty;
    OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
    if (parameter.IsSuccess)
      str = $"s={parameter.Content};";
    int result;
    if (!address.StartsWith("M") || !int.TryParse(address.Substring(1), out result) || result >= 1536 /*0x0600*/ || result + value.Length <= 1536 /*0x0600*/)
      return writeBoolFunc(address, value);
    ushort length = (ushort) (1536 /*0x0600*/ - result);
    OperateResult operateResult1 = writeBoolFunc(str + address, value.SelectBegin<bool>((int) length));
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult operateResult2 = writeBoolFunc(str + "M1536", value.RemoveBegin<bool>((int) length));
    return !operateResult2.IsSuccess ? operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <summary>读取台达PLC的原始字节变量，重写了读D地址时，跨区域读4096地址时，将会分割多次读取</summary>
  /// <param name="readFunc">底层基础的读取方法</param>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>读取的结果</returns>
  public static OperateResult<byte[]> Read(
    Func<string, ushort, OperateResult<byte[]>> readFunc,
    string address,
    ushort length)
  {
    string str = string.Empty;
    OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
    if (parameter.IsSuccess)
      str = $"s={parameter.Content};";
    int result;
    if (!address.StartsWith("D") || !int.TryParse(address.Substring(1), out result) || result >= 4096 /*0x1000*/ || result + (int) length <= 4096 /*0x1000*/)
      return readFunc(address, length);
    ushort num1 = (ushort) (4096 /*0x1000*/ - result);
    ushort num2 = (ushort) ((uint) length - (uint) num1);
    OperateResult<byte[]> operateResult1 = readFunc(str + address, num1);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = readFunc(str + "D4096", num2);
    if (!operateResult2.IsSuccess)
      return operateResult2;
    return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(operateResult1.Content, operateResult2.Content));
  }

  /// <summary>写入台达PLC的原始字节数据，当发现是D类型的数据，并且地址出现跨4096时，进行切割写入操作</summary>
  /// <param name="writeFunc">底层的写入操作方法</param>
  /// <param name="address">PLC的起始地址信息</param>
  /// <param name="value">等待写入的数据信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    Func<string, byte[], OperateResult> writeFunc,
    string address,
    byte[] value)
  {
    string str = string.Empty;
    OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
    if (parameter.IsSuccess)
      str = $"s={parameter.Content};";
    int result;
    if (!address.StartsWith("D") || !int.TryParse(address.Substring(1), out result) || result >= 4096 /*0x1000*/ || result + value.Length / 2 <= 4096 /*0x1000*/)
      return writeFunc(address, value);
    ushort num = (ushort) (4096 /*0x1000*/ - result);
    OperateResult operateResult1 = writeFunc(str + address, value.SelectBegin<byte>((int) num * 2));
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult operateResult2 = writeFunc(str + "D4096", value.RemoveBegin<byte>((int) num * 2));
    return !operateResult2.IsSuccess ? operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Delta.Helper.DeltaDvpHelper.ReadBool(System.Func{System.String,System.UInt16,HslCommunication.OperateResult{System.Boolean[]}},System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    Func<string, ushort, Task<OperateResult<bool[]>>> readBoolFunc,
    string address,
    ushort length)
  {
    string station = string.Empty;
    OperateResult<int> stationPara = HslHelper.ExtractParameter(ref address, "s");
    if (stationPara.IsSuccess)
      station = $"s={stationPara.Content};";
    int add;
    if (address.StartsWith("M") && int.TryParse(address.Substring(1), out add) && add < 1536 /*0x0600*/ && add + (int) length > 1536 /*0x0600*/)
    {
      ushort len1 = (ushort) (1536 /*0x0600*/ - add);
      ushort len2 = (ushort) ((uint) length - (uint) len1);
      OperateResult<bool[]> read1 = await readBoolFunc(station + address, len1);
      if (!read1.IsSuccess)
        return read1;
      OperateResult<bool[]> read2 = await readBoolFunc(station + "M1536", len2);
      if (!read2.IsSuccess)
        return read2;
      return OperateResult.CreateSuccessResult<bool[]>(SoftBasic.SpliceArray<bool>(read1.Content, read2.Content));
    }
    OperateResult<bool[]> operateResult = await readBoolFunc(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Delta.Helper.DeltaDvpHelper.Write(System.Func{System.String,System.Boolean[],HslCommunication.OperateResult},System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    Func<string, bool[], Task<OperateResult>> writeBoolFunc,
    string address,
    bool[] value)
  {
    string station = string.Empty;
    OperateResult<int> stationPara = HslHelper.ExtractParameter(ref address, "s");
    if (stationPara.IsSuccess)
      station = $"s={stationPara.Content};";
    int add;
    if (address.StartsWith("M") && int.TryParse(address.Substring(1), out add) && add < 1536 /*0x0600*/ && add + value.Length > 1536 /*0x0600*/)
    {
      ushort len1 = (ushort) (1536 /*0x0600*/ - add);
      OperateResult write1 = await writeBoolFunc(station + address, value.SelectBegin<bool>((int) len1));
      if (!write1.IsSuccess)
        return write1;
      OperateResult write2 = await writeBoolFunc(station + "M1536", value.RemoveBegin<bool>((int) len1));
      return write2.IsSuccess ? OperateResult.CreateSuccessResult() : write2;
    }
    OperateResult operateResult = await writeBoolFunc(address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Delta.Helper.DeltaDvpHelper.Read(System.Func{System.String,System.UInt16,HslCommunication.OperateResult{System.Byte[]}},System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    Func<string, ushort, Task<OperateResult<byte[]>>> readFunc,
    string address,
    ushort length)
  {
    string station = string.Empty;
    OperateResult<int> stationPara = HslHelper.ExtractParameter(ref address, "s");
    if (stationPara.IsSuccess)
      station = $"s={stationPara.Content};";
    int add;
    if (address.StartsWith("D") && int.TryParse(address.Substring(1), out add) && add < 4096 /*0x1000*/ && add + (int) length > 4096 /*0x1000*/)
    {
      ushort len1 = (ushort) (4096 /*0x1000*/ - add);
      ushort len2 = (ushort) ((uint) length - (uint) len1);
      OperateResult<byte[]> read1 = await readFunc(station + address, len1);
      if (!read1.IsSuccess)
        return read1;
      OperateResult<byte[]> read2 = await readFunc(station + "D4096", len2);
      if (!read2.IsSuccess)
        return read2;
      return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(read1.Content, read2.Content));
    }
    OperateResult<byte[]> operateResult = await readFunc(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Delta.Helper.DeltaDvpHelper.Write(System.Func{System.String,System.Byte[],HslCommunication.OperateResult},System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    Func<string, byte[], Task<OperateResult>> writeFunc,
    string address,
    byte[] value)
  {
    string station = string.Empty;
    OperateResult<int> stationPara = HslHelper.ExtractParameter(ref address, "s");
    if (stationPara.IsSuccess)
      station = $"s={stationPara.Content};";
    int add;
    if (address.StartsWith("D") && int.TryParse(address.Substring(1), out add) && add < 4096 /*0x1000*/ && add + value.Length / 2 > 4096 /*0x1000*/)
    {
      ushort len1 = (ushort) (4096 /*0x1000*/ - add);
      OperateResult write1 = await writeFunc(station + address, value.SelectBegin<byte>((int) len1 * 2));
      if (!write1.IsSuccess)
        return write1;
      OperateResult write2 = await writeFunc(station + "D4096", value.RemoveBegin<byte>((int) len1 * 2));
      return write2.IsSuccess ? OperateResult.CreateSuccessResult() : write2;
    }
    OperateResult operateResult = await writeFunc(address, value);
    return operateResult;
  }
}
