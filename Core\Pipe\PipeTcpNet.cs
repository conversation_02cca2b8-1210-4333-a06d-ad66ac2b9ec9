﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeTcpNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using System;
using System.Net;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// 用于TCP/IP协议的传输管道信息<br />
/// Transport pipe information of the IP protocol
/// </summary>
public class PipeTcpNet : CommunicationPipe
{
  /// <summary>配置的远程主机的名称，有可能是网址，也可能是IP</summary>
  protected string host = "127.0.0.1";
  private string ipAddress = "127.0.0.1";
  private int[] _port = new int[1]{ 2000 };
  private int indexPort = -1;
  private Socket socket;
  private int connectTimeOut = 10000;
  private int recvTimeOutTick = 0;
  private Func<IAsyncResult, int> socketEndMethod;

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSslNet.#ctor(System.Boolean)" />
  public PipeTcpNet()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSslNet.#ctor(System.String,System.Int32,System.Boolean)" />
  public PipeTcpNet(string ipAddress, int port)
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSslNet.#ctor(System.Net.Sockets.Socket,System.Net.IPEndPoint,System.Boolean)" />
  public PipeTcpNet(Socket socket, IPEndPoint iPEndPoint)
  {
    this.Socket = socket;
    this.IpAddress = iPEndPoint.Address.ToString();
    this.Port = iPEndPoint.Port;
  }

  /// <summary>
  /// 获取或设置绑定的本地的IP地址和端口号信息，如果端口设置为0，代表任何可用的端口<br />
  /// Get or set the bound local IP address and port number information, if the port is set to 0, it means any available port
  /// </summary>
  /// <remarks>
  /// 默认为NULL, 也即是不绑定任何本地的IP及端口号信息，使用系统自动分配的方式。<br />
  /// The default is NULL, which means that no local IP and port number information are bound, and the system automatically assigns it.
  /// </remarks>
  public IPEndPoint LocalBinding { get; set; }

  /// <summary>
  /// 获取或是设置远程服务器的IP地址，如果是本机测试，那么需要设置为127.0.0.1 <br />
  /// Get or set the IP address of the remote server. If it is a local test, then it needs to be set to 127.0.0.1
  /// </summary>
  /// <remarks>
  /// 最好实在初始化的时候进行指定，当使用短连接的时候，支持动态更改，切换；当使用长连接后，无法动态更改<br />
  /// 支持使用域名的网址方式，例如：www.hslcommunication.cn
  /// </remarks>
  /// <example>
  /// 以下举例modbus-tcp的短连接及动态更改ip地址的示例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkDoubleBase.cs" region="IpAddressExample" title="IpAddress示例" />
  /// </example>
  public string IpAddress
  {
    get => this.ipAddress;
    set
    {
      this.host = value;
      this.ipAddress = HslHelper.GetIpAddressFromInput(value);
    }
  }

  /// <summary>
  /// 获取当前设置的远程的地址，可能是IP地址，也可能是网址，也就是初始设置的地址信息<br />
  /// Obtain the address of the remote address that is currently set, which may be an IP address or a web address, that is, the address information that is initially set
  /// </summary>
  public string Host => this.host;

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkServerBase.SocketKeepAliveTime" />
  public int SocketKeepAliveTime { get; set; } = -1;

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.Port" />
  public int Port
  {
    get
    {
      if (this._port.Length == 1)
        return this._port[0];
      int index = this.indexPort;
      if (index < 0 || index >= this._port.Length)
        index = 0;
      return this._port[index];
    }
    set
    {
      if (this._port.Length == 1)
      {
        this._port[0] = value;
      }
      else
      {
        int index = this.indexPort;
        if (index < 0 || index >= this._port.Length)
          index = 0;
        this._port[index] = value;
      }
    }
  }

  /// <summary>
  /// 获取或设置当前的客户端用于服务器连接的套接字。<br />
  /// Gets or sets the socket currently used by the client for server connection.
  /// </summary>
  public Socket Socket
  {
    get => this.socket;
    set
    {
      this.socket = value;
      if (this.socket != null)
        this.socketEndMethod = new Func<IAsyncResult, int>(this.socket.EndReceive);
      else
        this.socketEndMethod = (Func<IAsyncResult, int>) null;
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.ReceiveTimeOut" />
  public int ConnectTimeOut
  {
    get => this.connectTimeOut;
    set => this.connectTimeOut = value;
  }

  /// <summary>
  /// 当因为接收超时而关闭连接的时候，获取或设置超过指定次数的时候，是否需要重新连接<br />
  /// When the connection is closed due to a reception timeout, and when the number of retrieves or Settings exceeds the specified limit, is it necessary to reconnect
  /// </summary>
  /// <remarks>
  /// 需要注意的是，当异步方法通信的时候，是不支持的，异步方法会自动处理连接的关闭和重新连接，如果小于0，则接收数据超时的次数无论多少，都不再关闭连接<br />
  /// </remarks>
  public int CloseOnRecvTimeOutTick { get; set; } = 1;

  /// <summary>
  /// 设置多个可选的端口号信息，例如在三菱的PLC里，支持配置多个端口号，当一个网络发生异常时，立即切换端口号连接读写，提升系统的稳定性<br />
  /// Set multiple optional port number information. For example, in Mitsubishi PLC, it supports to configure multiple port numbers.
  /// When an abnormality occurs in a network, the port number is immediately switched to connect to read and write to improve the stability of the system.
  /// </summary>
  /// <param name="ports">端口号数组信息</param>
  public void SetMultiPorts(int[] ports)
  {
    if (ports == null || ports.Length == 0)
      return;
    this._port = ports;
    this.indexPort = -1;
  }

  /// <summary>
  /// 获取当前的远程连接信息，如果端口号设置了可选的数组，那么每次获取对象就会发生端口号切换的操作。<br />
  /// Get the current remote connection information. If the port number is set to an optional array, the port number switching operation will occur every time the object is obtained.
  /// </summary>
  /// <returns>远程连接的对象</returns>
  public IPEndPoint GetConnectIPEndPoint()
  {
    if (this._port.Length == 1)
      return new IPEndPoint(IPAddress.Parse(this.IpAddress), this._port[0]);
    this.ChangePorts();
    int port = this._port[this.indexPort];
    return new IPEndPoint(IPAddress.Parse(this.IpAddress), port);
  }

  /// <summary>
  /// 变更当前的端口号信息，如果设置了多个端口号的话，就切换其他可用的端口<br />
  /// Change the current port number information, and if multiple port numbers are set, switch to other available ports
  /// </summary>
  private void ChangePorts()
  {
    if (this._port.Length == 1)
      return;
    if (this.indexPort < this._port.Length - 1)
      ++this.indexPort;
    else
      this.indexPort = 0;
  }

  /// <inheritdoc />
  public override bool IsConnectError() => this.socket == null || base.IsConnectError();

  /// <summary>当管道打开成功的时候执行的事件，如果返回失败，则管道的打开操作返回失败</summary>
  /// <param name="socket">通信的套接字</param>
  /// <returns>是否真的打开成功</returns>
  protected virtual OperateResult OnCommunicationOpen(Socket socket)
  {
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult<bool> OpenCommunication()
  {
    if (!this.IsConnectError())
      return OperateResult.CreateSuccessResult<bool>(false);
    NetSupport.CloseSocket(this.socket);
    OperateResult<Socket> socketAndConnect = NetSupport.CreateSocketAndConnect(this.GetConnectIPEndPoint(), this.ConnectTimeOut, this.LocalBinding);
    if (!socketAndConnect.IsSuccess)
      return new OperateResult<bool>(-this.IncrConnectErrorCount(), socketAndConnect.Message);
    OperateResult operateResult = this.OnCommunicationOpen(socketAndConnect.Content);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<bool>();
    if (this.SocketKeepAliveTime > 0)
      socketAndConnect.Content.SetKeepAlive(this.SocketKeepAliveTime, this.SocketKeepAliveTime);
    this.ResetConnectErrorCount();
    this.Socket = socketAndConnect.Content;
    return OperateResult.CreateSuccessResult<bool>(true);
  }

  /// <inheritdoc />
  public override OperateResult CloseCommunication()
  {
    NetSupport.CloseSocket(this.socket);
    this.Socket = (Socket) null;
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult Send(byte[] data, int offset, int size)
  {
    OperateResult operateResult = NetSupport.SocketSend(this.socket, data, offset, size);
    if (operateResult.IsSuccess || operateResult.ErrorCode != NetSupport.SocketErrorCode)
      return operateResult;
    this.CloseCommunication();
    return (OperateResult) new OperateResult<byte[]>(-this.IncrConnectErrorCount(), operateResult.Message);
  }

  /// <inheritdoc />
  public override OperateResult<int> Receive(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    OperateResult<int> operateResult = NetSupport.SocketReceive(this.socket, buffer, offset, length, timeOut, reportProgress);
    if (!operateResult.IsSuccess && operateResult.ErrorCode == NetSupport.SocketErrorCode)
    {
      if (operateResult.Content == int.MaxValue)
      {
        if (this.CloseOnRecvTimeOutTick < 0 || Interlocked.Increment(ref this.recvTimeOutTick) < this.CloseOnRecvTimeOutTick)
          return new OperateResult<int>(-1, operateResult.Message);
        this.CloseCommunication();
        Interlocked.Exchange(ref this.recvTimeOutTick, 0);
      }
      else
        this.CloseCommunication();
      return new OperateResult<int>(-this.IncrConnectErrorCount(), operateResult.Message);
    }
    if (operateResult.IsSuccess)
      Interlocked.Exchange(ref this.recvTimeOutTick, 0);
    return operateResult;
  }

  /// <inheritdoc />
  public override OperateResult StartReceiveBackground(INetMessage netMessage)
  {
    if (this.UseServerActivePush)
    {
      OperateResult result = this.socket.BeginReceiveResult(new AsyncCallback(this.ServerSocketActivePushAsync), (object) netMessage);
      if (!result.IsSuccess)
        return result;
    }
    return base.StartReceiveBackground(netMessage);
  }

  private async void ServerSocketActivePushAsync(IAsyncResult ar)
  {
    if (!(ar.AsyncState is INetMessage netMessage))
    {
      netMessage = (INetMessage) null;
    }
    else
    {
      OperateResult<int> endResult = this.socket.EndReceiveResult(ar);
      if (!endResult.IsSuccess)
      {
        this.IncrConnectErrorCount();
        netMessage = (INetMessage) null;
      }
      else
      {
        OperateResult<byte[]> receive = await this.ReceiveMessageAsync(netMessage, (byte[]) null, false).ConfigureAwait(false);
        if (!receive.IsSuccess)
        {
          this.IncrConnectErrorCount();
          netMessage = (INetMessage) null;
        }
        else
        {
          if (this.DecideWhetherQAMessageFunction != null)
          {
            if (this.DecideWhetherQAMessageFunction((CommunicationPipe) this, receive))
              this.SetBufferQA(receive.Content);
          }
          else
            this.SetBufferQA(receive.Content);
          OperateResult receiveAgain = this.socket.BeginReceiveResult(new AsyncCallback(this.ServerSocketActivePushAsync), (object) netMessage);
          if (!receiveAgain.IsSuccess)
            this.IncrConnectErrorCount();
          endResult = (OperateResult<int>) null;
          receive = (OperateResult<byte[]>) null;
          receiveAgain = (OperateResult) null;
          netMessage = (INetMessage) null;
        }
      }
    }
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool>> OpenCommunicationAsync()
  {
    if (!this.IsConnectError())
      return OperateResult.CreateSuccessResult<bool>(false);
    NetSupport.CloseSocket(this.socket);
    IPEndPoint endPoint = this.GetConnectIPEndPoint();
    OperateResult<Socket> connect = await NetSupport.CreateSocketAndConnectAsync(endPoint, this.ConnectTimeOut, this.LocalBinding).ConfigureAwait(false);
    if (!connect.IsSuccess)
      return new OperateResult<bool>(-this.IncrConnectErrorCount(), connect.Message);
    OperateResult onOpen = this.OnCommunicationOpen(connect.Content);
    if (!onOpen.IsSuccess)
      return onOpen.ConvertFailed<bool>();
    if (this.SocketKeepAliveTime > 0)
      connect.Content.SetKeepAlive(this.SocketKeepAliveTime, this.SocketKeepAliveTime);
    this.ResetConnectErrorCount();
    this.Socket = connect.Content;
    return OperateResult.CreateSuccessResult<bool>(true);
  }

  /// <inheritdoc />
  public override async Task<OperateResult> CloseCommunicationAsync()
  {
    NetSupport.CloseSocket(this.socket);
    this.Socket = (Socket) null;
    OperateResult operateResult = await Task.FromResult<OperateResult>(OperateResult.CreateSuccessResult());
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> SendAsync(byte[] data, int offset, int size)
  {
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = NetSupport.SocketSendAsync(this.socket, data, offset, size).ConfigureAwait(false);
    OperateResult send = await configuredTaskAwaitable;
    if (send.IsSuccess || send.ErrorCode != NetSupport.SocketErrorCode)
      return send;
    configuredTaskAwaitable = this.CloseCommunicationAsync().ConfigureAwait(false);
    OperateResult operateResult = await configuredTaskAwaitable;
    return (OperateResult) new OperateResult<byte[]>(-this.IncrConnectErrorCount(), send.Message);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<int>> ReceiveAsync(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    Func<IAsyncResult, int> endMethodTmp = this.socketEndMethod;
    if (endMethodTmp == null && this.socket != null)
      endMethodTmp = new Func<IAsyncResult, int>(this.socket.EndReceive);
    OperateResult<int> receive = await NetSupport.SocketReceiveAsync2(this.socket, buffer, offset, length, timeOut, reportProgress, endMethodTmp).ConfigureAwait(false);
    if (receive.IsSuccess || receive.ErrorCode != NetSupport.SocketErrorCode)
      return receive;
    OperateResult operateResult = await this.CloseCommunicationAsync().ConfigureAwait(false);
    return new OperateResult<int>(-this.IncrConnectErrorCount(), receive.Message);
  }

  /// <inheritdoc />
  public override string ToString() => $"PipeTcpNet[{this.ipAddress}:{this.Port}]";
}
