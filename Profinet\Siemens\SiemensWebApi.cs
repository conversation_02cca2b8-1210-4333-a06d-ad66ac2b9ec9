﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensWebApi
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Reflection;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>
/// 西门子的基于WebApi协议读写数据对象，支持对PLC的标签进行读取，适用于1500系列，该数据标签需要共享开放出来。<br />
/// Siemens reads and writes data objects based on the WebApi protocol, supports reading PLC tags,
/// and is suitable for the 1500 series. The data tags need to be shared and opened.
/// </summary>
public class SiemensWebApi : DeviceWebApi
{
  private string rawUrl = "api/jsonrpc";
  private string token = string.Empty;
  private SoftIncrementCount incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);

  /// <summary>
  /// 实例化一个默认的西门子WebApi通信对象<br />
  /// Instantiate a default Siemens WebApi communication object
  /// </summary>
  public SiemensWebApi()
    : this("127.0.0.1")
  {
  }

  /// <summary>
  /// 使用指定的ip地址及端口号来实例化一个对象，端口号默认使用443，如果是http访问，使用80端口号<br />
  /// Use the specified ip address and port number to instantiate an object, the port number is 443 by default, if it is http access, port 80 is used
  /// </summary>
  /// <param name="ipAddress">ip地址信息</param>
  /// <param name="port">端口号信息</param>
  public SiemensWebApi(string ipAddress, int port = 443)
    : base(ipAddress, port)
  {
    this.WordLength = (ushort) 2;
    this.UseHttps = true;
    this.DefaultContentType = "application/json";
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
  }

  /// <inheritdoc />
  protected override void AddRequestHeaders(HttpContentHeaders headers)
  {
    if (string.IsNullOrEmpty(this.token))
      return;
    headers.Add("X-Auth-Token", this.token);
  }

  /// <summary>
  /// 根据设置好的用户名和密码信息，登录远程的PLC，返回是否登录成功！在读写之前，必须成功调用当前的方法，获取到token，否则无法进行通信。<br />
  /// According to the set user name and password information, log in to the remote PLC, and return whether the login is successful!
  /// Before reading and writing, the current method must be successfully called to obtain the token,
  /// otherwise communication cannot be carried out.
  /// </summary>
  /// <returns>是否连接成功</returns>
  public OperateResult ConnectServer()
  {
    OperateResult<string> operateResult = this.Post(this.rawUrl, SiemensWebApi.BuildConnectBody(this.incrementCount.GetCurrentValue(), this.UserName, this.Password).ToString());
    return !operateResult.IsSuccess ? (OperateResult) operateResult : this.CheckLoginResult(operateResult.Content);
  }

  /// <summary>
  /// 和PLC断开当前的连接信息，主要是使得Token信息失效。<br />
  /// Disconnecting the current connection information from the PLC mainly makes the token information invalid.
  /// </summary>
  /// <returns>是否断开成功</returns>
  public OperateResult ConnectClose() => this.Logout();

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectServer" />
  public async Task<OperateResult> ConnectServerAsync()
  {
    JArray body = SiemensWebApi.BuildConnectBody(this.incrementCount.GetCurrentValue(), this.UserName, this.Password);
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    OperateResult operateResult = read.IsSuccess ? this.CheckLoginResult(read.Content) : (OperateResult) read;
    body = (JArray) null;
    read = (OperateResult<string>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectClose" />
  public async Task<OperateResult> ConnectCloseAsync()
  {
    OperateResult operateResult = await this.LogoutAsync();
    return operateResult;
  }

  private OperateResult CheckLoginResult(string response)
  {
    try
    {
      JObject json = (JObject) JArray.Parse(response)[0];
      OperateResult result = SiemensWebApi.CheckErrorResult(json);
      if (!result.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<JToken>(result);
      if (!json.ContainsKey("result"))
        return new OperateResult($"Can't find result key and none token, login failed:{Environment.NewLine}{response}");
      this.token = (json["result"] as JObject).Value<string>((object) "token");
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return new OperateResult($"CheckLoginResult failed:{ex.Message}{Environment.NewLine}{response}");
    }
  }

  /// <summary>
  /// 当前PLC的通信令牌，当调用<see cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectServer" />时，会自动获取，当然你也可以手动赋值一个合法的令牌，跳过<see cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectServer" />直接进行读写操作。<br />
  /// The communication token of the current PLC will be automatically obtained when <see cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectServer" /> is called. Of course,
  /// you can also manually assign a valid token, skip <see cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectServer" /> and read it directly Write operation.
  /// </summary>
  public string Token
  {
    get => this.token;
    set => this.token = value;
  }

  /// <summary>
  /// 从PLC中根据输入的数据标签名称，读取出原始的字节数组信息，长度参数无效，需要二次解析<br />
  /// According to the input data tag name, read the original byte array information from the PLC,
  /// the length parameter is invalid, and a second analysis is required
  /// </summary>
  /// <param name="address">标签的地址信息，例如 "全局DB".Static_21</param>
  /// <param name="length">无效的参数</param>
  /// <returns>原始的字节数组信息</returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<string> result = this.Post(this.rawUrl, SiemensWebApi.BuildReadRawBody(this.incrementCount.GetCurrentValue(), address).ToString());
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : SiemensWebApi.CheckReadRawResult(result.Content);
  }

  /// <summary>
  /// 从PLC中根据输入的数据标签名称，按照类型byte进行读取出数据信息<br />
  /// According to the input data tag name from the PLC, read the data information according to the type byte
  /// </summary>
  /// <param name="address">标签的地址信息，例如 "全局DB".Static_21</param>
  /// <returns>包含是否成功的结果对象</returns>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<byte[]> result = this.Read(address, (ushort) 0);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(result.Content.ToBoolArray().SelectBegin<bool>((int) length));
  }

  /// <summary>
  /// 将原始的字节数组信息写入到PLC的指定的数据标签里，写入方式为raw写入，是否写入成功取决于PLC的返回信息<br />
  /// Write the original byte array information to the designated data tag of the PLC.
  /// The writing method is raw writing. Whether the writing is successful depends on the return information of the PLC.
  /// </summary>
  /// <param name="address">标签的地址信息，例如 "全局DB".Static_21</param>
  /// <param name="value">原始的字节数据信息</param>
  /// <returns>是否成功写入PLC的结果对象</returns>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<string> operateResult = this.Post(this.rawUrl, SiemensWebApi.BuildWriteRawBody(this.incrementCount.GetCurrentValue(), address, value).ToString());
    return !operateResult.IsSuccess ? (OperateResult) operateResult : SiemensWebApi.CheckWriteResult(operateResult.Content);
  }

  /// <summary>
  /// 写入<see cref="T:System.Byte" />数组数据，返回是否成功<br />
  /// Write <see cref="T:System.Byte" /> array data, return whether the write was successful
  /// </summary>
  /// <param name="address">标签的地址信息，例如 "全局DB".Static_21</param>
  /// <param name="value">写入值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <summary>
  /// 批量写入<see cref="T:System.Boolean" />数组数据，返回是否成功<br />
  /// Batch write <see cref="T:System.Boolean" /> array data, return whether the write was successful
  /// </summary>
  /// <param name="address">标签的地址信息，例如 "全局DB".Static_21</param>
  /// <param name="value">写入值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    byte[] byteArray = value.ToByteArray();
    return this.Write(address, byteArray);
  }

  /// <summary>
  /// 从PLC中读取字符串内容，需要指定数据标签名称，使用JSON方式读取，所以无论字符串是中英文都是支持读取的。<br />
  /// To read the string content from the PLC, you need to specify the data tag name and use JSON to read it,
  /// so no matter whether the string is in Chinese or English, it supports reading.
  /// </summary>
  /// <param name="address">标签的地址信息，例如 "全局DB".Static_21</param>
  /// <param name="length">无效参数</param>
  /// <returns>包含是否成功的结果对象</returns>
  [HslMqttApi("ReadString", "读取字符串")]
  public override OperateResult<string> ReadString(string address, ushort length)
  {
    OperateResult<string> result1 = this.Post(this.rawUrl, SiemensWebApi.BuildReadJTokenBody(this.incrementCount.GetCurrentValue(), address).ToString());
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<JToken> result2 = SiemensWebApi.CheckAndExtraOneJsonResult(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result2) : OperateResult.CreateSuccessResult<string>(result2.Content.Value<string>());
  }

  /// <summary>
  /// 将字符串信息写入到PLC中，需要指定数据标签名称，如果PLC指定了类型为WString，才支持写入中文，否则会出现乱码。<br />
  /// To write string information into the PLC, you need to specify the data tag name.
  /// If the PLC specifies the type as WString, it supports writing in Chinese, otherwise garbled characters will appear.
  /// </summary>
  /// <param name="address">标签的地址信息，例如 "全局DB".Static_21</param>
  /// <param name="value">字符串数据信息</param>
  /// <returns>是否成功写入</returns>
  [HslMqttApi("WriteString", "")]
  public override OperateResult Write(string address, string value)
  {
    OperateResult<string> operateResult = this.Post(this.rawUrl, SiemensWebApi.BuildWriteJTokenBody(this.incrementCount.GetCurrentValue(), address, (JToken) new JValue(value)).ToString());
    return !operateResult.IsSuccess ? (OperateResult) operateResult : SiemensWebApi.CheckWriteResult(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    JArray body = SiemensWebApi.BuildReadRawBody(this.incrementCount.GetCurrentValue(), address);
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    OperateResult<byte[]> operateResult = read.IsSuccess ? SiemensWebApi.CheckReadRawResult(read.Content) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
    body = (JArray) null;
    read = (OperateResult<string>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 0);
    OperateResult<bool[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray().SelectBegin<bool>((int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ReadString(System.String,System.UInt16)" />
  public override async Task<OperateResult<string>> ReadStringAsync(string address, ushort length)
  {
    JArray body = SiemensWebApi.BuildReadJTokenBody(this.incrementCount.GetCurrentValue(), address);
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    OperateResult<JToken> extra = SiemensWebApi.CheckAndExtraOneJsonResult(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult<string>(extra.Content.Value<string>()) : OperateResult.CreateFailedResult<string>((OperateResult) extra);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    JArray body = SiemensWebApi.BuildWriteRawBody(this.incrementCount.GetCurrentValue(), address, value);
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    OperateResult operateResult = read.IsSuccess ? SiemensWebApi.CheckWriteResult(read.Content) : (OperateResult) read;
    body = (JArray) null;
    read = (OperateResult<string>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    byte[] buffer = value.ToByteArray();
    OperateResult operateResult = await this.WriteAsync(address, buffer);
    buffer = (byte[]) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Write(System.String,System.String)" />
  public override async Task<OperateResult> WriteAsync(string address, string value)
  {
    JArray body = SiemensWebApi.BuildWriteJTokenBody(this.incrementCount.GetCurrentValue(), address, (JToken) new JValue(value));
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    OperateResult operateResult = read.IsSuccess ? SiemensWebApi.CheckWriteResult(read.Content) : (OperateResult) read;
    body = (JArray) null;
    read = (OperateResult<string>) null;
    return operateResult;
  }

  /// <summary>
  /// 读取当前PLC的操作模式，如果读取成功，结果将会是如下值之一：STOP, STARTUP, RUN, HOLD, -<br />
  /// Read the current operating mode of the PLC. If the reading is successful,
  /// the result will be one of the following values: STOP, STARTUP, RUN, HOLD,-
  /// </summary>
  /// <returns>结果对象</returns>
  [HslMqttApi("读取当前PLC的操作模式，如果读取成功，结果将会是如下值之一：STOP, STARTUP, RUN, HOLD, -")]
  public OperateResult<string> ReadOperatingMode()
  {
    OperateResult<string> result1 = this.Post(this.rawUrl, SiemensWebApi.BuildRequestBody("Plc.ReadOperatingMode", (JObject) null, this.incrementCount.GetCurrentValue()).ToString());
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<JToken> result2 = SiemensWebApi.CheckAndExtraOneJsonResult(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result2) : OperateResult.CreateSuccessResult<string>(result2.Content.Value<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ReadOperatingMode" />
  public async Task<OperateResult<string>> ReadOperatingModeAsync()
  {
    JArray body = SiemensWebApi.BuildRequestBody("Plc.ReadOperatingMode", (JObject) null, this.incrementCount.GetCurrentValue());
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    OperateResult<JToken> extra = SiemensWebApi.CheckAndExtraOneJsonResult(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult<string>(extra.Content.Value<string>()) : OperateResult.CreateFailedResult<string>((OperateResult) extra);
  }

  /// <summary>
  /// <b>[商业授权]</b> 从PLC读取多个地址的数据信息，每个地址的数据类型可以不一致，需要自动从<see cref="T:Newtonsoft.Json.Linq.JToken" />中提取出正确的数据<br />
  /// <b>[Authorization]</b> Read the data information of multiple addresses from the PLC, the data type of each address can be inconsistent,
  /// you need to automatically extract the correct data from <see cref="T:Newtonsoft.Json.Linq.JToken" />
  /// </summary>
  /// <remarks>一旦中间有一个地址失败了，本方法就会返回失败，所以在调用本方法时，需要确保所有的地址正确。</remarks>
  /// <param name="address">"全局DB".Static_21</param>
  /// <returns>返回是否读取成功的结果对象</returns>
  [HslMqttApi("ReadJTokens", "从PLC读取多个地址的数据信息，每个地址的数据类型可以不一致")]
  public OperateResult<JToken[]> Read(string[] address)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<JToken[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<string> result = this.Post(this.rawUrl, SiemensWebApi.BuildReadJTokenBody(this.incrementCount.GetCurrentValue(), address).ToString());
    return !result.IsSuccess ? OperateResult.CreateFailedResult<JToken[]>((OperateResult) result) : SiemensWebApi.CheckAndExtraJsonResult(result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Read(System.String[])" />
  public async Task<OperateResult<JToken[]>> ReadAsync(string[] address)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<JToken[]>(StringResources.Language.InsufficientPrivileges);
    JArray body = SiemensWebApi.BuildReadJTokenBody(this.incrementCount.GetCurrentValue(), address);
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    return read.IsSuccess ? SiemensWebApi.CheckAndExtraJsonResult(read.Content) : OperateResult.CreateFailedResult<JToken[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadDateTime(System.String)" />
  [HslMqttApi("ReadDateTime", "读取PLC的时间格式的数据，这个格式是s7格式的一种")]
  public OperateResult<DateTime> ReadDateTime(string address)
  {
    OperateResult<byte[]> result = this.Read(address, (ushort) 8);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<DateTime>((OperateResult) result) : SiemensDateTime.FromByteArray(result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.DateTime)" />
  [HslMqttApi("WriteDateTime", "写入PLC的时间格式的数据，这个格式是s7格式的一种")]
  public OperateResult Write(string address, DateTime dateTime)
  {
    return this.Write(address, SiemensDateTime.ToByteArray(dateTime));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ReadDateTime(System.String)" />
  public async Task<OperateResult<DateTime>> ReadDateTimeAsync(string address)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 8);
    OperateResult<DateTime> operateResult = read.IsSuccess ? SiemensDateTime.FromByteArray(read.Content) : OperateResult.CreateFailedResult<DateTime>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Write(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteAsync(string address, DateTime dateTime)
  {
    OperateResult operateResult = await this.WriteAsync(address, SiemensDateTime.ToByteArray(dateTime));
    return operateResult;
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取PLC的RPC接口的版本号信息<br />
  /// <b>[Authorization]</b> Read the version number information of the PLC's RPC interface
  /// </summary>
  /// <returns>包含是否成功的结果对象</returns>
  [HslMqttApi("读取PLC的RPC接口的版本号信息")]
  public OperateResult<double> ReadVersion()
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<double>(StringResources.Language.InsufficientPrivileges);
    OperateResult<string> result1 = this.Post(this.rawUrl, SiemensWebApi.BuildRequestBody("Api.Version", (JObject) null, this.incrementCount.GetCurrentValue()).ToString());
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) result1);
    OperateResult<JToken> result2 = SiemensWebApi.CheckAndExtraOneJsonResult(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<double>((OperateResult) result2) : OperateResult.CreateSuccessResult<double>(result2.Content.Value<double>());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ReadVersion" />
  public async Task<OperateResult<double>> ReadVersionAsync()
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<double>(StringResources.Language.InsufficientPrivileges);
    JArray body = SiemensWebApi.BuildRequestBody("Api.Version", (JObject) null, this.incrementCount.GetCurrentValue());
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) read);
    OperateResult<JToken> extra = SiemensWebApi.CheckAndExtraOneJsonResult(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult<double>(extra.Content.Value<double>()) : OperateResult.CreateFailedResult<double>((OperateResult) extra);
  }

  /// <summary>
  /// <b>[商业授权]</b> 对PLC对象进行PING操作<br />
  /// <b>[Authorization]</b> PING the PLC object
  /// </summary>
  /// <returns>是否PING成功</returns>
  [HslMqttApi("对PLC对象进行PING操作")]
  public OperateResult ReadPing()
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<double>(StringResources.Language.InsufficientPrivileges);
    OperateResult<string> operateResult = this.Post(this.rawUrl, SiemensWebApi.BuildRequestBody("Api.Ping", (JObject) null, this.incrementCount.GetCurrentValue()).ToString());
    return !operateResult.IsSuccess ? (OperateResult) operateResult : SiemensWebApi.CheckErrorResult(JArray.Parse(operateResult.Content)[0] as JObject);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ReadPing" />
  public async Task<OperateResult> ReadPingAsync()
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<double>(StringResources.Language.InsufficientPrivileges);
    JArray body = SiemensWebApi.BuildRequestBody("Api.Ping", (JObject) null, this.incrementCount.GetCurrentValue());
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    return read.IsSuccess ? SiemensWebApi.CheckErrorResult(JArray.Parse(read.Content)[0] as JObject) : (OperateResult) read;
  }

  /// <summary>
  /// 从PLC退出登录，当前的token信息失效，需要再次调用<see cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectServer" />获取新的token信息才可以。<br />
  /// Log out from the PLC, the current token information is invalid, you need to call <see cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.ConnectServer" /> again to get the new token information.
  /// </summary>
  /// <returns>是否成功</returns>
  [HslMqttApi("从PLC退出登录，当前的token信息失效，需要再次调用ConnectServer获取新的token信息才可以")]
  public OperateResult Logout()
  {
    OperateResult<string> operateResult = this.Post(this.rawUrl, SiemensWebApi.BuildRequestBody("Api.Logout", (JObject) null, this.incrementCount.GetCurrentValue()).ToString());
    return !operateResult.IsSuccess ? (OperateResult) operateResult : SiemensWebApi.CheckErrorResult(JArray.Parse(operateResult.Content)[0] as JObject);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensWebApi.Logout" />
  public async Task<OperateResult> LogoutAsync()
  {
    JArray body = SiemensWebApi.BuildRequestBody("Api.Logout", (JObject) null, this.incrementCount.GetCurrentValue());
    OperateResult<string> read = await this.PostAsync(this.rawUrl, body.ToString());
    OperateResult operateResult = read.IsSuccess ? SiemensWebApi.CheckErrorResult(JArray.Parse(read.Content)[0] as JObject) : (OperateResult) read;
    body = (JArray) null;
    read = (OperateResult<string>) null;
    return operateResult;
  }

  private static JObject GetJsonRpc(string method, JObject paramsJson, long id)
  {
    JObject jsonRpc = new JObject();
    jsonRpc.Add("jsonrpc", (JToken) new JValue("2.0"));
    jsonRpc.Add(nameof (method), (JToken) new JValue(method));
    jsonRpc.Add(nameof (id), (JToken) new JValue(id));
    if (paramsJson != null)
      jsonRpc.Add("params", (JToken) paramsJson);
    return jsonRpc;
  }

  private static JArray BuildRequestBody(string method, JObject paramsJson, long id)
  {
    return new JArray()
    {
      (JToken) SiemensWebApi.GetJsonRpc(method, paramsJson, id)
    };
  }

  private static JArray BuildConnectBody(long id, string name, string password)
  {
    return SiemensWebApi.BuildRequestBody("Api.Login", new JObject()
    {
      {
        "user",
        (JToken) new JValue(name)
      },
      {
        nameof (password),
        (JToken) new JValue(password)
      }
    }, id);
  }

  private static JArray BuildReadRawBody(long id, string address)
  {
    return SiemensWebApi.BuildRequestBody("PlcProgram.Read", new JObject()
    {
      {
        "var",
        (JToken) new JValue(address)
      },
      {
        "mode",
        (JToken) new JValue("raw")
      }
    }, id);
  }

  private static JArray BuildWriteRawBody(long id, string address, byte[] value)
  {
    return SiemensWebApi.BuildRequestBody("PlcProgram.Write", new JObject()
    {
      {
        "var",
        (JToken) new JValue(address)
      },
      {
        "mode",
        (JToken) new JValue("raw")
      },
      {
        nameof (value),
        (JToken) new JArray((object) ((IEnumerable<byte>) value).Select<byte, int>((Func<byte, int>) (m => (int) m)).ToArray<int>())
      }
    }, id);
  }

  private static JArray BuildWriteJTokenBody(long id, string address, JToken value)
  {
    return SiemensWebApi.BuildRequestBody("PlcProgram.Write", new JObject()
    {
      {
        "var",
        (JToken) new JValue(address)
      },
      {
        nameof (value),
        value
      }
    }, id);
  }

  private static JArray BuildReadJTokenBody(long id, string address)
  {
    return SiemensWebApi.BuildRequestBody("PlcProgram.Read", new JObject()
    {
      {
        "var",
        (JToken) new JValue(address)
      }
    }, id);
  }

  private static JArray BuildReadJTokenBody(long id, string[] address)
  {
    JArray jarray = new JArray();
    for (int index = 0; index < address.Length; ++index)
      jarray.Add((JToken) SiemensWebApi.GetJsonRpc("PlcProgram.Read", new JObject()
      {
        {
          "var",
          (JToken) new JValue(address[index])
        }
      }, id + (long) index));
    return jarray;
  }

  private static OperateResult CheckErrorResult(JObject json)
  {
    if (!json.ContainsKey("error"))
      return OperateResult.CreateSuccessResult();
    JObject jobject = json["error"] as JObject;
    return new OperateResult(jobject.Value<int>((object) "code"), jobject.Value<string>((object) "message"));
  }

  private static OperateResult<byte[]> CheckReadRawResult(string response)
  {
    OperateResult<JToken> result = SiemensWebApi.CheckAndExtraOneJsonResult(response);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>((result.Content as JArray).Select<JToken, byte>((Func<JToken, byte>) (m => m.Value<byte>())).ToArray<byte>());
  }

  private static OperateResult CheckWriteResult(string response)
  {
    JObject json = (JObject) JArray.Parse(response)[0];
    OperateResult result = SiemensWebApi.CheckErrorResult(json);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<JToken>(result);
    return json.ContainsKey("result") ? (json["result"].Value<bool>() ? OperateResult.CreateSuccessResult() : new OperateResult(json.ToString())) : (OperateResult) new OperateResult<JToken>($"Can't find result key and none token, login failed:{Environment.NewLine}{response}");
  }

  private static OperateResult<JToken> CheckAndExtraOneJsonResult(string response)
  {
    OperateResult<JToken[]> result = SiemensWebApi.CheckAndExtraJsonResult(response);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<JToken>((OperateResult) result) : OperateResult.CreateSuccessResult<JToken>(result.Content[0]);
  }

  private static OperateResult<JToken[]> CheckAndExtraJsonResult(string response)
  {
    try
    {
      JArray jarray = JArray.Parse(response);
      List<JToken> jtokenList = new List<JToken>();
      for (int index = 0; index < jarray.Count; ++index)
      {
        if (jarray[index] is JObject json)
        {
          OperateResult result = SiemensWebApi.CheckErrorResult(json);
          if (!result.IsSuccess)
            return OperateResult.CreateFailedResult<JToken[]>(result);
          if (!json.ContainsKey("result"))
            return new OperateResult<JToken[]>($"Can't find result key and none token, login failed:{Environment.NewLine}{response}");
          jtokenList.Add(json["result"]);
        }
      }
      return OperateResult.CreateSuccessResult<JToken[]>(jtokenList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<JToken[]>($"CheckAndExtraJsonResult failed: {ex.Message}{Environment.NewLine}Content: {response}");
    }
  }
}
