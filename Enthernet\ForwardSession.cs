﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.ForwardSession
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using System;
using System.Net;
using System.Net.Sockets;

#nullable disable
namespace HslCommunication.Enthernet;

/// <summary>转发过程中的中间会话信息</summary>
public class ForwardSession : PipeSession
{
  /// <summary>实例化一个默认的对象</summary>
  public ForwardSession()
  {
    this.ServerBuffer = new byte[2048 /*0x0800*/];
    this.BytesBuffer = new byte[2048 /*0x0800*/];
  }

  /// <summary>指定客户端的 socket 来实例化一个对象</summary>
  /// <param name="pipe">客户端的管道信息</param>
  /// <param name="endPoint">客户端的远程连接点</param>
  /// <param name="cacheSize">指定当前的缓冲区的大小</param>
  public ForwardSession(PipeTcpNet pipe, IPEndPoint endPoint, int cacheSize = 2048 /*0x0800*/)
  {
    this.Communication = (CommunicationPipe) pipe;
    this.IpEndPoint = endPoint;
    this.ServerBuffer = new byte[cacheSize];
    this.BytesBuffer = new byte[cacheSize];
  }

  /// <summary>连接服务端的socket</summary>
  public Socket ServerSocket { get; set; }

  /// <summary>服务端的缓存数据信息</summary>
  public byte[] ServerBuffer { get; set; }

  /// <summary>转发客户端的缓存数据</summary>
  public byte[] BytesBuffer { get; set; }

  /// <summary>客户端的远程终结点</summary>
  public IPEndPoint IpEndPoint { get; set; }

  /// <inheritdoc />
  public override void Close()
  {
    base.Close();
    NetSupport.CloseSocket(this.ServerSocket);
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"Server[{this.ServerSocket.RemoteEndPoint}] Local[{this.IpEndPoint}] Online:{SoftBasic.GetTimeSpanDescription(DateTime.Now - this.OnlineTime)}";
  }
}
