﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Beckhoff.AdsDeviceInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Beckhoff;

/// <summary>
/// Ads设备的相关信息，主要是版本号，设备名称<br />
/// Information about Ads devices, primarily version numbers, device names.
/// </summary>
public class AdsDeviceInfo
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public AdsDeviceInfo()
  {
  }

  /// <summary>
  /// 根据原始的数据内容来实例化一个对象<br />
  /// Instantiate an object based on the original data content
  /// </summary>
  /// <param name="data">原始的数据内容</param>
  public AdsDeviceInfo(byte[] data)
  {
    this.Major = data[0];
    this.Minor = data[1];
    this.Build = BitConverter.ToUInt16(data, 2);
    this.DeviceName = Encoding.ASCII.GetString(data.RemoveBegin<byte>(4)).Trim(char.MinValue, ' ');
  }

  /// <summary>
  /// 主版本号<br />
  /// Main Version
  /// </summary>
  public byte Major { get; set; }

  /// <summary>
  /// 次版本号<br />
  /// Minor Version
  /// </summary>
  public byte Minor { get; set; }

  /// <summary>
  /// 构建版本号<br />
  /// Build version
  /// </summary>
  public ushort Build { get; set; }

  /// <summary>
  /// 设备的名字<br />
  /// Device Name
  /// </summary>
  public string DeviceName { get; set; }
}
