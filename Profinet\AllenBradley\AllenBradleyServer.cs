﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>
/// AB PLC的虚拟服务器，仅支持和HSL组件的完美通信，可以手动添加一些节点。<br />
/// AB PLC's virtual server only supports perfect communication with HSL components. You can manually add some nodes.
/// </summary>
/// <remarks>本AB的虚拟PLC仅限商业授权用户使用，感谢支持。</remarks>
public class AllenBradleyServer : DeviceServer
{
  private const int DataPoolLength = 65536 /*0x010000*/;
  /// <summary>所有标签的词典信息</summary>
  protected Dictionary<string, AllenBradleyItemValue> abValues;
  /// <summary>当前词典的锁</summary>
  protected SimpleHybirdLock simpleHybird;
  private bool createTagWithWrite = false;
  private int value_6b_f68f = 123456;

  /// <summary>
  /// 实例化一个AB PLC协议的服务器<br />
  /// Instantiate an AB PLC protocol server
  /// </summary>
  public AllenBradleyServer()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.Port = 44818;
    this.simpleHybird = new SimpleHybirdLock();
    this.abValues = new Dictionary<string, AllenBradleyItemValue>();
  }

  /// <summary>
  /// 获取或设置当前的服务器的数据字节排序情况<br />
  /// Gets or sets the data byte ordering of the current server
  /// </summary>
  public DataFormat DataFormat
  {
    get => this.ByteTransform.DataFormat;
    set => this.ByteTransform.DataFormat = value;
  }

  /// <summary>
  /// 获取或设置当调用写入方法的时候，如果标签不存在，是否创建新的标签信息<br />
  /// Gets or sets whether to create a new tag information if the tag does not exist when the write method is called
  /// </summary>
  public bool CreateTagWithWrite
  {
    get => this.createTagWithWrite;
    set => this.createTagWithWrite = value;
  }

  /// <summary>
  /// 向服务器新增一个新的Tag值<br />
  /// Add a new tag value to the server
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">标签值</param>
  public void AddTagValue(string key, AllenBradleyItemValue value)
  {
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(key))
      this.abValues[key] = value;
    else
      this.abValues.Add(key, value);
    this.simpleHybird.Leave();
  }

  /// <summary>
  /// 向服务器新增一个新的bool类型的Tag值，并赋予初始化的值<br />
  /// Add a new tag value of type bool to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, bool value)
  {
    string key1 = key;
    AllenBradleyItemValue bradleyItemValue1 = new AllenBradleyItemValue();
    bradleyItemValue1.IsArray = false;
    AllenBradleyItemValue bradleyItemValue2 = bradleyItemValue1;
    byte[] numArray;
    if (!value)
      numArray = new byte[2];
    else
      numArray = new byte[2]{ byte.MaxValue, byte.MaxValue };
    bradleyItemValue2.Buffer = numArray;
    bradleyItemValue1.TypeLength = 2;
    bradleyItemValue1.TypeCode = (ushort) 193;
    AllenBradleyItemValue bradleyItemValue3 = bradleyItemValue1;
    this.AddTagValue(key1, bradleyItemValue3);
  }

  /// <summary>
  /// 向服务器新增一个新的bool数组类型的Tag值，并赋予初始化的值<br />
  /// Add a new tag value of type bool array to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, bool[] value)
  {
    if (value == null)
      value = new bool[0];
    byte[] numArray = new byte[value.Length];
    for (int index = 0; index < value.Length; ++index)
      numArray[index] = value[index] ? (byte) 1 : (byte) 0;
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = numArray,
      TypeLength = 1,
      TypeCode = (ushort) 193
    });
  }

  /// <summary>
  /// 向服务器新增一个新的type类型的Tag值，并赋予初始化的值<br />
  /// Add a new type tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, byte value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = new byte[1]{ value },
      TypeLength = 1,
      TypeCode = (ushort) 194
    });
  }

  /// <summary>
  /// 向服务器新增一个新的short类型的Tag值，并赋予初始化的值<br />
  /// Add a new short tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, short value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 2,
      TypeCode = (ushort) 195
    });
  }

  /// <summary>
  /// 向服务器新增一个新的short数组的Tag值，并赋予初始化的值<br />
  /// Add a new short array Tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, short[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 2,
      TypeCode = (ushort) 195
    });
  }

  /// <summary>
  /// 向服务器新增一个新的ushort类型的Tag值，并赋予初始化的值<br />
  /// Add a new tag value of ushort type to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, ushort value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 2,
      TypeCode = (ushort) 199
    });
  }

  /// <summary>
  /// 向服务器新增一个新的ushort数组的Tag值，并赋予初始化的值<br />
  /// Add a new ushort array Tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, ushort[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 2,
      TypeCode = (ushort) 199
    });
  }

  /// <summary>
  /// 向服务器新增一个新的int类型的Tag值，并赋予初始化的值<br />
  /// Add a new Tag value of type int to the server and assign the initialized value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, int value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 4,
      TypeCode = (ushort) 196
    });
  }

  /// <summary>
  /// 向服务器新增一个新的int数组的Tag值，并赋予初始化的值<br />
  /// Add a new Tag value of the int array to the server and assign the initialized value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, int[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 4,
      TypeCode = (ushort) 196
    });
  }

  /// <summary>
  /// 向服务器新增一个新的uint类型的Tag值，并赋予初始化的值<br />
  /// Add a new uint tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, uint value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 4,
      TypeCode = (ushort) 200
    });
  }

  /// <summary>
  /// 向服务器新增一个新的uint数组的Tag值，并赋予初始化的值<br />
  /// Add a new uint array Tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, uint[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 4,
      TypeCode = (ushort) 200
    });
  }

  /// <summary>
  /// 向服务器新增一个新的long类型的Tag值，并赋予初始化的值<br />
  /// Add a new Tag value of type long to the server and assign the initialized value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, long value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 8,
      TypeCode = (ushort) 197
    });
  }

  /// <summary>
  /// 向服务器新增一个新的long数组的Tag值，并赋予初始化的值<br />
  /// Add a new Long array Tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, long[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 8,
      TypeCode = (ushort) 197
    });
  }

  /// <summary>
  /// 向服务器新增一个新的ulong类型的Tag值，并赋予初始化的值<br />
  /// Add a new Ulong type Tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, ulong value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 8,
      TypeCode = (ushort) 201
    });
  }

  /// <summary>
  /// 向服务器新增一个新的ulong数组的Tag值，并赋予初始化的值<br />
  /// Add a new Ulong array Tag value to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, ulong[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 8,
      TypeCode = (ushort) 201
    });
  }

  /// <summary>
  /// 向服务器新增一个新的float类型的Tag值，并赋予初始化的值<br />
  /// Add a new tag value of type float to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, float value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 4,
      TypeCode = (ushort) 202
    });
  }

  /// <summary>
  /// 向服务器新增一个新的float数组的Tag值，并赋予初始化的值<br />
  /// Add a new Tag value of the float array to the server and assign the initialized value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, float[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 4,
      TypeCode = (ushort) 202
    });
  }

  /// <summary>
  /// 向服务器新增一个新的double类型的Tag值，并赋予初始化的值<br />
  /// Add a new tag value of type double to the server and assign the initialized value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, double value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 8,
      TypeCode = (ushort) 203
    });
  }

  /// <summary>
  /// 向服务器新增一个新的double数组的Tag值，并赋予初始化的值<br />
  /// Add a new double array Tag value to the server and assign the initialized value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  public void AddTagValue(string key, double[] value)
  {
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = this.ByteTransform.TransByte(value),
      TypeLength = 8,
      TypeCode = (ushort) 203
    });
  }

  /// <summary>
  /// 向服务器新增一个新的string类型的Tag值，并赋予初始化的值<br />
  /// Add a new Tag value of string type to the server and assign the initial value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  /// <param name="maxLength">字符串的最长值</param>
  public virtual void AddTagValue(string key, string value, int maxLength)
  {
    byte[] bytes = Encoding.UTF8.GetBytes(value);
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = SoftBasic.ArrayExpandToLength<byte>(SoftBasic.SpliceArray<byte>(new byte[2], BitConverter.GetBytes(bytes.Length), Encoding.UTF8.GetBytes(value)), maxLength),
      TypeLength = maxLength,
      TypeCode = (ushort) 209
    });
  }

  /// <summary>
  /// 向服务器新增一个新的string数组的Tag值，并赋予初始化的值<br />
  /// Add a new String array Tag value to the server and assign the initialized value
  /// </summary>
  /// <param name="key">Tag名称</param>
  /// <param name="value">值信息</param>
  /// <param name="maxLength">字符串的最长值</param>
  public virtual void AddTagValue(string key, string[] value, int maxLength)
  {
    byte[] numArray = new byte[maxLength * value.Length];
    for (int index = 0; index < value.Length; ++index)
    {
      byte[] bytes = Encoding.UTF8.GetBytes(value[index]);
      BitConverter.GetBytes(bytes.Length).CopyTo((Array) numArray, maxLength * index + 2);
      bytes.CopyTo((Array) numArray, maxLength * index + 6);
    }
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = numArray,
      TypeLength = maxLength,
      TypeCode = (ushort) 209
    });
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return this.ReadWithType(address, (int) length).Then<byte[]>((Func<byte[], ushort, OperateResult<byte[]>>) ((m, n) => OperateResult.CreateSuccessResult<byte[]>(m)));
  }

  /// <summary>获取当前的索引信息</summary>
  /// <param name="address">数据标签信息</param>
  /// <returns>索引信息</returns>
  protected int GetAddressIndex(ref string address)
  {
    Match match = Regex.Match(address, "\\[[0-9]+\\]$");
    if (!match.Success)
      return 0;
    address = address.Substring(0, match.Index);
    return Convert.ToInt32(match.Value.Substring(1, match.Length - 2));
  }

  /// <inheritdoc />
  protected OperateResult<byte[], ushort> ReadWithType(string address, int length)
  {
    int addressIndex = this.GetAddressIndex(ref address);
    ushort num = 0;
    byte[] destinationArray = (byte[]) null;
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(address))
    {
      AllenBradleyItemValue abValue = this.abValues[address];
      num = abValue.TypeCode;
      if (!abValue.IsArray)
      {
        destinationArray = new byte[abValue.Buffer.Length];
        abValue.Buffer.CopyTo((Array) destinationArray, 0);
      }
      else if (length < 0)
      {
        destinationArray = new byte[abValue.Buffer.Length];
        Array.Copy((Array) abValue.Buffer, 0, (Array) destinationArray, 0, abValue.Buffer.Length);
      }
      else if (addressIndex * abValue.TypeLength + length * abValue.TypeLength <= abValue.Buffer.Length)
      {
        destinationArray = new byte[length * abValue.TypeLength];
        Array.Copy((Array) abValue.Buffer, addressIndex * abValue.TypeLength, (Array) destinationArray, 0, destinationArray.Length);
      }
    }
    this.simpleHybird.Leave();
    return destinationArray == null ? new OperateResult<byte[], ushort>(StringResources.Language.AllenBradley04) : OperateResult.CreateSuccessResult<byte[], ushort>(destinationArray, num);
  }

  /// <summary>根据地址获取标签信息</summary>
  /// <param name="address">地址信息</param>
  /// <returns>ab-plc的点位标签信息</returns>
  protected AllenBradleyItemValue GetAddressItemValue(string address)
  {
    this.GetAddressIndex(ref address);
    AllenBradleyItemValue addressItemValue = (AllenBradleyItemValue) null;
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(address))
      addressItemValue = this.abValues[address];
    this.simpleHybird.Leave();
    return addressItemValue;
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return this.Write(address, value, 0);
  }

  private OperateResult Write(string address, byte[] value, int offset)
  {
    int addressIndex = this.GetAddressIndex(ref address);
    bool flag = false;
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(address))
    {
      AllenBradleyItemValue abValue = this.abValues[address];
      if (!abValue.IsArray)
      {
        if (abValue.Buffer.Length == value.Length)
        {
          abValue.Buffer = value;
          flag = true;
        }
        else if (abValue.Buffer.Length > value.Length)
        {
          value.CopyTo((Array) abValue.Buffer, 0);
          flag = true;
        }
      }
      else if (addressIndex * abValue.TypeLength + value.Length <= abValue.Buffer.Length)
      {
        Array.Copy((Array) value, 0, (Array) abValue.Buffer, addressIndex * abValue.TypeLength + offset, value.Length);
        flag = true;
      }
    }
    this.simpleHybird.Leave();
    return !flag ? new OperateResult(StringResources.Language.AllenBradley04) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadByte(System.String)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Byte)" />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    int addressIndex = this.GetAddressIndex(ref address);
    bool flag = false;
    bool[] flagArray = (bool[]) null;
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(address))
    {
      flag = true;
      AllenBradleyItemValue abValue = this.abValues[address];
      if (!abValue.IsArray)
      {
        if (abValue.TypeCode == (ushort) 193)
        {
          if (abValue.Buffer[0] == byte.MaxValue && abValue.Buffer[1] == byte.MaxValue)
            flagArray = new bool[1]{ true };
          else
            flagArray = new bool[1];
        }
        else
          flagArray = abValue.Buffer.ToBoolArray();
      }
      else if (addressIndex * abValue.TypeLength + (int) length * abValue.TypeLength <= abValue.Buffer.Length)
      {
        flagArray = new bool[(int) length * abValue.TypeLength];
        for (int index = 0; index < flagArray.Length; ++index)
          flagArray[index] = abValue.Buffer[addressIndex * abValue.TypeLength + index] > (byte) 0;
      }
    }
    this.simpleHybird.Leave();
    return !flag ? new OperateResult<bool[]>(StringResources.Language.AllenBradley04) : OperateResult.CreateSuccessResult<bool[]>(flagArray);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    if (this.IsNeedCreateTag(address))
    {
      this.AddTagValue(address, value);
      return OperateResult.CreateSuccessResult();
    }
    int addressIndex = this.GetAddressIndex(ref address);
    bool flag = false;
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(address))
    {
      flag = true;
      AllenBradleyItemValue abValue = this.abValues[address];
      if (!abValue.IsArray)
      {
        if (value[0])
        {
          byte[] buffer1 = abValue.Buffer;
          if (buffer1 != null && buffer1.Length != 0)
            abValue.Buffer[0] = byte.MaxValue;
          byte[] buffer2 = abValue.Buffer;
          if (buffer2 != null && buffer2.Length > 1)
            abValue.Buffer[1] = byte.MaxValue;
        }
        else
        {
          byte[] buffer3 = abValue.Buffer;
          if (buffer3 != null && buffer3.Length != 0)
            abValue.Buffer[0] = (byte) 0;
          byte[] buffer4 = abValue.Buffer;
          if (buffer4 != null && buffer4.Length > 1)
            abValue.Buffer[1] = (byte) 0;
        }
      }
      else if (addressIndex * abValue.TypeLength + value.Length * abValue.TypeLength <= abValue.Buffer.Length)
      {
        for (int index = 0; index < value.Length; ++index)
          abValue.Buffer[addressIndex * abValue.TypeLength + index] = value[index] ? (byte) 1 : (byte) 0;
      }
    }
    this.simpleHybird.Leave();
    return !flag ? new OperateResult(StringResources.Language.AllenBradley04) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> result = this.Read(address, length);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    if (result.Content.Length < 6)
      return OperateResult.CreateSuccessResult<string>(encoding.GetString(result.Content));
    int int32 = BitConverter.ToInt32(result.Content, 2);
    return OperateResult.CreateSuccessResult<string>(encoding.GetString(result.Content, 6, int32));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    if (this.IsNeedCreateTag(address))
    {
      this.AddTagValue(address, value, 1024 /*0x0400*/);
      return OperateResult.CreateSuccessResult();
    }
    bool flag = false;
    int addressIndex = this.GetAddressIndex(ref address);
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(address))
    {
      flag = true;
      AllenBradleyItemValue abValue = this.abValues[address];
      byte[] buffer = abValue.Buffer;
      if (buffer != null && buffer.Length >= 6)
      {
        byte[] bytes = encoding.GetBytes(value);
        BitConverter.GetBytes(bytes.Length).CopyTo((Array) abValue.Buffer, 2 + addressIndex * abValue.TypeLength);
        if (bytes.Length != 0)
          Array.Copy((Array) bytes, 0, (Array) abValue.Buffer, 6 + addressIndex * abValue.TypeLength, Math.Min(bytes.Length, abValue.Buffer.Length - 6));
      }
    }
    this.simpleHybird.Leave();
    return !flag ? (OperateResult) new OperateResult<bool>(StringResources.Language.AllenBradley04) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new AllenBradleyMessage();

  /// <inheritdoc />
  protected override OperateResult ThreadPoolLoginAfterClientCheck(
    PipeSession session,
    IPEndPoint endPoint)
  {
    session.Tag = (object) new CipSessionTag();
    CommunicationPipe communication = session.Communication;
    OperateResult<byte[]> message = communication.ReceiveMessage((INetMessage) new AllenBradleyMessage(), (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
    if (!message.IsSuccess)
      return (OperateResult) message;
    if (message.Content[0] == (byte) 4)
    {
      byte[] numArray = AllenBradleyHelper.PackRequestHeader((ushort) 4, 0U, "01 00 00 01 14 00 01 00 20 01 43 6F 6D 6D 75 6E 69 63 61 74 69 6F 6E 73 00 00".ToHexBytes(), message.Content.Length >= 20 ? message.Content.SelectMiddle<byte>(12, 8) : (byte[]) null);
      this.LogSendMessage(numArray, session);
      OperateResult operateResult = communication.Send(numArray);
      if (!operateResult.IsSuccess)
        return operateResult;
      message = communication.ReceiveMessage((INetMessage) new AllenBradleyMessage(), (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
      if (!message.IsSuccess)
        return (OperateResult) message;
    }
    byte[] buffer = new byte[4];
    HslHelper.HslRandom.NextBytes(buffer);
    byte[] numArray1 = AllenBradleyHelper.PackRequestHeader((ushort) 101, this.ByteTransform.TransUInt32(buffer, 0), message.Content.Length >= 24 ? message.Content.RemoveBegin<byte>(24) : new byte[0], message.Content.Length >= 20 ? message.Content.SelectMiddle<byte>(12, 8) : (byte[]) null);
    this.LogSendMessage(numArray1, session);
    OperateResult operateResult1 = communication.Send(numArray1);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    session.SessionID = this.ByteTransform.TransUInt32(buffer, 0).ToString();
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    string str = this.ByteTransform.TransUInt32(receive, 4).ToString();
    if (str != session.SessionID)
    {
      this.LogNet?.WriteDebug(this.ToString(), $"SessionID 不一致的请求，要求ID：{session.SessionID} 实际ID：{str}");
      return OperateResult.CreateSuccessResult<byte[]>(AllenBradleyHelper.PackRequestHeader((ushort) 102, 100U, this.ByteTransform.TransUInt32(receive, 4), new byte[0]));
    }
    byte[] destinationArray = this.ReadFromCipCore(session, receive);
    if (destinationArray == null)
      return OperateResult.CreateSuccessResult<byte[]>(AllenBradleyHelper.PackRequestHeader((ushort) 111, 1U, BitConverter.ToUInt32(receive, 4), new byte[0]));
    if (destinationArray.Length >= 8)
      Array.Copy((Array) receive, 4, (Array) destinationArray, 4, 4);
    if (receive != null && receive.Length >= 20 && destinationArray != null && destinationArray.Length > 20)
      Array.Copy((Array) receive, 12, (Array) destinationArray, 12, 8);
    return OperateResult.CreateSuccessResult<byte[]>(destinationArray);
  }

  /// <summary>当收到CIP协议的报文的时候应该触发的方法，允许继承重写，来实现自定义的返回，或是数据监听。</summary>
  /// <param name="session">当前客户端的会话信息</param>
  /// <param name="cipAll">CIP报文数据</param>
  /// <returns>返回的报文信息</returns>
  protected virtual byte[] ReadFromCipCore(PipeSession session, byte[] cipAll)
  {
    CipSessionTag tag = session.Tag as CipSessionTag;
    uint uint32 = BitConverter.ToUInt32(cipAll, 4);
    if (BitConverter.ToInt16(cipAll, 0) == (short) 102)
      return AllenBradleyHelper.PackRequestHeader((ushort) 102, uint32, new byte[0]);
    byte[] numArray1 = SoftBasic.ArrayRemoveBegin<byte>(cipAll, 24);
    if (numArray1.Length == 22)
      return AllenBradleyHelper.PackRequestHeader((ushort) 102, uint32, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService("810000002f000c006706020630005be104010a4e4a3530312d31353030".ToHexBytes())));
    if (numArray1[12] == (byte) 178 && numArray1[16 /*0x10*/] == (byte) 91)
    {
      tag.IsConnectedCIP = true;
      tag.TOConnectID = numArray1.SelectMiddle<byte>(28, 4);
      return AllenBradleyHelper.PackRequestHeader((ushort) 102, uint32, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService("db000000c109415f0100fe8002001b0558bcbf0280841e0080841e000000".ToHexBytes())));
    }
    if (cipAll[0] == (byte) 111 && numArray1[12] == (byte) 178 && numArray1[16 /*0x10*/] == (byte) 84)
    {
      tag.IsConnectedCIP = true;
      byte[] hexBytes = "d4 00 00 00 02 6f 4c 02 01 47 4c 02 02 00 01 00 f0 78 07 01 e0 70 72 00 e0 70 72 00 00 00".ToHexBytes();
      BitConverter.GetBytes(HslHelper.HslRandom.Next()).CopyTo((Array) hexBytes, 4);
      tag.TOConnectID = numArray1.SelectMiddle<byte>(28, 4);
      Array.Copy((Array) numArray1, 28, (Array) hexBytes, 8, 6);
      return AllenBradleyHelper.PackRequestHeader((ushort) 111, uint32, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService(hexBytes)), cipAll.SelectMiddle<byte>(12, 8));
    }
    if (cipAll[0] == (byte) 111 && numArray1[12] == (byte) 178 && numArray1[16 /*0x10*/] == (byte) 78)
      return AllenBradleyHelper.PackRequestHeader((ushort) 111, uint32, AllenBradleyHelper.PackCommandSpecificData(this.GetConnectAddressItem(tag), AllenBradleyHelper.PackCommandSingleService("CE 00 00 00 02 6f 4c 02 01 47 4c 02 02 00 01 00 f0 78 07 01 e0 70 72 00 e0 70 72 00 00 00".ToHexBytes())), cipAll.SelectMiddle<byte>(12, 8));
    if (numArray1[26] == (byte) 10 && numArray1[27] == (byte) 2 && numArray1[28] == (byte) 32 /*0x20*/ && numArray1[29] == (byte) 2 && numArray1[30] == (byte) 36 && numArray1[31 /*0x1F*/] == (byte) 1)
      return AllenBradleyHelper.PackRequestHeader((ushort) 111, 1U, uint32, new byte[0]);
    if (tag.IsConnectedCIP)
    {
      byte[] cipCore = this.ByteTransform.TransByte(numArray1, 22, (int) BitConverter.ToInt16(numArray1, 18) - 2);
      if (cipCore[0] == (byte) 76)
        return AllenBradleyHelper.PackRequestHeader((ushort) 112 /*0x70*/, uint32, AllenBradleyHelper.PackCommandSpecificData(this.GetConnectAddressItem(tag), AllenBradleyHelper.PackCommandSingleService(this.ReadByCommand(session, cipCore), (ushort) 177, true, BitConverter.ToUInt16(numArray1, 20))));
      if (cipCore[0] != (byte) 77)
        return AllenBradleyHelper.PackRequestHeader((ushort) 111, 1U, uint32, new byte[0]);
      byte[] numArray2 = AllenBradleyHelper.PackCommandSingleService(this.WriteByMessage(cipCore), (ushort) 177, true, BitConverter.ToUInt16(numArray1, 20));
      numArray2[2] = (byte) 0;
      numArray2[3] = (byte) 0;
      return AllenBradleyHelper.PackRequestHeader((ushort) 102, uint32, AllenBradleyHelper.PackCommandSpecificData(this.GetConnectAddressItem(tag), numArray2));
    }
    byte[] cipCore1 = this.ByteTransform.TransByte(numArray1, 26, (int) BitConverter.ToInt16(numArray1, 24));
    if (cipCore1[0] == (byte) 76 || cipCore1[0] == (byte) 82)
      return AllenBradleyHelper.PackRequestHeader((ushort) 102, uint32, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService(this.ReadByCommand(session, cipCore1))));
    if (cipCore1[0] == (byte) 77)
      return AllenBradleyHelper.PackRequestHeader((ushort) 102, uint32, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService(this.WriteByMessage(cipCore1))));
    if (cipCore1[0] != (byte) 85)
      return AllenBradleyHelper.PackRequestHeader((ushort) 111, 1U, uint32, new byte[0]);
    return AllenBradleyHelper.PackRequestHeader((ushort) 111, uint32, AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandSingleService(this.ReadList(cipCore1))));
  }

  private byte[] GetConnectAddressItem(CipSessionTag tag)
  {
    byte[] connectAddressItem = new byte[8]
    {
      (byte) 161,
      (byte) 0,
      (byte) 4,
      (byte) 0,
      (byte) 65,
      (byte) 1,
      (byte) 25,
      (byte) 7
    };
    tag.TOConnectID.CopyTo((Array) connectAddressItem, 4);
    return connectAddressItem;
  }

  private byte[] ReadList(byte[] cipCore)
  {
    if (cipCore[1] == (byte) 14 && cipCore[2] == (byte) 145)
      return SoftBasic.HexStringToBytes("\r\nD5 00 00 00\r\nfa 1b 00 00 02 00 42 41 c1 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 20 24 00 00 13 00 52 6f 75 74\r\n69 6e 65 3a 4d 61 69 6e 52 6f 75 74 69 6e 65 6d\r\n10 00 00 00 00 00 00 00 00 00 00 00 00 82 25 00\r\n00 13 00 5f 5f 6c 30 31 44 38 34 31 38 46 32 46\r\n38 31 46 31 46 30 c4 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 e9 2b 00 00 09 00 5f 5f 53 4c 34 39\r\n31 30 39 c4 20 04 00 00 00 00 00 00 00 00 00 00\r\n00 68 31 00 00 13 00 5f 5f 6c 30 31 44 38 35 46\r\n41 34 32 46 38 31 43 32 35 33 c4 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 3d 38 00 00 13 00 5f 5f\r\n6c 30 31 44 38 30 31 44 46 32 46 38 31 38 44 45\r\n38 c4 00 00 00 00 00 00 00 00 00 00 00 00 00 24\r\n59 00 00 0d 00 52 6f 75 74 69 6e 65 3a 54 49 4d\r\n45 52 6d 10 00 00 00 00 00 00 00 00 00 00 00 00\r\n4b 7c 00 00 13 00 5f 5f 6c 30 31 44 38 36 44 43\r\n35 32 46 38 31 42 44 34 38 c4 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 97 8a 00 00 06 00 53 43 4c\r\n5f 30 31 8a 8f 00 00 00 00 00 00 00 00 00 00 00\r\n00 23 b7 00 00 02 00 41 42 c1 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 d5 bf 00 00 0d 00 52 6f 75\r\n74 69 6e 65 3a 43 4f 55 4e 54 6d 10 00 00 00 00\r\n00 00 00 00 00 00 00 00 1e da 00 00 0e 00 52 6f\r\n75 74 69 6e 65 3a 61 6e 61 6c 6f 67 6d 10 00 00\r\n00 00 00 00 00 00 00 00 00 00\r\n");
    switch (BitConverter.ToUInt16(cipCore, 6))
    {
      case 0:
        return SoftBasic.HexStringToBytes("\r\nd5 00 06 00\r\n40 07 00 00 03 00 4f 55 54 c4 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 e7 0e 00 00 0d 00 54 69 6d\r\n69 6e 67 5f 41 63 74 69 76 65 c1 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 d1 18 00 00 03 00 5a 58\r\n43 c1 00 00 00 00 00 00 00 00 00 00 00 00 00 fa\r\n1b 00 00 13 00 50 72 6f 67 72 61 6d 3a 4d 61 69\r\n6e 50 72 6f 67 72 61 6d 68 10 00 00 00 00 00 00\r\n00 00 00 00 00 00 3f 20 00 00 04 00 54 45 53 54\r\nce 8f 00 00 00 00 00 00 00 00 00 00 00 00 20 24\r\n00 00 09 00 4d 61 70 3a 4c 6f 63 61 6c 69 10 00\r\n00 00 00 00 00 00 00 00 00 00 00 82 25 00 00 12\r\n00 43 78 6e 3a 46 75 73 65 64 3a 33 32 39 32 64\r\n66 32 33 7e 10 00 00 00 00 00 00 00 00 00 00 00\r\n00 eb 2a 00 00 02 00 49 4e c4 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 e9 2b 00 00 09 00 4c 6f 63\r\n61 6c 3a 32 3a 43 23 82 00 00 00 00 00 00 00 00\r\n00 00 00 00 68 31 00 00 09 00 4d 61 70 3a 4f 56\r\n31 36 45 69 10 00 00 00 00 00 00 00 00 00 00 00\r\n00 b4 35 00 00 06 00 54 49 4d 45 52 31 83 8f 00\r\n00 00 00 00 00 00 00 00 00 00 00 3d 38 00 00 09\r\n00 4c 6f 63 61 6c 3a 33 3a 4f d5 8a 00 00 00 00\r\n00 00 00 00 00 00 00 00 2e 3f 00 00 08 00 49 6e\r\n52 61 77 4d 61 78 c4 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 a5 41 00 00 0f 00 53 65 6c 65 63 74\r\n6f 72 5f 53 77 69 74 63 68 c1 03 00 00 00 00 00\r\n00 00 00 00 00 00 00 73 4a 00 00 07 00 49 4e 54\r\n54 45 53 54 c4 20 01 00 00 00 00 00 00 00 00 00\r\n00 00 ec 50 00 00 09 00 4c 6f 63 61 6c 3a 33 3a\r\n43 24 86 00 00 00 00 00 00 00 00 00 00 00 00 24\r\n59 00 00 08 00 4d 61 70 3a 49 56 33 32 69 10 00\r\n00 00 00 00 00 00 00 00 00 00 00\r\n");
      case 22821:
        return SoftBasic.HexStringToBytes("\r\nd5 00 00 00\r\n36 71 00 00 07 00 49 6e 45 75 4d 61 78 c4 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 4b 7c 00 00 09\r\n00 4c 6f 63 61 6c 3a 33 3a 49 fa 8e 00 00 00 00\r\n00 00 00 00 00 00 00 00 c3 81 00 00 05 00 43 4f\r\n55 4e 54 82 8f 00 00 00 00 00 00 00 00 00 00 00\r\n00 97 8a 00 00 09 00 4c 6f 63 61 6c 3a 32 3a 49\r\n20 89 00 00 00 00 00 00 00 00 00 00 00 00 b0 9b\r\n00 00 08 00 70 65 69 66 61 6e 67 73 97 8d 00 00\r\n00 00 00 00 00 00 00 00 00 00 0a b4 00 00 09 00\r\n52 54 4f 5f 52 65 73 65 74 c1 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 a1 b5 00 00 07 00 49 6e 45\r\n75 4d 69 6e c4 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 23 b7 00 00 0d 00 54 61 73 6b 3a 4d 61 69\r\n6e 54 61 73 6b 70 10 00 00 00 00 00 00 00 00 00\r\n00 00 00 d5 bf 00 00 08 00 4d 61 70 3a 4c 49 4e\r\n4b 69 10 00 00 00 00 00 00 00 00 00 00 00 00 9b\r\nc1 00 00 08 00 49 6e 52 61 77 4d 69 6e c4 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 26 c2 00 00 03\r\n00 41 42 43 c1 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 1e da 00 00 1a 00 43 78 6e 3a 53 74 61 6e\r\n64 61 72 64 49 6e 70 75 74 3a 32 34 66 36 36 38\r\n30 36 7e 10 00 00 00 00 00 00 00 00 00 00 00 00\r\n92 e7 00 00 0e 00 53 65 6c 65 63 74 6f 72 53 77\r\n69 74 63 68 c1 02 00 00 00 00 00 00 00 00 00 00\r\n00 00\r\n\r\n");
      default:
        return (byte[]) null;
    }
  }

  /// <summary>根据CIP的核心读取到相关实际数据信息</summary>
  /// <param name="session">当前的会话信息</param>
  /// <param name="cipCore">CIP核心报文</param>
  /// <returns>实际的报文信息</returns>
  protected virtual byte[] ReadByCommand(PipeSession session, byte[] cipCore)
  {
    OperateResult<byte[], ushort> operateResult = (OperateResult<byte[], ushort>) null;
    byte[] pathCommand = this.ByteTransform.TransByte(cipCore, 2, (int) cipCore[1] * 2);
    OperateResult<int, int> instanceAddressing = AllenBradleyHelper.ParseRequestPathSymbolInstanceAddressing(pathCommand);
    if (instanceAddressing.IsSuccess)
    {
      if (instanceAddressing.Content1 == 107 && instanceAddressing.Content2 == 63119)
        operateResult = OperateResult.CreateSuccessResult<byte[], ushort>(BitConverter.GetBytes(this.value_6b_f68f), (ushort) 196);
    }
    else
      operateResult = this.ReadWithType(AllenBradleyHelper.ParseRequestPathCommand(pathCommand), (int) BitConverter.ToUInt16(cipCore, 2 + pathCommand.Length));
    if (operateResult == null)
      operateResult = new OperateResult<byte[], ushort>(StringResources.Language.AllenBradley04);
    byte[] numArray = AllenBradleyHelper.PackCommandResponse(operateResult.Content1, true);
    if (numArray.Length > 6)
      BitConverter.GetBytes(operateResult.Content2).CopyTo((Array) numArray, 4);
    return numArray;
  }

  /// <summary>根据CIP和核心写入相关的地址数据信息</summary>
  /// <param name="cipCore">CIP核心报文</param>
  /// <returns>实际的报文信息</returns>
  protected virtual byte[] WriteByMessage(byte[] cipCore)
  {
    if (!this.EnableWrite)
      return AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
    byte[] pathCommand = this.ByteTransform.TransByte(cipCore, 2, (int) cipCore[1] * 2);
    OperateResult<int, int> instanceAddressing = AllenBradleyHelper.ParseRequestPathSymbolInstanceAddressing(pathCommand);
    if (instanceAddressing.IsSuccess)
    {
      byte[] numArray = this.ByteTransform.TransByte(cipCore, 6 + pathCommand.Length, cipCore.Length - 6 - pathCommand.Length);
      if (instanceAddressing.Content1 != 107 || instanceAddressing.Content2 != 63119)
        return AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
      if (numArray.Length >= 4)
        this.value_6b_f68f = BitConverter.ToInt32(numArray, 0);
      return AllenBradleyHelper.PackCommandResponse(new byte[0], false);
    }
    string requestPathCommand = AllenBradleyHelper.ParseRequestPathCommand(pathCommand);
    if (requestPathCommand.EndsWith(".LEN"))
      return AllenBradleyHelper.PackCommandResponse(new byte[0], false);
    if (requestPathCommand.EndsWith(".DATA[0]"))
      return this.Write(requestPathCommand.Replace(".DATA[0]", ""), Encoding.UTF8.GetString(this.ByteTransform.TransByte(cipCore, 6 + pathCommand.Length, cipCore.Length - 6 - pathCommand.Length)).TrimEnd(new char[1]), Encoding.UTF8).IsSuccess ? AllenBradleyHelper.PackCommandResponse(new byte[0], false) : AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
    ushort uint16_1 = BitConverter.ToUInt16(cipCore, 2 + pathCommand.Length);
    ushort uint16_2 = BitConverter.ToUInt16(cipCore, 4 + pathCommand.Length);
    if (cipCore[0] == (byte) 83)
    {
      int int32 = BitConverter.ToInt32(cipCore, 6 + pathCommand.Length);
      byte[] numArray = this.ByteTransform.TransByte(cipCore, 10 + pathCommand.Length, cipCore.Length - 10 - pathCommand.Length);
      return this.Write(requestPathCommand, numArray, int32).IsSuccess ? AllenBradleyHelper.PackCommandResponse(new byte[0], false) : AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
    }
    byte[] numArray1 = this.ByteTransform.TransByte(cipCore, 6 + pathCommand.Length, cipCore.Length - 6 - pathCommand.Length);
    if (uint16_1 == (ushort) 193 && uint16_2 == (ushort) 1)
    {
      bool flag = false;
      if (numArray1.Length == 2 && numArray1[0] == byte.MaxValue && numArray1[1] == byte.MaxValue)
        flag = true;
      return this.Write(requestPathCommand, flag).IsSuccess ? AllenBradleyHelper.PackCommandResponse(new byte[0], false) : AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
    }
    return this.Write(requestPathCommand, numArray1).IsSuccess ? AllenBradleyHelper.PackCommandResponse(new byte[0], false) : AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
      this.simpleHybird.Dispose();
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt16Array", "")]
  public override OperateResult<short[]> ReadInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<short[]>(this.Read(address, length), (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt16Array", "")]
  public override OperateResult<ushort[]> ReadUInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(this.Read(address, length), (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt32Array", "")]
  public override OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<int[]>(this.Read(address, length), (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt32Array", "")]
  public override OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<uint[]>(this.Read(address, length), (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt64Array", "")]
  public override OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<long[]>(this.Read(address, length), (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt64Array", "")]
  public override OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(this.Read(address, length), (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadFloatArray", "")]
  public override OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<float[]>(this.Read(address, length), (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<double[]>(this.Read(address, length), (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<short[]>> ReadInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<short[]>(result, (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ushort[]>> ReadUInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(result, (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<int[]>(result, (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<uint[]>(result, (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<long[]>(result, (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(result, (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<float[]>(result, (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<double[]>(result, (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <summary>是否需要创建节点信息</summary>
  /// <param name="address">标签地址</param>
  /// <returns>是否包含值</returns>
  protected internal bool IsNeedCreateTag(string address)
  {
    if (!this.CreateTagWithWrite || Regex.IsMatch(address, "\\[[0-9]+\\]$"))
      return false;
    this.GetAddressIndex(ref address);
    this.simpleHybird.Enter();
    bool tag = !this.abValues.ContainsKey(address);
    this.simpleHybird.Leave();
    return tag;
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt16", "")]
  public override OperateResult Write(string address, short value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt16Array", "")]
  public override OperateResult Write(string address, short[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt16", "")]
  public override OperateResult Write(string address, ushort value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt16Array", "")]
  public override OperateResult Write(string address, ushort[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt32", "")]
  public override OperateResult Write(string address, int value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt32Array", "")]
  public override OperateResult Write(string address, int[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt32", "")]
  public override OperateResult Write(string address, uint value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt32Array", "")]
  public override OperateResult Write(string address, uint[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteFloat", "")]
  public override OperateResult Write(string address, float value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteFloatArray", "")]
  public override OperateResult Write(string address, float[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt64", "")]
  public override OperateResult Write(string address, long value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt64Array", "")]
  public override OperateResult Write(string address, long[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt64", "")]
  public override OperateResult Write(string address, ulong value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt64Array", "")]
  public override OperateResult Write(string address, ulong[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteDouble", "")]
  public override OperateResult Write(string address, double value)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, value);
    this.AddTagValue(address, value);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    if (!this.IsNeedCreateTag(address))
      return base.Write(address, values);
    this.AddTagValue(address, values);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, short value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, short[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, ushort value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, ushort[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, int value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, uint value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, float value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, long value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, long[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, ulong value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, ulong[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, double value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    XElement xelement = new XElement((XName) "Tags");
    foreach (KeyValuePair<string, AllenBradleyItemValue> abValue in this.abValues)
    {
      abValue.Value.Name = abValue.Key;
      xelement.Add((object) abValue.Value.ToXml());
    }
    return Encoding.UTF8.GetBytes(xelement.ToString());
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    this.abValues.Clear();
    foreach (XElement element in XElement.Parse(Encoding.UTF8.GetString(content)).Elements())
    {
      if (element.Name == (XName) "AllenBradleyItemValue")
      {
        AllenBradleyItemValue bradleyItemValue = new AllenBradleyItemValue(element);
        this.abValues.Add(bradleyItemValue.Name, bradleyItemValue);
      }
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"AllenBradleyServer[{this.Port}]";
}
