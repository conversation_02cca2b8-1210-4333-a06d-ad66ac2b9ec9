﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.SoftCacheArrayBase
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>内存队列的基类</summary>
public abstract class SoftCacheArrayBase
{
  /// <summary>字节数据流</summary>
  protected byte[] DataBytes = (byte[]) null;
  /// <summary>数据数组变动时的数据锁</summary>
  protected SimpleHybirdLock HybirdLock = new SimpleHybirdLock();

  /// <summary>数据的长度</summary>
  public int ArrayLength { get; protected set; }

  /// <summary>用于从保存的数据对象初始化的</summary>
  /// <param name="dataSave"></param>
  /// <exception cref="T:System.NullReferenceException"></exception>
  public virtual void LoadFromBytes(byte[] dataSave)
  {
  }

  /// <summary>获取原本的数据字节</summary>
  /// <returns>字节数组</returns>
  public byte[] GetAllData()
  {
    byte[] allData = new byte[this.DataBytes.Length];
    this.DataBytes.CopyTo((Array) allData, 0);
    return allData;
  }
}
