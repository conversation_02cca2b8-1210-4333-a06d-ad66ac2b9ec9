﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Toyota.ToyoPuc
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Toyota;

/// <summary>
/// 丰田工机的计算机链接协议实现，通过2PORT-EFR模块实现对PLC的读写数据操作，需要在PLC配置相关的以太网端口信息，才能进行通信。具体的地址参考DEMO程序信息。<br />
/// Toyota Gongki's computer link protocol realizes the reading and writing data operation of the PLC through the 2PORT-EFR module,
/// and the relevant Ethernet port information needs to be configured in the PLC in order to communicate. For specific addresses, refer to Demo UI.
/// </summary>
/// <remarks>
/// 感谢QQ：435416143 对本类的测试，时间：2023年5月2日<br />
/// 位地址支持  K,V,T,C,L,X,Y,M,EK,EV,EC,ET,EL,EX,EY,EM,GX,GY,GM 地址，也可以指定prg参数，例如 prg=1;K000<br />
/// 当然也可以使用字地址访问上述的位地址的，例如 M100~M10F 的位地址，就等于 M10 读字，也就是short。字地址额外支持：S,N,R,D,B,ES,EN,H,U,EB
/// </remarks>
public class ToyoPuc : DeviceTcpNet
{
  /// <summary>实例化一个默认的对象</summary>
  public ToyoPuc()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
  }

  /// <summary>指定IP地址，端口号信息来实例化一个对象</summary>
  /// <param name="ipAddress">IP地址</param>
  /// <param name="port">端口号</param>
  public ToyoPuc(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new ToyoPucMessage();

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    byte[] numArray = new byte[command.Length + 4];
    numArray[2] = BitConverter.GetBytes(command.Length)[0];
    numArray[3] = BitConverter.GetBytes(command.Length)[1];
    command.CopyTo((Array) numArray, 4);
    return numArray;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    if (response == null || response.Length < 4)
      return new OperateResult<byte[]>("Receive data too short: " + response.ToHexString(' '));
    if (response[0] != (byte) 128 /*0x80*/)
      return new OperateResult<byte[]>("FT check failed: " + response.ToHexString(' '));
    if (response[1] > (byte) 0)
      return new OperateResult<byte[]>(response.Length == 4 ? ToyoPuc.GetErrorText(response[1]) : ToyoPuc.GetErrorText(response[4]));
    return response.Length > 5 ? OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(5)) : OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
  }

  private static string GetErrorText(byte code)
  {
    switch (code)
    {
      case 17:
        return StringResources.Language.ToyoPuc11;
      case 32 /*0x20*/:
        return StringResources.Language.ToyoPuc20;
      case 33:
        return StringResources.Language.ToyoPuc21;
      case 35:
        return StringResources.Language.ToyoPuc23;
      case 36:
        return StringResources.Language.ToyoPuc24;
      case 37:
        return StringResources.Language.ToyoPuc25;
      case 52:
        return StringResources.Language.ToyoPuc34;
      case 62:
        return StringResources.Language.ToyoPuc3E;
      case 63 /*0x3F*/:
        return StringResources.Language.ToyoPuc3F;
      case 64 /*0x40*/:
        return StringResources.Language.ToyoPuc40;
      case 65:
        return StringResources.Language.ToyoPuc41;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  private OperateResult<ToyoPuc.WordAddress> ExtraWordAddress(string address, ushort length)
  {
    try
    {
      int length1 = address.IndexOf('.');
      int int32;
      if (length1 > 0)
      {
        int32 = Convert.ToInt32(address.Substring(length1 + 1), 16 /*0x10*/);
        address = address.Substring(0, length1);
      }
      else
      {
        int32 = Convert.ToInt32(address.Substring(address.Length - 1), 16 /*0x10*/);
        address = address.Substring(0, address.Length - 1);
        if (address.StartsWith("EK", StringComparison.OrdinalIgnoreCase) || address.StartsWith("EV", StringComparison.OrdinalIgnoreCase) || address.StartsWith("ET", StringComparison.OrdinalIgnoreCase) || address.StartsWith("EC", StringComparison.OrdinalIgnoreCase) || address.StartsWith("EL", StringComparison.OrdinalIgnoreCase) || address.StartsWith("EX", StringComparison.OrdinalIgnoreCase) || address.StartsWith("EY", StringComparison.OrdinalIgnoreCase) || address.StartsWith("EM", StringComparison.OrdinalIgnoreCase))
        {
          if (address.Length == 2)
            address += "0";
        }
        else if (address.Length == 1)
          address += "0";
      }
      int num = (int32 + (int) length + 15) / 16 /*0x10*/;
      return OperateResult.CreateSuccessResult<ToyoPuc.WordAddress>(new ToyoPuc.WordAddress()
      {
        Address = address,
        BitIndex = int32,
        WordLength = (ushort) num
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<ToyoPuc.WordAddress>("ExtraWordAddress failed: " + ex.Message);
    }
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址类型地址 K,V,T,C,L,X,Y,M,EK,EV,EC,ET,EL,EX,EY,EM 地址，也可以指定程序号(prg)参数，例如 prg=1;K000
  /// </remarks>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (address.IndexOf(".") > 0)
      return HslHelper.ReadBool((IReadWriteNet) this, address, length);
    OperateResult<ToyoPuc.WordAddress> result1 = this.ExtraWordAddress(address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.Read(result1.Content.Address, result1.Content.WordLength);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<bool[]>(result2.Content.ToBoolArray().SelectMiddle<bool>(result1.Content.BitIndex, (int) length));
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址类型地址 K,V,T,C,L,X,Y,M,EK,EV,EC,ET,EL,EX,EY,EM 地址，也可以指定程序号(prg)参数，例如 prg=1;K000
  /// </remarks>
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    if (Regex.IsMatch(address, "prg=[0-9+];", RegexOptions.IgnoreCase))
      return HslHelper.WriteBool((IReadWriteNet) this, address, new bool[1]
      {
        value
      }, insertPoint: true);
    if (address.IndexOf('.') > 0)
      return HslHelper.WriteBool((IReadWriteNet) this, address, new bool[1]
      {
        value
      });
    OperateResult<byte[]> operateResult = ToyoPuc.BuildWriteBoolCommand(address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return HslHelper.WriteBool((IReadWriteNet) this, address, value, insertPoint: true);
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址类型地址 K,V,T,C,L,X,Y,M,EK,EV,EC,ET,EL,EX,EY,EM,S,N,R,D,B,ES,EN,H,U,EB地址，也可以指定程序号(prg)参数，例如 prg=1;K000
  /// </remarks>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = ToyoPuc.BuildReadWordCommand(address, length);
    return !operateResult.IsSuccess ? operateResult : this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址类型地址 K,V,T,C,L,X,Y,M,EK,EV,EC,ET,EL,EX,EY,EM,S,N,R,D,B,ES,EN,H,U,EB地址，也可以指定程序号(prg)参数，例如 prg=1;K000
  /// </remarks>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = ToyoPuc.BuildWriteWordCommand(address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <summary>
  /// 随机读取字地址的数据，可以一次性读取多个地址的数据，返回原始字节类型，需要自己做一些二次分析操作<br />
  /// Random read word address data, you can read multiple address data at one time, return the original byte type, you need to do some secondary analysis operations
  /// </summary>
  /// <param name="address">地址数组信息</param>
  /// <returns>读取成功的结果对象，原始字节数据信息</returns>
  [HslMqttApi("ReadRandom", "")]
  public OperateResult<byte[]> ReadRandom(string[] address)
  {
    OperateResult<List<byte[]>> result = ToyoPuc.BuildReadRandomCommand(address);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : this.ReadFromCoreServer((IEnumerable<byte[]>) result.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<ToyoPuc.WordAddress> analysis = this.ExtraWordAddress(address, length);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) analysis);
    OperateResult<byte[]> read = await this.ReadAsync(analysis.Content.Address, analysis.Content.WordLength);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray().SelectMiddle<bool>(analysis.Content.BitIndex, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    if (Regex.IsMatch(address, "prg=[0-9+];", RegexOptions.IgnoreCase))
    {
      OperateResult operateResult = await HslHelper.WriteBoolAsync((IReadWriteNet) this, address, new bool[1]
      {
        value
      }, insertPoint: true);
      return operateResult;
    }
    if (address.IndexOf('.') > 0)
    {
      OperateResult operateResult = await HslHelper.WriteBoolAsync((IReadWriteNet) this, address, new bool[1]
      {
        value
      });
      return operateResult;
    }
    OperateResult<byte[]> command = ToyoPuc.BuildWriteBoolCommand(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult1 = await this.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult operateResult = await HslHelper.WriteBoolAsync((IReadWriteNet) this, address, value, insertPoint: true);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> command = ToyoPuc.BuildReadWordCommand(address, length);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> command = ToyoPuc.BuildWriteWordCommand(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.ReadRandom(System.String[])" />
  public async Task<OperateResult<byte[]>> ReadRandomAsync(string[] address)
  {
    OperateResult<List<byte[]>> command = ToyoPuc.BuildReadRandomCommand(address);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"ToyoPuc[{this.IpAddress}:{this.Port}]";

  private static OperateResult<byte[]> BuildReadBoolCommand(string address)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, (ushort) 1, true);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    ToyoPucAddress content = from.Content;
    if (content.PRG >= 0)
      return new OperateResult<byte[]>();
    return OperateResult.CreateSuccessResult<byte[]>(new byte[3]
    {
      (byte) 32 /*0x20*/,
      BitConverter.GetBytes(content.AddressStart)[0],
      BitConverter.GetBytes(content.AddressStart)[1]
    });
  }

  private static OperateResult<byte[]> BuildReadWordCommand(string address, ushort length)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, length, false);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    ToyoPucAddress content = from.Content;
    return content.PRG >= 0 ? OperateResult.CreateSuccessResult<byte[]>(new byte[6]
    {
      (byte) 148,
      (byte) content.PRG,
      BitConverter.GetBytes(content.AddressStart)[0],
      BitConverter.GetBytes(content.AddressStart)[1],
      BitConverter.GetBytes(length)[0],
      BitConverter.GetBytes(length)[1]
    }) : OperateResult.CreateSuccessResult<byte[]>(new byte[5]
    {
      (byte) 28,
      BitConverter.GetBytes(content.AddressStart)[0],
      BitConverter.GetBytes(content.AddressStart)[1],
      BitConverter.GetBytes(length)[0],
      BitConverter.GetBytes(length)[1]
    });
  }

  private static OperateResult<byte[]> BuildWriteWordCommand(string address, byte[] value)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, (ushort) 1, false);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    ToyoPucAddress content = from.Content;
    if (content.PRG >= 0)
    {
      byte[] numArray = new byte[4 + value.Length];
      numArray[0] = (byte) 149;
      numArray[1] = (byte) content.PRG;
      numArray[2] = BitConverter.GetBytes(content.AddressStart)[0];
      numArray[3] = BitConverter.GetBytes(content.AddressStart)[1];
      value.CopyTo((Array) numArray, 4);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    byte[] numArray1 = new byte[3 + value.Length];
    numArray1[0] = (byte) 29;
    numArray1[1] = BitConverter.GetBytes(content.AddressStart)[0];
    numArray1[2] = BitConverter.GetBytes(content.AddressStart)[1];
    value.CopyTo((Array) numArray1, 3);
    return OperateResult.CreateSuccessResult<byte[]>(numArray1);
  }

  private static OperateResult<byte[]> BuildWriteBoolCommand(string address, bool value)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, (ushort) 1, true);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if (!from.Content.WriteBitEnabled)
      return new OperateResult<byte[]>($"Address: {address}  is not support write bit, because it is word address");
    ToyoPucAddress content = from.Content;
    if (content.PRG >= 0)
      return new OperateResult<byte[]>("Not supported prg write bool");
    byte[] numArray = new byte[4]
    {
      (byte) 33,
      BitConverter.GetBytes(content.AddressStart)[0],
      BitConverter.GetBytes(content.AddressStart)[1],
      (byte) 0
    };
    if (value)
      numArray[3] = (byte) 1;
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  private static OperateResult<List<byte[]>> BuildReadRandomCommand(string[] address)
  {
    List<string[]> strArrayList = SoftBasic.ArraySplitByLength<string>(address, 80 /*0x50*/);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index1 = 0; index1 < strArrayList.Count; ++index1)
    {
      string[] strArray = strArrayList[index1];
      byte[] numArray = new byte[1 + strArray.Length * 2];
      numArray[0] = (byte) 34;
      for (int index2 = 0; index2 < strArray.Length; ++index2)
      {
        OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(strArray[index2], (ushort) 1, false);
        if (!from.IsSuccess)
          return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from);
        ToyoPucAddress content = from.Content;
        numArray[index2 * 2 + 1] = BitConverter.GetBytes(content.AddressStart)[0];
        numArray[index2 * 2 + 2] = BitConverter.GetBytes(content.AddressStart)[1];
      }
      numArrayList.Add(numArray);
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  private class WordAddress
  {
    public string Address { get; set; }

    public int BitIndex { get; set; }

    public ushort WordLength { get; set; }
  }
}
