﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.Delixi.ElectricalParameters
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Instrument.Delixi;

/// <summary>电参数类</summary>
public class ElectricalParameters
{
  /// <summary>A相电压，单位V</summary>
  public float VoltageA { get; set; }

  /// <summary>B相电压，单位V</summary>
  public float VoltageB { get; set; }

  /// <summary>C相电压，单位V</summary>
  public float VoltageC { get; set; }

  /// <summary>A相电流，单位A</summary>
  public float CurrentA { get; set; }

  /// <summary>B相电流，单位A</summary>
  public float CurrentB { get; set; }

  /// <summary>C相电流，单位A</summary>
  public float CurrentC { get; set; }

  /// <summary>瞬时A相有功功率，单位 kw</summary>
  public float InstantaneousActivePowerA { get; set; }

  /// <summary>瞬时B相有功功率，单位 kw</summary>
  public float InstantaneousActivePowerB { get; set; }

  /// <summary>瞬时C相有功功率，单位 kw</summary>
  public float InstantaneousActivePowerC { get; set; }

  /// <summary>瞬时总有功功率，单位 kw</summary>
  public float InstantaneousTotalActivePower { get; set; }

  /// <summary>瞬时A相无功功率，单位 kvar</summary>
  public float InstantaneousReactivePowerA { get; set; }

  /// <summary>瞬时B相无功功率，单位 kvar</summary>
  public float InstantaneousReactivePowerB { get; set; }

  /// <summary>瞬时C相无功功率，单位 kvar</summary>
  public float InstantaneousReactivePowerC { get; set; }

  /// <summary>瞬时总无功功率，单位 kvar</summary>
  public float InstantaneousTotalReactivePower { get; set; }

  /// <summary>瞬时A相视在功率，单位 kVA</summary>
  public float InstantaneousApparentPowerA { get; set; }

  /// <summary>瞬时B相视在功率，单位 kVA</summary>
  public float InstantaneousApparentPowerB { get; set; }

  /// <summary>瞬时C相视在功率，单位 kVA</summary>
  public float InstantaneousApparentPowerC { get; set; }

  /// <summary>瞬时总视在功率，单位 kVA</summary>
  public float InstantaneousTotalApparentPower { get; set; }

  /// <summary>A相功率因数</summary>
  public float PowerFactorA { get; set; }

  /// <summary>B相功率因数</summary>
  public float PowerFactorB { get; set; }

  /// <summary>C相功率因数</summary>
  public float PowerFactorC { get; set; }

  /// <summary>总功率因数</summary>
  public float TotalPowerFactor { get; set; }

  /// <summary>频率，Hz</summary>
  public float Frequency { get; set; }

  /// <summary>根据德力西电表的原始字节数据，解析出真实的电量参数信息</summary>
  /// <param name="data">原始的字节数据</param>
  /// <param name="byteTransform">字节变换操作</param>
  /// <returns>掂量参数信息</returns>
  public static ElectricalParameters ParseFromDelixi(byte[] data, IByteTransform byteTransform)
  {
    return new ElectricalParameters()
    {
      VoltageA = (float) byteTransform.TransInt16(data, 0) / 10f,
      VoltageB = (float) byteTransform.TransInt16(data, 2) / 10f,
      VoltageC = (float) byteTransform.TransInt16(data, 4) / 10f,
      CurrentA = (float) byteTransform.TransInt16(data, 6) / 100f,
      CurrentB = (float) byteTransform.TransInt16(data, 8) / 100f,
      CurrentC = (float) byteTransform.TransInt16(data, 10) / 100f,
      InstantaneousActivePowerA = (float) byteTransform.TransInt16(data, 12) / 100f,
      InstantaneousActivePowerB = (float) byteTransform.TransInt16(data, 14) / 100f,
      InstantaneousActivePowerC = (float) byteTransform.TransInt16(data, 16 /*0x10*/) / 100f,
      InstantaneousTotalActivePower = (float) byteTransform.TransInt16(data, 18) / 100f,
      InstantaneousReactivePowerA = (float) byteTransform.TransInt16(data, 20) / 100f,
      InstantaneousReactivePowerB = (float) byteTransform.TransInt16(data, 22) / 100f,
      InstantaneousReactivePowerC = (float) byteTransform.TransInt16(data, 24) / 100f,
      InstantaneousTotalReactivePower = (float) byteTransform.TransInt16(data, 26) / 100f,
      InstantaneousApparentPowerA = (float) byteTransform.TransInt16(data, 28) / 100f,
      InstantaneousApparentPowerB = (float) byteTransform.TransInt16(data, 30) / 100f,
      InstantaneousApparentPowerC = (float) byteTransform.TransInt16(data, 32 /*0x20*/) / 100f,
      InstantaneousTotalApparentPower = (float) byteTransform.TransInt16(data, 34) / 100f,
      PowerFactorA = (float) byteTransform.TransInt16(data, 36) / 1000f,
      PowerFactorB = (float) byteTransform.TransInt16(data, 38) / 1000f,
      PowerFactorC = (float) byteTransform.TransInt16(data, 40) / 1000f,
      TotalPowerFactor = (float) byteTransform.TransInt16(data, 42) / 1000f,
      Frequency = (float) byteTransform.TransInt16(data, 44) / 100f
    };
  }
}
