﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.FujiSPBMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.ModBus;
using System;
using System.IO;
using System.Text;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>富士SPB的消息内容</summary>
public class FujiSPBMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 5;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    return this.HeadBytes == null ? 0 : Convert.ToInt32(Encoding.ASCII.GetString(this.HeadBytes, 3, 2), 16 /*0x10*/) * 2 + 2;
  }

  /// <inheritdoc />
  public override bool CheckReceiveDataComplete(byte[] send, MemoryStream ms)
  {
    return ModbusInfo.CheckAsciiReceiveDataComplete(ms.ToArray());
  }
}
