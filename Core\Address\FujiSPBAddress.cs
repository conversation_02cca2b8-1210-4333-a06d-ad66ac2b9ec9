﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.FujiSPBAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Profinet.Fuji;
using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>FujiSPB的地址信息，可以携带数据类型，起始地址操作</summary>
public class FujiSPBAddress : DeviceAddressDataBase
{
  /// <summary>数据的类型代码</summary>
  public string TypeCode { get; set; }

  /// <summary>当是位地址的时候，用于标记的信息</summary>
  public int BitIndex { get; set; }

  /// <summary>获取读写字数据的时候的地址信息内容</summary>
  /// <returns>报文信息</returns>
  public string GetWordAddress()
  {
    return this.TypeCode + FujiSPBHelper.AnalysisIntegerAddress(this.AddressStart);
  }

  /// <summary>获取命令，写入字地址的某一位的命令内容</summary>
  /// <returns>报文信息</returns>
  public string GetWriteBoolAddress()
  {
    int address = this.AddressStart * 2;
    int bitIndex = this.BitIndex;
    if (bitIndex >= 8)
    {
      ++address;
      bitIndex -= 8;
    }
    return $"{this.TypeCode}{FujiSPBHelper.AnalysisIntegerAddress(address)}{bitIndex:X2}";
  }

  /// <summary>按照位为单位获取相关的索引信息</summary>
  /// <returns>位数据信息</returns>
  public int GetBitIndex() => this.AddressStart * 16 /*0x10*/ + this.BitIndex;

  /// <summary>
  /// 从实际的Fuji的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual Fuji address
  /// </summary>
  /// <param name="address">富士的地址数据信息</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<FujiSPBAddress> ParseFrom(string address)
  {
    return FujiSPBAddress.ParseFrom(address, (ushort) 0);
  }

  /// <summary>
  /// 从实际的Fuji的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual Fuji address
  /// </summary>
  /// <param name="address">富士的地址数据信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<FujiSPBAddress> ParseFrom(string address, ushort length)
  {
    FujiSPBAddress fujiSpbAddress = new FujiSPBAddress();
    fujiSpbAddress.Length = length;
    try
    {
      fujiSpbAddress.BitIndex = HslHelper.GetBitIndexInformation(ref address);
      switch (address[0])
      {
        case 'C':
        case 'c':
          if (address[1] == 'N' || address[1] == 'n')
          {
            fujiSpbAddress.TypeCode = "0B";
            fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(2), 10);
            break;
          }
          if (address[1] != 'C' && address[1] != 'c')
            throw new Exception(StringResources.Language.NotSupportedDataType);
          fujiSpbAddress.TypeCode = "05";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(2), 10);
          break;
        case 'D':
        case 'd':
          fujiSpbAddress.TypeCode = "0C";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          break;
        case 'L':
        case 'l':
          fujiSpbAddress.TypeCode = "03";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          break;
        case 'M':
        case 'm':
          fujiSpbAddress.TypeCode = "02";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          break;
        case 'R':
        case 'r':
          fujiSpbAddress.TypeCode = "0D";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          break;
        case 'T':
        case 't':
          if (address[1] == 'N' || address[1] == 'n')
          {
            fujiSpbAddress.TypeCode = "0A";
            fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(2), 10);
            break;
          }
          if (address[1] != 'C' && address[1] != 'c')
            throw new Exception(StringResources.Language.NotSupportedDataType);
          fujiSpbAddress.TypeCode = "04";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(2), 10);
          break;
        case 'W':
        case 'w':
          fujiSpbAddress.TypeCode = "0E";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          break;
        case 'X':
        case 'x':
          fujiSpbAddress.TypeCode = "01";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          break;
        case 'Y':
        case 'y':
          fujiSpbAddress.TypeCode = "00";
          fujiSpbAddress.AddressStart = (int) Convert.ToUInt16(address.Substring(1), 10);
          break;
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<FujiSPBAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
    return OperateResult.CreateSuccessResult<FujiSPBAddress>(fujiSpbAddress);
  }
}
