﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslRedisKeyAttribute
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>对应redis的一个键值信息的内容</summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class HslRedisKeyAttribute : Attribute
{
  /// <summary>键值的名称</summary>
  public string KeyName { get; set; }

  /// <summary>根据键名来读取写入当前的数据信息</summary>
  /// <param name="key">键名</param>
  public HslRedisKeyAttribute(string key) => this.KeyName = key;
}
