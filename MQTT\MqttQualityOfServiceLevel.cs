﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttQualityOfServiceLevel
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// Mqtt消息的质量等级<br />
/// Mqtt message quality level
/// </summary>
public enum MqttQualityOfServiceLevel
{
  /// <summary>最多一次</summary>
  AtMostOnce,
  /// <summary>最少一次</summary>
  AtLeastOnce,
  /// <summary>只有一次</summary>
  ExactlyOnce,
  /// <summary>
  /// 消息只发送到服务器而不触发发布订阅，该消息质量等级只对HSL的MQTT服务器有效<br />
  /// The message is only sent to the server without triggering publish and subscribe, the message quality level is only valid for the HSL MQTT server
  /// </summary>
  OnlyTransfer,
}
