﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.FANUC.FanucRobotServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;

#nullable disable
namespace HslCommunication.Robot.FANUC;

/// <summary>
/// 虚拟的FANUC机器人的服务器对象，支持I,Q,M,D,AI,AQ数据区的数据读写，其中D区是机器人数据存放的区域，相关的数据需要去机器人区读取。详细参见api文档信息。<br />
/// The server object of the virtual FANUC robot supports data reading and writing in I, Q, M, D, AI, and AQ data areas,
/// where D area is the area where the robot data is stored, and related data needs to be read in the robot area. See the api documentation for details.
/// </summary>
/// <remarks>
/// 本虚拟服务器需要商业授权，否则只能运行24小时。
/// <inheritdoc cref="T:HslCommunication.Robot.FANUC.FanucInterfaceNet" path="remarks" />
/// </remarks>
public class FanucRobotServer : DeviceServer
{
  private SoftBuffer dBuffer;
  private SoftBuffer iBuffer;
  private SoftBuffer qBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer aqBuffer;
  private SoftBuffer aiBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个默认的对象信息，并初始化机器人的相关数据<br />
  /// Instantiate a default object information and initialize the relevant data of the robot
  /// </summary>
  public FanucRobotServer()
  {
    this.dBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.iBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.qBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.aqBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.aiBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.dBuffer.SetBytes(SoftBasic.HexStringToBytes("\r\n02 00 4e 00 00 00 00 00\r\n00 00 e4 07 03 00 0c 00 0f 00 22 00 12 00 46 49\r\n4c 45 2d 30 37 38 20 d7 d4 b6 af b1 b8 b7 dd cd\r\nea b3 c9 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 57 41\r\n52 4e 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n02 00 4d 00 00 00 00 00 00 00 e4 07 03 00 0c 00\r\n0f 00 1e 00 0c 00 46 49 4c 45 2d 30 37 37 20 d7\r\nd4 b6 af b1 b8 b7 dd bf aa ca bc 20 28 46 52 41\r\n3a 5c 29 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 57 41 52 4e 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 4c 00 7d 00 00 00 00 00\r\n22 00 e4 07 03 00 0c 00 0f 00 1b 00 06 00 44 4e\r\n45 54 2d 31 32 35 20 b4 d3 b6 af d6 e1 c1 ac bd\r\nd3 cf d0 d6 c3 3a 20 42 64 20 32 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 50 41\r\n55 53 45 2e 47 00 00 00 00 00 00 00 00 00 00 00\r\n4c 00 7d 00 00 00 00 00 22 00 e4 07 03 00 0c 00\r\n0f 00 1b 00 06 00 44 4e 45 54 2d 31 32 35 20 b4\r\nd3 b6 af d6 e1 c1 ac bd d3 cf d0 d6 c3 3a 20 42\r\n64 20 32 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 50 41 55 53 45 2e 47 00 00 00\r\n00 00 00 00 00 00 00 00 0b 00 03 00 00 00 00 00\r\n36 00 e4 07 03 00 0c 00 0f 00 1b 00 06 00 53 52\r\n56 4f 2d 30 30 33 20 b0 b2 c8 ab bf aa b9 d8 d2\r\nd1 ca cd b7 c5 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 53 45\r\n52 56 4f 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n4c 00 7d 00 00 00 00 00 22 00 e4 07 03 00 0c 00\r\n0f 00 1b 00 06 00 44 4e 45 54 2d 31 32 35 20 b4\r\nd3 b6 af d6 e1 c1 ac bd d3 cf d0 d6 c3 3a 20 42\r\n64 20 32 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 50 41 55 53 45 2e 47 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n31 00 b4 43 6f 2f ed bb ea fd 8b 43 b4 79 31 43\r\na3 fe b3 c2 08 80 21 40 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 01 00 00 00 00 00\r\n00 00 01 00 08 ff 96 ba 03 93 0e 39 33 b3 28 bb\r\nb0 3f 81 b7 f6 f6 b6 b8 33 33 0b 39 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 00 00 01 00 00 00\r\n00 00 00 00 31 00 b4 43 6f 2f ed bb ea fd 8b 43\r\nb4 79 31 43 a3 fe b3 c2 08 80 21 40 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 01 00\r\n00 00 00 00 00 00 01 00 08 ff 96 ba 03 93 0e 39\r\n33 b3 28 bb b0 3f 81 b7 f6 f6 b6 b8 33 33 0b 39\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00\r\n01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 d5 f4 a2 43 61 60 d2 42\r\n1a 78 ec 43 4b 46 0e 41 8d a1 0e 42 de 4c cb 42\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00\r\n01 00 01 00 00 00 00 00 00 00 01 00 8f c2 31 41\r\n8f c2 b1 41 ec 51 05 42 8f c2 31 42 33 33 5e 42\r\nec 51 85 42 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 ff ff ff ff 00 00 00 00 00 00 c3 dd 5e 43\r\na8 92 21 43 36 99 da 43 67 a4 db c2 8a 58 8c c1\r\n0c 77 18 42 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 00 00 00 00 01 00 00 00 00 00 00 00 01 00\r\n8f c2 b1 41 8f c2 31 42 ec 51 85 42 8f c2 b1 42\r\n33 33 de 42 ec 51 05 43 00 00 00 00 00 00 00 00\r\n00 00 00 00 01 00 ff ff ff ff 00 00 00 00 00 00\r\nec 80 19 43 4e 0b e6 42 b5 fe 9f 43 5d c7 25 43\r\nc4 cd 81 c1 bd c5 cd 42 00 00 00 00 00 00 00 00\r\n00 00 00 00 01 00 00 00 00 00 01 00 00 00 00 00\r\n01 00 01 00 ec 51 05 42 ec 51 85 42 e1 fa c7 42\r\nec 51 05 43 66 a6 26 43 e1 fa 47 43 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 ff ff ff ff 00 00\r\n00 00 00 00 5a c1 0d 42 01 b7 00 42 21 62 04 43\r\n4e d0 32 43 f7 1b bc 3f a8 b5 04 43 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 00 00 00 00 01 00\r\n00 00 00 00 01 00 01 00 8f c2 31 42 8f c2 b1 42\r\nec 51 05 43 8f c2 31 43 33 33 5e 43 ec 51 85 43\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 ff ff\r\nff ff 00 00 00 00 00 00 fd ac 96 c2 8d af db c1\r\n40 d3 bb c2 be 5d 13 43 51 2e 0f c2 ac 8b 0e c3\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00\r\n00 00 00 00 00 00 01 00 01 00 01 00 33 33 5e 42\r\n33 33 de 42 66 a6 26 43 33 33 5e 43 00 e0 8a 43\r\n66 a6 a6 43 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 ff ff ff ff 00 00 00 00 00 00 04 63 a4 c2\r\n8e 77 de c2 e0 ae 9f c3 fc ae e9 42 d8 29 2c 42\r\n85 9f 02 c2 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 00 00 00 00 00 00 00 00 01 00 01 00 01 00\r\nec 51 85 42 ec 51 05 43 e1 fa 47 43 ec 51 85 43\r\n66 a6 a6 43 e1 fa c7 43 00 00 00 00 00 00 00 00\r\n00 00 00 00 01 00 ff ff ff ff 00 00 00 00 00 00\r\n7a 3d 26 41 32 82 90 c2 80 e5 07 c4 22 64 2e c3\r\n79 cf d2 41 75 c0 0f 43 00 00 00 00 00 00 00 00\r\n00 00 00 00 01 00 00 00 00 00 00 00 00 00 01 00\r\n01 00 01 00 3d 8a 9b 42 3d 8a 1b 43 5c 4f 69 43\r\n3d 8a 9b 43 cd 6c c2 43 5c 4f e9 43 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 ff ff ff ff 00 00\r\n00 00 00 00 89 6e e1 40 8c e2 a3 42 56 57 0c c4\r\nff 7a 10 43 52 f6 9d 42 ab a8 03 c3 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 00 00 00 00 01 00\r\n00 00 01 00 01 00 01 00 8f c2 b1 42 8f c2 31 43\r\nec 51 85 43 8f c2 b1 43 33 33 de 43 ec 51 05 44\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 ff ff\r\nff ff 00 00 00 00 00 00 e4 09 27 c2 0c a2 8c 42\r\n80 f5 d2 c3 4b 74 b9 c1 58 a9 05 c1 72 1b 96 42\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00\r\n00 00 01 00 00 00 01 00 02 00 01 00 e1 fa c7 42\r\ne1 fa 47 43 29 fc 95 43 e1 fa c7 43 9a f9 f9 43\r\n29 fc 15 44 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 ff ff ff ff 00 00 00 00 00 00 9b 51 b7 40\r\n05 7a 13 42 f1 2e 8a c3 b2 c6 6d 42 6e 3a 11 c2\r\n31 46 65 42 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 00 00 00 00 01 00 00 00 01 00 02 00 01 00\r\n33 33 de 42 33 33 5e 43 66 a6 a6 43 33 33 de 43\r\n00 e0 0a 44 66 a6 26 44 00 00 00 00 00 00 00 00\r\n00 00 00 00 01 00 ff ff ff ff 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 bf 59 0b 00 00 00 00 00\r\n54 2f a7 b7 00 00 c8 41 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 80 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 01 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 ff ff ff ff 00 00 00 00 00 00 4d 4f 56 45\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 d5 f4 a2 43 61 60 d2 42 1a 78 ec 43\r\n4b 46 0e 41 8d a1 0e 42 de 4c cb 42 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 00 00 01 00 01 00\r\n00 00 00 00 00 00 c3 dd 5e 43 a8 92 21 43 36 99\r\nda 43 67 a4 db c2 8a 58 8c c1 0c 77 18 42 00 00\r\n00 00 00 00 00 00 00 00 00 00 01 00 00 00 00 00\r\n01 00 00 00 00 00 00 00 ec 80 19 43 4e 0b e6 42\r\nb5 fe 9f 43 5d c7 25 43 c4 cd 81 c1 bd c5 cd 42\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00\r\n00 00 01 00 00 00 00 00 01 00 5a c1 0d 42 01 b7\r\n00 42 21 62 04 43 4e d0 32 43 f7 1b bc 3f a8 b5\r\n04 43 00 00 00 00 00 00 00 00 00 00 00 00 01 00\r\n00 00 00 00 01 00 00 00 00 00 01 00 fd ac 96 c2\r\n8d af db c1 40 d3 bb c2 be 5d 13 43 51 2e 0f c2\r\nac 8b 0e c3 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 00 00 00 00 00 00 00 00 01 00 01 00 04 63\r\na4 c2 8e 77 de c2 e0 ae 9f c3 fc ae e9 42 d8 29\r\n2c 42 85 9f 02 c2 00 00 00 00 00 00 00 00 00 00\r\n00 00 01 00 00 00 00 00 00 00 00 00 01 00 01 00\r\n7a 3d 26 41 32 82 90 c2 80 e5 07 c4 22 64 2e c3\r\n79 cf d2 41 75 c0 0f 43 00 00 00 00 00 00 00 00\r\n00 00 00 00 01 00 00 00 00 00 00 00 00 00 01 00\r\n01 00 89 6e e1 40 8c e2 a3 42 56 57 0c c4 ff 7a\r\n10 43 52 f6 9d 42 ab a8 03 c3 00 00 00 00 00 00\r\n00 00 00 00 00 00 01 00 00 00 00 00 01 00 00 00\r\n01 00 01 00 e4 09 27 c2 0c a2 8c 42 80 f5 d2 c3\r\n4b 74 b9 c1 58 a9 05 c1 72 1b 96 42 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 00 00 00 00 01 00\r\n00 00 01 00 02 00 9b 51 b7 40 05 7a 13 42 f1 2e\r\n8a c3 b2 c6 6d 42 6e 3a 11 c2 31 46 65 42 00 00\r\n00 00 00 00 00 00 00 00 00 00 01 00 00 00 00 00\r\n01 00 00 00 01 00 02 00 d5 f4 a2 43 61 60 d2 42\r\n1a 78 ec 43 4b 46 0e 41 8d a1 0e 42 de 4c cb 42\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00\r\n01 00 01 00 00 00 00 00 00 00 c3 dd 5e 43 a8 92\r\n21 43 36 99 da 43 67 a4 db c2 8a 58 8c c1 0c 77\r\n18 42 00 00 00 00 00 00 00 00 00 00 00 00 01 00\r\n00 00 00 00 01 00 00 00 00 00 00 00 ec 80 19 43\r\n4e 0b e6 42 b5 fe 9f 43 5d c7 25 43 c4 cd 81 c1\r\nbd c5 cd 42 00 00 00 00 00 00 00 00 00 00 00 00\r\n01 00 00 00 00 00 01 00 00 00 00 00 01 00 5a c1\r\n0d 42 01 b7 00 42 21 62 04 43 4e d0 32 43 f7 1b\r\nbc 3f a8 b5 04 43 00 00 00 00 00 00 00 00 00 00\r\n00 00 01 00 00 00 00 00 01 00 00 00 00 00 01 00\r\nfd ac 96 c2 8d af db c1 40 d3 bb c2 be 5d 13 43\r\n51 2e 0f c2 ac 8b 0e c3 00 00 00 00 00 00 00 00\r\n00 00 00 00 01 00 00 00 00 00 00 00 00 00 01 00\r\n01 00 04 63 a4 c2 8e 77 de c2 e0 ae 9f c3 fc ae\r\ne9 42 d8 29 2c 42 85 9f 02 c2 00 00 00 00 00 00\r\n00 00 00 00 00 00 01 00 00 00 00 00 00 00 00 00\r\n01 00 01 00 7a 3d 26 41 32 82 90 c2 80 e5 07 c4\r\n22 64 2e c3 79 cf d2 41 75 c0 0f 43 00 00 00 00\r\n00 00 00 00 00 00 00 00 01 00 00 00 00 00 00 00\r\n00 00 01 00 01 00 89 6e e1 40 8c e2 a3 42 56 57\r\n0c c4 ff 7a 10 43 52 f6 9d 42 ab a8 03 c3 00 00\r\n00 00 00 00 00 00 00 00 00 00 01 00 00 00 00 00\r\n01 00 00 00 01 00 01 00 e4 09 27 c2 0c a2 8c 42\r\n80 f5 d2 c3 4b 74 b9 c1 58 a9 05 c1 72 1b 96 42\r\n00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 00\r\n00 00 01 00 00 00 01 00 02 00 9b 51 b7 40 05 7a\r\n13 42 f1 2e 8a c3 b2 c6 6d 42 6e 3a 11 c2 31 46\r\n65 42 00 00 00 00 00 00 00 00 00 00 00 00 01 00\r\n00 00 00 00 01 00 00 00 01 00 02 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 4f 70 65 6e\r\n20 47 72 69 70 70 65 72 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 43 6c 6f 73\r\n65 20 47 72 69 70 70 65 72 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 2a 49 4d 53\r\n54 50 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 2a 48 6f 6c\r\n64 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 2a 53 46 53\r\n50 44 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 43 6d 64 20\r\n65 6e 61 62 6c 65 64 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 46 61 75 6c\r\n74 20 72 65 73 65 74 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 52 65 6d 6f\r\n74 65 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 48 6f 6c 64\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 43 79 63 6c\r\n65 20 73 74 61 72 74 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 48 6f 6c 64\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 46 61 75 6c\r\n74 20 4c 45 44 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 30 31 2d 4a\r\n41 4e 2d 38 30 20 30 30 3a 30 30 20 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 20 20 20 20\r\n20 20 20 20 20 20 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 32 31 2d 46\r\n45 42 2d 32 30 20 30 39 3a 35 31 20 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 52 46 49 44\r\n20 64 61 74 65 20 69 6e 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 52 46 49 44\r\n20 63 6f 6d 6d 61 6e 64 20 69 6e 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 52 46 49 44\r\n20 64 61 74 65 20 6f 75 74 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00\r\n00 00 00 00 00 00 00 00 00 00 00 00\r\n"), 0);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "Batch read byte array information, need to specify the address and length, return the original byte array")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte, ushort> result = FanucHelper.AnalysisFanucAddress(address, false);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (result.Content2 == (ushort) 0)
      return new OperateResult<byte[]>("Address start can't be zero");
    --result.Content2;
    if (result.Content1 == (byte) 8)
      return OperateResult.CreateSuccessResult<byte[]>(this.dBuffer.GetBytes((int) result.Content2 * 2, (int) length * 2));
    if (result.Content1 == (byte) 10)
      return OperateResult.CreateSuccessResult<byte[]>(this.aiBuffer.GetBytes((int) result.Content2 * 2, (int) length * 2));
    return result.Content1 == (byte) 12 ? OperateResult.CreateSuccessResult<byte[]>(this.aqBuffer.GetBytes((int) result.Content2 * 2, (int) length * 2)) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType + ", Current address not support word read/write");
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "Write the original byte array data to the specified address, and return whether the write was successful")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte, ushort> result = FanucHelper.AnalysisFanucAddress(address, false);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (result.Content2 == (ushort) 0)
      return new OperateResult("Address start can't be zero");
    --result.Content2;
    if (result.Content1 == (byte) 8)
      this.dBuffer.SetBytes(value, (int) result.Content2 * 2);
    else if (result.Content1 == (byte) 10)
    {
      this.aiBuffer.SetBytes(value, (int) result.Content2 * 2);
    }
    else
    {
      if (result.Content1 != (byte) 12)
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType + ", Current address not support word read/write");
      this.aqBuffer.SetBytes(value, (int) result.Content2 * 2);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "Batch read bool array information, need to specify the address and length, return bool array")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<byte, ushort> result = FanucHelper.AnalysisFanucAddress(address, true);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    if (result.Content2 == (ushort) 0)
      return new OperateResult<bool[]>("Address start can't be zero");
    --result.Content2;
    if (result.Content1 == (byte) 70)
      return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.iBuffer.GetBytes((int) result.Content2, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
    if (result.Content1 == (byte) 72)
      return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.qBuffer.GetBytes((int) result.Content2, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
    return result.Content1 == (byte) 76 ? OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.mBuffer.GetBytes((int) result.Content2, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType + ", Current address not support bool read/write");
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "Batch write bool array data, return whether the write was successful")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<byte, ushort> result = FanucHelper.AnalysisFanucAddress(address, true);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    if (result.Content2 == (ushort) 0)
      return new OperateResult("Address start can't be zero");
    --result.Content2;
    if (result.Content1 == (byte) 70)
      this.iBuffer.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), (int) result.Content2);
    else if (result.Content1 == (byte) 72)
    {
      this.qBuffer.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), (int) result.Content2);
    }
    else
    {
      if (result.Content1 != (byte) 76)
        return new OperateResult(StringResources.Language.NotSupportedDataType + ", Current address not support bool read/write");
      this.mBuffer.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), (int) result.Content2);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new FanucRobotMessage();

  /// <inheritdoc />
  protected override OperateResult ThreadPoolLoginAfterClientCheck(
    PipeSession session,
    IPEndPoint endPoint)
  {
    CommunicationPipe communication = session.Communication;
    FanucRobotMessage fanucRobotMessage = new FanucRobotMessage();
    OperateResult<byte[]> message1 = communication.ReceiveMessage((INetMessage) fanucRobotMessage, (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
    if (!message1.IsSuccess)
      return (OperateResult) message1;
    byte[] bytes1 = SoftBasic.HexStringToBytes("01 00 00 00 00 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00");
    this.LogSendMessage(bytes1, session);
    OperateResult operateResult1 = communication.Send(bytes1);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> message2 = communication.ReceiveMessage((INetMessage) fanucRobotMessage, (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
    if (!message2.IsSuccess)
      return (OperateResult) message2;
    byte[] bytes2 = SoftBasic.HexStringToBytes("03 00 01 00 00 00 00 00 00 01 00 00 00 00 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 00 01 d4 10 0e 00 00 30 3a 00 00 01 01 00 00 00 00 00 00 01 01 ff 02 00 00 7c 21");
    this.LogSendMessage(bytes2, session);
    OperateResult operateResult2 = communication.Send(bytes2);
    return !message2.IsSuccess ? operateResult2 : base.ThreadPoolLoginAfterClientCheck(session, endPoint);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    int select = receive[2] == (byte) 6 ? (int) receive[43] : (receive[2] == (byte) 8 ? (int) receive[43] : (receive[2] == (byte) 9 ? (int) receive[51] : 0));
    byte[] numArray;
    if (select == 56)
      numArray = this.GetCommandBackMessage(receive[2]);
    else if (receive[2] == (byte) 6)
    {
      numArray = this.GetReadBackMessage(receive, select);
    }
    else
    {
      if (receive[2] != (byte) 8 && receive[2] != (byte) 9)
        return new OperateResult<byte[]>($"Not supported function: {receive[2]}");
      numArray = this.GetWriteBackMessage(receive, select);
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  private byte[] GetCommandBackMessage(byte command)
  {
    byte[] bytes = SoftBasic.HexStringToBytes("\r\n03 00 08 00 00 00 00 00 00 01 00 00 00 00 00 00\r\n00 01 00 00 00 00 00 00 00 00 00 00 00 00 08 d4\r\n10 0e 00 00 30 3a 00 00 01 01 00 00 00 00 00 00\r\n01 01 ff 04 00 00 7c 21");
    bytes[2] = command;
    return bytes;
  }

  private byte[] GetReadBackMessage(byte[] command, int select)
  {
    ushort uint16_1 = BitConverter.ToUInt16(command, 44);
    ushort uint16_2 = BitConverter.ToUInt16(command, 46);
    int num;
    switch (select)
    {
      case 8:
        return FanucHelper.BuildReadResponseData(this.dBuffer.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
      case 10:
        return FanucHelper.BuildReadResponseData(this.aiBuffer.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
      case 12:
        return FanucHelper.BuildReadResponseData(this.aqBuffer.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
      case 70:
      case 76:
        num = 1;
        break;
      default:
        num = select == 72 ? 1 : 0;
        break;
    }
    if (num == 0)
      return FanucHelper.BuildReadResponseData(new byte[0]);
    int index = (int) uint16_1 / 8 * 8;
    int length = (int) uint16_1 + (int) uint16_2 - index;
    SoftBuffer softBuffer;
    switch (select)
    {
      case 70:
        softBuffer = this.iBuffer;
        break;
      case 76:
        softBuffer = this.mBuffer;
        break;
      default:
        softBuffer = this.qBuffer;
        break;
    }
    return FanucHelper.BuildReadResponseData(SoftBasic.BoolArrayToByte(((IEnumerable<byte>) softBuffer.GetBytes(index, length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()));
  }

  private byte[] GetWriteBackMessage(byte[] command, int select)
  {
    if (!this.EnableWrite)
      return (byte[]) null;
    ushort uint16_1 = BitConverter.ToUInt16(command, command[2] == (byte) 8 ? 44 : 52);
    ushort uint16_2 = BitConverter.ToUInt16(command, command[2] == (byte) 8 ? 46 : 54);
    int num = command[2] == (byte) 8 ? 48 /*0x30*/ : 56;
    if (select == 8 || select == 10 || select == 12)
    {
      switch (select)
      {
        case 8:
          this.dBuffer.SetBytes(SoftBasic.ArraySelectMiddle<byte>(command, num, (int) uint16_2 * 2), (int) uint16_1 * 2, (int) uint16_2 * 2);
          break;
        case 10:
          this.aiBuffer.SetBytes(SoftBasic.ArraySelectMiddle<byte>(command, num, (int) uint16_2 * 2), (int) uint16_1 * 2, (int) uint16_2 * 2);
          break;
        case 12:
          this.aqBuffer.SetBytes(SoftBasic.ArraySelectMiddle<byte>(command, num, (int) uint16_2 * 2), (int) uint16_1 * 2, (int) uint16_2 * 2);
          break;
      }
    }
    else
    {
      int index = (int) uint16_1 % 8;
      byte[] array = ((IEnumerable<bool>) SoftBasic.ArraySelectMiddle<bool>(SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, num)), index, (int) uint16_2)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
      switch (select)
      {
        case 70:
          this.iBuffer.SetBytes(array, (int) uint16_1);
          break;
        case 72:
          this.qBuffer.SetBytes(array, (int) uint16_1);
          break;
        case 76:
          this.mBuffer.SetBytes(array, (int) uint16_1);
          break;
      }
    }
    byte[] bytes = SoftBasic.HexStringToBytes("03 00 09 00 00 00 00 00 00 01 00 00 00 00 00 00 00 01 00 00 00 00 00 00 \r\n00 00 00 00 00 00 09 d4 10 0e 00 00 30 3a 00 00 01 01 00 00 00 00 00 00 01 01 ff 04 00 00 7c 21");
    bytes[2] = command[2];
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 393216 /*0x060000*/)
      throw new Exception("File is not correct");
    this.iBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.qBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.aiBuffer.SetBytes(content, 196608 /*0x030000*/, 0, 65536 /*0x010000*/);
    this.aqBuffer.SetBytes(content, 262144 /*0x040000*/, 0, 65536 /*0x010000*/);
    this.dBuffer.SetBytes(content, 327680 /*0x050000*/, 0, 65536 /*0x010000*/);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[393216 /*0x060000*/];
    Array.Copy((Array) this.iBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.qBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.mBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.aiBuffer.GetBytes(), 0, (Array) destinationArray, 196608 /*0x030000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.aqBuffer.GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dBuffer.GetBytes(), 0, (Array) destinationArray, 327680 /*0x050000*/, 65536 /*0x010000*/);
    return destinationArray;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.iBuffer?.Dispose();
      this.qBuffer?.Dispose();
      this.mBuffer?.Dispose();
      this.dBuffer?.Dispose();
      this.aiBuffer?.Dispose();
      this.aqBuffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"FanucRobotServer[{this.Port}]";
}
