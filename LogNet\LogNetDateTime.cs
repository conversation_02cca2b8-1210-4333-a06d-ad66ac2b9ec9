﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.LogNetDateTime
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>
/// 一个日志组件，可以根据时间来区分不同的文件存储<br />
/// A log component that can distinguish different file storages based on time
/// </summary>
/// <remarks>
/// 此日志实例将根据日期时间来进行分类，支持的时间分类如下：
/// <list type="number">
/// <item>分钟</item>
/// <item>小时</item>
/// <item>天</item>
/// <item>周</item>
/// <item>月份</item>
/// <item>季度</item>
/// <item>年份</item>
/// </list>
/// 当然你也可以指定允许存在多少个日志文件，比如我允许存在最多10个文件，如果你的日志是根据天来分文件的，那就是10天的数据。
/// 同理，如果你的日志是根据年来分文件的，那就是10年的日志文件。
/// </remarks>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\LogNet\LogNetSingle.cs" region="Example3" title="日期存储实例化" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\LogNet\LogNetSingle.cs" region="Example4" title="基本的使用" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\LogNet\LogNetSingle.cs" region="Example5" title="所有日志不存储" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\LogNet\LogNetSingle.cs" region="Example6" title="仅存储ERROR等级" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\LogNet\LogNetSingle.cs" region="Example7" title="不指定路径" />
/// </example>
public class LogNetDateTime : LogPathBase, ILogNet, IDisposable
{
  private GenerateMode generateMode = GenerateMode.ByEveryYear;
  private int fileSize = -1;

  /// <summary>
  /// 实例化一个根据时间存储的日志组件，需要指定每个文件的存储时间范围<br />
  /// Instantiate a log component based on time, you need to specify the storage time range for each file
  /// </summary>
  /// <param name="filePath">文件存储的路径</param>
  /// <param name="generateMode">存储文件的间隔</param>
  /// <param name="fileQuantity">指定当前的日志文件数量上限，如果小于0，则不限制，文件一直增加，如果设置为10，就限制最多10个文件，会删除最近时间的10个文件之外的文件。</param>
  public LogNetDateTime(string filePath, GenerateMode generateMode = GenerateMode.ByEveryYear, int fileQuantity = -1)
  {
    this.filePath = filePath;
    this.generateMode = generateMode;
    this.LogSaveMode = LogSaveMode.Time;
    this.controlFileQuantity = fileQuantity;
    if (string.IsNullOrEmpty(filePath) || Directory.Exists(filePath))
      return;
    Directory.CreateDirectory(filePath);
  }

  /// <summary>
  /// 实例化一个根据时间存储的日志组件，需要指定每个文件的存储时间范围<br />
  /// Instantiate a log component based on time, you need to specify the storage time range for each file
  /// </summary>
  /// <param name="filePath">文件存储的路径</param>
  /// <param name="generateMode">存储文件的间隔</param>
  /// <param name="timeQuantity">指定当前的日志文件的时间单位数量，如果小于0，则不限制，文件一直增加，如果设置为10，就限制最多10个单位的文件，会删除最近时间的10天之外的文件。</param>
  /// <param name="fileSize">指定文件的大小</param>
  public LogNetDateTime(
    string filePath,
    GenerateMode generateMode,
    int timeQuantity,
    int fileSize)
    : this(filePath, generateMode, timeQuantity)
  {
    this.fileSize = fileSize;
  }

  private string GetFileSaveName(int index)
  {
    if (string.IsNullOrEmpty(this.filePath))
      return string.Empty;
    string str = index > 0 ? $"({index}).txt" : ".txt";
    switch (this.generateMode)
    {
      case GenerateMode.ByEveryMinute:
        return Path.Combine(this.filePath, LogNetManagment.LogFileHeadString + DateTime.Now.ToString("yyyyMMdd_HHmm") + str);
      case GenerateMode.ByEveryHour:
        return Path.Combine(this.filePath, LogNetManagment.LogFileHeadString + DateTime.Now.ToString("yyyyMMdd_HH") + str);
      case GenerateMode.ByEveryDay:
        return Path.Combine(this.filePath, LogNetManagment.LogFileHeadString + DateTime.Now.ToString("yyyyMMdd") + str);
      case GenerateMode.ByEveryWeek:
        int weekOfYear = new GregorianCalendar().GetWeekOfYear(DateTime.Now, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
        return Path.Combine(this.filePath, $"{LogNetManagment.LogFileHeadString}{DateTime.Now.Year.ToString()}_W{weekOfYear.ToString()}{str}");
      case GenerateMode.ByEveryMonth:
        return Path.Combine(this.filePath, LogNetManagment.LogFileHeadString + DateTime.Now.ToString("yyyy_MM") + str);
      case GenerateMode.ByEverySeason:
        string filePath = this.filePath;
        string[] strArray = new string[5]
        {
          LogNetManagment.LogFileHeadString,
          null,
          null,
          null,
          null
        };
        DateTime now = DateTime.Now;
        strArray[1] = now.Year.ToString();
        strArray[2] = "_Q";
        now = DateTime.Now;
        strArray[3] = (now.Month / 3 + 1).ToString();
        strArray[4] = str;
        string path2 = string.Concat(strArray);
        return Path.Combine(filePath, path2);
      case GenerateMode.ByEveryYear:
        return Path.Combine(this.filePath, LogNetManagment.LogFileHeadString + DateTime.Now.Year.ToString() + str);
      default:
        return string.Empty;
    }
  }

  /// <inheritdoc />
  protected override string GetFileSaveName()
  {
    if (string.IsNullOrEmpty(this.filePath))
      return string.Empty;
    if (this.fileSize <= 0)
      return this.GetFileSaveName(0);
    int index = 0;
    string fileSaveName;
    while (true)
    {
      fileSaveName = this.GetFileSaveName(index);
      if (File.Exists(fileSaveName))
      {
        try
        {
          if (new FileInfo(fileSaveName).Length < (long) this.fileSize)
            return fileSaveName;
        }
        catch
        {
          return fileSaveName;
        }
        ++index;
      }
      else
        break;
    }
    return fileSaveName;
  }

  private DateTime GetDeleteTime()
  {
    switch (this.generateMode)
    {
      case GenerateMode.ByEveryMinute:
        return DateTime.Now.AddMinutes((double) -this.controlFileQuantity);
      case GenerateMode.ByEveryHour:
        return DateTime.Now.AddHours((double) -this.controlFileQuantity);
      case GenerateMode.ByEveryDay:
        return DateTime.Now.AddDays((double) -this.controlFileQuantity);
      case GenerateMode.ByEveryWeek:
        return DateTime.Now.AddDays((double) (-this.controlFileQuantity * 7));
      case GenerateMode.ByEveryMonth:
        return DateTime.Now.AddMonths(-this.controlFileQuantity);
      case GenerateMode.ByEverySeason:
        return DateTime.Now.AddMonths(-this.controlFileQuantity * 3);
      case GenerateMode.ByEveryYear:
        return DateTime.Now.AddYears(-this.controlFileQuantity);
      default:
        return DateTime.Now.AddDays((double) -this.controlFileQuantity);
    }
  }

  /// <inheritdoc />
  protected override void OnWriteCompleted(bool createNewLogFile)
  {
    if (this.fileSize <= 0)
      base.OnWriteCompleted(createNewLogFile);
    else if (createNewLogFile && this.controlFileQuantity > 1)
    {
      try
      {
        string[] existLogFileNames = this.GetExistLogFileNames();
        if (existLogFileNames.Length > this.controlFileQuantity)
        {
          List<FileInfo> fileInfoList = new List<FileInfo>();
          for (int index = 0; index < existLogFileNames.Length; ++index)
            fileInfoList.Add(new FileInfo(existLogFileNames[index]));
          fileInfoList.Sort((Comparison<FileInfo>) ((m, n) => m.CreationTime.CompareTo(n.CreationTime)));
          DateTime deleteTime = this.GetDeleteTime();
          for (int index = 0; index < fileInfoList.Count && fileInfoList[index].CreationTime < deleteTime; ++index)
            File.Delete(fileInfoList[index].FullName);
        }
      }
      catch
      {
      }
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"LogNetDateTime[{this.generateMode}]";
}
