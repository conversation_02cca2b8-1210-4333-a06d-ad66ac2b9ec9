﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.CommunicationTcpServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Pipe;
using HslCommunication.LogNet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Threading;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>仅仅包含 TCP 服务器</summary>
public class CommunicationTcpServer
{
  private List<string> TrustedClients = (List<string>) null;
  private bool IsTrustedClientsOnly = false;
  private object lock_trusted_clients = new object();
  /// <summary>核心的socket服务器</summary>
  protected Socket socketServer = (Socket) null;
  private bool useSSL = false;
  private X509Certificate certificate;
  private Action<PipeSslNet> pipeSslNetAction = (Action<PipeSslNet>) null;
  private AsyncCallback beginAcceptCallback = (AsyncCallback) null;

  /// <summary>实例化一个默认的对象</summary>
  public CommunicationTcpServer()
  {
    this.beginAcceptCallback = new AsyncCallback(this.AsyncAcceptCallback);
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceServer.IsStarted" />
  public bool IsStarted { get; protected set; }

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceServer.Port" />
  public int Port { get; set; } = 10000;

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceServer.EnableIPv6" />
  public bool EnableIPv6 { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Core.Device.DeviceServer.SocketKeepAliveTime" />
  public int SocketKeepAliveTime { get; set; } = -1;

  /// <inheritdoc cref="P:HslCommunication.Core.Net.BinaryCommunication.LogNet" />
  public ILogNet LogNet { get; set; }

  /// <summary>
  /// 记录一些调试日志的委托，将会进行输出调试文本。<br />
  /// The delegate that records some debug logs will output debug text.
  /// </summary>
  public Action<string> LogDebugMessage { get; set; }

  /// <summary>使用SSL通信，传递一个证书的对象</summary>
  /// <param name="cert">证书对象</param>
  public void UseSSL(X509Certificate cert)
  {
    this.useSSL = true;
    this.certificate = cert;
  }

  /// <summary>使用SSL通信，传递一个证书的路径，以及证书的密码</summary>
  /// <param name="cert">传递一个证书</param>
  /// <param name="password">证书的密码</param>
  public void UseSSL(string cert, string password = "")
  {
    this.useSSL = true;
    this.certificate = string.IsNullOrEmpty(password) ? new X509Certificate(cert) : new X509Certificate(cert, password);
  }

  /// <summary>
  /// 设置一个SSL的管道操作对象，在管道实例化之后，可以进行一些初始化的属性设置，例如自定义 SslProtocols 枚举<br />
  /// </summary>
  /// <param name="action">设置方法</param>
  public void SetSslPipeAction(Action<PipeSslNet> action) => this.pipeSslNetAction = action;

  /// <summary>
  /// 指定端口号来启动服务器的引擎<br />
  /// Specify the port number to start the server's engine
  /// </summary>
  /// <param name="port">指定一个端口号</param>
  public void ServerStart(int port)
  {
    if (this.IsStarted)
      return;
    if (!this.EnableIPv6)
    {
      this.socketServer = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
      this.socketServer.Bind((EndPoint) new IPEndPoint(IPAddress.Any, port));
    }
    else
    {
      this.socketServer = new Socket(AddressFamily.InterNetworkV6, SocketType.Stream, ProtocolType.Tcp);
      this.socketServer.Bind((EndPoint) new IPEndPoint(IPAddress.IPv6Any, port));
    }
    this.socketServer.Listen(500);
    this.socketServer.BeginAccept(this.beginAcceptCallback, (object) this.socketServer);
    this.IsStarted = true;
    this.Port = port;
    this.ExtraOnStart();
    this.LogDebugMsg(StringResources.Language.NetEngineStart);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationTcpServer.ServerStart(System.Int32)" />
  public void ServerStart() => this.ServerStart(this.Port);

  /// <summary>
  /// 关闭服务器的引擎<br />
  /// Shut down the server's engine
  /// </summary>
  public void ServerClose()
  {
    if (!this.IsStarted)
      return;
    this.IsStarted = false;
    this.ExtraOnClose();
    NetSupport.CloseSocket(this.socketServer);
    this.LogDebugMsg(StringResources.Language.NetEngineClose);
  }

  /// <summary>
  /// 设置并启动受信任的客户端登录并读写，如果为null，将关闭对客户端的ip验证<br />
  /// Set and start the trusted client login and read and write, if it is null, the client's IP verification will be turned off
  /// </summary>
  /// <param name="clients">受信任的客户端列表</param>
  public void SetTrustedIpAddress(List<string> clients)
  {
    lock (this.lock_trusted_clients)
    {
      if (clients != null && clients.Count > 0)
      {
        this.TrustedClients = clients.Select<string, string>((Func<string, string>) (m => HslHelper.GetIpAddressFromInput(m))).ToList<string>();
        this.IsTrustedClientsOnly = true;
      }
      else
      {
        this.TrustedClients = new List<string>();
        this.IsTrustedClientsOnly = false;
      }
    }
  }

  /// <summary>
  /// 检查该Ip地址是否是受信任的<br />
  /// Check if the IP address is trusted
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <returns>是受信任的返回<c>True</c>，否则返回<c>False</c></returns>
  private bool CheckIpAddressTrusted(string ipAddress)
  {
    if (!this.IsTrustedClientsOnly)
      return false;
    bool flag = false;
    lock (this.lock_trusted_clients)
    {
      for (int index = 0; index < this.TrustedClients.Count; ++index)
      {
        if (this.TrustedClients[index] == ipAddress)
        {
          flag = true;
          break;
        }
      }
    }
    return flag;
  }

  /// <summary>
  /// 获取受信任的客户端列表<br />
  /// Get a list of trusted clients
  /// </summary>
  /// <returns>字符串数据信息</returns>
  public string[] GetTrustedClients()
  {
    string[] trustedClients = new string[0];
    lock (this.lock_trusted_clients)
    {
      if (this.TrustedClients != null)
        trustedClients = this.TrustedClients.ToArray();
    }
    return trustedClients;
  }

  private void AsyncAcceptCallback(IAsyncResult iar)
  {
    if (!(iar.AsyncState is Socket asyncState))
      return;
    Socket socket = (Socket) null;
    try
    {
      socket = asyncState.EndAccept(iar);
      if (this.SocketKeepAliveTime > 0)
        socket.SetKeepAlive(this.SocketKeepAliveTime, this.SocketKeepAliveTime);
      ThreadPool.QueueUserWorkItem(new WaitCallback(this.ThreadPoolLogin), (object) socket);
    }
    catch (ObjectDisposedException ex)
    {
      return;
    }
    catch (SocketException ex)
    {
      if (!this.IsStarted && ex.SocketErrorCode == SocketError.OperationAborted)
        return;
      NetSupport.CloseSocket(socket);
      this.LogDebugMsg($"{StringResources.Language.SocketAcceptCallbackException} {ex.Message}");
    }
    catch (Exception ex)
    {
      NetSupport.CloseSocket(socket);
      this.LogDebugMsg($"{StringResources.Language.SocketAcceptCallbackException} {ex.Message}");
    }
    int num = 0;
    while (num < 3)
    {
      if (!this.IsStarted)
      {
        NetSupport.CloseSocket(socket);
        return;
      }
      try
      {
        asyncState.BeginAccept(this.beginAcceptCallback, (object) asyncState);
        break;
      }
      catch (ObjectDisposedException ex)
      {
        if (!this.IsStarted)
          return;
      }
      catch (Exception ex)
      {
        HslHelper.ThreadSleep(100);
        this.LogDebugMsg($"{StringResources.Language.SocketReAcceptCallbackException} {ex.Message}");
        ++num;
      }
    }
    if (num >= 3)
    {
      this.LogDebugMsg(StringResources.Language.SocketReAcceptCallbackException);
      throw new Exception(StringResources.Language.SocketReAcceptCallbackException);
    }
  }

  private void ThreadPoolLogin(object obj)
  {
    if (!(obj is Socket socket))
      return;
    IPEndPoint remoteEndPoint = (IPEndPoint) socket.RemoteEndPoint;
    if (this.IsTrustedClientsOnly && !this.CheckIpAddressTrusted(remoteEndPoint == null ? string.Empty : remoteEndPoint.Address.ToString()))
    {
      this.LogDebugMsg(string.Format(StringResources.Language.ClientDisableLogin, (object) remoteEndPoint));
      NetSupport.CloseSocket(socket);
    }
    else
    {
      OperateResult operateResult = this.SocketAcceptExtraCheck(socket, remoteEndPoint);
      if (!operateResult.IsSuccess)
      {
        this.LogDebugMsg($"Client <{remoteEndPoint}> SocketAcceptExtraCheck failed: {operateResult.Message}");
        NetSupport.CloseSocket(socket);
      }
      else
      {
        PipeTcpNet pipe;
        if (this.useSSL)
        {
          PipeSslNet pipeSslNet = new PipeSslNet(socket, remoteEndPoint, true);
          pipeSslNet.Certificate = this.certificate;
          Action<PipeSslNet> pipeSslNetAction = this.pipeSslNetAction;
          if (pipeSslNetAction != null)
            pipeSslNetAction(pipeSslNet);
          OperateResult<SslStream> sslStream = pipeSslNet.CreateSslStream(socket, true);
          if (!sslStream.IsSuccess)
          {
            pipeSslNet.CloseCommunication();
            this.LogNet?.WriteDebug(this.ToString(), $"[{remoteEndPoint}] WebScoket SSL Check Failed:" + sslStream.Message);
            return;
          }
          pipe = (PipeTcpNet) pipeSslNet;
        }
        else
          pipe = new PipeTcpNet(socket, remoteEndPoint);
        this.ThreadPoolLogin(pipe, remoteEndPoint);
      }
    }
  }

  /// <summary>
  /// 当客户端连接到服务器，并听过额外的检查后，进行回调的方法<br />
  /// Callback method when the client connects to the server and has heard additional checks
  /// </summary>
  /// <param name="pipe">socket对象</param>
  /// <param name="endPoint">远程的终结点</param>
  protected virtual void ThreadPoolLogin(PipeTcpNet pipe, IPEndPoint endPoint)
  {
  }

  /// <summary>关闭的时候额外执行的功能代码</summary>
  protected virtual void ExtraOnClose()
  {
  }

  /// <summary>服务器启动的时候额外执行的功能代码</summary>
  protected virtual void ExtraOnStart()
  {
  }

  /// <summary>
  /// 当客户端的socket登录的时候额外检查的操作，并返回操作的结果信息。<br />
  /// The operation is additionally checked when the client's socket logs in, and the result information of the operation is returned.
  /// </summary>
  /// <param name="socket">套接字</param>
  /// <param name="endPoint">终结点</param>
  /// <returns>验证的结果</returns>
  protected virtual OperateResult SocketAcceptExtraCheck(Socket socket, IPEndPoint endPoint)
  {
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>记录当前的日志信息</summary>
  /// <param name="message">消息文本</param>
  protected void LogDebugMsg(string message)
  {
    this.LogNet?.WriteDebug(this.ToString(), message);
    Action<string> logDebugMessage = this.LogDebugMessage;
    if (logDebugMessage == null)
      return;
    logDebugMessage(message);
  }
}
