﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttSessionInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>用于客户端获取服务器会话状态监视数据的类</summary>
public class MqttSessionInfo
{
  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.EndPoint" />
  public string EndPoint { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.ClientId" />
  public string ClientId { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.ActiveTime" />
  public DateTime ActiveTime { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.OnlineTime" />
  public DateTime OnlineTime { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.Topics" />
  public string[] Topics { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.UserName" />
  public string UserName { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.Protocol" />
  public string Protocol { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.WillTopic" />
  public string WillTopic { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.DeveloperPermissions" />
  public bool DeveloperPermissions { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.IsAesCryptography" />
  public bool IsAesCryptography { get; set; }

  /// <inheritdoc cref="P:HslCommunication.MQTT.MqttSession.ForbidPublishTopic" />
  public bool ForbidPublishTopic { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    StringBuilder stringBuilder = new StringBuilder($"{this.Protocol} Session[IP:{this.EndPoint}]");
    if (!string.IsNullOrEmpty(this.ClientId))
      stringBuilder.Append($" [ID:{this.ClientId}]");
    if (!string.IsNullOrEmpty(this.UserName))
      stringBuilder.Append($" [Name:{this.UserName}]");
    if (this.IsAesCryptography)
      stringBuilder.Append("[RSA/AES]");
    return stringBuilder.ToString();
  }
}
