﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslRedisListAttribute
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>对应redis的一个列表信息的内容</summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class HslRedisListAttribute : Attribute
{
  /// <summary>列表键值的名称</summary>
  public string ListKey { get; set; }

  /// <summary>当前的位置的索引</summary>
  public long StartIndex { get; set; }

  /// <summary>当前位置的结束索引</summary>
  public long EndIndex { get; set; } = -1;

  /// <summary>根据键名来读取写入当前的列表中的多个信息</summary>
  /// <param name="listKey">列表键名</param>
  public HslRedisListAttribute(string listKey) => this.ListKey = listKey;

  /// <summary>根据键名来读取写入当前的列表中的多个信息</summary>
  /// <param name="listKey">列表键名</param>
  /// <param name="startIndex">开始的索引信息</param>
  public HslRedisListAttribute(string listKey, long startIndex)
  {
    this.ListKey = listKey;
    this.StartIndex = startIndex;
  }

  /// <summary>根据键名来读取写入当前的列表中的多个信息</summary>
  /// <param name="listKey">列表键名</param>
  /// <param name="startIndex">开始的索引信息</param>
  /// <param name="endIndex">结束的索引位置，-1为倒数第一个，以此类推。</param>
  public HslRedisListAttribute(string listKey, long startIndex, long endIndex)
  {
    this.ListKey = listKey;
    this.StartIndex = startIndex;
    this.EndIndex = endIndex;
  }
}
