﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Sick.SickIcrTcpServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Sick;

/// <summary>
/// Sick的扫码器的服务器信息，只要启动服务器之后，扫码器配置将条码发送到PC的指定端口上来即可，就可以持续的接收条码信息，同样也适用于海康，基恩士，DATELOGIC 。<br />
/// The server information of <PERSON>'s code scanner, as long as the server is started, the code scanner is configured to send the barcode to the designated port of the PC, and it can continuously receive the barcode information.
/// </summary>
public class SickIcrTcpServer : CommunicationServer
{
  /// <summary>
  /// 实例化一个默认的服务器对象<br />
  /// Instantiate a default server object
  /// </summary>
  public SickIcrTcpServer()
  {
    this.OnPipeMessageReceived += new CommunicationServer.PipeMessageReceived(this.SickIcrTcpServer_OnPipeMessageReceived);
  }

  private void SickIcrTcpServer_OnPipeMessageReceived(PipeSession session, byte[] buffer)
  {
    string ipAddress = string.Empty;
    if (session.Communication is PipeTcpNet communication)
      ipAddress = communication.IpAddress;
    if (session != null)
      this.LogNet?.WriteDebug(this.ToString(), $"<{session.Communication}> Recv: " + buffer.ToHexString(' '));
    SickIcrTcpServer.ReceivedBarCodeDelegate onReceivedBarCode = this.OnReceivedBarCode;
    if (onReceivedBarCode == null)
      return;
    onReceivedBarCode(ipAddress, this.TranslateCode(Encoding.ASCII.GetString(buffer)));
  }

  /// <summary>
  /// 当接收到条码数据的时候触发<br />
  /// Triggered when barcode data is received
  /// </summary>
  public event SickIcrTcpServer.ReceivedBarCodeDelegate OnReceivedBarCode;

  private string TranslateCode(string code)
  {
    StringBuilder stringBuilder = new StringBuilder("");
    for (int index = 0; index < code.Length; ++index)
    {
      if (char.IsLetterOrDigit(code, index))
        stringBuilder.Append(code[index]);
    }
    return stringBuilder.ToString();
  }

  /// <inheritdoc />
  public override string ToString() => $"SickIcrTcpServer[{this.Port}]";

  /// <summary>
  /// 接收条码数据的委托信息<br />
  /// Entrusted information to receive barcode data
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="barCode">条码信息</param>
  public delegate void ReceivedBarCodeDelegate(string ipAddress, string barCode);
}
