﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT645With1997OverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Instrument.DLT.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>
/// 基于多功能电能表通信协议实现的通讯类，参考的文档是DLT645-1997，主要实现了对电表数据的读取和一些功能方法，数据标识格式为 B6-11，具体参照文档手册。<br />
/// Based on the communication class implemented by the multi-function energy meter communication protocol, the reference document is DLT645-1997,
/// which mainly implements the reading of meter data and some functional methods, the data identification format is B6-11, please refer to the document manual for details.
/// </summary>
/// <remarks>
/// 如果一对多的模式，地址可以携带地址域访问，例如 "s=2;B6-11"，主要使用 <see cref="M:HslCommunication.Instrument.DLT.DLT645With1997OverTcp.ReadDouble(System.String,System.UInt16)" /> 方法来读取浮点数，<see cref="M:HslCommunication.Core.Device.DeviceCommunication.ReadString(System.String,System.UInt16)" /> 方法来读取字符串
/// </remarks>
/// <example>
/// <inheritdoc cref="T:HslCommunication.Instrument.DLT.DLT645With1997" path="example" />
/// </example>
public class DLT645With1997OverTcp : DeviceTcpNet, IDlt645, IReadWriteDevice, IReadWriteNet
{
  private string station = "1";

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.#ctor" />
  public DLT645With1997OverTcp()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>
  /// 指定IP地址，端口，地址域来实例化一个对象<br />
  /// Specify the IP address, port, address field, password, and operator code to instantiate an object
  /// </summary>
  /// <param name="ipAddress">TcpServer的IP地址</param>
  /// <param name="port">TcpServer的端口</param>
  /// <param name="station">设备的站号信息</param>
  public DLT645With1997OverTcp(string ipAddress, int port = 502, string station = "1")
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
    this.station = station;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new DLT645Message(this.CheckDataId);
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    if (!this.EnableCodeFE)
      return base.PackCommandWithHeader(command);
    return SoftBasic.SpliceArray<byte>(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, command);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ActiveDeveice" />
  public OperateResult ActiveDeveice()
  {
    return (OperateResult) this.ReadFromCoreServer(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, false, true);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return DLT645Helper.Read((IDlt645) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ReadDouble(System.String,System.UInt16)" />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return DLT645Helper.ReadDouble((IDlt645) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ReadString(System.String,System.UInt16,System.Text.Encoding)" />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromArray<string>(this.ReadStringArray(address));
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ReadStringArray(System.String)" />
  public OperateResult<string[]> ReadStringArray(string address)
  {
    return DLT645Helper.ReadStringArray((IDlt645) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ActiveDeveice" />
  public async Task<OperateResult> ActiveDeveiceAsync()
  {
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, false, false);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await DLT645Helper.ReadAsync((IDlt645) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997OverTcp.ReadDouble(System.String,System.UInt16)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<double[]> operateResult = await DLT645Helper.ReadDoubleAsync((IDlt645) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997OverTcp.ReadString(System.String,System.UInt16,System.Text.Encoding)" />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<string[]> result = await this.ReadStringArrayAsync(address);
    return ByteTransformHelper.GetResultFromArray<string>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ReadStringArray(System.String)" />
  public async Task<OperateResult<string[]>> ReadStringArrayAsync(string address)
  {
    OperateResult<string[]> operateResult = await DLT645Helper.ReadStringArrayAsync((IDlt645) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.Write(System.String,System.Byte[])" />
  public override OperateResult Write(string address, byte[] value)
  {
    return DLT645Helper.Write((IDlt645) this, "", "", address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    return DLT645Helper.Write((IDlt645) this, "", "", address, ((IEnumerable<double>) values).Select<double, string>((Func<double, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.WriteAddress(System.String)" />
  public OperateResult WriteAddress(string address)
  {
    return DLT645Helper.WriteAddress((IDlt645) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.BroadcastTime(System.DateTime)" />
  public OperateResult BroadcastTime(DateTime dateTime)
  {
    return DLT645Helper.BroadcastTime((IDlt645) this, dateTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ChangeBaudRate(System.String)" />
  public OperateResult ChangeBaudRate(string baudRate)
  {
    return DLT645Helper.ChangeBaudRate((IDlt645) this, baudRate);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ReadAddress" />
  public OperateResult<string> ReadAddress()
  {
    return new OperateResult<string>(StringResources.Language.NotSupportedFunction);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997OverTcp.Trip(System.String,System.DateTime)" />
  public OperateResult Trip(DateTime validTime) => this.Trip(this.Station, validTime);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.Trip(System.String,System.DateTime)" />
  public OperateResult Trip(string station, DateTime validTime)
  {
    return DLT645Helper.Function1C((IDlt645) this, "", "", station, (byte) 26, validTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997OverTcp.SwitchingOn(System.String,System.DateTime)" />
  public OperateResult SwitchingOn(DateTime validTime) => this.SwitchingOn(this.Station, validTime);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.SwitchingOn(System.String,System.DateTime)" />
  public OperateResult SwitchingOn(string station, DateTime validTime)
  {
    return DLT645Helper.Function1C((IDlt645) this, "", "", station, (byte) 27, validTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override Task<OperateResult> WriteAsync(string address, double[] values)
  {
    return DLT645Helper.WriteAsync((IDlt645) this, "", "", address, ((IEnumerable<double>) values).Select<double, string>((Func<double, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, "", "", address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.WriteAddress(System.String)" />
  public async Task<OperateResult> WriteAddressAsync(string address)
  {
    OperateResult operateResult = await DLT645Helper.WriteAddressAsync((IDlt645) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.BroadcastTime(System.DateTime)" />
  public async Task<OperateResult> BroadcastTimeAsync(DateTime dateTime)
  {
    OperateResult operateResult = await DLT645Helper.BroadcastTimeAsync((IDlt645) this, dateTime, new Func<byte[], bool, bool, Task<OperateResult<byte[]>>>(((BinaryCommunication) this).ReadFromCoreServerAsync));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ChangeBaudRate(System.String)" />
  public async Task<OperateResult> ChangeBaudRateAsync(string baudRate)
  {
    OperateResult operateResult = await DLT645Helper.ChangeBaudRateAsync((IDlt645) this, baudRate);
    return operateResult;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.DLT645.Station" />
  public string Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.DLT645.EnableCodeFE" />
  public bool EnableCodeFE { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.DLTType" />
  public DLT645Type DLTType { get; } = DLT645Type.DLT1997;

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.Password" />
  public string Password { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.OpCode" />
  public string OpCode { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.CheckDataId" />
  /// "/&gt;
  public bool CheckDataId { get; set; } = true;

  /// <inheritdoc />
  public override string ToString() => $"DLT645With1997OverTcp[{this.IpAddress}:{this.Port}]";
}
