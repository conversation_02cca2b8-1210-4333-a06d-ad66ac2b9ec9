﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.McHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>MC协议的辅助类对象，提供了MC协议的读写操作的基本支持</summary>
public class McHelper
{
  /// <summary>返回按照字单位读取的最低的长度信息</summary>
  /// <param name="type">MC协议的类型</param>
  /// <returns>长度信息</returns>
  public static int GetReadWordLength(McType type)
  {
    return type == McType.McBinary || type == McType.McRBinary ? 950 : 460;
  }

  /// <summary>返回按照位单位读取的最低的长度信息</summary>
  /// <param name="type">MC协议的类型</param>
  /// <returns>长度信息</returns>
  public static int GetReadBoolLength(McType type)
  {
    return type == McType.McBinary || type == McType.McRBinary ? 7168 : 3584 /*0x0E00*/;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read(System.String,System.UInt16)" />
  /// <remarks>
  /// 初步支持普通的数据地址之外，还额外支持高级的地址写法，以下是示例（适用于MC协议为二进制和ASCII格式）：<br />
  /// [商业授权] 扩展的数据地址: 表示为 ext=1;W100  访问扩展区域为1的W100的地址信息<br />
  /// [商业授权] 缓冲存储器地址: 表示为 mem=32  访问地址为32的本站缓冲存储器地址<br />
  /// [商业授权] 智能模块地址：表示为 module=3;4106  访问模块号3，偏移地址是4106的数据，偏移地址需要根据模块的详细信息来确认。<br />
  /// [商业授权] 基于标签的地址: 表示位 s=AAA  假如标签的名称为AAA，但是标签的读取是有条件的，详细参照<see cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" /><br />
  /// </remarks>
  public static OperateResult<byte[]> Read(IReadWriteMc mc, string address, ushort length)
  {
    if (address.StartsWith("s=") || address.StartsWith("S="))
    {
      if (mc.McType == McType.McBinary)
        return McBinaryHelper.ReadTags(mc, new string[1]
        {
          address.Substring(2)
        }, new ushort[1]{ length });
      if (mc.McType != McType.MCAscii)
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      return McAsciiHelper.ReadTags(mc, new string[1]
      {
        address.Substring(2)
      }, new ushort[1]{ length });
    }
    if ((mc.McType == McType.McBinary || mc.McType == McType.MCAscii) && Regex.IsMatch(address, "ext=[0-9]+;", RegexOptions.IgnoreCase))
    {
      string input = Regex.Match(address, "ext=[0-9]+;").Value;
      ushort extend = ushort.Parse(Regex.Match(input, "[0-9]+").Value);
      return McHelper.ReadExtend(mc, extend, address.Substring(input.Length), length);
    }
    if ((mc.McType == McType.McBinary || mc.McType == McType.MCAscii) && Regex.IsMatch(address, "mem=", RegexOptions.IgnoreCase))
      return McHelper.ReadMemory(mc, address.Substring(4), length);
    if ((mc.McType == McType.McBinary || mc.McType == McType.MCAscii) && Regex.IsMatch(address, "module=[0-9]+;", RegexOptions.IgnoreCase))
    {
      string input = Regex.Match(address, "module=[0-9]+;").Value;
      ushort module = ushort.Parse(Regex.Match(input, "[0-9]+").Value);
      return McHelper.ReadSmartModule(mc, module, address.Substring(input.Length), (ushort) ((uint) length * 2U));
    }
    OperateResult<McAddressData> result = mc.McAnalysisAddress(address, length, false);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    List<byte> byteList = new List<byte>();
    ushort num1 = 0;
    while ((int) num1 < (int) length)
    {
      ushort num2 = (ushort) Math.Min((int) length - (int) num1, McHelper.GetReadWordLength(mc.McType));
      result.Content.Length = num2;
      byte[] send = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMcCoreCommand(result.Content, false) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMcCoreCommand(result.Content, false) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildReadMcCoreCommand(result.Content, false) : (byte[]) null));
      OperateResult<byte[]> operateResult = mc.ReadFromCoreServer(send);
      if (!operateResult.IsSuccess)
        return operateResult;
      byteList.AddRange((IEnumerable<byte>) mc.ExtractActualData(operateResult.Content, false));
      num1 += num2;
      if (result.Content.McDataType.DataType == (byte) 0)
        result.Content.AddressStart += (int) num2;
      else
        result.Content.AddressStart += (int) num2 * 16 /*0x10*/;
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Byte[])" />
  public static OperateResult Write(IReadWriteMc mc, string address, byte[] value)
  {
    if (address.StartsWith("s=") || address.StartsWith("S="))
    {
      if (mc.McType == McType.McBinary)
        return McBinaryHelper.WriteTag(mc, address.Substring(2), value);
      if (mc.McType == McType.MCAscii)
        return McAsciiHelper.WriteTag(mc, address.Substring(2), value);
    }
    OperateResult<McAddressData> result = mc.McAnalysisAddress(address, (ushort) 0, false);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    byte[] send = mc.McType == McType.McBinary ? McBinaryHelper.BuildWriteWordCoreCommand(result.Content, value) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiWriteWordCoreCommand(result.Content, value) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildWriteWordCoreCommand(result.Content, value) : (byte[]) null));
    OperateResult<byte[]> operateResult = mc.ReadFromCoreServer(send);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String)" />
  /// <remarks>
  /// 对于X,Y类型的地址，有两种表示方式，十六进制和八进制，默认16进制，比如输入 X10 是16进制的，如果想要输入8进制的地址，地址补0操作，例如 X010
  /// </remarks>
  public static OperateResult<bool> ReadBool(IReadWriteMc mc, string address)
  {
    return ByteTransformHelper.GetResultFromArray<bool>(McHelper.ReadBool(mc, address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  /// <remarks>
  /// 当读取的长度过大时，会自动进行切割，对于二进制格式，切割长度为7168，对于ASCII格式协议来说，切割长度则是3584<br />
  /// 对于X,Y类型的地址，有两种表示方式，十六进制和八进制，默认16进制，比如输入 X10 是16进制的，如果想要输入8进制的地址，地址补0操作，例如 X010
  /// </remarks>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteMc mc,
    string address,
    ushort length,
    bool supportWordAdd = true)
  {
    if (mc.McType == McType.McBinary && address.StartsWith("s=") || address.StartsWith("S="))
      return McBinaryHelper.ReadBoolTag(mc, address.Substring(2), length);
    if (supportWordAdd && address.IndexOf('.') > 0)
      return HslHelper.ReadBool((IReadWriteNet) mc, address, length);
    OperateResult<McAddressData> result1 = mc.McAnalysisAddress(address, length, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    ushort num = 0;
    while ((int) num < (int) length)
    {
      ushort count = (ushort) Math.Min((int) length - (int) num, McHelper.GetReadBoolLength(mc.McType));
      result1.Content.Length = count;
      byte[] send = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMcCoreCommand(result1.Content, true) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMcCoreCommand(result1.Content, true) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildReadMcCoreCommand(result1.Content, true) : (byte[]) null));
      OperateResult<byte[]> result2 = mc.ReadFromCoreServer(send);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      boolList.AddRange((IEnumerable<bool>) ((IEnumerable<byte>) mc.ExtractActualData(result2.Content, true)).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 1)).Take<bool>((int) count).ToArray<bool>());
      num += count;
      result1.Content.AddressStart += (int) count;
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  /// <remarks>
  /// 当读取的长度过大时，会自动进行切割，对于二进制格式，切割长度为7168，对于ASCII格式协议来说，切割长度则是3584<br />
  /// 对于X,Y类型的地址，有两种表示方式，十六进制和八进制，默认16进制，比如输入 X10 是16进制的，如果想要输入8进制的地址，地址补0操作，例如 X010
  /// </remarks>
  public static OperateResult Write(
    IReadWriteMc mc,
    string address,
    bool[] values,
    bool supportWordAdd = true)
  {
    if (supportWordAdd && mc.EnableWriteBitToWordRegister && address.Contains("."))
      return ReadWriteNetHelper.WriteBoolWithWord((IReadWriteNet) mc, address, values);
    OperateResult<McAddressData> operateResult1 = mc.McAnalysisAddress(address, (ushort) 0, true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    byte[] send = mc.McType == McType.McBinary ? McBinaryHelper.BuildWriteBitCoreCommand(operateResult1.Content, values) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiWriteBitCoreCommand(operateResult1.Content, values) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildWriteBitCoreCommand(operateResult1.Content, values) : (byte[]) null));
    OperateResult<byte[]> operateResult2 = mc.ReadFromCoreServer(send);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.Read(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteMc mc,
    string address,
    ushort length)
  {
    if (mc.McType == McType.McBinary && address.StartsWith("s=") || address.StartsWith("S="))
    {
      OperateResult<byte[]> operateResult = await McBinaryHelper.ReadTagsAsync(mc, new string[1]
      {
        address.Substring(2)
      }, new ushort[1]{ length });
      return operateResult;
    }
    if ((mc.McType == McType.McBinary || mc.McType == McType.MCAscii) && Regex.IsMatch(address, "ext=[0-9]+;", RegexOptions.IgnoreCase))
    {
      string extStr = Regex.Match(address, "ext=[0-9]+;").Value;
      ushort ext = ushort.Parse(Regex.Match(extStr, "[0-9]+").Value);
      OperateResult<byte[]> operateResult = await McHelper.ReadExtendAsync(mc, ext, address.Substring(extStr.Length), length);
      return operateResult;
    }
    if ((mc.McType == McType.McBinary || mc.McType == McType.MCAscii) && Regex.IsMatch(address, "mem=", RegexOptions.IgnoreCase))
    {
      OperateResult<byte[]> operateResult = await McHelper.ReadMemoryAsync(mc, address.Substring(4), length);
      return operateResult;
    }
    if ((mc.McType == McType.McBinary || mc.McType == McType.MCAscii) && Regex.IsMatch(address, "module=[0-9]+;", RegexOptions.IgnoreCase))
    {
      string moduleStr = Regex.Match(address, "module=[0-9]+;").Value;
      ushort module = ushort.Parse(Regex.Match(moduleStr, "[0-9]+").Value);
      OperateResult<byte[]> operateResult = await McHelper.ReadSmartModuleAsync(mc, module, address.Substring(moduleStr.Length), (ushort) ((uint) length * 2U));
      return operateResult;
    }
    OperateResult<McAddressData> addressResult = mc.McAnalysisAddress(address, length, false);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
    List<byte> bytesContent = new List<byte>();
    ushort alreadyFinished = 0;
    while ((int) alreadyFinished < (int) length)
    {
      ushort readLength = (ushort) Math.Min((int) length - (int) alreadyFinished, McHelper.GetReadWordLength(mc.McType));
      addressResult.Content.Length = readLength;
      byte[] command = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMcCoreCommand(addressResult.Content, false) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMcCoreCommand(addressResult.Content, false) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildReadMcCoreCommand(addressResult.Content, false) : (byte[]) null));
      OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(command);
      if (!read.IsSuccess)
        return read;
      bytesContent.AddRange((IEnumerable<byte>) mc.ExtractActualData(read.Content, false));
      alreadyFinished += readLength;
      if (addressResult.Content.McDataType.DataType == (byte) 0)
        addressResult.Content.AddressStart += (int) readLength;
      else
        addressResult.Content.AddressStart += (int) readLength * 16 /*0x10*/;
      command = (byte[]) null;
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(bytesContent.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(IReadWriteMc mc, string address, byte[] value)
  {
    if (mc.McType == McType.McBinary && address.StartsWith("s=") || address.StartsWith("S="))
    {
      OperateResult operateResult = await McBinaryHelper.WriteTagAsync(mc, address.Substring(2), value);
      return operateResult;
    }
    OperateResult<McAddressData> addressResult = mc.McAnalysisAddress(address, (ushort) 0, false);
    if (!addressResult.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
    byte[] coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildWriteWordCoreCommand(addressResult.Content, value) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiWriteWordCoreCommand(addressResult.Content, value) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildWriteWordCoreCommand(addressResult.Content, value) : (byte[]) null));
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadBool(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String,System.UInt16,System.Boolean)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteMc mc,
    string address,
    ushort length,
    bool supportWordAdd = true)
  {
    if (mc.McType == McType.McBinary && address.StartsWith("s=") || address.StartsWith("S="))
    {
      OperateResult<bool[]> operateResult = await McBinaryHelper.ReadBoolTagAsync(mc, address.Substring(2), length);
      return operateResult;
    }
    if (supportWordAdd && address.IndexOf('.') > 0)
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) mc, address, length);
      return operateResult;
    }
    OperateResult<McAddressData> addressResult = mc.McAnalysisAddress(address, length, true);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) addressResult);
    List<bool> boolContent = new List<bool>();
    ushort alreadyFinished = 0;
    while ((int) alreadyFinished < (int) length)
    {
      ushort readLength = (ushort) Math.Min((int) length - (int) alreadyFinished, McHelper.GetReadBoolLength(mc.McType));
      addressResult.Content.Length = readLength;
      byte[] coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMcCoreCommand(addressResult.Content, true) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMcCoreCommand(addressResult.Content, true) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildReadMcCoreCommand(addressResult.Content, true) : (byte[]) null));
      OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      boolContent.AddRange((IEnumerable<bool>) ((IEnumerable<byte>) mc.ExtractActualData(read.Content, true)).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 1)).Take<bool>((int) readLength).ToArray<bool>());
      alreadyFinished += readLength;
      addressResult.Content.AddressStart += (int) readLength;
      coreResult = (byte[]) null;
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolContent.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteMc mc,
    string address,
    bool[] values,
    bool supportWordAdd = true)
  {
    if (supportWordAdd && mc.EnableWriteBitToWordRegister && address.Contains("."))
    {
      OperateResult operateResult = await ReadWriteNetHelper.WriteBoolWithWordAsync((IReadWriteNet) mc, address, values).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<McAddressData> addressResult = mc.McAnalysisAddress(address, (ushort) 0, true);
    if (!addressResult.IsSuccess)
      return (OperateResult) addressResult;
    byte[] coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildWriteBitCoreCommand(addressResult.Content, values) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiWriteBitCoreCommand(addressResult.Content, values) : (mc.McType == McType.McRBinary ? MelsecMcRNet.BuildWriteBitCoreCommand(addressResult.Content, values) : (byte[]) null));
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) read;
  }

  /// <summary>
  /// 随机读取PLC的数据信息，可以跨地址，跨类型组合，但是每个地址只能读取一个word，也就是2个字节的内容。收到结果后，需要自行解析数据<br />
  /// Randomly read PLC data information, which can be combined across addresses and types, but each address can only read one word,
  /// which is the content of 2 bytes. After receiving the results, you need to parse the data yourself
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <param name="address">所有的地址的集合</param>
  /// <remarks>
  /// 访问安装有 Q 系列 C24/E71 的站 QCPU 上位站 经由 Q 系列兼容网络系统 MELSECNET/H MELSECNET/10 Ethernet 的 QCPU 其他站 时
  /// 访问点数········1≦ 字访问点数 双字访问点数 ≦192
  /// <br />
  /// 访问 QnACPU 其他站 经由 QnA 系列兼容网络系统 MELSECNET/10 Ethernet 的 Q/QnACPU 其他站 时访问点数········1≦ 字访问点数 双字访问点数 ≦96
  /// <br />
  /// 访问上述以外的 PLC CPU 其他站 时访问点数········1≦字访问点数≦10
  /// </remarks>
  /// <example>
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\melsecTest.cs" region="ReadExample3" title="随机字读取示例" /></example>
  /// <returns>结果</returns>
  public static OperateResult<byte[]> ReadRandom(IReadWriteMc mc, string[] address)
  {
    McAddressData[] address1 = new McAddressData[address.Length];
    for (int index = 0; index < address.Length; ++index)
    {
      OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address[index], (ushort) 1, false);
      if (!melsecFrom.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) melsecFrom);
      address1[index] = melsecFrom.Content;
    }
    byte[] send = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadRandomWordCommand(address1) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadRandomWordCommand(address1) : (byte[]) null);
    OperateResult<byte[]> result = mc.ReadFromCoreServer(send);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(result.Content, false));
  }

  /// <summary>
  /// 使用块读取PLC的数据信息，可以跨地址，跨类型组合，每个地址是任意的长度。收到结果后，需要自行解析数据，目前只支持字地址，比如D区，W区，R区，不支持X，Y，M，B，L等等<br />
  /// Read the data information of the PLC randomly. It can be combined across addresses and types. Each address is of any length. After receiving the results,
  /// you need to parse the data yourself. Currently, only word addresses are supported, such as D area, W area, R area. X, Y, M, B, L, etc
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <param name="address">所有的地址的集合</param>
  /// <param name="length">每个地址的长度信息</param>
  /// <remarks>
  /// 实际测试不一定所有的plc都可以读取成功，具体情况需要具体分析
  /// <br />
  /// 1 块数按照下列要求指定 120 ≧ 字软元件块数 + 位软元件块数
  /// <br />
  /// 2 各软元件点数按照下列要求指定 960 ≧ 字软元件各块的合计点数 + 位软元件各块的合计点数
  /// </remarks>
  /// <example>
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\melsecTest.cs" region="ReadExample4" title="随机批量字读取示例" />
  /// </example>
  /// <returns>结果</returns>
  public static OperateResult<byte[]> ReadRandom(
    IReadWriteMc mc,
    string[] address,
    ushort[] length)
  {
    if (length.Length != address.Length)
      return new OperateResult<byte[]>(StringResources.Language.TwoParametersLengthIsNotSame);
    McAddressData[] address1 = new McAddressData[address.Length];
    for (int index = 0; index < address.Length; ++index)
    {
      OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address[index], length[index], false);
      if (!melsecFrom.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) melsecFrom);
      address1[index] = melsecFrom.Content;
    }
    byte[] send = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadRandomCommand(address1) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadRandomCommand(address1) : (byte[]) null);
    OperateResult<byte[]> result = mc.ReadFromCoreServer(send);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(result.Content, false));
  }

  /// <summary>
  /// 随机读取PLC的数据信息，可以跨地址，跨类型组合，但是每个地址只能读取一个word，也就是2个字节的内容。收到结果后，自动转换为了short类型的数组<br />
  /// Randomly read PLC data information, which can be combined across addresses and types, but each address can only read one word,
  /// which is the content of 2 bytes. After receiving the result, it is automatically converted to an array of type short.
  /// </summary>
  /// <param name="mc">MC协议的通信对象</param>
  /// <param name="address">所有的地址的集合</param>
  /// <remarks>
  /// 访问安装有 Q 系列 C24/E71 的站 QCPU 上位站 经由 Q 系列兼容网络系统 MELSECNET/H MELSECNET/10 Ethernet 的 QCPU 其他站 时
  /// 访问点数········1≦ 字访问点数 双字访问点数 ≦192
  /// 
  /// 访问 QnACPU 其他站 经由 QnA 系列兼容网络系统 MELSECNET/10 Ethernet 的 Q/QnACPU 其他站 时访问点数········1≦ 字访问点数 双字访问点数 ≦96
  /// 
  /// 访问上述以外的 PLC CPU 其他站 时访问点数········1≦字访问点数≦10
  /// </remarks>
  /// <returns>包含是否成功的结果对象</returns>
  public static OperateResult<short[]> ReadRandomInt16(IReadWriteMc mc, string[] address)
  {
    OperateResult<byte[]> result = McHelper.ReadRandom(mc, address);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<short[]>((OperateResult) result) : OperateResult.CreateSuccessResult<short[]>(mc.ByteTransform.TransInt16(result.Content, 0, address.Length));
  }

  /// <summary>
  /// 随机读取PLC的数据信息，可以跨地址，跨类型组合，但是每个地址只能读取一个word，也就是2个字节的内容。收到结果后，自动转换为了ushort类型的数组<br />
  /// Randomly read PLC data information, which can be combined across addresses and types, but each address can only read one word,
  /// which is the content of 2 bytes. After receiving the result, it is automatically converted to an array of type ushort.
  /// </summary>
  /// <param name="mc">MC协议的通信对象</param>
  /// <param name="address">所有的地址的集合</param>
  /// <remarks>
  /// 访问安装有 Q 系列 C24/E71 的站 QCPU 上位站 经由 Q 系列兼容网络系统 MELSECNET/H MELSECNET/10 Ethernet 的 QCPU 其他站 时
  /// 访问点数········1≦ 字访问点数 双字访问点数 ≦192
  /// 
  /// 访问 QnACPU 其他站 经由 QnA 系列兼容网络系统 MELSECNET/10 Ethernet 的 Q/QnACPU 其他站 时访问点数········1≦ 字访问点数 双字访问点数 ≦96
  /// 
  /// 访问上述以外的 PLC CPU 其他站 时访问点数········1≦字访问点数≦10
  /// </remarks>
  /// <returns>包含是否成功的结果对象</returns>
  public static OperateResult<ushort[]> ReadRandomUInt16(IReadWriteMc mc, string[] address)
  {
    OperateResult<byte[]> result = McHelper.ReadRandom(mc, address);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<ushort[]>((OperateResult) result) : OperateResult.CreateSuccessResult<ushort[]>(mc.ByteTransform.TransUInt16(result.Content, 0, address.Length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandom(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[])" />
  public static async Task<OperateResult<byte[]>> ReadRandomAsync(IReadWriteMc mc, string[] address)
  {
    McAddressData[] mcAddressDatas = new McAddressData[address.Length];
    for (int i = 0; i < address.Length; ++i)
    {
      OperateResult<McAddressData> addressResult = McAddressData.ParseMelsecFrom(address[i], (ushort) 1, false);
      if (!addressResult.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
      mcAddressDatas[i] = addressResult.Content;
      addressResult = (OperateResult<McAddressData>) null;
    }
    byte[] coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadRandomWordCommand(mcAddressDatas) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadRandomWordCommand(mcAddressDatas) : (byte[]) null);
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(read.Content, false)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandom(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" />
  public static async Task<OperateResult<byte[]>> ReadRandomAsync(
    IReadWriteMc mc,
    string[] address,
    ushort[] length)
  {
    if (length.Length != address.Length)
      return new OperateResult<byte[]>(StringResources.Language.TwoParametersLengthIsNotSame);
    McAddressData[] mcAddressDatas = new McAddressData[address.Length];
    for (int i = 0; i < address.Length; ++i)
    {
      OperateResult<McAddressData> addressResult = McAddressData.ParseMelsecFrom(address[i], length[i], false);
      if (!addressResult.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
      mcAddressDatas[i] = addressResult.Content;
      addressResult = (OperateResult<McAddressData>) null;
    }
    byte[] coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadRandomCommand(mcAddressDatas) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadRandomCommand(mcAddressDatas) : (byte[]) null);
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(read.Content, false)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandomInt16(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[])" />
  public static async Task<OperateResult<short[]>> ReadRandomInt16Async(
    IReadWriteMc mc,
    string[] address)
  {
    OperateResult<byte[]> read = await McHelper.ReadRandomAsync(mc, address);
    OperateResult<short[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<short[]>(mc.ByteTransform.TransInt16(read.Content, 0, address.Length)) : OperateResult.CreateFailedResult<short[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandomUInt16(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[])" />
  public static async Task<OperateResult<ushort[]>> ReadRandomUInt16Async(
    IReadWriteMc mc,
    string[] address)
  {
    OperateResult<byte[]> read = await McHelper.ReadRandomAsync(mc, address);
    OperateResult<ushort[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<ushort[]>(mc.ByteTransform.TransUInt16(read.Content, 0, address.Length)) : OperateResult.CreateFailedResult<ushort[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取缓冲寄存器的数据信息，地址直接为偏移地址<br />
  /// <b>[Authorization]</b> Read the data information of the buffer register, the address is directly the offset address
  /// </summary>
  /// <remarks>
  /// 本指令不可以访问下述缓冲存储器:<br />
  /// 1. 本站(SLMP对应设备)上安装的智能功能模块<br />
  /// 2. 其它站缓冲存储器<br />
  /// </remarks>
  /// <param name="mc">MC通信对象</param>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>读取的内容</returns>
  public static OperateResult<byte[]> ReadMemory(IReadWriteMc mc, string address, ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMemoryCommand(address, length) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMemoryCommand(address, length) : (OperateResult<byte[]>) null);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte[]> result = mc.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(result.Content, false));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadMemory(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadMemoryAsync(
    IReadWriteMc mc,
    string address,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMemoryCommand(address, length) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMemoryCommand(address, length) : (OperateResult<byte[]>) null);
    if (!coreResult.IsSuccess)
      return coreResult;
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(read.Content, false)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取智能模块的数据信息，需要指定模块地址，偏移地址，读取的字节长度<br />
  /// <b>[Authorization]</b> To read the extended data information, you need to enter the extended value information in addition to the original address and length information
  /// </summary>
  /// <param name="mc">MC通信对象</param>
  /// <param name="module">模块地址</param>
  /// <param name="address">地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>返回结果</returns>
  public static OperateResult<byte[]> ReadSmartModule(
    IReadWriteMc mc,
    ushort module,
    string address,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadSmartModule(module, address, length) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadSmartModule(module, address, length) : (OperateResult<byte[]>) null);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte[]> result = mc.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(result.Content, false));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadSmartModule(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.UInt16,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadSmartModuleAsync(
    IReadWriteMc mc,
    ushort module,
    string address,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadSmartModule(module, address, length) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadSmartModule(module, address, length) : (OperateResult<byte[]>) null);
    if (!coreResult.IsSuccess)
      return coreResult;
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(read.Content, false)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取扩展的数据信息，需要在原有的地址，长度信息之外，输入扩展值信息<br />
  /// <b>[Authorization]</b> To read the extended data information, you need to enter the extended value information in addition to the original address and length information
  /// </summary>
  /// <param name="mc">MC通信对象</param>
  /// <param name="extend">扩展信息</param>
  /// <param name="address">地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>返回结果</returns>
  public static OperateResult<byte[]> ReadExtend(
    IReadWriteMc mc,
    ushort extend,
    string address,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<McAddressData> result1 = mc.McAnalysisAddress(address, length, false);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    byte[] send = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMcCoreExtendCommand(result1.Content, extend, false) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMcCoreExtendCommand(result1.Content, extend, false) : (byte[]) null);
    OperateResult<byte[]> result2 = mc.ReadFromCoreServer(send);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(result2.Content, false));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadExtend(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.UInt16,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadExtendAsync(
    IReadWriteMc mc,
    ushort extend,
    string address,
    ushort length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<McAddressData> addressResult = mc.McAnalysisAddress(address, length, false);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
    byte[] coreResult = mc.McType == McType.McBinary ? McBinaryHelper.BuildReadMcCoreExtendCommand(addressResult.Content, extend, false) : (mc.McType == McType.MCAscii ? McAsciiHelper.BuildAsciiReadMcCoreExtendCommand(addressResult.Content, extend, false) : (byte[]) null);
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(mc.ExtractActualData(read.Content, false)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <summary>
  /// 远程Run操作<br />
  /// Remote Run Operation
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult RemoteRun(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult;
    if (mc.McType != McType.McBinary)
      operateResult = mc.McType == McType.MCAscii ? mc.ReadFromCoreServer(Encoding.ASCII.GetBytes("1001000000010000")) : new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
    else
      operateResult = mc.ReadFromCoreServer(new byte[8]
      {
        (byte) 1,
        (byte) 16 /*0x10*/,
        (byte) 0,
        (byte) 0,
        (byte) 1,
        (byte) 0,
        (byte) 0,
        (byte) 0
      });
    return (OperateResult) operateResult;
  }

  /// <summary>
  /// 远程Stop操作<br />
  /// Remote Stop operation
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult RemoteStop(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult;
    if (mc.McType != McType.McBinary)
      operateResult = mc.McType == McType.MCAscii ? mc.ReadFromCoreServer(Encoding.ASCII.GetBytes("100200000001")) : new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
    else
      operateResult = mc.ReadFromCoreServer(new byte[6]
      {
        (byte) 2,
        (byte) 16 /*0x10*/,
        (byte) 0,
        (byte) 0,
        (byte) 1,
        (byte) 0
      });
    return (OperateResult) operateResult;
  }

  /// <summary>
  /// 远程Reset操作<br />
  /// Remote Reset Operation
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult RemoteReset(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult;
    if (mc.McType != McType.McBinary)
      operateResult = mc.McType == McType.MCAscii ? mc.ReadFromCoreServer(Encoding.ASCII.GetBytes("100600000001")) : new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
    else
      operateResult = mc.ReadFromCoreServer(new byte[6]
      {
        (byte) 6,
        (byte) 16 /*0x10*/,
        (byte) 0,
        (byte) 0,
        (byte) 1,
        (byte) 0
      });
    return (OperateResult) operateResult;
  }

  /// <summary>
  /// 读取PLC的型号信息，例如 Q02HCPU<br />
  /// Read PLC model information, such as Q02HCPU
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <returns>返回型号的结果对象</returns>
  public static OperateResult<string> ReadPlcType(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult;
    if (mc.McType != McType.McBinary)
      operateResult = mc.McType == McType.MCAscii ? mc.ReadFromCoreServer(Encoding.ASCII.GetBytes("01010000")) : new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
    else
      operateResult = mc.ReadFromCoreServer(new byte[4]
      {
        (byte) 1,
        (byte) 1,
        (byte) 0,
        (byte) 0
      });
    OperateResult<byte[]> result = operateResult;
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result) : OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(result.Content, 0, 16 /*0x10*/).TrimEnd());
  }

  /// <summary>
  /// LED 熄灭 出错代码初始化<br />
  /// LED off Error code initialization
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <returns>是否成功</returns>
  public static OperateResult ErrorStateReset(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult;
    if (mc.McType != McType.McBinary)
      operateResult = mc.McType == McType.MCAscii ? mc.ReadFromCoreServer(Encoding.ASCII.GetBytes("16170000")) : new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
    else
      operateResult = mc.ReadFromCoreServer(new byte[4]
      {
        (byte) 23,
        (byte) 22,
        (byte) 0,
        (byte) 0
      });
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.RemoteRun(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  public static async Task<OperateResult> RemoteRunAsync(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult1;
    if (mc.McType == McType.McBinary)
    {
      operateResult1 = await mc.ReadFromCoreServerAsync(new byte[8]
      {
        (byte) 1,
        (byte) 16 /*0x10*/,
        (byte) 0,
        (byte) 0,
        (byte) 1,
        (byte) 0,
        (byte) 0,
        (byte) 0
      });
    }
    else
    {
      OperateResult<byte[]> operateResult2;
      if (mc.McType == McType.MCAscii)
        operateResult2 = await mc.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("1001000000010000"));
      else
        operateResult2 = new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      operateResult1 = operateResult2;
      operateResult2 = (OperateResult<byte[]>) null;
    }
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.RemoteStop(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  public static async Task<OperateResult> RemoteStopAsync(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult1;
    if (mc.McType == McType.McBinary)
    {
      operateResult1 = await mc.ReadFromCoreServerAsync(new byte[6]
      {
        (byte) 2,
        (byte) 16 /*0x10*/,
        (byte) 0,
        (byte) 0,
        (byte) 1,
        (byte) 0
      });
    }
    else
    {
      OperateResult<byte[]> operateResult2;
      if (mc.McType == McType.MCAscii)
        operateResult2 = await mc.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("100200000001"));
      else
        operateResult2 = new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      operateResult1 = operateResult2;
      operateResult2 = (OperateResult<byte[]>) null;
    }
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.RemoteReset(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  public static async Task<OperateResult> RemoteResetAsync(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult1;
    if (mc.McType == McType.McBinary)
    {
      operateResult1 = await mc.ReadFromCoreServerAsync(new byte[6]
      {
        (byte) 6,
        (byte) 16 /*0x10*/,
        (byte) 0,
        (byte) 0,
        (byte) 1,
        (byte) 0
      });
    }
    else
    {
      OperateResult<byte[]> operateResult2;
      if (mc.McType == McType.MCAscii)
        operateResult2 = await mc.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("100600000001"));
      else
        operateResult2 = new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      operateResult1 = operateResult2;
      operateResult2 = (OperateResult<byte[]>) null;
    }
    return (OperateResult) operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadPlcType(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  public static async Task<OperateResult<string>> ReadPlcTypeAsync(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult1;
    if (mc.McType == McType.McBinary)
    {
      operateResult1 = await mc.ReadFromCoreServerAsync(new byte[4]
      {
        (byte) 1,
        (byte) 1,
        (byte) 0,
        (byte) 0
      });
    }
    else
    {
      OperateResult<byte[]> operateResult2;
      if (mc.McType == McType.MCAscii)
        operateResult2 = await mc.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("01010000"));
      else
        operateResult2 = new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      operateResult1 = operateResult2;
      operateResult2 = (OperateResult<byte[]>) null;
    }
    OperateResult<byte[]> read = operateResult1;
    operateResult1 = (OperateResult<byte[]>) null;
    OperateResult<string> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(read.Content, 0, 16 /*0x10*/).TrimEnd()) : OperateResult.CreateFailedResult<string>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ErrorStateReset(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  public static async Task<OperateResult> ErrorStateResetAsync(IReadWriteMc mc)
  {
    OperateResult<byte[]> operateResult1;
    if (mc.McType == McType.McBinary)
    {
      operateResult1 = await mc.ReadFromCoreServerAsync(new byte[4]
      {
        (byte) 23,
        (byte) 22,
        (byte) 0,
        (byte) 0
      });
    }
    else
    {
      OperateResult<byte[]> operateResult2;
      if (mc.McType == McType.MCAscii)
        operateResult2 = await mc.ReadFromCoreServerAsync(Encoding.ASCII.GetBytes("16170000"));
      else
        operateResult2 = new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      operateResult1 = operateResult2;
      operateResult2 = (OperateResult<byte[]>) null;
    }
    return (OperateResult) operateResult1;
  }
}
