﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.McAsciiHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Address;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>基于MC协议的ASCII格式的辅助类</summary>
public class McAsciiHelper
{
  /// <summary>将MC协议的核心报文打包成一个可以直接对PLC进行发送的原始报文</summary>
  /// <param name="mc">三菱MC协议的核心通信对象</param>
  /// <param name="mcCore">MC协议的核心报文</param>
  /// <returns>原始报文信息</returns>
  public static byte[] PackMcCommand(IReadWriteMc mc, byte[] mcCore)
  {
    byte[] numArray1 = SoftBasic.BuildAsciiBytesFrom(mc.TargetIOStation);
    byte[] numArray2 = new byte[22 + mcCore.Length];
    numArray2[0] = (byte) 53;
    numArray2[1] = (byte) 48 /*0x30*/;
    numArray2[2] = (byte) 48 /*0x30*/;
    numArray2[3] = (byte) 48 /*0x30*/;
    numArray2[4] = SoftBasic.BuildAsciiBytesFrom(mc.NetworkNumber)[0];
    numArray2[5] = SoftBasic.BuildAsciiBytesFrom(mc.NetworkNumber)[1];
    numArray2[6] = SoftBasic.BuildAsciiBytesFrom(mc.PLCNumber)[0];
    numArray2[7] = SoftBasic.BuildAsciiBytesFrom(mc.PLCNumber)[1];
    numArray2[8] = numArray1[0];
    numArray2[9] = numArray1[1];
    numArray2[10] = numArray1[2];
    numArray2[11] = numArray1[3];
    numArray2[12] = SoftBasic.BuildAsciiBytesFrom(mc.NetworkStationNumber)[0];
    numArray2[13] = SoftBasic.BuildAsciiBytesFrom(mc.NetworkStationNumber)[1];
    numArray2[14] = SoftBasic.BuildAsciiBytesFrom((ushort) (numArray2.Length - 18))[0];
    numArray2[15] = SoftBasic.BuildAsciiBytesFrom((ushort) (numArray2.Length - 18))[1];
    numArray2[16 /*0x10*/] = SoftBasic.BuildAsciiBytesFrom((ushort) (numArray2.Length - 18))[2];
    numArray2[17] = SoftBasic.BuildAsciiBytesFrom((ushort) (numArray2.Length - 18))[3];
    numArray2[18] = (byte) 48 /*0x30*/;
    numArray2[19] = (byte) 48 /*0x30*/;
    numArray2[20] = (byte) 49;
    numArray2[21] = (byte) 48 /*0x30*/;
    mcCore.CopyTo((Array) numArray2, 22);
    return numArray2;
  }

  /// <summary>从PLC反馈的数据中提取出实际的数据内容，需要传入反馈数据，是否位读取</summary>
  /// <param name="response">反馈的数据内容</param>
  /// <param name="isBit">是否位读取</param>
  /// <returns>解析后的结果对象</returns>
  public static byte[] ExtractActualDataHelper(byte[] response, bool isBit)
  {
    return isBit ? ((IEnumerable<byte>) response).Select<byte, byte>((Func<byte, byte>) (m => m != (byte) 48 /*0x30*/ ? (byte) 1 : (byte) 0)).ToArray<byte>() : MelsecHelper.TransAsciiByteArrayToByteArray(response);
  }

  /// <summary>检查反馈的内容是否正确的</summary>
  /// <param name="content">MC的反馈的内容</param>
  /// <returns>是否正确</returns>
  public static OperateResult CheckResponseContent(byte[] content)
  {
    if (content == null || content.Length < 22)
      return new OperateResult($"{StringResources.Language.ReceiveDataLengthTooShort}22, Content: {SoftBasic.GetAsciiStringRender(content)}");
    ushort uint16 = Convert.ToUInt16(Encoding.ASCII.GetString(content, 18, 4), 16 /*0x10*/);
    return uint16 > (ushort) 0 ? new OperateResult((int) uint16, MelsecHelper.GetErrorDescription((int) uint16)) : OperateResult.CreateSuccessResult();
  }

  /// <summary>从三菱地址，是否位读取进行创建读取Ascii格式的MC的核心报文</summary>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <param name="isBit">是否进行了位读取操作</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildAsciiReadMcCoreCommand(McAddressData addressData, bool isBit)
  {
    return new byte[20]
    {
      (byte) 48 /*0x30*/,
      (byte) 52,
      (byte) 48 /*0x30*/,
      (byte) 49,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      isBit ? (byte) 49 : (byte) 48 /*0x30*/,
      Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[0],
      Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[1],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[0],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[1],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[2],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[3],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[4],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[5],
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[0],
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[1],
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[2],
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[3]
    };
  }

  /// <summary>从三菱扩展地址，是否位读取进行创建读取的MC的核心报文</summary>
  /// <param name="isBit">是否进行了位读取操作</param>
  /// <param name="extend">扩展指定</param>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildAsciiReadMcCoreExtendCommand(
    McAddressData addressData,
    ushort extend,
    bool isBit)
  {
    return new byte[32 /*0x20*/]
    {
      (byte) 48 /*0x30*/,
      (byte) 52,
      (byte) 48 /*0x30*/,
      (byte) 49,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 56,
      isBit ? (byte) 49 : (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 74,
      SoftBasic.BuildAsciiBytesFrom(extend)[1],
      SoftBasic.BuildAsciiBytesFrom(extend)[2],
      SoftBasic.BuildAsciiBytesFrom(extend)[3],
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[0],
      Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[1],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[0],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[1],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[2],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[3],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[4],
      MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[5],
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[0],
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[1],
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[2],
      SoftBasic.BuildAsciiBytesFrom(addressData.Length)[3]
    };
  }

  /// <summary>以字为单位，创建ASCII数据写入的核心报文</summary>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <param name="value">实际的原始数据信息</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildAsciiWriteWordCoreCommand(McAddressData addressData, byte[] value)
  {
    value = MelsecHelper.TransByteArrayToAsciiByteArray(value);
    byte[] numArray = new byte[20 + value.Length];
    numArray[0] = (byte) 49;
    numArray[1] = (byte) 52;
    numArray[2] = (byte) 48 /*0x30*/;
    numArray[3] = (byte) 49;
    numArray[4] = (byte) 48 /*0x30*/;
    numArray[5] = (byte) 48 /*0x30*/;
    numArray[6] = (byte) 48 /*0x30*/;
    numArray[7] = (byte) 48 /*0x30*/;
    numArray[8] = Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[0];
    numArray[9] = Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[1];
    numArray[10] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[0];
    numArray[11] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[1];
    numArray[12] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[2];
    numArray[13] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[3];
    numArray[14] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[4];
    numArray[15] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[5];
    numArray[16 /*0x10*/] = SoftBasic.BuildAsciiBytesFrom((ushort) (value.Length / 4))[0];
    numArray[17] = SoftBasic.BuildAsciiBytesFrom((ushort) (value.Length / 4))[1];
    numArray[18] = SoftBasic.BuildAsciiBytesFrom((ushort) (value.Length / 4))[2];
    numArray[19] = SoftBasic.BuildAsciiBytesFrom((ushort) (value.Length / 4))[3];
    value.CopyTo((Array) numArray, 20);
    return numArray;
  }

  /// <summary>以位为单位，创建ASCII数据写入的核心报文</summary>
  /// <param name="addressData">三菱Mc协议的数据地址</param>
  /// <param name="value">原始的bool数组数据</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildAsciiWriteBitCoreCommand(McAddressData addressData, bool[] value)
  {
    if (value == null)
      value = new bool[0];
    byte[] array = ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>();
    byte[] numArray = new byte[20 + array.Length];
    numArray[0] = (byte) 49;
    numArray[1] = (byte) 52;
    numArray[2] = (byte) 48 /*0x30*/;
    numArray[3] = (byte) 49;
    numArray[4] = (byte) 48 /*0x30*/;
    numArray[5] = (byte) 48 /*0x30*/;
    numArray[6] = (byte) 48 /*0x30*/;
    numArray[7] = (byte) 49;
    numArray[8] = Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[0];
    numArray[9] = Encoding.ASCII.GetBytes(addressData.McDataType.AsciiCode)[1];
    numArray[10] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[0];
    numArray[11] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[1];
    numArray[12] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[2];
    numArray[13] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[3];
    numArray[14] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[4];
    numArray[15] = MelsecHelper.BuildBytesFromAddress(addressData.AddressStart, addressData.McDataType)[5];
    numArray[16 /*0x10*/] = SoftBasic.BuildAsciiBytesFrom((ushort) value.Length)[0];
    numArray[17] = SoftBasic.BuildAsciiBytesFrom((ushort) value.Length)[1];
    numArray[18] = SoftBasic.BuildAsciiBytesFrom((ushort) value.Length)[2];
    numArray[19] = SoftBasic.BuildAsciiBytesFrom((ushort) value.Length)[3];
    array.CopyTo((Array) numArray, 20);
    return numArray;
  }

  /// <summary>按字为单位随机读取的指令创建</summary>
  /// <param name="address">地址数组</param>
  /// <returns>指令</returns>
  public static byte[] BuildAsciiReadRandomWordCommand(McAddressData[] address)
  {
    byte[] numArray = new byte[12 + address.Length * 8];
    numArray[0] = (byte) 48 /*0x30*/;
    numArray[1] = (byte) 52;
    numArray[2] = (byte) 48 /*0x30*/;
    numArray[3] = (byte) 51;
    numArray[4] = (byte) 48 /*0x30*/;
    numArray[5] = (byte) 48 /*0x30*/;
    numArray[6] = (byte) 48 /*0x30*/;
    numArray[7] = (byte) 48 /*0x30*/;
    numArray[8] = SoftBasic.BuildAsciiBytesFrom((byte) address.Length)[0];
    numArray[9] = SoftBasic.BuildAsciiBytesFrom((byte) address.Length)[1];
    numArray[10] = (byte) 48 /*0x30*/;
    numArray[11] = (byte) 48 /*0x30*/;
    for (int index = 0; index < address.Length; ++index)
    {
      numArray[index * 8 + 12] = Encoding.ASCII.GetBytes(address[index].McDataType.AsciiCode)[0];
      numArray[index * 8 + 13] = Encoding.ASCII.GetBytes(address[index].McDataType.AsciiCode)[1];
      numArray[index * 8 + 14] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[0];
      numArray[index * 8 + 15] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[1];
      numArray[index * 8 + 16 /*0x10*/] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[2];
      numArray[index * 8 + 17] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[3];
      numArray[index * 8 + 18] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[4];
      numArray[index * 8 + 19] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[5];
    }
    return numArray;
  }

  /// <summary>随机读取的指令创建</summary>
  /// <param name="address">地址数组</param>
  /// <returns>指令</returns>
  public static byte[] BuildAsciiReadRandomCommand(McAddressData[] address)
  {
    byte[] numArray = new byte[12 + address.Length * 12];
    numArray[0] = (byte) 48 /*0x30*/;
    numArray[1] = (byte) 52;
    numArray[2] = (byte) 48 /*0x30*/;
    numArray[3] = (byte) 54;
    numArray[4] = (byte) 48 /*0x30*/;
    numArray[5] = (byte) 48 /*0x30*/;
    numArray[6] = (byte) 48 /*0x30*/;
    numArray[7] = (byte) 48 /*0x30*/;
    numArray[8] = SoftBasic.BuildAsciiBytesFrom((byte) address.Length)[0];
    numArray[9] = SoftBasic.BuildAsciiBytesFrom((byte) address.Length)[1];
    numArray[10] = (byte) 48 /*0x30*/;
    numArray[11] = (byte) 48 /*0x30*/;
    for (int index = 0; index < address.Length; ++index)
    {
      numArray[index * 12 + 12] = Encoding.ASCII.GetBytes(address[index].McDataType.AsciiCode)[0];
      numArray[index * 12 + 13] = Encoding.ASCII.GetBytes(address[index].McDataType.AsciiCode)[1];
      numArray[index * 12 + 14] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[0];
      numArray[index * 12 + 15] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[1];
      numArray[index * 12 + 16 /*0x10*/] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[2];
      numArray[index * 12 + 17] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[3];
      numArray[index * 12 + 18] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[4];
      numArray[index * 12 + 19] = MelsecHelper.BuildBytesFromAddress(address[index].AddressStart, address[index].McDataType)[5];
      numArray[index * 12 + 20] = SoftBasic.BuildAsciiBytesFrom(address[index].Length)[0];
      numArray[index * 12 + 21] = SoftBasic.BuildAsciiBytesFrom(address[index].Length)[1];
      numArray[index * 12 + 22] = SoftBasic.BuildAsciiBytesFrom(address[index].Length)[2];
      numArray[index * 12 + 23] = SoftBasic.BuildAsciiBytesFrom(address[index].Length)[3];
    }
    return numArray;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.BuildReadMemoryCommand(System.String,System.UInt16)" />
  public static OperateResult<byte[]> BuildAsciiReadMemoryCommand(string address, ushort length)
  {
    try
    {
      uint num = uint.Parse(address);
      byte[] numArray = new byte[20];
      numArray[0] = (byte) 48 /*0x30*/;
      numArray[1] = (byte) 54;
      numArray[2] = (byte) 49;
      numArray[3] = (byte) 51;
      numArray[4] = (byte) 48 /*0x30*/;
      numArray[5] = (byte) 48 /*0x30*/;
      numArray[6] = (byte) 48 /*0x30*/;
      numArray[7] = (byte) 48 /*0x30*/;
      SoftBasic.BuildAsciiBytesFrom(num).CopyTo((Array) numArray, 8);
      SoftBasic.BuildAsciiBytesFrom(length).CopyTo((Array) numArray, 16 /*0x10*/);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.BuildReadSmartModule(System.UInt16,System.String,System.UInt16)" />
  public static OperateResult<byte[]> BuildAsciiReadSmartModule(
    ushort module,
    string address,
    ushort length)
  {
    try
    {
      uint num = uint.Parse(address);
      byte[] numArray = new byte[24];
      numArray[0] = (byte) 48 /*0x30*/;
      numArray[1] = (byte) 54;
      numArray[2] = (byte) 48 /*0x30*/;
      numArray[3] = (byte) 49;
      numArray[4] = (byte) 48 /*0x30*/;
      numArray[5] = (byte) 48 /*0x30*/;
      numArray[6] = (byte) 48 /*0x30*/;
      numArray[7] = (byte) 48 /*0x30*/;
      SoftBasic.BuildAsciiBytesFrom(num).CopyTo((Array) numArray, 8);
      SoftBasic.BuildAsciiBytesFrom(length).CopyTo((Array) numArray, 16 /*0x10*/);
      SoftBasic.BuildAsciiBytesFrom(module).CopyTo((Array) numArray, 20);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.BuildReadTag(System.String[],System.UInt16[],System.Boolean)" />
  public static byte[] BuildAsciiReadTag(string[] tags, ushort[] lengths, bool isBit = false)
  {
    if (tags.Length != lengths.Length)
      throw new Exception(StringResources.Language.TwoParametersLengthIsNotSame);
    MemoryStream ms = new MemoryStream();
    ms.Write(Encoding.ASCII.GetBytes("041A"));
    ms.Write(Encoding.ASCII.GetBytes("0000"));
    ms.Write(Encoding.ASCII.GetBytes(tags.Length.ToString("X4")));
    ms.Write(Encoding.ASCII.GetBytes("0000"));
    for (int index1 = 0; index1 < tags.Length; ++index1)
    {
      byte[] bytes = Encoding.Unicode.GetBytes(tags[index1]);
      ms.Write(SoftBasic.BuildAsciiBytesFrom((ushort) (bytes.Length / 2)));
      for (int index2 = 0; index2 < bytes.Length; ++index2)
        ms.Write(SoftBasic.BuildAsciiBytesFrom(bytes[index2]));
      if (isBit)
        ms.Write(Encoding.ASCII.GetBytes("00"));
      else
        ms.Write(Encoding.ASCII.GetBytes("01"));
      ms.Write(Encoding.ASCII.GetBytes("00"));
      ms.Write(SoftBasic.BuildAsciiBytesFrom((ushort) ((uint) lengths[index1] * 2U)));
    }
    return ms.ToArray();
  }

  /// <summary>创建写入标签的报文数据信息</summary>
  /// <param name="tag">标签名称</param>
  /// <param name="data">写入的数据信息</param>
  /// <returns>报文数据信息</returns>
  public static byte[] BuildAsciiWriteTag(string tag, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    MemoryStream ms = new MemoryStream();
    ms.Write(Encoding.ASCII.GetBytes("141A"));
    ms.Write(Encoding.ASCII.GetBytes("0000"));
    ms.Write(Encoding.ASCII.GetBytes("0001"));
    ms.Write(Encoding.ASCII.GetBytes("0000"));
    byte[] bytes = Encoding.Unicode.GetBytes(tag);
    ms.Write(SoftBasic.BuildAsciiBytesFrom((ushort) (bytes.Length / 2)));
    for (int index = 0; index < bytes.Length; ++index)
      ms.Write(SoftBasic.BuildAsciiBytesFrom(bytes[index]));
    ms.Write(Encoding.ASCII.GetBytes("01"));
    ms.Write(Encoding.ASCII.GetBytes("00"));
    ms.Write(SoftBasic.BuildAsciiBytesFrom((ushort) data.Length));
    ms.Write(Encoding.ASCII.GetBytes(SoftBasic.BytesReverseByWord(data).ToHexString()));
    return ms.ToArray();
  }

  /// <summary>解析出标签读取的数据内容</summary>
  /// <param name="content">返回的数据信息</param>
  /// <returns>解析结果</returns>
  public static OperateResult<byte[]> ExtraTagData(byte[] content)
  {
    try
    {
      int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(content, 0, 4), 16 /*0x10*/);
      int num = 4;
      List<byte> byteList = new List<byte>(20);
      for (int index = 0; index < int32_1; ++index)
      {
        int int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(content, num + 4, 4), 16 /*0x10*/);
        byteList.AddRange((IEnumerable<byte>) SoftBasic.BytesReverseByWord(Encoding.ASCII.GetString(content, num + 8, int32_2 * 2).ToHexBytes()));
        num += 8 + int32_2 * 2;
      }
      return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"{ex.Message} Source:{SoftBasic.ByteToHexString(content, ' ')}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McBinaryHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" />
  public static OperateResult<byte[]> ReadTags(IReadWriteMc mc, string[] tags, ushort[] length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] send = McAsciiHelper.BuildAsciiReadTag(tags, length);
    OperateResult<byte[]> result = mc.ReadFromCoreServer(send);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : McAsciiHelper.ExtraTagData(mc.ExtractActualData(result.Content, false));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McAsciiHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" />
  public static async Task<OperateResult<byte[]>> ReadTagsAsync(
    IReadWriteMc mc,
    string[] tags,
    ushort[] length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] coreResult = McAsciiHelper.BuildAsciiReadTag(tags, length);
    OperateResult<byte[]> read = await mc.ReadFromCoreServerAsync(coreResult);
    return read.IsSuccess ? McAsciiHelper.ExtraTagData(mc.ExtractActualData(read.Content, false)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <summary>
  /// <b>[商业授权]</b> 写入PLC的标签数据，需要传入标签的名称，实际写入的字节数据信息，标签举例：A; label[1]; bbb[10,10,10]<br />
  /// <b>[Authorization]</b> To write PLC label data, you need to pass the name of the label, the actual written byte data information, label example: A; label[1];  BBB,10,10 [10]
  /// </summary>
  /// <param name="mc">MC协议通信对象</param>
  /// <param name="tag">标签名</param>
  /// <param name="data">写入的数据</param>
  /// <returns>是否写入成功</returns>
  /// <remarks>
  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McAsciiHelper.ReadTags(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" path="remarks" />
  /// </remarks>
  public static OperateResult WriteTag(IReadWriteMc mc, string tag, byte[] data)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] send = McAsciiHelper.BuildAsciiWriteTag(tag, data);
    return (OperateResult) mc.ReadFromCoreServer(send);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McAsciiHelper.WriteTag(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteTagAsync(IReadWriteMc mc, string tag, byte[] data)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    byte[] coreResult = McAsciiHelper.BuildAsciiWriteTag(tag, data);
    OperateResult<byte[]> operateResult = await mc.ReadFromCoreServerAsync(coreResult);
    return (OperateResult) operateResult;
  }
}
