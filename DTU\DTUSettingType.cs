﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.DTU.DTUSettingType
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Device;
using HslCommunication.ModBus;
using HslCommunication.Profinet.AllenBradley;
using HslCommunication.Profinet.Melsec;
using HslCommunication.Profinet.Omron;
using HslCommunication.Profinet.Siemens;
using Newtonsoft.Json.Linq;
using System;

#nullable disable
namespace HslCommunication.DTU;

/// <summary>DTU的类型设置器</summary>
public class DTUSettingType
{
  /// <summary>设备的唯一ID信息</summary>
  public string DtuId { get; set; }

  /// <summary>当前的设备的类型</summary>
  public string DtuType { get; set; } = "ModbusRtuOverTcp";

  /// <summary>额外的参数都存放在json里面</summary>
  public string JsonParameter { get; set; } = "{}";

  /// <inheritdoc />
  public override string ToString() => $"{this.DtuId} [{this.DtuType}]";

  /// <summary>根据类型，获取连接对象</summary>
  /// <returns>获取设备的连接对象</returns>
  public virtual DeviceTcpNet GetClient()
  {
    JObject jobject = JObject.Parse(this.JsonParameter);
    if (this.DtuType == "ModbusRtuOverTcp")
    {
      ModbusRtuOverTcp client = new ModbusRtuOverTcp("127.0.0.1", station: jobject["Station"].Value<byte>());
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "ModbusTcpNet")
    {
      ModbusTcpNet client = new ModbusTcpNet("127.0.0.1", station: jobject["Station"].Value<byte>());
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "MelsecMcNet")
    {
      MelsecMcNet client = new MelsecMcNet("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "MelsecMcAsciiNet")
    {
      MelsecMcAsciiNet client = new MelsecMcAsciiNet("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "MelsecA1ENet")
    {
      MelsecA1ENet client = new MelsecA1ENet("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "MelsecA1EAsciiNet")
    {
      MelsecA1EAsciiNet client = new MelsecA1EAsciiNet("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "MelsecA3CNetOverTcp")
    {
      MelsecA3CNetOverTcp client = new MelsecA3CNetOverTcp("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "MelsecFxLinksOverTcp")
    {
      MelsecFxLinksOverTcp client = new MelsecFxLinksOverTcp("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "MelsecFxSerialOverTcp")
    {
      MelsecFxSerialOverTcp client = new MelsecFxSerialOverTcp("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "SiemensS7Net")
    {
      SiemensS7Net client = new SiemensS7Net((SiemensPLCS) Enum.Parse(typeof (SiemensPLCS), jobject["SiemensPLCS"].Value<string>()));
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "SiemensFetchWriteNet")
    {
      SiemensFetchWriteNet client = new SiemensFetchWriteNet("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "SiemensPPIOverTcp")
    {
      SiemensPPIOverTcp client = new SiemensPPIOverTcp("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "OmronFinsNet")
    {
      OmronFinsNet client = new OmronFinsNet("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (this.DtuType == "OmronHostLinkOverTcp")
    {
      OmronHostLinkOverTcp client = new OmronHostLinkOverTcp("127.0.0.1", 5000);
      client.ConnectionId = this.DtuId;
      return (DeviceTcpNet) client;
    }
    if (!(this.DtuType == "AllenBradleyNet"))
      throw new NotImplementedException();
    AllenBradleyNet client1 = new AllenBradleyNet("127.0.0.1", 5000);
    client1.ConnectionId = this.DtuId;
    return (DeviceTcpNet) client1;
  }
}
