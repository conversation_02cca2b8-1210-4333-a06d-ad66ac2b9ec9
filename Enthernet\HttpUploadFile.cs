﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.HttpUploadFile
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Enthernet;

/// <summary>获取或设置Http模式下上传的文件信息</summary>
public class HttpUploadFile
{
  /// <summary>获取或设置文本的名称</summary>
  public string FileName { get; set; }

  /// <summary>获取或设置用户设置的名称</summary>
  public string Name { get; set; }

  /// <summary>获取或设置文件的内容</summary>
  public byte[] Content { get; set; }
}
