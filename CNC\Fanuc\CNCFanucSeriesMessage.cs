﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.CNCFanucSeriesMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>Fanuc床子的消息对象</summary>
public class CNCFanucSeriesMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc />
  public int ProtocolHeadBytesLength => 10;

  /// <inheritdoc />
  public int GetContentLengthByHeadBytes()
  {
    return (int) this.HeadBytes[8] * 256 /*0x0100*/ + (int) this.HeadBytes[9];
  }

  /// <inheritdoc />
  public override string ToString() => nameof (CNCFanucSeriesMessage);
}
