﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.IEC.IecValueObject`1
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Instrument.IEC.Helper;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Instrument.IEC;

/// <summary>IEC的数据对象，带值，品质信息，地址信息，时标信息</summary>
/// <typeparam name="T">数据的类型</typeparam>
public class IecValueObject<T>
{
  private T m_value;

  /// <summary>值信息</summary>
  public T Value
  {
    get => this.m_value;
    set
    {
      bool flag = false;
      if (!this.m_value.Equals((object) value))
        flag = true;
      this.m_value = value;
      if (!flag)
        return;
      Action<IecValueObject<T>> onValueChanged = this.OnValueChanged;
      if (onValueChanged == null)
        return;
      onValueChanged(this);
    }
  }

  /// <summary>品质数据</summary>
  public byte Quality { get; set; }

  /// <summary>时间信息，对于不带绝对时标的则无效</summary>
  public DateTime Time { get; set; }

  /// <summary>地址</summary>
  public int Address { get; set; }

  /// <summary>值发生变化的事件</summary>
  public Action<IecValueObject<T>> OnValueChanged { get; set; }

  /// <summary>用户关联的自定义对象</summary>
  public object Tag { get; set; }

  /// <summary>
  /// 解析不连续的遥信值的方法，对于返回的 byte 类型的指，单点遥信：0 开，1合、双点遥信: 1开，2合，0和3不确定状态或中间填充
  /// </summary>
  /// <param name="message">IEC104的消息</param>
  /// <returns>列表值</returns>
  public static List<IecValueObject<byte>> ParseYaoXinValue(IEC104MessageEventArgs message)
  {
    bool flag = message.WithTimeInfo();
    List<IecValueObject<byte>> yaoXinValue = new List<IecValueObject<byte>>();
    DateTime dateTime = DateTime.MinValue;
    if (message.IsAddressContinuous)
    {
      if (flag && message.Body.Length >= message.InfoObjectCount + 3 + 7)
        dateTime = IECHelper.PraseTimeFromAbsoluteTimeScale(message.Body, message.InfoObjectCount + 3);
      ushort uint16 = BitConverter.ToUInt16(message.Body, 0);
      for (int index1 = 0; index1 < message.InfoObjectCount; ++index1)
      {
        int index2 = 3 + index1;
        if (index2 >= message.Body.Length)
          return yaoXinValue;
        IecValueObject<byte> iecValueObject = new IecValueObject<byte>();
        iecValueObject.Address = (int) uint16 + index1;
        iecValueObject.Value = (byte) ((uint) message.Body[index2] & 15U);
        iecValueObject.Quality = (byte) ((uint) message.Body[index2] & 240U /*0xF0*/);
        if (flag)
          iecValueObject.Time = dateTime;
        yaoXinValue.Add(iecValueObject);
      }
    }
    else
    {
      if (flag && message.Body.Length >= message.InfoObjectCount * 4 + 7)
        dateTime = IECHelper.PraseTimeFromAbsoluteTimeScale(message.Body, message.InfoObjectCount * 4);
      for (int index = 0; index < message.InfoObjectCount; ++index)
      {
        int startIndex = 4 * index;
        if (startIndex >= message.Body.Length)
          return yaoXinValue;
        IecValueObject<byte> iecValueObject = new IecValueObject<byte>();
        iecValueObject.Address = (int) BitConverter.ToUInt16(message.Body, startIndex);
        iecValueObject.Value = (byte) ((uint) message.Body[startIndex + 3] & 15U);
        iecValueObject.Quality = (byte) ((uint) message.Body[startIndex + 3] & 240U /*0xF0*/);
        if (flag)
          iecValueObject.Time = dateTime;
        yaoXinValue.Add(iecValueObject);
      }
    }
    return yaoXinValue;
  }

  /// <summary>解析不连续的的数据，返回指定的类型信息</summary>
  /// <param name="iEC104Message">IEC104的消息</param>
  /// <returns>值列表信息</returns>
  public static List<IecValueObject<short>> ParseInt16Value(IEC104MessageEventArgs iEC104Message)
  {
    return IECHelper.ParseYaoCeValue<short>(iEC104Message, new Func<byte[], int, short>(BitConverter.ToInt16), 2);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.IEC.IecValueObject`1.ParseInt16Value(HslCommunication.Instrument.IEC.IEC104MessageEventArgs)" />
  public static List<IecValueObject<float>> ParseFloatValue(IEC104MessageEventArgs iEC104Message)
  {
    return IECHelper.ParseYaoCeValue<float>(iEC104Message, new Func<byte[], int, float>(BitConverter.ToSingle), 4);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.IEC.IecValueObject`1.ParseInt16Value(HslCommunication.Instrument.IEC.IEC104MessageEventArgs)" />
  public static List<IecValueObject<uint>> ParseUInt32Value(IEC104MessageEventArgs iEC104Message)
  {
    return IECHelper.ParseYaoCeValue<uint>(iEC104Message, new Func<byte[], int, uint>(BitConverter.ToUInt32), 4);
  }
}
