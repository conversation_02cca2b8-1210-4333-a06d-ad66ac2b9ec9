﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Beckhoff.AmsTcpHeaderFlags
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Beckhoff;

/// <summary>AMS消息的命令号</summary>
public enum AmsTcpHeaderFlags : ushort
{
  /// <summary>AmsCommand (AMS_TCP_PORT_AMS_CMD, 0x0000)</summary>
  Command = 0,
  /// <summary>Port Close command (AMS_TCP_PORT_CLOSE, 0x0001)</summary>
  PortClose = 1,
  /// <summary>Port connect command (AMS_TCP_PORT_CONNECT, 0x1000)</summary>
  PortConnect = 4096, // 0x1000
  /// <summary>
  /// Router Notification (AMS_TCP_PORT_ROUTER_NOTE, 0x1001)
  /// </summary>
  RouterNotification = 4097, // 0x1001
  /// <summary>Get LocalNetId header</summary>
  GetLocalNetId = 4098, // 0x1002
}
