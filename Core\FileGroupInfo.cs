﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.FileGroupInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>文件的分类信息</summary>
public class FileGroupInfo
{
  /// <summary>命令码</summary>
  public int Command { get; set; }

  /// <summary>文件名</summary>
  public string FileName { get; set; }

  /// <summary>文件名列表</summary>
  public string[] FileNames { get; set; }

  /// <summary>第一级分类信息</summary>
  public string Factory { get; set; }

  /// <summary>第二级分类信息</summary>
  public string Group { get; set; }

  /// <summary>第三级分类信息</summary>
  public string Identify { get; set; }
}
