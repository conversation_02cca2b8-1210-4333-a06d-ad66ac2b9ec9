﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.FanucSysInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Text;

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>Fanuc的系统信息</summary>
public class FanucSysInfo
{
  /// <summary>实例化一个空对象</summary>
  public FanucSysInfo()
  {
  }

  /// <summary>使用缓存数据来实例化一个对象</summary>
  /// <param name="buffer">原始的字节信息</param>
  public FanucSysInfo(byte[] buffer)
  {
    this.TypeCode = Encoding.ASCII.GetString(buffer, 32 /*0x20*/, 2);
    switch (this.TypeCode)
    {
      case " 0":
        this.CncType = "Series 0i";
        break;
      case "15":
        this.CncType = "Series 15/15i";
        break;
      case "16":
        this.CncType = "Series 16/16i";
        break;
      case "18":
        this.CncType = "Series 18/18i";
        break;
      case "21":
        this.CncType = "Series 21/210i";
        break;
      case "30":
        this.CncType = "Series 30i";
        break;
      case "31":
        this.CncType = "Series 31i";
        break;
      case "32":
        this.CncType = "Series 32i";
        break;
      case "PD":
        this.CncType = "Power Mate i-D";
        break;
      case "PH":
        this.CncType = "Power Mate i-H";
        break;
    }
    this.CncType += "-";
    switch (Encoding.ASCII.GetString(buffer, 34, 2))
    {
      case " L":
        this.MtType = "Laser";
        break;
      case " M":
        this.MtType = "Machining center";
        break;
      case " P":
        this.MtType = "Punch press";
        break;
      case " T":
        this.MtType = "Lathe";
        break;
      case " W":
        this.MtType = "Wire cut";
        break;
      case "MM":
        this.MtType = "M series with 2 path control";
        break;
      case "MT":
        this.MtType = "T series with compound machining function";
        break;
      case "TT":
        this.MtType = "T series with 2/3 path control";
        break;
    }
    this.CncType += Encoding.ASCII.GetString(buffer, 34, 2).Trim();
    switch (buffer[28])
    {
      case 1:
        this.CncType += "A";
        break;
      case 2:
        this.CncType += "B";
        break;
      case 3:
        this.CncType += "C";
        break;
      case 4:
        this.CncType += "D";
        break;
      case 6:
        this.CncType += "F";
        break;
    }
    this.Series = Encoding.ASCII.GetString(buffer, 36, 4);
    this.Version = Encoding.ASCII.GetString(buffer, 40, 4);
    this.Axes = int.Parse(Encoding.ASCII.GetString(buffer, 44, 2));
  }

  /// <summary>CNC的类型代号</summary>
  public string TypeCode { get; set; }

  /// <summary>CNC的类型</summary>
  public string CncType { get; set; }

  /// <summary>Kind of M/T,</summary>
  public string MtType { get; set; }

  /// <summary>系列信息</summary>
  public string Series { get; set; }

  /// <summary>版本号信息</summary>
  public string Version { get; set; }

  /// <summary>Current controlled axes</summary>
  public int Axes { get; set; }
}
