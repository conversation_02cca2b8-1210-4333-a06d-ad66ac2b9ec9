﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.StringResources
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Language;
using System.Globalization;

#nullable disable
namespace HslCommunication;

/// <summary>
/// 系统的字符串资源及多语言管理中心<br />
/// System string resource and multi-language management Center
/// </summary>
public static class StringResources
{
  /// <summary>
  /// 获取或设置系统的语言选项<br />
  /// Gets or sets the language options for the system
  /// </summary>
  public static DefaultLanguage Language = new DefaultLanguage();

  static StringResources()
  {
    if (CultureInfo.CurrentCulture.ToString().StartsWith("zh"))
      StringResources.SetLanguageChinese();
    else
      StringResources.SeteLanguageEnglish();
  }

  /// <summary>
  /// 将语言设置为中文<br />
  /// Set the language to Chinese
  /// </summary>
  public static void SetLanguageChinese() => StringResources.Language = new DefaultLanguage();

  /// <summary>
  /// 将语言设置为英文<br />
  /// Set the language to English
  /// </summary>
  public static void SeteLanguageEnglish()
  {
    StringResources.Language = (DefaultLanguage) new English();
  }
}
