﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.XinJEAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>
/// 信捷内部协议的地址类对象<br />
/// The address class object of Xinjie internal protocol
/// </summary>
public class XinJEAddress : DeviceAddressDataBase
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// instantiate a default object
  /// </summary>
  public XinJEAddress()
  {
  }

  /// <summary>
  /// 指定类型，地址偏移，临界地址来实例化一个对象<br />
  /// Specify the type, address offset, and critical address to instantiate an object
  /// </summary>
  /// <param name="dataCode">数据的类型代号</param>
  /// <param name="address">偏移地址信息</param>
  /// <param name="criticalAddress">临界地址信息</param>
  /// <param name="station">站号信息</param>
  public XinJEAddress(byte dataCode, int address, int criticalAddress, byte station)
  {
    this.DataCode = dataCode;
    this.AddressStart = address;
    this.CriticalAddress = criticalAddress;
    this.Station = station;
  }

  /// <summary>
  /// 获取或设置等待读取的数据的代码<br />
  /// Get or set the code of the data waiting to be read
  /// </summary>
  public byte DataCode { get; set; }

  /// <summary>
  /// 获取或设置当前的站号信息<br />
  /// Get or set the current station number information
  /// </summary>
  public byte Station { get; set; }

  /// <summary>
  /// 获取或设置协议升级时候的临界地址信息<br />
  /// Get or set the critical address information when the protocol is upgraded
  /// </summary>
  public int CriticalAddress { get; set; }

  /// <inheritdoc />
  public override string ToString() => this.AddressStart.ToString();

  /// <summary>
  /// 从实际的信捷PLC的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual XinJE address
  /// </summary>
  /// <param name="address">信捷的地址数据信息</param>
  /// <param name="length">读取的长度信息</param>
  /// <param name="defaultStation">默认的站号信息</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<XinJEAddress> ParseFrom(
    string address,
    ushort length,
    byte defaultStation)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, defaultStation);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<XinJEAddress>((OperateResult) from);
    from.Content.Length = length;
    return OperateResult.CreateSuccessResult<XinJEAddress>(from.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Address.XinJEAddress.ParseFrom(System.String,System.UInt16,System.Byte)" />
  public static OperateResult<XinJEAddress> ParseFrom(string address, byte defaultStation)
  {
    try
    {
      byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) defaultStation);
      if (address.StartsWith("HSCD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 139, int.Parse(address.Substring(4)), int.MaxValue, parameter));
      if (address.StartsWith("ETD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 133, int.Parse(address.Substring(3)), 0, parameter));
      if (address.StartsWith("HSD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 140, int.Parse(address.Substring(3)), 1024 /*0x0400*/, parameter));
      if (address.StartsWith("HTD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 137, int.Parse(address.Substring(3)), 1024 /*0x0400*/, parameter));
      if (address.StartsWith("HCD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 138, int.Parse(address.Substring(3)), 1024 /*0x0400*/, parameter));
      if (address.StartsWith("SFD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 142, int.Parse(address.Substring(3)), 4096 /*0x1000*/, parameter));
      if (address.StartsWith("HSC"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 12, int.Parse(address.Substring(3)), int.MaxValue, parameter));
      if (address.StartsWith("SD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 131, int.Parse(address.Substring(2)), 4096 /*0x1000*/, parameter));
      if (address.StartsWith("TD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 129, int.Parse(address.Substring(2)), 4096 /*0x1000*/, parameter));
      if (address.StartsWith("CD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 130, int.Parse(address.Substring(2)), 4096 /*0x1000*/, parameter));
      if (address.StartsWith("HD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 136, int.Parse(address.Substring(2)), 6144, parameter));
      if (address.StartsWith("FD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 141, int.Parse(address.Substring(2)), 8192 /*0x2000*/, parameter));
      if (address.StartsWith("ID"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 134, int.Parse(address.Substring(2)), 0, parameter));
      if (address.StartsWith("QD"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 135, int.Parse(address.Substring(2)), 0, parameter));
      if (address.StartsWith("SM"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 13, int.Parse(address.Substring(2)), 4096 /*0x1000*/, parameter));
      if (address.StartsWith("ET"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 7, int.Parse(address.Substring(2)), 0, parameter));
      if (address.StartsWith("HM"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 8, int.Parse(address.Substring(2)), 6144, parameter));
      if (address.StartsWith("HS"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 9, int.Parse(address.Substring(2)), int.MaxValue, parameter));
      if (address.StartsWith("HT"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 10, int.Parse(address.Substring(2)), 1024 /*0x0400*/, parameter));
      if (address.StartsWith("HC"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 11, int.Parse(address.Substring(2)), 1024 /*0x0400*/, parameter));
      if (address.StartsWith("D"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 128 /*0x80*/, int.Parse(address.Substring(1)), 20480 /*0x5000*/, parameter));
      if (address.StartsWith("M"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 3, int.Parse(address.Substring(1)), 20480 /*0x5000*/, parameter));
      if (address.StartsWith("T"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 5, int.Parse(address.Substring(1)), 4096 /*0x1000*/, parameter));
      if (address.StartsWith("C"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 6, int.Parse(address.Substring(1)), 4096 /*0x1000*/, parameter));
      if (address.StartsWith("Y"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 2, Convert.ToInt32(address.Substring(1), 8), int.MaxValue, parameter));
      if (address.StartsWith("X"))
        return OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 1, Convert.ToInt32(address.Substring(1), 8), int.MaxValue, parameter));
      return address.StartsWith("S") ? OperateResult.CreateSuccessResult<XinJEAddress>(new XinJEAddress((byte) 4, int.Parse(address.Substring(1)), 8000, parameter)) : throw new Exception(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<XinJEAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }
}
