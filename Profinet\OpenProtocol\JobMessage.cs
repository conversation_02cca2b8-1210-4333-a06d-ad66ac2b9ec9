﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.JobMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>Job message</summary>
public class JobMessage
{
  private OpenProtocolNet openProtocol;

  /// <summary>指定Open协议实例化一个任务消息对象</summary>
  /// <param name="openProtocol">连接通道</param>
  public JobMessage(OpenProtocolNet openProtocol) => this.openProtocol = openProtocol;

  /// <summary>
  /// This is a request for a transmission of all the valid Job IDs of the controller. The result of this command is a transmission of all the valid Job IDs.
  /// </summary>
  /// <param name="revision">Revision</param>
  /// <returns>任务ID的列表信息</returns>
  public OperateResult<int[]> JobIDUpload(int revision = 1)
  {
    OperateResult<string> result = this.openProtocol.ReadCustomer(30, revision, -1, -1, (List<string>) null);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int[]>((OperateResult) result) : this.PraseMID0031(result.Content);
  }

  /// <summary>
  /// Request to upload the data for a specific Job from the controller.
  /// </summary>
  /// <param name="id">job id</param>
  /// <returns>任务数据的结果对象</returns>
  public OperateResult<JobData> JobDataUpload(int id)
  {
    OperateResult<string> result = this.openProtocol.ReadCustomer(32 /*0x20*/, 1, -1, -1, new List<string>()
    {
      id.ToString("D2")
    });
    return !result.IsSuccess ? OperateResult.CreateFailedResult<JobData>((OperateResult) result) : this.PraseMID0033(result.Content);
  }

  /// <summary>
  /// A subscription for the Job info. MID 0035 Job info is sent to the integrator when a new Job is selected and after each tightening performed during the Job.
  /// </summary>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult JobInfoSubscribe()
  {
    return (OperateResult) this.openProtocol.ReadCustomer(34, 1, -1, -1, (List<string>) null);
  }

  /// <summary>Reset the subscription for a Job info message.</summary>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult JobInfoUnsubscribe()
  {
    return (OperateResult) this.openProtocol.ReadCustomer(37, 1, -1, -1, (List<string>) null);
  }

  /// <summary>
  /// Message to select Job. If the requested ID is not present in the controller, then the command will not be performed.
  /// </summary>
  /// <param name="id">Job ID</param>
  /// <param name="revision">Revision</param>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult SelectJob(int id, int revision = 1)
  {
    return (OperateResult) this.openProtocol.ReadCustomer(38, revision, -1, -1, new List<string>()
    {
      revision == 1 ? id.ToString("D2") : id.ToString("D4")
    });
  }

  /// <summary>Job restart message.</summary>
  /// <param name="id">Job ID</param>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult JobRestart(int id)
  {
    return (OperateResult) this.openProtocol.ReadCustomer(39, 1, -1, -1, new List<string>()
    {
      id.ToString("D2")
    });
  }

  private OperateResult<int[]> PraseMID0031(string reply)
  {
    try
    {
      int length = Convert.ToInt32(reply.Substring(8, 3)) == 1 ? 2 : 4;
      int int32 = Convert.ToInt32(reply.Substring(20, length));
      int[] numArray = new int[int32];
      for (int index = 0; index < int32; ++index)
        numArray[index] = Convert.ToInt32(reply.Substring(20 + length + index * length, length));
      return OperateResult.CreateSuccessResult<int[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<int[]>($"MID0031 prase failed: {ex.Message}{Environment.NewLine}Source: {reply}");
    }
  }

  private OperateResult<JobData> PraseMID0033(string reply)
  {
    try
    {
      JobData jobData = new JobData();
      jobData.JobID = Convert.ToInt32(reply.Substring(22, 2));
      jobData.JobName = reply.Substring(26, 25).Trim();
      jobData.ForcedOrder = Convert.ToInt32(reply.Substring(53, 1));
      jobData.MaxTimeForFirstTightening = Convert.ToInt32(reply.Substring(56, 4));
      jobData.MaxTimeToCompleteJob = Convert.ToInt32(reply.Substring(62, 5));
      jobData.JobBatchMode = Convert.ToInt32(reply.Substring(69, 1));
      jobData.LockAtJobDone = reply[72] == '1';
      jobData.UseLineControl = reply[75] == '1';
      jobData.RepeatJob = reply[78] == '1';
      jobData.ToolLoosening = Convert.ToInt32(reply.Substring(81, 1));
      jobData.Reserved = Convert.ToInt32(reply.Substring(86, 1));
      jobData.JobList = new List<JobItem>();
      int int32 = Convert.ToInt32(reply.Substring(89, 2));
      for (int index = 0; index < int32; ++index)
        jobData.JobList.Add(new JobItem(reply.Substring(92 + index * 12, 11)));
      return OperateResult.CreateSuccessResult<JobData>(jobData);
    }
    catch (Exception ex)
    {
      return new OperateResult<JobData>($"MID0033 prase failed: {ex.Message}{Environment.NewLine}Source: {reply}");
    }
  }
}
