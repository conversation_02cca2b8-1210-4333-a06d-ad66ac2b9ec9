﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.ToyoPucMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>丰田工机的PLC的协议消息</summary>
public class ToyoPucMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 4;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    try
    {
      byte[] headBytes = this.HeadBytes;
      return headBytes != null && headBytes.Length >= 4 ? (int) BitConverter.ToUInt16(this.HeadBytes, 2) : 0;
    }
    catch
    {
      return 0;
    }
  }
}
