﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.FANUC.FanucInterfaceNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using Newtonsoft.Json;
using System;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Robot.FANUC;

/// <summary>
/// Fanuc机器人的PC Interface实现，在R-30iB mate plus型号上测试通过，支持读写任意的数据，写入操作务必谨慎调用，写入数据不当造成生命财产损失，作者概不负责。读写任意的地址见api文档信息<br />
/// The Fanuc robot's PC Interface implementation has been tested on R-30iB mate plus models. It supports reading and writing arbitrary data. The writing operation must be called carefully.
/// Improper writing of data will cause loss of life and property. The author is not responsible. Read and write arbitrary addresses see api documentation information
/// </summary>
/// <remarks>
/// 注意：如果再读取机器人的数据时，发生了GB2312编码获取的异常的时候(通常是基于.net core的项目会报错)，使用如下的方法进行解决<br />
/// 1. 从nuget安装组件 <b>System.Text.Encoding.CodePages</b><br />
/// 2. 刚进入系统的时候，调用一行代码： System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);<br />
/// Note: If you read the data of the robot again, when an exception occurs in the GB2312 code acquisition (usually a project based on .net core will report an error), use the following method to solve it.<br />
/// 1. Install the component <b>System.Text.Encoding.CodePages</b> from nuget<br />
/// 2. When you first enter the system, call a line of code: System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);<br />
/// </remarks>
/// <example>
/// 我们看看实际的地址支持的情况，如果使用绝对地址进行访问的话，支持的地址格式如下：
/// <list type="table">
///   <listheader>
///     <term>地址名称</term>
///     <term>地址代号</term>
///     <term>示例</term>
///     <term>地址进制</term>
///     <term>字操作</term>
///     <term>位操作</term>
///     <term>备注</term>
///   </listheader>
///   <item>
///     <term>数据寄存器</term>
///     <term>D</term>
///     <term>D100,D200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>×</term>
///     <term></term>
///   </item>
///   <item>
///     <term>R寄存器</term>
///     <term>R</term>
///     <term>R1-R10</term>
///     <term>10</term>
///     <term>√</term>
///     <term>×</term>
///     <term>R1-R5为int类型，R6-R10为float类型，本质还是数据寄存器</term>
///   </item>
///   <item>
///     <term>输入寄存器</term>
///     <term>AI</term>
///     <term>AI100,AI200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>×</term>
///     <term></term>
///   </item>
///   <item>
///     <term>输出寄存器</term>
///     <term>AQ</term>
///     <term>AQ100,Q200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>×</term>
///     <term></term>
///   </item>
///   <item>
///     <term>输入继电器</term>
///     <term>I</term>
///     <term>I100,I200</term>
///     <term>10</term>
///     <term>×</term>
///     <term>√</term>
///     <term></term>
///   </item>
///   <item>
///     <term>输出继电器</term>
///     <term>Q</term>
///     <term>Q100,Q200</term>
///     <term>10</term>
///     <term>×</term>
///     <term>√</term>
///     <term></term>
///   </item>
///   <item>
///     <term>中间继电器</term>
///     <term>M</term>
///     <term>M100,M200</term>
///     <term>10</term>
///     <term>×</term>
///     <term>√</term>
///     <term></term>
///   </item>
/// </list>
/// 我们先来看看简单的情况
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Robot\FANUC\FanucInterfaceNetSample.cs" region="Sample1" title="简单的读取" />
/// 读取fanuc部分数据
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Robot\FANUC\FanucInterfaceNetSample.cs" region="Sample2" title="属性读取" />
/// 最后是比较高级的任意数据读写
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Robot\FANUC\FanucInterfaceNetSample.cs" region="Sample3" title="复杂读取" />
/// </example>
public class FanucInterfaceNet : DeviceTcpNet, IRobotNet, IReadWriteNet
{
  private FanucData fanucDataRetain = (FanucData) null;
  private DateTime fanucDataRefreshTime = DateTime.Now.AddSeconds(-10.0);
  private PropertyInfo[] fanucDataPropertyInfo = typeof (FanucData).GetProperties();
  private byte[] connect_req = new byte[56];
  private byte[] session_req = new byte[56]
  {
    (byte) 8,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 192 /*0xC0*/,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 16 /*0x10*/,
    (byte) 14,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 1,
    (byte) 79,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0
  };

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public FanucInterfaceNet()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.StringEncoding = Encoding.Default;
  }

  /// <summary>
  /// 指定ip及端口来实例化一个默认的对象，端口默认60008<br />
  /// Specify the IP and port to instantiate a default object, the port defaults to 60008
  /// </summary>
  /// <param name="ipAddress">ip地址</param>
  /// <param name="port">端口号</param>
  public FanucInterfaceNet(string ipAddress, int port = 60008)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new FanucRobotMessage();

  /// <summary>
  /// 获取或设置当前客户端的ID信息，默认为1024<br />
  /// Gets or sets the ID information of the current client. The default is 1024.
  /// </summary>
  public int ClientId { get; private set; } = 1024 /*0x0400*/;

  /// <summary>
  /// 获取或设置缓存的Fanuc数据的有效时间，对<see cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadString(System.String)" />方法有效，默认为100，单位毫秒。也即是在100ms内频繁读取机器人的属性数据的时候，优先读取缓存值，提高读取效率。<br />
  /// Gets or sets the valid time of the cached Fanuc data. It is valid for the <see cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadString(System.String)" /> method. The default is 100, in milliseconds.
  /// That is, when the attribute data of the robot is frequently read within 100ms, the cache value is preferentially read to improve the reading efficiency.
  /// </summary>
  public int FanucDataRetainTime { get; set; } = 100;

  /// <summary>
  /// 获取或设置解析当前的字符串数据时使用的编码信息，默认使用 Encoding.Default 编码。<br />
  /// Gets or sets the encoding information used when parsing the current string data, using Encoding.Default encoding by default.
  /// </summary>
  public Encoding StringEncoding { get; set; }

  private OperateResult ReadCommandFromRobot(CommunicationPipe pipe, string[] cmds)
  {
    for (int index = 0; index < cmds.Length; ++index)
    {
      byte[] bytes = Encoding.ASCII.GetBytes(cmds[index]);
      OperateResult<byte[]> operateResult = this.ReadFromCoreServer(pipe, FanucHelper.BuildWriteData((byte) 56, (ushort) 1, bytes, bytes.Length), true, true);
      if (!operateResult.IsSuccess)
        return (OperateResult) operateResult;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    BitConverter.GetBytes(this.ClientId).CopyTo((Array) this.connect_req, 1);
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(this.CommunicationPipe, this.connect_req, true, true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.CommunicationPipe, this.session_req, true, true);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : this.ReadCommandFromRobot(this.CommunicationPipe, FanucHelper.GetFanucCmds());
  }

  private async Task<OperateResult> ReadCommandFromRobotAsync(CommunicationPipe pipe, string[] cmds)
  {
    for (int i = 0; i < cmds.Length; ++i)
    {
      byte[] buffer = Encoding.ASCII.GetBytes(cmds[i]);
      OperateResult<byte[]> write = await this.ReadFromCoreServerAsync(pipe, FanucHelper.BuildWriteData((byte) 56, (ushort) 1, buffer, buffer.Length), true, true);
      if (!write.IsSuccess)
        return (OperateResult) write;
      buffer = (byte[]) null;
      write = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    BitConverter.GetBytes(this.ClientId).CopyTo((Array) this.connect_req, 1);
    OperateResult<byte[]> receive = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.connect_req, true, true).ConfigureAwait(false);
    if (!receive.IsSuccess)
      return (OperateResult) receive;
    receive = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.session_req, true, true).ConfigureAwait(false);
    if (!receive.IsSuccess)
      return (OperateResult) receive;
    OperateResult operateResult = await this.ReadCommandFromRobotAsync(this.CommunicationPipe, FanucHelper.GetFanucCmds()).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.IRobotNet.Read(System.String)" />
  [HslMqttApi(ApiTopic = "ReadRobotByte", Description = "Read the robot's original byte data information according to the address")]
  public OperateResult<byte[]> Read(string address)
  {
    return this.Read((byte) 8, (ushort) 1, (ushort) 6130);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.IRobotNet.ReadString(System.String)" />
  [HslMqttApi(ApiTopic = "ReadRobotString", Description = "Read the string data information of the robot based on the address")]
  public OperateResult<string> ReadString(string address)
  {
    if (string.IsNullOrEmpty(address))
    {
      OperateResult<FanucData> result = this.ReadFanucData();
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) result);
      this.fanucDataRetain = result.Content;
      this.fanucDataRefreshTime = DateTime.Now;
      return OperateResult.CreateSuccessResult<string>(JsonConvert.SerializeObject((object) result.Content, Formatting.Indented));
    }
    if ((DateTime.Now - this.fanucDataRefreshTime).TotalMilliseconds > (double) this.FanucDataRetainTime || this.fanucDataRetain == null)
    {
      OperateResult<FanucData> result = this.ReadFanucData();
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) result);
      this.fanucDataRetain = result.Content;
      this.fanucDataRefreshTime = DateTime.Now;
    }
    foreach (PropertyInfo propertyInfo in this.fanucDataPropertyInfo)
    {
      if (propertyInfo.Name == address)
        return OperateResult.CreateSuccessResult<string>(JsonConvert.SerializeObject(propertyInfo.GetValue((object) this.fanucDataRetain, (object[]) null), Formatting.Indented));
    }
    return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.Read(System.String)" />
  public async Task<OperateResult<byte[]>> ReadAsync(string address)
  {
    OperateResult<byte[]> operateResult = await this.ReadAsync((byte) 8, (ushort) 1, (ushort) 6130);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadString(System.String)" />
  public async Task<OperateResult<string>> ReadStringAsync(string address)
  {
    if (string.IsNullOrEmpty(address))
    {
      OperateResult<FanucData> read = await this.ReadFanucDataAsync();
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) read);
      this.fanucDataRetain = read.Content;
      this.fanucDataRefreshTime = DateTime.Now;
      return OperateResult.CreateSuccessResult<string>(JsonConvert.SerializeObject((object) read.Content, Formatting.Indented));
    }
    if ((DateTime.Now - this.fanucDataRefreshTime).TotalMilliseconds > (double) this.FanucDataRetainTime || this.fanucDataRetain == null)
    {
      OperateResult<FanucData> read = await this.ReadFanucDataAsync();
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) read);
      this.fanucDataRetain = read.Content;
      this.fanucDataRefreshTime = DateTime.Now;
      read = (OperateResult<FanucData>) null;
    }
    PropertyInfo[] propertyInfoArray = this.fanucDataPropertyInfo;
    for (int index = 0; index < propertyInfoArray.Length; ++index)
    {
      PropertyInfo item = propertyInfoArray[index];
      if (item.Name == address)
        return OperateResult.CreateSuccessResult<string>(JsonConvert.SerializeObject(item.GetValue((object) this.fanucDataRetain, (object[]) null), Formatting.Indented));
      item = (PropertyInfo) null;
    }
    propertyInfoArray = (PropertyInfo[]) null;
    return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
  }

  /// <summary>
  /// 按照字为单位批量读取设备的原始数据，需要指定地址及长度，地址示例：D1，AI1，AQ1，共计3个区的数据，注意地址的起始为1<br />
  /// Read the raw data of the device in batches in units of words. You need to specify the address and length. Example addresses: D1, AI1, AQ1, a total of 3 areas of data. Note that the start of the address is 1.
  /// </summary>
  /// <remarks>地址也支持直接使用 GO100, GI100</remarks>
  /// <param name="address">起始地址，地址示例：D1，AI1，AQ1，共计3个区的数据，注意起始的起始为1</param>
  /// <param name="length">读取的长度，字为单位</param>
  /// <returns>返回的数据信息结果</returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte, ushort> result = FanucHelper.AnalysisFanucAddress(address, false);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    return result.Content1 == (byte) 8 || result.Content1 == (byte) 10 || result.Content1 == (byte) 12 ? this.Read(result.Content1, result.Content2, length) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType + ", Current address not support word read/write");
  }

  /// <summary>
  /// 写入原始的byte数组数据到指定的地址，返回是否写入成功，地址示例：D1，AI1，AQ1，共计3个区的数据，注意起始的起始为1<br />
  /// Write the original byte array data to the specified address, and return whether the write was successful. Example addresses: D1, AI1, AQ1, a total of 3 areas of data. Note that the start of the address is 1.
  /// </summary>
  /// <remarks>地址也支持直接使用 GO100, GI100</remarks>
  /// <param name="address">起始地址，地址示例：D1，AI1，AQ1，共计3个区的数据，注意起始的起始为1</param>
  /// <param name="value">写入值</param>
  /// <returns>带有成功标识的结果类对象</returns>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte, ushort> result = FanucHelper.AnalysisFanucAddress(address, false);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    return result.Content1 == (byte) 8 || result.Content1 == (byte) 10 || result.Content1 == (byte) 12 ? this.Write(result.Content1, result.Content2, value) : (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType + ", Current address not support word read/write");
  }

  /// <summary>
  /// 按照位为单位批量读取设备的原始数据，需要指定地址及长度，地址示例：M1，I1，Q1，共计3个区的数据，注意地址的起始为1<br />
  /// Read the raw data of the device in batches in units of boolean. You need to specify the address and length. Example addresses: M1，I1，Q1, a total of 3 areas of data. Note that the start of the address is 1.
  /// </summary>
  /// <remarks>
  /// 地址也支持直接使用 SDO100, SDI100, RDI100, RDO100, UI100, UO100, SI100, SO100
  /// </remarks>
  /// <param name="address">起始地址，地址示例：M1，I1，Q1，共计3个区的数据，注意地址的起始为1</param>
  /// <param name="length">读取的长度，位为单位</param>
  /// <returns>返回的数据信息结果</returns>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<byte, ushort> result = FanucHelper.AnalysisFanucAddress(address, true);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    return result.Content1 == (byte) 70 || result.Content1 == (byte) 72 || result.Content1 == (byte) 76 ? this.ReadBool(result.Content1, result.Content2, length) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType + ", Current address not support bool read/write");
  }

  /// <summary>
  /// 批量写入<see cref="T:System.Boolean" />数组数据，返回是否写入成功，需要指定起始地址，地址示例：M1，I1，Q1，共计3个区的数据，注意地址的起始为1<br />
  /// Write <see cref="T:System.Boolean" /> array data in batches. If the write success is returned, you need to specify the starting address. Example address: M1, I1, Q1, a total of 3 areas of data. Note that the starting address is 1.
  /// </summary>
  /// <remarks>
  /// 地址也支持直接使用 SDO100, SDI100, RDI100, RDO100, UI100, UO100, SI100, SO100
  /// </remarks>
  /// <param name="address">起始地址，地址示例：M1，I1，Q1，共计3个区的数据，注意地址的起始为1</param>
  /// <param name="value">等待写入的数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<byte, ushort> operateResult = FanucHelper.AnalysisFanucAddress(address, true);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    return operateResult.Content1 == (byte) 70 || operateResult.Content1 == (byte) 72 || operateResult.Content1 == (byte) 76 ? this.WriteBool(operateResult.Content1, operateResult.Content2, value) : new OperateResult(StringResources.Language.NotSupportedDataType + ", Current address not support bool read/write");
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte, ushort> analysis = FanucHelper.AnalysisFanucAddress(address, false);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    if (analysis.Content1 != (byte) 8 && analysis.Content1 != (byte) 10 && analysis.Content1 != (byte) 12)
      return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType + ", Current address not support word read/write");
    OperateResult<byte[]> operateResult = await this.ReadAsync(analysis.Content1, analysis.Content2, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte, ushort> analysis = FanucHelper.AnalysisFanucAddress(address, false);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    if (analysis.Content1 != (byte) 8 && analysis.Content1 != (byte) 10 && analysis.Content1 != (byte) 12)
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType + ", Current address not support word read/write");
    OperateResult operateResult = await this.WriteAsync(analysis.Content1, analysis.Content2, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<byte, ushort> analysis = FanucHelper.AnalysisFanucAddress(address, true);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) analysis);
    if (analysis.Content1 != (byte) 70 && analysis.Content1 != (byte) 72 && analysis.Content1 != (byte) 76)
      return new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType + ", Current address not support bool read/write");
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync(analysis.Content1, analysis.Content2, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult<byte, ushort> analysis = FanucHelper.AnalysisFanucAddress(address, true);
    if (!analysis.IsSuccess)
      return (OperateResult) analysis;
    if (analysis.Content1 != (byte) 70 && analysis.Content1 != (byte) 72 && analysis.Content1 != (byte) 76)
      return new OperateResult(StringResources.Language.NotSupportedDataType + ", Current address not support bool read/write");
    OperateResult operateResult = await this.WriteBoolAsync(analysis.Content1, analysis.Content2, value);
    return operateResult;
  }

  /// <summary>
  /// 按照字为单位批量读取设备的原始数据，需要指定数据块地址，偏移地址及长度，主要针对08, 10, 12的数据块，注意地址的起始为1<br />
  /// Read the raw data of the device in batches in units of words. You need to specify the data block address, offset address, and length. It is mainly for data blocks of 08, 10, and 12. Note that the start of the address is 1.
  /// </summary>
  /// <param name="select">数据块信息</param>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取的长度，字为单位</param>
  public OperateResult<byte[]> Read(byte select, ushort address, ushort length)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(FanucHelper.BulidReadData(select, address, length));
    if (!operateResult.IsSuccess)
      return operateResult;
    if (operateResult.Content[31 /*0x1F*/] == (byte) 148)
      return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.ArrayRemoveBegin<byte>(operateResult.Content, 56));
    return operateResult.Content[31 /*0x1F*/] == (byte) 212 ? OperateResult.CreateSuccessResult<byte[]>(SoftBasic.ArraySelectMiddle<byte>(operateResult.Content, 44, (int) length * 2)) : new OperateResult<byte[]>((int) operateResult.Content[31 /*0x1F*/], "Error");
  }

  /// <summary>
  /// 写入原始的byte数组数据到指定的地址，返回是否写入成功，，需要指定数据块地址，偏移地址，主要针对08, 10, 12的数据块，注意起始的起始为1<br />
  /// Write the original byte array data to the specified address, and return whether the writing is successful. You need to specify the data block address and offset address,
  /// which are mainly for the data blocks of 08, 10, and 12. Note that the start of the start is 1.
  /// </summary>
  /// <param name="select">数据块信息</param>
  /// <param name="address">偏移地址</param>
  /// <param name="value">原始数据内容</param>
  public OperateResult Write(byte select, ushort address, byte[] value)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(FanucHelper.BuildWriteData(select, address, value, value.Length / 2));
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    return operateResult.Content[31 /*0x1F*/] == (byte) 212 ? OperateResult.CreateSuccessResult() : (OperateResult) new OperateResult<byte[]>((int) operateResult.Content[31 /*0x1F*/], "Error");
  }

  /// <summary>
  /// 按照位为单位批量读取设备的原始数据，需要指定数据块地址，偏移地址及长度，主要针对70, 72, 76的数据块，注意地址的起始为1<br />
  /// </summary>
  /// <param name="select">数据块信息</param>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取的长度，字为单位</param>
  public OperateResult<bool[]> ReadBool(byte select, ushort address, ushort length)
  {
    int num = (int) address - 1 - ((int) address - 1) % 8 + 1;
    int length1 = ((((int) address + (int) length - 1) % 8 == 0 ? (int) address + (int) length - 1 : ((int) address + (int) length - 1) / 8 * 8 + 8) - num + 1) / 8;
    OperateResult<byte[]> result = this.ReadFromCoreServer(FanucHelper.BulidReadData(select, address, (ushort) (length1 * 8)));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    if (result.Content[31 /*0x1F*/] == (byte) 148)
    {
      bool[] boolArray = SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(result.Content, 56));
      bool[] destinationArray = new bool[(int) length];
      Array.Copy((Array) boolArray, (int) address - num, (Array) destinationArray, 0, (int) length);
      return OperateResult.CreateSuccessResult<bool[]>(destinationArray);
    }
    if (result.Content[31 /*0x1F*/] != (byte) 212)
      return new OperateResult<bool[]>((int) result.Content[31 /*0x1F*/], "Error");
    bool[] boolArray1 = SoftBasic.ByteToBoolArray(SoftBasic.ArraySelectMiddle<byte>(result.Content, 44, length1));
    bool[] destinationArray1 = new bool[(int) length];
    Array.Copy((Array) boolArray1, (int) address - num, (Array) destinationArray1, 0, (int) length);
    return OperateResult.CreateSuccessResult<bool[]>(destinationArray1);
  }

  /// <summary>
  /// 批量写入<see cref="T:System.Boolean" />数组数据，返回是否写入成功，需要指定数据块地址，偏移地址，主要针对70, 72, 76的数据块，注意起始的起始为1
  /// </summary>
  /// <param name="select">数据块信息</param>
  /// <param name="address">偏移地址</param>
  /// <param name="value">原始的数据内容</param>
  /// <returns>是否写入成功</returns>
  public OperateResult WriteBool(byte select, ushort address, bool[] value)
  {
    int num = (int) address - 1 - ((int) address - 1) % 8 + 1;
    bool[] flagArray = new bool[((((int) address + value.Length - 1) % 8 == 0 ? (int) address + value.Length - 1 : ((int) address + value.Length - 1) / 8 * 8 + 8) - num + 1) / 8 * 8];
    Array.Copy((Array) value, 0, (Array) flagArray, (int) address - num, value.Length);
    OperateResult<byte[]> result = this.ReadFromCoreServer(FanucHelper.BuildWriteData(select, address, this.ByteTransform.TransByte(flagArray), value.Length));
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<short[]>((OperateResult) result);
    return result.Content[31 /*0x1F*/] == (byte) 212 ? OperateResult.CreateSuccessResult() : new OperateResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.Read(System.Byte,System.UInt16,System.UInt16)" />
  public async Task<OperateResult<byte[]>> ReadAsync(byte select, ushort address, ushort length)
  {
    byte[] send = FanucHelper.BulidReadData(select, address, length);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(send);
    OperateResult<byte[]> operateResult = read.IsSuccess ? (read.Content[31 /*0x1F*/] != (byte) 148 ? (read.Content[31 /*0x1F*/] != (byte) 212 ? new OperateResult<byte[]>((int) read.Content[31 /*0x1F*/], "Error") : OperateResult.CreateSuccessResult<byte[]>(SoftBasic.ArraySelectMiddle<byte>(read.Content, 44, (int) length * 2))) : OperateResult.CreateSuccessResult<byte[]>(SoftBasic.ArrayRemoveBegin<byte>(read.Content, 56))) : read;
    send = (byte[]) null;
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.Write(System.Byte,System.UInt16,System.Byte[])" />
  public async Task<OperateResult> WriteAsync(byte select, ushort address, byte[] value)
  {
    byte[] send = FanucHelper.BuildWriteData(select, address, value, value.Length / 2);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(send);
    OperateResult operateResult = read.IsSuccess ? (read.Content[31 /*0x1F*/] != (byte) 212 ? (OperateResult) new OperateResult<byte[]>((int) read.Content[31 /*0x1F*/], "Error") : OperateResult.CreateSuccessResult()) : (OperateResult) read;
    send = (byte[]) null;
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadBool(System.Byte,System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadBoolAsync(
    byte select,
    ushort address,
    ushort length)
  {
    int byteStartIndex = (int) address - 1 - ((int) address - 1) % 8 + 1;
    int byteEndIndex = ((int) address + (int) length - 1) % 8 == 0 ? (int) address + (int) length - 1 : ((int) address + (int) length - 1) / 8 * 8 + 8;
    int byteLength = (byteEndIndex - byteStartIndex + 1) / 8;
    byte[] send = FanucHelper.BulidReadData(select, address, (ushort) (byteLength * 8));
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(send);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    if (read.Content[31 /*0x1F*/] == (byte) 148)
    {
      bool[] array = SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(read.Content, 56));
      bool[] buffer = new bool[(int) length];
      Array.Copy((Array) array, (int) address - byteStartIndex, (Array) buffer, 0, (int) length);
      return OperateResult.CreateSuccessResult<bool[]>(buffer);
    }
    if (read.Content[31 /*0x1F*/] != (byte) 212)
      return new OperateResult<bool[]>((int) read.Content[31 /*0x1F*/], "Error");
    bool[] array1 = SoftBasic.ByteToBoolArray(SoftBasic.ArraySelectMiddle<byte>(read.Content, 44, byteLength));
    bool[] buffer1 = new bool[(int) length];
    Array.Copy((Array) array1, (int) address - byteStartIndex, (Array) buffer1, 0, (int) length);
    return OperateResult.CreateSuccessResult<bool[]>(buffer1);
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteBool(System.Byte,System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WriteBoolAsync(byte select, ushort address, bool[] value)
  {
    int byteStartIndex = (int) address - 1 - ((int) address - 1) % 8 + 1;
    int byteEndIndex = ((int) address + value.Length - 1) % 8 == 0 ? (int) address + value.Length - 1 : ((int) address + value.Length - 1) / 8 * 8 + 8;
    int byteLength = (byteEndIndex - byteStartIndex + 1) / 8;
    bool[] buffer = new bool[byteLength * 8];
    Array.Copy((Array) value, 0, (Array) buffer, (int) address - byteStartIndex, value.Length);
    byte[] send = FanucHelper.BuildWriteData(select, address, this.ByteTransform.TransByte(buffer), value.Length);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(send);
    OperateResult operateResult = read.IsSuccess ? (read.Content[31 /*0x1F*/] != (byte) 212 ? new OperateResult() : OperateResult.CreateSuccessResult()) : (OperateResult) OperateResult.CreateFailedResult<short[]>((OperateResult) read);
    buffer = (bool[]) null;
    send = (byte[]) null;
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <summary>
  /// 读取机器人的详细信息，返回解析后的数据类型<br />
  /// Read the details of the robot and return the resolved data type
  /// </summary>
  /// <returns>结果数据信息</returns>
  [HslMqttApi(Description = "Read the details of the robot and return the resolved data type")]
  public OperateResult<FanucData> ReadFanucData()
  {
    OperateResult<byte[]> result = this.Read("");
    return !result.IsSuccess ? OperateResult.CreateFailedResult<FanucData>((OperateResult) result) : FanucData.PraseFrom(result.Content, this.StringEncoding);
  }

  /// <summary>
  /// 读取机器人的SDO信息<br />
  /// Read the SDO information of the robot
  /// </summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取的长度</param>
  /// <returns>结果数据</returns>
  [HslMqttApi(Description = "Read the SDO information of the robot")]
  public OperateResult<bool[]> ReadSDO(ushort address, ushort length)
  {
    return this.ReadBool("SDO" + address.ToString(), length);
  }

  /// <summary>
  /// 写入机器人的SDO信息<br />
  /// Write the SDO information of the robot
  /// </summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "Write the SDO information of the robot")]
  public OperateResult WriteSDO(ushort address, bool[] value)
  {
    return this.Write("SDO" + address.ToString(), value);
  }

  /// <summary>
  /// 读取机器人的SDI信息<br />
  /// Read the SDI information of the robot
  /// </summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果内容</returns>
  [HslMqttApi(Description = "Read the SDI information of the robot")]
  public OperateResult<bool[]> ReadSDI(ushort address, ushort length)
  {
    return this.ReadBool("SDI" + address.ToString(), length);
  }

  /// <summary>
  /// 写入机器人的SDI信息<br />
  /// Write the SDI information of the robot
  /// </summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "Write the SDI information of the robot")]
  public OperateResult WriteSDI(ushort address, bool[] value)
  {
    return this.Write("SDI" + address.ToString(), value);
  }

  /// <summary>读取机器人的RDI信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<bool[]> ReadRDI(ushort address, ushort length)
  {
    return this.ReadBool("RDI" + address.ToString(), length);
  }

  /// <summary>写入机器人的RDI信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WriteRDI(ushort address, bool[] value)
  {
    return this.Write("RDI" + address.ToString(), value);
  }

  /// <summary>读取机器人的UI信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<bool[]> ReadUI(ushort address, ushort length)
  {
    return this.ReadBool("UI" + address.ToString(), length);
  }

  /// <summary>读取机器人的UO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<bool[]> ReadUO(ushort address, ushort length)
  {
    return this.ReadBool("UO" + address.ToString(), length);
  }

  /// <summary>写入机器人的UO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WriteUO(ushort address, bool[] value)
  {
    return this.Write("UO" + address.ToString(), value);
  }

  /// <summary>读取机器人的SI信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<bool[]> ReadSI(ushort address, ushort length)
  {
    return this.ReadBool("SI" + address.ToString(), length);
  }

  /// <summary>读取机器人的SO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<bool[]> ReadSO(ushort address, ushort length)
  {
    return this.ReadBool("SO" + address.ToString(), length);
  }

  /// <summary>写入机器人的SO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WriteSO(ushort address, bool[] value)
  {
    return this.Write("SO" + address.ToString(), value);
  }

  /// <summary>读取机器人的GI信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<ushort[]> ReadGI(ushort address, ushort length)
  {
    return this.ReadUInt16("GI" + address.ToString(), length);
  }

  /// <summary>写入机器人的GI信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WriteGI(ushort address, ushort[] value)
  {
    return this.Write("GI" + address.ToString(), value);
  }

  /// <summary>读取机器人的GO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<ushort[]> ReadGO(ushort address, ushort length)
  {
    return this.ReadUInt16("GO" + address.ToString(), length);
  }

  /// <summary>写入机器人的GO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>写入结果</returns>
  [HslMqttApi]
  public OperateResult WriteGO(ushort address, ushort[] value)
  {
    return this.Write("GO" + address.ToString(), value);
  }

  /// <summary>读取机器人的PMCR2信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<bool[]> ReadPMCR2(ushort address, ushort length)
  {
    return this.ReadBool((byte) 76, address, length);
  }

  /// <summary>写入机器人的PMCR2信息</summary>
  /// <param name="address">偏移信息</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WritePMCR2(ushort address, bool[] value)
  {
    return this.WriteBool((byte) 76, address, value);
  }

  /// <summary>读取机器人的RDO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">读取长度</param>
  /// <returns>结果信息</returns>
  [HslMqttApi]
  public OperateResult<bool[]> ReadRDO(ushort address, ushort length)
  {
    return this.ReadBool("RDO" + address.ToString(), length);
  }

  /// <summary>写入机器人的RDO信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WriteRDO(ushort address, bool[] value)
  {
    return this.Write("RDO" + address.ToString(), value);
  }

  /// <summary>写入机器人的Rxyzwpr信息，谨慎调用，</summary>
  /// <param name="Address">偏移地址</param>
  /// <param name="Xyzwpr">姿态信息</param>
  /// <param name="Config">设置信息</param>
  /// <param name="UserFrame">参考系</param>
  /// <param name="UserTool">工具</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WriteRXyzwpr(
    ushort Address,
    float[] Xyzwpr,
    short[] Config,
    short UserFrame,
    short UserTool)
  {
    byte[] numArray = new byte[Xyzwpr.Length * 4 + Config.Length * 2 + 2];
    this.ByteTransform.TransByte(Xyzwpr).CopyTo((Array) numArray, 0);
    this.ByteTransform.TransByte(Config).CopyTo((Array) numArray, 36);
    OperateResult operateResult1 = this.Write((byte) 8, Address, numArray);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    if ((short) 0 <= UserFrame && UserFrame <= (short) 15)
    {
      if ((short) 0 <= UserTool && UserTool <= (short) 15)
      {
        OperateResult operateResult2 = this.Write((byte) 8, (ushort) ((uint) Address + 45U), this.ByteTransform.TransByte(new short[2]
        {
          UserFrame,
          UserTool
        }));
        if (!operateResult2.IsSuccess)
          return operateResult2;
      }
      else
      {
        OperateResult operateResult3 = this.Write((byte) 8, (ushort) ((uint) Address + 45U), this.ByteTransform.TransByte(new short[1]
        {
          UserFrame
        }));
        if (!operateResult3.IsSuccess)
          return operateResult3;
      }
    }
    else if ((short) 0 <= UserTool && UserTool <= (short) 15)
    {
      OperateResult operateResult4 = this.Write((byte) 8, (ushort) ((uint) Address + 46U), this.ByteTransform.TransByte(new short[1]
      {
        UserTool
      }));
      if (!operateResult4.IsSuccess)
        return operateResult4;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>写入机器人的Joint信息</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="joint">关节坐标</param>
  /// <param name="UserFrame">参考系</param>
  /// <param name="UserTool">工具</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi]
  public OperateResult WriteRJoint(ushort address, float[] joint, short UserFrame, short UserTool)
  {
    OperateResult operateResult1 = this.Write((byte) 8, (ushort) ((uint) address + 26U), this.ByteTransform.TransByte(joint));
    if (!operateResult1.IsSuccess)
      return operateResult1;
    if ((short) 0 <= UserFrame && UserFrame <= (short) 15)
    {
      if ((short) 0 <= UserTool && UserTool <= (short) 15)
      {
        OperateResult operateResult2 = this.Write((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[3]
        {
          (short) 0,
          UserFrame,
          UserTool
        }));
        if (!operateResult2.IsSuccess)
          return operateResult2;
      }
      else
      {
        OperateResult operateResult3 = this.Write((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[2]
        {
          (short) 0,
          UserFrame
        }));
        if (!operateResult3.IsSuccess)
          return operateResult3;
      }
    }
    else
    {
      OperateResult operateResult4 = this.Write((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[1]));
      if (!operateResult4.IsSuccess)
        return operateResult4;
      if ((short) 0 <= UserTool && UserTool <= (short) 15)
      {
        OperateResult operateResult5 = this.Write((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[2]
        {
          (short) 0,
          UserTool
        }));
        if (!operateResult5.IsSuccess)
          return operateResult5;
      }
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadFanucData" />
  public async Task<OperateResult<FanucData>> ReadFanucDataAsync()
  {
    OperateResult<byte[]> read = await this.ReadAsync("");
    OperateResult<FanucData> operateResult = read.IsSuccess ? FanucData.PraseFrom(read.Content, this.StringEncoding) : OperateResult.CreateFailedResult<FanucData>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadSDO(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadSDOAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("SDO" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteSDO(System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WriteSDOAsync(ushort address, bool[] value)
  {
    OperateResult operateResult = await this.WriteAsync("SDO" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadSDI(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadSDIAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("SDI" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteSDI(System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WriteSDIAsync(ushort address, bool[] value)
  {
    OperateResult operateResult = await this.WriteAsync("SDI" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadRDI(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadRDIAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("RDI" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteRDI(System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WriteRDIAsync(ushort address, bool[] value)
  {
    OperateResult operateResult = await this.WriteAsync("RDI" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadUI(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadUIAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("UI" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadUO(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadUOAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("UO" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteUO(System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WriteUOAsync(ushort address, bool[] value)
  {
    OperateResult operateResult = await this.WriteAsync("UO" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadSI(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadSIAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("SI" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadSO(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadSOAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("SO" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteSO(System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WriteSOAsync(ushort address, bool[] value)
  {
    OperateResult operateResult = await this.WriteAsync("SO" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadGI(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<ushort[]>> ReadGIAsync(ushort address, ushort length)
  {
    OperateResult<ushort[]> operateResult = await this.ReadUInt16Async("GI" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteGI(System.UInt16,System.UInt16[])" />
  public async Task<OperateResult> WriteGIAsync(ushort address, ushort[] value)
  {
    OperateResult operateResult = await this.WriteAsync("GI" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadGO(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<ushort[]>> ReadGOAsync(ushort address, ushort length)
  {
    OperateResult<ushort[]> operateResult = await this.ReadUInt16Async("GO" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteGO(System.UInt16,System.UInt16[])" />
  public async Task<OperateResult> WriteGOAsync(ushort address, ushort[] value)
  {
    OperateResult operateResult = await this.WriteAsync("GO" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadPMCR2(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadPMCR2Async(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync((byte) 76, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WritePMCR2(System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WritePMCR2Async(ushort address, bool[] value)
  {
    OperateResult operateResult = await this.WriteBoolAsync((byte) 76, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.ReadRDO(System.UInt16,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadRDOAsync(ushort address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync("RDO" + address.ToString(), length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteRDO(System.UInt16,System.Boolean[])" />
  public async Task<OperateResult> WriteRDOAsync(ushort address, bool[] value)
  {
    OperateResult operateResult = await this.WriteAsync("RDO" + address.ToString(), value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteRXyzwpr(System.UInt16,System.Single[],System.Int16[],System.Int16,System.Int16)" />
  public async Task<OperateResult> WriteRXyzwprAsync(
    ushort Address,
    float[] Xyzwpr,
    short[] Config,
    short UserFrame,
    short UserTool)
  {
    int num = Xyzwpr.Length * 4 + Config.Length * 2 + 2;
    byte[] robotBuffer = new byte[num];
    this.ByteTransform.TransByte(Xyzwpr).CopyTo((Array) robotBuffer, 0);
    this.ByteTransform.TransByte(Config).CopyTo((Array) robotBuffer, 36);
    OperateResult write = await this.WriteAsync((byte) 8, Address, robotBuffer);
    if (!write.IsSuccess)
      return write;
    if ((short) 0 <= UserFrame && UserFrame <= (short) 15)
    {
      if ((short) 0 <= UserTool && UserTool <= (short) 15)
      {
        write = await this.WriteAsync((byte) 8, (ushort) ((uint) Address + 45U), this.ByteTransform.TransByte(new short[2]
        {
          UserFrame,
          UserTool
        }));
        if (!write.IsSuccess)
          return write;
      }
      else
      {
        write = await this.WriteAsync((byte) 8, (ushort) ((uint) Address + 45U), this.ByteTransform.TransByte(new short[1]
        {
          UserFrame
        }));
        if (!write.IsSuccess)
          return write;
      }
    }
    else if ((short) 0 <= UserTool && UserTool <= (short) 15)
    {
      write = await this.WriteAsync((byte) 8, (ushort) ((uint) Address + 46U), this.ByteTransform.TransByte(new short[1]
      {
        UserTool
      }));
      if (!write.IsSuccess)
        return write;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.FANUC.FanucInterfaceNet.WriteRJoint(System.UInt16,System.Single[],System.Int16,System.Int16)" />
  public async Task<OperateResult> WriteRJointAsync(
    ushort address,
    float[] joint,
    short UserFrame,
    short UserTool)
  {
    OperateResult write = await this.WriteAsync((byte) 8, (ushort) ((uint) address + 26U), this.ByteTransform.TransByte(joint));
    if (!write.IsSuccess)
      return write;
    if ((short) 0 <= UserFrame && UserFrame <= (short) 15)
    {
      if ((short) 0 <= UserTool && UserTool <= (short) 15)
      {
        write = await this.WriteAsync((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[3]
        {
          (short) 0,
          UserFrame,
          UserTool
        }));
        if (!write.IsSuccess)
          return write;
      }
      else
      {
        write = await this.WriteAsync((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[2]
        {
          (short) 0,
          UserFrame
        }));
        if (!write.IsSuccess)
          return write;
      }
    }
    else
    {
      write = await this.WriteAsync((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[1]));
      if (!write.IsSuccess)
        return write;
      if ((short) 0 <= UserTool && UserTool <= (short) 15)
      {
        write = await this.WriteAsync((byte) 8, (ushort) ((uint) address + 44U), this.ByteTransform.TransByte(new short[2]
        {
          (short) 0,
          UserTool
        }));
        if (!write.IsSuccess)
          return write;
      }
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override string ToString() => $"FanucInterfaceNet[{this.IpAddress}:{this.Port}]";
}
