﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Device.DeviceServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Threading;

#nullable disable
namespace HslCommunication.Core.Device;

/// <summary>设备服务器类</summary>
public class DeviceServer : DeviceCommunication
{
  private System.Threading.Timer timerHeart;
  private CommunicationServer server;
  private int udpServePort = 0;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public DeviceServer()
  {
    this.ActiveTimeSpan = TimeSpan.FromHours(24.0);
    this.server = new CommunicationServer();
    this.server.OnPipeMessageReceived += new CommunicationServer.PipeMessageReceived(this.Server_OnPipeMessageReceived);
    this.server.LogDebugMessage = (Action<string>) (m => this.LogNet?.WriteDebug(this.ToString(), m));
    this.server.CreateNewMessage = new Func<INetMessage>(((BinaryCommunication) this).GetNewNetMessage);
    this.server.ThreadPoolLoginAfterClientCheck = new Func<PipeSession, IPEndPoint, OperateResult>(this.ThreadPoolLoginAfterClientCheck);
    this.server.CheckSerialDataComplete = new Func<byte[], int, bool>(this.CheckSerialReceiveDataComplete);
    this.timerHeart = new System.Threading.Timer(new TimerCallback(this.ThreadTimerHeartCheck), (object) null, 2000, 10000);
  }

  /// <summary>
  /// 服务器引擎是否启动<br />
  /// Whether the server engine is started
  /// </summary>
  public bool IsStarted { get; private set; }

  /// <summary>
  /// 获取或设置服务器的端口号，如果是设置，需要在服务器启动前设置完成，才能生效。<br />
  /// Gets or sets the port number of the server. If it is set, it needs to be set before the server starts to take effect.
  /// </summary>
  /// <remarks>需要在服务器启动之前设置为有效</remarks>
  public int Port
  {
    get => this.server.Port;
    set => this.server.Port = value;
  }

  /// <summary>
  /// 当服务器同时启动TCP及UDP服务的时候，获取当前的UDP服务的端口号<br />
  /// When the server starts TCP and UDP services at the same time, it obtains the port number of the current UDP service
  /// </summary>
  public int BothModeUdpPort => this.udpServePort;

  /// <summary>
  /// 获取或设置服务器是否支持IPv6的地址协议信息<br />
  /// Get or set whether the server supports IPv6 address protocol information
  /// </summary>
  /// <remarks>
  /// 默认为 <c>False</c>，也就是不启动
  /// </remarks>
  public bool EnableIPv6
  {
    get => this.server.EnableIPv6;
    set => this.server.EnableIPv6 = value;
  }

  /// <summary>
  /// 获取或设置客户端的Socket的心跳时间信息，这个是Socket底层自动实现的心跳包，不基于协议层实现。默认小于0，不开启心跳检测，如果需要开启，设置 60_000 比较合适，单位毫秒<br />
  /// Get or set the heartbeat time information of the Socket of the client. This is the heartbeat packet automatically implemented by the bottom layer of the Socket, not based on the protocol layer.
  /// The default value is less than 0, and heartbeat detection is not enabled. If you need to enable it, it is more appropriate to set 60_000, in milliseconds.
  /// </summary>
  /// <remarks>经测试，在linux上，基于.net core3.1的程序运行时，设置了这个值是无效的。</remarks>
  public int SocketKeepAliveTime
  {
    get => this.server.SocketKeepAliveTime;
    set => this.server.SocketKeepAliveTime = value;
  }

  /// <summary>
  /// 获取或设置当前的服务器是否允许远程客户端进行写入数据操作，默认为<c>True</c><br />
  /// Gets or sets whether the current server allows remote clients to write data, the default is <c>True</c>
  /// </summary>
  /// <remarks>
  /// 如果设置为<c>False</c>，那么所有远程客户端的操作都会失败，直接返回错误码或是关闭连接。
  /// </remarks>
  public bool EnableWrite { get; set; } = true;

  /// <summary>
  /// 获取或设置两次数据交互时的最小时间间隔，默认为24小时。如果超过该设定的时间不进行数据交互，服务器就会强制断开当前的连接操作。<br />
  /// Get or set the minimum time interval between two data interactions, the default is 24 hours.
  /// If the data exchange is not performed for more than the set time, the server will forcibly disconnect the current connection operation.
  /// </summary>
  /// <remarks>
  /// 举例设置为10分钟，ActiveTimeSpan = TimeSpan.FromMinutes( 10 );
  /// </remarks>
  public TimeSpan ActiveTimeSpan { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.CommunicationServer.SerialReceiveAtleastTime" />
  public int SerialReceiveAtleastTime
  {
    get => this.server.SerialReceiveAtleastTime;
    set => this.server.SerialReceiveAtleastTime = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.CommunicationServer.ForceSerialReceiveOnce" />
  public bool ForceSerialReceiveOnce
  {
    get => this.server.ForceSerialReceiveOnce;
    set => this.server.ForceSerialReceiveOnce = value;
  }

  /// <summary>
  /// 获取在线的客户端的数量<br />
  /// Get the number of clients online
  /// </summary>
  public int OnlineCount => this.server.GetPipeSessions().Length;

  /// <summary>
  /// 当前服务器的模式，0：TCP服务器，1：UDP服务器，2：TCP及UDP服务器<br />
  /// Gets whether the current server is a TCP server or a UDP server
  /// </summary>
  public int ServerMode { get; private set; }

  /// <summary>获取当前的核心服务器信息</summary>
  /// <returns>核心服务器</returns>
  public CommunicationServer GetCommunicationServer() => this.server;

  /// <summary>
  /// 当客户端的socket登录的时候额外检查的操作，并返回操作的结果信息。<br />
  /// The operation is additionally checked when the client's socket logs in, and the result information of the operation is returned.
  /// </summary>
  /// <param name="socket">套接字</param>
  /// <param name="endPoint">终结点</param>
  /// <returns>验证的结果</returns>
  protected virtual OperateResult SocketAcceptExtraCheck(Socket socket, IPEndPoint endPoint)
  {
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 服务器启动时额外的初始化信息，可以用于启动一些额外的服务的操作。<br />
  /// The extra initialization information when the server starts can be used to start some additional service operations.
  /// </summary>
  /// <remarks>需要在派生类中重写</remarks>
  protected virtual void StartInitialization()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.ServerStart(System.Int32,System.Boolean)" />
  public virtual void ServerStart(int port, bool modeTcp = true)
  {
    if (this.IsStarted)
      return;
    this.IsStarted = true;
    this.ServerMode = modeTcp ? 0 : 1;
    this.StartInitialization();
    this.server.ServerStart(port, modeTcp);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.ServerStart(System.Int32,System.Int32)" />
  public void ServerStart(int tcpPort, int udpPort)
  {
    if (this.IsStarted)
      return;
    this.IsStarted = true;
    this.ServerMode = 2;
    this.StartInitialization();
    this.server.ServerStart(tcpPort, udpPort);
    this.udpServePort = udpPort;
  }

  /// <summary>
  /// 使用已经配置好的端口启动服务器的引擎，并且使用TCP模式<br />
  /// Use the configured port to start the server's engine
  /// </summary>
  public void ServerStart() => this.ServerStart(this.Port);

  /// <summary>
  /// 服务器关闭的时候需要做的事情<br />
  /// Things to do when the server is down
  /// </summary>
  protected virtual void CloseAction()
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationTcpServer.ServerClose" />
  public virtual void ServerClose()
  {
    if (!this.IsStarted)
      return;
    this.IsStarted = false;
    this.server.ServerClose();
    this.CloseAction();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.CloseSerialSlave" />
  public void CloseSerialSlave() => this.server.CloseSerialSlave();

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationTcpServer.UseSSL(System.Security.Cryptography.X509Certificates.X509Certificate)" />
  /// <param name="cert">证书对象</param>
  public void UseSSL(X509Certificate cert) => this.server.UseSSL(cert);

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationTcpServer.UseSSL(System.String,System.String)" />
  public void UseSSL(string cert, string password = "") => this.server.UseSSL(cert, password);

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationTcpServer.SetTrustedIpAddress(System.Collections.Generic.List{System.String})" />
  public void SetTrustedIpAddress(List<string> clients) => this.server.SetTrustedIpAddress(clients);

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationTcpServer.GetTrustedClients" />
  public string[] GetTrustedClients() => this.server.GetTrustedClients();

  /// <summary>
  /// 将本系统的数据池数据存储到指定的文件<br />
  /// Store the data pool data of this system to the specified file
  /// </summary>
  /// <param name="path">指定文件的路径</param>
  /// <exception cref="T:System.ArgumentException"></exception>
  /// <exception cref="T:System.ArgumentNullException"></exception>
  /// <exception cref="T:System.IO.PathTooLongException"></exception>
  /// <exception cref="T:System.IO.DirectoryNotFoundException"></exception>
  /// <exception cref="T:System.IO.IOException"></exception>
  /// <exception cref="T:System.UnauthorizedAccessException"></exception>
  /// <exception cref="T:System.NotSupportedException"></exception>
  /// <exception cref="T:System.Security.SecurityException"></exception>
  public void SaveDataPool(string path)
  {
    byte[] bytes = this.SaveToBytes();
    System.IO.File.WriteAllBytes(path, bytes);
  }

  /// <summary>
  /// 从文件加载数据池信息<br />
  /// Load datapool information from a file
  /// </summary>
  /// <param name="path">文件路径</param>
  /// <exception cref="T:System.ArgumentException"></exception>
  /// <exception cref="T:System.ArgumentNullException"></exception>
  /// <exception cref="T:System.IO.PathTooLongException"></exception>
  /// <exception cref="T:System.IO.DirectoryNotFoundException"></exception>
  /// <exception cref="T:System.IO.IOException"></exception>
  /// <exception cref="T:System.UnauthorizedAccessException"></exception>
  /// <exception cref="T:System.NotSupportedException"></exception>
  /// <exception cref="T:System.Security.SecurityException"></exception>
  public void LoadDataPool(string path)
  {
    if (!System.IO.File.Exists(path))
      return;
    this.LoadFromBytes(System.IO.File.ReadAllBytes(path));
  }

  /// <summary>
  /// 从字节数据加载数据信息，需要进行重写方法<br />
  /// Loading data information from byte data requires rewriting method
  /// </summary>
  /// <param name="content">字节数据</param>
  protected virtual void LoadFromBytes(byte[] content)
  {
  }

  /// <summary>
  /// 将数据信息存储到字节数组去，需要进行重写方法<br />
  /// To store data information into a byte array, a rewrite method is required
  /// </summary>
  /// <returns>所有的内容</returns>
  protected virtual byte[] SaveToBytes() => new byte[0];

  /// <summary>
  /// 接收到数据的时候就触发的事件，示例详细参考API文档信息<br />
  /// An event that is triggered when data is received
  /// </summary>
  /// <remarks>
  /// 事件共有三个参数，sender指服务器本地的对象，例如 <see cref="T:HslCommunication.ModBus.ModbusTcpServer" /> 对象，source 指会话对象，网口对象为 <see cref="T:HslCommunication.Core.Net.AppSession" />，
  /// 串口为<see cref="T:System.IO.Ports.SerialPort" /> 对象，需要根据实际判断，data 为收到的原始数据 byte[] 对象
  /// </remarks>
  /// <example>
  /// 我们以Modbus的Server为例子，其他的虚拟服务器同理，因为都集成自本服务器对象
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkDataServerBaseSample.cs" region="OnDataReceivedSample" title="数据接收触发的示例" />
  /// </example>
  public event DeviceServer.DataReceivedDelegate OnDataReceived;

  /// <summary>
  /// 触发一个数据接收的事件信息<br />
  /// Event information that triggers a data reception
  /// </summary>
  /// <param name="session">管道会话</param>
  /// <param name="receive">接收数据信息</param>
  protected void RaiseDataReceived(PipeSession session, byte[] receive)
  {
    DeviceServer.DataReceivedDelegate onDataReceived = this.OnDataReceived;
    if (onDataReceived == null)
      return;
    onDataReceived((object) this, session, receive);
  }

  /// <summary>
  /// 数据发送的时候就触发的事件<br />
  /// Events that are triggered when data is sent
  /// </summary>
  public event DeviceServer.DataSendDelegate OnDataSend;

  /// <summary>
  /// 触发一个数据发送的事件信息<br />
  /// Event information that triggers a data transmission
  /// </summary>
  /// <param name="send">数据内容</param>
  protected void RaiseDataSend(byte[] send)
  {
    DeviceServer.DataSendDelegate onDataSend = this.OnDataSend;
    if (onDataSend == null)
      return;
    onDataSend((object) this, send);
  }

  private void Server_OnPipeMessageReceived(PipeSession session, byte[] buffer)
  {
    this.LogRevcMessage(buffer, session);
    OperateResult<byte[]> operateResult;
    try
    {
      operateResult = this.ReadFromCoreServer(session, buffer);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException("ReadFromCoreServer", "Source Data: " + buffer.ToHexString(' '), ex);
      return;
    }
    if (!operateResult.IsSuccess)
    {
      if (operateResult.ErrorCode != int.MinValue)
        this.LogNet?.WriteDebug(this.ToString(), $"<{session.Communication}> {operateResult.Message}");
      if (operateResult.Content != null && operateResult.Content.Length != 0)
        operateResult.IsSuccess = true;
    }
    if (operateResult.IsSuccess && operateResult.Content != null && operateResult.Content.Length != 0)
    {
      session.Communication.Send(operateResult.Content);
      this.RaiseDataSend(operateResult.Content);
      this.LogSendMessage(operateResult.Content, session);
    }
    this.RaiseDataReceived(session, buffer);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkDoubleBase.ReadFromCoreServer(System.Byte[])" />
  protected virtual OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
  }

  /// <summary>
  /// 当客户端登录后，在Ip信息的过滤后，然后触发本方法，进行后续的数据接收，处理，并返回相关的数据信息<br />
  /// When the client logs in, after filtering the IP information, this method is then triggered to perform subsequent data reception,
  /// processing, and return related data information
  /// </summary>
  /// <param name="session">管道信息</param>
  /// <param name="endPoint">终端节点</param>
  protected virtual OperateResult ThreadPoolLoginAfterClientCheck(
    PipeSession session,
    IPEndPoint endPoint)
  {
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.OnDataSend = (DeviceServer.DataSendDelegate) null;
      this.OnDataReceived = (DeviceServer.DataReceivedDelegate) null;
      this.ServerClose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.StartSerialSlave(System.String)" />
  public OperateResult StartSerialSlave(string com) => this.server.StartSerialSlave(com);

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.StartSerialSlave(System.String,System.Int32)" />
  public OperateResult StartSerialSlave(string com, int baudRate)
  {
    return this.server.StartSerialSlave(com, baudRate);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.StartSerialSlave(System.String,System.Int32,System.Int32,System.IO.Ports.Parity,System.IO.Ports.StopBits)" />
  public OperateResult StartSerialSlave(
    string com,
    int baudRate,
    int dataBits,
    Parity parity,
    StopBits stopBits)
  {
    return this.server.StartSerialSlave(com, baudRate, dataBits, parity, stopBits);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.StartSerialSlave(System.Action{System.IO.Ports.SerialPort})" />
  public OperateResult StartSerialSlave(Action<SerialPort> inni)
  {
    return this.server.StartSerialSlave(inni);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.CommunicationServer.CheckSerialReceiveDataComplete(System.Byte[],System.Int32)" />
  protected virtual bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength) => false;

  private void ThreadTimerHeartCheck(object obj) => this.server.RemoveSession(this.ActiveTimeSpan);

  /// <summary>
  /// 当接收到来自客户的数据信息时触发的对象，该数据可能来自tcp或是串口<br />
  /// The object that is triggered when receiving data information from the customer, the data may come from tcp or serial port
  /// </summary>
  /// <param name="sender">触发的服务器对象</param>
  /// <param name="session">消息的来源对象</param>
  /// <param name="data">实际的数据信息</param>
  public delegate void DataReceivedDelegate(object sender, PipeSession session, byte[] data);

  /// <summary>
  /// 数据发送的时候委托<br />
  /// Show DataSend To PLC
  /// </summary>
  /// <param name="sender">数据发送对象</param>
  /// <param name="data">数据内容</param>
  public delegate void DataSendDelegate(object sender, byte[] data);
}
