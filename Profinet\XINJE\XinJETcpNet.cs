﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.XINJE.XinJETcpNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.ModBus;
using HslCommunication.Reflection;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.XINJE;

/// <summary>
/// 信捷PLC的XC,XD,XL系列的网口通讯类，底层使用ModbusTcp协议实现，每个系列支持的地址类型及范围不一样，详细参考Demo程序<br />
/// XC, XD, XL series of Xinje PLC's network port communication class, the bottom layer is realized by ModbusTcp protocol,
/// each series supports different address types and ranges, please refer to the Demo for details
/// </summary>
/// <remarks>
/// 对于XC系列适用于XC1/XC2/XC3/XC5/XCM/XCC系列，线圈支持X,Y,S,M,T,C，寄存器支持D,F,E,T,C<br />
/// 对于XD,XL系列适用于XD1/XD2/XD3/XD5/XDM/XDC/XD5E/XDME/XDH/XL1/XL3/XL5/XL5E/XLME，
/// 线圈支持X,Y,S,M,SM,T,C,ET,SEM,HM,HS,HT,HC,HSC 寄存器支持D,ID,QD,SD,TD,CD,ETD,HD,HSD,HTD,HCD,HSCD,FD,SFD,FS<br />
/// </remarks>
public class XinJETcpNet : ModbusTcpNet
{
  /// <summary>实例化一个默认的对象</summary>
  public XinJETcpNet() => this.Series = XinJESeries.XC;

  /// <summary>通过指定站号，ip地址，端口号来实例化一个新的对象</summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="station">站号信息</param>
  public XinJETcpNet(string ipAddress, int port = 502, byte station = 1)
    : base(ipAddress, port, station)
  {
    this.Series = XinJESeries.XC;
  }

  /// <summary>
  /// 通过指定站号，IP地址，端口以及PLC的系列来实例化一个新的对象<br />
  /// Instantiate a new object by specifying the station number and PLC series
  /// </summary>
  /// <param name="series">PLC的系列</param>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="station">站号信息</param>
  public XinJETcpNet(XinJESeries series, string ipAddress, int port = 502, byte station = 1)
    : base(ipAddress, port, station)
  {
    this.Series = series;
  }

  /// <summary>获取或设置当前的信捷PLC的系列，默认XC系列</summary>
  public XinJESeries Series { get; set; }

  /// <inheritdoc />
  public override OperateResult<string> TranslateToModbusAddress(string address, byte modbusCode)
  {
    return XinJEHelper.PraseXinJEAddress(this.Series, address, modbusCode);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return XinJEHelper.Read((IModbus) this, address, length, new Func<string, ushort, OperateResult<byte[]>>(((ModbusTcpNet) this).Read));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return XinJEHelper.Write((IModbus) this, address, value, new Func<string, byte[], OperateResult>(((ModbusTcpNet) this).Write));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt16", "")]
  public override OperateResult Write(string address, short value)
  {
    return XinJEHelper.Write((IModbus) this, address, value, new Func<string, short, OperateResult>(((ModbusTcpNet) this).Write));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt16", "")]
  public override OperateResult Write(string address, ushort value)
  {
    return XinJEHelper.Write((IModbus) this, address, value, new Func<string, ushort, OperateResult>(((ModbusTcpNet) this).Write));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return XinJEHelper.ReadBool((IModbus) this, address, length, new Func<string, ushort, OperateResult<bool[]>>(((ModbusTcpNet) this).ReadBool));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    return XinJEHelper.Write((IModbus) this, address, values, new Func<string, bool[], OperateResult>(((ModbusTcpNet) this).Write));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return XinJEHelper.Write((IModbus) this, address, value, new Func<string, bool, OperateResult>(((ModbusTcpNet) this).Write));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address1, ushort length1)
  {
    OperateResult<byte[]> operateResult = await XinJEHelper.ReadAsync((IModbus) this, address1, length1, (Func<string, ushort, Task<OperateResult<byte[]>>>) ([DebuggerHidden] (address2, length2) => base.ReadAsync(address2, length2)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address1, byte[] value1)
  {
    OperateResult operateResult = await XinJEHelper.WriteAsync((IModbus) this, address1, value1, (Func<string, byte[], Task<OperateResult>>) ([DebuggerHidden] (address2, value2) => base.WriteAsync(address2, value2)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address1, short value1)
  {
    OperateResult operateResult = await XinJEHelper.WriteAsync((IModbus) this, address1, value1, (Func<string, short, Task<OperateResult>>) ([DebuggerHidden] (address2, value2) => base.WriteAsync(address2, value2)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address1, ushort value1)
  {
    OperateResult operateResult = await XinJEHelper.WriteAsync((IModbus) this, address1, value1, (Func<string, ushort, Task<OperateResult>>) ([DebuggerHidden] (address2, value2) => base.WriteAsync(address2, value2)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address1, ushort length1)
  {
    OperateResult<bool[]> operateResult = await XinJEHelper.ReadBoolAsync((IModbus) this, address1, length1, (Func<string, ushort, Task<OperateResult<bool[]>>>) ([DebuggerHidden] (address2, length2) => base.ReadBoolAsync(address2, length2)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address1, bool[] values1)
  {
    OperateResult operateResult = await XinJEHelper.WriteAsync((IModbus) this, address1, values1, (Func<string, bool[], Task<OperateResult>>) ([DebuggerHidden] (address2, values2) => base.WriteAsync(address2, values2)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address1, bool value1)
  {
    OperateResult operateResult = await XinJEHelper.WriteAsync((IModbus) this, address1, value1, (Func<string, bool, Task<OperateResult>>) ([DebuggerHidden] (address2, value2) => base.WriteAsync(address2, value2)));
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"XinJETcpNet<{this.Series}>[{this.IpAddress}:{this.Port}]";
}
