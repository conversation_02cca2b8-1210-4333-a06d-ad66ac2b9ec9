﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensS7Net
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Siemens.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>
/// 一个西门子的客户端类，使用S7协议来进行数据交互，对于s300,s400需要关注<see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.Slot" />和<see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.Rack" />的设置值，
/// 对于s200，需要关注<see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.LocalTSAP" />和<see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.DestTSAP" />的设置值，详细参考demo的设置。 <br />
/// A Siemens client class uses the S7 protocol for data exchange. For s300 and s400,
/// you need to pay attention to the setting values of <see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.Slot" /> and <see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.Rack" />. For s200,
/// you need to pay attention to <see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.Slot" /> and <see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.Rack" />. See cref="LocalTSAP"/&gt; and <see cref="P:HslCommunication.Profinet.Siemens.SiemensS7Net.DestTSAP" /> settings,
/// please refer to the demo settings for details.
/// </summary>
/// <remarks>
/// 暂时不支持bool[]的批量写入操作，请使用 Write(string, byte[]) 替换。<br />
/// <note type="important">对于200smartPLC的V区，就是DB1.X，例如，V100=DB1.100，当然了你也可以输入V100</note><br />
/// 如果读取PLC的字符串string数据，可以使用 <see cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadString(System.String)" />
/// </remarks>
/// <example>
/// <note type="important">对于200smartPLC的V区，就是DB1.X，例如，V100=DB1.100</note>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="Usage" title="简单的短连接使用" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="Usage2" title="简单的长连接使用" />
/// 
/// 假设起始地址为M100，M100存储了温度，100.6℃值为1006，M102存储了压力，1.23Mpa值为123，M104，M105，M106，M107存储了产量计数，读取如下：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadExample2" title="Read示例" />
/// 以下是读取不同类型数据的示例
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadExample1" title="Read示例" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="WriteExample1" title="Write示例" />
/// 以下是一个复杂的读取示例
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadExample3" title="Read示例" />
/// 在西门子PLC，字符串分为普通的string，和WString类型，前者为单字节的类型，后者为双字节的字符串类型<br />
/// 一个字符串除了本身的数据信息，还有字符串的长度信息，比如字符串 "12345"，比如在PLC的地址 DB1.0 存储的字节是 FE 05 31 32 33 34 35, 第一个字节是最大长度，第二个字节是当前长度，后面的才是字符串的数据信息。<br />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadWriteString" title="字符串读写示例" />
/// </example>
public class SiemensS7Net : DeviceTcpNet
{
  private byte[] plcHead1 = new byte[22]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 22,
    (byte) 17,
    (byte) 224 /*0xE0*/,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 192 /*0xC0*/,
    (byte) 1,
    (byte) 10,
    (byte) 193,
    (byte) 2,
    (byte) 1,
    (byte) 2,
    (byte) 194,
    (byte) 2,
    (byte) 1,
    (byte) 0
  };
  private byte[] plcHead2 = new byte[25]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 25,
    (byte) 2,
    (byte) 240 /*0xF0*/,
    (byte) 128 /*0x80*/,
    (byte) 50,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 4,
    (byte) 0,
    (byte) 0,
    (byte) 8,
    (byte) 0,
    (byte) 0,
    (byte) 240 /*0xF0*/,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 1,
    (byte) 1,
    (byte) 224 /*0xE0*/
  };
  private byte[] plcOrderNumber = new byte[33]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 33,
    (byte) 2,
    (byte) 240 /*0xF0*/,
    (byte) 128 /*0x80*/,
    (byte) 50,
    (byte) 7,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 8,
    (byte) 0,
    (byte) 8,
    (byte) 0,
    (byte) 1,
    (byte) 18,
    (byte) 4,
    (byte) 17,
    (byte) 68,
    (byte) 1,
    (byte) 0,
    byte.MaxValue,
    (byte) 9,
    (byte) 0,
    (byte) 4,
    (byte) 0,
    (byte) 17,
    (byte) 0,
    (byte) 0
  };
  private SiemensPLCS CurrentPlc = SiemensPLCS.S1200;
  private byte[] plcHead1_200smart = new byte[22]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 22,
    (byte) 17,
    (byte) 224 /*0xE0*/,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 193,
    (byte) 2,
    (byte) 16 /*0x10*/,
    (byte) 0,
    (byte) 194,
    (byte) 2,
    (byte) 3,
    (byte) 0,
    (byte) 192 /*0xC0*/,
    (byte) 1,
    (byte) 10
  };
  private byte[] plcHead2_200smart = new byte[25]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 25,
    (byte) 2,
    (byte) 240 /*0xF0*/,
    (byte) 128 /*0x80*/,
    (byte) 50,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 204,
    (byte) 193,
    (byte) 0,
    (byte) 8,
    (byte) 0,
    (byte) 0,
    (byte) 240 /*0xF0*/,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 1,
    (byte) 3,
    (byte) 192 /*0xC0*/
  };
  private byte[] plcHead1_200 = new byte[22]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 22,
    (byte) 17,
    (byte) 224 /*0xE0*/,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 193,
    (byte) 2,
    (byte) 77,
    (byte) 87,
    (byte) 194,
    (byte) 2,
    (byte) 77,
    (byte) 87,
    (byte) 192 /*0xC0*/,
    (byte) 1,
    (byte) 9
  };
  private byte[] plcHead2_200 = new byte[25]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 25,
    (byte) 2,
    (byte) 240 /*0xF0*/,
    (byte) 128 /*0x80*/,
    (byte) 50,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 8,
    (byte) 0,
    (byte) 0,
    (byte) 240 /*0xF0*/,
    (byte) 0,
    (byte) 0,
    (byte) 1,
    (byte) 0,
    (byte) 1,
    (byte) 3,
    (byte) 192 /*0xC0*/
  };
  private byte[] S7_STOP = new byte[33]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 33,
    (byte) 2,
    (byte) 240 /*0xF0*/,
    (byte) 128 /*0x80*/,
    (byte) 50,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 14,
    (byte) 0,
    (byte) 0,
    (byte) 16 /*0x10*/,
    (byte) 0,
    (byte) 0,
    (byte) 41,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 9,
    (byte) 80 /*0x50*/,
    (byte) 95,
    (byte) 80 /*0x50*/,
    (byte) 82,
    (byte) 79,
    (byte) 71,
    (byte) 82,
    (byte) 65,
    (byte) 77
  };
  private byte[] S7_HOT_START = new byte[37]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 37,
    (byte) 2,
    (byte) 240 /*0xF0*/,
    (byte) 128 /*0x80*/,
    (byte) 50,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 12,
    (byte) 0,
    (byte) 0,
    (byte) 20,
    (byte) 0,
    (byte) 0,
    (byte) 40,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 253,
    (byte) 0,
    (byte) 0,
    (byte) 9,
    (byte) 80 /*0x50*/,
    (byte) 95,
    (byte) 80 /*0x50*/,
    (byte) 82,
    (byte) 79,
    (byte) 71,
    (byte) 82,
    (byte) 65,
    (byte) 77
  };
  private byte[] S7_COLD_START = new byte[39]
  {
    (byte) 3,
    (byte) 0,
    (byte) 0,
    (byte) 39,
    (byte) 2,
    (byte) 240 /*0xF0*/,
    (byte) 128 /*0x80*/,
    (byte) 50,
    (byte) 1,
    (byte) 0,
    (byte) 0,
    (byte) 15,
    (byte) 0,
    (byte) 0,
    (byte) 22,
    (byte) 0,
    (byte) 0,
    (byte) 40,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 0,
    (byte) 253,
    (byte) 0,
    (byte) 2,
    (byte) 67,
    (byte) 32 /*0x20*/,
    (byte) 9,
    (byte) 80 /*0x50*/,
    (byte) 95,
    (byte) 80 /*0x50*/,
    (byte) 82,
    (byte) 79,
    (byte) 71,
    (byte) 82,
    (byte) 65,
    (byte) 77
  };
  private byte plc_rack = 0;
  private byte plc_slot = 0;
  private int pdu_length = 200;
  private const byte pduStart = 40;
  private const byte pduStop = 41;
  private const byte pduAlreadyStarted = 2;
  private const byte pduAlreadyStopped = 7;
  private SoftIncrementCount incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);

  /// <summary>
  /// 实例化一个西门子的S7协议的通讯对象 <br />
  /// Instantiate a communication object for a Siemens S7 protocol
  /// </summary>
  /// <param name="siemens">指定西门子的型号</param>
  public SiemensS7Net(SiemensPLCS siemens) => this.Initialization(siemens, string.Empty);

  /// <summary>
  /// 实例化一个西门子的S7协议的通讯对象并指定Ip地址 <br />
  /// Instantiate a communication object for a Siemens S7 protocol and specify an IP address
  /// </summary>
  /// <param name="siemens">指定西门子的型号</param>
  /// <param name="ipAddress">Ip地址</param>
  public SiemensS7Net(SiemensPLCS siemens, string ipAddress)
  {
    this.Initialization(siemens, ipAddress);
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new S7Message();

  /// <summary>
  /// 初始化方法<br />
  /// Initialize method
  /// </summary>
  /// <param name="siemens">指定西门子的型号 -&gt; Designation of Siemens</param>
  /// <param name="ipAddress">Ip地址 -&gt; IpAddress</param>
  private void Initialization(SiemensPLCS siemens, string ipAddress)
  {
    this.WordLength = (ushort) 2;
    this.IpAddress = ipAddress;
    this.Port = 102;
    this.CurrentPlc = siemens;
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    switch (siemens)
    {
      case SiemensPLCS.S1200:
        this.plcHead1[21] = (byte) 0;
        break;
      case SiemensPLCS.S300:
        this.plcHead1[21] = (byte) 2;
        break;
      case SiemensPLCS.S400:
        this.plcHead1[21] = (byte) 3;
        this.plcHead1[17] = (byte) 0;
        break;
      case SiemensPLCS.S1500:
        this.plcHead1[21] = (byte) 0;
        break;
      case SiemensPLCS.S200Smart:
        this.plcHead1 = this.plcHead1_200smart;
        this.plcHead2 = this.plcHead2_200smart;
        break;
      case SiemensPLCS.S200:
        this.plcHead1 = this.plcHead1_200;
        this.plcHead2 = this.plcHead2_200;
        break;
      default:
        this.plcHead1[18] = (byte) 0;
        break;
    }
  }

  /// <summary>
  /// PLC的槽号，针对S7-400的PLC设置的<br />
  /// The slot number of PLC is set for PLC of s7-400
  /// </summary>
  public byte Slot
  {
    get => this.plc_slot;
    set
    {
      this.plc_slot = value;
      if (this.CurrentPlc == SiemensPLCS.S200 || this.CurrentPlc == SiemensPLCS.S200Smart)
        return;
      this.plcHead1[21] = (byte) ((uint) this.plc_rack * 32U /*0x20*/ + (uint) this.plc_slot);
    }
  }

  /// <summary>
  /// PLC的机架号，针对S7-400的PLC设置的<br />
  /// The frame number of the PLC is set for the PLC of s7-400
  /// </summary>
  public byte Rack
  {
    get => this.plc_rack;
    set
    {
      this.plc_rack = value;
      if (this.CurrentPlc == SiemensPLCS.S200 || this.CurrentPlc == SiemensPLCS.S200Smart)
        return;
      this.plcHead1[21] = (byte) ((uint) this.plc_rack * 32U /*0x20*/ + (uint) this.plc_slot);
    }
  }

  /// <summary>
  /// 获取或设置当前PLC的连接方式，PG: 0x01，OP: 0x02，S7Basic: 0x03...0x10<br />
  /// Get or set the current PLC connection mode, PG: 0x01, OP: 0x02, S7Basic: 0x03...0x10
  /// </summary>
  public byte ConnectionType
  {
    get => this.plcHead1[20];
    set
    {
      if (this.CurrentPlc == SiemensPLCS.S200 || this.CurrentPlc == SiemensPLCS.S200Smart)
        return;
      this.plcHead1[20] = value;
    }
  }

  /// <summary>
  /// 西门子相关的本地TSAP参数信息<br />
  /// A parameter information related to Siemens
  /// </summary>
  public int LocalTSAP
  {
    get
    {
      return this.CurrentPlc == SiemensPLCS.S200 || this.CurrentPlc == SiemensPLCS.S200Smart ? (int) this.plcHead1[13] * 256 /*0x0100*/ + (int) this.plcHead1[14] : (int) this.plcHead1[16 /*0x10*/] * 256 /*0x0100*/ + (int) this.plcHead1[17];
    }
    set
    {
      if (this.CurrentPlc == SiemensPLCS.S200 || this.CurrentPlc == SiemensPLCS.S200Smart)
      {
        this.plcHead1[13] = BitConverter.GetBytes(value)[1];
        this.plcHead1[14] = BitConverter.GetBytes(value)[0];
      }
      else
      {
        this.plcHead1[16 /*0x10*/] = BitConverter.GetBytes(value)[1];
        this.plcHead1[17] = BitConverter.GetBytes(value)[0];
      }
    }
  }

  /// <summary>
  /// 西门子相关的远程TSAP参数信息<br />
  /// A parameter information related to Siemens
  /// </summary>
  public int DestTSAP
  {
    get
    {
      return this.CurrentPlc == SiemensPLCS.S200 || this.CurrentPlc == SiemensPLCS.S200Smart ? (int) this.plcHead1[17] * 256 /*0x0100*/ + (int) this.plcHead1[18] : (int) this.plcHead1[20] * 256 /*0x0100*/ + (int) this.plcHead1[21];
    }
    set
    {
      if (this.CurrentPlc == SiemensPLCS.S200 || this.CurrentPlc == SiemensPLCS.S200Smart)
      {
        this.plcHead1[17] = BitConverter.GetBytes(value)[1];
        this.plcHead1[18] = BitConverter.GetBytes(value)[0];
      }
      else
      {
        this.plcHead1[20] = BitConverter.GetBytes(value)[1];
        this.plcHead1[21] = BitConverter.GetBytes(value)[0];
      }
    }
  }

  /// <summary>
  /// 获取当前西门子的PDU的长度信息，不同型号PLC的值会不一样。<br />
  /// Get the length information of the current Siemens PDU, the value of different types of PLC will be different.
  /// </summary>
  public int PDULength => this.pdu_length;

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData,
    bool usePackAndUnpack)
  {
    OperateResult<byte[]> operateResult;
    byte[] content;
    do
    {
      operateResult = base.ReadFromCoreServer(pipe, send, hasResponseData, usePackAndUnpack);
      if (operateResult.IsSuccess)
        content = operateResult.Content;
      else
        goto label_1;
    }
    while (content == null || content.Length < 4 || (int) operateResult.Content[2] * 256 /*0x0100*/ + (int) operateResult.Content[3] == 7);
    goto label_3;
label_1:
    return operateResult;
label_3:
    return operateResult;
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(this.CommunicationPipe, this.plcHead1, true, true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.CommunicationPipe, this.plcHead2, true, true);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    this.pdu_length = (int) this.ByteTransform.TransUInt16(operateResult2.Content.SelectLast<byte>(2), 0) - 28;
    if (this.pdu_length < 200)
      this.pdu_length = 200;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData,
    bool usePackAndUnpack)
  {
    OperateResult<byte[]> read;
    while (true)
    {
      read = await base.ReadFromCoreServerAsync(pipe, send, hasResponseData, usePackAndUnpack);
      if (read.IsSuccess)
      {
        byte[] content = read.Content;
        if (content == null || content.Length < 4 || (int) read.Content[2] * 256 /*0x0100*/ + (int) read.Content[3] == 7)
          read = (OperateResult<byte[]>) null;
        else
          goto label_4;
      }
      else
        break;
    }
    return read;
label_4:
    return read;
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    OperateResult<byte[]> read_first = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.plcHead1, true, true);
    if (!read_first.IsSuccess)
      return (OperateResult) read_first;
    OperateResult<byte[]> read_second = await this.ReadFromCoreServerAsync(this.CommunicationPipe, this.plcHead2, true, true);
    if (!read_second.IsSuccess)
      return (OperateResult) read_second;
    this.pdu_length = (int) this.ByteTransform.TransUInt16(read_second.Content.SelectLast<byte>(2), 0) - 28;
    if (this.pdu_length < 200)
      this.pdu_length = 200;
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 1L);
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 从PLC读取订货号信息<br />
  /// Reading order number information from PLC
  /// </summary>
  /// <returns>CPU的订货号信息 -&gt; Order number information for the CPU</returns>
  [HslMqttApi("ReadOrderNumber", "获取到PLC的订货号信息")]
  public OperateResult<string> ReadOrderNumber()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.plcOrderNumber);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    return result.Content == null || result.Content.Length < 91 ? new OperateResult<string>($"{StringResources.Language.ReceiveDataLengthTooShort}91, Source: {result.Content.ToHexString(' ')}") : OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(result.Content, 71, 20));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadOrderNumber" />
  public async Task<OperateResult<string>> ReadOrderNumberAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.plcOrderNumber);
    OperateResult<string> operateResult = read.IsSuccess ? (read.Content != null && read.Content.Length >= 91 ? OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(read.Content, 71, 20)) : new OperateResult<string>($"{StringResources.Language.ReceiveDataLengthTooShort}91, Source: {read.Content.ToHexString(' ')}")) : OperateResult.CreateFailedResult<string>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  private OperateResult CheckStartResult(byte[] content)
  {
    if (content == null || content.Length < 19)
      return new OperateResult("Receive error length < 19");
    if (content[19] != (byte) 40)
      return new OperateResult("Can not start PLC");
    return content[20] != (byte) 2 ? new OperateResult("Can not start PLC") : OperateResult.CreateSuccessResult();
  }

  private OperateResult CheckStopResult(byte[] content)
  {
    if (content == null || content.Length < 19)
      return new OperateResult("Receive error length < 19");
    if (content[19] != (byte) 41)
      return new OperateResult("Can not stop PLC");
    return content[20] != (byte) 7 ? new OperateResult("Can not stop PLC") : OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 对PLC进行热启动，目前仅适用于200smart型号<br />
  /// Hot start for PLC, currently only applicable to 200smart model
  /// </summary>
  /// <returns>是否启动成功的结果对象</returns>
  [HslMqttApi]
  public OperateResult HotStart()
  {
    return ByteTransformHelper.GetResultFromOther<byte[]>(this.ReadFromCoreServer(this.S7_HOT_START), new Func<byte[], OperateResult>(this.CheckStartResult));
  }

  /// <summary>
  /// 对PLC进行冷启动，目前仅适用于200smart型号<br />
  /// Cold start for PLC, currently only applicable to 200smart model
  /// </summary>
  /// <returns>是否启动成功的结果对象</returns>
  [HslMqttApi]
  public OperateResult ColdStart()
  {
    return ByteTransformHelper.GetResultFromOther<byte[]>(this.ReadFromCoreServer(this.S7_COLD_START), new Func<byte[], OperateResult>(this.CheckStartResult));
  }

  /// <summary>
  /// 对PLC进行停止，目前仅适用于200smart型号<br />
  /// Stop the PLC, currently only applicable to the 200smart model
  /// </summary>
  /// <returns>是否启动成功的结果对象</returns>
  [HslMqttApi]
  public OperateResult Stop()
  {
    return ByteTransformHelper.GetResultFromOther<byte[]>(this.ReadFromCoreServer(this.S7_STOP), new Func<byte[], OperateResult>(this.CheckStopResult));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.HotStart" />
  public async Task<OperateResult> HotStartAsync()
  {
    OperateResult<byte[]> result = await this.ReadFromCoreServerAsync(this.S7_HOT_START);
    return ByteTransformHelper.GetResultFromOther<byte[]>(result, new Func<byte[], OperateResult>(this.CheckStartResult));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ColdStart" />
  public async Task<OperateResult> ColdStartAsync()
  {
    OperateResult<byte[]> result = await this.ReadFromCoreServerAsync(this.S7_COLD_START);
    return ByteTransformHelper.GetResultFromOther<byte[]>(result, new Func<byte[], OperateResult>(this.CheckStartResult));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Stop" />
  public async Task<OperateResult> StopAsync()
  {
    OperateResult<byte[]> result = await this.ReadFromCoreServerAsync(this.S7_STOP);
    return ByteTransformHelper.GetResultFromOther<byte[]>(result, new Func<byte[], OperateResult>(this.CheckStopResult));
  }

  /// <summary>
  /// 从PLC读取原始的字节数据，地址格式为I100，Q100，DB20.100，M100，长度参数以字节为单位<br />
  /// Read the original byte data from the PLC, the address format is I100, Q100, DB20.100, M100, length parameters in bytes
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100<br />
  /// Starting address, formatted as I100,M100,Q100,DB20.100</param>
  /// <param name="length">读取的数量，以字节为单位<br />
  /// The number of reads, in bytes</param>
  /// <returns>
  /// 是否读取成功的结果对象 <br />
  /// Whether to read the successful result object</returns>
  /// <remarks>
  /// <inheritdoc cref="T:HslCommunication.Profinet.Siemens.SiemensS7Net" path="note" />
  /// </remarks>
  /// <example>
  /// 假设起始地址为M100，M100存储了温度，100.6℃值为1006，M102存储了压力，1.23Mpa值为123，M104，M105，M106，M107存储了产量计数，读取如下：
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadExample2" title="Read示例" />
  /// 以下是读取不同类型数据的示例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadExample1" title="Read示例" />
  /// </example>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address, length);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    return this.Read(new S7AddressData[1]{ from.Content });
  }

  /// <summary>
  /// 从PLC读取数据，地址格式为I100，Q100，DB20.100，M100，以位为单位 -&gt;
  /// Read the data from the PLC, the address format is I100，Q100，DB20.100，M100, in bits units
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt;
  /// Starting address, formatted as I100,M100,Q100,DB20.100</param>
  /// <returns>是否读取成功的结果对象 -&gt; Whether to read the successful result object</returns>
  private OperateResult<byte[]> ReadBitFromPLC(string address)
  {
    OperateResult<byte[]> result = SiemensS7Net.BuildBitReadCommand(address, this.GetMessageId());
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(result.Content);
    return !operateResult.IsSuccess ? operateResult : SiemensS7Helper.AnalysisReadBit(operateResult.Content);
  }

  /// <summary>
  /// 一次性从PLC获取所有的数据，按照先后顺序返回一个统一的Buffer，需要按照顺序处理，两个数组长度必须一致，数组长度无限制<br />
  /// One-time from the PLC to obtain all the data, in order to return a unified buffer, need to be processed sequentially, two array length must be consistent
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100<br />
  /// Starting address, formatted as I100,M100,Q100,DB20.100</param>
  /// <param name="length">数据长度数组<br />
  /// Array of data Lengths</param>
  /// <returns>是否读取成功的结果对象 -&gt; Whether to read the successful result object</returns>
  /// <exception cref="T:System.NullReferenceException"></exception>
  /// <remarks>
  /// <note type="warning">原先的批量的长度为19，现在已经内部自动处理整合，目前的长度为任意和长度。</note>
  /// </remarks>
  /// <example>
  /// 以下是一个高级的读取示例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadExample3" title="Read示例" />
  /// </example>
  [HslMqttApi("ReadAddressArray", "一次性从PLC获取所有的数据，按照先后顺序返回一个统一的Buffer，需要按照顺序处理，两个数组长度必须一致，数组长度无限制")]
  public OperateResult<byte[]> Read(string[] address, ushort[] length)
  {
    S7AddressData[] s7Addresses = new S7AddressData[address.Length];
    for (int index = 0; index < address.Length; ++index)
    {
      OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address[index], length[index]);
      if (!from.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
      s7Addresses[index] = from.Content;
    }
    return this.Read(s7Addresses);
  }

  /// <summary>
  /// 读取西门子的地址数据信息，支持任意个数的数据读取<br />
  /// Read Siemens address data information, support any number of data reading
  /// </summary>
  /// <param name="s7Addresses">
  /// 西门子的数据地址<br />
  /// Siemens data address</param>
  /// <returns>返回的结果对象信息 -&gt; Whether to read the successful result object</returns>
  public OperateResult<byte[]> Read(S7AddressData[] s7Addresses)
  {
    List<byte> byteList = new List<byte>();
    List<S7AddressData[]> s7AddressDataArrayList = SiemensS7Helper.ArraySplitByLength(s7Addresses, this.pdu_length);
    for (int index = 0; index < s7AddressDataArrayList.Count; ++index)
    {
      S7AddressData[] s7Addresses1 = s7AddressDataArrayList[index];
      if (s7Addresses1.Length == 1 && (int) s7Addresses1[0].Length > this.pdu_length)
      {
        foreach (S7AddressData s7AddressData in SiemensS7Helper.SplitS7Address(s7Addresses1[0], this.pdu_length))
        {
          OperateResult<byte[]> operateResult = this.ReadS7AddressData(new S7AddressData[1]
          {
            s7AddressData
          });
          if (!operateResult.IsSuccess)
            return operateResult;
          byteList.AddRange((IEnumerable<byte>) operateResult.Content);
        }
      }
      else
      {
        OperateResult<byte[]> operateResult = this.ReadS7AddressData(s7Addresses1);
        if (!operateResult.IsSuccess)
          return operateResult;
        byteList.AddRange((IEnumerable<byte>) operateResult.Content);
      }
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>单次的读取，只能读取最多19个数组的长度，所以不再对外公开该方法</summary>
  /// <param name="s7Addresses">西门子的地址对象</param>
  /// <returns>返回的结果对象信息</returns>
  private OperateResult<byte[]> ReadS7AddressData(S7AddressData[] s7Addresses)
  {
    OperateResult<byte[]> operateResult1 = SiemensS7Net.BuildReadCommand(s7Addresses, this.GetMessageId());
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? operateResult2 : SiemensS7Net.AnalysisReadByte(s7Addresses, operateResult2.Content);
  }

  /// <summary>
  /// 基础的写入数据的操作支持<br />
  /// Operational support for the underlying write data
  /// </summary>
  /// <param name="entireValue">完整的字节数据 -&gt; Full byte data</param>
  /// <returns>是否写入成功的结果对象 -&gt; Whether to write a successful result object</returns>
  private OperateResult WriteBase(byte[] entireValue)
  {
    return ByteTransformHelper.GetResultFromOther<byte[]>(this.ReadFromCoreServer(entireValue), new Func<byte[], OperateResult>(SiemensS7Net.AnalysisWrite));
  }

  /// <summary>
  /// 将数据写入到PLC数据，地址格式为I100，Q100，DB20.100，M100，以字节为单位<br />
  /// Writes data to the PLC data, in the address format I100,Q100,DB20.100,M100, in bytes
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt;
  /// Starting address, formatted as I100,M100,Q100,DB20.100</param>
  /// <param name="value">写入的原始数据 -&gt; Raw data written to</param>
  /// <returns>是否写入成功的结果对象 -&gt; Whether to write a successful result object</returns>
  /// <example>
  /// 假设起始地址为M100，M100,M101存储了温度，100.6℃值为1006，M102,M103存储了压力，1.23Mpa值为123，M104-M107存储了产量计数，写入如下：
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="WriteExample2" title="Write示例" />
  /// 以下是写入不同类型数据的示例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="WriteExample1" title="Write示例" />
  /// </example>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    return !from.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from) : this.Write(from.Content, value);
  }

  private OperateResult Write(S7AddressData address, byte[] value)
  {
    int length1 = value.Length;
    ushort index = 0;
    while ((int) index < length1)
    {
      ushort length2 = (ushort) Math.Min(length1 - (int) index, this.pdu_length);
      byte[] data = this.ByteTransform.TransByte(value, (int) index, (int) length2);
      OperateResult<byte[]> operateResult1 = SiemensS7Net.BuildWriteByteCommand(address, data, this.GetMessageId());
      if (!operateResult1.IsSuccess)
        return (OperateResult) operateResult1;
      OperateResult operateResult2 = this.WriteBase(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      index += length2;
      address.AddressStart += (int) length2 * 8;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 使用离散的方式，同时写入多个数据块到不同的地址中去，但是支持的地址数量及写入数据的最大长度是有限制的，不能超过pdu长度限制。<br />
  /// Using the discrete method, multiple blocks of data are written to different addresses at the same time, but the number of supported addresses and the maximum length of the data written are limited, and cannot exceed the PDU length limit.
  /// </summary>
  /// <param name="address">地址数组信息</param>
  /// <param name="data">原始数据列表</param>
  /// <returns>是否写入成功</returns>
  public OperateResult Write(string[] address, List<byte[]> data)
  {
    S7AddressData[] s7Address = new S7AddressData[address.Length];
    for (int index = 0; index < address.Length; ++index)
    {
      OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address[index], (ushort) 1);
      if (!from.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
      s7Address[index] = from.Content;
    }
    OperateResult<byte[]> operateResult1 = SiemensS7Net.BuildWriteByteCommand(s7Address, data, this.GetMessageId());
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : SiemensS7Net.AnalysisWrite(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<S7AddressData> addressResult = S7AddressData.ParseFrom(address, length);
    if (!addressResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressResult);
    OperateResult<byte[]> operateResult = await this.ReadAsync(new S7AddressData[1]
    {
      addressResult.Content
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadBitFromPLC(System.String)" />
  private async Task<OperateResult<byte[]>> ReadBitFromPLCAsync(string address)
  {
    OperateResult<byte[]> command = SiemensS7Net.BuildBitReadCommand(address, this.GetMessageId());
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? SiemensS7Helper.AnalysisReadBit(read.Content) : read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Read(System.String[],System.UInt16[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address, ushort[] length)
  {
    S7AddressData[] addressResult = new S7AddressData[address.Length];
    for (int i = 0; i < address.Length; ++i)
    {
      OperateResult<S7AddressData> tmp = S7AddressData.ParseFrom(address[i], length[i]);
      if (!tmp.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) tmp);
      addressResult[i] = tmp.Content;
      tmp = (OperateResult<S7AddressData>) null;
    }
    OperateResult<byte[]> operateResult = await this.ReadAsync(addressResult);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Read(HslCommunication.Core.Address.S7AddressData[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(S7AddressData[] s7Addresses)
  {
    List<byte> bytes = new List<byte>();
    List<S7AddressData[]> groups = SiemensS7Helper.ArraySplitByLength(s7Addresses, this.pdu_length);
    for (int i = 0; i < groups.Count; ++i)
    {
      S7AddressData[] group = groups[i];
      if (group.Length == 1 && (int) group[0].Length > this.pdu_length)
      {
        S7AddressData[] array = SiemensS7Helper.SplitS7Address(group[0], this.pdu_length);
        for (int j = 0; j < array.Length; ++j)
        {
          OperateResult<byte[]> read = await this.ReadS7AddressDataAsync(new S7AddressData[1]
          {
            array[j]
          });
          if (!read.IsSuccess)
            return read;
          bytes.AddRange((IEnumerable<byte>) read.Content);
          read = (OperateResult<byte[]>) null;
        }
        array = (S7AddressData[]) null;
      }
      else
      {
        OperateResult<byte[]> read = await this.ReadS7AddressDataAsync(group);
        if (!read.IsSuccess)
          return read;
        bytes.AddRange((IEnumerable<byte>) read.Content);
        read = (OperateResult<byte[]>) null;
      }
      group = (S7AddressData[]) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(bytes.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadS7AddressData(HslCommunication.Core.Address.S7AddressData[])" />
  private async Task<OperateResult<byte[]>> ReadS7AddressDataAsync(S7AddressData[] s7Addresses)
  {
    OperateResult<byte[]> command = SiemensS7Net.BuildReadCommand(s7Addresses, this.GetMessageId());
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? SiemensS7Net.AnalysisReadByte(s7Addresses, read.Content) : read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.WriteBase(System.Byte[])" />
  private async Task<OperateResult> WriteBaseAsync(byte[] entireValue)
  {
    OperateResult<byte[]> result = await this.ReadFromCoreServerAsync(entireValue);
    return ByteTransformHelper.GetResultFromOther<byte[]>(result, new Func<byte[], OperateResult>(SiemensS7Net.AnalysisWrite));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<S7AddressData> analysis = S7AddressData.ParseFrom(address);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    OperateResult operateResult = await this.WriteAsync(analysis.Content, value);
    return operateResult;
  }

  private async Task<OperateResult> WriteAsync(S7AddressData address, byte[] value)
  {
    int length = value.Length;
    ushort alreadyFinished = 0;
    while ((int) alreadyFinished < length)
    {
      ushort writeLength = (ushort) Math.Min(length - (int) alreadyFinished, this.pdu_length);
      byte[] buffer = this.ByteTransform.TransByte(value, (int) alreadyFinished, (int) writeLength);
      OperateResult<byte[]> command = SiemensS7Net.BuildWriteByteCommand(address, buffer, this.GetMessageId());
      if (!command.IsSuccess)
        return (OperateResult) command;
      OperateResult write = await this.WriteBaseAsync(command.Content);
      if (!write.IsSuccess)
        return write;
      alreadyFinished += writeLength;
      address.AddressStart += (int) writeLength * 8;
      buffer = (byte[]) null;
      command = (OperateResult<byte[]>) null;
      write = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String[],System.Collections.Generic.List{System.Byte[]})" />
  public async Task<OperateResult> WriteAsync(string[] address, List<byte[]> data)
  {
    S7AddressData[] addressResult = new S7AddressData[address.Length];
    for (int i = 0; i < address.Length; ++i)
    {
      OperateResult<S7AddressData> tmp = S7AddressData.ParseFrom(address[i], (ushort) 1);
      if (!tmp.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) tmp);
      addressResult[i] = tmp.Content;
      tmp = (OperateResult<S7AddressData>) null;
    }
    OperateResult<byte[]> command = SiemensS7Net.BuildWriteByteCommand(addressResult, data, this.GetMessageId());
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? SiemensS7Net.AnalysisWrite(read.Content) : (OperateResult) read;
  }

  /// <summary>
  /// 读取指定地址的bool数据，地址格式为I100，M100，Q100，DB20.100<br />
  /// reads bool data for the specified address in the format I100，M100，Q100，DB20.100
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt;
  /// Starting address, formatted as I100,M100,Q100,DB20.100</param>
  /// <returns>是否读取成功的结果对象 -&gt; Whether to read the successful result object</returns>
  /// <remarks>
  /// <note type="important">
  /// 对于200smartPLC的V区，就是DB1.X，例如，V100=DB1.100
  /// </note>
  /// </remarks>
  /// <example>
  /// 假设读取M100.0的位是否通断
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="ReadBool" title="ReadBool示例" />
  /// </example>
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    return ByteTransformHelper.GetResultFromBytes<bool>(this.ReadBitFromPLC(address), (Func<byte[], bool>) (m => m[0] > (byte) 0));
  }

  /// <summary>
  /// 读取指定地址的bool数组，地址格式为I100，M100，Q100，DB20.100<br />
  /// reads bool array data for the specified address in the format I100，M100，Q100，DB20.100
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt;
  /// Starting address, formatted as I100,M100,Q100,DB20.100</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>是否读取成功的结果对象 -&gt; Whether to read the successful result object</returns>
  /// <remarks>
  /// <note type="important">
  /// 对于200smartPLC的V区，就是DB1.X，例如，V100=DB1.100
  /// </note>
  /// </remarks>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    int newStart;
    ushort byteLength;
    int offset;
    HslHelper.CalculateStartBitIndexAndLength(from.Content.AddressStart, length, out newStart, out byteLength, out offset);
    from.Content.AddressStart = newStart;
    from.Content.Length = byteLength;
    OperateResult<byte[]> result = this.Read(new S7AddressData[1]
    {
      from.Content
    });
    return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(result.Content.ToBoolArray().SelectMiddle<bool>(offset, (int) length));
  }

  /// <summary>
  /// 写入PLC的一个位，例如"M100.6"，"I100.7"，"Q100.0"，"DB20.100.0"，如果只写了"M100"默认为"M100.0"<br />
  /// Write a bit of PLC, for example  "M100.6",  "I100.7",  "Q100.0",  "DB20.100.0", if only write  "M100" defaults to  "M100.0"
  /// </summary>
  /// <param name="address">起始地址，格式为"M100.6",  "I100.7",  "Q100.0",  "DB20.100.0" -&gt;
  /// Start address, format  "M100.6",  "I100.7",  "Q100.0",  "DB20.100.0"</param>
  /// <param name="value">写入的数据，True或是False -&gt; Writes the data, either True or False</param>
  /// <returns>是否写入成功的结果对象 -&gt; Whether to write a successful result object</returns>
  /// <example>
  /// 假设写入M100.0的位是否通断
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7Net.cs" region="WriteBool" title="WriteBool示例" />
  /// </example>
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    OperateResult<byte[]> operateResult = SiemensS7Net.BuildWriteBitCommand(address, value, this.GetMessageId());
    return !operateResult.IsSuccess ? (OperateResult) operateResult : this.WriteBase(operateResult.Content);
  }

  /// <summary>
  /// [警告] 向PLC中写入bool数组，比如你写入M100,那么data[0]对应M100.0，写入的长度应该小于1600位<br />
  /// [Warn] Write the bool array to the PLC, for example, if you write M100, then data[0] corresponds to M100.0,
  /// The length of the write should be less than 1600 bits
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt; Starting address, formatted as I100,mM100,Q100,DB20.100</param>
  /// <param name="values">要写入的bool数组，长度为8的倍数 -&gt; The bool array to write, a multiple of 8 in length</param>
  /// <returns>是否写入成功的结果对象 -&gt; Whether to write a successful result object</returns>
  /// <remarks>
  /// <note type="warning">
  /// 批量写入bool数组存在一定的风险，举例写入M100.5的值 [true,false,true,true,false,true]，会读取M100-M101的byte[]，然后修改中间的位，再写入回去，
  /// 如果读取之后写入之前，PLC修改了其他位，则会影响其他的位的数据，请谨慎使用。<br />
  /// There is a certain risk in batch writing bool arrays. For example, writing the value of M100.5 [true,false,true,true,false,true],
  /// will read the byte[] of M100-M101, then modify the middle bit, and then Write back.
  /// If the PLC modifies other bits after reading and before writing, it will affect the data of other bits. Please use it with caution.
  /// </note>
  /// </remarks>
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    int newStart;
    ushort byteLength;
    int offset;
    HslHelper.CalculateStartBitIndexAndLength(from.Content.AddressStart, (ushort) values.Length, out newStart, out byteLength, out offset);
    from.Content.AddressStart = newStart;
    from.Content.Length = byteLength;
    OperateResult<byte[]> result = this.Read(new S7AddressData[1]
    {
      from.Content
    });
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    bool[] boolArray = result.Content.ToBoolArray();
    Array.Copy((Array) values, 0, (Array) boolArray, offset, values.Length);
    return this.Write(from.Content, SoftBasic.BoolArrayToByte(boolArray));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadBool(System.String)" />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadBitFromPLCAsync(address);
    return ByteTransformHelper.GetResultFromBytes<bool>(result, (Func<byte[], bool>) (m => m[0] > (byte) 0));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<S7AddressData> analysis = S7AddressData.ParseFrom(address);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) analysis);
    int newStart;
    ushort byteLength;
    int offset;
    HslHelper.CalculateStartBitIndexAndLength(analysis.Content.AddressStart, length, out newStart, out byteLength, out offset);
    analysis.Content.AddressStart = newStart;
    analysis.Content.Length = byteLength;
    OperateResult<byte[]> read = await this.ReadAsync(new S7AddressData[1]
    {
      analysis.Content
    });
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray().SelectMiddle<bool>(offset, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult<byte[]> command = SiemensS7Net.BuildWriteBitCommand(address, value, this.GetMessageId());
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult operateResult = await this.WriteBaseAsync(command.Content);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] values)
  {
    OperateResult<S7AddressData> analysis = S7AddressData.ParseFrom(address);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) analysis);
    int newStart;
    ushort byteLength;
    int offset;
    HslHelper.CalculateStartBitIndexAndLength(analysis.Content.AddressStart, (ushort) values.Length, out newStart, out byteLength, out offset);
    analysis.Content.AddressStart = newStart;
    analysis.Content.Length = byteLength;
    OperateResult<byte[]> read = await this.ReadAsync(new S7AddressData[1]
    {
      analysis.Content
    });
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    bool[] boolArray = read.Content.ToBoolArray();
    Array.Copy((Array) values, 0, (Array) boolArray, offset, values.Length);
    OperateResult operateResult = await this.WriteAsync(analysis.Content, SoftBasic.BoolArrayToByte(boolArray));
    return operateResult;
  }

  /// <summary>
  /// 读取指定地址的byte数据，地址格式I100，M100，Q100，DB20.100<br />
  /// Reads the byte data of the specified address, the address format I100,Q100,DB20.100,M100
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt;
  /// Starting address, formatted as I100,M100,Q100,DB20.100</param>
  /// <returns>是否读取成功的结果对象 -&gt; Whether to read the successful result object</returns>
  /// <example>参考<see cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Read(System.String,System.UInt16)" />的注释</example>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>
  /// 向PLC中写入byte数据，返回值说明<br />
  /// Write byte data to the PLC, return value description
  /// </summary>
  /// <param name="address">起始地址，格式为I100，M100，Q100，DB20.100 -&gt; Starting address, formatted as I100,mM100,Q100,DB20.100</param>
  /// <param name="value">byte数据 -&gt; Byte data</param>
  /// <returns>是否写入成功的结果对象 -&gt; Whether to write a successful result object</returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    return SiemensS7Helper.Write((IReadWriteNet) this, this.CurrentPlc, address, value, encoding);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.WriteWString(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.String)" />
  [HslMqttApi(ApiTopic = "WriteWString", Description = "写入unicode编码的字符串，支持中文")]
  public OperateResult WriteWString(string address, string value)
  {
    return SiemensS7Helper.WriteWString((IReadWriteNet) this, this.CurrentPlc, address, value);
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return length != (ushort) 0 ? base.ReadString(address, length, encoding) : this.ReadString(address, encoding);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadString(System.String,System.Text.Encoding)" />
  [HslMqttApi("ReadS7String", "读取S7格式的字符串")]
  public OperateResult<string> ReadString(string address)
  {
    return this.ReadString(address, Encoding.ASCII);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.ReadString(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.Text.Encoding)" />
  public OperateResult<string> ReadString(string address, Encoding encoding)
  {
    return SiemensS7Helper.ReadString((IReadWriteNet) this, this.CurrentPlc, address, encoding);
  }

  /// <summary>
  /// 读取西门子的地址的字符串信息，这个信息是和西门子绑定在一起，长度随西门子的信息动态变化的<br />
  /// Read the Siemens address string information. This information is bound to Siemens and its length changes dynamically with the Siemens information
  /// </summary>
  /// <param name="address">数据地址，具体的格式需要参照类的说明文档</param>
  /// <returns>带有是否成功的字符串结果类对象</returns>
  [HslMqttApi("ReadWString", "读取S7格式的双字节字符串")]
  public OperateResult<string> ReadWString(string address)
  {
    return SiemensS7Helper.ReadWString((IReadWriteNet) this, this.CurrentPlc, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.Write(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.String,System.Text.Encoding)" />
  public override async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    OperateResult operateResult = await SiemensS7Helper.WriteAsync((IReadWriteNet) this, this.CurrentPlc, address, value, encoding);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.WriteWString(System.String,System.String)" />
  public async Task<OperateResult> WriteWStringAsync(string address, string value)
  {
    OperateResult operateResult = await SiemensS7Helper.WriteWStringAsync((IReadWriteNet) this, this.CurrentPlc, address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<string> operateResult;
    if (length == (ushort) 0)
      operateResult = await this.ReadStringAsync(address, encoding);
    else
      operateResult = await base.ReadStringAsync(address, length, encoding);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadString(System.String)" />
  public async Task<OperateResult<string>> ReadStringAsync(string address)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, Encoding.ASCII);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadString(System.String,System.Text.Encoding)" />
  public async Task<OperateResult<string>> ReadStringAsync(string address, Encoding encoding)
  {
    OperateResult<string> operateResult = await SiemensS7Helper.ReadStringAsync((IReadWriteNet) this, this.CurrentPlc, address, encoding);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadWString(System.String)" />
  public async Task<OperateResult<string>> ReadWStringAsync(string address)
  {
    OperateResult<string> operateResult = await SiemensS7Helper.ReadWStringAsync((IReadWriteNet) this, this.CurrentPlc, address);
    return operateResult;
  }

  /// <summary>
  /// 从PLC中读取时间格式的数据<br />
  /// Read time format data from PLC
  /// </summary>
  /// <param name="address">地址</param>
  /// <returns>时间对象</returns>
  [HslMqttApi("ReadDateTime", "读取PLC的时间格式的数据，这个格式是s7格式的一种")]
  public OperateResult<DateTime> ReadDateTime(string address)
  {
    OperateResult<byte[]> result = this.Read(address, (ushort) 8);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<DateTime>((OperateResult) result) : SiemensDateTime.FromByteArray(result.Content);
  }

  /// <summary>从PLC中读取DTL时间格式的数据</summary>
  /// <param name="address">地址信息</param>
  /// <returns>时间对象</returns>
  [HslMqttApi("ReadDTLDataTime", "读取PLC的时间格式的数据，这个格式是s7的DTL格式")]
  public OperateResult<DateTime> ReadDTLDataTime(string address)
  {
    OperateResult<byte[]> result = this.Read(address, (ushort) 12);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<DateTime>((OperateResult) result) : SiemensDateTime.GetDTLTime(this.ByteTransform, result.Content, 0);
  }

  /// <summary>
  /// 从PLC中读取日期格式的数据<br />
  /// Read data in date format from PLC
  /// </summary>
  /// <param name="address">PLC的地址</param>
  /// <returns>日期对象</returns>
  public OperateResult<DateTime> ReadDate(string address)
  {
    return this.ReadUInt16(address).Then<DateTime>((Func<ushort, OperateResult<DateTime>>) (m => OperateResult.CreateSuccessResult<DateTime>(new DateTime(1990, 1, 1).AddDays((double) m))));
  }

  /// <summary>
  /// 向PLC中写入时间格式的数据<br />
  /// Writes data in time format to the PLC
  /// </summary>
  /// <param name="address">地址</param>
  /// <param name="dateTime">时间</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteDateTime", "写入PLC的时间格式的数据，这个格式是s7格式的一种")]
  public OperateResult Write(string address, DateTime dateTime)
  {
    return this.Write(address, SiemensDateTime.ToByteArray(dateTime));
  }

  /// <summary>向PLC中写入DTL格式的时间数据信息</summary>
  /// <param name="address">写入的地址信息</param>
  /// <param name="dateTime">时间</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteDTLTime", "写入PLC的时间格式的数据，这个格式是s7格式的DTL格式")]
  public OperateResult WriteDTLTime(string address, DateTime dateTime)
  {
    return this.Write(address, SiemensDateTime.GetBytesFromDTLTime(this.ByteTransform, dateTime));
  }

  /// <summary>
  /// 向PLC中写入日期格式的数据，日期格式里只有年，月，日<br />
  /// Write data in date format to PLC, only year, month, day in date format
  /// </summary>
  /// <param name="address">等待写入的PLC地址</param>
  /// <param name="dateTime">等待写入的日期</param>
  /// <returns>是否写入成功</returns>
  public OperateResult WriteDate(string address, DateTime dateTime)
  {
    return this.Write(address, Convert.ToUInt16((dateTime - new DateTime(1990, 1, 1)).TotalDays));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadDateTime(System.String)" />
  public async Task<OperateResult<DateTime>> ReadDateTimeAsync(string address)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 8);
    OperateResult<DateTime> operateResult = read.IsSuccess ? SiemensDateTime.FromByteArray(read.Content) : OperateResult.CreateFailedResult<DateTime>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadDTLDataTime(System.String)" />
  public async Task<OperateResult<DateTime>> ReadDTLDataTimeAsync(string address)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 12);
    OperateResult<DateTime> operateResult = read.IsSuccess ? SiemensDateTime.GetDTLTime(this.ByteTransform, read.Content, 0) : OperateResult.CreateFailedResult<DateTime>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadDate(System.String)" />
  public async Task<OperateResult<DateTime>> ReadDateAsync(string address)
  {
    OperateResult<ushort> operateResult = await this.ReadUInt16Async(address);
    return operateResult.Then<DateTime>((Func<ushort, OperateResult<DateTime>>) (m => OperateResult.CreateSuccessResult<DateTime>(new DateTime(1990, 1, 1).AddDays((double) m))));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteAsync(string address, DateTime dateTime)
  {
    OperateResult operateResult = await this.WriteAsync(address, SiemensDateTime.ToByteArray(dateTime));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.WriteDTLTime(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteDTLTimeAsync(string address, DateTime dateTime)
  {
    OperateResult operateResult = await this.WriteAsync(address, SiemensDateTime.GetBytesFromDTLTime(this.ByteTransform, dateTime));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.WriteDate(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteDateAsync(string address, DateTime dateTime)
  {
    OperateResult operateResult = await this.WriteAsync(address, Convert.ToUInt16((dateTime - new DateTime(1990, 1, 1)).TotalDays));
    return operateResult;
  }

  /// <summary>
  /// 强制输出一个位到指定的地址，针对PLC类型为 200smart 时有效<br />
  /// Force output one bit to the specified address, valid for PLC type 200smart
  /// </summary>
  /// <remarks>测试型号: S7-200 smart CPU SR30</remarks>
  /// <param name="address">西门子的地址信息，例如 I0.0, Q1.0, M2.0</param>
  /// <param name="value">输出值 false=断开, true=闭合</param>
  /// <returns>是否强制输出成功</returns>
  public OperateResult ForceBool(string address, bool value)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) from;
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(new byte[47]
    {
      (byte) 3,
      (byte) 0,
      (byte) 0,
      (byte) 47,
      (byte) 2,
      (byte) 240 /*0xF0*/,
      (byte) 128 /*0x80*/,
      (byte) 50,
      (byte) 7,
      (byte) 0,
      (byte) 0,
      (byte) 121,
      (byte) 249,
      (byte) 0,
      (byte) 12,
      (byte) 0,
      (byte) 18,
      (byte) 0,
      (byte) 1,
      (byte) 18,
      (byte) 8,
      (byte) 18,
      (byte) 72,
      (byte) 11,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      byte.MaxValue,
      (byte) 9,
      (byte) 0,
      (byte) 14,
      (byte) 0,
      (byte) 1,
      (byte) 16 /*0x10*/,
      (byte) 1,
      (byte) 0,
      (byte) 1,
      (byte) ((uint) from.Content.DbBlock / 256U /*0x0100*/),
      (byte) ((uint) from.Content.DbBlock % 256U /*0x0100*/),
      from.Content.DataCode,
      (byte) (from.Content.AddressStart / 256 /*0x0100*/ / 256 /*0x0100*/ % 256 /*0x0100*/),
      (byte) (from.Content.AddressStart / 256 /*0x0100*/ % 256 /*0x0100*/),
      (byte) (from.Content.AddressStart % 256 /*0x0100*/),
      value ? (byte) 1 : (byte) 0,
      (byte) 0
    });
    return !operateResult.IsSuccess ? (OperateResult) operateResult : OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 取消所有强制输出，针对PLC类型为 200smart 时有效<br />
  /// Cancel all forced outputs, effective for PLC type 200smart
  /// </summary>
  /// <returns>是否取消成功</returns>
  public OperateResult CancelAllForce()
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(new byte[35]
    {
      (byte) 3,
      (byte) 0,
      (byte) 0,
      (byte) 35,
      (byte) 2,
      (byte) 240 /*0xF0*/,
      (byte) 128 /*0x80*/,
      (byte) 50,
      (byte) 7,
      (byte) 0,
      (byte) 0,
      (byte) 166,
      (byte) 209,
      (byte) 0,
      (byte) 12,
      (byte) 0,
      (byte) 6,
      (byte) 0,
      (byte) 1,
      (byte) 18,
      (byte) 8,
      (byte) 18,
      (byte) 72,
      (byte) 11,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      byte.MaxValue,
      (byte) 9,
      (byte) 0,
      (byte) 2,
      (byte) 2,
      (byte) 0
    });
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    if (operateResult.Content.Length < 33)
      return new OperateResult("Receive error");
    return operateResult.Content[11] != (byte) 166 || operateResult.Content[12] != (byte) 209 ? new OperateResult("CancelAllForceOut Fail!") : OperateResult.CreateSuccessResult();
  }

  private int GetMessageId() => (int) this.incrementCount.GetCurrentValue();

  /// <inheritdoc />
  public override string ToString()
  {
    return $"SiemensS7Net {this.CurrentPlc}[{this.IpAddress}:{this.Port}]";
  }

  /// <summary>
  /// A general method for generating a command header to read a Word data
  /// </summary>
  /// <param name="s7Addresses">siemens address</param>
  /// <param name="msgId">message id informaion</param>
  /// <returns>Message containing the result object</returns>
  public static OperateResult<byte[]> BuildReadCommand(S7AddressData[] s7Addresses, int msgId)
  {
    if (s7Addresses == null)
      throw new NullReferenceException(nameof (s7Addresses));
    int num = s7Addresses.Length <= 19 ? s7Addresses.Length : throw new Exception(StringResources.Language.SiemensReadLengthCannotLargerThan19);
    byte[] numArray = new byte[19 + num * 12];
    numArray[0] = (byte) 3;
    numArray[1] = (byte) 0;
    numArray[2] = (byte) (numArray.Length / 256 /*0x0100*/);
    numArray[3] = (byte) (numArray.Length % 256 /*0x0100*/);
    numArray[4] = (byte) 2;
    numArray[5] = (byte) 240 /*0xF0*/;
    numArray[6] = (byte) 128 /*0x80*/;
    numArray[7] = (byte) 50;
    numArray[8] = (byte) 1;
    numArray[9] = (byte) 0;
    numArray[10] = (byte) 0;
    numArray[11] = BitConverter.GetBytes(msgId)[1];
    numArray[12] = BitConverter.GetBytes(msgId)[0];
    numArray[13] = (byte) ((numArray.Length - 17) / 256 /*0x0100*/);
    numArray[14] = (byte) ((numArray.Length - 17) % 256 /*0x0100*/);
    numArray[15] = (byte) 0;
    numArray[16 /*0x10*/] = (byte) 0;
    numArray[17] = (byte) 4;
    numArray[18] = (byte) num;
    for (int index = 0; index < num; ++index)
    {
      numArray[19 + index * 12] = (byte) 18;
      numArray[20 + index * 12] = (byte) 10;
      numArray[21 + index * 12] = (byte) 16 /*0x10*/;
      if (s7Addresses[index].DataCode == (byte) 30 || s7Addresses[index].DataCode == (byte) 31 /*0x1F*/)
      {
        numArray[22 + index * 12] = s7Addresses[index].DataCode;
        numArray[23 + index * 12] = (byte) ((int) s7Addresses[index].Length / 2 / 256 /*0x0100*/);
        numArray[24 + index * 12] = (byte) ((int) s7Addresses[index].Length / 2 % 256 /*0x0100*/);
      }
      else if (s7Addresses[index].DataCode == (byte) 6 | s7Addresses[index].DataCode == (byte) 7)
      {
        numArray[22 + index * 12] = (byte) 4;
        numArray[23 + index * 12] = (byte) ((int) s7Addresses[index].Length / 2 / 256 /*0x0100*/);
        numArray[24 + index * 12] = (byte) ((int) s7Addresses[index].Length / 2 % 256 /*0x0100*/);
      }
      else
      {
        numArray[22 + index * 12] = (byte) 2;
        numArray[23 + index * 12] = (byte) ((uint) s7Addresses[index].Length / 256U /*0x0100*/);
        numArray[24 + index * 12] = (byte) ((uint) s7Addresses[index].Length % 256U /*0x0100*/);
      }
      numArray[25 + index * 12] = (byte) ((uint) s7Addresses[index].DbBlock / 256U /*0x0100*/);
      numArray[26 + index * 12] = (byte) ((uint) s7Addresses[index].DbBlock % 256U /*0x0100*/);
      numArray[27 + index * 12] = s7Addresses[index].DataCode;
      numArray[28 + index * 12] = (byte) (s7Addresses[index].AddressStart / 256 /*0x0100*/ / 256 /*0x0100*/ % 256 /*0x0100*/);
      numArray[29 + index * 12] = (byte) (s7Addresses[index].AddressStart / 256 /*0x0100*/ % 256 /*0x0100*/);
      numArray[30 + index * 12] = (byte) (s7Addresses[index].AddressStart % 256 /*0x0100*/);
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>
  /// 生成一个位读取数据指令头的通用方法 -&gt;
  /// A general method for generating a bit-read-Data instruction header
  /// </summary>
  /// <param name="address">起始地址，例如M100.0，I0.1，Q0.1，DB2.100.2 -&gt;
  /// Start address, such as M100.0,I0.1,Q0.1,DB2.100.2
  /// </param>
  /// <param name="msgId">message id informaion</param>
  /// <returns>包含结果对象的报文 -&gt; Message containing the result object</returns>
  public static OperateResult<byte[]> BuildBitReadCommand(string address, int msgId)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    byte[] numArray = new byte[31 /*0x1F*/];
    numArray[0] = (byte) 3;
    numArray[1] = (byte) 0;
    numArray[2] = (byte) (numArray.Length / 256 /*0x0100*/);
    numArray[3] = (byte) (numArray.Length % 256 /*0x0100*/);
    numArray[4] = (byte) 2;
    numArray[5] = (byte) 240 /*0xF0*/;
    numArray[6] = (byte) 128 /*0x80*/;
    numArray[7] = (byte) 50;
    numArray[8] = (byte) 1;
    numArray[9] = (byte) 0;
    numArray[10] = (byte) 0;
    numArray[11] = BitConverter.GetBytes(msgId)[1];
    numArray[12] = BitConverter.GetBytes(msgId)[0];
    numArray[13] = (byte) ((numArray.Length - 17) / 256 /*0x0100*/);
    numArray[14] = (byte) ((numArray.Length - 17) % 256 /*0x0100*/);
    numArray[15] = (byte) 0;
    numArray[16 /*0x10*/] = (byte) 0;
    numArray[17] = (byte) 4;
    numArray[18] = (byte) 1;
    numArray[19] = (byte) 18;
    numArray[20] = (byte) 10;
    numArray[21] = (byte) 16 /*0x10*/;
    numArray[22] = (byte) 1;
    numArray[23] = (byte) 0;
    numArray[24] = (byte) 1;
    numArray[25] = (byte) ((uint) from.Content.DbBlock / 256U /*0x0100*/);
    numArray[26] = (byte) ((uint) from.Content.DbBlock % 256U /*0x0100*/);
    numArray[27] = from.Content.DataCode;
    numArray[28] = (byte) (from.Content.AddressStart / 256 /*0x0100*/ / 256 /*0x0100*/ % 256 /*0x0100*/);
    numArray[29] = (byte) (from.Content.AddressStart / 256 /*0x0100*/ % 256 /*0x0100*/);
    numArray[30] = (byte) (from.Content.AddressStart % 256 /*0x0100*/);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>
  /// 生成一个写入字节数据的指令 -&gt; Generate an instruction to write byte data
  /// </summary>
  /// <param name="s7Address">起始地址，示例M100,I100,Q100,DB1.100 -&gt; Start Address, example M100,I100,Q100,DB1.100</param>
  /// <param name="data">原始的字节数据 -&gt; Raw byte data</param>
  /// <param name="msgId">message id informaion</param>
  /// <returns>包含结果对象的报文 -&gt; Message containing the result object</returns>
  public static OperateResult<byte[]> BuildWriteByteCommand(
    S7AddressData s7Address,
    byte[] data,
    int msgId)
  {
    return SiemensS7Net.BuildWriteByteCommand(new S7AddressData[1]
    {
      s7Address
    }, new List<byte[]>() { data }, msgId);
  }

  private static void WriteS7AddressToStream(
    MemoryStream ms,
    S7AddressData add,
    byte writeType,
    int dataLen)
  {
    ms.WriteByte((byte) 18);
    ms.WriteByte((byte) 10);
    ms.WriteByte((byte) 16 /*0x10*/);
    if (add.DataCode == (byte) 6 || add.DataCode == (byte) 7)
    {
      ms.WriteByte((byte) 4);
      ms.WriteByte(BitConverter.GetBytes(dataLen / 2)[1]);
      ms.WriteByte(BitConverter.GetBytes(dataLen / 2)[0]);
    }
    else
    {
      ms.WriteByte(writeType);
      ms.WriteByte(BitConverter.GetBytes(dataLen)[1]);
      ms.WriteByte(BitConverter.GetBytes(dataLen)[0]);
    }
    ms.WriteByte(BitConverter.GetBytes(add.DbBlock)[1]);
    ms.WriteByte(BitConverter.GetBytes(add.DbBlock)[0]);
    ms.WriteByte(add.DataCode);
    ms.WriteByte(BitConverter.GetBytes(add.AddressStart)[2]);
    ms.WriteByte(BitConverter.GetBytes(add.AddressStart)[1]);
    ms.WriteByte(BitConverter.GetBytes(add.AddressStart)[0]);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.BuildWriteByteCommand(HslCommunication.Core.Address.S7AddressData,System.Byte[],System.Int32)" />
  public static OperateResult<byte[]> BuildWriteByteCommand(
    S7AddressData[] s7Address,
    List<byte[]> data,
    int msgId)
  {
    MemoryStream ms = new MemoryStream();
    ms.Write(new byte[7]
    {
      (byte) 3,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 2,
      (byte) 240 /*0xF0*/,
      (byte) 128 /*0x80*/
    });
    ms.WriteByte((byte) 50);
    ms.WriteByte((byte) 1);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 0);
    ms.WriteByte(BitConverter.GetBytes(msgId)[1]);
    ms.WriteByte(BitConverter.GetBytes(msgId)[0]);
    ms.WriteByte(BitConverter.GetBytes(s7Address.Length * 12 + 2)[1]);
    ms.WriteByte(BitConverter.GetBytes(s7Address.Length * 12 + 2)[0]);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 5);
    ms.WriteByte((byte) s7Address.Length);
    for (int index = 0; index < s7Address.Length; ++index)
      SiemensS7Net.WriteS7AddressToStream(ms, s7Address[index], (byte) 2, data[index] == null ? 0 : data[index].Length);
    int length = (int) ms.Length;
    for (int index = 0; index < data.Count; ++index)
    {
      ms.WriteByte((byte) 0);
      ms.WriteByte((byte) 4);
      if (data[index] != null)
      {
        ms.WriteByte(BitConverter.GetBytes(data[index].Length * 8)[1]);
        ms.WriteByte(BitConverter.GetBytes(data[index].Length * 8)[0]);
        ms.Write(data[index]);
        if (index < data.Count - 1 && data[index].Length % 2 == 1)
          ms.WriteByte((byte) 0);
      }
      else
      {
        ms.WriteByte((byte) 0);
        ms.WriteByte((byte) 0);
      }
    }
    byte[] array = ms.ToArray();
    array[2] = (byte) (array.Length / 256 /*0x0100*/);
    array[3] = (byte) (array.Length % 256 /*0x0100*/);
    array[15] = BitConverter.GetBytes(array.Length - length)[1];
    array[16 /*0x10*/] = BitConverter.GetBytes(array.Length - length)[0];
    return OperateResult.CreateSuccessResult<byte[]>(array);
  }

  /// <summary>
  /// 生成一个写入位数据的指令 -&gt; Generate an instruction to write bit data
  /// </summary>
  /// <param name="address">起始地址，示例M100,I100,Q100,DB1.100 -&gt; Start Address, example M100,I100,Q100,DB1.100</param>
  /// <param name="data">是否通断 -&gt; Power on or off</param>
  /// <param name="msgId">message id informaion</param>
  /// <returns>包含结果对象的报文 -&gt; Message containing the result object</returns>
  public static OperateResult<byte[]> BuildWriteBitCommand(string address, bool data, int msgId)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    byte[] numArray1 = new byte[1]
    {
      data ? (byte) 1 : (byte) 0
    };
    byte[] numArray2 = new byte[35 + numArray1.Length];
    numArray2[0] = (byte) 3;
    numArray2[1] = (byte) 0;
    numArray2[2] = (byte) ((35 + numArray1.Length) / 256 /*0x0100*/);
    numArray2[3] = (byte) ((35 + numArray1.Length) % 256 /*0x0100*/);
    numArray2[4] = (byte) 2;
    numArray2[5] = (byte) 240 /*0xF0*/;
    numArray2[6] = (byte) 128 /*0x80*/;
    numArray2[7] = (byte) 50;
    numArray2[8] = (byte) 1;
    numArray2[9] = (byte) 0;
    numArray2[10] = (byte) 0;
    numArray2[11] = BitConverter.GetBytes(msgId)[1];
    numArray2[12] = BitConverter.GetBytes(msgId)[0];
    numArray2[13] = (byte) 0;
    numArray2[14] = (byte) 14;
    numArray2[15] = (byte) ((4 + numArray1.Length) / 256 /*0x0100*/);
    numArray2[16 /*0x10*/] = (byte) ((4 + numArray1.Length) % 256 /*0x0100*/);
    numArray2[17] = (byte) 5;
    numArray2[18] = (byte) 1;
    numArray2[19] = (byte) 18;
    numArray2[20] = (byte) 10;
    numArray2[21] = (byte) 16 /*0x10*/;
    numArray2[22] = (byte) 1;
    numArray2[23] = (byte) (numArray1.Length / 256 /*0x0100*/);
    numArray2[24] = (byte) (numArray1.Length % 256 /*0x0100*/);
    numArray2[25] = (byte) ((uint) from.Content.DbBlock / 256U /*0x0100*/);
    numArray2[26] = (byte) ((uint) from.Content.DbBlock % 256U /*0x0100*/);
    numArray2[27] = from.Content.DataCode;
    numArray2[28] = (byte) (from.Content.AddressStart / 256 /*0x0100*/ / 256 /*0x0100*/);
    numArray2[29] = (byte) (from.Content.AddressStart / 256 /*0x0100*/);
    numArray2[30] = (byte) (from.Content.AddressStart % 256 /*0x0100*/);
    if (from.Content.DataCode == (byte) 28)
    {
      numArray2[31 /*0x1F*/] = (byte) 0;
      numArray2[32 /*0x20*/] = (byte) 9;
    }
    else
    {
      numArray2[31 /*0x1F*/] = (byte) 0;
      numArray2[32 /*0x20*/] = (byte) 3;
    }
    numArray2[33] = (byte) (numArray1.Length / 256 /*0x0100*/);
    numArray2[34] = (byte) (numArray1.Length % 256 /*0x0100*/);
    numArray1.CopyTo((Array) numArray2, 35);
    return OperateResult.CreateSuccessResult<byte[]>(numArray2);
  }

  private static OperateResult<byte[]> AnalysisReadByte(S7AddressData[] s7Addresses, byte[] content)
  {
    try
    {
      int length = 0;
      for (int index = 0; index < s7Addresses.Length; ++index)
      {
        if (s7Addresses[index].DataCode == (byte) 31 /*0x1F*/ || s7Addresses[index].DataCode == (byte) 30)
          length += (int) s7Addresses[index].Length * 2;
        else
          length += (int) s7Addresses[index].Length;
      }
      if (content.Length < 21 || (int) content[20] != s7Addresses.Length)
        return new OperateResult<byte[]>($"{StringResources.Language.SiemensDataLengthCheckFailed} Msg:{SoftBasic.ByteToHexString(content, ' ')}");
      byte[] destinationArray = new byte[length];
      int index1 = 0;
      int destinationIndex = 0;
      for (int index2 = 21; index2 < content.Length; ++index2)
      {
        if (index2 + 1 < content.Length)
        {
          if (content[index2] == byte.MaxValue && content[index2 + 1] == (byte) 4)
          {
            Array.Copy((Array) content, index2 + 4, (Array) destinationArray, destinationIndex, (int) s7Addresses[index1].Length);
            index2 += (int) s7Addresses[index1].Length + 3;
            destinationIndex += (int) s7Addresses[index1].Length;
            ++index1;
          }
          else if (content[index2] == byte.MaxValue && content[index2 + 1] == (byte) 9)
          {
            int num = (int) content[index2 + 2] * 256 /*0x0100*/ + (int) content[index2 + 3];
            if (num % 3 == 0)
            {
              for (int index3 = 0; index3 < num / 3; ++index3)
              {
                Array.Copy((Array) content, index2 + 5 + 3 * index3, (Array) destinationArray, destinationIndex, 2);
                destinationIndex += 2;
              }
            }
            else
            {
              for (int index4 = 0; index4 < num / 5; ++index4)
              {
                Array.Copy((Array) content, index2 + 7 + 5 * index4, (Array) destinationArray, destinationIndex, 2);
                destinationIndex += 2;
              }
            }
            index2 += num + 4;
            ++index1;
          }
          else
          {
            if (content[index2] == (byte) 5 && content[index2 + 1] == (byte) 0)
              return new OperateResult<byte[]>((int) content[index2], StringResources.Language.SiemensReadLengthOverPlcAssign);
            if (content[index2] == (byte) 6 && content[index2 + 1] == (byte) 0)
              return new OperateResult<byte[]>((int) content[index2], StringResources.Language.SiemensError0006);
            if (content[index2] == (byte) 10 && content[index2 + 1] == (byte) 0)
              return new OperateResult<byte[]>((int) content[index2], StringResources.Language.SiemensError000A);
          }
        }
      }
      return OperateResult.CreateSuccessResult<byte[]>(destinationArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"AnalysisReadByte failed: {ex.Message}{Environment.NewLine} Msg:{SoftBasic.ByteToHexString(content, ' ')}");
    }
  }

  private static OperateResult AnalysisWrite(byte[] content)
  {
    try
    {
      if (content == null || content.Length < 22)
        return new OperateResult($"{StringResources.Language.UnknownError} Msg:{SoftBasic.ByteToHexString(content, ' ')}");
      int num = (int) content[20];
      for (int index = 0; index < num; ++index)
      {
        byte err = content[21 + index];
        switch (err)
        {
          case 5:
            return new OperateResult((int) err, StringResources.Language.SiemensReadLengthOverPlcAssign);
          case 6:
            return new OperateResult((int) err, StringResources.Language.SiemensError0006);
          case 10:
            return new OperateResult((int) err, StringResources.Language.SiemensError000A);
          case byte.MaxValue:
            continue;
          default:
            return new OperateResult((int) err, $"{StringResources.Language.SiemensWriteError}{err.ToString()} Msg:{SoftBasic.ByteToHexString(content, ' ')}");
        }
      }
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<byte[]>($"AnalysisWrite failed: {ex.Message}{Environment.NewLine} Msg:{SoftBasic.ByteToHexString(content, ' ')}");
    }
  }
}
