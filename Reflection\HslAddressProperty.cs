﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslAddressProperty
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Reflection;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>Hsl相关地址的属性信息</summary>
public class HslAddressProperty
{
  /// <summary>该属性绑定的地址特性</summary>
  public HslDeviceAddressAttribute DeviceAddressAttribute { get; set; }

  /// <summary>地址绑定的属性信息</summary>
  public PropertyInfo PropertyInfo { get; set; }

  /// <summary>起始的字节偏移信息</summary>
  public int ByteOffset { get; set; }

  /// <summary>读取的字节的长度信息</summary>
  public int ByteLength { get; set; }

  /// <summary>缓存的数据对象</summary>
  public byte[] Buffer { get; set; }
}
