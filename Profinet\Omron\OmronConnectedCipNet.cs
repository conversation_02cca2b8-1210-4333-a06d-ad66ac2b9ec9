﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronConnectedCipNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.AllenBradley;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// 基于连接的对象访问的CIP协议的实现，用于对Omron PLC进行标签的数据读写，对数组，多维数组进行读写操作，支持的数据类型请参照API文档手册。<br />
/// The implementation of the CIP protocol based on connected object access is used to read and write tag data to Omron PLC,
/// and read and write arrays and multidimensional arrays. For the supported data types, please refer to the API documentation manual.
/// </summary>
/// <remarks>
/// 支持普通标签的读写，类型要和标签对应上。如果标签是数组，例如 A 是 INT[0...9] 那么Read("A", 1)，返回的是10个short所有字节数组。
/// 如果需要返回10个长度的short数组，请调用 ReadInt16("A[0], 10"); 地址必须写 "A[0]"，不能写 "A" , 如需要读取结构体，参考 <see cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadStruct``1(System.String)" />
/// </remarks>
/// <example>
/// 首先说明支持的类型地址，在PLC里支持了大量的类型，有些甚至在C#里是不存在的。现在做个统一的声明
/// <list type="table">
///   <listheader>
///     <term>PLC类型</term>
///     <term>含义</term>
///     <term>代号</term>
///     <term>C# 类型</term>
///     <term>备注</term>
///   </listheader>
///   <item>
///     <term>bool</term>
///     <term>位类型数据</term>
///     <term>0xC1</term>
///     <term>bool</term>
///     <term></term>
///   </item>
///   <item>
///     <term>SINT</term>
///     <term>8位的整型</term>
///     <term>0xC2</term>
///     <term>sbyte</term>
///     <term>有符号8位很少用，HSL直接用byte</term>
///   </item>
///   <item>
///     <term>USINT</term>
///     <term>无符号8位的整型</term>
///     <term>0xC6</term>
///     <term>byte</term>
///     <term>如需要，使用<see cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteTag(System.String,System.UInt16,System.Byte[],System.Int32)" />实现</term>
///   </item>
///   <item>
///     <term>BYTE</term>
///     <term>8位字符数据</term>
///     <term>0xD1</term>
///     <term>byte</term>
///     <term>如需要，使用<see cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteTag(System.String,System.UInt16,System.Byte[],System.Int32)" />实现</term>
///   </item>
///   <item>
///     <term>INT</term>
///     <term>16位的整型</term>
///     <term>0xC3</term>
///     <term>short</term>
///     <term></term>
///   </item>
///   <item>
///     <term>UINT</term>
///     <term>无符号的16位整型</term>
///     <term>0xC7</term>
///     <term>ushort</term>
///     <term></term>
///   </item>
///   <item>
///     <term>DINT</term>
///     <term>32位的整型</term>
///     <term>0xC4</term>
///     <term>int</term>
///     <term></term>
///   </item>
///   <item>
///     <term>UDINT</term>
///     <term>无符号的32位整型</term>
///     <term>0xC8</term>
///     <term>uint</term>
///     <term></term>
///   </item>
///   <item>
///     <term>LINT</term>
///     <term>64位的整型</term>
///     <term>0xC5</term>
///     <term>long</term>
///     <term></term>
///   </item>
///   <item>
///     <term>ULINT</term>
///     <term>无符号的64位的整型</term>
///     <term>0xC9</term>
///     <term>ulong</term>
///     <term></term>
///   </item>
///   <item>
///     <term>REAL</term>
///     <term>单精度浮点数</term>
///     <term>0xCA</term>
///     <term>float</term>
///     <term></term>
///   </item>
///   <item>
///     <term>DOUBLE</term>
///     <term>双精度浮点数</term>
///     <term>0xCB</term>
///     <term>double</term>
///     <term></term>
///   </item>
///   <item>
///     <term>STRING</term>
///     <term>字符串数据</term>
///     <term>0xD0</term>
///     <term>string</term>
///     <term>前两个字节为字符长度</term>
///   </item>
///   <item>
///     <term>8bit string BYTE</term>
///     <term>8位的字符串</term>
///     <term>0xD1</term>
///     <term></term>
///     <term>本质是BYTE数组</term>
///   </item>
///   <item>
///     <term>16bit string WORD</term>
///     <term>16位的字符串</term>
///     <term>0xD2</term>
///     <term></term>
///     <term>本质是WORD数组，可存放中文</term>
///   </item>
///   <item>
///     <term>32bit string DWORD</term>
///     <term>32位的字符串</term>
///     <term>0xD2</term>
///     <term></term>
///     <term>本质是DWORD数组，可存放中文</term>
///   </item>
/// </list>
/// 在读写操作之前，先看看怎么实例化和连接PLC<br />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage" title="实例化及连接示例" />
/// 现在来说明以下具体的操作细节。我们假设有如下的变量：<br />
/// CESHI_A       SINT<br />
/// CESHI_B       BYTE<br />
/// CESHI_C       INT<br />
/// CESHI_D       UINT<br />
/// CESHI_E       SINT[0..9]<br />
/// CESHI_F       BYTE[0..9]<br />
/// CESHI_G       INT[0..9]<br />
/// CESHI_H       UINT[0..9]<br />
/// CESHI_I       INT[0..511]<br />
/// CESHI_J       STRING[12]<br />
/// ToPc_ID1      ARRAY[0..99] OF STRING[20]<br />
/// CESHI_O       BOOL<br />
/// CESHI_P       BOOL[0..31]<br />
/// 对 CESHI_A 来说，读写这么操作
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage2" title="读写示例" />
/// 对于 CESHI_B 来说，写入的操作有点特殊
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage3" title="读写示例" />
/// 对于 CESHI_C, CESHI_D 来说，就是 ReadInt16(string address) , Write( string address, short value ) 和 ReadUInt16(string address) 和 Write( string address, ushort value ) 差别不大。
/// 所以我们着重来看看数组的情况，以 CESHI_G 标签为例子:<br />
/// 情况一，我想一次读取这个标签所有的字节数组（当长度满足的情况下，会一次性返回数据）
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage4" title="读写示例" />
/// 情况二，我想读取第3个数，或是第6个数开始，一共5个数
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage5" title="读写示例" />
/// 其他的数组情况都是类似的，我们来看看字符串 CESHI_J 变量
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage6" title="读写示例" />
/// 对于 bool 变量来说，就是 ReadBool("CESHI_O") 和 Write("CESHI_O", true) 操作，如果是bool数组，就不一样了
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage7" title="读写示例" />
/// 最后我们来看看结构体的操作，假设我们有个结构体<br />
/// MyData.Code     STRING(12)<br />
/// MyData.Value1   INT<br />
/// MyData.Value2   INT<br />
/// MyData.Value3   REAL<br />
/// MyData.Value4   INT<br />
/// MyData.Value5   INT<br />
/// MyData.Value6   INT[0..3]<br />
/// 因为bool比较复杂，暂时不考虑。要读取上述的结构体，我们需要定义结构一样的数据
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage8" title="结构体" />
/// 定义好后，我们再来读取就很简单了。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage9" title="读写示例" />
/// </example>
public class OmronConnectedCipNet : NetworkConnectedCip, IReadWriteCip, IReadWriteNet
{
  /// <summary>实例化一个默认的对象</summary>
  public OmronConnectedCipNet()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>根据指定的IP及端口来实例化这个连接对象</summary>
  /// <param name="ipAddress">PLC的Ip地址</param>
  /// <param name="port">PLC的端口号信息</param>
  public OmronConnectedCipNet(string ipAddress, int port = 44818)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override byte[] GetLargeForwardOpen(ushort connectionID)
  {
    uint num = 2164129793U + (uint) connectionID;
    this.TOConnectionId = num;
    byte[] hexBytes = "\r\n00 00 00 00 00 00 02 00 00 00 00 00 b2 00 34 00\r\n5b 02 20 06 24 01 0e 9c 02 00 00 80 01 00 fe 80\r\n02 00 1b 05 30 a7 2b 03 02 00 00 00 80 84 1e 00\r\ncc 07 00 42 80 84 1e 00 cc 07 00 42 a3 03 20 02\r\n24 01 2c 01".ToHexBytes();
    BitConverter.GetBytes(2147483650U /*0x80000002*/ + (uint) connectionID).CopyTo((Array) hexBytes, 24);
    BitConverter.GetBytes(num).CopyTo((Array) hexBytes, 28);
    BitConverter.GetBytes((ushort) (2U + (uint) connectionID)).CopyTo((Array) hexBytes, 32 /*0x20*/);
    BitConverter.GetBytes((ushort) 4105).CopyTo((Array) hexBytes, 34);
    HslHelper.HslRandom.GetBytes(4).CopyTo((Array) hexBytes, 36);
    hexBytes[40] = this.ConnectionTimeoutMultiplier;
    return hexBytes;
  }

  /// <summary>
  /// 当前产品的型号信息<br />
  /// Model information of the current product
  /// </summary>
  public string ProductName { get; private set; }

  /// <summary>
  /// 获取或设置不通信时超时的时间，默认02，为 32 秒，设置06 时为8分多，计算方法为 (2的x次方乘以8) 的秒数<br />
  /// Get or set the timeout time when there is no communication. The default is 02, which is 32 seconds, and when 06 is set, it is more than 8 minutes. The calculation method is (2 times the power of x times 8) seconds.
  /// </summary>
  public byte ConnectionTimeoutMultiplier { get; set; } = 2;

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult operateResult1 = base.InitializationOnConnect();
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, this.SessionHandle, this.GetAttributeAll()), true, false);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    if (operateResult2.Content.Length > 59 && operateResult2.Content.Length >= 59 + (int) operateResult2.Content[58])
      this.ProductName = Encoding.UTF8.GetString(operateResult2.Content, 59, (int) operateResult2.Content[58]);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    OperateResult ini = await base.InitializationOnConnectAsync().ConfigureAwait(false);
    if (!ini.IsSuccess)
      return ini;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.CommunicationPipe, AllenBradleyHelper.PackRequestHeader((ushort) 111, this.SessionHandle, this.GetAttributeAll()), true, false).ConfigureAwait(false);
    if (!read.IsSuccess)
      return (OperateResult) read;
    if (read.Content.Length > 59 && read.Content.Length >= 59 + (int) read.Content[58])
      this.ProductName = Encoding.UTF8.GetString(read.Content, 59, (int) read.Content[58]);
    return OperateResult.CreateSuccessResult();
  }

  private byte[] GetAttributeAll()
  {
    return "00 00 00 00 00 00 02 00 00 00 00 00 b2 00 06 00 01 02 20 01 24 01".ToHexBytes();
  }

  private OperateResult<byte[]> BuildReadCommand(string[] address, ushort[] length)
  {
    try
    {
      List<byte[]> numArrayList = new List<byte[]>();
      for (int index = 0; index < address.Length; ++index)
        numArrayList.Add(AllenBradleyHelper.PackRequsetRead(address[index], (int) length[index], true));
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommandService(numArrayList.ToArray()));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address Wrong:" + ex.Message);
    }
  }

  private OperateResult<byte[]> BuildWriteCommand(
    string address,
    ushort typeCode,
    byte[] data,
    int length = 1)
  {
    try
    {
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommandService(AllenBradleyHelper.PackRequestWrite(address, typeCode, data, length, true)));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address Wrong:" + ex.Message);
    }
  }

  private OperateResult<byte[], ushort, bool> ReadWithType(string[] address, ushort[] length)
  {
    OperateResult<byte[]> result1 = this.BuildReadCommand(address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) result2);
    OperateResult result3 = AllenBradleyHelper.CheckResponse(result2.Content);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<byte[], ushort, bool>(result3) : NetworkConnectedCip.ExtractActualData(result2.Content, true);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadCipFromServer(System.Byte[][])" />
  public OperateResult<byte[]> ReadCipFromServer(params byte[][] cips)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.PackCommandService(((IEnumerable<byte[]>) cips).ToArray<byte[]>()));
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult result = AllenBradleyHelper.CheckResponse(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult<byte[]>(operateResult.Content);
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取一个结构体的对象，需要事先根据实际的数据点位定义好结构体，然后使用本方法进行读取，当结构体定义不对时，本方法将会读取失败<br />
  /// <b>[Authorization]</b> To read a structure object, you need to define the structure in advance according to the actual data points,
  /// and then use this method to read. When the structure definition is incorrect, this method will fail to read
  /// </summary>
  /// <remarks>本方法需要商业授权支持，具体的使用方法，参考API文档的示例代码</remarks>
  /// <example>
  /// 我们来看看结构体的操作，假设我们有个结构体<br />
  /// MyData.Code     STRING(12)<br />
  /// MyData.Value1   INT<br />
  /// MyData.Value2   INT<br />
  /// MyData.Value3   REAL<br />
  /// MyData.Value4   INT<br />
  /// MyData.Value5   INT<br />
  /// MyData.Value6   INT[0..3]<br />
  /// 因为bool比较复杂，暂时不考虑。要读取上述的结构体，我们需要定义结构一样的数据
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage8" title="结构体" />
  /// 定义好后，我们再来读取就很简单了。
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\OmronConnectedCipNetSample.cs" region="Usage9" title="读写示例" />
  /// </example>
  /// <typeparam name="T">结构体的类型</typeparam>
  /// <param name="address">结构体对象的地址</param>
  /// <returns>是否读取成功的对象</returns>
  public OperateResult<T> ReadStruct<T>(string address) where T : struct
  {
    OperateResult<byte[]> result = this.Read(address, (ushort) 1);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<T>((OperateResult) result) : HslHelper.ByteArrayToStruct<T>(result.Content.RemoveBegin<byte>(2));
  }

  private async Task<OperateResult<byte[], ushort, bool>> ReadWithTypeAsync(
    string[] address,
    ushort[] length)
  {
    OperateResult<byte[]> command = this.BuildReadCommand(address, length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) command);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<byte[], ushort, bool>((OperateResult) read);
    OperateResult check = AllenBradleyHelper.CheckResponse(read.Content);
    return check.IsSuccess ? NetworkConnectedCip.ExtractActualData(read.Content, true) : OperateResult.CreateFailedResult<byte[], ushort, bool>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadCipFromServer(System.Byte[][])" />
  public async Task<OperateResult<byte[]>> ReadCipFromServerAsync(params byte[][] cips)
  {
    byte[] command = this.PackCommandService(((IEnumerable<byte[]>) cips).ToArray<byte[]>());
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command);
    if (!read.IsSuccess)
      return read;
    OperateResult check = AllenBradleyHelper.CheckResponse(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content) : OperateResult.CreateFailedResult<byte[]>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadStruct``1(System.String)" />
  public async Task<OperateResult<T>> ReadStructAsync<T>(string address) where T : struct
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 1);
    OperateResult<T> operateResult = read.IsSuccess ? HslHelper.ByteArrayToStruct<T>(read.Content.RemoveBegin<byte>(2)) : OperateResult.CreateFailedResult<T>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <summary>获取传递的最大长度的字节信息</summary>
  /// <returns>字节长度</returns>
  protected virtual int GetMaxTransferBytes() => 1988;

  private int GetLengthFromRemain(ushort dataType, int length)
  {
    if (dataType == (ushort) 193 || dataType == (ushort) 194 || dataType == (ushort) 198 || dataType == (ushort) 211)
      return Math.Min(length, this.GetMaxTransferBytes());
    if (dataType == (ushort) 199 || dataType == (ushort) 195)
      return Math.Min(length, this.GetMaxTransferBytes() / 2);
    return dataType == (ushort) 196 || dataType == (ushort) 200 || dataType == (ushort) 202 ? Math.Min(length, this.GetMaxTransferBytes() / 4) : Math.Min(length, this.GetMaxTransferBytes() / 8);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    HslHelper.ExtractParameter(ref address, "type", 0);
    if (length == (ushort) 1)
    {
      OperateResult<byte[], ushort, bool> result = this.ReadWithType(new string[1]
      {
        address
      }, new ushort[1]{ length });
      return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(result.Content1);
    }
    int num1 = 0;
    int num2 = 0;
    string format = "[{0}]";
    List<byte> byteList = new List<byte>();
    Match match1 = Regex.Match(address, "\\[[0-9]+\\]$");
    if (match1.Success)
    {
      address = address.Remove(match1.Index, match1.Length);
      num2 = int.Parse(match1.Value.Substring(1, match1.Value.Length - 2));
    }
    else
    {
      Match match2 = Regex.Match(address, "\\[[0-9]+,[0-9]+\\]$");
      if (match2.Success)
      {
        address = address.Remove(match2.Index, match2.Length);
        string s = Regex.Matches(match2.Value, "[0-9]+")[1].Value;
        format = match2.Value.Replace(s + "]", "{0}]");
        num2 = int.Parse(s);
      }
    }
    ushort dataType = 0;
    while (num1 < (int) length)
    {
      if (num1 == 0)
      {
        ushort num3 = Math.Min(length, (ushort) 248);
        OperateResult<byte[], ushort, bool> result = this.ReadWithType(new string[1]
        {
          address + string.Format(format, (object) num2)
        }, new ushort[1]{ num3 });
        if (!result.IsSuccess)
          return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
        dataType = result.Content2;
        num1 += (int) num3;
        num2 += (int) num3;
        byteList.AddRange((IEnumerable<byte>) result.Content1);
      }
      else
      {
        ushort lengthFromRemain = (ushort) this.GetLengthFromRemain(dataType, (int) length - num1);
        OperateResult<byte[], ushort, bool> result = this.ReadWithType(new string[1]
        {
          address + string.Format(format, (object) num2)
        }, new ushort[1]{ lengthFromRemain });
        if (!result.IsSuccess)
          return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
        num1 += (int) lengthFromRemain;
        num2 += (int) lengthFromRemain;
        byteList.AddRange((IEnumerable<byte>) result.Content1);
      }
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Read(System.String[],System.UInt16[])" />
  [HslMqttApi("ReadMultiAddress", "")]
  public OperateResult<byte[]> Read(string[] address, ushort[] length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[], ushort, bool> result = this.ReadWithType(address, length);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(result.Content1);
  }

  /// <summary>
  /// 读取bool数据信息，如果读取的是单bool变量，就直接写变量名，如果是 bool 数组，就 <br />
  /// Read a single bool data information, if it is a single bool variable, write the variable name directly,
  /// if it is a value of a bool array composed of int, it is always accessed with "i=" at the beginning, for example, "i=A[0]"
  /// </summary>
  /// <param name="address">节点的名称 -&gt; Name of the node </param>
  /// <param name="length">读取的数组长度信息</param>
  /// <returns>带有结果对象的结果数据 -&gt; Result data with result info </returns>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (length == (ushort) 1 && !Regex.IsMatch(address, "\\[[0-9]+\\]$"))
    {
      OperateResult<byte[]> result = this.Read(address, length);
      return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(result.Content));
    }
    OperateResult<byte[]> result1 = this.Read(address, length);
    return !result1.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result1) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) result1.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).Take<bool>((int) length).ToArray<bool>());
  }

  /// <summary>
  /// 读取PLC的byte类型的数据<br />
  /// Read the byte type of PLC data
  /// </summary>
  /// <param name="address">节点的名称 -&gt; Name of the node </param>
  /// <returns>带有结果对象的结果数据 -&gt; Result data with result info </returns>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadTag(System.String,System.UInt16)" />
  public OperateResult<ushort, byte[]> ReadTag(string address, ushort length = 1)
  {
    OperateResult<byte[], ushort, bool> result = this.ReadWithType(new string[1]
    {
      address
    }, new ushort[1]{ length });
    return !result.IsSuccess ? OperateResult.CreateFailedResult<ushort, byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<ushort, byte[]>(result.Content2, result.Content1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice)" />
  [HslMqttApi(Description = "获取PLC的型号信息")]
  public OperateResult<string> ReadPlcType()
  {
    return OperateResult.CreateSuccessResult<string>(this.ProductName);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (length == (ushort) 1 && !Regex.IsMatch(address, "\\[[0-9]+\\]$"))
    {
      OperateResult<byte[]> read = await this.ReadAsync(address, length);
      return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(read.Content)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    }
    OperateResult<byte[]> read1 = await this.ReadAsync(address, length);
    return read1.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) read1.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).Take<bool>((int) length).ToArray<bool>()) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read1);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    HslHelper.ExtractParameter(ref address, "type", 0);
    if (length == (ushort) 1)
    {
      OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(new string[1]
      {
        address
      }, new ushort[1]{ length });
      return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content1) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
    }
    int count = 0;
    int index = 0;
    string format = "[{0}]";
    List<byte> array = new List<byte>();
    Match match = Regex.Match(address, "\\[[0-9]+\\]$");
    if (match.Success)
    {
      address = address.Remove(match.Index, match.Length);
      index = int.Parse(match.Value.Substring(1, match.Value.Length - 2));
    }
    else
    {
      Match match2 = Regex.Match(address, "\\[[0-9]+,[0-9]+\\]$");
      if (match2.Success)
      {
        address = address.Remove(match2.Index, match2.Length);
        string index2 = Regex.Matches(match2.Value, "[0-9]+")[1].Value;
        format = match2.Value.Replace(index2 + "]", "{0}]");
        index = int.Parse(index2);
        index2 = (string) null;
      }
      match2 = (Match) null;
    }
    ushort dataType = 0;
    while (count < (int) length)
    {
      if (count == 0)
      {
        ushort first = Math.Min(length, (ushort) 248);
        OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(new string[1]
        {
          address + string.Format(format, (object) index)
        }, new ushort[1]{ first });
        if (!read.IsSuccess)
          return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
        dataType = read.Content2;
        count += (int) first;
        index += (int) first;
        array.AddRange((IEnumerable<byte>) read.Content1);
        read = (OperateResult<byte[], ushort, bool>) null;
      }
      else
      {
        ushort len = (ushort) this.GetLengthFromRemain(dataType, (int) length - count);
        OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(new string[1]
        {
          address + string.Format(format, (object) index)
        }, new ushort[1]{ len });
        if (!read.IsSuccess)
          return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
        count += (int) len;
        index += (int) len;
        array.AddRange((IEnumerable<byte>) read.Content1);
        read = (OperateResult<byte[], ushort, bool>) null;
      }
    }
    return OperateResult.CreateSuccessResult<byte[]>(array.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Read(System.String[],System.UInt16[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address, ushort[] length)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(address, length);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(read.Content1) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.ReadTag(System.String,System.UInt16)" />
  public async Task<OperateResult<ushort, byte[]>> ReadTagAsync(string address, ushort length = 1)
  {
    OperateResult<byte[], ushort, bool> read = await this.ReadWithTypeAsync(new string[1]
    {
      address
    }, new ushort[1]{ length });
    OperateResult<ushort, byte[]> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<ushort, byte[]>(read.Content2, read.Content1) : OperateResult.CreateFailedResult<ushort, byte[]>((OperateResult) read);
    read = (OperateResult<byte[], ushort, bool>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return this.WriteTag(address, (ushort) 209, value, HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyNet.WriteTag(System.String,System.UInt16,System.Byte[],System.Int32)" />
  public virtual OperateResult WriteTag(string address, ushort typeCode, byte[] value, int length = 1)
  {
    typeCode = (ushort) HslHelper.ExtractParameter(ref address, "type", (int) typeCode);
    OperateResult<byte[]> operateResult1 = this.BuildWriteCommand(address, typeCode, value, length);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult result = AllenBradleyHelper.CheckResponse(operateResult2.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>(result) : (OperateResult) AllenBradleyHelper.ExtractActualData(operateResult2.Content, false);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 209, value, HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteTag(System.String,System.UInt16,System.Byte[],System.Int32)" />
  public virtual async Task<OperateResult> WriteTagAsync(
    string address,
    ushort typeCode,
    byte[] value,
    int length = 1)
  {
    typeCode = (ushort) HslHelper.ExtractParameter(ref address, "type", (int) typeCode);
    OperateResult<byte[]> command = this.BuildWriteCommand(address, typeCode, value, length);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = AllenBradleyHelper.CheckResponse(read.Content);
    return check.IsSuccess ? (OperateResult) AllenBradleyHelper.ExtractActualData(read.Content, false) : (OperateResult) OperateResult.CreateFailedResult<byte[]>(check);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt16Array", "")]
  public override OperateResult<short[]> ReadInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<short[]>(this.Read(address, length), (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt16Array", "")]
  public override OperateResult<ushort[]> ReadUInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(this.Read(address, length), (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt32Array", "")]
  public override OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<int[]>(this.Read(address, length), (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt32Array", "")]
  public override OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<uint[]>(this.Read(address, length), (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadFloatArray", "")]
  public override OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<float[]>(this.Read(address, length), (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt64Array", "")]
  public override OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<long[]>(this.Read(address, length), (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt64Array", "")]
  public override OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(this.Read(address, length), (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<double[]>(this.Read(address, length), (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public OperateResult<string> ReadString(string address)
  {
    return this.ReadString(address, (ushort) 1, Encoding.UTF8);
  }

  /// <summary>
  /// 读取字符串数据，默认为UTF-8编码<br />
  /// Read string data, default is UTF-8 encoding
  /// </summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">数据长度</param>
  /// <returns>带有成功标识的string数据</returns>
  /// <example>
  /// 以下为三菱的连接对象示例，其他的设备读写情况参照下面的代码：
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkDeviceBase.cs" region="ReadString" title="String类型示例" />
  /// </example>
  [HslMqttApi("ReadString", "")]
  public override OperateResult<string> ReadString(string address, ushort length)
  {
    return this.ReadString(address, length, Encoding.UTF8);
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> result = this.Read(address, length);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result) : this.ExtraStringContent(result.Content, encoding);
  }

  private OperateResult<string> ExtraStringContent(byte[] content, Encoding encoding)
  {
    try
    {
      if (content.Length < 2)
        return OperateResult.CreateSuccessResult<string>(encoding.GetString(content));
      int count = (int) this.ByteTransform.TransUInt16(content, 0);
      return OperateResult.CreateSuccessResult<string>(encoding.GetString(content, 2, count));
    }
    catch (Exception ex)
    {
      return new OperateResult<string>($"Parse string failed: {ex.Message} Source: {content.ToHexString(' ')}");
    }
  }

  /// <inheritdoc />
  public override async Task<OperateResult<short[]>> ReadInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<short[]>(result, (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ushort[]>> ReadUInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(result, (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<int[]>(result, (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<uint[]>(result, (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<float[]>(result, (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<long[]>(result, (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(result, (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<double[]>(result, (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc />
  public async Task<OperateResult<string>> ReadStringAsync(string address)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, (ushort) 1, Encoding.UTF8);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadString(System.String,System.UInt16)" />
  public override async Task<OperateResult<string>> ReadStringAsync(string address, ushort length)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, length, Encoding.UTF8);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, length);
    OperateResult<string> operateResult = read.IsSuccess ? this.ExtraStringContent(read.Content, encoding) : OperateResult.CreateFailedResult<string>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt16Array", "")]
  public override OperateResult Write(string address, short[] values)
  {
    return this.WriteTag(address, (ushort) 195, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt16Array", "")]
  public override OperateResult Write(string address, ushort[] values)
  {
    return this.WriteTag(address, (ushort) 199, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt32Array", "")]
  public override OperateResult Write(string address, int[] values)
  {
    return this.WriteTag(address, (ushort) 196, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt32Array", "")]
  public override OperateResult Write(string address, uint[] values)
  {
    return this.WriteTag(address, (ushort) 200, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteFloatArray", "")]
  public override OperateResult Write(string address, float[] values)
  {
    return this.WriteTag(address, (ushort) 202, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteInt64Array", "")]
  public override OperateResult Write(string address, long[] values)
  {
    return this.WriteTag(address, (ushort) 197, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteUInt64Array", "")]
  public override OperateResult Write(string address, ulong[] values)
  {
    return this.WriteTag(address, (ushort) 201, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    return this.WriteTag(address, (ushort) 203, this.ByteTransform.TransByte(values), values.Length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteString", "")]
  public override OperateResult Write(string address, string value)
  {
    return this.Write(address, value, Encoding.UTF8);
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    byte[] numArray = string.IsNullOrEmpty(value) ? new byte[0] : encoding.GetBytes(value);
    return this.WriteTag(address, (ushort) 208 /*0xD0*/, SoftBasic.SpliceArray<byte>(BitConverter.GetBytes((ushort) numArray.Length), numArray), 1);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    string address1 = address;
    byte[] numArray;
    if (!value)
      numArray = new byte[2];
    else
      numArray = new byte[2]{ byte.MaxValue, byte.MaxValue };
    return this.WriteTag(address1, (ushort) 193, numArray, 1);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return this.WriteTag(address, (ushort) 193, ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.WriteTag(address, (ushort) 194, new byte[1]
    {
      value
    }, 1);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Int16[])" />
  public override async Task<OperateResult> WriteAsync(string address, short[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 195, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.UInt16[])" />
  public override async Task<OperateResult> WriteAsync(string address, ushort[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 199, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Int32[])" />
  public override async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 196, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.UInt32[])" />
  public override async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 200, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Single[])" />
  public override async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 202, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Int64[])" />
  public override async Task<OperateResult> WriteAsync(string address, long[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 197, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.UInt64[])" />
  public override async Task<OperateResult> WriteAsync(string address, ulong[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 201, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Double[])" />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 203, this.ByteTransform.TransByte(values), values.Length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.String)" />
  public override async Task<OperateResult> WriteAsync(string address, string value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value, Encoding.UTF8);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    byte[] buffer = string.IsNullOrEmpty(value) ? new byte[0] : encoding.GetBytes(value);
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 208 /*0xD0*/, SoftBasic.SpliceArray<byte>(BitConverter.GetBytes((ushort) buffer.Length), buffer), 1);
    buffer = (byte[]) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    string address1 = address;
    byte[] numArray;
    if (!value)
      numArray = new byte[2];
    else
      numArray = new byte[2]{ byte.MaxValue, byte.MaxValue };
    OperateResult operateResult = await this.WriteTagAsync(address1, (ushort) 193, numArray, 1);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 193, ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), HslHelper.IsAddressEndWithIndex(address) ? value.Length : 1);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteTagAsync(address, (ushort) 194, new byte[1]
    {
      value
    }, 1);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String)" />
  public OperateResult<DateTime> ReadDate(string address)
  {
    return AllenBradleyHelper.ReadDate((IReadWriteCip) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.DateTime)" />
  public OperateResult WriteDate(string address, DateTime date)
  {
    return AllenBradleyHelper.WriteDate((IReadWriteCip) this, address, date);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteDate(System.String,System.DateTime)" />
  public OperateResult WriteTimeAndDate(string address, DateTime date)
  {
    return AllenBradleyHelper.WriteTimeAndDate((IReadWriteCip) this, address, date);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadTime(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String)" />
  public OperateResult<TimeSpan> ReadTime(string address)
  {
    return AllenBradleyHelper.ReadTime((IReadWriteCip) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteTime(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.TimeSpan)" />
  public OperateResult WriteTime(string address, TimeSpan time)
  {
    return AllenBradleyHelper.WriteTime((IReadWriteCip) this, address, time);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteTimeOfDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.TimeSpan)" />
  public OperateResult WriteTimeOfDate(string address, TimeSpan timeOfDate)
  {
    return AllenBradleyHelper.WriteTimeOfDate((IReadWriteCip) this, address, timeOfDate);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadDate(System.String)" />
  public async Task<OperateResult<DateTime>> ReadDateAsync(string address)
  {
    OperateResult<DateTime> operateResult = await AllenBradleyHelper.ReadDateAsync((IReadWriteCip) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteDate(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteDateAsync(string address, DateTime date)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteDateAsync((IReadWriteCip) this, address, date);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteTimeAndDate(System.String,System.DateTime)" />
  public async Task<OperateResult> WriteTimeAndDateAsync(string address, DateTime date)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteTimeAndDateAsync((IReadWriteCip) this, address, date);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.ReadTime(System.String)" />
  public async Task<OperateResult<TimeSpan>> ReadTimeAsync(string address)
  {
    OperateResult<TimeSpan> operateResult = await AllenBradleyHelper.ReadTimeAsync((IReadWriteCip) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteTime(System.String,System.TimeSpan)" />
  public async Task<OperateResult> WriteTimeAsync(string address, TimeSpan time)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteTimeAsync((IReadWriteCip) this, address, time);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronConnectedCipNet.WriteTimeOfDate(System.String,System.TimeSpan)" />
  public async Task<OperateResult> WriteTimeOfDateAsync(string address, TimeSpan timeOfDate)
  {
    OperateResult operateResult = await AllenBradleyHelper.WriteTimeOfDateAsync((IReadWriteCip) this, address, timeOfDate);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronConnectedCipNet[{this.IpAddress}:{this.Port}]";
}
