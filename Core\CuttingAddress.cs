﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.CuttingAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>等待切割的地址信息</summary>
public class CuttingAddress
{
  /// <summary>实例化一个默认的对象</summary>
  public CuttingAddress()
  {
  }

  /// <summary>指定地址类型，临界地址，地址的进制来实例化一个对象</summary>
  /// <param name="type">地址类型</param>
  /// <param name="address">临界地址</param>
  /// <param name="fromBase">地址的进制</param>
  public CuttingAddress(string type, int address, int fromBase = 10)
  {
    this.DataType = type;
    this.Address = address;
    this.FromBase = fromBase;
  }

  /// <summary>地址的切割类型</summary>
  public string DataType { get; set; }

  /// <summary>等待切割的地址的临界点</summary>
  public int Address { get; set; }

  /// <summary>地址的进制信息</summary>
  public int FromBase { get; set; } = 10;
}
