﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.TcpForward
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Net;
using System.Net.Sockets;

#nullable disable
namespace HslCommunication.Enthernet;

/// <summary>
/// 用于转发的TCP服务类，可以用来实现TCP协议的转发功能，需要指定本机端口，服务器的ip及端口信息<br />
/// The TCP service class used for forwarding can be used to implement the forwarding function of the TCP protocol. It is necessary to specify the local port, the server's ip and port information.
/// </summary>
public class TcpForward : CommunicationServer
{
  private string _hostIp = string.Empty;
  private int _port = 0;

  /// <summary>实例化一个TCP转发的对象，需要本机端口号，远程ip地址及远程端口号</summary>
  /// <param name="localPort">本机侦听的端口号</param>
  /// <param name="host">远程的IP地址</param>
  /// <param name="hostPort">远程的端口号信息</param>
  public TcpForward(int localPort, string host, int hostPort)
  {
    this.Port = localPort;
    this._hostIp = host;
    this._port = hostPort;
    this.ConnectTimeOut = 5000;
    this.CreatePipeSession = (Func<CommunicationPipe, PipeSession>) (m =>
    {
      return (PipeSession) new ForwardSession()
      {
        Communication = m
      };
    });
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkDoubleBase.ConnectTimeOut" />
  [HslMqttApi(HttpMethod = "GET", Description = "Gets or sets the timeout for the connection, in milliseconds")]
  public virtual int ConnectTimeOut { get; set; }

  /// <summary>
  /// 获取或设置当前缓冲区的大小，以字节为单位，默认 2048<br />
  /// Gets or sets the size of the current buffer, in bytes, with a default of 2048
  /// </summary>
  public int CacheSize { get; set; } = 2048 /*0x0800*/;

  /// <summary>获取当前的用于中转数据的会话数量</summary>
  public int OnlineSessionsCount => this.GetPipeSessions().Length;

  /// <inheritdoc />
  protected override void ThreadPoolLogin(PipeTcpNet pipe, IPEndPoint endPoint)
  {
    this.LogNet?.WriteInfo(this.ToString(), $"Local client[{endPoint}] connected");
    OperateResult<Socket> socketAndConnect = NetSupport.CreateSocketAndConnect(this._hostIp, this._port, this.ConnectTimeOut);
    if (!socketAndConnect.IsSuccess)
    {
      this.LogNet?.WriteError(this.ToString(), "Connect server failed, local client close: " + socketAndConnect.Message);
      pipe?.CloseCommunication();
    }
    else
    {
      this.LogNet?.WriteInfo(this.ToString(), $"Connect [{this._hostIp}:{this._port}] success");
      ForwardSession forwardSession = new ForwardSession(pipe, endPoint, this.CacheSize);
      forwardSession.ServerSocket = socketAndConnect.Content;
      try
      {
        forwardSession.ServerSocket.BeginReceive(forwardSession.ServerBuffer, 0, forwardSession.ServerBuffer.Length, SocketFlags.None, new AsyncCallback(this.ServerReceiveAsync), (object) forwardSession);
      }
      catch (Exception ex)
      {
        this.LogNet?.WriteError(this.ToString(), "Server begin receive failed, local client close. " + ex.Message);
        forwardSession.Close();
        return;
      }
      try
      {
        (forwardSession.Communication as PipeTcpNet).Socket.BeginReceive(forwardSession.BytesBuffer, 0, forwardSession.BytesBuffer.Length, SocketFlags.None, new AsyncCallback(this.LocalReceiveAsync), (object) forwardSession);
      }
      catch (Exception ex)
      {
        this.LogNet?.WriteError(this.ToString(), "Local begin receive failed, server close. " + ex.Message);
        forwardSession.Close();
        return;
      }
      this.AddSession((PipeSession) forwardSession);
    }
  }

  /// <summary>当接收到远程的数据触发的事件</summary>
  public event TcpForward.OnMessageReceivedDelegate OnRemoteMessageReceived;

  /// <summary>当接收到客户端数据触发的事件</summary>
  public event TcpForward.OnMessageReceivedDelegate OnClientMessageReceive;

  private void ServerReceiveAsync(IAsyncResult ar)
  {
    if (!(ar.AsyncState is ForwardSession asyncState))
      return;
    asyncState.HeartTime = DateTime.Now;
    int length = 0;
    try
    {
      length = asyncState.ServerSocket.EndReceive(ar);
    }
    catch (ObjectDisposedException ex)
    {
      this.RemoveSession((PipeSession) asyncState, string.Empty);
    }
    catch (Exception ex)
    {
      this.RemoveSession((PipeSession) asyncState, "Server socket endreceive failed: " + ex.Message);
      return;
    }
    if (length == 0)
    {
      this.RemoveSession((PipeSession) asyncState, $"Server socket [{this._hostIp}:{this._port}], local closed");
    }
    else
    {
      byte[] numArray = asyncState.ServerBuffer.SelectBegin<byte>(length);
      this.LogBuffer("Remote->Client", numArray);
      TcpForward.OnMessageReceivedDelegate remoteMessageReceived = this.OnRemoteMessageReceived;
      if (remoteMessageReceived != null)
        remoteMessageReceived(asyncState, numArray);
      try
      {
        asyncState.Communication.Send(numArray);
      }
      catch (Exception ex)
      {
        this.RemoveSession((PipeSession) asyncState, "Local send failed, server closed: " + ex.Message);
        return;
      }
      try
      {
        asyncState.ServerSocket.BeginReceive(asyncState.ServerBuffer, 0, asyncState.ServerBuffer.Length, SocketFlags.None, new AsyncCallback(this.ServerReceiveAsync), (object) asyncState);
      }
      catch (Exception ex)
      {
        this.RemoveSession((PipeSession) asyncState, "Server socket beginReceive failed, local client close. " + ex.Message);
      }
    }
  }

  private void LocalReceiveAsync(IAsyncResult ar)
  {
    if (!(ar.AsyncState is ForwardSession asyncState))
      return;
    asyncState.HeartTime = DateTime.Now;
    PipeTcpNet communication = asyncState.Communication as PipeTcpNet;
    int length;
    try
    {
      length = communication.Socket.EndReceive(ar);
    }
    catch (Exception ex)
    {
      this.RemoveSession((PipeSession) asyncState, "local socket endreceive failed: " + ex.Message);
      return;
    }
    if (length == 0)
    {
      this.RemoveSession((PipeSession) asyncState, $"local socket closed[{asyncState.IpEndPoint}], server[{this._hostIp}:{this._port}] closed");
    }
    else
    {
      byte[] numArray = asyncState.BytesBuffer.SelectBegin<byte>(length);
      this.LogBuffer("Client->Remote", numArray);
      TcpForward.OnMessageReceivedDelegate clientMessageReceive = this.OnClientMessageReceive;
      if (clientMessageReceive != null)
        clientMessageReceive(asyncState, numArray);
      try
      {
        asyncState.ServerSocket.Send(numArray);
      }
      catch (Exception ex)
      {
        this.RemoveSession((PipeSession) asyncState, "Server send failed, local closed: " + ex.Message);
        return;
      }
      try
      {
        communication.Socket.BeginReceive(asyncState.BytesBuffer, 0, asyncState.BytesBuffer.Length, SocketFlags.None, new AsyncCallback(this.LocalReceiveAsync), (object) asyncState);
      }
      catch (Exception ex)
      {
        this.RemoveSession((PipeSession) asyncState, "local socket beginReceive failed, server socket close. " + ex.Message);
      }
    }
  }

  private void LogBuffer(string info, byte[] buffer)
  {
    this.LogNet?.WriteInfo(this.ToString(), $"[{info}] {(this.LogMsgFormatBinary ? SoftBasic.ByteToHexString(buffer, ' ') : SoftBasic.GetAsciiStringRender(buffer))}");
  }

  /// <inheritdoc cref="F:HslCommunication.Core.Net.BinaryCommunication.LogMsgFormatBinary" />
  public bool LogMsgFormatBinary { get; set; } = true;

  /// <inheritdoc />
  public override string ToString() => $"TcpForward[{this.Port}->{this._hostIp}:{this._port}]";

  /// <summary>接收消息触发的委托信息</summary>
  /// <param name="session">会话对象</param>
  /// <param name="data">原始报文数据信息</param>
  public delegate void OnMessageReceivedDelegate(ForwardSession session, byte[] data);
}
