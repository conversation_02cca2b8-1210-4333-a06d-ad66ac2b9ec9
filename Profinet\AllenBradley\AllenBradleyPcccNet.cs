﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>在CIP协议里，使用PCCC命令进行访问设备的原始数据文件的通信方法，</summary>
/// <remarks>
/// <inheritdoc cref="T:HslCommunication.Profinet.AllenBradley.AllenBradleySLCNet" path="remarks" />
/// </remarks>
/// <example>
/// <inheritdoc cref="T:HslCommunication.Profinet.AllenBradley.AllenBradleySLCNet" path="example" />
/// </example>
public class AllenBradleyPcccNet : NetworkConnectedCip
{
  private SoftIncrementCount incrementCount = new SoftIncrementCount((long) ushort.MaxValue, 2L, 2);

  /// <summary>实例化一个默认的对象</summary>
  public AllenBradleyPcccNet()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>根据指定的IP及端口来实例化这个连接对象</summary>
  /// <param name="ipAddress">PLC的Ip地址</param>
  /// <param name="port">PLC的端口号信息</param>
  public AllenBradleyPcccNet(string ipAddress, int port = 44818)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override byte[] GetLargeForwardOpen(ushort connectionID)
  {
    this.TOConnectionId = (uint) HslHelper.HslRandom.Next();
    byte[] hexBytes = "\r\n00 00 00 00 0a 00 02 00 00 00 00 00 b2 00 30 00\r\n54 02 20 06 24 01 0a 05 00 00 00 00 e8 a3 14 00\r\n27 04 09 10 0b 46 a5 c1 07 00 00 00 01 40 20 00\r\nf4 43 01 40 20 00 f4 43 a3 03 01 00 20 02 24 01".ToHexBytes();
    BitConverter.GetBytes((ushort) 4105).CopyTo((Array) hexBytes, 34);
    BitConverter.GetBytes(3248834059U).CopyTo((Array) hexBytes, 36);
    BitConverter.GetBytes(this.TOConnectionId).CopyTo((Array) hexBytes, 28);
    return hexBytes;
  }

  /// <inheritdoc />
  protected override byte[] GetLargeForwardClose()
  {
    return "\r\n00 00 00 00 0a 00 02 00 00 00 00 00 b2 00 18 00\r\n4e 02 20 06 24 01 0a 05 27 04 09 10 0b 46 a5 c1\r\n03 00 01 00 20 02 24 01".ToHexBytes();
  }

  /// <inheritdoc />
  /// <remarks>读取PLC的原始数据信息，地址示例：N7:0</remarks>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult1 = AllenBradleyHelper.PackExecutePCCCRead((int) this.incrementCount.GetCurrentValue(), address, (int) length);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommandService(operateResult1.Content));
    if (!operateResult2.IsSuccess)
      return operateResult2;
    OperateResult result = AllenBradleyHelper.CheckResponse(operateResult2.Content);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result);
    OperateResult<byte[], ushort, bool> actualData = NetworkConnectedCip.ExtractActualData(operateResult2.Content, true);
    return !actualData.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) actualData) : OperateResult.CreateSuccessResult<byte[]>(actualData.Content1);
  }

  /// <inheritdoc />
  /// <remarks>写入PLC的原始数据信息，地址示例：N7:0</remarks>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = AllenBradleyHelper.PackExecutePCCCWrite((int) this.incrementCount.GetCurrentValue(), address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommandService(operateResult1.Content));
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult result = AllenBradleyHelper.CheckResponse(operateResult2.Content);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>(result);
    OperateResult<byte[], ushort, bool> actualData = NetworkConnectedCip.ExtractActualData(operateResult2.Content, true);
    return !actualData.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) actualData) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    int bitIndex;
    address = AllenBradleySLCNet.AnalysisBitIndex(address, out bitIndex);
    OperateResult<byte[]> result = this.Read(address, (ushort) (bitIndex / 16 /*0x10*/ * 2 + 2));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) result) : OperateResult.CreateSuccessResult<bool>(result.Content.ToBoolArray()[bitIndex]);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    int bitIndex;
    address = AllenBradleySLCNet.AnalysisBitIndex(address, out bitIndex);
    OperateResult<byte[]> operateResult1 = AllenBradleyHelper.PackExecutePCCCWrite((int) this.incrementCount.GetCurrentValue(), address, bitIndex, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommandService(operateResult1.Content));
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult result = AllenBradleyHelper.CheckResponse(operateResult2.Content);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>(result);
    OperateResult<byte[], ushort, bool> actualData = NetworkConnectedCip.ExtractActualData(operateResult2.Content, true);
    return !actualData.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) actualData) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.Read(System.String,System.UInt16)" />
  public OperateResult<string> ReadString(string address)
  {
    return this.ReadString(address, (ushort) 0, Encoding.ASCII);
  }

  /// <inheritdoc />
  /// <remarks>
  /// 读取PLC的地址信息，如果输入了 ST 的地址，例如 ST10:2, 当长度指定为 0 的时候，这时候就是动态的读取PLC来获取实际的字符串长度。<br />
  /// Read the PLC address information, if the ST address is entered, such as ST10:2, when the length is specified as 0, then the PLC is dynamically read to obtain the actual string length.
  /// </remarks>
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    if (string.IsNullOrEmpty(address) || !address.StartsWith("ST"))
      return base.ReadString(address, length, encoding);
    if (length <= (ushort) 0)
    {
      OperateResult<byte[]> result1 = this.Read(address, (ushort) 2);
      if (!result1.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) result1);
      int count = (int) this.ByteTransform.TransUInt16(result1.Content, 0);
      OperateResult<byte[]> result2 = this.Read(address, (ushort) (2 + (count % 2 != 0 ? count + 1 : count)));
      return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result2) : OperateResult.CreateSuccessResult<string>(encoding.GetString(SoftBasic.BytesReverseByWord(result2.Content), 2, count));
    }
    OperateResult<byte[]> result = this.Read(address, (int) length % 2 != 0 ? (ushort) ((int) length + 3) : (ushort) ((int) length + 2));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    int count1 = (int) this.ByteTransform.TransUInt16(result.Content, 0);
    if (count1 + 2 > result.Content.Length)
      count1 = result.Content.Length - 2;
    return OperateResult.CreateSuccessResult<string>(encoding.GetString(SoftBasic.BytesReverseByWord(result.Content), 2, count1));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    if (string.IsNullOrEmpty(address) || !address.StartsWith("ST"))
      return base.Write(address, value, encoding);
    byte[] data = this.ByteTransform.TransByte(value, encoding);
    int length = data.Length;
    byte[] lengthEven = SoftBasic.ArrayExpandToLengthEven<byte>(data);
    return this.Write(address, SoftBasic.SpliceArray<byte>(new byte[2]
    {
      BitConverter.GetBytes(length)[0],
      BitConverter.GetBytes(length)[1]
    }, SoftBasic.BytesReverseByWord(lengthEven)));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> command = AllenBradleyHelper.PackExecutePCCCRead((int) this.incrementCount.GetCurrentValue(), address, (int) length);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.PackCommandService(command.Content));
    if (!read.IsSuccess)
      return read;
    OperateResult check = AllenBradleyHelper.CheckResponse(read.Content);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(check);
    OperateResult<byte[], ushort, bool> extra = NetworkConnectedCip.ExtractActualData(read.Content, true);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(extra.Content1) : OperateResult.CreateFailedResult<byte[]>((OperateResult) extra);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> command = AllenBradleyHelper.PackExecutePCCCWrite((int) this.incrementCount.GetCurrentValue(), address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.PackCommandService(command.Content));
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = AllenBradleyHelper.CheckResponse(read.Content);
    if (!check.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>(check);
    OperateResult<byte[], ushort, bool> extra = NetworkConnectedCip.ExtractActualData(read.Content, true);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) extra);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.ReadBool(System.String)" />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    int bitIndex;
    address = AllenBradleySLCNet.AnalysisBitIndex(address, out bitIndex);
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) (bitIndex / 16 /*0x10*/ * 2 + 2));
    OperateResult<bool> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<bool>(read.Content.ToBoolArray()[bitIndex]) : OperateResult.CreateFailedResult<bool>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    int bitIndex;
    address = AllenBradleySLCNet.AnalysisBitIndex(address, out bitIndex);
    OperateResult<byte[]> command = AllenBradleyHelper.PackExecutePCCCWrite((int) this.incrementCount.GetCurrentValue(), address, bitIndex, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.PackCommandService(command.Content));
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult check = AllenBradleyHelper.CheckResponse(read.Content);
    if (!check.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>(check);
    OperateResult<byte[], ushort, bool> extra = NetworkConnectedCip.ExtractActualData(read.Content, true);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) extra);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.Read(System.String,System.UInt16)" />
  public async Task<OperateResult<string>> ReadStringAsync(string address)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, (ushort) 0, Encoding.ASCII);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.ReadString(System.String,System.UInt16,System.Text.Encoding)" />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    if (string.IsNullOrEmpty(address) || !address.StartsWith("ST"))
      return base.ReadString(address, length, encoding);
    if (length <= (ushort) 0)
    {
      OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 2);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<string>((OperateResult) read);
      int len = (int) this.ByteTransform.TransUInt16(read.Content, 0);
      read = await this.ReadAsync(address, (ushort) (2 + (len % 2 != 0 ? len + 1 : len)));
      return read.IsSuccess ? OperateResult.CreateSuccessResult<string>(encoding.GetString(SoftBasic.BytesReverseByWord(read.Content), 2, len)) : OperateResult.CreateFailedResult<string>((OperateResult) read);
    }
    OperateResult<byte[]> read1 = await this.ReadAsync(address, (int) length % 2 != 0 ? (ushort) ((int) length + 3) : (ushort) ((int) length + 2));
    if (!read1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read1);
    int len1 = (int) this.ByteTransform.TransUInt16(read1.Content, 0);
    if (len1 + 2 > read1.Content.Length)
      len1 = read1.Content.Length - 2;
    return OperateResult.CreateSuccessResult<string>(encoding.GetString(SoftBasic.BytesReverseByWord(read1.Content), 2, len1));
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    if (!string.IsNullOrEmpty(address) && address.StartsWith("ST"))
    {
      byte[] temp = this.ByteTransform.TransByte(value, encoding);
      int len = temp.Length;
      temp = SoftBasic.ArrayExpandToLengthEven<byte>(temp);
      OperateResult operateResult = await this.WriteAsync(address, SoftBasic.SpliceArray<byte>(new byte[2]
      {
        BitConverter.GetBytes(len)[0],
        BitConverter.GetBytes(len)[1]
      }, SoftBasic.BytesReverseByWord(temp)));
      return operateResult;
    }
    OperateResult operateResult1 = await base.WriteAsync(address, value, encoding);
    return operateResult1;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.Write(System.String,System.Byte)" />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyPcccNet.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"AllenBradleyPcccNet[{this.IpAddress}:{this.Port}]";
}
