﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeSslNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.IO;
using System.Net;
using System.Net.Security;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// 基于SSL/TLS加密的管道信息，内部基于 TCP/IP 通信实现<br />
/// Pipe information based on SSL/TLS encryption, and internal TCP/IP communication
/// </summary>
public class PipeSslNet : PipeTcpNet
{
  private bool isServerMode = true;
  private NetworkStream networkStream;
  private SslStream sslStream = (SslStream) null;
  private X509Certificate certificate = (X509Certificate) null;

  /// <summary>实例化一个默认的对象</summary>
  /// <param name="serverMode">是否为服务器模式</param>
  public PipeSslNet(bool serverMode)
  {
    this.isServerMode = serverMode;
    this.sslInit();
  }

  /// <summary>
  /// 通过指定的IP地址和端口号来实例化一个对象<br />
  /// Instantiate an object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号</param>
  /// <param name="serverMode">是否为服务器模式</param>
  public PipeSslNet(string ipAddress, int port, bool serverMode)
    : base(ipAddress, port)
  {
    this.isServerMode = serverMode;
    this.sslInit();
  }

  /// <summary>
  /// 通过指定的套接字及连接的终结点来实例化一个对象<br />
  /// Instantiate an object by specifying the socket and the endpoint of the connection
  /// </summary>
  /// <param name="socket">连接的套接字对象</param>
  /// <param name="iPEndPoint">连接的远程地址信息</param>
  /// <param name="serverMode">是否为服务器模式</param>
  public PipeSslNet(Socket socket, IPEndPoint iPEndPoint, bool serverMode)
    : base(socket, iPEndPoint)
  {
    this.isServerMode = serverMode;
    this.sslInit();
  }

  private void sslInit()
  {
    this.SslProtocols = SslProtocols.Tls | SslProtocols.Tls11 | SslProtocols.Tls12;
  }

  /// <summary>获取或设置当前的证书内容，</summary>
  public X509Certificate Certificate
  {
    get => this.certificate;
    set => this.certificate = value;
  }

  /// <summary>
  /// 获取或设置是否检查远程的证书，默认为不检查<br />
  /// </summary>
  public bool RemoteCertificateCheck { get; set; } = false;

  /// <summary>
  /// 获取或设置当前的 SSL/TLS 协议版本，默认为 Tls | Tls11 | Tls12<br />
  /// </summary>
  public SslProtocols SslProtocols { get; set; } = SslProtocols.Tls;

  /// <summary>
  /// 使用一个证书路径来初始化 SSL/TLS 通信<br />
  /// Use a certificate path to initialize SSL/TLS communication
  /// </summary>
  /// <param name="certificateFile">证书路径</param>
  public void SetCertficate(string certificateFile)
  {
    if (string.IsNullOrEmpty(certificateFile))
      return;
    this.certificate = X509Certificate.CreateFromCertFile(certificateFile);
  }

  /// <inheritdoc />
  protected override OperateResult OnCommunicationOpen(Socket socket)
  {
    OperateResult<SslStream> sslStream = this.CreateSslStream(socket, true);
    return !sslStream.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) sslStream) : base.OnCommunicationOpen(socket);
  }

  /// <summary>
  /// 当前的管道创建一个 SSL/TLS 加密的流<br />
  /// The current pipeline creates an SSL/TLS encrypted stream
  /// </summary>
  /// <param name="socket">套接字对象</param>
  /// <param name="createNew">是否创建一个新的流</param>
  /// <returns>如果创建成功，返回流对象</returns>
  public OperateResult<SslStream> CreateSslStream(Socket socket, bool createNew = false)
  {
    if (!createNew)
      return OperateResult.CreateSuccessResult<SslStream>(this.sslStream);
    this.networkStream?.Close();
    this.sslStream?.Close();
    this.networkStream = new NetworkStream(socket, false);
    this.sslStream = new SslStream((Stream) this.networkStream, false, new RemoteCertificateValidationCallback(this.ValidateCertificate), (LocalCertificateSelectionCallback) null);
    try
    {
      if (this.isServerMode)
      {
        this.sslStream.AuthenticateAsServer(this.certificate, false, this.SslProtocols, true);
        return OperateResult.CreateSuccessResult<SslStream>(this.sslStream);
      }
      if (this.certificate == null)
        this.sslStream.AuthenticateAsClient(this.host);
      else
        this.sslStream.AuthenticateAsClient(this.host, new X509CertificateCollection(new X509Certificate[1]
        {
          this.certificate
        }), this.SslProtocols, false);
      return OperateResult.CreateSuccessResult<SslStream>(this.sslStream);
    }
    catch (Exception ex)
    {
      return ex.InnerException != null ? new OperateResult<SslStream>(ex.InnerException.Message) : new OperateResult<SslStream>(ex.Message);
    }
  }

  private bool ValidateCertificate(
    object sender,
    X509Certificate certificate,
    X509Chain chain,
    SslPolicyErrors sslPolicyErrors)
  {
    return sslPolicyErrors == SslPolicyErrors.None || !this.RemoteCertificateCheck;
  }

  /// <inheritdoc />
  public override OperateResult Send(byte[] data, int offset, int size)
  {
    OperateResult<SslStream> sslStream = this.CreateSslStream(this.Socket);
    if (!sslStream.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) sslStream);
    OperateResult operateResult = NetSupport.SocketSend(sslStream.Content, data, offset, size);
    if (operateResult.IsSuccess || operateResult.ErrorCode != NetSupport.SocketErrorCode)
      return operateResult;
    this.CloseCommunication();
    return (OperateResult) new OperateResult<byte[]>(-this.IncrConnectErrorCount(), operateResult.Message);
  }

  /// <inheritdoc />
  public override OperateResult<int> Receive(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    OperateResult<SslStream> sslStream = this.CreateSslStream(this.Socket);
    if (!sslStream.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) sslStream);
    OperateResult<int> operateResult = NetSupport.SocketReceive(sslStream.Content, buffer, offset, length, timeOut, reportProgress);
    if (operateResult.IsSuccess || operateResult.ErrorCode != NetSupport.SocketErrorCode)
      return operateResult;
    this.CloseCommunication();
    return new OperateResult<int>(-this.IncrConnectErrorCount(), "Socket Exception -> " + operateResult.Message);
  }

  /// <inheritdoc />
  public override async Task<OperateResult> SendAsync(byte[] data, int offset, int size)
  {
    OperateResult<SslStream> ssl = this.CreateSslStream(this.Socket);
    if (!ssl.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) ssl);
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = NetSupport.SocketSendAsync(ssl.Content, data, offset, size).ConfigureAwait(false);
    OperateResult send = await configuredTaskAwaitable;
    if (send.IsSuccess || send.ErrorCode != NetSupport.SocketErrorCode)
      return send;
    configuredTaskAwaitable = this.CloseCommunicationAsync().ConfigureAwait(false);
    OperateResult operateResult = await configuredTaskAwaitable;
    return (OperateResult) new OperateResult<byte[]>(-this.IncrConnectErrorCount(), send.Message);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<int>> ReceiveAsync(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    OperateResult<SslStream> ssl = this.CreateSslStream(this.Socket);
    if (!ssl.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) ssl);
    OperateResult<int> receive = await NetSupport.SocketReceiveAsync(ssl.Content, buffer, offset, length, timeOut, reportProgress).ConfigureAwait(false);
    if (receive.IsSuccess || receive.ErrorCode != NetSupport.SocketErrorCode)
      return receive;
    OperateResult operateResult = await this.CloseCommunicationAsync().ConfigureAwait(false);
    return new OperateResult<int>(-this.IncrConnectErrorCount(), "Socket Exception -> " + receive.Message);
  }

  /// <inheritdoc />
  public override string ToString() => $"PipeSslNet[{this.host}:{this.Port}]";
}
