﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.CoordinationStatus
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>线程的协调逻辑状态</summary>
internal enum CoordinationStatus
{
  /// <summary>所有项完成</summary>
  AllDone,
  /// <summary>超时</summary>
  Timeout,
  /// <summary>任务取消</summary>
  Cancel,
}
