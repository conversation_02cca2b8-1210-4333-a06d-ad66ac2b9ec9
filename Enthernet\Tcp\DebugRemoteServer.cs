﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.Tcp.DebugRemoteServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Enthernet.Tcp;

/// <summary>用于调试的远程服务器</summary>
public class DebugRemoteServer : DeviceServer
{
  private Dictionary<int, BinaryCommunication> binaryCommunication;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.#ctor" />
  public DebugRemoteServer()
  {
    this.binaryCommunication = new Dictionary<int, BinaryCommunication>();
  }

  /// <summary>设置当前的服务器关联的通信设备对象，默认的key为0，如果已经存在，则自动覆盖</summary>
  /// <param name="communication">设备通信对象</param>
  public void SetDeviceCommunication(BinaryCommunication communication)
  {
    if (this.binaryCommunication.ContainsKey(0))
      this.binaryCommunication[0] = communication;
    else
      this.binaryCommunication.Add(0, communication);
  }

  /// <summary>新增加当前的服务器关联的通信设备对象，需要指定唯一标识，如果已经存在，则引发异常</summary>
  /// <param name="key">设备的唯一标识</param>
  /// <param name="communication">设备通信对象</param>
  public void AddDeviceCommunication(ushort key, BinaryCommunication communication)
  {
    this.binaryCommunication.Add((int) key, communication);
  }

  /// <summary>移除一个设备的通信对象，如果存在的话，返回是否移除成功</summary>
  /// <param name="key">设备的唯一标识</param>
  public bool RemoveDeviceCommunication(ushort key)
  {
    return this.binaryCommunication.ContainsKey((int) key) && this.binaryCommunication.Remove((int) key);
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new AdsNetMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive == null || receive.Length < 6)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (this.binaryCommunication == null)
    {
      this.LogNet?.WriteError(this.ToString(), "BinaryCommunication is null");
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    }
    int uint16 = (int) BitConverter.ToUInt16(receive, 0);
    byte[] send = receive.RemoveBegin<byte>(6);
    if (!this.binaryCommunication.ContainsKey(uint16))
    {
      this.LogNet?.WriteError(this.ToString(), $"{session.Communication} Communication Key[{uint16}] is not exist. ");
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    }
    OperateResult<byte[]> result = this.binaryCommunication[uint16].ReadFromCoreServer(send, true, false);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(result.Content);
  }

  /// <inheritdoc />
  public override string ToString() => $"DebugRemoteServer[{this.Port}]";
}
