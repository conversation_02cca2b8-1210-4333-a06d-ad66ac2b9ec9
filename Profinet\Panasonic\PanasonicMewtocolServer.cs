﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Panasonic.PanasonicMewtocolServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Panasonic;

/// <summary>
/// 松下Mewtocol协议的虚拟服务器，支持串口和网口的操作<br />
/// Panasonic Mewtocol protocol virtual server, supports serial and network port operations
/// </summary>
/// <remarks>
/// 地址的地址分为线圈型和整型，线圈支持X,Y,R,L, 字单位的整型支持 X,Y,R,L,D,LD,F<br />
/// The address of the address is divided into coil type and integer type, the coil supports X, Y, R, L, and the integer type of word unit supports X, Y, R, L, D, LD, F
/// </remarks>
public class PanasonicMewtocolServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer rBuffer;
  private SoftBuffer dtBuffer;
  private SoftBuffer ldBuffer;
  private SoftBuffer flBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer lBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private byte station = 1;

  /// <summary>实例化一个默认的对象</summary>
  public PanasonicMewtocolServer()
  {
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.dtBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ldBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.flBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.xBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.lBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.yBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.ByteTransform.DataFormat = DataFormat.DCBA;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[917504 /*0x0E0000*/];
    Array.Copy((Array) this.rBuffer.GetBytes(), 0, (Array) destinationArray, 0, 131072 /*0x020000*/);
    Array.Copy((Array) this.dtBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.ldBuffer.GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.flBuffer.GetBytes(), 0, (Array) destinationArray, 393216 /*0x060000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.xBuffer.GetBytes(), 0, (Array) destinationArray, 524288 /*0x080000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.lBuffer.GetBytes(), 0, (Array) destinationArray, 655360 /*0x0A0000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.yBuffer.GetBytes(), 0, (Array) destinationArray, 786432 /*0x0C0000*/, 131072 /*0x020000*/);
    return destinationArray;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 917504 /*0x0E0000*/)
      throw new Exception("File is not correct");
    this.rBuffer.SetBytes(content, 0, 0, 131072 /*0x020000*/);
    this.dtBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 131072 /*0x020000*/);
    this.ldBuffer.SetBytes(content, 262144 /*0x040000*/, 0, 131072 /*0x020000*/);
    this.flBuffer.SetBytes(content, 393216 /*0x060000*/, 0, 131072 /*0x020000*/);
    this.xBuffer.SetBytes(content, 524288 /*0x080000*/, 0, 131072 /*0x020000*/);
    this.lBuffer.SetBytes(content, 655360 /*0x0A0000*/, 0, 131072 /*0x020000*/);
    this.yBuffer.SetBytes(content, 786432 /*0x0C0000*/, 0, 131072 /*0x020000*/);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.Read(System.String,System.UInt16)" />
  /// <remarks>
  /// 在服务器端的功能实现里，暂时不支持C,T数据的访问。<br />
  /// In the server-side function implementation, access to C and T data is temporarily not supported.
  /// </remarks>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<string, int> result = PanasonicHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (result.Content1 == "D")
      return OperateResult.CreateSuccessResult<byte[]>(this.dtBuffer.GetBytes(result.Content2 * 2, (int) length * 2));
    if (result.Content1 == "LD")
      return OperateResult.CreateSuccessResult<byte[]>(this.ldBuffer.GetBytes(result.Content2 * 2, (int) length * 2));
    if (result.Content1 == "F")
      return OperateResult.CreateSuccessResult<byte[]>(this.flBuffer.GetBytes(result.Content2 * 2, (int) length * 2));
    if (result.Content1 == "X")
      return OperateResult.CreateSuccessResult<byte[]>(this.xBuffer.GetBool(result.Content2, (int) length * 16 /*0x10*/).ToByteArray());
    if (result.Content1 == "Y")
      return OperateResult.CreateSuccessResult<byte[]>(this.yBuffer.GetBool(result.Content2, (int) length * 16 /*0x10*/).ToByteArray());
    if (result.Content1 == "R")
      return OperateResult.CreateSuccessResult<byte[]>(this.rBuffer.GetBool(result.Content2, (int) length * 16 /*0x10*/).ToByteArray());
    return result.Content1 == "L" ? OperateResult.CreateSuccessResult<byte[]>(this.lBuffer.GetBool(result.Content2, (int) length * 16 /*0x10*/).ToByteArray()) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.Write(System.String,System.Byte[])" />
  /// <remarks>
  /// 在服务器端的功能实现里，暂时不支持C,T数据的访问。<br />
  /// In the server-side function implementation, access to C and T data is temporarily not supported.
  /// </remarks>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<string, int> result = PanasonicHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (result.Content1 == "D")
      this.dtBuffer.SetBytes(value, result.Content2 * 2);
    else if (result.Content1 == "LD")
      this.ldBuffer.SetBytes(value, result.Content2 * 2);
    else if (result.Content1 == "F")
      this.flBuffer.SetBytes(value, result.Content2 * 2);
    else if (result.Content1 == "X")
      this.xBuffer.SetBool(value.ToBoolArray(), result.Content2);
    else if (result.Content1 == "Y")
      this.yBuffer.SetBool(value.ToBoolArray(), result.Content2);
    else if (result.Content1 == "R")
    {
      this.rBuffer.SetBool(value.ToBoolArray(), result.Content2);
    }
    else
    {
      if (!(result.Content1 == "L"))
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      this.lBuffer.SetBool(value.ToBoolArray(), result.Content2);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.ReadBool(System.String,System.UInt16)" />
  /// <remarks>
  /// 在服务器端的功能实现里，长度支持任意的长度信息。<br />
  /// In the server-side function implementation, the length supports arbitrary length information.
  /// </remarks>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<string, int> result = PanasonicHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    if (result.Content1 == "X")
      return OperateResult.CreateSuccessResult<bool[]>(this.xBuffer.GetBool(result.Content2, (int) length));
    if (result.Content1 == "Y")
      return OperateResult.CreateSuccessResult<bool[]>(this.yBuffer.GetBool(result.Content2, (int) length));
    if (result.Content1 == "R")
      return OperateResult.CreateSuccessResult<bool[]>(this.rBuffer.GetBool(result.Content2, (int) length));
    return result.Content1 == "L" ? OperateResult.CreateSuccessResult<bool[]>(this.lBuffer.GetBool(result.Content2, (int) length)) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<string, int> result = PanasonicHelper.AnalysisAddress(address);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (result.Content1 == "X")
      this.xBuffer.SetBool(value, result.Content2);
    else if (result.Content1 == "Y")
      this.yBuffer.SetBool(value, result.Content2);
    else if (result.Content1 == "R")
    {
      this.rBuffer.SetBool(value, result.Content2);
    }
    else
    {
      if (!(result.Content1 == "L"))
        return (OperateResult) new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
      this.lBuffer.SetBool(value, result.Content2);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return receive.Length < 5 ? new OperateResult<byte[]>("Uknown Data：" + receive.ToHexString(' ')) : PanasonicHelper.PackPanasonicCommand(this.station, this.ReadFromCommand(receive), receive[0] == (byte) 60);
  }

  /// <summary>创建一个失败的返回消息，指定错误码即可，会自动计算出来BCC校验和</summary>
  /// <param name="code">错误码</param>
  /// <returns>原始字节报文，用于反馈消息</returns>
  protected string CreateFailedResponse(byte code) => "!" + code.ToString("D2");

  /// <summary>根据命令来获取相关的数据内容</summary>
  /// <param name="cmd">原始的命令码</param>
  /// <returns>返回的数据信息</returns>
  public virtual string ReadFromCommand(byte[] cmd)
  {
    try
    {
      string str = Encoding.ASCII.GetString(cmd);
      if (str[0] != '%' && str[0] != '<')
        return this.CreateFailedResponse((byte) 41);
      byte num1 = Convert.ToByte(str.Substring(1, 2), 16 /*0x10*/);
      bool flag = str[0] == '<';
      if ((int) num1 != (int) this.station)
      {
        this.LogNet?.WriteError(this.ToString(), $"Station not match, need:{this.station}, but now: {num1}");
        return this.CreateFailedResponse((byte) 50);
      }
      if (str[3] != '#')
        return this.CreateFailedResponse((byte) 41);
      if (str.Substring(4, 3) == "RCS")
      {
        int destIndex = Convert.ToInt32(str.Substring(8, 3)) * 16 /*0x10*/ + Convert.ToInt32(str.Substring(11, 1), 16 /*0x10*/);
        if (str[7] == 'R')
          return "$RC" + (this.rBuffer.GetBool(destIndex) ? "1" : "0");
        if (str[7] == 'X')
          return "$RC" + (this.xBuffer.GetBool(destIndex) ? "1" : "0");
        if (str[7] == 'Y')
          return "$RC" + (this.yBuffer.GetBool(destIndex) ? "1" : "0");
        return str[7] == 'L' ? "$RC" + (this.lBuffer.GetBool(destIndex) ? "1" : "0") : this.CreateFailedResponse((byte) 42);
      }
      if (str.Substring(4, 3) == "WCS")
      {
        int destIndex = Convert.ToInt32(str.Substring(8, 3)) * 16 /*0x10*/ + Convert.ToInt32(str.Substring(11, 1), 16 /*0x10*/);
        if (str[7] == 'R')
        {
          this.rBuffer.SetBool(str[12] == '1', destIndex);
          return "$WC";
        }
        if (str[7] == 'X')
        {
          this.xBuffer.SetBool(str[12] == '1', destIndex);
          return "$WC";
        }
        if (str[7] == 'Y')
        {
          this.yBuffer.SetBool(str[12] == '1', destIndex);
          return "$WC";
        }
        if (str[7] != 'L')
          return this.CreateFailedResponse((byte) 42);
        this.lBuffer.SetBool(str[12] == '1', destIndex);
        return "$WC";
      }
      if (str.Substring(4, 3) == "RCP")
      {
        int num2 = (int) str[7] - 48 /*0x30*/;
        if (num2 > 8)
          return this.CreateFailedResponse((byte) 42);
        StringBuilder stringBuilder = new StringBuilder();
        for (int index = 0; index < num2; ++index)
        {
          int destIndex = Convert.ToInt32(str.Substring(9 + 5 * index, 3)) * 16 /*0x10*/ + Convert.ToInt32(str.Substring(12 + 5 * index, 1), 16 /*0x10*/);
          if (str[8 + 5 * index] == 'R')
            stringBuilder.Append(this.rBuffer.GetBool(destIndex) ? "1" : "0");
          else if (str[8 + 5 * index] == 'X')
            stringBuilder.Append(this.xBuffer.GetBool(destIndex) ? "1" : "0");
          else if (str[8 + 5 * index] == 'Y')
            stringBuilder.Append(this.yBuffer.GetBool(destIndex) ? "1" : "0");
          else if (str[8 + 5 * index] == 'L')
            stringBuilder.Append(this.lBuffer.GetBool(destIndex) ? "1" : "0");
        }
        return "$RC" + stringBuilder.ToString();
      }
      if (str.Substring(4, 3) == "WCP")
      {
        int num3 = (int) str[7] - 48 /*0x30*/;
        if (num3 > 8)
          return this.CreateFailedResponse((byte) 42);
        for (int index = 0; index < num3; ++index)
        {
          int destIndex = Convert.ToInt32(str.Substring(9 + 6 * index, 3)) * 16 /*0x10*/ + Convert.ToInt32(str.Substring(12 + 6 * index, 1), 16 /*0x10*/);
          if (str[8 + 6 * index] == 'R')
            this.rBuffer.SetBool(str[13 + 6 * index] == '1', destIndex);
          else if (str[8 + 6 * index] == 'X')
            this.xBuffer.SetBool(str[13 + 6 * index] == '1', destIndex);
          else if (str[8 + 6 * index] == 'Y')
            this.yBuffer.SetBool(str[13 + 6 * index] == '1', destIndex);
          else if (str[8 + 6 * index] == 'L')
            this.lBuffer.SetBool(str[13 + 6 * index] == '1', destIndex);
        }
        return "$WC";
      }
      if (str.Substring(4, 3) == "RCC")
      {
        int int32 = Convert.ToInt32(str.Substring(8, 4));
        int num4 = Convert.ToInt32(str.Substring(12, 4)) - int32 + 1;
        if (num4 > (flag ? 509 : 27))
          return this.CreateFailedResponse((byte) 42);
        if (str[7] == 'R')
          return "$RC" + this.rBuffer.GetBytes(int32 * 2, num4 * 2).ToHexString();
        if (str[7] == 'X')
          return "$RC" + this.xBuffer.GetBytes(int32 * 2, num4 * 2).ToHexString();
        if (str[7] == 'Y')
          return "$RC" + this.yBuffer.GetBytes(int32 * 2, num4 * 2).ToHexString();
        return str[7] == 'L' ? "$RC" + this.lBuffer.GetBytes(int32 * 2, num4 * 2).ToHexString() : this.CreateFailedResponse((byte) 42);
      }
      if (str.Substring(4, 3) == "WCC")
      {
        int int32 = Convert.ToInt32(str.Substring(8, 4));
        int num5 = Convert.ToInt32(str.Substring(12, 4)) - int32 + 1;
        byte[] hexBytes = str.Substring(16 /*0x10*/, num5 * 4).ToHexBytes();
        if (hexBytes.Length > (flag ? 2028 : 98))
          return this.CreateFailedResponse((byte) 42);
        if (str[7] == 'R')
        {
          this.rBuffer.SetBytes(hexBytes, int32 * 2);
          return "$WC";
        }
        if (str[7] == 'X')
        {
          this.xBuffer.SetBytes(hexBytes, int32 * 2);
          return "$WC";
        }
        if (str[7] == 'Y')
        {
          this.yBuffer.SetBytes(hexBytes, int32 * 2);
          return "$WC";
        }
        if (str[7] != 'L')
          return this.CreateFailedResponse((byte) 42);
        this.lBuffer.SetBytes(hexBytes, int32 * 2);
        return "$WC";
      }
      if (str.Substring(4, 2) == "RD")
      {
        int int32 = Convert.ToInt32(str.Substring(7, 5));
        int num6 = Convert.ToInt32(str.Substring(12, 5)) - int32 + 1;
        if (num6 > (flag ? 509 : 27))
          return this.CreateFailedResponse((byte) 42);
        if (str[6] == 'D')
          return "$RD" + this.dtBuffer.GetBytes(int32 * 2, num6 * 2).ToHexString();
        if (str[6] == 'L')
          return "$RD" + this.ldBuffer.GetBytes(int32 * 2, num6 * 2).ToHexString();
        return str[6] == 'F' ? "$RD" + this.flBuffer.GetBytes(int32 * 2, num6 * 2).ToHexString() : this.CreateFailedResponse((byte) 42);
      }
      if (str.Substring(4, 2) == "WD")
      {
        int int32 = Convert.ToInt32(str.Substring(7, 5));
        int num7 = Convert.ToInt32(str.Substring(12, 5)) - int32 + 1;
        byte[] hexBytes = str.Substring(17, num7 * 4).ToHexBytes();
        if (hexBytes.Length > (flag ? 2028 : 98))
          return this.CreateFailedResponse((byte) 42);
        if (str[6] == 'D')
        {
          this.dtBuffer.SetBytes(hexBytes, int32 * 2);
          return "$WD";
        }
        if (str[6] == 'L')
        {
          this.ldBuffer.SetBytes(hexBytes, int32 * 2);
          return "$WD";
        }
        if (str[6] != 'F')
          return this.CreateFailedResponse((byte) 42);
        this.flBuffer.SetBytes(hexBytes, int32 * 2);
        return "$WD";
      }
      return str.Substring(4, 2) == "RT" ? "$RT0300160000000000" : this.CreateFailedResponse((byte) 41);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), ex);
      return this.CreateFailedResponse((byte) 41);
    }
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength > 5 ? buffer[receivedLength - 1] == (byte) 13 : base.CheckSerialReceiveDataComplete(buffer, receivedLength);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.xBuffer?.Dispose();
      this.rBuffer?.Dispose();
      this.dtBuffer?.Dispose();
      this.ldBuffer?.Dispose();
      this.flBuffer?.Dispose();
      this.yBuffer?.Dispose();
      this.lBuffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"PanasonicMewtocolServer[{this.Port}]";
}
