﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.CommunicationServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Net;
using System.Net.Sockets;
using System.Threading;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>通信的服务器实现，包含了TCP服务器，UDP服务器，串口服务器操作</summary>
public class CommunicationServer : CommunicationTcpServer
{
  private Socket udpServer = (Socket) null;
  private EndPoint udpEndPoint = (EndPoint) null;
  private byte[] udpBuffer;
  private List<PipeSession> pipes;
  private object lockSession = new object();
  private AsyncCallback beginReceiveCallback = (AsyncCallback) null;
  private AsyncCallback udpBeginReceiveCallback = (AsyncCallback) null;
  private AsyncCallback dtuSocketAsyncCallBack = (AsyncCallback) null;

  /// <summary>实例化一个默认的对象</summary>
  public CommunicationServer()
  {
    this.beginReceiveCallback = new AsyncCallback(this.ReceiveCallback);
    this.udpBeginReceiveCallback = new AsyncCallback(this.UdpAsyncCallback);
    this.dtuSocketAsyncCallBack = new AsyncCallback(this.InitiativeSocketAsyncCallBack);
    this.CreatePipeSession = (Func<CommunicationPipe, PipeSession>) (m => new PipeSession()
    {
      Communication = m
    });
    this.pipes = new List<PipeSession>();
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeUdpNet.ReceiveCacheLength" />
  public int UdpBufferSize { get; set; } = 2048 /*0x0800*/;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.GetNewNetMessage" />
  public Func<INetMessage> CreateNewMessage { get; set; }

  /// <summary>检查串口接收到的数据是否完整</summary>
  public Func<byte[], int, bool> CheckSerialDataComplete { get; set; }

  /// <summary>
  /// 指定端口号来启动服务器的引擎<br />
  /// Specify the port number to start the server's engine
  /// </summary>
  /// <param name="port">指定一个端口号</param>
  /// <param name="modeTcp">是否使用TCP格式，如果需要UDP，则为 false</param>
  public void ServerStart(int port, bool modeTcp)
  {
    if (modeTcp)
    {
      this.ServerStart(port);
    }
    else
    {
      if (this.IsStarted)
        return;
      if (!this.EnableIPv6)
      {
        this.udpServer = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        this.udpServer.Bind((EndPoint) new IPEndPoint(IPAddress.Any, port));
      }
      else
      {
        this.udpServer = new Socket(AddressFamily.InterNetworkV6, SocketType.Dgram, ProtocolType.Udp);
        this.udpServer.Bind((EndPoint) new IPEndPoint(IPAddress.IPv6Any, port));
      }
      this.IsStarted = true;
      this.Port = port;
      if (this.udpBuffer == null)
        this.udpBuffer = new byte[this.UdpBufferSize];
      this.udpEndPoint = (EndPoint) new IPEndPoint(this.EnableIPv6 ? IPAddress.Parse("::1") : IPAddress.Parse("127.0.0.1"), 0);
      PipeUdpNet pipeUdpNet = new PipeUdpNet();
      pipeUdpNet.Socket = this.udpServer;
      PipeSession session = this.CreatePipeSession((CommunicationPipe) pipeUdpNet);
      this.SetUdpIp(session);
      this.AddSession(session);
      this.UdpRefreshReceive(session);
      this.ExtraOnStart();
      this.LogDebugMsg(StringResources.Language.NetEngineStart);
    }
  }

  /// <summary>
  /// 指定一个TCP端口及UDP端口，同时启动两种模式的服务器<br />
  /// Specify a TCP port and a UDP port to start the server in both modes at the same time
  /// </summary>
  /// <param name="tcpPort">tcp端口</param>
  /// <param name="udpPort">udp端口</param>
  public void ServerStart(int tcpPort, int udpPort)
  {
    if (this.IsStarted)
      return;
    this.ServerStart(tcpPort);
    if (!this.EnableIPv6)
    {
      this.udpServer = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
      this.udpServer.Bind((EndPoint) new IPEndPoint(IPAddress.Any, udpPort));
    }
    else
    {
      this.udpServer = new Socket(AddressFamily.InterNetworkV6, SocketType.Dgram, ProtocolType.Udp);
      this.udpServer.Bind((EndPoint) new IPEndPoint(IPAddress.IPv6Any, udpPort));
    }
    if (this.udpBuffer == null)
      this.udpBuffer = new byte[this.UdpBufferSize];
    this.udpEndPoint = (EndPoint) new IPEndPoint(this.EnableIPv6 ? IPAddress.Parse("::1") : IPAddress.Parse("127.0.0.1"), 0);
    PipeUdpNet pipeUdpNet = new PipeUdpNet();
    pipeUdpNet.Socket = this.udpServer;
    PipeSession session = this.CreatePipeSession((CommunicationPipe) pipeUdpNet);
    this.SetUdpIp(session);
    this.AddSession(session);
    this.UdpRefreshReceive(session);
  }

  /// <inheritdoc />
  protected override void ExtraOnClose()
  {
    base.ExtraOnClose();
    lock (this.lockSession)
    {
      for (int index = 0; index < this.pipes.Count; ++index)
        this.pipes[index].Close();
      this.pipes.Clear();
    }
    if (this.udpServer == null)
      return;
    NetSupport.CloseSocket(this.udpServer);
  }

  /// <summary>设置当前的服务器接收的消息信息</summary>
  /// <param name="netMessage">消息对象</param>
  public void SetNetMessage(INetMessage netMessage)
  {
    this.CreateNewMessage = (Func<INetMessage>) (() => netMessage);
  }

  /// <summary>
  /// 当客户端上线时候的触发的事件<br />
  /// Event triggered when the client goes online
  /// </summary>
  public event CommunicationServer.OnClientStatusChangeDelegate OnClientOnline;

  /// <summary>
  /// 当客户端下线时候的触发的事件<br />
  /// Event triggered when the client goes offline
  /// </summary>
  public event CommunicationServer.OnClientStatusChangeDelegate OnClientOffline;

  /// <summary>
  /// 新增加一个管道会话信息<br />
  /// A new pipeline session information has been added
  /// </summary>
  /// <param name="session">管道会话</param>
  public void AddSession(PipeSession session)
  {
    lock (this.lockSession)
      this.pipes.Add(session);
    this.LogDebugMsg(string.Format(StringResources.Language.ClientOnlineInfo, (object) session.Communication));
    CommunicationServer.OnClientStatusChangeDelegate onClientOnline = this.OnClientOnline;
    if (onClientOnline == null)
      return;
    onClientOnline((object) this, session);
  }

  /// <summary>
  /// 移除一个管道会话<br />
  /// Remove a pipeline session
  /// </summary>
  /// <param name="session">管道会话</param>
  /// <param name="reason">移除的原因</param>
  public void RemoveSession(PipeSession session, string reason)
  {
    bool flag = false;
    lock (this.lockSession)
      flag = this.pipes.Remove(session);
    if (!flag)
      return;
    session.Close();
    this.LogDebugMsg($"{string.Format(StringResources.Language.ClientOfflineInfo, (object) session.Communication)} {reason}");
    CommunicationServer.OnClientStatusChangeDelegate onClientOffline = this.OnClientOffline;
    if (onClientOffline != null)
      onClientOffline((object) this, session);
  }

  /// <summary>
  /// 获取或设置当前允许登录的最大客户端数量，默认为 uint.MaxValue = 4294967295<br />
  /// Gets or sets the maximum number of clients that are currently allowed to log in, which defaults to uint.MaxValue = 4294967295
  /// </summary>
  public uint SessionsMax { get; set; } = uint.MaxValue;

  /// <summary>
  /// 获取管道会话的列表<br />
  /// Get a list of pipeline sessions
  /// </summary>
  /// <returns>会话列表</returns>
  public PipeSession[] GetPipeSessions()
  {
    PipeSession[] pipeSessions = (PipeSession[]) null;
    lock (this.lockSession)
      pipeSessions = this.pipes.ToArray();
    return pipeSessions;
  }

  /// <summary>
  /// 指定超时时间移除当前的会话列表，只有是TCP的管道（<see cref="T:HslCommunication.Core.Pipe.PipeTcpNet" />）才需要被移除。<br />
  /// Specify a timeout to remove the current session list. Only TCP pipe (<see cref="T:HslCommunication.Core.Pipe.PipeTcpNet" />) need to be removed.
  /// </summary>
  /// <param name="timeSpan">指定的超时时间</param>
  public void RemoveSession(TimeSpan timeSpan)
  {
    lock (this.lockSession)
    {
      for (int index = this.pipes.Count - 1; index >= 0; --index)
      {
        PipeSession pipe = this.pipes[index];
        if (pipe.Communication is PipeTcpNet communication && communication.GetType() == typeof (PipeTcpNet) && DateTime.Now - pipe.HeartTime > timeSpan)
        {
          pipe.Close();
          this.pipes.RemoveAt(index);
          this.LogDebugMsg(string.Format(StringResources.Language.ClientOfflineInfo, (object) pipe.Communication) + $" Not communication for times[{timeSpan}]");
        }
      }
    }
  }

  /// <summary>
  /// 新增一个主动连接的请求，将不会收到是否连接成功的信息，当网络中断及奔溃之后，会自动重新连接。<br />
  /// A new active connection request will not receive a message whether the connection is successful. When the network is interrupted and crashed, it will automatically reconnect.
  /// </summary>
  /// <param name="ipAddress">对方的Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="dtu">使用自定义的DTU数据报文</param>
  public RemoteConnectInfo ConnectRemoteServer(string ipAddress, int port, byte[] dtu = null)
  {
    RemoteConnectInfo state = new RemoteConnectInfo(ipAddress, port, dtu);
    ThreadPool.QueueUserWorkItem(new WaitCallback(this.ConnectIpEndPoint), (object) state);
    return state;
  }

  /// <summary>
  /// 创建一个指定的异形客户端连接，使用Hsl协议来发送注册包<br />
  /// Create a specified profiled client connection and use the Hsl protocol to send registration packets
  /// </summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="dtuId">设备唯一ID号，最长11</param>
  /// <param name="password">密码信息</param>
  /// <param name="needAckResult">是否需要返回注册结果报文</param>
  /// <returns>是否成功连接</returns>
  public RemoteConnectInfo ConnectHslAlientClient(
    string ipAddress,
    int port,
    string dtuId,
    string password = "",
    bool needAckResult = true)
  {
    RemoteConnectInfo state = new RemoteConnectInfo(ipAddress, port, dtuId, password, needAckResult);
    ThreadPool.QueueUserWorkItem(new WaitCallback(this.ConnectIpEndPoint), (object) state);
    return state;
  }

  private OperateResult CheckConnectRemote(RemoteConnectInfo remoteConnect, PipeSession session)
  {
    PipeTcpNet communication = session.Communication as PipeTcpNet;
    if (remoteConnect.DtuBytes != null)
    {
      OperateResult operateResult = communication.Send(remoteConnect.DtuBytes);
      if (!operateResult.IsSuccess)
        return operateResult;
      if (remoteConnect.NeedAckResult)
      {
        OperateResult<byte[]> message = communication.ReceiveMessage((INetMessage) new AlienMessage(), (byte[]) null);
        if (!message.IsSuccess)
          return (OperateResult) message;
        switch (message.Content[5])
        {
          case 1:
            return new OperateResult(StringResources.Language.DeviceCurrentIsLoginRepeat);
          case 2:
            return new OperateResult(StringResources.Language.DeviceCurrentIsLoginForbidden);
          case 3:
            return new OperateResult(StringResources.Language.PasswordCheckFailed);
        }
      }
    }
    OperateResult operateResult1 = this.SocketAcceptExtraCheck(communication.Socket, remoteConnect.EndPoint);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    if (this.ThreadPoolLoginAfterClientCheck != null)
    {
      OperateResult operateResult2 = this.ThreadPoolLoginAfterClientCheck(session, remoteConnect.EndPoint);
      if (!operateResult2.IsSuccess)
        return operateResult2;
    }
    return OperateResult.CreateSuccessResult();
  }

  private void ConnectIpEndPoint(object obj)
  {
    if (!(obj is RemoteConnectInfo remoteConnectInfo))
      return;
    if (remoteConnectInfo.Status == DtuStatus.Closed)
    {
      remoteConnectInfo.Session?.Close();
    }
    else
    {
      remoteConnectInfo.Status = DtuStatus.Connecting;
      OperateResult<Socket> socketAndConnect = NetSupport.CreateSocketAndConnect(remoteConnectInfo.EndPoint, 10000);
      if (!socketAndConnect.IsSuccess)
      {
        this.LogDebugMsg($"RemoteConnectInfo[{remoteConnectInfo.EndPoint}] Socket Connected Failed : {socketAndConnect.Message} 10s later retry...");
        HslHelper.ThreadSleep(10000);
        ThreadPool.QueueUserWorkItem(new WaitCallback(this.ConnectIpEndPoint), (object) remoteConnectInfo);
      }
      else
      {
        this.LogDebugMsg($"RemoteConnectInfo[{remoteConnectInfo.EndPoint}] Socket Connected Success");
        PipeTcpNet pipeTcpNet = new PipeTcpNet(remoteConnectInfo.EndPoint.Address.ToString(), remoteConnectInfo.EndPoint.Port);
        pipeTcpNet.Socket = socketAndConnect.Content;
        PipeSession session = this.CreatePipeSession((CommunicationPipe) pipeTcpNet);
        OperateResult operateResult = this.CheckConnectRemote(remoteConnectInfo, session);
        if (!operateResult.IsSuccess)
        {
          this.LogDebugMsg($"RemoteConnectInfo[{remoteConnectInfo.EndPoint}] Socket Check Failed : {operateResult.Message} 10s later retry...");
          NetSupport.CloseSocket(socketAndConnect.Content);
          HslHelper.ThreadSleep(10000);
          ThreadPool.QueueUserWorkItem(new WaitCallback(this.ConnectIpEndPoint), (object) remoteConnectInfo);
        }
        else
        {
          this.LogDebugMsg($"RemoteConnectInfo[{remoteConnectInfo.EndPoint}] Socket Check Success");
          remoteConnectInfo.Session = session;
          remoteConnectInfo.Status = DtuStatus.Connected;
          session.OnlineTime = DateTime.Now;
          try
          {
            pipeTcpNet.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.dtuSocketAsyncCallBack, (object) remoteConnectInfo);
            this.AddSession(session);
          }
          catch (Exception ex)
          {
            this.LogDebugMsg($"ConnectIpEndPoint[{remoteConnectInfo.EndPoint}] Socket.BeginReceive failed: " + ex.Message);
            NetSupport.CloseSocket(pipeTcpNet.Socket);
            ThreadPool.QueueUserWorkItem(new WaitCallback(this.ConnectIpEndPoint), (object) remoteConnectInfo);
          }
        }
      }
    }
  }

  private void InitiativeSocketAsyncCallBack(IAsyncResult ar)
  {
    if (!(ar.AsyncState is RemoteConnectInfo asyncState))
      return;
    PipeSession session = asyncState.Session;
    PipeTcpNet communication = session.Communication as PipeTcpNet;
    byte[] content;
    try
    {
      Socket socket = communication.Socket;
      if (socket == null)
      {
        this.RemoveSession(session, string.Empty);
        return;
      }
      socket.EndReceive(ar);
      INetMessage newNetMessage = this.GetNewNetMessage();
      OperateResult<byte[]> message = communication.ReceiveMessage(newNetMessage, (byte[]) null, false);
      if (!message.IsSuccess)
      {
        this.RemoveSession(session, message.Message);
        if (!this.IsStarted)
          return;
        this.LogDebugMsg($"RemoteConnectInfo[{asyncState.EndPoint}] Socket Connected 10s later retry...");
        HslHelper.ThreadSleep(10000);
        this.ConnectIpEndPoint((object) asyncState);
        return;
      }
      session.HeartTime = DateTime.Now;
      content = message.Content;
    }
    catch (Exception ex)
    {
      this.RemoveSession(session, ex.Message);
      if (!this.IsStarted)
        return;
      this.ConnectIpEndPoint((object) asyncState);
      return;
    }
    CommunicationServer.PipeMessageReceived pipeMessageReceived = this.OnPipeMessageReceived;
    if (pipeMessageReceived != null)
      pipeMessageReceived(session, content);
    try
    {
      communication.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.dtuSocketAsyncCallBack, (object) asyncState);
    }
    catch (Exception ex)
    {
      this.RemoveSession(session, ex.Message);
      HslHelper.ThreadSleep(1000);
      if (!this.IsStarted)
        return;
      this.ConnectIpEndPoint((object) asyncState);
    }
  }

  private void SetUdpIp(PipeSession session)
  {
    PipeUdpNet communication = session.Communication as PipeUdpNet;
    if (!(this.udpEndPoint is IPEndPoint udpEndPoint))
      return;
    communication.IpAddress = udpEndPoint.Address.ToString();
    communication.Port = udpEndPoint.Port;
  }

  private void UdpRefreshReceive(PipeSession session)
  {
    try
    {
      this.udpServer.BeginReceiveFrom(this.udpBuffer, 0, this.udpBuffer.Length, SocketFlags.None, ref this.udpEndPoint, this.udpBeginReceiveCallback, (object) session);
    }
    catch (Exception ex)
    {
      this.RemoveSession(session, "UdpRefreshReceive exception: " + ex.Message);
    }
  }

  private void UdpAsyncCallback(IAsyncResult ar)
  {
    if (!(ar.AsyncState is PipeSession asyncState))
      return;
    PipeUdpNet communication = asyncState.Communication as PipeUdpNet;
    if (communication.Socket == null)
    {
      this.RemoveSession(asyncState, "Server closed");
    }
    else
    {
      byte[] buffer;
      try
      {
        int from = communication.Socket.EndReceiveFrom(ar, ref this.udpEndPoint);
        asyncState.HeartTime = DateTime.Now;
        this.SetUdpIp(asyncState);
        if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
        {
          this.RemoveSession(asyncState, StringResources.Language.AuthorizationFailed);
          return;
        }
        INetMessage newNetMessage = this.GetNewNetMessage();
        if (newNetMessage != null)
        {
          OperateResult<byte[]> message = communication.ReceiveMessage(newNetMessage, (byte[]) null, this.udpBuffer.SelectBegin<byte>(from), closeOnException: false);
          if (!message.IsSuccess)
          {
            this.LogDebugMsg($"<{communication}> Udp ReceiveMessage faild: " + message.Message + (from > 0 ? this.udpBuffer.SelectBegin<byte>(from).ToHexString(' ') : string.Empty));
            this.UdpRefreshReceive(asyncState);
            return;
          }
          buffer = message.Content;
        }
        else
          buffer = this.udpBuffer.SelectBegin<byte>(from);
      }
      catch (ObjectDisposedException ex)
      {
        this.RemoveSession(asyncState, "Socket ObjectDisposedException");
        return;
      }
      catch (Exception ex)
      {
        this.LogDebugMsg($"<{communication}> UdpAsyncCallback faild: " + ex.Message);
        this.UdpRefreshReceive(asyncState);
        return;
      }
      CommunicationServer.PipeMessageReceived pipeMessageReceived = this.OnPipeMessageReceived;
      if (pipeMessageReceived != null)
        pipeMessageReceived(asyncState, buffer);
      this.UdpRefreshReceive(asyncState);
    }
  }

  /// <summary>当线程检查后，进行登录之前的检查，通常用于自定义的握手包校验操作。仅对TCP通信的时候有效。</summary>
  public Func<PipeSession, IPEndPoint, OperateResult> ThreadPoolLoginAfterClientCheck { get; set; }

  /// <summary>创建会话状态的委托对象，也就可以自己指定创建自定义的会话</summary>
  public Func<CommunicationPipe, PipeSession> CreatePipeSession { get; set; }

  /// <summary>
  /// 当客户端连接到服务器，并听过额外的检查后，进行回调的方法<br />
  /// Callback method when the client connects to the server and has heard additional checks
  /// </summary>
  /// <param name="pipeTcpNet">socket对象</param>
  /// <param name="endPoint">远程的终结点</param>
  protected override void ThreadPoolLogin(PipeTcpNet pipeTcpNet, IPEndPoint endPoint)
  {
    PipeSession pipeSession = this.CreatePipeSession((CommunicationPipe) pipeTcpNet);
    if (this.ThreadPoolLoginAfterClientCheck != null && !this.ThreadPoolLoginAfterClientCheck(pipeSession, endPoint).IsSuccess)
    {
      this.LogNet?.WriteDebug(this.ToString(), string.Format(StringResources.Language.ClientDisableLogin, (object) endPoint) + " LoginAfterClientCheck failed");
      pipeTcpNet?.CloseCommunication();
    }
    else if ((long) this.GetPipeSessions().Length >= (long) this.SessionsMax)
    {
      this.LogNet?.WriteDebug(this.ToString(), string.Format(StringResources.Language.ClientDisableLogin, (object) endPoint) + $" Online count > SessionsMax({this.SessionsMax})");
      pipeTcpNet?.CloseCommunication();
    }
    else
    {
      try
      {
        pipeTcpNet.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) pipeSession);
        this.AddSession(pipeSession);
      }
      catch (Exception ex)
      {
        this.LogDebugMsg($"{StringResources.Language.SocketReceiveException} {ex.Message}");
      }
    }
  }

  private async void ReceiveCallback(IAsyncResult ar)
  {
    if (!(ar.AsyncState is PipeSession session))
    {
      session = (PipeSession) null;
    }
    else
    {
      PipeTcpNet pipeTcpNet = session.Communication as PipeTcpNet;
      byte[] buffer = (byte[]) null;
      try
      {
        Socket client = pipeTcpNet.Socket;
        if (client == null)
        {
          this.RemoveSession(session, string.Empty);
          session = (PipeSession) null;
          return;
        }
        int bytesRead = client.EndReceive(ar);
        INetMessage netMessage = this.GetNewNetMessage();
        OperateResult<byte[]> read = await pipeTcpNet.ReceiveMessageAsync(netMessage, (byte[]) null, false);
        if (!read.IsSuccess)
        {
          this.RemoveSession(session, read.Message);
          session = (PipeSession) null;
          return;
        }
        session.HeartTime = DateTime.Now;
        buffer = read.Content;
        client = (Socket) null;
        netMessage = (INetMessage) null;
        read = (OperateResult<byte[]>) null;
      }
      catch (Exception ex)
      {
        if (ex.Message.Contains(StringResources.Language.SocketRemoteCloseException))
        {
          this.RemoveSession(session, string.Empty);
          session = (PipeSession) null;
          return;
        }
        this.RemoveSession(session, ex.Message);
        session = (PipeSession) null;
        return;
      }
      CommunicationServer.PipeMessageReceived pipeMessageReceived = this.OnPipeMessageReceived;
      if (pipeMessageReceived != null)
        pipeMessageReceived(session, buffer);
      try
      {
        pipeTcpNet.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) session);
      }
      catch (Exception ex)
      {
        this.RemoveSession(session, ex.Message);
        session = (PipeSession) null;
        return;
      }
      pipeTcpNet = (PipeTcpNet) null;
      buffer = (byte[]) null;
      session = (PipeSession) null;
    }
  }

  /// <summary>当管道接收到消息时触发</summary>
  public event CommunicationServer.PipeMessageReceived OnPipeMessageReceived;

  /// <summary>
  /// 获取或设置串口模式下，接收一条数据最短的时间要求，当设备发送的数据非常慢的时候，或是分割发送数据的时候，就需要将本值设置的大一点，默认为20ms<br />
  /// Get or set the shortest time required to receive a piece of data in serial port mode.
  /// When the data sent by the device is very slow, or when the data is divided and sent, you need to set this value to a larger value, the default is 20ms
  /// </summary>
  public int SerialReceiveAtleastTime { get; set; } = 20;

  /// <summary>
  /// 获取或设置当前的服务器接收串口数据时候，是否强制只接收一次数据，默认为false，适合点对点通信，如果你总线形式的连接，则需要设置 True<br />
  /// Get or set whether to force the data to be received only once when the current server receives serial port data. The default value is false,
  /// which is suitable for point-to-point communication. If you have a bus connection, you need to set True
  /// </summary>
  public bool ForceSerialReceiveOnce { get; set; }

  /// <summary>
  /// 启动串口的从机服务，使用默认的参数进行初始化串口，9600波特率，8位数据位，无奇偶校验，1位停止位<br />
  /// Start the slave service of serial, initialize the serial port with default parameters, 9600 baud rate, 8 data bits, no parity, 1 stop bit
  /// </summary>
  /// <remarks>
  /// com支持格式化的方式，例如输入 COM3-9600-8-N-1，COM5-19200-7-E-2，其中奇偶校验的字母可选，N:无校验，O：奇校验，E:偶校验，停止位可选 0, 1, 2, 1.5 四种选项
  /// </remarks>
  /// <param name="com">串口信息</param>
  public OperateResult StartSerialSlave(string com)
  {
    return com.Contains("-") || com.Contains(";") ? this.StartSerialSlave((Action<SerialPort>) (sp => sp.IniSerialByFormatString(com))) : this.StartSerialSlave(com, 9600);
  }

  /// <summary>
  /// 启动串口的从机服务，使用默认的参数进行初始化串口，8位数据位，无奇偶校验，1位停止位<br />
  /// Start the slave service of serial, initialize the serial port with default parameters, 8 data bits, no parity, 1 stop bit
  /// </summary>
  /// <param name="com">串口信息</param>
  /// <param name="baudRate">波特率</param>
  public OperateResult StartSerialSlave(string com, int baudRate)
  {
    return this.StartSerialSlave((Action<SerialPort>) (sp =>
    {
      sp.PortName = com;
      sp.BaudRate = baudRate;
      sp.DataBits = 8;
      sp.Parity = Parity.None;
      sp.StopBits = StopBits.One;
    }));
  }

  /// <summary>
  /// 启动串口的从机服务，使用指定的参数进行初始化串口，指定数据位，指定奇偶校验，指定停止位<br />
  /// </summary>
  /// <param name="com">串口信息</param>
  /// <param name="baudRate">波特率</param>
  /// <param name="dataBits">数据位</param>
  /// <param name="parity">奇偶校验</param>
  /// <param name="stopBits">停止位</param>
  public OperateResult StartSerialSlave(
    string com,
    int baudRate,
    int dataBits,
    Parity parity,
    StopBits stopBits)
  {
    return this.StartSerialSlave((Action<SerialPort>) (sp =>
    {
      sp.PortName = com;
      sp.BaudRate = baudRate;
      sp.DataBits = dataBits;
      sp.Parity = parity;
      sp.StopBits = stopBits;
    }));
  }

  /// <summary>
  /// 启动串口的从机服务，使用自定义的初始化方法初始化串口的参数<br />
  /// Start the slave service of serial and initialize the parameters of the serial port using a custom initialization method
  /// </summary>
  /// <param name="inni">初始化信息的委托</param>
  public OperateResult StartSerialSlave(Action<SerialPort> inni)
  {
    PipeSerialPort pipeSerialPort = new PipeSerialPort();
    pipeSerialPort.SerialPortInni(inni);
    pipeSerialPort.GetPipe().DataReceived += new SerialDataReceivedEventHandler(this.SerialPort_DataReceived);
    OperateResult operateResult = (OperateResult) pipeSerialPort.OpenCommunication();
    if (!operateResult.IsSuccess)
      return operateResult;
    this.AddSession(this.CreatePipeSession((CommunicationPipe) pipeSerialPort));
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 关闭提供从机服务的串口对象<br />
  /// Close the serial port object that provides slave services
  /// </summary>
  public void CloseSerialSlave()
  {
    lock (this.lockSession)
    {
      for (int index = this.pipes.Count - 1; index >= 0; --index)
      {
        if (this.pipes[index].Communication is PipeSerialPort communication)
        {
          communication.CloseCommunication();
          this.pipes.RemoveAt(index);
        }
      }
    }
  }

  private PipeSession FindSerialPortSession(string portName)
  {
    lock (this.lockSession)
    {
      for (int index = this.pipes.Count - 1; index >= 0; --index)
      {
        PipeSession pipe = this.pipes[index];
        if (pipe.Communication is PipeSerialPort communication && communication.GetPipe().PortName == portName)
          return pipe;
      }
    }
    return (PipeSession) null;
  }

  /// <summary>接收到串口数据的时候触发</summary>
  /// <param name="sender">串口对象</param>
  /// <param name="e">消息</param>
  private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
  {
    if (!HslCommunication.Authorization.nzugaydgwadawdibbas())
      return;
    SerialPort serialPort = sender as SerialPort;
    PipeSession serialPortSession = this.FindSerialPortSession(serialPort.PortName);
    if (serialPortSession == null)
      return;
    int num1 = 0;
    int num2 = 0;
    byte[] buffer = new byte[2048 /*0x0800*/];
    DateTime now = DateTime.Now;
    while (true)
    {
      try
      {
        int num3 = serialPort.Read(buffer, num1, serialPort.BytesToRead);
        if (num3 != 0 || num2 == 0 || (DateTime.Now - now).TotalMilliseconds < (double) this.SerialReceiveAtleastTime)
        {
          num1 += num3;
          ++num2;
        }
        else
          break;
      }
      catch (Exception ex)
      {
        this.LogDebugMsg("SerialPort_DataReceived Error: " + ex.Message);
        break;
      }
      if ((!this.ForceSerialReceiveOnce || num1 <= 0) && !this.CheckSerialReceiveDataComplete(buffer, num1))
        HslHelper.ThreadSleep(20);
      else
        break;
    }
    if (num1 == 0)
      return;
    try
    {
      buffer = buffer.SelectBegin<byte>(num1);
    }
    catch (Exception ex)
    {
      this.LogDebugMsg("SerialPort_DataReceived: " + ex.Message);
    }
    serialPortSession.HeartTime = DateTime.Now;
    CommunicationServer.PipeMessageReceived pipeMessageReceived = this.OnPipeMessageReceived;
    if (pipeMessageReceived == null)
      return;
    pipeMessageReceived(serialPortSession, buffer);
  }

  /// <summary>
  /// 检查串口接收的数据是否完成的方法，如果接收完成，则返回<c>True</c>
  /// </summary>
  /// <param name="buffer">缓存的数据信息</param>
  /// <param name="receivedLength">当前已经接收的数据长度信息</param>
  /// <returns>是否接收完成</returns>
  protected virtual bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return this.CheckSerialDataComplete != null && this.CheckSerialDataComplete(buffer, receivedLength);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.GetNewNetMessage" />
  protected virtual INetMessage GetNewNetMessage()
  {
    return this.CreateNewMessage == null ? (INetMessage) null : this.CreateNewMessage();
  }

  /// <inheritdoc />
  public override string ToString() => $"CommunicationServer[{this.Port}]";

  /// <summary>
  /// 表示客户端状态变化的委托信息<br />
  /// Delegate information representing the state change of the client
  /// </summary>
  /// <param name="server">当前的服务器对象信息</param>
  /// <param name="session">当前的客户端会话信息</param>
  public delegate void OnClientStatusChangeDelegate(object server, PipeSession session);

  /// <summary>接收管道消息的事件</summary>
  /// <param name="session">管道的会话，可能是TCP管道，可能是UDP管道，可能是串口管道</param>
  /// <param name="buffer">接收到的数据信息</param>
  public delegate void PipeMessageReceived(PipeSession session, byte[] buffer);
}
