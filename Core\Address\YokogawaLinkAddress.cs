﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.YokogawaLinkAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>
/// 横河PLC的地址表示类<br />
/// Yokogawa PLC address display class
/// </summary>
public class YokogawaLinkAddress : DeviceAddressDataBase
{
  /// <summary>
  /// 获取或设置等待读取的数据的代码<br />
  /// Get or set the code of the data waiting to be read
  /// </summary>
  public int DataCode { get; set; }

  /// <summary>
  /// 获取当前横河PLC的地址的二进制表述方式<br />
  /// Obtain the binary representation of the current Yokogawa PLC address
  /// </summary>
  /// <returns>二进制数据信息</returns>
  public byte[] GetAddressBinaryContent()
  {
    return new byte[6]
    {
      BitConverter.GetBytes(this.DataCode)[1],
      BitConverter.GetBytes(this.DataCode)[0],
      BitConverter.GetBytes(this.AddressStart)[3],
      BitConverter.GetBytes(this.AddressStart)[2],
      BitConverter.GetBytes(this.AddressStart)[1],
      BitConverter.GetBytes(this.AddressStart)[0]
    };
  }

  /// <inheritdoc />
  public override void Parse(string address, ushort length)
  {
    OperateResult<YokogawaLinkAddress> from = YokogawaLinkAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return;
    this.AddressStart = from.Content.AddressStart;
    this.Length = from.Content.Length;
    this.DataCode = from.Content.DataCode;
  }

  /// <inheritdoc />
  public override string ToString()
  {
    switch (this.DataCode)
    {
      case 2:
        return "B" + this.AddressStart.ToString();
      case 3:
        return "C" + this.AddressStart.ToString();
      case 4:
        return "D" + this.AddressStart.ToString();
      case 5:
        return "E" + this.AddressStart.ToString();
      case 6:
        return "F" + this.AddressStart.ToString();
      case 9:
        return "I" + this.AddressStart.ToString();
      case 12:
        return "L" + this.AddressStart.ToString();
      case 13:
        return "M" + this.AddressStart.ToString();
      case 18:
        return "R" + this.AddressStart.ToString();
      case 20:
        return "T" + this.AddressStart.ToString();
      case 22:
        return "V" + this.AddressStart.ToString();
      case 23:
        return "W" + this.AddressStart.ToString();
      case 24:
        return "X" + this.AddressStart.ToString();
      case 25:
        return "Y" + this.AddressStart.ToString();
      case 26:
        return "Z" + this.AddressStart.ToString();
      case 33:
        return "TN" + this.AddressStart.ToString();
      case 49:
        return "CN" + this.AddressStart.ToString();
      default:
        return this.AddressStart.ToString();
    }
  }

  /// <summary>从普通的PLC的地址转换为HSL标准的地址信息</summary>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>是否成功的地址结果</returns>
  public static OperateResult<YokogawaLinkAddress> ParseFrom(string address, ushort length)
  {
    try
    {
      int num1;
      int num2;
      if (address.StartsWith("CN") || address.StartsWith("cn"))
      {
        num1 = 49;
        num2 = int.Parse(address.Substring(2));
      }
      else if (address.StartsWith("TN") || address.StartsWith("tn"))
      {
        num1 = 33;
        num2 = int.Parse(address.Substring(2));
      }
      else if (address.StartsWith("X") || address.StartsWith("x"))
      {
        num1 = 24;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("Y") || address.StartsWith("y"))
      {
        num1 = 25;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("I") || address.StartsWith("i"))
      {
        num1 = 9;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("E") || address.StartsWith("e"))
      {
        num1 = 5;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("M") || address.StartsWith("m"))
      {
        num1 = 13;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("T") || address.StartsWith("t"))
      {
        num1 = 20;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("C") || address.StartsWith("c"))
      {
        num1 = 3;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("L") || address.StartsWith("l"))
      {
        num1 = 12;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("D") || address.StartsWith("d"))
      {
        num1 = 4;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("B") || address.StartsWith("b"))
      {
        num1 = 2;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("F") || address.StartsWith("f"))
      {
        num1 = 6;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("R") || address.StartsWith("r"))
      {
        num1 = 18;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("V") || address.StartsWith("v"))
      {
        num1 = 22;
        num2 = int.Parse(address.Substring(1));
      }
      else if (address.StartsWith("Z") || address.StartsWith("z"))
      {
        num1 = 26;
        num2 = int.Parse(address.Substring(1));
      }
      else
      {
        if (!address.StartsWith("W") && !address.StartsWith("w"))
          throw new Exception(StringResources.Language.NotSupportedDataType);
        num1 = 23;
        num2 = int.Parse(address.Substring(1));
      }
      YokogawaLinkAddress yokogawaLinkAddress = new YokogawaLinkAddress();
      yokogawaLinkAddress.DataCode = num1;
      yokogawaLinkAddress.AddressStart = num2;
      yokogawaLinkAddress.Length = length;
      return OperateResult.CreateSuccessResult<YokogawaLinkAddress>(yokogawaLinkAddress);
    }
    catch (Exception ex)
    {
      return new OperateResult<YokogawaLinkAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }
}
