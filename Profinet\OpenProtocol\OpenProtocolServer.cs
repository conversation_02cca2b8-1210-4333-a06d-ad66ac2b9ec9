﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.OpenProtocolServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>开放协议的虚拟服务器</summary>
public class OpenProtocolServer : CommunicationServer
{
  private Timer timer;
  private long tick = 0;
  private DateTime mid80Time = DateTime.Now;
  private string parameterSetID = "001";
  private DateTime parameterSetTime = DateTime.Now;

  /// <summary>实例化一个默认的对象</summary>
  public OpenProtocolServer()
  {
    this.OnPipeMessageReceived += new CommunicationServer.PipeMessageReceived(this.OpenProtocolServer_OnPipeMessageReceived);
    this.CreatePipeSession = (Func<CommunicationPipe, PipeSession>) (m =>
    {
      return (PipeSession) new OpenProtocolSession()
      {
        Communication = m
      };
    });
    this.timer = new Timer(new TimerCallback(this.ThreadKeepAlive), (object) null, 2000, 2000);
  }

  private byte[] CreateMID0015Message()
  {
    return OpenProtocolNet.BuildOpenProtocolMessage(15, 1, 0, -1, -1, false, this.parameterSetID, this.parameterSetTime.ToString("yyyy-MM-dd:HH:mm:ss")).Content;
  }

  private void ThreadKeepAlive(object state)
  {
    this.SendAll(this.CreateMID0015Message(), (Func<OpenProtocolSession, bool>) (m => m.MID0014Subscribe));
    this.SendAll(OpenProtocolNet.BuildOpenProtocolMessage(35, 1, 0, -1, -1, true, "01", "0", "0", $"{this.tick:D4}", $"{this.tick:D4}", this.GetTimeNowOpenString()).Content, (Func<OpenProtocolSession, bool>) (m => m.MID0034Subscribe));
    this.SendAll(OpenProtocolNet.BuildOpenProtocolMessage(52, 1, 0, -1, -1, true, $"{this.tick:D25}").Content, (Func<OpenProtocolSession, bool>) (m => m.MID0051Subscribe));
    this.SendAll(OpenProtocolNet.BuildOpenProtocolMessage(61, 1, 0, -1, -1, true, "0001", "01", "airbag" + $"{this.tick:D19}", "KPOL3456JKLO897".PadRight(25, ' '), "00", "003", "0000", $"{this.tick:D4}", "0", "0", "1", $"{this.tick:D6}", "001400", "001200", "000739", "00000", "09999", "00000", "00000", this.GetTimeNowOpenString(-10), this.GetTimeNowOpenString(), "1", "3456798765").Content, (Func<OpenProtocolSession, bool>) (m => m.MID0060Subscribe));
    this.SendAll(OpenProtocolNet.BuildOpenProtocolMessage(71, 1, 0, -1, -1, true, "E404", "1", "1", this.GetTimeNowOpenString()).Content, (Func<OpenProtocolSession, bool>) (m => m.MID0070Subscribe));
    this.SendAll(OpenProtocolNet.BuildOpenProtocolMessage(106, 1, 0, -1, -1, true, "11", "01", "4294967295", "07", "ABCD1234569876543210", this.GetTimeNowOpenString(), "50", $"{this.tick:D20}", "1", "0", $"{this.tick:D40}", "01").Content, (Func<OpenProtocolSession, bool>) (m => m.MID0105Subscribe));
    ++this.tick;
    if (this.tick < 10000L)
      return;
    this.tick = 0L;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new OpenProtocolMessage();

  private byte[] CreateCommandAccepted(int mid)
  {
    return OpenProtocolNet.BuildOpenProtocolMessage(5, 1, -1, -1, -1, false, mid.ToString("D4")).Content;
  }

  private byte[] CreateCommandError(int mid, int err)
  {
    return OpenProtocolNet.BuildOpenProtocolMessage(4, 1, -1, -1, -1, false, mid.ToString("D4") + err.ToString("D2")).Content;
  }

  private string GetTimeNowOpenString(int seconds = 0)
  {
    DateTime dateTime = DateTime.Now;
    dateTime = dateTime.AddSeconds((double) seconds);
    return dateTime.ToString("yyyy-MM-dd:HH:mm:ss");
  }

  private void SendData(PipeSession session, byte[] buffer)
  {
    session.Communication.Send(buffer);
    this.LogDebugMsg($"{session} Send: " + SoftBasic.GetAsciiStringRender(buffer));
  }

  private static int GetRevisionInfo(byte[] cmds)
  {
    string str = Encoding.ASCII.GetString(cmds, 8, 3);
    return string.IsNullOrEmpty(str) ? 1 : Convert.ToInt32(str);
  }

  private void OpenProtocolServer_OnPipeMessageReceived(PipeSession session, byte[] buffer)
  {
    this.LogDebugMsg($"{session} Recv: " + SoftBasic.GetAsciiStringRender(buffer));
    if (buffer.Length < 20)
      return;
    try
    {
      int int32 = Convert.ToInt32(Encoding.ASCII.GetString(buffer, 4, 4));
      if (int32 > 0 && int32 < 10)
      {
        switch (int32)
        {
          case 1:
            this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(2, 1, -1, -1, -1, true, "0001", "01", "Airbag1".PadRight(25, ' ')).Content);
            break;
          case 3:
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
          case 8:
            if (Convert.ToInt32(Encoding.ASCII.GetString(buffer, 20, 4)) == 1202 && session is OpenProtocolSession openProtocolSession1)
              openProtocolSession1.MID0060Subscribe = true;
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
          case 9:
            if (Convert.ToInt32(Encoding.ASCII.GetString(buffer, 20, 4)) == 1202 && session is OpenProtocolSession openProtocolSession2)
              openProtocolSession2.MID0060Subscribe = false;
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
        }
      }
      else if (int32 >= 10 && int32 < 30)
      {
        switch (int32)
        {
          case 10:
            this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(11, 1, -1, -1, -1, false, "002001002").Content);
            break;
          case 12:
            string str1 = Encoding.ASCII.GetString(buffer, 20, 3);
            this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(13, 1, -1, -1, -1, true, str1, "Airbag1".PadRight(25, ' '), "1", "00", "123456", "654321", "112233", "00000", "99999", $"0{this.tick:D4}").Content);
            break;
          case 14:
            if (!(session is OpenProtocolSession openProtocolSession3))
              break;
            if (!openProtocolSession3.MID0014Subscribe)
            {
              openProtocolSession3.MID0014Subscribe = true;
              this.SendData(session, this.CreateCommandAccepted(int32));
              this.SendData(session, this.CreateMID0015Message());
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 13));
            break;
          case 17:
            if (!(session is OpenProtocolSession openProtocolSession4))
              break;
            if (openProtocolSession4.MID0014Subscribe)
            {
              openProtocolSession4.MID0014Subscribe = false;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 14));
            break;
          case 18:
            this.parameterSetID = Encoding.ASCII.GetString(buffer, 20, 3);
            this.parameterSetTime = DateTime.Now;
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
          case 19:
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
          case 20:
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
        }
      }
      else if (int32 >= 30 && int32 < 40)
      {
        switch (int32)
        {
          case 30:
            switch (OpenProtocolServer.GetRevisionInfo(buffer))
            {
              case 1:
                this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(31 /*0x1F*/, 1, -1, -1, -1, false, "020102").Content);
                return;
              case 2:
                this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(31 /*0x1F*/, 2, -1, -1, -1, false, "000200010002").Content);
                return;
              default:
                return;
            }
          case 32 /*0x20*/:
            if (buffer.Length < 22)
            {
              this.SendData(session, this.CreateCommandError(int32, 1));
              break;
            }
            string str2 = Encoding.ASCII.GetString(buffer, 20, 2);
            this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(33, 1, -1, -1, -1, true, str2, "Airbag1".PadRight(25, ' '), "0", $"{this.tick:D4}", "00000", "0", "1", "1", "0", "0", "1", "01", "15:011:0:22;").Content);
            break;
          case 34:
            if (!(session is OpenProtocolSession openProtocolSession5))
              break;
            if (!openProtocolSession5.MID0034Subscribe)
            {
              openProtocolSession5.MID0034Subscribe = true;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 18));
            break;
          case 37:
            if (!(session is OpenProtocolSession openProtocolSession6))
              break;
            if (openProtocolSession6.MID0034Subscribe)
            {
              openProtocolSession6.MID0034Subscribe = false;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 19));
            break;
          case 38:
            if (OpenProtocolServer.GetRevisionInfo(buffer) == 1)
            {
              if (Encoding.ASCII.GetString(buffer, 20, 2) == "01")
              {
                this.SendData(session, this.CreateCommandAccepted(int32));
                break;
              }
              this.SendData(session, this.CreateCommandError(int32, 17));
              break;
            }
            if (Encoding.ASCII.GetString(buffer, 20, 4) == "0001")
              this.SendData(session, this.CreateCommandAccepted(int32));
            else
              this.SendData(session, this.CreateCommandError(int32, 17));
            break;
          case 39:
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
        }
      }
      else if (int32 >= 40 && int32 < 50)
      {
        if (int32 == 40)
          this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(41, 1, -1, -1, -1, true, "C341212".PadRight(14, ' '), "548796".PadRight(10, ' '), this.GetTimeNowOpenString(), "670919".PadRight(10, ' ')).Content);
        else
          this.SendData(session, this.CreateCommandAccepted(int32));
      }
      else if (int32 >= 50 && int32 < 60)
      {
        switch (int32)
        {
          case 50:
            this.SendData(session, this.CreateCommandAccepted(int32));
            this.SetVinNumber(Encoding.ASCII.GetString(buffer, 20, 25).Trim());
            break;
          case 51:
            if (!(session is OpenProtocolSession openProtocolSession7))
              break;
            if (!openProtocolSession7.MID0051Subscribe)
            {
              openProtocolSession7.MID0051Subscribe = true;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 6));
            break;
          case 54:
            if (session is OpenProtocolSession openProtocolSession8)
            {
              if (openProtocolSession8.MID0051Subscribe)
              {
                openProtocolSession8.MID0051Subscribe = false;
                this.SendData(session, this.CreateCommandAccepted(int32));
              }
              else
                this.SendData(session, this.CreateCommandError(int32, 7));
            }
            break;
        }
      }
      else if (int32 >= 60 && int32 < 70)
      {
        switch (int32)
        {
          case 60:
            if (!(session is OpenProtocolSession openProtocolSession9))
              break;
            if (!openProtocolSession9.MID0060Subscribe)
            {
              openProtocolSession9.MID0060Subscribe = true;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 9));
            break;
          case 63 /*0x3F*/:
            if (!(session is OpenProtocolSession openProtocolSession10))
              break;
            if (openProtocolSession10.MID0060Subscribe)
            {
              openProtocolSession10.MID0060Subscribe = false;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 10));
            break;
          case 64 /*0x40*/:
            string str3 = Encoding.ASCII.GetString(buffer, 20, 10);
            this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(65, 1, -1, -1, -1, true, str3, "AIRBAG".PadRight(25, ' '), "001", $"{this.tick:D4}", "0", "0", "0", "001467", "00046", this.GetTimeNowOpenString(), "2").Content);
            break;
        }
      }
      else if (int32 >= 70 && int32 < 80 /*0x50*/)
      {
        switch (int32)
        {
          case 70:
            if (!(session is OpenProtocolSession openProtocolSession11))
              break;
            if (!openProtocolSession11.MID0070Subscribe)
            {
              openProtocolSession11.MID0070Subscribe = true;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 11));
            break;
          case 73:
            if (!(session is OpenProtocolSession openProtocolSession12))
              break;
            if (openProtocolSession12.MID0070Subscribe)
            {
              openProtocolSession12.MID0070Subscribe = false;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 12));
            break;
          case 78:
            this.SendData(session, this.CreateCommandAccepted(int32));
            break;
        }
      }
      else if (int32 >= 80 /*0x50*/ && int32 < 90)
      {
        switch (int32)
        {
          case 80 /*0x50*/:
            this.SendData(session, OpenProtocolNet.BuildOpenProtocolMessage(81, 1, -1, -1, -1, false, this.mid80Time.ToString("yyyy-MM-dd:HH:mm:ss")).Content);
            break;
          case 82:
            byte[] bytes = buffer.SelectMiddle<byte>(20, 19);
            bytes[10] = (byte) 32 /*0x20*/;
            DateTime result;
            if (DateTime.TryParse(Encoding.ASCII.GetString(bytes), out result))
            {
              this.mid80Time = result;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 1));
            break;
        }
      }
      else if (int32 >= 105 && int32 < 110)
      {
        switch (int32)
        {
          case 105:
            if (!(session is OpenProtocolSession openProtocolSession13))
              break;
            if (!openProtocolSession13.MID0105Subscribe)
            {
              openProtocolSession13.MID0105Subscribe = true;
              this.SendData(session, this.CreateCommandAccepted(int32));
            }
            else
              this.SendData(session, this.CreateCommandError(int32, 9));
            break;
          case 109:
            if (session is OpenProtocolSession openProtocolSession14)
            {
              if (openProtocolSession14.MID0105Subscribe)
              {
                openProtocolSession14.MID0105Subscribe = false;
                this.SendData(session, this.CreateCommandAccepted(int32));
              }
              else
                this.SendData(session, this.CreateCommandError(int32, 10));
            }
            break;
        }
      }
      else if (int32 == 9999)
        this.SendData(session, buffer);
      else
        this.SendData(session, this.CreateCommandError(int32, 1));
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException("OpenProtocolServer_OnPipeMessageReceived -> ", ex);
    }
  }

  private void SendAll(byte[] buffer, Func<OpenProtocolSession, bool> select)
  {
    foreach (PipeSession pipeSession in this.GetPipeSessions())
    {
      if (pipeSession is OpenProtocolSession session && select(session))
        this.SendData((PipeSession) session, buffer);
    }
  }

  /// <summary>设置一个新的 vin number 数据，并且发布给所有订阅的客户端</summary>
  /// <param name="vin">VIN Number</param>
  public void SetVinNumber(string vin)
  {
    this.SendAll(OpenProtocolNet.BuildOpenProtocolMessage(51, 1, 0, -1, -1, true, vin.PadRight(25, ' ')).Content, (Func<OpenProtocolSession, bool>) (m => m.MID0051Subscribe));
  }

  /// <summary>发布一个SECS的消息，需要指定一些参数信息</summary>
  public void Publish(
    int mid,
    int revison,
    int stationId,
    int spindleId,
    List<string> parameters)
  {
    this.SendAll(OpenProtocolNet.BuildOpenProtocolMessage(mid, revison, 0, stationId, spindleId, false, parameters.ToArray()).Content, (Func<OpenProtocolSession, bool>) (m => true));
  }

  /// <inheritdoc />
  public override string ToString() => $"OpenProtocolServer[{this.Port}]";
}
