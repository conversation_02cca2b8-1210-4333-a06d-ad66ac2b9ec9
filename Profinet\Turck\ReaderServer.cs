﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Turck.ReaderServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;

#nullable disable
namespace HslCommunication.Profinet.Turck;

/// <summary>图尔克reader协议的虚拟服务器</summary>
public class ReaderServer : DeviceServer
{
  private int bytesOfBlock = 8;
  private SoftBuffer buffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>实例化一个默认的图尔克虚拟服务器</summary>
  public ReaderServer()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.buffer = new SoftBuffer(65536 /*0x010000*/);
  }

  /// <summary>获取或设置每个block占用的字节数信息</summary>
  public int BytesOfBlock
  {
    get => this.bytesOfBlock;
    set => this.bytesOfBlock = value;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new TurckReaderMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (!ReaderNet.CheckCRC(receive, receive.Length - 2))
      return OperateResult.CreateSuccessResult<byte[]>(ReaderNet.PackReaderCommand(new byte[5]
      {
        receive[3],
        (byte) 131,
        (byte) 0,
        (byte) 1,
        (byte) 0
      }));
    CommunicationPipe communication = session.Communication;
    byte[] numArray1;
    if (receive[3] == (byte) 104)
    {
      byte[] numArray2 = ReaderNet.PackReaderCommand(new byte[2]
      {
        (byte) 104,
        (byte) 137
      });
      this.LogSendMessage(numArray2, session);
      communication.Send(numArray2);
      numArray1 = this.ReadByMessage(receive);
    }
    else if (receive[3] == (byte) 105)
    {
      byte[] numArray3 = ReaderNet.PackReaderCommand(new byte[2]
      {
        (byte) 105,
        (byte) 137
      });
      this.LogSendMessage(numArray3, session);
      communication.Send(numArray3);
      numArray1 = this.WriteByMessage(receive);
    }
    else
    {
      if (receive[3] != (byte) 112 /*0x70*/)
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      byte[] numArray4 = ReaderNet.PackReaderCommand(new byte[2]
      {
        (byte) 112 /*0x70*/,
        (byte) 137
      });
      this.LogSendMessage(numArray4, session);
      communication.Send(numArray4);
      numArray1 = ReaderNet.PackReaderCommand(new byte[15]
      {
        (byte) 112 /*0x70*/,
        (byte) 138,
        (byte) 113,
        (byte) 38,
        (byte) 208 /*0xD0*/,
        (byte) 229,
        (byte) 215,
        (byte) 1,
        (byte) 8,
        (byte) 224 /*0xE0*/,
        (byte) 0,
        (byte) 0,
        (byte) 249,
        (byte) (this.bytesOfBlock - 1),
        (byte) 116
      });
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray1);
  }

  private byte[] ReadByMessage(byte[] receive)
  {
    byte[] bytes = this.buffer.GetBytes((int) receive[5] * this.bytesOfBlock, ((int) receive[6] + 1) * this.bytesOfBlock);
    return ReaderNet.PackReaderCommand(SoftBasic.SpliceArray<byte>(new byte[2]
    {
      receive[3],
      (byte) 154
    }, bytes));
  }

  private byte[] WriteByMessage(byte[] receive)
  {
    if (!this.EnableWrite)
      return ReaderNet.PackReaderCommand(new byte[5]
      {
        receive[3],
        (byte) 131,
        (byte) 18,
        (byte) 0,
        (byte) 0
      });
    if (((int) receive[6] + 1) * this.bytesOfBlock != receive.Length - 9)
      return ReaderNet.PackReaderCommand(new byte[5]
      {
        receive[3],
        (byte) 131,
        (byte) 1,
        (byte) 0,
        (byte) 0
      });
    this.buffer.SetBytes(receive.SelectMiddle<byte>(7, receive.Length - 9), (int) receive[5] * this.bytesOfBlock);
    return ReaderNet.PackReaderCommand(new byte[2]
    {
      receive[3],
      (byte) 138
    });
  }

  /// <inheritdoc />
  public override string ToString() => $"ReaderServer[{this.Port}]";
}
