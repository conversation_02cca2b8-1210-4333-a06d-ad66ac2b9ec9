﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Delta.IDelta
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Profinet.Delta;

/// <summary>台达PLC的相关的接口信息</summary>
public interface IDelta : IReadWriteDevice, IReadWriteNet
{
  /// <summary>
  /// 获取或设置当前的台达PLC的系列信息，默认为 DVP 系列<br />
  /// Get or set the current series information of Delta PLC, the default is DVP series
  /// </summary>
  DeltaSeries Series { get; set; }
}
