﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.RKC.TemperatureController
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Instrument.RKC.Helper;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.RKC;

/// <summary>
/// RKC的CD/CH系列数字式温度控制器的串口类对象，可以读取测量值，CT1输入值，CT2输入值等等，地址的地址需要参考API文档的示例<br />
/// The serial port object of RKC's CD/CH series digital temperature controller can read the measured value, CT1 input value,
/// CT2 input value, etc. The address of the address needs to refer to the example of the API document
/// </summary>
/// <remarks>
/// 只能使用ReadDouble(string),Write(string,double)方法来读写数据，设备的串口默认参数为 8-1-N,8 个数据位，一个停止位，无奇偶校验<br />
/// 地址支持站号信息，例如 s=2;M1
/// </remarks>
public class TemperatureController : DeviceSerialPort
{
  private byte station = 1;

  /// <inheritdoc cref="M:HslCommunication.Instrument.RKC.TemperatureControllerOverTcp.#ctor" />
  public TemperatureController()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new RkcTemperatureMessage();

  /// <inheritdoc cref="P:HslCommunication.Instrument.RKC.TemperatureControllerOverTcp.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.RKC.Helper.TemperatureControllerHelper.ReadDouble(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String)" />
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    OperateResult<double> result = TemperatureControllerHelper.ReadDouble((IReadWriteDevice) this, this.station, address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<double[]>((OperateResult) result);
    return OperateResult.CreateSuccessResult<double[]>(new double[1]
    {
      result.Content
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.RKC.Helper.TemperatureControllerHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Double)" />
  public override OperateResult Write(string address, double[] values)
  {
    return values == null || values.Length == 0 ? OperateResult.CreateSuccessResult() : TemperatureControllerHelper.Write((IReadWriteDevice) this, this.station, address, values[0]);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.RKC.Helper.TemperatureControllerHelper.ReadDouble(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<double[]> operateResult = await Task.Run<OperateResult<double[]>>((Func<OperateResult<double[]>>) (() => this.ReadDouble(address, length)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.RKC.Helper.TemperatureControllerHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Double)" />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"RkcTemperatureController[{this.PortName}:{this.BaudRate}]";
}
