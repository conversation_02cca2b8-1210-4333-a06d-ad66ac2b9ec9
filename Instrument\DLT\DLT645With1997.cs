﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT645With1997
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Instrument.DLT.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>
/// 基于多功能电能表通信协议实现的通讯类，参考的文档是DLT645-1997，主要实现了对电表数据的读取和一些功能方法，数据标识格式为 B6-11，具体参照文档手册。<br />
/// Based on the communication class implemented by the multi-function energy meter communication protocol, the reference document is DLT645-1997,
/// which mainly implements the reading of meter data and some functional methods, the data identification format is B6-11, please refer to the document manual for details.
/// </summary>
/// <remarks>
/// 如果一对多的模式，地址可以携带地址域访问，例如 "s=2;B6-11"，主要使用 <see cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ReadDouble(System.String,System.UInt16)" /> 方法来读取浮点数，<see cref="M:HslCommunication.Core.Device.DeviceCommunication.ReadString(System.String,System.UInt16)" /> 方法来读取字符串
/// </remarks>
/// <example>
///  具体的地址请参考相关的手册内容，如果没有，可以联系HSL作者或者，下面列举一些常用的地址<br />
///  <list type="table">
///    <listheader>
///      <term>DI1-DI0</term>
///      <term>读取方式</term>
///      <term>数据项名称</term>
///      <term>备注</term>
///    </listheader>
///    <item>
///      <term>90-10</term>
///      <term>ReadDouble</term>
///      <term>(当前)正向有功总电能(kwh)</term>
///      <term>90-11至90-1E表示费率1-14的正向有功电能，90-1F表示正向有功电能数据块</term>
///    </item>
///    <item>
///      <term>90-20</term>
///      <term>ReadDouble</term>
///      <term>(当前)反向有功总电能(kwh)</term>
///      <term>90-21至90-2E表示费率1-14的反向有功电能，90-2F表示反向有功电能数据块</term>
///    </item>
///    <item>
///      <term>91-10</term>
///      <term>ReadDouble</term>
///      <term>(当前)正向无功总电能(kvarh)</term>
///      <term>91-11至91-1E表示费率1-14的正向无功电能，91-1F表示正向无功电能数据块</term>
///    </item>
///    <item>
///      <term>91-20</term>
///      <term>ReadDouble</term>
///      <term>(当前)反向无功总电能(kvarh)</term>
///      <term>91-21至91-2E表示费率1-14的反向无功电能，91-2F表示正向无功电能数据块</term>
///    </item>
///    <item>
///      <term>A0-10</term>
///      <term>ReadDouble</term>
///      <term>(当前)正向有功总最大需量( kw)</term>
///      <term>A0-11至A0-1E表示费率1-14的正向有功最大需，A0-1F表示有功最大需量数据块</term>
///    </item>
///    <item>
///      <term>A0-20</term>
///      <term>ReadDouble</term>
///      <term>(当前)反向有功总最大需量( kw)</term>
///      <term>A0-21至A0-2E表示费率1-14的反向有功最大需，A0-2F表示反向有功最大需量数据块</term>
///    </item>
///    <item>
///      <term>A1-10</term>
///      <term>ReadDouble</term>
///      <term>(当前)正向无功总最大需量( kvar)</term>
///      <term>A1-11至A1-1E表示费率1-14的正向无功最大需，A1-1F表示无功最大需量数据块</term>
///    </item>
///    <item>
///      <term>A1-20</term>
///      <term>ReadDouble</term>
///      <term>(当前)反向无功总最大需量( kvar)</term>
///      <term>A1-21至A1-2E表示费率1-14的反向无功最大需，A1-2F表示反向无功最大需量数据块</term>
///    </item>
///    <item>
///      <term>B2-10</term>
///      <term>ReadString</term>
///      <term>最近一次编程时间</term>
///      <term>单位月日小时分钟，MMDDHHmm</term>
///    </item>
///    <item>
///      <term>B2-12</term>
///      <term>ReadDouble</term>
///      <term>编程次数</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B2-14</term>
///      <term>ReadDouble</term>
///      <term>电池工作时间(min)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B3-10</term>
///      <term>ReadDouble</term>
///      <term>总断相次数</term>
///      <term>B3-11至B3-13分别表示A相，B相，C相</term>
///    </item>
///    <item>
///      <term>B3-20</term>
///      <term>ReadDouble</term>
///      <term>断相时间累计值(min)</term>
///      <term>B3-21至B3-23分别表示A相，B相，C相</term>
///    </item>
///    <item>
///      <term>B6-11</term>
///      <term>ReadDouble</term>
///      <term>A相电压(V)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B6-12</term>
///      <term>ReadDouble</term>
///      <term>B相电压(V)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B6-13</term>
///      <term>ReadDouble</term>
///      <term>C相电压(V)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B6-21</term>
///      <term>ReadDouble</term>
///      <term>A相电流(A)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B6-22</term>
///      <term>ReadDouble</term>
///      <term>B相电流(A)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B6-23</term>
///      <term>ReadDouble</term>
///      <term>C相电流(A)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>B6-30</term>
///      <term>ReadDouble</term>
///      <term>瞬时有功功率(kw)</term>
///      <term>B6-31至B6-33分别表示A相，B相，C相</term>
///    </item>
///    <item>
///      <term>B6-40</term>
///      <term>ReadDouble</term>
///      <term>瞬时无功功率(kvarh)</term>
///      <term>B6-41至B6-43分别表示A相，B相，C相</term>
///    </item>
///    <item>
///      <term>B6-50</term>
///      <term>ReadDouble</term>
///      <term>总功率因数</term>
///      <term>B6-41至B6-43分别表示A相，B相，C相</term>
///    </item>
///    <item>
///      <term>C0-10</term>
///      <term>ReadString</term>
///      <term>日期及周次</term>
///      <term>年月日，YYMMDDWW</term>
///    </item>
///    <item>
///      <term>C0-11</term>
///      <term>ReadString</term>
///      <term>时间</term>
///      <term>时分秒，hhmmss</term>
///    </item>
///    <item>
///      <term>C0-30</term>
///      <term>ReadString</term>
///      <term>电表常数(有功)</term>
///      <term>p/(kwh)</term>
///    </item>
///    <item>
///      <term>C0-31</term>
///      <term>ReadString</term>
///      <term>电表常数(无功)</term>
///      <term>p/(kvarh)</term>
///    </item>
///    <item>
///      <term>C0-32</term>
///      <term>ReadString</term>
///      <term>表号</term>
///      <term></term>
///    </item>
///    <item>
///      <term>C0-33</term>
///      <term>ReadString</term>
///      <term>用户号</term>
///      <term></term>
///    </item>
///    <item>
///      <term>C0-34</term>
///      <term>ReadString</term>
///      <term>设备码</term>
///      <term></term>
///    </item>
///    <item>
///      <term>C1-12</term>
///      <term>ReadDouble</term>
///      <term>滑差时间(s)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>C1-13</term>
///      <term>ReadDouble</term>
///      <term>循显时间(s)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>C1-14</term>
///      <term>ReadDouble</term>
///      <term>停显时间(s)</term>
///      <term></term>
///    </item>
///    <item>
///      <term>C1-15</term>
///      <term>ReadDouble</term>
///      <term>显示电能小数位数</term>
///      <term></term>
///    </item>
///    <item>
///      <term>C1-17</term>
///      <term>ReadString</term>
///      <term>自动抄表日期</term>
///      <term>日时，DDhh</term>
///    </item>
///   </list>
/// </example>
public class DLT645With1997 : DeviceSerialPort, IDlt645, IReadWriteDevice, IReadWriteNet
{
  private string station = "1";

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.#ctor" />
  public DLT645With1997()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.ReceiveEmptyDataCount = 5;
  }

  /// <summary>通过指定的站号实例化一个设备对象</summary>
  /// <param name="station">设备的地址信息，是一个12字符的BCD码</param>
  public DLT645With1997(string station)
    : this()
  {
    this.station = station;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new DLT645Message(this.CheckDataId);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(byte[] send)
  {
    OperateResult<byte[]> operateResult = base.ReadFromCoreServer(send);
    if (!operateResult.IsSuccess)
      return operateResult;
    int headCode68H = DLT645Helper.FindHeadCode68H(operateResult.Content);
    return headCode68H > 0 ? OperateResult.CreateSuccessResult<byte[]>(operateResult.Content.RemoveBegin<byte>(headCode68H)) : operateResult;
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    if (!this.EnableCodeFE)
      return base.PackCommandWithHeader(command);
    return SoftBasic.SpliceArray<byte>(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, command);
  }

  /// <summary>
  /// 激活设备的命令，只发送数据到设备，不等待设备数据返回<br />
  /// The command to activate the device, only send data to the device, do not wait for the device data to return
  /// </summary>
  /// <returns>是否发送成功</returns>
  public OperateResult ActiveDeveice()
  {
    return (OperateResult) this.ReadFromCoreServer(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, false, false);
  }

  /// <summary>
  /// 根据指定的数据标识来读取相关的原始数据信息，地址标识根据手册来，从高位到地位，例如 B6-11，分割符可以任意特殊字符或是没有分隔符。<br />
  /// Read the relevant original data information according to the specified data identifier. The address identifier is based on the manual,
  /// from high to position, such as B6-11. The separator can be any special character or no separator.
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;B6-11" 或是 "s=100000;B6-11"，关于数据域信息，需要查找手册，例如:B6-30 表示： (当前)正向有功总电能
  /// </remarks>
  /// <param name="address">数据标识，具体需要查找手册来对应</param>
  /// <param name="length">数据长度信息</param>
  /// <returns>结果信息</returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return DLT645Helper.Read((IDlt645) this, address, length);
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;B6-11" 或是 "s=100000;B6-11"，关于数据域信息，需要查找手册，例如:B6-30 表示： 瞬时有功功率
  /// </remarks>
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return DLT645Helper.ReadDouble((IDlt645) this, address, length);
  }

  /// <inheritdoc />
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;B6-11" 或是 "s=100000;B6-11"，关于数据域信息，需要查找手册，例如:B6-30 表示： (当前)正向有功总电能
  /// </remarks>
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromArray<string>(this.ReadStringArray(address));
  }

  /// <summary>读取指定地址的所有的字符串数据信息，一般来说，一个地址只有一个数据</summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;B6-11" 或是 "s=100000;B6-11"，关于数据域信息，需要查找手册，例如:B6-30 表示： 瞬时有功功率
  /// </remarks>
  /// <param name="address">数据标识，具体需要查找手册来对应</param>
  /// <returns>字符串数组信息</returns>
  public OperateResult<string[]> ReadStringArray(string address)
  {
    return DLT645Helper.ReadStringArray((IDlt645) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ReadDouble(System.String,System.UInt16)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<double[]> operateResult = await Task.Run<OperateResult<double[]>>((Func<OperateResult<double[]>>) (() => this.ReadDouble(address, length)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.ReadString(System.String,System.UInt16,System.Text.Encoding)" />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<string> operateResult = await Task.Run<OperateResult<string>>((Func<OperateResult<string>>) (() => this.ReadString(address, length, encoding)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, values)));
    return operateResult;
  }

  /// <summary>
  /// 根据指定的数据标识来写入相关的原始数据信息，地址标识根据手册来，从高位到地位，例如 B6-34(正向有功功率上限值)，分割符可以任意特殊字符或是没有分隔符。<br />
  /// Read the relevant original data information according to the specified data identifier. The address identifier is based on the manual,
  /// from high to position, such as B6-34. The separator can be any special character or no separator.
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;B6-34" 或是 "s=100000;B6-34"，关于数据域信息，需要查找手册，例如:B6-30 表示： 瞬时有功功率<br />
  /// </remarks>
  /// <param name="address">地址信息</param>
  /// <param name="value">写入的数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return DLT645Helper.Write((IDlt645) this, "", "", address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    return DLT645Helper.Write((IDlt645) this, "", "", address, ((IEnumerable<double>) values).Select<double, string>((Func<double, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.WriteAddress(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public OperateResult WriteAddress(string address)
  {
    return DLT645Helper.WriteAddress((IDlt645) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.BroadcastTime(HslCommunication.Instrument.DLT.Helper.IDlt645,System.DateTime)" />
  public OperateResult BroadcastTime(DateTime dateTime)
  {
    return DLT645Helper.BroadcastTime((IDlt645) this, dateTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.ChangeBaudRate(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public OperateResult ChangeBaudRate(string baudRate)
  {
    return DLT645Helper.ChangeBaudRate((IDlt645) this, baudRate);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ReadAddress" />
  public OperateResult<string> ReadAddress()
  {
    return new OperateResult<string>(StringResources.Language.NotSupportedFunction);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.Trip(System.String,System.DateTime)" />
  public OperateResult Trip(DateTime validTime) => this.Trip(this.Station, validTime);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.Trip(System.String,System.DateTime)" />
  public OperateResult Trip(string station, DateTime validTime)
  {
    return DLT645Helper.Function1C((IDlt645) this, "", "", station, (byte) 26, validTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645With1997.SwitchingOn(System.String,System.DateTime)" />
  public OperateResult SwitchingOn(DateTime validTime) => this.SwitchingOn(this.Station, validTime);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.SwitchingOn(System.String,System.DateTime)" />
  public OperateResult SwitchingOn(string station, DateTime validTime)
  {
    return DLT645Helper.Function1C((IDlt645) this, "", "", station, (byte) 27, validTime);
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.Station" />
  public string Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.EnableCodeFE" />
  public bool EnableCodeFE { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.DLTType" />
  public DLT645Type DLTType { get; } = DLT645Type.DLT1997;

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.Password" />
  public string Password { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.OpCode" />
  public string OpCode { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.CheckDataId" />
  /// "/&gt;
  public bool CheckDataId { get; set; } = true;

  /// <inheritdoc />
  public override string ToString() => $"DLT645With1997[{this.PortName}:{this.BaudRate}]";
}
