﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.YAMAHA.YamahaRCX
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Robot.YAMAHA;

/// <summary>雅马哈机器人的数据访问类</summary>
public class YamahaRCX : TcpNetCommunication
{
  /// <summary>实例化一个默认的对象</summary>
  public YamahaRCX()
  {
    this.ReceiveTimeOut = 30000;
    this.LogMsgFormatBinary = false;
  }

  /// <summary>指定IP地址和端口来实例化一个对象</summary>
  /// <param name="ipAddress">IP地址</param>
  /// <param name="port">端口号</param>
  public YamahaRCX(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13, (byte) 10);
  }

  private int CalculateReceiveTimes(byte[] buffer)
  {
    int receiveTimes = 0;
    for (int index = 0; index < buffer.Length - 1; ++index)
    {
      if (buffer[index] == (byte) 13 && buffer[index + 1] == (byte) 10)
      {
        ++receiveTimes;
        ++index;
      }
    }
    return receiveTimes;
  }

  private string GetErrorText(string err)
  {
    return err.Substring(3) == "14.000" ? "通信中断错误 (Communicate disconnected)" : string.Empty;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData,
    bool usePackAndUnpack)
  {
    if (usePackAndUnpack)
      send = this.PackCommandWithHeader(send);
    this.LogSendMessage(send);
    OperateResult<byte[]> operateResult1 = pipe.ReadFromCoreServer(this.GetNewNetMessage(), send, false, new Action<byte[]>(((BinaryCommunication) this).LogRevcMessage));
    if (!operateResult1.IsSuccess)
      return operateResult1;
    int receiveTimes = this.CalculateReceiveTimes(send);
    MemoryStream ms = new MemoryStream();
    int num = 0;
    OperateResult<byte[]> operateResult2;
    string err;
    while (true)
    {
      do
      {
        operateResult2 = pipe.ReadFromCoreServer(this.GetNewNetMessage(), new byte[0], hasResponseData, new Action<byte[]>(((BinaryCommunication) this).LogRevcMessage));
        if (operateResult2.IsSuccess)
          err = Encoding.ASCII.GetString(operateResult2.Content);
        else
          goto label_5;
      }
      while (err.StartsWith("Welcome to ", StringComparison.OrdinalIgnoreCase) || err == "\r\n");
      ms.Write(operateResult2.Content);
      if (err.StartsWith("OK\r\n", StringComparison.OrdinalIgnoreCase))
      {
        ++num;
        if (num >= receiveTimes)
          goto label_18;
      }
      if (err.StartsWith("END\r\n", StringComparison.OrdinalIgnoreCase))
      {
        ++num;
        if (num >= receiveTimes)
          goto label_18;
      }
      if (err.StartsWith("NG=", StringComparison.OrdinalIgnoreCase))
      {
        ++num;
        if (num >= receiveTimes)
          goto label_13;
      }
    }
label_5:
    return operateResult2;
label_13:
    if (err.EndsWith("\r\n"))
      err = err.RemoveLast(2);
    return new OperateResult<byte[]>($"faild: {err}{this.GetErrorText(err)}");
label_18:
    if (!usePackAndUnpack)
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
    OperateResult<byte[]> operateResult3 = this.UnpackResponseContent(send, ms.ToArray());
    if (!operateResult3.IsSuccess && operateResult3.ErrorCode == int.MinValue)
      operateResult3.ErrorCode = 10000;
    return operateResult3;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    CommunicationPipe pipe,
    byte[] send,
    bool hasResponseData,
    bool usePackAndUnpack)
  {
    if (usePackAndUnpack)
      send = this.PackCommandWithHeader(send);
    this.LogSendMessage(send);
    OperateResult<byte[]> cmdSend = await pipe.ReadFromCoreServerAsync(this.GetNewNetMessage(), send, false, new Action<byte[]>(((BinaryCommunication) this).LogRevcMessage));
    if (!cmdSend.IsSuccess)
      return cmdSend;
    int count = this.CalculateReceiveTimes(send);
    MemoryStream ms = new MemoryStream();
    int received = 0;
    OperateResult<byte[]> cmdRecv;
    string str;
    while (true)
    {
      do
      {
        cmdRecv = await pipe.ReadFromCoreServerAsync(this.GetNewNetMessage(), new byte[0], hasResponseData, new Action<byte[]>(((BinaryCommunication) this).LogRevcMessage));
        if (cmdRecv.IsSuccess)
          str = Encoding.ASCII.GetString(cmdRecv.Content);
        else
          goto label_7;
      }
      while (str.StartsWith("Welcome to ", StringComparison.OrdinalIgnoreCase) || str == "\r\n");
      ms.Write(cmdRecv.Content);
      if (str.StartsWith("OK\r\n", StringComparison.OrdinalIgnoreCase))
      {
        ++received;
        if (received >= count)
          goto label_20;
      }
      if (str.StartsWith("END\r\n", StringComparison.OrdinalIgnoreCase))
      {
        ++received;
        if (received >= count)
          goto label_20;
      }
      if (str.StartsWith("NG=", StringComparison.OrdinalIgnoreCase))
      {
        ++received;
        if (received >= count)
          goto label_15;
      }
      cmdRecv = (OperateResult<byte[]>) null;
      str = (string) null;
    }
label_7:
    return cmdRecv;
label_15:
    if (str.EndsWith("\r\n"))
      str = str.RemoveLast(2);
    return new OperateResult<byte[]>($"faild: {str}{this.GetErrorText(str)}");
label_20:
    if (!usePackAndUnpack)
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
    OperateResult<byte[]> unpack = this.UnpackResponseContent(send, ms.ToArray());
    if (!unpack.IsSuccess && unpack.ErrorCode == int.MinValue)
      unpack.ErrorCode = 10000;
    return unpack;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadCommand(System.String)" />
  /// <remarks>
  /// 已经弃用，请使用 <see cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadCommandAsync(System.String)" /> 方法替换
  /// </remarks>
  [Obsolete]
  public async Task<OperateResult<string[]>> ReadCommandAsync(string command, int lines)
  {
    OperateResult<string[]> operateResult = await this.ReadCommandAsync(command);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadCommand(System.String)" />
  public async Task<OperateResult<string[]>> ReadCommandAsync(string command)
  {
    if (command == null)
      throw new ArgumentNullException(nameof (command));
    if (!command.EndsWith("\r\n"))
      command += "\r\n";
    byte[] buffer = Encoding.ASCII.GetBytes(command);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(buffer);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) read);
    return OperateResult.CreateSuccessResult<string[]>(Encoding.ASCII.GetString(read.Content).Split(new char[2]
    {
      '\r',
      '\n'
    }, StringSplitOptions.RemoveEmptyEntries));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.Reset" />
  public async Task<OperateResult> ResetAsync()
  {
    OperateResult<string[]> operateResult = await this.ReadCommandAsync("@ RESET ");
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.Run" />
  public async Task<OperateResult> RunAsync()
  {
    OperateResult<string[]> operateResult = await this.ReadCommandAsync("@ RUN ");
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.Stop" />
  public async Task<OperateResult> StopAsync()
  {
    OperateResult<string[]> operateResult = await this.ReadCommandAsync("@ STOP ");
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadMotorStatus" />
  public async Task<OperateResult<int>> ReadMotorStatusAsync()
  {
    OperateResult<string[]> read = await this.ReadCommandAsync("@?MOTOR ");
    return this.GetTValueHelper<int>(read, new Func<string, int>(Convert.ToInt32));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadModeStatus" />
  public async Task<OperateResult<int>> ReadModeStatusAsync()
  {
    OperateResult<string[]> read = await this.ReadCommandAsync("@?MODE ");
    return this.GetTValueHelper<int>(read, new Func<string, int>(Convert.ToInt32));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadJoints" />
  public async Task<OperateResult<float[]>> ReadJointsAsync()
  {
    OperateResult<string[]> read = await this.ReadCommandAsync("@?WHERE ");
    return this.GetTValueHelper<float[]>(read, new Func<string, float[]>(this.ConvertToSingleHelper));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadEmergencyStatus" />
  public async Task<OperateResult<int>> ReadEmergencyStatusAsync()
  {
    OperateResult<string[]> read = await this.ReadCommandAsync("@?EMG ");
    return this.GetTValueHelper<int>(read, new Func<string, int>(Convert.ToInt32));
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadDI(System.Int32)" />
  public async Task<OperateResult<bool[]>> ReadDIAsync(int index)
  {
    OperateResult<string[]> read = await this.ReadCommandAsync($"@?DI{index}()");
    return this.GetBoolArray(read);
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadDO(System.Int32)" />
  public async Task<OperateResult<bool[]>> ReadDOAsync(int index)
  {
    OperateResult<string[]> read = await this.ReadCommandAsync($"@?DO{index}()");
    return this.GetBoolArray(read);
  }

  /// <inheritdoc cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadCommand(System.String)" />
  /// <remarks>
  /// 已经弃用，请使用 <see cref="M:HslCommunication.Robot.YAMAHA.YamahaRCX.ReadCommand(System.String)" /> 方法替换
  /// </remarks>
  [Obsolete]
  public OperateResult<string[]> ReadCommand(string command, int lines)
  {
    return this.ReadCommand(command);
  }

  /// <summary>
  /// 读取指定的命令的方法，需要指定命令<br />
  /// The method of reading the specified command requires the specified command and the line number information of the received command
  /// </summary>
  /// <param name="command">命令</param>
  /// <returns>接收的命令</returns>
  [HslMqttApi(Description = "The method of reading the specified command requires the specified command and the line number information of the received command")]
  public OperateResult<string[]> ReadCommand(string command)
  {
    if (command == null)
      throw new ArgumentNullException(nameof (command));
    if (!command.EndsWith("\r\n"))
      command += "\r\n";
    OperateResult<byte[]> result = this.ReadFromCoreServer(Encoding.ASCII.GetBytes(command));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result);
    return OperateResult.CreateSuccessResult<string[]>(Encoding.ASCII.GetString(result.Content).Split(new char[2]
    {
      '\r',
      '\n'
    }, StringSplitOptions.RemoveEmptyEntries));
  }

  /// <summary>
  /// 指定程序复位信息，对所有的程序进行复位。当重新启动了程序时，从主程序或者任务 1 中最后执行的程序开头开始执行。<br />
  /// Specify the program reset information to reset all programs. When the program is restarted,
  /// execution starts from the beginning of the main program or the last executed program in task 1.
  /// </summary>
  /// <returns>执行结果是否成功</returns>
  [HslMqttApi(Description = "Specify the program reset information to reset all programs. When the program is restarted")]
  public OperateResult Reset() => (OperateResult) this.ReadCommand("@ RESET ");

  /// <summary>
  /// 执行程序运行。执行所有的 RUN 状态程序。<br />
  /// Execute the program to run. Execute all RUN state programs.
  /// </summary>
  /// <returns>执行结果是否成功</returns>
  [HslMqttApi(Description = "Execute the program to run. Execute all RUN state programs.")]
  public OperateResult Run() => (OperateResult) this.ReadCommand("@ RUN ");

  /// <summary>
  /// 按照优先顺序 p 将指定的程序登录到任务 n 中。已登录程序变为 STOP 状态。<br />
  /// Logs the specified program into task n in order of p. The logged-in program changes to the STOP state.
  /// </summary>
  /// <param name="program">程序名称</param>
  /// <param name="taskId">任务编号</param>
  /// <returns>是否加载成功</returns>
  public OperateResult Load(string program, int taskId)
  {
    return (OperateResult) this.ReadCommand($"＠ LOAD <{program}>, T{taskId}");
  }

  /// <summary>
  /// 执行程序停止。执行所有的 STOP 状态程序。<br />
  /// The execution program stops. Execute all STOP state programs.
  /// </summary>
  /// <returns>执行结果是否成功</returns>
  [HslMqttApi(Description = "The execution program stops. Execute all STOP state programs.")]
  public OperateResult Stop() => (OperateResult) this.ReadCommand("@ STOP ");

  private string GetJogXYCommand(int axis, int robot = 1, bool tail = false)
  {
    StringBuilder stringBuilder = new StringBuilder("@ JOGXY ");
    if (robot != 1)
      stringBuilder.Append($"[{robot}] ");
    stringBuilder.Append(Math.Abs(axis).ToString());
    stringBuilder.Append(axis > 0 ? "+" : "-");
    if (tail)
      stringBuilder.Append("\r\n");
    return stringBuilder.ToString();
  }

  /// <summary>只是发送机器人的指定轴进行手动移动（点动）的命令</summary>
  /// <param name="robot">机器人编号</param>
  /// <param name="axis">轴ID信息</param>
  /// <returns>是否操作成功</returns>
  public OperateResult SendJogXY(int axis, int robot = 1)
  {
    return this.CommunicationPipe.Send(Encoding.ASCII.GetBytes(this.GetJogXYCommand(axis, robot, true)));
  }

  /// <summary>
  /// 对＜机器人编号＞指定机器人的指定轴进行手动移动（点动）。＜机器人编号＞可以省略。当进行省略时，机器人 1 被指定。
  /// </summary>
  /// <param name="robot">机器人编号</param>
  /// <param name="axis">轴ID信息</param>
  /// <returns>是否操作成功</returns>
  public OperateResult JogXY(int axis, int robot = 1)
  {
    OperateResult<string[]> result = this.ReadCommand(this.GetJogXYCommand(axis, robot));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<int>((OperateResult) result) : (OperateResult) result;
  }

  /// <summary>
  /// 获取马达电源状态，返回的0:马达电源关闭; 1:马达电源开启; 2:马达电源开启＋所有机器人伺服开启<br />
  /// Get the motor power status, return 0: motor power off; 1: motor power on; 2: motor power on + all robot servos on
  /// </summary>
  /// <returns>返回的0:马达电源关闭; 1:马达电源开启; 2:马达电源开启＋所有机器人伺服开启</returns>
  [HslMqttApi(Description = "Get the motor power status, return 0: motor power off; 1: motor power on; 2: motor power on + all robot servos on")]
  public OperateResult<int> ReadMotorStatus()
  {
    return this.GetTValueHelper<int>(this.ReadCommand("@?MOTOR "), new Func<string, int>(Convert.ToInt32));
  }

  /// <summary>
  /// 读取模式状态<br />
  /// Read mode status
  /// </summary>
  /// <returns>模式的状态信息</returns>
  [HslMqttApi(Description = "Read mode status")]
  public OperateResult<int> ReadModeStatus()
  {
    return this.GetTValueHelper<int>(this.ReadCommand("@?MODE "), new Func<string, int>(Convert.ToInt32));
  }

  /// <summary>
  /// 读取关节的基本数据信息<br />
  /// Read the basic data information of the joint
  /// </summary>
  /// <returns>关节信息</returns>
  [HslMqttApi(Description = "Read the basic data information of the joint")]
  public OperateResult<float[]> ReadJoints()
  {
    return this.GetTValueHelper<float[]>(this.ReadCommand("@?WHERE "), new Func<string, float[]>(this.ConvertToSingleHelper));
  }

  /// <summary>
  /// 读取紧急停止状态，0 ：正常状态、1 ：紧急停止状态<br />
  /// Read emergency stop state, 0: normal state, 1: emergency stop state
  /// </summary>
  /// <returns>0 ：正常状态、1 ：紧急停止状态</returns>
  [HslMqttApi(Description = "Read emergency stop state, 0: normal state, 1: emergency stop state")]
  public OperateResult<int> ReadEmergencyStatus()
  {
    return this.GetTValueHelper<int>(this.ReadCommand("@?EMG "), new Func<string, int>(Convert.ToInt32));
  }

  /// <summary>
  /// 读取输入的点位信息，返回bool数组<br />
  /// Read the input point information and return a bool array
  /// </summary>
  /// <param name="index">哪个位置的数据</param>
  /// <returns>点位的通断信息</returns>
  [HslMqttApi(Description = "Read the input point information and return a bool array")]
  public OperateResult<bool[]> ReadDI(int index)
  {
    return this.GetBoolArray(this.ReadCommand($"@?DI{index}()"));
  }

  /// <summary>
  /// 读取输出的点位信息，返回bool数组<br />
  /// Read the output point information and return a bool array
  /// </summary>
  /// <param name="index">哪个位置的数据</param>
  /// <returns>点位的通断信息</returns>
  [HslMqttApi(Description = "Read the output point information and return a bool array")]
  public OperateResult<bool[]> ReadDO(int index)
  {
    return this.GetBoolArray(this.ReadCommand($"@?DO{index}()"));
  }

  private OperateResult<bool[]> GetBoolArray(OperateResult<string[]> read)
  {
    return this.GetTValueHelper<bool[]>(read, (Func<string, bool[]>) (m => new byte[1]
    {
      (byte) Convert.ToInt32(m)
    }.ToBoolArray()));
  }

  private OperateResult<T> GetTValueHelper<T>(OperateResult<string[]> read, Func<string, T> func)
  {
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) read);
    string empty = string.Empty;
    try
    {
      empty = read.Content[0];
      return OperateResult.CreateSuccessResult<T>(func(empty));
    }
    catch (Exception ex)
    {
      return new OperateResult<T>($"{ex.Message} Source: {empty}");
    }
  }

  private float[] ConvertToSingleHelper(string content)
  {
    return ((IEnumerable<string>) content.Split(new char[1]
    {
      ' '
    }, StringSplitOptions.RemoveEmptyEntries)).Select<string, float>((Func<string, float>) (m => Convert.ToSingle(m))).ToArray<float>();
  }

  /// <inheritdoc />
  public override string ToString() => $"YamahaRCX[{this.IpAddress}:{this.Port}]";
}
