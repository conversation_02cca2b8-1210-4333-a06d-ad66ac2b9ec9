﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.DLT645Message
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Instrument.DLT.Helper;
using System.IO;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>DLT 645协议的串口透传的消息类</summary>
public class DLT645Message : NetMessageBase, INetMessage
{
  private bool checkDataId = true;

  /// <summary>实例化一个默认的对象</summary>
  public DLT645Message()
  {
  }

  /// <summary>
  /// 根据是否需要检查数据标识，实例化一个对象<br />
  /// Instantiate an object based on whether it is necessary to check the data identifier
  /// </summary>
  /// <param name="checkDataId"></param>
  public DLT645Message(bool checkDataId) => this.checkDataId = checkDataId;

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 10;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => (int) this.HeadBytes[9] + 2;

  /// <inheritdoc />
  public override int PependedUselesByteLength(byte[] headByte)
  {
    int headCode68H = DLT645Helper.FindHeadCode68H(headByte);
    return headCode68H < 0 ? 10 : headCode68H;
  }

  /// <inheritdoc />
  /// s
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    if (!this.checkDataId)
      return base.CheckMessageMatch(send, receive);
    if (send == null || receive == null || send.Length < 16 /*0x10*/ || receive.Length < 16 /*0x10*/)
      return base.CheckMessageMatch(send, receive);
    int headCode68H1 = DLT645Helper.FindHeadCode68H(send);
    int headCode68H2 = DLT645Helper.FindHeadCode68H(receive);
    if (headCode68H1 < 0 || headCode68H2 < 0 || headCode68H1 > 4 || headCode68H2 > 4)
      return base.CheckMessageMatch(send, receive);
    if (send[headCode68H1 + 8] == (byte) 17 && receive[headCode68H2 + 8] == (byte) 145)
    {
      for (int index = 0; index < 4 && headCode68H1 + 10 + index < send.Length && headCode68H2 + 10 + index < receive.Length; ++index)
      {
        if ((int) send[headCode68H1 + 10 + index] != (int) receive[headCode68H2 + 10 + index])
          return -1;
      }
      return 1;
    }
    if (send[headCode68H1 + 8] != (byte) 1 || receive[headCode68H2 + 8] != (byte) 129)
      return base.CheckMessageMatch(send, receive);
    for (int index = 0; index < 2 && headCode68H1 + 10 + index < send.Length && headCode68H2 + 10 + index < receive.Length; ++index)
    {
      if ((int) send[headCode68H1 + 10 + index] != (int) receive[headCode68H2 + 10 + index])
        return -1;
    }
    return 1;
  }

  /// <inheritdoc />
  public override bool CheckReceiveDataComplete(byte[] send, MemoryStream ms)
  {
    return DLT645Helper.CheckReceiveDataComplete(ms);
  }
}
