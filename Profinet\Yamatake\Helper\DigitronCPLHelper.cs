﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Yamatake.Helper.DigitronCPLHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Yamatake.Helper;

/// <summary>辅助类方法</summary>
public class DigitronCPLHelper
{
  /// <summary>构建写入操作的报文信息</summary>
  /// <param name="station">站号</param>
  /// <param name="address">地址</param>
  /// <param name="length">长度的长度</param>
  /// <returns>报文内容</returns>
  public static OperateResult<byte[]> BuildReadCommand(byte station, string address, ushort length)
  {
    try
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append('\u0002');
      stringBuilder.Append(station.ToString("X2"));
      stringBuilder.Append("00XRS,");
      stringBuilder.Append(ushort.Parse(address).ToString());
      stringBuilder.Append("W,");
      stringBuilder.Append(length.ToString());
      stringBuilder.Append('\u0003');
      int num1 = 0;
      for (int index = 0; index < stringBuilder.Length; ++index)
        num1 += (int) stringBuilder[index];
      byte num2 = (byte) (256 /*0x0100*/ - num1 % 256 /*0x0100*/);
      stringBuilder.Append(num2.ToString("X2"));
      stringBuilder.Append("\r\n");
      return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address wrong: " + ex.Message);
    }
  }

  /// <summary>构建写入操作的命令报文</summary>
  /// <param name="station">站号信息</param>
  /// <param name="address">数据的地址</param>
  /// <param name="value">等待写入的值</param>
  /// <returns>写入的报文命令</returns>
  public static OperateResult<byte[]> BuildWriteCommand(byte station, string address, byte[] value)
  {
    try
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append('\u0002');
      stringBuilder.Append(station.ToString("X2"));
      stringBuilder.Append("00XWS,");
      stringBuilder.Append(ushort.Parse(address).ToString());
      stringBuilder.Append("W");
      for (int index = 0; index < value.Length / 2; ++index)
      {
        short int16 = BitConverter.ToInt16(value, index * 2);
        stringBuilder.Append(",");
        stringBuilder.Append(int16.ToString());
      }
      stringBuilder.Append('\u0003');
      int num1 = 0;
      for (int index = 0; index < stringBuilder.Length; ++index)
        num1 += (int) stringBuilder[index];
      byte num2 = (byte) (256 /*0x0100*/ - num1 % 256 /*0x0100*/);
      stringBuilder.Append(num2.ToString("X2"));
      stringBuilder.Append("\r\n");
      return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>("Address wrong: " + ex.Message);
    }
  }

  /// <summary>用于服务器反馈的数据的报文打包操作</summary>
  /// <param name="station">站号</param>
  /// <param name="err">错误码，如果为0则表示正常</param>
  /// <param name="value">原始数据值信息</param>
  /// <param name="dataType">数据类型</param>
  /// <returns>打包的报文数据信息</returns>
  public static byte[] PackResponseContent(byte station, int err, byte[] value, byte dataType)
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append('\u0002');
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("00X");
    stringBuilder.Append(err.ToString("D2"));
    if (err == 0 && value != null)
    {
      for (int index = 0; index < value.Length / 2; ++index)
      {
        if (dataType == (byte) 87)
        {
          short int16 = BitConverter.ToInt16(value, index * 2);
          stringBuilder.Append(",");
          stringBuilder.Append(int16.ToString());
        }
        else
        {
          ushort uint16 = BitConverter.ToUInt16(value, index * 2);
          stringBuilder.Append(",");
          stringBuilder.Append(uint16.ToString());
        }
      }
    }
    stringBuilder.Append('\u0003');
    int num1 = 0;
    for (int index = 0; index < stringBuilder.Length; ++index)
      num1 += (int) stringBuilder[index];
    byte num2 = (byte) (256 /*0x0100*/ - num1 % 256 /*0x0100*/);
    stringBuilder.Append(num2.ToString("X2"));
    stringBuilder.Append("\r\n");
    return Encoding.ASCII.GetBytes(stringBuilder.ToString());
  }

  /// <summary>根据错误码获取到相关的错误代号信息</summary>
  /// <param name="err">错误码</param>
  /// <returns>错误码对应的文本描述信息</returns>
  public static string GetErrorText(int err)
  {
    switch (err)
    {
      case 40:
        return StringResources.Language.YamatakeDigitronCPL40;
      case 41:
        return StringResources.Language.YamatakeDigitronCPL41;
      case 42:
        return StringResources.Language.YamatakeDigitronCPL42;
      case 43:
        return StringResources.Language.YamatakeDigitronCPL43;
      case 44:
        return StringResources.Language.YamatakeDigitronCPL44;
      case 45:
        return StringResources.Language.YamatakeDigitronCPL45;
      case 46:
        return StringResources.Language.YamatakeDigitronCPL46;
      case 47:
        return StringResources.Language.YamatakeDigitronCPL47;
      case 48 /*0x30*/:
        return StringResources.Language.YamatakeDigitronCPL48;
      case 99:
        return StringResources.Language.YamatakeDigitronCPL99;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>从反馈的数据内容中解析出真实的数据信息</summary>
  /// <param name="response">仪表反馈的真实的数据信息</param>
  /// <returns>解析之后的实际数据信息</returns>
  public static OperateResult<byte[]> ExtraActualResponse(byte[] response)
  {
    try
    {
      int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, 6, 2));
      if (int32 > 0)
        return new OperateResult<byte[]>(int32, DigitronCPLHelper.GetErrorText(int32));
      int num = 8;
      for (int index = 8; index < response.Length; ++index)
      {
        if (response[index] == (byte) 3)
        {
          num = index;
          break;
        }
      }
      int index1 = response[8] == (byte) 44 ? 9 : 8;
      if (num - index1 <= 0)
        return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      short[] array = ((IEnumerable<string>) Encoding.ASCII.GetString(response, index1, num - index1).Split(new char[1]
      {
        ','
      }, StringSplitOptions.RemoveEmptyEntries)).Select<string, short>((Func<string, short>) (m => short.Parse(m))).ToArray<short>();
      byte[] numArray = new byte[array.Length * 2];
      for (int index2 = 0; index2 < array.Length; ++index2)
        BitConverter.GetBytes(array[index2]).CopyTo((Array) numArray, index2 * 2);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"Data wrong: {ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
    }
  }
}
