﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.CutterInfo
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>刀具信息</summary>
public class CutterInfo
{
  /// <summary>长度形状补偿</summary>
  public double LengthSharpOffset { get; set; }

  /// <summary>长度磨损补偿</summary>
  public double LengthWearOffset { get; set; }

  /// <summary>半径形状补偿</summary>
  public double RadiusSharpOffset { get; set; }

  /// <summary>半径磨损补偿</summary>
  public double RadiusWearOffset { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"LengthSharpOffset:{this.LengthSharpOffset:10} LengthWearOffset:{this.LengthWearOffset:10} RadiusSharpOffset:{this.RadiusSharpOffset:10} RadiusWearOffset:{this.RadiusWearOffset:10}";
  }
}
