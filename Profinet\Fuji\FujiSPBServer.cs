﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Fuji.FujiSPBServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.ModBus;
using HslCommunication.Reflection;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Fuji;

/// <summary>
/// 富士的SPB虚拟的PLC，线圈支持X,Y,M的读写，其中X只能远程读，寄存器支持D,R,W的读写操作。<br />
/// Fuji's SPB virtual PLC, the coil supports X, Y, M read and write,
/// X can only be read remotely, and the register supports D, R, W read and write operations.
/// </summary>
public class FujiSPBServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer rBuffer;
  private SoftBuffer wBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private int station = 1;

  /// <summary>实例化一个富士SPB的网口和串口服务器，支持数据读写操作</summary>
  public FujiSPBServer()
  {
    this.xBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.yBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.wBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.DataFormat" />
  public DataFormat DataFormat
  {
    get => this.ByteTransform.DataFormat;
    set => this.ByteTransform.DataFormat = value;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.IsStringReverse" />
  public bool IsStringReverse
  {
    get => this.ByteTransform.IsStringReverseByteWord;
    set => this.ByteTransform.IsStringReverseByteWord = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.Station" />
  public int Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] bytes = new byte[589824 /*0x090000*/];
    this.xBuffer.GetBytes().CopyTo((Array) bytes, 0);
    this.yBuffer.GetBytes().CopyTo((Array) bytes, 65536 /*0x010000*/);
    this.mBuffer.GetBytes().CopyTo((Array) bytes, 131072 /*0x020000*/);
    this.dBuffer.GetBytes().CopyTo((Array) bytes, 196608 /*0x030000*/);
    this.rBuffer.GetBytes().CopyTo((Array) bytes, 327680 /*0x050000*/);
    this.wBuffer.GetBytes().CopyTo((Array) bytes, 458752 /*0x070000*/);
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 589824 /*0x090000*/)
      throw new Exception("File is not correct");
    this.xBuffer.SetBytes(content, 0, 65536 /*0x010000*/);
    this.yBuffer.SetBytes(content, 65536 /*0x010000*/, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 131072 /*0x020000*/, 65536 /*0x010000*/);
    this.dBuffer.SetBytes(content, 196608 /*0x030000*/, 131072 /*0x020000*/);
    this.rBuffer.SetBytes(content, 327680 /*0x050000*/, 131072 /*0x020000*/);
    this.wBuffer.SetBytes(content, 458752 /*0x070000*/, 131072 /*0x020000*/);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = new OperateResult<byte[]>();
    try
    {
      switch (address[0])
      {
        case 'D':
        case 'd':
          return OperateResult.CreateSuccessResult<byte[]>(this.dBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
        case 'M':
        case 'm':
          return OperateResult.CreateSuccessResult<byte[]>(this.mBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
        case 'R':
        case 'r':
          return OperateResult.CreateSuccessResult<byte[]>(this.rBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
        case 'W':
        case 'w':
          return OperateResult.CreateSuccessResult<byte[]>(this.wBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
        case 'X':
        case 'x':
          return OperateResult.CreateSuccessResult<byte[]>(this.xBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
        case 'Y':
        case 'y':
          return OperateResult.CreateSuccessResult<byte[]>(this.yBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      operateResult.Message = ex.Message;
      return operateResult;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = new OperateResult<byte[]>();
    try
    {
      switch (address[0])
      {
        case 'D':
        case 'd':
          this.dBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
          return OperateResult.CreateSuccessResult();
        case 'M':
        case 'm':
          this.mBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
          return OperateResult.CreateSuccessResult();
        case 'R':
        case 'r':
          this.rBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
          return OperateResult.CreateSuccessResult();
        case 'W':
        case 'w':
          this.wBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
          return OperateResult.CreateSuccessResult();
        case 'X':
        case 'x':
          this.xBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
          return OperateResult.CreateSuccessResult();
        case 'Y':
        case 'y':
          this.yBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
          return OperateResult.CreateSuccessResult();
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      operateResult.Message = ex.Message;
      return (OperateResult) operateResult;
    }
  }

  private int GetBitIndex(string address)
  {
    int bitIndex;
    if (address.LastIndexOf('.') > 0)
    {
      int indexInformation = HslHelper.GetBitIndexInformation(ref address);
      bitIndex = Convert.ToInt32(address.Substring(1)) * 16 /*0x10*/ + indexInformation;
    }
    else
      bitIndex = address[0] != 'X' && address[0] != 'x' && address[0] != 'Y' && address[0] != 'y' && address[0] != 'M' && address[0] != 'm' ? Convert.ToInt32(address.Substring(1)) * 16 /*0x10*/ : Convert.ToInt32(address.Substring(1));
    return bitIndex;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBOverTcp.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    try
    {
      int bitIndex = this.GetBitIndex(address);
      switch (address[0])
      {
        case 'D':
        case 'd':
          return OperateResult.CreateSuccessResult<bool[]>(this.dBuffer.GetBool(bitIndex, (int) length));
        case 'M':
        case 'm':
          return OperateResult.CreateSuccessResult<bool[]>(this.mBuffer.GetBool(bitIndex, (int) length));
        case 'R':
        case 'r':
          return OperateResult.CreateSuccessResult<bool[]>(this.rBuffer.GetBool(bitIndex, (int) length));
        case 'W':
        case 'w':
          return OperateResult.CreateSuccessResult<bool[]>(this.wBuffer.GetBool(bitIndex, (int) length));
        case 'X':
        case 'x':
          return OperateResult.CreateSuccessResult<bool[]>(this.xBuffer.GetBool(bitIndex, (int) length));
        case 'Y':
        case 'y':
          return OperateResult.CreateSuccessResult<bool[]>(this.yBuffer.GetBool(bitIndex, (int) length));
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceCommunication.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    try
    {
      int bitIndex = this.GetBitIndex(address);
      switch (address[0])
      {
        case 'D':
        case 'd':
          this.dBuffer.SetBool(value, bitIndex);
          return OperateResult.CreateSuccessResult();
        case 'M':
        case 'm':
          this.mBuffer.SetBool(value, bitIndex);
          return OperateResult.CreateSuccessResult();
        case 'R':
        case 'r':
          this.rBuffer.SetBool(value, bitIndex);
          return OperateResult.CreateSuccessResult();
        case 'W':
        case 'w':
          this.wBuffer.SetBool(value, bitIndex);
          return OperateResult.CreateSuccessResult();
        case 'X':
        case 'x':
          this.xBuffer.SetBool(value, bitIndex);
          return OperateResult.CreateSuccessResult();
        case 'Y':
        case 'y':
          this.yBuffer.SetBool(value, bitIndex);
          return OperateResult.CreateSuccessResult();
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<bool[]>(ex.Message);
    }
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new FujiSPBMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length < 5)
      return new OperateResult<byte[]>("Uknown message: " + receive.ToHexString(' '));
    if (receive[0] != (byte) 58)
      return new OperateResult<byte[]>("Message must start with 0x3A: " + receive.ToHexString(' '));
    return Encoding.ASCII.GetString(receive, 1, 2) != this.station.ToString("X2") ? new OperateResult<byte[]>($"Station not match , Except: {this.station:X2} , Actual: {Encoding.ASCII.GetString(receive, 1, 2)}") : OperateResult.CreateSuccessResult<byte[]>(this.ReadFromSPBCore(receive));
  }

  private byte[] CreateResponseBack(byte err, string command, byte[] data, bool addLength = true)
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(':');
    stringBuilder.Append(this.Station.ToString("X2"));
    stringBuilder.Append("00");
    stringBuilder.Append(command.Substring(9, 4));
    stringBuilder.Append(err.ToString("X2"));
    if (err == (byte) 0 && data != null)
    {
      if (addLength)
        stringBuilder.Append(FujiSPBHelper.AnalysisIntegerAddress(data.Length / 2));
      stringBuilder.Append(data.ToHexString());
    }
    stringBuilder[3] = ((stringBuilder.Length - 5) / 2).ToString("X2")[0];
    stringBuilder[4] = ((stringBuilder.Length - 5) / 2).ToString("X2")[1];
    stringBuilder.Append("\r\n");
    return Encoding.ASCII.GetBytes(stringBuilder.ToString());
  }

  private int AnalysisAddress(string address)
  {
    return Convert.ToInt32(address.Substring(2) + address.Substring(0, 2));
  }

  private byte[] ReadFromSPBCore(byte[] receive)
  {
    if (receive.Length < 15)
      return (byte[]) null;
    if (receive[receive.Length - 2] == (byte) 13 && receive[receive.Length - 1] == (byte) 10)
      receive = receive.RemoveLast<byte>(2);
    string command = Encoding.ASCII.GetString(receive);
    if (Convert.ToInt32(command.Substring(3, 2), 16 /*0x10*/) != (command.Length - 5) / 2)
      return this.CreateResponseBack((byte) 3, command, (byte[]) null);
    if (command.Substring(9, 4) == "0000")
      return this.ReadByCommand(command);
    if (command.Substring(9, 4) == "0100")
      return this.WriteByCommand(command);
    return command.Substring(9, 4) == "0102" ? this.WriteBitByCommand(command) : (byte[]) null;
  }

  private byte[] ReadByCommand(string command)
  {
    string str = command.Substring(13, 2);
    int num1 = this.AnalysisAddress(command.Substring(15, 4));
    int num2 = this.AnalysisAddress(command.Substring(19, 4));
    if (num2 > 105)
      this.CreateResponseBack((byte) 3, command, (byte[]) null);
    switch (str)
    {
      case "0C":
        return this.CreateResponseBack((byte) 0, command, this.dBuffer.GetBytes(num1 * 2, num2 * 2));
      case "0D":
        return this.CreateResponseBack((byte) 0, command, this.rBuffer.GetBytes(num1 * 2, num2 * 2));
      case "0E":
        return this.CreateResponseBack((byte) 0, command, this.wBuffer.GetBytes(num1 * 2, num2 * 2));
      case "01":
        return this.CreateResponseBack((byte) 0, command, this.xBuffer.GetBytes(num1 * 2, num2 * 2));
      case "00":
        return this.CreateResponseBack((byte) 0, command, this.yBuffer.GetBytes(num1 * 2, num2 * 2));
      case "02":
        return this.CreateResponseBack((byte) 0, command, this.mBuffer.GetBytes(num1 * 2, num2 * 2));
      default:
        return this.CreateResponseBack((byte) 2, command, (byte[]) null);
    }
  }

  private byte[] WriteByCommand(string command)
  {
    if (!this.EnableWrite)
      return this.CreateResponseBack((byte) 2, command, (byte[]) null);
    string str = command.Substring(13, 2);
    int num = this.AnalysisAddress(command.Substring(15, 4));
    if (this.AnalysisAddress(command.Substring(19, 4)) * 4 != command.Length - 23)
      return this.CreateResponseBack((byte) 3, command, (byte[]) null);
    byte[] hexBytes = command.Substring(23).ToHexBytes();
    switch (str)
    {
      case "0C":
        this.dBuffer.SetBytes(hexBytes, num * 2);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "0D":
        this.rBuffer.SetBytes(hexBytes, num * 2);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "0E":
        this.wBuffer.SetBytes(hexBytes, num * 2);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "00":
        this.yBuffer.SetBytes(hexBytes, num * 2);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "02":
        this.mBuffer.SetBytes(hexBytes, num * 2);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      default:
        return this.CreateResponseBack((byte) 2, command, (byte[]) null);
    }
  }

  private byte[] WriteBitByCommand(string command)
  {
    if (!this.EnableWrite)
      return this.CreateResponseBack((byte) 2, command, (byte[]) null);
    string str = command.Substring(13, 2);
    int num = this.AnalysisAddress(command.Substring(15, 4));
    int int32 = Convert.ToInt32(command.Substring(19, 2));
    bool flag = command.Substring(21, 2) != "00";
    switch (str)
    {
      case "0C":
        this.dBuffer.SetBool(flag, num * 8 + int32);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "0D":
        this.rBuffer.SetBool(flag, num * 8 + int32);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "0E":
        this.wBuffer.SetBool(flag, num * 8 + int32);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "00":
        this.yBuffer.SetBool(flag, num * 8 + int32);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      case "02":
        this.mBuffer.SetBool(flag, num * 8 + int32);
        return this.CreateResponseBack((byte) 0, command, (byte[]) null);
      default:
        return this.CreateResponseBack((byte) 2, command, (byte[]) null);
    }
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return ModbusInfo.CheckAsciiReceiveDataComplete(buffer, receivedLength);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.xBuffer.Dispose();
      this.yBuffer.Dispose();
      this.mBuffer.Dispose();
      this.dBuffer.Dispose();
      this.rBuffer.Dispose();
      this.wBuffer.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"FujiSPBServer[{this.Port}]";
}
