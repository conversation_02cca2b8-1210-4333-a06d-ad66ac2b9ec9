﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Helper.Secs1
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Secs.Helper;

/// <summary>Secs-1的协议信息</summary>
public class Secs1
{
  /// <summary>根据传入的参数信息，构建完整的SECS消息报文列表</summary>
  /// <param name="deviceID">装置识别码</param>
  /// <param name="streamNo">主功能码</param>
  /// <param name="functionNo">子功能码</param>
  /// <param name="blockNo">数据块号</param>
  /// <param name="messageID">消息序号</param>
  /// <param name="data">真实数据消息</param>
  /// <param name="wBit">是否必须回复讯息</param>
  /// <returns>完整的报文信息</returns>
  public static List<byte[]> BuildSecsOneMessage(
    ushort deviceID,
    byte streamNo,
    byte functionNo,
    ushort blockNo,
    uint messageID,
    byte[] data,
    bool wBit)
  {
    List<byte[]> numArrayList1 = new List<byte[]>();
    List<byte[]> numArrayList2 = data.Length <= 244 ? SoftBasic.ArraySplitByLength<byte>(data, 244) : SoftBasic.ArraySplitByLength<byte>(data, 224 /*0xE0*/);
    for (int index = 0; index < numArrayList2.Count; ++index)
    {
      byte[] buffer = new byte[13 + numArrayList2[index].Length];
      buffer[0] = (byte) (10 + numArrayList2[index].Length);
      buffer[1] = BitConverter.GetBytes(deviceID)[1];
      buffer[2] = BitConverter.GetBytes(deviceID)[0];
      buffer[3] = wBit ? (byte) ((uint) streamNo | 128U /*0x80*/) : streamNo;
      buffer[4] = functionNo;
      buffer[5] = index == numArrayList2.Count - 1 ? (byte) ((uint) BitConverter.GetBytes(blockNo)[1] | 128U /*0x80*/) : BitConverter.GetBytes(blockNo)[1];
      buffer[6] = BitConverter.GetBytes(blockNo)[0];
      buffer[7] = BitConverter.GetBytes(messageID)[3];
      buffer[8] = BitConverter.GetBytes(messageID)[2];
      buffer[9] = BitConverter.GetBytes(messageID)[1];
      buffer[10] = BitConverter.GetBytes(messageID)[0];
      numArrayList2[index].CopyTo((Array) buffer, 11);
      int acc = SoftLRC.CalculateAcc(buffer, 1, 2);
      buffer[buffer.Length - 2] = BitConverter.GetBytes(acc)[1];
      buffer[buffer.Length - 1] = BitConverter.GetBytes(acc)[0];
      numArrayList1.Add(buffer);
    }
    return numArrayList1;
  }

  /// <summary>根据传入的参数信息，构建完整的SECS/HSMS消息报文列表</summary>
  /// <param name="deviceID">装置识别码</param>
  /// <param name="streamNo">主功能码</param>
  /// <param name="functionNo">子功能码</param>
  /// <param name="blockNo">数据块号</param>
  /// <param name="messageID">消息序号</param>
  /// <param name="data">真实数据消息</param>
  /// <param name="wBit">是否必须回复讯息</param>
  /// <returns>完整的报文信息</returns>
  public static byte[] BuildHSMSMessage(
    ushort deviceID,
    byte streamNo,
    byte functionNo,
    ushort blockNo,
    uint messageID,
    byte[] data,
    bool wBit)
  {
    if (data == null)
      data = new byte[0];
    byte[] numArray = new byte[14 + data.Length];
    numArray[0] = BitConverter.GetBytes(numArray.Length - 4)[3];
    numArray[1] = BitConverter.GetBytes(numArray.Length - 4)[2];
    numArray[2] = BitConverter.GetBytes(numArray.Length - 4)[1];
    numArray[3] = BitConverter.GetBytes(numArray.Length - 4)[0];
    numArray[4] = BitConverter.GetBytes(deviceID)[1];
    numArray[5] = BitConverter.GetBytes(deviceID)[0];
    numArray[6] = wBit ? (byte) ((uint) streamNo | 128U /*0x80*/) : streamNo;
    numArray[7] = functionNo;
    numArray[8] = BitConverter.GetBytes(blockNo)[1];
    numArray[9] = BitConverter.GetBytes(blockNo)[0];
    numArray[10] = BitConverter.GetBytes(messageID)[3];
    numArray[11] = BitConverter.GetBytes(messageID)[2];
    numArray[12] = BitConverter.GetBytes(messageID)[1];
    numArray[13] = BitConverter.GetBytes(messageID)[0];
    data.CopyTo((Array) numArray, 14);
    return numArray;
  }
}
