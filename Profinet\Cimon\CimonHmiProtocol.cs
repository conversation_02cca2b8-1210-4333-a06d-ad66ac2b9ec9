﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Cimon.CimonHmiProtocol
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Profinet.Cimon.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Cimon;

/// <summary>
/// 西蒙PLC的数据读取类，支持的地址请参考Demo测试工具上的示例<br />
/// Simon PLC data reading class, the supported address please refer to the Demo test tool on the example
/// </summary>
public class CimonHmiProtocol : DeviceTcpNet
{
  private byte frameNo = 1;

  /// <summary>实例化一个默认的对象</summary>
  public CimonHmiProtocol()
  {
    this.WordLength = (ushort) 2;
    this.IpAddress = "127.0.0.1";
    this.Port = 10260;
    this.ByteTransform = (IByteTransform) new ReverseWordTransform();
  }

  /// <summary>根据IP地址，端口号来实例化一个对象</summary>
  /// <param name="ip">IP地址</param>
  /// <param name="port">端口号</param>
  public CimonHmiProtocol(string ip, int port = 10260)
    : this()
  {
    this.IpAddress = ip;
    this.Port = port;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return CimonHelper.ExtractActualData(response);
  }

  /// <summary>
  /// 获取或设置当前的站号信息<br />
  /// Gets or sets the current station number information
  /// </summary>
  public byte FrameNo
  {
    get => this.frameNo;
    set => this.frameNo = value;
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> result = CimonHelper.BuildReadByteCommand(this.frameNo, address, (int) length);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : this.ReadFromCoreServer(result.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = CimonHelper.BuildWriteByteCommand(this.frameNo, address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (Regex.IsMatch(address, "D[0-9]+\\.[0-9a-fA-F]"))
      return HslHelper.ReadBool((IReadWriteNet) this, address, length, reverseByWord: true);
    OperateResult<byte[]> result1 = CimonHelper.BuildReadBitCommand(this.frameNo, address, (int) length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) result2.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<byte[]> operateResult = CimonHelper.BuildWriteBitCommand(this.FrameNo, address, value);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> command = CimonHelper.BuildReadByteCommand(this.frameNo, address, (int) length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command.Content);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> command = CimonHelper.BuildWriteByteCommand(this.frameNo, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (Regex.IsMatch(address, "D[0-9]+\\.[0-9a-fA-F]"))
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) this, address, length, reverseByWord: true);
      return operateResult;
    }
    OperateResult<byte[]> command = CimonHelper.BuildReadBitCommand(this.frameNo, address, (int) length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) read.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult<byte[]> command = CimonHelper.BuildWriteBitCommand(this.FrameNo, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"CimonHmiProtocol<{this.CommunicationPipe}>";
}
