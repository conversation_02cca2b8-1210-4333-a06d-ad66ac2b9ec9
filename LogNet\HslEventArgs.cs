﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.HslEventArgs
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>带有日志消息的事件</summary>
public class HslEventArgs : EventArgs
{
  /// <summary>消息信息</summary>
  public HslMessageItem HslMessage { get; set; }
}
