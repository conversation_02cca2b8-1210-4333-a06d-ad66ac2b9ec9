﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Serial.SerialBase
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.IO.Ports;

#nullable disable
namespace HslCommunication.Serial;

/// <summary>
/// 所有串行通信类的基类，提供了一些基础的服务，核心的通信实现<br />
/// The base class of all serial communication classes provides some basic services for the core communication implementation
/// </summary>
public class SerialBase : BinaryCommunication, IDisposable
{
  private bool disposedValue = false;
  private PipeSerialPort pipe;

  /// <summary>
  /// 实例化一个无参的构造方法<br />
  /// Instantiate a parameterless constructor
  /// </summary>
  public SerialBase() => this.CommunicationPipe = (CommunicationPipe) new PipeSerialPort();

  /// <inheritdoc />
  public override CommunicationPipe CommunicationPipe
  {
    get => base.CommunicationPipe;
    set
    {
      base.CommunicationPipe = value;
      if (!(value is PipeSerialPort pipeSerialPort))
        return;
      this.pipe = pipeSerialPort;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSerialPort.SerialPortInni(System.String)" />
  public virtual void SerialPortInni(string portName) => this.pipe.SerialPortInni(portName);

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceSerialPort.SerialPortInni(System.String,System.Int32)" />
  public virtual void SerialPortInni(string portName, int baudRate)
  {
    this.SerialPortInni(portName, baudRate, 8, StopBits.One, Parity.None);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSerialPort.SerialPortInni(System.String,System.Int32,System.Int32,System.IO.Ports.StopBits,System.IO.Ports.Parity)" />
  public virtual void SerialPortInni(
    string portName,
    int baudRate,
    int dataBits,
    StopBits stopBits,
    Parity parity)
  {
    this.pipe.SerialPortInni(portName, baudRate, dataBits, stopBits, parity);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSerialPort.SerialPortInni(System.Action{System.IO.Ports.SerialPort})" />
  public void SerialPortInni(Action<SerialPort> initi)
  {
    this.pipe.SerialPortInni(initi);
    this.PortName = this.pipe.GetPipe().PortName;
    this.BaudRate = this.pipe.GetPipe().BaudRate;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeSerialPort.OpenCommunication" />
  public virtual OperateResult Open()
  {
    OperateResult<bool> operateResult = this.pipe.OpenCommunication();
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    return operateResult.Content ? this.InitializationOnConnect() : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceSerialPort.IsOpen" />
  public bool IsOpen() => this.pipe.GetPipe().IsOpen;

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceSerialPort.Close" />
  public void Close()
  {
    if (!this.pipe.GetPipe().IsOpen)
      return;
    this.ExtraOnDisconnect();
    this.pipe.CloseCommunication();
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeSerialPort.RtsEnable" />
  [HslMqttApi(Description = "Gets or sets a value indicating whether the request sending (RTS) signal is enabled in serial communication.")]
  public bool RtsEnable
  {
    get => this.pipe.RtsEnable;
    set => this.pipe.RtsEnable = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Pipe.PipeSerialPort.ReceiveEmptyDataCount" />
  [HslMqttApi(Description = "Get or set the number of consecutive empty data receptions, which is valid when data reception is completed, default is 1")]
  public int ReceiveEmptyDataCount
  {
    get => this.pipe.ReceiveEmptyDataCount;
    set => this.pipe.ReceiveEmptyDataCount = value;
  }

  /// <summary>
  /// 是否在发送数据前清空缓冲数据，默认是false<br />
  /// Whether to empty the buffer before sending data, the default is false
  /// </summary>
  [HslMqttApi(Description = "Whether to empty the buffer before sending data, the default is false")]
  public bool IsClearCacheBeforeRead
  {
    get => this.pipe.IsClearCacheBeforeRead;
    set => this.pipe.IsClearCacheBeforeRead = value;
  }

  /// <summary>
  /// 当前连接串口信息的端口号名称<br />
  /// The port name of the current connection serial port information
  /// </summary>
  [HslMqttApi(Description = "The port name of the current connection serial port information")]
  public string PortName { get; private set; }

  /// <summary>
  /// 当前连接串口信息的波特率<br />
  /// Baud rate of current connection serial port information
  /// </summary>
  [HslMqttApi(Description = "Baud rate of current connection serial port information")]
  public int BaudRate { get; private set; }

  /// <summary>释放当前的对象</summary>
  /// <param name="disposing">是否在</param>
  protected virtual void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (disposing)
      this.pipe?.CloseCommunication();
    this.disposedValue = true;
  }

  /// <summary>释放当前的对象</summary>
  public void Dispose() => this.Dispose(true);

  /// <inheritdoc />
  public override string ToString() => $"SerialBase{this.CommunicationPipe}";
}
