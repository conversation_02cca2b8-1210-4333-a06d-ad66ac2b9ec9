﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeMqttClient
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.MQTT;
using System;
using System.IO;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>基于MQTT通信实现的管道信息</summary>
public class PipeMqttClient : CommunicationPipe
{
  private MqttClient mqttClient;
  private string writeTopic = string.Empty;
  private string readTopic = string.Empty;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public PipeMqttClient(MqttClient mqttClient, string readTopic, string writeTopic)
  {
    this.readTopic = readTopic;
    this.writeTopic = writeTopic;
    if (mqttClient != null && mqttClient.GetSubscribeTopic(readTopic) == null)
    {
      mqttClient.OnClientConnected += new MqttClient.OnClientConnectedDelegate(this.MqttClient_OnClientConnected);
      mqttClient.SubscribeMessage(readTopic);
      mqttClient.GetSubscribeTopic(readTopic).OnMqttMessageReceived += new MqttClient.MqttMessageReceiveDelegate(this.SubscribeTopic_OnMqttMessageReceived);
    }
    this.UseServerActivePush = true;
    this.mqttClient = mqttClient;
  }

  private void MqttClient_OnClientConnected(MqttClient client)
  {
    client.SubscribeMessage(this.readTopic);
  }

  private void SubscribeTopic_OnMqttMessageReceived(
    MqttClient client,
    MqttApplicationMessage message)
  {
    this.SetBufferQA(message.Payload);
  }

  /// <summary>
  /// 获取当前管道绑定的<see cref="T:HslCommunication.MQTT.MqttClient" />对象信息<br />
  /// Obtain the <see cref="T:HslCommunication.MQTT.MqttClient" /> object bound to the current pipeline
  /// </summary>
  public MqttClient MqttClient => this.mqttClient;

  /// <summary>
  /// 获取当前管道里的用于读取操作的主题名称<br />
  /// Gets the name of the topic used for the read operation in the current pipeline
  /// </summary>
  public string ReadTopic => this.readTopic;

  /// <summary>
  /// 获取当前管道里的用于写入操作的主题名称<br />
  /// Gets the name of the topic used for the write operation in the current pipeline
  /// </summary>
  public string WriteTopic => this.writeTopic;

  /// <inheritdoc />
  public override OperateResult<bool> OpenCommunication()
  {
    if (this.mqttClient.GetSubscribeTopic(this.readTopic) == null)
    {
      this.mqttClient.OnClientConnected += new MqttClient.OnClientConnectedDelegate(this.MqttClient_OnClientConnected);
      this.mqttClient.SubscribeMessage(this.readTopic);
      this.mqttClient.GetSubscribeTopic(this.readTopic).OnMqttMessageReceived += new MqttClient.MqttMessageReceiveDelegate(this.SubscribeTopic_OnMqttMessageReceived);
    }
    return OperateResult.CreateSuccessResult<bool>(false);
  }

  /// <inheritdoc />
  public override OperateResult CloseCommunication()
  {
    SubscribeTopic subscribeTopic = this.mqttClient.GetSubscribeTopic(this.readTopic);
    if (subscribeTopic != null)
      subscribeTopic.OnMqttMessageReceived -= new MqttClient.MqttMessageReceiveDelegate(this.SubscribeTopic_OnMqttMessageReceived);
    this.mqttClient.OnClientConnected -= new MqttClient.OnClientConnectedDelegate(this.MqttClient_OnClientConnected);
    this.mqttClient.UnSubscribeMessage(this.readTopic);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult Send(byte[] data, int offset, int size)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    return this.mqttClient.PublishMessage(new MqttApplicationMessage()
    {
      Topic = this.writeTopic,
      Payload = size == data.Length ? data : data.SelectMiddle<byte>(offset, size)
    });
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReceiveMessage(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    DateTime now = DateTime.Now;
    MemoryStream ms = new MemoryStream();
    do
    {
      if (this.ReceiveTimeOut < 0 || (DateTime.Now - now).TotalMilliseconds <= (double) this.ReceiveTimeOut)
      {
        if (this.autoResetEvent.WaitOne(this.ReceiveTimeOut))
        {
          byte[] bufferQa = this.bufferQA;
          if (bufferQa != null && bufferQa.Length != 0)
          {
            ms.Write(this.bufferQA);
            if (logMessage != null)
              logMessage(this.bufferQA);
          }
        }
        else
          goto label_8;
      }
      else
        goto label_1;
    }
    while (netMessage != null && !this.CheckMessageComplete(netMessage, sendValue, ref ms));
    goto label_10;
label_1:
    return new OperateResult<byte[]>(StringResources.Language.ReceiveDataTimeout + this.ReceiveTimeOut.ToString());
label_8:
    return new OperateResult<byte[]>(-10000, $"{StringResources.Language.ReceiveDataTimeout}{this.ReceiveTimeOut.ToString()} Received: {ms.ToArray().ToHexString(' ')}");
label_10:
    return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
  }

  /// <inheritdoc />
  public override string ToString() => $"PipeMqttClient[{this.mqttClient}]";
}
