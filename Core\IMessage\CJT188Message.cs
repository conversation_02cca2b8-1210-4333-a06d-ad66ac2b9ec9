﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.CJT188Message
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Instrument.DLT.Helper;
using System.IO;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>CJT188的协议信息</summary>
public class CJT188Message : NetMessageBase, INetMessage
{
  /// <summary>根据是否进行站号检查来实例化一个对象</summary>
  /// <param name="stationMatch">是否进行站号检查</param>
  public CJT188Message(bool stationMatch) => this.StationMatch = stationMatch;

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 11;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => (int) this.HeadBytes[10] + 2;

  /// <inheritdoc />
  public override int PependedUselesByteLength(byte[] headByte)
  {
    return DLT645Helper.FindHeadCode68H(headByte);
  }

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    if (!this.StationMatch || send.Length < 9 || receive.Length < 9)
      return 1;
    string hexString1 = send.SelectMiddle<byte>(2, 7).ToHexString();
    string hexString2 = receive.SelectMiddle<byte>(2, 7).ToHexString();
    return hexString1 == "AAAAAAAAAAAAAA" || hexString2 == "AAAAAAAAAAAAAA" || hexString1 == hexString2 ? 1 : -1;
  }

  /// <inheritdoc />
  public override bool CheckReceiveDataComplete(byte[] send, MemoryStream ms)
  {
    byte[] array = ms.ToArray();
    if (array.Length < 11)
      return false;
    int headCode68H = DLT645Helper.FindHeadCode68H(array);
    return headCode68H >= 0 && (int) array[headCode68H + 10] + 13 + headCode68H == array.Length && array[array.Length - 1] == (byte) 22;
  }

  /// <summary>
  /// 获取或设置是否验证匹配接收到的站号信息<br />
  /// Gets or sets whether to verify that the received station number information is matched
  /// </summary>
  public bool StationMatch { get; set; } = false;
}
