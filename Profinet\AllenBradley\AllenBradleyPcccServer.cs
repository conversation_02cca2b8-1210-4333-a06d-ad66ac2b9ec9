﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyPcccServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Net;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>虚拟的PCCC服务器，模拟的AB 1400通信</summary>
public class AllenBradleyPcccServer : DeviceServer
{
  private SoftBuffer aBuffer;
  private SoftBuffer bBuffer;
  private SoftBuffer nBuffer;
  private SoftBuffer fBuffer;
  private SoftBuffer sBuffer;
  private SoftBuffer iBuffer;
  private SoftBuffer oBuffer;
  private uint sessionID = 3305331106;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>实例化一个默认的对象</summary>
  public AllenBradleyPcccServer()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 2;
    this.aBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.bBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.nBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.fBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.sBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.iBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.oBuffer = new SoftBuffer(65536 /*0x010000*/);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    switch (from.Content.DataCode)
    {
      case 130:
        return OperateResult.CreateSuccessResult<byte[]>(this.oBuffer.GetBytes(from.Content.AddressStart, (int) length));
      case 131:
        return OperateResult.CreateSuccessResult<byte[]>(this.iBuffer.GetBytes(from.Content.AddressStart, (int) length));
      case 132:
        return OperateResult.CreateSuccessResult<byte[]>(this.sBuffer.GetBytes(from.Content.AddressStart, (int) length));
      case 133:
        return OperateResult.CreateSuccessResult<byte[]>(this.bBuffer.GetBytes(from.Content.AddressStart, (int) length));
      case 137:
        return OperateResult.CreateSuccessResult<byte[]>(this.nBuffer.GetBytes(from.Content.AddressStart, (int) length));
      case 138:
        return OperateResult.CreateSuccessResult<byte[]>(this.fBuffer.GetBytes(from.Content.AddressStart, (int) length));
      case 142:
        return OperateResult.CreateSuccessResult<byte[]>(this.aBuffer.GetBytes(from.Content.AddressStart, (int) length));
      default:
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) from;
    switch (from.Content.DataCode)
    {
      case 130:
        this.oBuffer.SetBytes(value, from.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      case 131:
        this.iBuffer.SetBytes(value, from.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      case 132:
        this.sBuffer.SetBytes(value, from.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      case 133:
        this.bBuffer.SetBytes(value, from.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      case 137:
        this.nBuffer.SetBytes(value, from.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      case 138:
        this.fBuffer.SetBytes(value, from.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      case 142:
        this.aBuffer.SetBytes(value, from.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      default:
        return new OperateResult(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new AllenBradleyMessage();

  /// <inheritdoc />
  protected override OperateResult ThreadPoolLoginAfterClientCheck(
    PipeSession session,
    IPEndPoint endPoint)
  {
    CommunicationPipe communication = session.Communication;
    AllenBradleyMessage allenBradleyMessage = new AllenBradleyMessage();
    OperateResult<byte[]> message1 = communication.ReceiveMessage((INetMessage) allenBradleyMessage, (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
    if (!message1.IsSuccess)
      return (OperateResult) message1;
    message1.Content.SelectMiddle<byte>(12, 8).ToHexString();
    byte[] numArray1 = AllenBradleyHelper.PackRequestHeader((ushort) 101, this.sessionID, new byte[4]
    {
      (byte) 1,
      (byte) 0,
      (byte) 0,
      (byte) 0
    });
    this.LogSendMessage(numArray1, session);
    OperateResult operateResult1 = communication.Send(numArray1);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> message2 = communication.ReceiveMessage((INetMessage) allenBradleyMessage, (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
    if (!message2.IsSuccess)
      return (OperateResult) message2;
    byte[] numArray2 = AllenBradleyHelper.PackRequestHeader((ushort) 111, this.sessionID, "00 00 00 00 00 04 02 00 00 00 00 00 b2 00 1e 00 d4 00 00 00 cc 31 59 a2 e8 a3 14 00 27 04 09 10 0b 46 a5 c1 01 40 20 00 01 40 20 00 00 00".ToHexBytes());
    this.LogSendMessage(numArray2, session);
    OperateResult operateResult2 = communication.Send(numArray2);
    return !operateResult2.IsSuccess ? operateResult2 : base.ThreadPoolLoginAfterClientCheck(session, endPoint);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    byte[] numArray;
    if (receive[0] == (byte) 111)
      numArray = AllenBradleyHelper.PackRequestHeader((ushort) 111, this.sessionID, AllenBradleyHelper.PackCommandSpecificData(AllenBradleyHelper.PackCommandSingleService((byte[]) null, (ushort) 0), AllenBradleyHelper.PackCommandSingleService("ce 00 00 00 27 04 09 10 0b 46 a5 c1 00 00".ToHexBytes())));
    else
      numArray = receive[0] != (byte) 102 ? this.ReadWriteCommand(receive.RemoveBegin<byte>(59)) : AllenBradleyHelper.PackRequestHeader((ushort) 111, this.sessionID, (byte[]) null);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  private byte[] GetResponse(int status, byte[] data)
  {
    byte[] response = AllenBradleyHelper.PackRequestHeader((ushort) 112 /*0x70*/, this.sessionID, AllenBradleyHelper.PackCommandSpecificData(AllenBradleyHelper.PackCommandSingleService("e8 a3 14 00".ToHexBytes(), (ushort) 161), AllenBradleyHelper.PackCommandSingleService(SoftBasic.SpliceArray<byte>("09 00 cb 00 00 00 07 09 10 0b 46 a5 c1 4f 00 08 00".ToHexBytes(), data), (ushort) 177)));
    this.ByteTransform.TransByte(status).CopyTo((Array) response, 8);
    return response;
  }

  private int GetDynamicLengthData(byte[] fccc, ref int offset)
  {
    int uint16 = (int) fccc[offset++];
    if (uint16 == (int) byte.MaxValue)
    {
      uint16 = (int) BitConverter.ToUInt16(fccc, offset);
      offset += 2;
    }
    return uint16;
  }

  private byte[] ReadWriteCommand(byte[] fccc)
  {
    int length = (int) fccc[5];
    int offset1 = 6;
    this.GetDynamicLengthData(fccc, ref offset1);
    byte[] numArray = fccc;
    int index = offset1;
    int offset2 = index + 1;
    byte num1 = numArray[index];
    int dynamicLengthData = this.GetDynamicLengthData(fccc, ref offset2);
    this.GetDynamicLengthData(fccc, ref offset2);
    if (fccc[4] == (byte) 162)
    {
      switch (num1)
      {
        case 130:
          return this.GetResponse(0, this.oBuffer.GetBytes(dynamicLengthData, length));
        case 131:
          return this.GetResponse(0, this.iBuffer.GetBytes(dynamicLengthData, length));
        case 132:
          return this.GetResponse(0, this.sBuffer.GetBytes(dynamicLengthData, length));
        case 133:
          return this.GetResponse(0, this.bBuffer.GetBytes(dynamicLengthData, length));
        case 137:
          return this.GetResponse(0, this.nBuffer.GetBytes(dynamicLengthData, length));
        case 138:
          return this.GetResponse(0, this.fBuffer.GetBytes(dynamicLengthData, length));
        case 142:
          return this.GetResponse(0, this.aBuffer.GetBytes(dynamicLengthData, length));
        default:
          return this.GetResponse(1, (byte[]) null);
      }
    }
    else if (fccc[4] == (byte) 170)
    {
      byte[] data = fccc.RemoveBegin<byte>(offset2);
      switch (num1)
      {
        case 130:
          this.oBuffer.SetBytes(data, dynamicLengthData);
          return this.GetResponse(0, (byte[]) null);
        case 131:
          this.iBuffer.SetBytes(data, dynamicLengthData);
          return this.GetResponse(0, (byte[]) null);
        case 132:
          this.sBuffer.SetBytes(data, dynamicLengthData);
          return this.GetResponse(0, (byte[]) null);
        case 133:
          this.bBuffer.SetBytes(data, dynamicLengthData);
          return this.GetResponse(0, (byte[]) null);
        case 137:
          this.nBuffer.SetBytes(data, dynamicLengthData);
          return this.GetResponse(0, (byte[]) null);
        case 138:
          this.fBuffer.SetBytes(data, dynamicLengthData);
          return this.GetResponse(0, (byte[]) null);
        case 142:
          this.aBuffer.SetBytes(data, dynamicLengthData);
          return this.GetResponse(0, (byte[]) null);
        default:
          return this.GetResponse(1, (byte[]) null);
      }
    }
    else
    {
      if (fccc[4] != (byte) 171)
        return this.GetResponse(1, (byte[]) null);
      SoftBuffer softBuffer;
      switch (num1)
      {
        case 130:
          softBuffer = this.oBuffer;
          break;
        case 131:
          softBuffer = this.iBuffer;
          break;
        case 132:
          softBuffer = this.sBuffer;
          break;
        case 133:
          softBuffer = this.bBuffer;
          break;
        case 137:
          softBuffer = this.nBuffer;
          break;
        case 138:
          softBuffer = this.fBuffer;
          break;
        case 142:
          softBuffer = this.aBuffer;
          break;
        default:
          return this.GetResponse(1, (byte[]) null);
      }
      int uint16_1 = (int) BitConverter.ToUInt16(fccc, offset2);
      int uint16_2 = (int) BitConverter.ToUInt16(fccc, offset2 + 2);
      ushort num2 = (ushort) ((int) softBuffer.GetUInt16(dynamicLengthData) & ~uint16_1 | uint16_2);
      softBuffer.SetValue(num2, dynamicLengthData);
      return this.GetResponse(0, (byte[]) null);
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"AllenBradleyPcccServer[{this.Port}]";
}
