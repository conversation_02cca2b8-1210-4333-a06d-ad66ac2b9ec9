﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.LSisServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Panasonic;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.LSIS;

/// <summary>
/// Lsis的虚拟服务器，其中TCP的端口支持Fnet协议，串口支持Cnet协议<br />
/// LSisServer
/// </summary>
public class LSisServer : DeviceServer
{
  private SoftBuffer pBuffer;
  private SoftBuffer qBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer iBuffer;
  private SoftBuffer uBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer tBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private int station = 1;

  /// <summary>LSisServer</summary>
  public LSisServer(string CpuType)
  {
    this.pBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.qBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.iBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.uBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.tBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.SetCpuType = CpuType;
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>set plc</summary>
  public string SetCpuType { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<string> byteUnit = this.AnalysisAddressToByteUnit(address, false);
    if (!byteUnit.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) byteUnit);
    int index = int.Parse(byteUnit.Content.Substring(1));
    switch (byteUnit.Content[0])
    {
      case 'D':
        return OperateResult.CreateSuccessResult<byte[]>(this.dBuffer.GetBytes(index, (int) length));
      case 'I':
        return OperateResult.CreateSuccessResult<byte[]>(this.iBuffer.GetBytes(index, (int) length));
      case 'M':
        return OperateResult.CreateSuccessResult<byte[]>(this.mBuffer.GetBytes(index, (int) length));
      case 'P':
        return OperateResult.CreateSuccessResult<byte[]>(this.pBuffer.GetBytes(index, (int) length));
      case 'Q':
        return OperateResult.CreateSuccessResult<byte[]>(this.qBuffer.GetBytes(index, (int) length));
      case 'T':
        return OperateResult.CreateSuccessResult<byte[]>(this.tBuffer.GetBytes(index, (int) length));
      case 'U':
        return OperateResult.CreateSuccessResult<byte[]>(this.uBuffer.GetBytes(index, (int) length));
      default:
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<string> byteUnit = this.AnalysisAddressToByteUnit(address, false);
    if (!byteUnit.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) byteUnit);
    int destIndex = int.Parse(byteUnit.Content.Substring(1));
    switch (byteUnit.Content[0])
    {
      case 'D':
        this.dBuffer.SetBytes(value, destIndex);
        break;
      case 'I':
        this.iBuffer.SetBytes(value, destIndex);
        break;
      case 'M':
        this.mBuffer.SetBytes(value, destIndex);
        break;
      case 'P':
        this.pBuffer.SetBytes(value, destIndex);
        break;
      case 'Q':
        this.qBuffer.SetBytes(value, destIndex);
        break;
      case 'T':
        this.tBuffer.SetBytes(value, destIndex);
        break;
      case 'U':
        this.uBuffer.SetBytes(value, destIndex);
        break;
      default:
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.ReadByte(System.String)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.Write(System.String,System.Byte)" />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<string> byteUnit = this.AnalysisAddressToByteUnit(address, true);
    if (!byteUnit.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) byteUnit);
    int destIndex = int.Parse(byteUnit.Content.Substring(1));
    switch (byteUnit.Content[0])
    {
      case 'I':
        return OperateResult.CreateSuccessResult<bool[]>(this.iBuffer.GetBool(destIndex, (int) length));
      case 'M':
        return OperateResult.CreateSuccessResult<bool[]>(this.mBuffer.GetBool(destIndex, (int) length));
      case 'P':
        return OperateResult.CreateSuccessResult<bool[]>(this.pBuffer.GetBool(destIndex, (int) length));
      case 'Q':
        return OperateResult.CreateSuccessResult<bool[]>(this.qBuffer.GetBool(destIndex, (int) length));
      case 'U':
        return OperateResult.CreateSuccessResult<bool[]>(this.uBuffer.GetBool(destIndex, (int) length));
      default:
        return new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<string> byteUnit = this.AnalysisAddressToByteUnit(address, true);
    if (!byteUnit.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) byteUnit);
    int destIndex = int.Parse(byteUnit.Content.Substring(1));
    switch (byteUnit.Content[0])
    {
      case 'I':
        this.iBuffer.SetBool(value, destIndex);
        return OperateResult.CreateSuccessResult();
      case 'M':
        this.mBuffer.SetBool(value, destIndex);
        return OperateResult.CreateSuccessResult();
      case 'P':
        this.pBuffer.SetBool(value, destIndex);
        return OperateResult.CreateSuccessResult();
      case 'Q':
        this.qBuffer.SetBool(value, destIndex);
        return OperateResult.CreateSuccessResult();
      case 'U':
        this.uBuffer.SetBool(value, destIndex);
        return OperateResult.CreateSuccessResult();
      default:
        return new OperateResult(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new LsisFastEnetMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (session.Communication is PipeSerialPort)
    {
      try
      {
        byte[] numArray = (byte[]) null;
        if (receive[3] == (byte) 114 || receive[3] == (byte) 82)
          numArray = this.ReadSerialByCommand(receive);
        else if (receive[3] == (byte) 119 || receive[3] == (byte) 87)
          numArray = this.WriteSerialByMessage(receive);
        return OperateResult.CreateSuccessResult<byte[]>(numArray);
      }
      catch (Exception ex)
      {
        return new OperateResult<byte[]>($"{ex.Message} Source: {receive.ToHexString(' ')}");
      }
    }
    else
    {
      byte[] numArray = (byte[]) null;
      if (receive[20] == (byte) 84)
        numArray = this.ReadByCommand(receive);
      else if (receive[20] == (byte) 88)
        numArray = this.WriteByMessage(receive);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
  }

  private byte[] ReadByCommand(byte[] command)
  {
    List<byte> byteList = new List<byte>();
    byteList.AddRange((IEnumerable<byte>) command.SelectBegin<byte>(20));
    byteList[9] = (byte) 17;
    byteList[10] = (byte) 1;
    byteList[12] = (byte) 160 /*0xA0*/;
    byteList[13] = (byte) 17;
    byteList[18] = (byte) 3;
    byteList.AddRange((IEnumerable<byte>) new byte[10]
    {
      (byte) 85,
      (byte) 0,
      command[22],
      command[23],
      (byte) 8,
      (byte) 1,
      (byte) 0,
      (byte) 0,
      (byte) 1,
      (byte) 0
    });
    int num = (int) command[28];
    string address = Encoding.ASCII.GetString(command, 31 /*0x1F*/, num - 1);
    byte[] collection;
    if (command[22] == (byte) 0)
    {
      int int32 = Convert.ToInt32(address.Substring(2));
      byte[] numArray;
      if (!this.ReadBool(address.Substring(0, 2) + (int32 / 16 /*0x10*/).ToString() + (int32 % 16 /*0x10*/).ToString("X1")).Content)
        numArray = new byte[1];
      else
        numArray = new byte[1]{ (byte) 1 };
      collection = numArray;
    }
    else if (command[22] == (byte) 1)
      collection = this.Read(address, (ushort) 1).Content;
    else if (command[22] == (byte) 2)
      collection = this.Read(address, (ushort) 2).Content;
    else if (command[22] == (byte) 3)
      collection = this.Read(address, (ushort) 4).Content;
    else if (command[22] == (byte) 4)
      collection = this.Read(address, (ushort) 8).Content;
    else if (command[22] == (byte) 20)
    {
      ushort uint16 = BitConverter.ToUInt16(command, 30 + num);
      collection = this.Read(address, uint16).Content;
    }
    else
      collection = this.Read(address, (ushort) 1).Content;
    byteList.AddRange((IEnumerable<byte>) BitConverter.GetBytes((ushort) collection.Length));
    byteList.AddRange((IEnumerable<byte>) collection);
    byteList[16 /*0x10*/] = (byte) (byteList.Count - 20);
    return byteList.ToArray();
  }

  private byte[] WriteByMessage(byte[] packCommand)
  {
    if (!this.EnableWrite)
      return (byte[]) null;
    List<byte> byteList = new List<byte>();
    byteList.AddRange((IEnumerable<byte>) packCommand.SelectBegin<byte>(20));
    byteList[9] = (byte) 17;
    byteList[10] = (byte) 1;
    byteList[12] = (byte) 160 /*0xA0*/;
    byteList[13] = (byte) 17;
    byteList[18] = (byte) 3;
    byteList.AddRange((IEnumerable<byte>) new byte[10]
    {
      (byte) 89,
      (byte) 0,
      (byte) 20,
      (byte) 0,
      (byte) 8,
      (byte) 1,
      (byte) 0,
      (byte) 0,
      (byte) 1,
      (byte) 0
    });
    int num1 = (int) packCommand[28];
    string address = Encoding.ASCII.GetString(packCommand, 31 /*0x1F*/, num1 - 1);
    int uint16 = (int) BitConverter.ToUInt16(packCommand, 30 + num1);
    byte[] numArray = this.ByteTransform.TransByte(packCommand, 32 /*0x20*/ + num1, uint16);
    if (packCommand[22] == (byte) 0)
    {
      int num2;
      if (address.IndexOf('.') < 0)
      {
        num2 = int.Parse(address.Substring(2), NumberStyles.HexNumber);
      }
      else
      {
        string[] strArray = address.Substring(2, address.Length - 2).Split('.');
        num2 = strArray.Length < 3 ? int.Parse(strArray[1], NumberStyles.HexNumber) : int.Parse(strArray[2], NumberStyles.HexNumber);
      }
      int int32 = Convert.ToInt32(num2);
      string str1 = address.Substring(0, 2);
      int num3 = int32 / 16 /*0x10*/;
      string str2 = num3.ToString();
      num3 = int32 % 16 /*0x10*/;
      string str3 = num3.ToString("X1");
      this.Write(str1 + str2 + str3, numArray[0] == (byte) 1);
    }
    else
      this.Write(address, numArray);
    byteList[16 /*0x10*/] = (byte) (byteList.Count - 20);
    return byteList.ToArray();
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 262144 /*0x040000*/)
      throw new Exception("File is not correct");
    this.pBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.qBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.dBuffer.SetBytes(content, 196608 /*0x030000*/, 0, 65536 /*0x010000*/);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[262144 /*0x040000*/];
    Array.Copy((Array) this.pBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.qBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.mBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dBuffer.GetBytes(), 0, (Array) destinationArray, 196608 /*0x030000*/, 65536 /*0x010000*/);
    return destinationArray;
  }

  /// <summary>NumberStyles HexNumber</summary>
  /// <param name="value"></param>
  /// <returns></returns>
  private static bool IsHex(string value)
  {
    if (string.IsNullOrEmpty(value))
      return false;
    bool flag = false;
    for (int index = 0; index < value.Length; ++index)
    {
      switch (value[index])
      {
        case 'A':
        case 'B':
        case 'C':
        case 'D':
        case 'E':
        case 'F':
        case 'a':
        case 'b':
        case 'c':
        case 'd':
        case 'e':
        case 'f':
          flag = true;
          break;
      }
    }
    return flag;
  }

  /// <summary>Check the intput string address</summary>
  /// <param name="address"></param>
  /// <returns></returns>
  public static int CheckAddress(string address)
  {
    int num = 0;
    if (LSisServer.IsHex(address))
    {
      int result;
      if (int.TryParse(address, NumberStyles.HexNumber, (IFormatProvider) CultureInfo.CurrentCulture, out result))
        num = result;
    }
    else
      num = int.Parse(address);
    return num;
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength > 5 ? buffer[receivedLength - 3] == (byte) 4 : base.CheckSerialReceiveDataComplete(buffer, receivedLength);
  }

  /// <inheritdoc />
  protected override string GetLogTextFromBinary(PipeSession session, byte[] content)
  {
    return session != null && session.Communication is PipeSerialPort ? SoftBasic.GetAsciiStringRender(content) : base.GetLogTextFromBinary(session, content);
  }

  private byte[] PackReadSerialResponse(byte[] receive, short err, List<byte[]> data)
  {
    List<byte> byteList = new List<byte>(24);
    if (err == (short) 0)
      byteList.Add((byte) 6);
    else
      byteList.Add((byte) 21);
    byteList.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) this.station));
    byteList.Add(receive[3]);
    byteList.Add(receive[4]);
    byteList.Add(receive[5]);
    if (err == (short) 0)
    {
      if (data != null)
      {
        if (Encoding.ASCII.GetString(receive, 4, 2) == "SS")
          byteList.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) data.Count));
        else if (Encoding.ASCII.GetString(receive, 4, 2) == "SB")
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes("01"));
        for (int index = 0; index < data.Count; ++index)
        {
          byteList.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) data[index].Length));
          byteList.AddRange((IEnumerable<byte>) SoftBasic.BytesToAsciiBytes(data[index]));
        }
      }
    }
    else
      byteList.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom(err));
    byteList.Add((byte) 3);
    int num = 0;
    for (int index = 0; index < byteList.Count; ++index)
      num += (int) byteList[index];
    byteList.AddRange((IEnumerable<byte>) SoftBasic.BuildAsciiBytesFrom((byte) num));
    return byteList.ToArray();
  }

  private byte[] ReadSerialByCommand(byte[] command)
  {
    SoftBasic.GetAsciiStringRender(command);
    if (Encoding.ASCII.GetString(command, 4, 2) == "SS")
    {
      int num = int.Parse(Encoding.ASCII.GetString(command, 6, 2));
      if (num > 16 /*0x10*/)
        return this.PackReadSerialResponse(command, (short) 4, (List<byte[]>) null);
      List<byte[]> data = new List<byte[]>();
      int index1 = 8;
      for (int index2 = 0; index2 < num; ++index2)
      {
        int int32 = Convert.ToInt32(Encoding.ASCII.GetString(command, index1, 2), 16 /*0x10*/);
        string address = Encoding.ASCII.GetString(command, index1 + 2 + 1, int32 - 1);
        if (address[1] != 'X')
        {
          OperateResult<byte[]> operateResult = this.Read(address, LSisServer.AnalysisAddressLength(address));
          if (!operateResult.IsSuccess)
            return this.PackReadSerialResponse(command, (short) 1, (List<byte[]>) null);
          data.Add(operateResult.Content);
        }
        else
        {
          OperateResult<bool> operateResult = this.ReadBool(address);
          if (!operateResult.IsSuccess)
            return this.PackReadSerialResponse(command, (short) 1, (List<byte[]>) null);
          List<byte[]> numArrayList = data;
          byte[] numArray;
          if (!operateResult.Content)
            numArray = new byte[1];
          else
            numArray = new byte[1]{ (byte) 1 };
          numArrayList.Add(numArray);
        }
        index1 += 2 + int32;
      }
      return this.PackReadSerialResponse(command, (short) 0, data);
    }
    if (!(Encoding.ASCII.GetString(command, 4, 2) == "SB"))
      return this.PackReadSerialResponse(command, (short) 1, (List<byte[]>) null);
    int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(command, 6, 2), 16 /*0x10*/);
    string address1 = Encoding.ASCII.GetString(command, 9, int32_1 - 1);
    ushort length = (ushort) ((uint) Convert.ToUInt16(Encoding.ASCII.GetString(command, 8 + int32_1, 2), 16 /*0x10*/) * (uint) LSisServer.AnalysisAddressLength(address1));
    if (length > (ushort) 120)
      return this.PackReadSerialResponse(command, (short) 4658, (List<byte[]>) null);
    OperateResult<byte[]> operateResult1 = this.Read(address1, length);
    if (!operateResult1.IsSuccess)
      return this.PackReadSerialResponse(command, (short) 1, (List<byte[]>) null);
    return this.PackReadSerialResponse(command, (short) 0, new List<byte[]>()
    {
      operateResult1.Content
    });
  }

  private byte[] WriteSerialByMessage(byte[] command)
  {
    if (!this.EnableWrite)
      return (byte[]) null;
    if (Encoding.ASCII.GetString(command, 4, 2) == "SS")
    {
      int num = int.Parse(Encoding.ASCII.GetString(command, 6, 2));
      int index1 = 8;
      if (num > 16 /*0x10*/)
        return this.PackReadSerialResponse(command, (short) 4, (List<byte[]>) null);
      for (int index2 = 0; index2 < num; ++index2)
      {
        int int32 = Convert.ToInt32(Encoding.ASCII.GetString(command, index1, 2), 16 /*0x10*/);
        string address = Encoding.ASCII.GetString(command, index1 + 2 + 1, int32 - 1);
        switch (address[1])
        {
          case 'B':
          case 'D':
          case 'L':
          case 'W':
            byte[] hexBytes = Encoding.ASCII.GetString(command, index1 + 2 + int32, (int) LSisServer.AnalysisAddressLength(address) * 2).ToHexBytes();
            if (!this.Write(address, hexBytes).IsSuccess)
              return this.PackReadSerialResponse(command, (short) 1, (List<byte[]>) null);
            index1 += 2 + int32 + (int) LSisServer.AnalysisAddressLength(address) * 2;
            break;
          case 'X':
            if (!this.Write(address, Convert.ToByte(Encoding.ASCII.GetString(command, index1 + 2 + int32, 2), 16 /*0x10*/) > (byte) 0).IsSuccess)
              return this.PackReadSerialResponse(command, (short) 1, (List<byte[]>) null);
            index1 += 2 + int32 + 2;
            break;
        }
      }
      return this.PackReadSerialResponse(command, (short) 0, (List<byte[]>) null);
    }
    if (!(Encoding.ASCII.GetString(command, 4, 2) == "SB"))
      return this.PackReadSerialResponse(command, (short) 4402, (List<byte[]>) null);
    int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(command, 6, 2), 16 /*0x10*/);
    string address1 = Encoding.ASCII.GetString(command, 9, int32_1 - 1);
    if (address1[1] == 'X')
      return this.PackReadSerialResponse(command, (short) 4402, (List<byte[]>) null);
    int num1 = (int) Convert.ToUInt16(Encoding.ASCII.GetString(command, 8 + int32_1, 2), 16 /*0x10*/) * (int) LSisServer.AnalysisAddressLength(address1);
    return !this.Write(address1, Encoding.ASCII.GetString(command, 10 + int32_1, num1 * 2).ToHexBytes()).IsSuccess ? this.PackReadSerialResponse(command, (short) 1, (List<byte[]>) null) : this.PackReadSerialResponse(command, (short) 0, (List<byte[]>) null);
  }

  /// <inheritdoc />
  public override string ToString() => $"LSisServer[{this.Port}]";

  private static ushort AnalysisAddressLength(string address)
  {
    switch (address[1])
    {
      case 'B':
        return 1;
      case 'D':
        return 4;
      case 'L':
        return 8;
      case 'W':
        return 2;
      case 'X':
        return 1;
      default:
        return 1;
    }
  }

  /// <summary>将带有数据类型的地址，转换成实际的byte数组的地址信息，例如 MW100 转成 M200</summary>
  /// <param name="address">带有类型的地址</param>
  /// <param name="isBit">是否是位操作</param>
  /// <returns>最终的按照字节为单位的地址信息</returns>
  public OperateResult<string> AnalysisAddressToByteUnit(string address, bool isBit)
  {
    if (!"PMLKFTCDSQINUZR".Contains(address.Substring(0, 1)))
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    try
    {
      int num;
      if (address[0] == 'D' || address[0] == 'T')
      {
        switch (address[1])
        {
          case 'B':
            num = Convert.ToInt32(address.Substring(2));
            break;
          case 'D':
            num = Convert.ToInt32(address.Substring(2)) * 4;
            break;
          case 'L':
            num = Convert.ToInt32(address.Substring(2)) * 8;
            break;
          case 'W':
            num = Convert.ToInt32(address.Substring(2)) * 2;
            break;
          default:
            num = Convert.ToInt32(address.Substring(1)) * 2;
            break;
        }
      }
      else if (isBit)
      {
        num = address[1] == 'X' ? PanasonicHelper.CalculateComplexAddress(address.Substring(2)) : PanasonicHelper.CalculateComplexAddress(address.Substring(1));
      }
      else
      {
        switch (address[1])
        {
          case 'B':
            num = Convert.ToInt32(address.Substring(2));
            break;
          case 'D':
            num = Convert.ToInt32(address.Substring(2)) * 4;
            break;
          case 'L':
            num = Convert.ToInt32(address.Substring(2)) * 8;
            break;
          case 'W':
            num = Convert.ToInt32(address.Substring(2)) * 2;
            break;
          case 'X':
            num = Convert.ToInt32(address.Substring(2));
            break;
          default:
            num = Convert.ToInt32(address.Substring(1)) * (isBit ? 1 : 2);
            break;
        }
      }
      return OperateResult.CreateSuccessResult<string>(address.Substring(0, 1) + num.ToString());
    }
    catch (Exception ex)
    {
      return new OperateResult<string>($"AnalysisAddress Failed: {ex.Message} Source: {address}");
    }
  }
}
