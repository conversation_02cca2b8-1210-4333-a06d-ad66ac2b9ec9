﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Geniitek.VibrationSensorPeekValue
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Geniitek;

/// <summary>振动传感器的峰值数据类</summary>
public class VibrationSensorPeekValue
{
  /// <summary>X轴的加速度，单位 m/s2</summary>
  public float AcceleratedSpeedX { get; set; }

  /// <summary>Y轴的加速度，单位 m/s2</summary>
  public float AcceleratedSpeedY { get; set; }

  /// <summary>Z轴的加速度，单位 m/s2</summary>
  public float AcceleratedSpeedZ { get; set; }

  /// <summary>X轴的速度，单位 mm/s</summary>
  public float SpeedX { get; set; }

  /// <summary>Y轴的速度，单位 mm/s</summary>
  public float SpeedY { get; set; }

  /// <summary>Z轴的速度，单位 mm/s</summary>
  public float SpeedZ { get; set; }

  /// <summary>X轴的位置，单位 um</summary>
  public int OffsetX { get; set; }

  /// <summary>Y轴的位移，单位 um</summary>
  public int OffsetY { get; set; }

  /// <summary>Z轴的位移，单位 um</summary>
  public int OffsetZ { get; set; }

  /// <summary>温度，单位 摄氏度</summary>
  public float Temperature { get; set; }

  /// <summary>电压，单位 伏特</summary>
  public float Voltage { get; set; }

  /// <summary>数据的发送间隔，单位秒</summary>
  public int SendingInterval { get; set; }
}
