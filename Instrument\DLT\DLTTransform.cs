﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLTTransform
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Instrument.DLT.Helper;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>DTL数据转换</summary>
public class DLTTransform
{
  /// <summary>Byte[]转ToHexString</summary>
  /// <param name="content">原始的字节内容</param>
  /// <param name="length">长度信息</param>
  /// <returns>字符串的结果信息</returns>
  public static OperateResult<string> TransStringFromDLt(byte[] content, ushort length)
  {
    OperateResult<string> operateResult;
    try
    {
      string empty = string.Empty;
      operateResult = OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(((IEnumerable<byte>) content.SelectBegin<byte>((int) length)).Reverse<byte>().ToArray<byte>().EveryByteAdd(-51)));
    }
    catch (Exception ex)
    {
      operateResult = new OperateResult<string>($"{ex.Message} Reason: {content.ToHexString(' ')}");
    }
    return operateResult;
  }

  private static byte[] CreateFromStrings(string[] content, bool reverse)
  {
    MemoryStream ms = new MemoryStream();
    for (int index = 0; index < content.Length; ++index)
    {
      if (reverse)
        ms.Write(((IEnumerable<byte>) content[index].ToHexBytes()).Reverse<byte>().ToArray<byte>());
      else
        ms.Write(((IEnumerable<byte>) content[index].ToHexBytes()).ToArray<byte>());
    }
    return ms.ToArray();
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLTTransform.TransDltFromStrings(HslCommunication.Instrument.DLT.Helper.DLT645Type,System.String[],System.Byte[],System.Boolean)" />
  public static OperateResult<byte[]> TransDltFromStrings(
    DLT645Type type,
    string content,
    byte[] dataID,
    bool reverse)
  {
    return DLTTransform.TransDltFromStrings(type, new string[1]
    {
      content
    }, dataID, (reverse ? 1 : 0) != 0);
  }

  /// <summary>将字符串数据，转变为实际的等待写入的数据信息</summary>
  /// <param name="type">DLT类型</param>
  /// <param name="contents">字符串数据信息</param>
  /// <param name="dataID">数据ID信息</param>
  /// <param name="reverse">数据是否反转</param>
  /// <returns>原始数据信息</returns>
  public static OperateResult<byte[]> TransDltFromStrings(
    DLT645Type type,
    string[] contents,
    byte[] dataID,
    bool reverse)
  {
    try
    {
      int[] numArray = type == DLT645Type.DLT2007 ? DLTTransform.GetDLT2007FormatWithDataArea(dataID) : DLTTransform.GetDLT1997FormatWithDataArea(dataID);
      if (numArray == null)
        return OperateResult.CreateSuccessResult<byte[]>(DLTTransform.CreateFromStrings(contents, reverse));
      MemoryStream ms = new MemoryStream();
      for (int index = 0; index < contents.Length && index < numArray.Length; ++index)
      {
        byte[] bytes = BitConverter.GetBytes(numArray[index]);
        int num1 = (int) bytes[0];
        int y = (int) bytes[1];
        int num2 = (int) bytes[2];
        string s = contents[index];
        if (bytes[3] == (byte) 1)
        {
          if (reverse)
            ms.Write(((IEnumerable<byte>) Encoding.ASCII.GetBytes(s)).Reverse<byte>().ToArray<byte>());
          else
            ms.Write(((IEnumerable<byte>) Encoding.ASCII.GetBytes(s)).ToArray<byte>());
        }
        else
        {
          bool flag = false;
          if (y >= 128 /*0x80*/)
            y -= 128 /*0x80*/;
          if (y != 0)
          {
            try
            {
              s = Convert.ToInt32(Convert.ToDouble(s) * Math.Pow(10.0, (double) y)).ToString();
            }
            catch (Exception ex)
            {
              return new OperateResult<byte[]>($"{ex.Message} ID:{dataID.ToHexString('-')} Value:{s}{Environment.NewLine}{ex.StackTrace}");
            }
          }
          if (s.StartsWith("-"))
          {
            flag = true;
            s = s.Substring(1);
          }
          if (s.Length < num1 * 2)
            s = s.PadLeft(num1 * 2, '0');
          if (s.Length > num1 * 2)
            s = s.Substring(0, num1 * 2);
          byte[] buffer = reverse ? ((IEnumerable<byte>) s.ToHexBytes()).Reverse<byte>().ToArray<byte>() : ((IEnumerable<byte>) s.ToHexBytes()).ToArray<byte>();
          if (flag)
            buffer[0] = (byte) ((uint) buffer[0] & 128U /*0x80*/);
          ms.Write(buffer);
        }
      }
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"{ex.Message} ID:{dataID.ToHexString('-')}{Environment.NewLine}{ex.StackTrace}");
    }
  }

  /// <summary>从读取的原始字节里解析出实际的字符串数组</summary>
  /// <param name="type">类型信息</param>
  /// <param name="content">原始字节内容</param>
  /// <param name="dataID">地址信息</param>
  /// <param name="reverse">指示当前的结果数据是否发生数据反转的操作</param>
  /// <returns>字符串的数组内容</returns>
  public static OperateResult<string[]> TransStringsFromDLt(
    DLT645Type type,
    byte[] content,
    byte[] dataID,
    bool reverse)
  {
    List<string> stringList = new List<string>();
    try
    {
      int[] numArray = type == DLT645Type.DLT2007 ? DLTTransform.GetDLT2007FormatWithDataArea(dataID) : DLTTransform.GetDLT1997FormatWithDataArea(dataID);
      if (numArray == null)
      {
        if (reverse)
          stringList.Add(((IEnumerable<byte>) content).Reverse<byte>().ToArray<byte>().EveryByteAdd(-51).ToHexString());
        else
          stringList.Add(content.EveryByteAdd(-51).ToHexString());
        return OperateResult.CreateSuccessResult<string[]>(stringList.ToArray());
      }
      int index1 = 0;
      for (int index2 = 0; index2 < numArray.Length; ++index2)
      {
        if (index1 >= content.Length)
          return OperateResult.CreateSuccessResult<string[]>(stringList.ToArray());
        byte[] bytes = BitConverter.GetBytes(numArray[index2]);
        int length = (int) bytes[0];
        int y = (int) bytes[1];
        int num1 = (int) bytes[2];
        if (bytes[3] == (byte) 1)
        {
          if (reverse)
            stringList.Add(Encoding.ASCII.GetString(((IEnumerable<byte>) content.SelectMiddle<byte>(index1, length)).Reverse<byte>().ToArray<byte>().EveryByteAdd(-51)));
          else
            stringList.Add(Encoding.ASCII.GetString(content.SelectMiddle<byte>(index1, length).EveryByteAdd(-51)));
        }
        else
        {
          double num2 = 1.0;
          byte[] InBytes = reverse ? ((IEnumerable<byte>) content.SelectMiddle<byte>(index1, length)).Reverse<byte>().ToArray<byte>().EveryByteAdd(-51) : content.SelectMiddle<byte>(index1, length).EveryByteAdd(-51);
          if (y >= 128 /*0x80*/)
          {
            y -= 128 /*0x80*/;
            num2 = ((int) InBytes[0] & 128 /*0x80*/) == 128 /*0x80*/ ? -1.0 : 1.0;
            InBytes[0] = (byte) ((uint) InBytes[0] & (uint) sbyte.MaxValue);
          }
          string hexString = InBytes.ToHexString();
          if (y == 0)
          {
            stringList.Add(hexString);
          }
          else
          {
            try
            {
              stringList.Add((Convert.ToDouble(hexString) * num2 / Math.Pow(10.0, (double) y)).ToString());
            }
            catch (Exception ex)
            {
              return new OperateResult<string[]>($"{ex.Message} ID:{dataID.ToHexString('-')} Value:{hexString}{Environment.NewLine}{ex.StackTrace}");
            }
          }
        }
        index1 += length;
      }
      return OperateResult.CreateSuccessResult<string[]>(stringList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<string[]>($"{ex.Message} ID:{dataID.ToHexString('-')}{Environment.NewLine}{ex.StackTrace}");
    }
  }

  /// <summary>Byte[]转Dlt double[]，使用一样的 format 格式来转换多个double类型的数据</summary>
  /// <param name="content">原始的字节数据</param>
  /// <param name="length">需要转换的数据长度</param>
  /// <param name="format">当前数据的解析格式</param>
  /// <returns>结果内容</returns>
  public static OperateResult<double[]> TransDoubleFromDLt(
    byte[] content,
    ushort length,
    string format = "XXXXXX.XX")
  {
    try
    {
      format = format.ToUpper();
      int length1 = format.Count<char>((Func<char, bool>) (m => m != '.')) / 2;
      int y = format.IndexOf('.') >= 0 ? format.Length - format.IndexOf('.') - 1 : 0;
      double[] numArray = new double[(int) length];
      for (int index = 0; index < numArray.Length; ++index)
      {
        byte[] InBytes = ((IEnumerable<byte>) content.SelectMiddle<byte>(index * length1, length1)).Reverse<byte>().ToArray<byte>().EveryByteAdd(-51);
        numArray[index] = Convert.ToDouble(InBytes.ToHexString()) / Math.Pow(10.0, (double) y);
      }
      return OperateResult.CreateSuccessResult<double[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<double[]>(ex.Message);
    }
  }

  private static int GetFormat(byte byteLength, int digtal, byte length = 1, bool negativeFlag = false)
  {
    return digtal >= 0 ? BitConverter.ToInt32(new byte[4]
    {
      byteLength,
      (byte) (digtal + (negativeFlag ? 128 /*0x80*/ : 0)),
      length,
      (byte) 0
    }, 0) : BitConverter.ToInt32(new byte[4]
    {
      byteLength,
      (byte) 0,
      length,
      (byte) 1
    }, 0);
  }

  private static int[] get03_30_02()
  {
    List<int> intList = new List<int>(50);
    intList.Add(DLTTransform.GetFormat((byte) 6, 0));
    intList.Add(DLTTransform.GetFormat((byte) 4, 0));
    for (int index = 0; index < 24; ++index)
    {
      intList.Add(DLTTransform.GetFormat((byte) 3, 4));
      intList.Add(DLTTransform.GetFormat((byte) 5, 0));
    }
    return intList.ToArray();
  }

  /// <summary>根据不同的数据地址，返回实际的数据格式，然后解析出正确的数据</summary>
  /// <param name="dataArea">数据标识地址，实际的byte数组，地位在前，高位在后</param>
  /// <returns>实际的数据格式信息</returns>
  public static int[] GetDLT2007FormatWithDataArea(byte[] dataArea)
  {
    if (dataArea[3] == (byte) 0)
      return dataArea[2] == (byte) 0 ? new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 2, negativeFlag: true)
      } : (dataArea[2] == (byte) 1 ? new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 2)
      } : (dataArea[2] == (byte) 2 ? new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 2)
      } : (dataArea[2] == (byte) 3 ? new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 2, negativeFlag: true)
      } : (dataArea[2] == (byte) 4 ? new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 2, negativeFlag: true)
      } : new int[1]{ DLTTransform.GetFormat((byte) 4, 2) }))));
    if (dataArea[3] == (byte) 1)
      return dataArea[2] == (byte) 3 ? new int[2]
      {
        DLTTransform.GetFormat((byte) 3, 4, negativeFlag: true),
        DLTTransform.GetFormat((byte) 5, 0)
      } : (dataArea[2] == (byte) 4 ? new int[2]
      {
        DLTTransform.GetFormat((byte) 3, 4, negativeFlag: true),
        DLTTransform.GetFormat((byte) 5, 0)
      } : new int[2]
      {
        DLTTransform.GetFormat((byte) 3, 4),
        DLTTransform.GetFormat((byte) 5, 0)
      });
    if (dataArea[3] == (byte) 2)
    {
      if (dataArea[2] == (byte) 1)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 1)
        };
      if (dataArea[2] == (byte) 2)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 3, negativeFlag: true)
        };
      if (dataArea[2] < (byte) 6)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 4, negativeFlag: true)
        };
      if (dataArea[2] == (byte) 6)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 3, negativeFlag: true)
        };
      if (dataArea[2] == (byte) 7)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 1)
        };
      if (dataArea[2] < (byte) 128 /*0x80*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 1)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 3, negativeFlag: true)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 2)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 3)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 4)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 4)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 4, negativeFlag: true)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 5)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 4, negativeFlag: true)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 6)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 4, negativeFlag: true)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 7)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 1, negativeFlag: true)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 8)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 9)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (dataArea[2] == (byte) 128 /*0x80*/ && dataArea[0] == (byte) 10)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
    }
    if (dataArea[3] == (byte) 3)
    {
      if (dataArea[2] < (byte) 5 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 6)
        };
      if (dataArea[2] < (byte) 5)
        return new int[18]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 3, 3),
          DLTTransform.GetFormat((byte) 3, 4, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 3),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 3, 3),
          DLTTransform.GetFormat((byte) 3, 4, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 3),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 3, 3),
          DLTTransform.GetFormat((byte) 3, 4, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 3),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4)
        };
      if (dataArea[2] == (byte) 5 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 5 && dataArea[1] == (byte) 0)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 3, 3),
          DLTTransform.GetFormat((byte) 6, 0)
        };
      if (dataArea[2] == (byte) 6 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 6 && dataArea[1] == (byte) 0)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 6, 0)
        };
      if (dataArea[2] == (byte) 7 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 7 && dataArea[1] == (byte) 0)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 16 /*0x10*/)
        };
      if (dataArea[2] == (byte) 8 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 8 && dataArea[1] == (byte) 0)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 16 /*0x10*/)
        };
      if (dataArea[2] == (byte) 9 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 9 && dataArea[1] == (byte) 0)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 16 /*0x10*/)
        };
      if (dataArea[2] == (byte) 10 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 10 && dataArea[1] == (byte) 0)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 16 /*0x10*/)
        };
      if ((dataArea[2] == (byte) 11 || dataArea[2] == (byte) 12 || dataArea[2] == (byte) 13) && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 6)
        };
      if (dataArea[2] == (byte) 11 || dataArea[2] == (byte) 12 || dataArea[2] == (byte) 13)
        return new int[17]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 3, 3),
          DLTTransform.GetFormat((byte) 3, 4, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 3),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 3, 3),
          DLTTransform.GetFormat((byte) 3, 4, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 3),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 4),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 3, 3),
          DLTTransform.GetFormat((byte) 3, 4, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 3)
        };
      if (dataArea[2] == (byte) 14 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 6)
        };
      if (dataArea[2] == (byte) 14)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 16 /*0x10*/)
        };
      if (dataArea[2] == (byte) 15 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 6)
        };
      if (dataArea[2] == (byte) 15)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 16 /*0x10*/)
        };
      if (dataArea[2] == (byte) 16 /*0x10*/)
        return new int[7]
        {
          DLTTransform.GetFormat((byte) 3, 0),
          DLTTransform.GetFormat((byte) 3, 2, (byte) 2),
          DLTTransform.GetFormat((byte) 3, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 2, 1),
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] == (byte) 17 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 17 && dataArea[1] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 18 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 6)
        };
      if (dataArea[2] == (byte) 18)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 3, 4),
          DLTTransform.GetFormat((byte) 5, 0, (byte) 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 0)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 4, 0, (byte) 10)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 1)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 24)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 2 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 2)
        return DLTTransform.get03_30_02();
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 3 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 3)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 4 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 4)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 5 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 5)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 3, 0, (byte) 14)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 6 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 6)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 3, 0, (byte) 28)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 7 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 7)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 8 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 8)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 4, 0, (byte) 254)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 9 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 9)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 10 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 10)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 11 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 11)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 12 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 12)
        return new int[3]
        {
          DLTTransform.GetFormat((byte) 6, 0),
          DLTTransform.GetFormat((byte) 4, 0),
          DLTTransform.GetFormat((byte) 2, 0, (byte) 3)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 13 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 13)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
          DLTTransform.GetFormat((byte) 4, 2, (byte) 12)
        };
      if (dataArea[2] == (byte) 48 /*0x30*/ && dataArea[1] == (byte) 14 && dataArea[0] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] != (byte) 48 /*0x30*/ || dataArea[1] != (byte) 14)
        return (int[]) null;
      return new int[2]
      {
        DLTTransform.GetFormat((byte) 6, 0, (byte) 2),
        DLTTransform.GetFormat((byte) 4, 2, (byte) 12)
      };
    }
    if (dataArea[3] == (byte) 4)
    {
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 1)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 2)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 3)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 4)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 5)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 6)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 5, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1 && dataArea[0] == (byte) 7)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 5, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 2 && dataArea[0] == (byte) 5)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 2)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 3)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4 && dataArea[0] <= (byte) 2)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 6, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4 && dataArea[0] == (byte) 3)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 32 /*0x20*/, -1)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4 && dataArea[0] <= (byte) 6)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 6, -1)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4 && dataArea[0] <= (byte) 8)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, -1)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4 && dataArea[0] <= (byte) 10)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4 && dataArea[0] <= (byte) 12)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 10, -1)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4 && dataArea[0] == (byte) 13)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 16 /*0x10*/, -1)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 5)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 6)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 7)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 8)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 9)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 10 && dataArea[0] == (byte) 1)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 10)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 11)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 12)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 13)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 3)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 14 && dataArea[0] < (byte) 3)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 4)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 14)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 1)
        };
      if (dataArea[2] == (byte) 1 && dataArea[1] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 14)
        };
      if (dataArea[2] == (byte) 2 && dataArea[1] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0, (byte) 14)
        };
      if (dataArea[2] == (byte) 3 && dataArea[1] == (byte) 0)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] == (byte) 4 && dataArea[1] == (byte) 1)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] == (byte) 4 && dataArea[1] == (byte) 2)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[2] != (byte) 128 /*0x80*/)
        return (int[]) null;
      return new int[1]
      {
        DLTTransform.GetFormat((byte) 32 /*0x20*/, -1)
      };
    }
    if (dataArea[3] == (byte) 5)
    {
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 0 && dataArea[0] == (byte) 1)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 5, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 1)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 2)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 3)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 4)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 5)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 6)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 7)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 8)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 2)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 9)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 3, 4),
          DLTTransform.GetFormat((byte) 5, 0)
        };
      if (dataArea[2] == (byte) 0 && dataArea[1] == (byte) 10)
        return new int[2]
        {
          DLTTransform.GetFormat((byte) 3, 4),
          DLTTransform.GetFormat((byte) 5, 0)
        };
      if (dataArea[2] != (byte) 0 || dataArea[1] != (byte) 16 /*0x10*/)
        return (int[]) null;
      return new int[1]
      {
        DLTTransform.GetFormat((byte) 3, 4, (byte) 8)
      };
    }
    if (dataArea[3] != (byte) 6)
      return (int[]) null;
    if (dataArea[1] == (byte) 0 && dataArea[0] == (byte) 0)
      return new int[1]
      {
        DLTTransform.GetFormat((byte) 1, 0)
      };
    if (dataArea[1] == (byte) 0 && dataArea[0] == (byte) 1)
      return new int[1]
      {
        DLTTransform.GetFormat((byte) 6, 0)
      };
    if (dataArea[1] != (byte) 0 || dataArea[0] != (byte) 2)
      return (int[]) null;
    return new int[1]{ DLTTransform.GetFormat((byte) 1, 0) };
  }

  /// <summary>DLT645/1997的地址参数数据格式</summary>
  /// <param name="dataArea">地址域</param>
  /// <returns>数据信息</returns>
  public static int[] GetDLT1997FormatWithDataArea(byte[] dataArea)
  {
    if (((int) dataArea[1] & 240 /*0xF0*/) == 144 /*0x90*/)
      return new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 2)
      };
    if (((int) dataArea[1] & 240 /*0xF0*/) == 160 /*0xA0*/)
      return new int[1]
      {
        DLTTransform.GetFormat((byte) 3, 4)
      };
    if (dataArea[1] == (byte) 176 /*0xB0*/ || dataArea[1] == (byte) 177 || dataArea[1] == (byte) 180 || dataArea[1] == (byte) 181 || dataArea[1] == (byte) 184 || dataArea[1] == (byte) 185)
      return new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 0)
      };
    if (dataArea[1] == (byte) 178)
      return dataArea[0] == (byte) 16 /*0x10*/ || dataArea[0] == (byte) 17 ? new int[1]
      {
        DLTTransform.GetFormat((byte) 4, 0)
      } : (dataArea[0] == (byte) 18 || dataArea[0] == (byte) 19 ? new int[1]
      {
        DLTTransform.GetFormat((byte) 2, 0)
      } : new int[1]{ DLTTransform.GetFormat((byte) 3, 0) });
    if (dataArea[1] == (byte) 179)
    {
      if (((int) dataArea[0] & 240 /*0xF0*/) == 16 /*0x10*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (((int) dataArea[0] & 240 /*0xF0*/) == 32 /*0x20*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (((int) dataArea[0] & 240 /*0xF0*/) == 48 /*0x30*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (((int) dataArea[0] & 240 /*0xF0*/) == 64 /*0x40*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
    }
    else if (dataArea[1] == (byte) 182)
    {
      if (((int) dataArea[0] & 240 /*0xF0*/) == 16 /*0x10*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (((int) dataArea[0] & 240 /*0xF0*/) == 32 /*0x20*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (dataArea[0] >= (byte) 48 /*0x30*/ && dataArea[0] < (byte) 52)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 4)
        };
      if (dataArea[0] == (byte) 52)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (dataArea[0] == (byte) 53)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (((int) dataArea[0] & 240 /*0xF0*/) == 64 /*0x40*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
      if (((int) dataArea[0] & 240 /*0xF0*/) == 80 /*0x50*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 2)
        };
    }
    else if (dataArea[1] == (byte) 192 /*0xC0*/)
    {
      if (dataArea[0] == (byte) 16 /*0x10*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
      if (dataArea[0] == (byte) 17)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (((int) dataArea[0] & 240 /*0xF0*/) == 32 /*0x20*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 48 /*0x30*/)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[0] == (byte) 49)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[0] == (byte) 50)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 6, 0)
        };
      if (dataArea[0] == (byte) 51)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 6, 0)
        };
      if (dataArea[0] == (byte) 52)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 6, 0)
        };
    }
    else if (dataArea[1] == (byte) 193)
    {
      if (dataArea[0] == (byte) 17)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 18)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 19)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 20)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 21)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 22)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 23)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (dataArea[0] == (byte) 24)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        };
      if (dataArea[0] == (byte) 25)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 1)
        };
      if (dataArea[0] == (byte) 26)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 1)
        };
    }
    else if (dataArea[1] == (byte) 194)
    {
      if (dataArea[0] == (byte) 17)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
      if (dataArea[0] == (byte) 18)
        return new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        };
    }
    else
    {
      if (dataArea[1] == (byte) 195)
        return ((int) dataArea[0] & 240 /*0xF0*/) == 16 /*0x10*/ ? new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        } : new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[1] == (byte) 196)
        return dataArea[0] == (byte) 30 ? new int[1]
        {
          DLTTransform.GetFormat((byte) 1, 0)
        } : new int[1]
        {
          DLTTransform.GetFormat((byte) 3, 0)
        };
      if (dataArea[1] == (byte) 197)
        return dataArea[0] == (byte) 16 /*0x10*/ ? new int[1]
        {
          DLTTransform.GetFormat((byte) 4, 0)
        } : new int[1]
        {
          DLTTransform.GetFormat((byte) 2, 0)
        };
    }
    return new int[1]{ DLTTransform.GetFormat((byte) 3, 0) };
  }
}
