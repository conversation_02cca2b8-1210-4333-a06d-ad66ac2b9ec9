﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Keyence.KeyencePLCS
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Keyence;

/// <summary>基恩士PLC的各种系列选择</summary>
public enum KeyencePLCS
{
  /// <summary>KV5500系列</summary>
  KV5500,
  /// <summary>KV5000系列</summary>
  KV5000,
  /// <summary>KV3000系列</summary>
  KV3000,
  /// <summary>KV1000系列</summary>
  KV1000,
  /// <summary>KV700系列</summary>
  KV700,
}
