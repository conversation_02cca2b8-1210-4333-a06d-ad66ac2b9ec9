﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Vigor.VigorServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.Vigor.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace HslCommunication.Profinet.Vigor;

/// <summary>
/// 丰炜的虚拟PLC，模拟了VS系列的通信，可以和对应的客户端进行数据读写测试，位地址支持 X,Y,M,S，字地址支持 D,R,SD
/// </summary>
public class VigorServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer sBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer rBuffer;
  private SoftBuffer sdBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private int station = 0;

  /// <summary>实例化一个丰炜PLC的网口和串口服务器，支持数据读写操作</summary>
  public VigorServer()
  {
    this.xBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.yBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.sBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.sdBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new VigorSerialMessage();

  /// <inheritdoc cref="P:HslCommunication.Profinet.Vigor.VigorSerial.Station" />
  public int Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] bytes = new byte[655360 /*0x0A0000*/];
    this.xBuffer.GetBytes().CopyTo((Array) bytes, 0);
    this.yBuffer.GetBytes().CopyTo((Array) bytes, 65536 /*0x010000*/);
    this.mBuffer.GetBytes().CopyTo((Array) bytes, 131072 /*0x020000*/);
    this.sBuffer.GetBytes().CopyTo((Array) bytes, 196608 /*0x030000*/);
    this.dBuffer.GetBytes().CopyTo((Array) bytes, 262144 /*0x040000*/);
    this.rBuffer.GetBytes().CopyTo((Array) bytes, 393216 /*0x060000*/);
    this.sdBuffer.GetBytes().CopyTo((Array) bytes, 524288 /*0x080000*/);
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 655360 /*0x0A0000*/)
      throw new Exception("File is not correct");
    this.xBuffer.SetBytes(content, 0, 65536 /*0x010000*/);
    this.yBuffer.SetBytes(content, 65536 /*0x010000*/, 65536 /*0x010000*/);
    this.mBuffer.SetBytes(content, 131072 /*0x020000*/, 65536 /*0x010000*/);
    this.sBuffer.SetBytes(content, 196608 /*0x030000*/, 65536 /*0x010000*/);
    this.dBuffer.SetBytes(content, 262144 /*0x040000*/, 131072 /*0x020000*/);
    this.rBuffer.SetBytes(content, 393216 /*0x060000*/, 131072 /*0x020000*/);
    this.sdBuffer.SetBytes(content, 524288 /*0x080000*/, 131072 /*0x020000*/);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.VigorSerial.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = new OperateResult<byte[]>();
    try
    {
      if (address.StartsWith("SD") || address.StartsWith("sd"))
        return OperateResult.CreateSuccessResult<byte[]>(this.sdBuffer.GetBytes(Convert.ToInt32(address.Substring(2)) * 2, (int) length * 2));
      if (address.StartsWith("D") || address.StartsWith("d"))
        return OperateResult.CreateSuccessResult<byte[]>(this.dBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
      if (address.StartsWith("R") || address.StartsWith("r"))
        return OperateResult.CreateSuccessResult<byte[]>(this.rBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
      throw new Exception(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      operateResult.Message = ex.Message;
      return operateResult;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.VigorSerial.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = new OperateResult<byte[]>();
    try
    {
      if (address.StartsWith("SD") || address.StartsWith("sd"))
      {
        this.sdBuffer.SetBytes(value, Convert.ToInt32(address.Substring(2)) * 2);
        return OperateResult.CreateSuccessResult();
      }
      if (address.StartsWith("D") || address.StartsWith("d"))
      {
        this.dBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
        return OperateResult.CreateSuccessResult();
      }
      if (!address.StartsWith("R") && !address.StartsWith("r"))
        throw new Exception(StringResources.Language.NotSupportedDataType);
      this.rBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      operateResult.Message = ex.Message;
      return (OperateResult) operateResult;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.VigorSerial.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    try
    {
      int int32 = Convert.ToInt32(address.Substring(1));
      switch (address[0])
      {
        case 'M':
        case 'm':
          return OperateResult.CreateSuccessResult<bool[]>(this.mBuffer.GetBool(int32, (int) length));
        case 'S':
        case 's':
          return OperateResult.CreateSuccessResult<bool[]>(this.sBuffer.GetBool(int32, (int) length));
        case 'X':
        case 'x':
          return OperateResult.CreateSuccessResult<bool[]>(this.xBuffer.GetBool(int32, (int) length));
        case 'Y':
        case 'y':
          return OperateResult.CreateSuccessResult<bool[]>(this.yBuffer.GetBool(int32, (int) length));
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceCommunication.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    try
    {
      int int32 = Convert.ToInt32(address.Substring(1));
      switch (address[0])
      {
        case 'M':
        case 'm':
          this.mBuffer.SetBool(value, int32);
          return OperateResult.CreateSuccessResult();
        case 'S':
        case 's':
          this.sBuffer.SetBool(value, int32);
          return OperateResult.CreateSuccessResult();
        case 'X':
        case 'x':
          this.xBuffer.SetBool(value, int32);
          return OperateResult.CreateSuccessResult();
        case 'Y':
        case 'y':
          this.yBuffer.SetBool(value, int32);
          return OperateResult.CreateSuccessResult();
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<bool[]>(ex.Message);
    }
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive[0] != (byte) 16 /*0x10*/ || receive[1] != (byte) 2)
      return new OperateResult<byte[]>("start message must be 0x10, 0x02");
    return (int) receive[2] != this.station ? new OperateResult<byte[]>($"Station not match , Except: {this.station:X2} , Actual: {receive[2]}") : OperateResult.CreateSuccessResult<byte[]>(this.ReadFromVigorCore(VigorVsHelper.UnPackCommand(receive)));
  }

  private byte[] CreateResponseBack(byte[] request, byte err, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    byte[] command = new byte[4 + data.Length];
    command[0] = request[2];
    command[1] = BitConverter.GetBytes(1 + data.Length)[0];
    command[2] = BitConverter.GetBytes(1 + data.Length)[1];
    command[3] = err;
    if (data.Length != 0)
      data.CopyTo((Array) command, 4);
    return VigorVsHelper.PackCommand(command, (byte) 6);
  }

  private byte[] ReadFromVigorCore(byte[] receive)
  {
    if (receive.Length < 16 /*0x10*/)
      return (byte[]) null;
    if (receive[5] == (byte) 32 /*0x20*/)
      return this.ReadWordByCommand(receive);
    if (receive[5] == (byte) 33)
      return this.ReadBoolByCommand(receive);
    if (receive[5] == (byte) 40)
      return this.WriteWordByCommand(receive);
    return receive[5] == (byte) 41 ? this.WriteBoolByCommand(receive) : this.CreateResponseBack(receive, (byte) 49, (byte[]) null);
  }

  private byte[] ReadWordByCommand(byte[] command)
  {
    int int32 = Convert.ToInt32(((IEnumerable<byte>) command.SelectMiddle<byte>(7, 3)).Reverse<byte>().ToArray<byte>().ToHexString());
    int num = (int) this.ByteTransform.TransUInt16(command, 10);
    switch (command[6])
    {
      case 160 /*0xA0*/:
        return this.CreateResponseBack(command, (byte) 0, this.dBuffer.GetBytes(int32 * 2, num * 2));
      case 161:
        return this.CreateResponseBack(command, (byte) 0, this.sdBuffer.GetBytes(int32 * 2, num * 2));
      case 162:
        return this.CreateResponseBack(command, (byte) 0, this.rBuffer.GetBytes(int32 * 2, num * 2));
      default:
        return this.CreateResponseBack(command, (byte) 49, (byte[]) null);
    }
  }

  private byte[] ReadBoolByCommand(byte[] command)
  {
    string hexString = ((IEnumerable<byte>) command.SelectMiddle<byte>(7, 3)).Reverse<byte>().ToArray<byte>().ToHexString();
    int length = (int) this.ByteTransform.TransUInt16(command, 10);
    switch (command[6])
    {
      case 144 /*0x90*/:
        return this.CreateResponseBack(command, (byte) 0, this.xBuffer.GetBool(Convert.ToInt32(hexString, 8), length).ToByteArray());
      case 145:
        return this.CreateResponseBack(command, (byte) 0, this.yBuffer.GetBool(Convert.ToInt32(hexString, 8), length).ToByteArray());
      case 146:
        return this.CreateResponseBack(command, (byte) 0, this.mBuffer.GetBool(Convert.ToInt32(hexString), length).ToByteArray());
      case 147:
        return this.CreateResponseBack(command, (byte) 0, this.sBuffer.GetBool(Convert.ToInt32(hexString), length).ToByteArray());
      default:
        return this.CreateResponseBack(command, (byte) 49, (byte[]) null);
    }
  }

  private byte[] WriteWordByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.CreateResponseBack(command, (byte) 49, (byte[]) null);
    int int32 = Convert.ToInt32(((IEnumerable<byte>) command.SelectMiddle<byte>(7, 3)).Reverse<byte>().ToArray<byte>().ToHexString());
    int length = (int) this.ByteTransform.TransUInt16(command, 3) - 7;
    byte[] data = command.SelectMiddle<byte>(12, length);
    switch (command[6])
    {
      case 160 /*0xA0*/:
        this.dBuffer.SetBytes(data, int32 * 2);
        return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
      case 161:
        this.sdBuffer.SetBytes(data, int32 * 2);
        return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
      case 162:
        this.rBuffer.SetBytes(data, int32 * 2);
        return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
      default:
        return this.CreateResponseBack(command, (byte) 49, (byte[]) null);
    }
  }

  private byte[] WriteBoolByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.CreateResponseBack(command, (byte) 49, (byte[]) null);
    string hexString = ((IEnumerable<byte>) command.SelectMiddle<byte>(7, 3)).Reverse<byte>().ToArray<byte>().ToHexString();
    int length1 = (int) this.ByteTransform.TransUInt16(command, 3) - 7;
    int length2 = (int) this.ByteTransform.TransUInt16(command, 10);
    bool[] flagArray = command.SelectMiddle<byte>(12, length1).ToBoolArray().SelectBegin<bool>(length2);
    switch (command[6])
    {
      case 144 /*0x90*/:
        this.xBuffer.SetBool(flagArray, Convert.ToInt32(hexString, 8));
        return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
      case 145:
        this.yBuffer.SetBool(flagArray, Convert.ToInt32(hexString, 8));
        return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
      case 146:
        this.mBuffer.SetBool(flagArray, Convert.ToInt32(hexString));
        return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
      case 147:
        this.sBuffer.SetBool(flagArray, Convert.ToInt32(hexString));
        return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
      default:
        return this.CreateResponseBack(command, (byte) 49, (byte[]) null);
    }
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return VigorVsHelper.CheckReceiveDataComplete(buffer, receivedLength);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.xBuffer.Dispose();
      this.yBuffer.Dispose();
      this.mBuffer.Dispose();
      this.sBuffer.Dispose();
      this.dBuffer.Dispose();
      this.rBuffer.Dispose();
      this.sdBuffer.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"VigorServer[{this.Port}]";
}
