﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.KukaVarProxyMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>Kuka机器人的 KRC4 控制器中的服务器KUKAVARPROXY</summary>
public class KukaVarProxyMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 4;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    byte[] headBytes = this.HeadBytes;
    return headBytes != null && headBytes.Length >= 4 ? (int) this.HeadBytes[2] * 256 /*0x0100*/ + (int) this.HeadBytes[3] : 0;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetHeadBytesIdentity" />
  public override int GetHeadBytesIdentity()
  {
    byte[] headBytes = this.HeadBytes;
    return headBytes != null && headBytes.Length >= 4 ? (int) this.HeadBytes[0] * 256 /*0x0100*/ + (int) this.HeadBytes[1] : 0;
  }
}
