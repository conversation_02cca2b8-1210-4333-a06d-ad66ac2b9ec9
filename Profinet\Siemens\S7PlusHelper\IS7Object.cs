﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.S7PlusHelper.IS7Object
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.IO;

#nullable disable
namespace HslCommunication.Profinet.Siemens.S7PlusHelper;

/// <summary>S7对象信息</summary>
public interface IS7Object
{
  /// <summary>将对象写入报文消息的方法</summary>
  /// <param name="ms">消息报文的数据流</param>
  void WriteMessgae(MemoryStream ms);

  /// <summary>获取字段的数量</summary>
  /// <returns></returns>
  int GetNumberOfFields();
}
