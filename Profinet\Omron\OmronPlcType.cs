﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronPlcType
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>欧姆龙PLC的类型</summary>
public enum OmronPlcType
{
  /// <summary>CS/CJ系列</summary>
  CSCJ = 1,
  /// <summary>CV系列</summary>
  CV = 2,
}
