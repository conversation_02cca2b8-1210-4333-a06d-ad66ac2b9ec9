﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.IEC.IEC104Server
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Instrument.IEC.Helper;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Instrument.IEC;

/// <summary>IEC104的虚拟服务器，支持总召唤操作</summary>
public class IEC104Server : DeviceServer
{
  private IecValueServerObject<byte> list_单点遥信 = new IecValueServerObject<byte>((ushort) 0, 100, (byte) 1, (Func<IecValueObject<byte>, byte[]>) (m => new byte[1]
  {
    (byte) ((uint) m.Quality | (uint) m.Value)
  }));
  private IecValueServerObject<byte> list_双点遥信 = new IecValueServerObject<byte>((ushort) 0, 100, (byte) 3, (Func<IecValueObject<byte>, byte[]>) (m => new byte[1]
  {
    (byte) ((uint) m.Quality | (uint) m.Value)
  }));
  private IecValueServerObject<short> list_归一化遥测值 = new IecValueServerObject<short>((ushort) 0, 100, (byte) 9, (Func<IecValueObject<short>, byte[]>) (m => BitConverter.GetBytes(m.Value)));
  private IecValueServerObject<short> list_标度化遥测值 = new IecValueServerObject<short>((ushort) 0, 100, (byte) 11, (Func<IecValueObject<short>, byte[]>) (m => BitConverter.GetBytes(m.Value)));
  private IecValueServerObject<float> list_短浮点遥测值 = new IecValueServerObject<float>((ushort) 0, 100, (byte) 13, (Func<IecValueObject<float>, byte[]>) (m => BitConverter.GetBytes(m.Value)));
  private IecValueServerObject<uint> list_比特串 = new IecValueServerObject<uint>((ushort) 0, 100, (byte) 7, (Func<IecValueObject<uint>, byte[]>) (m => BitConverter.GetBytes(m.Value)));

  /// <summary>实例化一个默认的对象</summary>
  public IEC104Server()
  {
    this.Port = 2404;
    this.list_单点遥信.OnIecValueChanged = new Action<IecValueServerObject<byte>, IecValueObject<byte>>(this.IecValueChangedHelper<byte>);
    this.list_双点遥信.OnIecValueChanged = new Action<IecValueServerObject<byte>, IecValueObject<byte>>(this.IecValueChangedHelper<byte>);
    this.list_归一化遥测值.OnIecValueChanged = new Action<IecValueServerObject<short>, IecValueObject<short>>(this.IecValueChangedHelper<short>);
    this.list_标度化遥测值.OnIecValueChanged = new Action<IecValueServerObject<short>, IecValueObject<short>>(this.IecValueChangedHelper<short>);
    this.list_短浮点遥测值.OnIecValueChanged = new Action<IecValueServerObject<float>, IecValueObject<float>>(this.IecValueChangedHelper<float>);
    this.list_比特串.OnIecValueChanged = new Action<IecValueServerObject<uint>, IecValueObject<uint>>(this.IecValueChangedHelper<uint>);
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new IEC104Message();

  /// <summary>获取或设置默认的数据单元公共地址，默认值为 1</summary>
  public byte PubAddress { get; set; } = 1;

  /// <summary>单点遥信，类型ID为: M_SP_NA_1</summary>
  public IecValueServerObject<byte> SingleYaoXin => this.list_单点遥信;

  /// <summary>双点遥信，类型ID为: M_DP_NA_1</summary>
  public IecValueServerObject<byte> DoubleYaoXin => this.list_双点遥信;

  /// <summary>归一化遥测值，类型ID: M_ME_NA_1</summary>
  public IecValueServerObject<short> YaoCeA => this.list_归一化遥测值;

  /// <summary>标度化遥测值，类型ID: M_ME_NB_1</summary>
  public IecValueServerObject<short> YaoCeB => this.list_标度化遥测值;

  /// <summary>短浮点遥测值，类型ID: M_ME_NC_1</summary>
  public IecValueServerObject<float> YaoCeC => this.list_短浮点遥测值;

  /// <summary>32位比特串，类型ID: M_BO_NA_1</summary>
  public IecValueServerObject<uint> BitArray => this.list_比特串;

  private void IecValueChangedHelper<T>(
    IecValueServerObject<T> iecValueServer,
    IecValueObject<T> iecValue)
  {
    foreach (PipeSession pipeSession in this.GetCommunicationServer().GetPipeSessions())
    {
      IECSessionInfo tag = pipeSession.Tag as IECSessionInfo;
      this.SendAsdu(pipeSession, tag, iecValueServer.GetAsduBreakOut(iecValue, this.PubAddress));
    }
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive == null || receive.Length < 6)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (receive[0] != (byte) 104)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (session.Tag == null)
      session.Tag = (object) new IECSessionInfo();
    if (!receive[2].GetBoolByIndex(0) && !receive[4].GetBoolByIndex(0))
    {
      IECSessionInfo tag = session.Tag as IECSessionInfo;
      tag.IncrRecvMessageID();
      if (receive[6] == (byte) 100 && receive[8] == (byte) 6)
      {
        this.SendAsdu(session, tag, "64 01 07 00 01 00 00 00 00 14");
        this.SendAdsu<byte>(session, tag, this.list_单点遥信, (byte) 20, this.PubAddress);
        this.SendAdsu<byte>(session, tag, this.list_双点遥信, (byte) 20, this.PubAddress);
        this.SendAdsu<short>(session, tag, this.list_归一化遥测值, (byte) 20, this.PubAddress);
        this.SendAdsu<short>(session, tag, this.list_标度化遥测值, (byte) 20, this.PubAddress);
        this.SendAdsu<float>(session, tag, this.list_短浮点遥测值, (byte) 20, this.PubAddress);
        this.SendAdsu<uint>(session, tag, this.list_比特串, (byte) 20, this.PubAddress);
        this.SendAsdu(session, tag, "64 01 0A 00 01 00 00 00 00 14");
      }
    }
    else if (receive[2].GetBoolByIndex(0) && !receive[4].GetBoolByIndex(0))
    {
      if (!receive[2].GetBoolByIndex(1))
        return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      if (receive[2] == (byte) 7)
        return OperateResult.CreateSuccessResult<byte[]>(IECHelper.PackIEC104Message((byte) 11, (byte) 0, (byte) 0, (byte) 0, (byte[]) null));
      if (receive[2] == (byte) 19)
        return OperateResult.CreateSuccessResult<byte[]>(IECHelper.PackIEC104Message((byte) 35, (byte) 0, (byte) 0, (byte) 0, (byte[]) null));
      if (receive[2] == (byte) 67)
        return OperateResult.CreateSuccessResult<byte[]>(IECHelper.PackIEC104Message((byte) 131, (byte) 0, (byte) 0, (byte) 0, (byte[]) null));
    }
    return base.ReadFromCoreServer(session, receive);
  }

  private void SendAdsu<T>(
    PipeSession session,
    IECSessionInfo sessionInfo,
    IecValueServerObject<T> value,
    byte reason,
    byte station)
  {
    List<byte[]> asduCommand = value.GetAsduCommand(reason, station);
    for (int index = 0; index < asduCommand.Count; ++index)
      this.SendAsdu(session, sessionInfo, asduCommand[index]);
  }

  private void SendAsdu(PipeSession session, IECSessionInfo sessionInfo, string asdu)
  {
    this.SendAsdu(session, sessionInfo, asdu.ToHexBytes());
  }

  private void SendAsdu(PipeSession session, IECSessionInfo sessionInfo, byte[] asdu)
  {
    byte[] numArray = IECHelper.PackIEC104Message((ushort) (sessionInfo.GetSendMessageID() * 2), (ushort) (sessionInfo.RecvMessageID * 2), asdu);
    session.Communication.Send(numArray);
    this.LogSendMessage(numArray, session);
  }

  /// <inheritdoc />
  public override string ToString() => $"IEC104Server[{this.Port}]";
}
