﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.ISqlDataType
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Data.SqlClient;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>数据库对应类的读取接口</summary>
public interface ISqlDataType
{
  /// <summary>根据sdr对象初始化数据的方法</summary>
  /// <param name="sdr">数据库reader对象</param>
  void LoadBySqlDataReader(SqlDataReader sdr);
}
