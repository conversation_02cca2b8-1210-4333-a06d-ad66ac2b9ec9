﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronHostLink
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Omron.Helper;
using HslCommunication.Reflection;
using System.IO.Ports;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// 欧姆龙的HostLink协议的实现，地址支持示例 DM区:D100; CIO区:C100; Work区:W100; Holding区:H100; Auxiliary区: A100<br />
/// Implementation of Omron's HostLink protocol, address support example DM area: D100; CIO area: C100; Work area: W100; Holding area: H100; Auxiliary area: A100
/// </summary>
/// <remarks>
/// 感谢 深圳～拾忆 的测试，地址可以携带站号信息，例如 s=2;D100
/// <br />
/// <note type="important">
/// 如果发现串口线和usb同时打开才能通信的情况，需要按照如下的操作：<br />
/// 串口线不是标准的串口线，电脑的串口线的235引脚分别接PLC的329引脚，45线短接，就可以通讯，感谢 深圳-小君(QQ932507362)提供的解决方案。
/// </note>
/// </remarks>
/// <example>
/// <inheritdoc cref="T:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp" path="example" />
/// </example>
public class OmronHostLink : DeviceSerialPort, IHostLink, IReadWriteDevice, IReadWriteNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.#ctor" />
  public OmronHostLink()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.WordLength = (ushort) 1;
    this.ByteTransform.IsStringReverseByteWord = true;
    this.LogMsgFormatBinary = false;
    this.ReceiveEmptyDataCount = 5;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.ICF" />
  public byte ICF { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.DA2" />
  public byte DA2 { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.SA2" />
  public byte SA2 { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.SID" />
  public byte SID { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.ResponseWaitTime" />
  public byte ResponseWaitTime { get; set; } = 48 /*0x30*/;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.UnitNumber" />
  public byte UnitNumber { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.ReadSplits" />
  public int ReadSplits { get; set; } = 260;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.Helper.IOmronFins.PlcType" />
  public OmronPlcType PlcType { get; set; } = OmronPlcType.CSCJ;

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return OmronHostLinkHelper.ResponseValidAnalysis(send, response);
  }

  /// <summary>
  /// 初始化串口信息，9600波特率，7位数据位，1位停止位，偶校验<br />
  /// Initial serial port information, 9600 baud rate, 7 data bits, 1 stop bit, even parity
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  public override void SerialPortInni(string portName) => base.SerialPortInni(portName);

  /// <summary>
  /// 初始化串口信息，波特率，7位数据位，1位停止位，偶校验<br />
  /// Initializes serial port information, baud rate, 7-bit data bit, 1-bit stop bit, even parity
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  /// <param name="baudRate">波特率</param>
  public override void SerialPortInni(string portName, int baudRate)
  {
    this.SerialPortInni(portName, baudRate, 7, StopBits.One, Parity.Even);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return OmronHostLinkHelper.Read((IHostLink) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return OmronHostLinkHelper.Write((IHostLink) this, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.Helper.OmronHostLinkHelper.Read(HslCommunication.Profinet.Omron.Helper.IHostLink,System.String[])" />
  public OperateResult<byte[]> Read(string[] address)
  {
    return OmronHostLinkHelper.Read((IHostLink) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return OmronHostLinkHelper.ReadBool((IHostLink) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    return OmronHostLinkHelper.Write((IHostLink) this, address, values);
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronHostLink[{this.PortName}:{this.BaudRate}]";
}
