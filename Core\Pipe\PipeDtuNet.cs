﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeDtuNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// DTU(数据转换模块)的管道信息<br />
/// Pipeline information of the DTU (Data Transfer unit)
/// </summary>
public class PipeDtuNet : PipeTcpNet
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public PipeDtuNet()
  {
  }

  /// <summary>
  /// 根据传入的TCP管道来初始化新的DTU管道实例<br />
  /// Initialize the new DTU pipe instance based on the incoming TCP pipe
  /// </summary>
  /// <param name="pipeTcpNet">TCP管道</param>
  public PipeDtuNet(PipeTcpNet pipeTcpNet)
  {
    this.Socket = pipeTcpNet.Socket;
    this.ReceiveTimeOut = pipeTcpNet.ReceiveTimeOut;
    this.IpAddress = pipeTcpNet.IpAddress;
    this.Port = pipeTcpNet.Port;
  }

  /// <inheritdoc />
  public override OperateResult<bool> OpenCommunication()
  {
    if (!this.IsConnectError())
      return OperateResult.CreateSuccessResult<bool>(false);
    NetSupport.CloseSocket(this.Socket);
    return new OperateResult<bool>(StringResources.Language.ConnectionIsNotAvailable);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool>> OpenCommunicationAsync()
  {
    if (this.IsConnectError())
    {
      OperateResult<bool> operateResult = await Task.FromResult<OperateResult<bool>>(new OperateResult<bool>(StringResources.Language.ConnectionIsNotAvailable)).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<bool> operateResult1 = await Task.FromResult<OperateResult<bool>>(OperateResult.CreateSuccessResult<bool>(false)).ConfigureAwait(false);
    return operateResult1;
  }

  /// <summary>
  /// 唯一的标识<br />
  /// Unique identification
  /// </summary>
  public string DTU { get; set; }

  /// <summary>
  /// 注册报文里的端口号信息，实际的使用过程中，你可以用来做一些额外的标记<br />
  /// You can use the port number information in the registration packet to make some other markings during actual use
  /// </summary>
  public ushort DTUPort { get; set; }

  /// <summary>
  /// 注册报文里的IP地址信息，实际的使用过程中，你也可以用来做一些额外的标记<br />
  /// You can also use the IP address information in the registration packet to make some other markings during actual use
  /// </summary>
  public int DTUIpAddress { get; set; }

  /// <summary>
  /// 密码信息<br />
  /// Password information
  /// </summary>
  public string Pwd { get; set; }

  /// <summary>当前的DTU会话关联的服务器信息</summary>
  public NetworkAlienClient DtuServer { get; set; }

  /// <inheritdoc />
  public override string ToString() => $"PipeDtuNet[{this.IpAddress}:{this.Port}-{this.DTU}]";
}
