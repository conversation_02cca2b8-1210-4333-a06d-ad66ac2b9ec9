﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Types.OnlineData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Secs.Types;

/// <summary>在线数据信息</summary>
public class OnlineData
{
  /// <summary>实例化一个默认的对象</summary>
  public OnlineData()
  {
  }

  /// <summary>指定类型及其版本号来实例化一个对象</summary>
  /// <param name="model">类型信息</param>
  /// <param name="version">版本号</param>
  public OnlineData(string model, string version)
  {
    this.ModelType = model;
    this.SoftVersion = version;
  }

  /// <summary>equipment model type</summary>
  public string ModelType { get; set; }

  /// <summary>software revision</summary>
  public string SoftVersion { get; set; }

  /// <summary>
  /// 赋值操作，可以直接赋值 <see cref="T:HslCommunication.Secs.Types.OnlineData" /> 数据
  /// </summary>
  /// <param name="value"><see cref="T:HslCommunication.Secs.Types.SecsValue" /> 数值</param>
  /// <returns>等值的消息对象</returns>
  public static implicit operator OnlineData(SecsValue value)
  {
    TypeHelper.TypeListCheck(value);
    return value.Value is SecsValue[] secsValueArray ? new OnlineData(secsValueArray[0].Value.ToString(), secsValueArray[1].Value.ToString()) : (OnlineData) null;
  }

  /// <summary>
  /// 也可以赋值给<see cref="T:HslCommunication.Secs.Types.SecsValue" /> 数据
  /// </summary>
  /// <param name="value"><see cref="T:HslCommunication.Secs.Types.SecsValue" /> 对象</param>
  /// <returns>等值的消息对象</returns>
  public static implicit operator SecsValue(OnlineData value)
  {
    return new SecsValue((IEnumerable<object>) new object[2]
    {
      (object) value.ModelType,
      (object) value.SoftVersion
    });
  }
}
