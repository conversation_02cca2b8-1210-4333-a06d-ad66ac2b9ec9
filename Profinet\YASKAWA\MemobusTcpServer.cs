﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.YASKAWA.MemobusTcpServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.YASKAWA.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace HslCommunication.Profinet.YASKAWA;

/// <summary>
/// 基于扩展memobus协议的虚拟服务器，支持线圈，输入线圈，保持型寄存器，输入寄存器，保持型寄存器（扩展），输入寄存器（扩展）的读写。对于远程客户端来说，还支持对扩展保持寄存器的随机字读取和写入操作。<br />
/// A virtual server based on the extended memobus protocol, which supports the reading and writing of coils, input coils, holding registers, input registers,
/// holding registers (expansion), input registers (expansion). For remote clients, random word reads and writes to extended hold registers are also supported.
/// </summary>
/// <remarks>
/// 支持的功能码为 01 02 03 04 05 06 08 09 0A 0B 0D 0E 0F 10, 访问方式分为位读写和字读写，位读写：线圈：100; 输入线圈：x=2;100<br />
/// 字读写时，保持型寄存器：100; 输入寄存器：x=4;100  保持型寄存器（扩展）: x=9;100   输入寄存器（扩展）: x=10;100
/// </remarks>
public class MemobusTcpServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer inputBuffer;
  private SoftBuffer rBuffer;
  private SoftBuffer rExtBuffer;
  private SoftBuffer inputExtBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个memobus协议的PLC的服务器，支持线圈，输入线圈，保持型寄存器，输入寄存器，保持型寄存器（扩展），输入寄存器（扩展）的读写。
  /// </summary>
  public MemobusTcpServer()
  {
    this.xBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.yBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.inputBuffer = new SoftBuffer(131072 /*0x020000*/)
    {
      IsBoolReverseByWord = true
    };
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/)
    {
      IsBoolReverseByWord = true
    };
    this.inputExtBuffer = new SoftBuffer(131072 /*0x020000*/)
    {
      IsBoolReverseByWord = true
    };
    this.rExtBuffer = new SoftBuffer(131072 /*0x020000*/)
    {
      IsBoolReverseByWord = true
    };
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
  }

  private OperateResult<SoftBuffer> GetDataAreaFromYokogawaAddress(
    MemobusAddress address,
    bool isBit)
  {
    switch (address.SFC)
    {
      case 1:
      case 5:
      case 15:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.yBuffer);
      case 2:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.xBuffer);
      case 3:
      case 6:
      case 16 /*0x10*/:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.rBuffer);
      case 4:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.inputBuffer);
      case 9:
      case 11:
      case 13:
      case 14:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.rExtBuffer);
      case 10:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.inputExtBuffer);
      default:
        return new OperateResult<SoftBuffer>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.MemobusTcpNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<MemobusAddress> from = MemobusAddress.ParseFrom(address, false);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, false);
    return !fromYokogawaAddress.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) fromYokogawaAddress) : OperateResult.CreateSuccessResult<byte[]>(fromYokogawaAddress.Content.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.MemobusTcpNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<MemobusAddress> from = MemobusAddress.ParseFrom(address, false);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, false);
    if (!fromYokogawaAddress.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) fromYokogawaAddress);
    fromYokogawaAddress.Content.SetBytes(value, from.Content.AddressStart * 2);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.MemobusTcpNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<MemobusAddress> from = MemobusAddress.ParseFrom(address, true);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, true);
    if (!fromYokogawaAddress.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) fromYokogawaAddress);
    return from.Content.SFC == (byte) 3 || from.Content.SFC == (byte) 4 ? OperateResult.CreateSuccessResult<bool[]>(fromYokogawaAddress.Content.GetBool(from.Content.AddressStart, (int) length)) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) fromYokogawaAddress.Content.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.YASKAWA.MemobusTcpNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<MemobusAddress> from = MemobusAddress.ParseFrom(address, true);
    if (!from.IsSuccess)
      return (OperateResult) from;
    OperateResult<SoftBuffer> fromYokogawaAddress = this.GetDataAreaFromYokogawaAddress(from.Content, true);
    if (!fromYokogawaAddress.IsSuccess)
      return (OperateResult) fromYokogawaAddress;
    if (from.Content.SFC == (byte) 3 || from.Content.SFC == (byte) 4)
      fromYokogawaAddress.Content.SetBool(value, from.Content.AddressStart);
    else
      fromYokogawaAddress.Content.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), from.Content.AddressStart);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new MemobusMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return OperateResult.CreateSuccessResult<byte[]>(receive[15] != (byte) 1 && receive[15] != (byte) 2 && receive[15] != (byte) 65 ? (receive[15] != (byte) 5 && receive[15] != (byte) 15 && receive[15] != (byte) 79 ? (receive[15] != (byte) 3 && receive[15] != (byte) 4 && receive[15] != (byte) 9 && receive[15] != (byte) 10 && receive[15] != (byte) 73 ? (receive[15] != (byte) 6 && receive[15] != (byte) 16 /*0x10*/ && receive[15] != (byte) 11 && receive[15] != (byte) 75 ? (receive[15] != (byte) 13 && receive[15] != (byte) 77 ? (receive[15] != (byte) 14 ? (receive[15] != (byte) 8 ? this.PackCommandBack(receive, (byte) 1, (byte[]) null) : this.PackCommandBack(receive, (byte) 0, (byte[]) null)) : this.WriteRandomWordByCommand(receive)) : this.ReadRandomWordByCommand(receive)) : this.WriteWordByCommand(receive)) : this.ReadWordByCommand(receive)) : this.WriteBoolByCommand(receive)) : this.ReadBoolByCommand(receive));
  }

  private byte[] ReadBoolByCommand(byte[] command)
  {
    if (command[15] == (byte) 1 || command[15] == (byte) 2)
    {
      int index = (int) this.ByteTransform.TransUInt16(command, 17);
      int length = (int) this.ByteTransform.TransUInt16(command, 19);
      if (index + length > (int) ushort.MaxValue)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      switch (command[15])
      {
        case 1:
          return this.PackCommandBack(command, (byte) 0, ((IEnumerable<byte>) this.yBuffer.GetBytes(index, length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
        case 2:
          return this.PackCommandBack(command, (byte) 0, ((IEnumerable<byte>) this.xBuffer.GetBytes(index, length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
        default:
          return this.PackCommandBack(command, (byte) 1, (byte[]) null);
      }
    }
    else
    {
      if (command[15] != (byte) 65)
        return this.PackCommandBack(command, (byte) 1, (byte[]) null);
      byte num = command[18];
      int int32_1 = BitConverter.ToInt32(command, 20);
      int int32_2 = BitConverter.ToInt32(command, 24);
      if (num == (byte) 77)
        return this.PackCommandBack(command, (byte) 0, this.rBuffer.GetBool(int32_1, int32_2).ToByteArray());
      return num == (byte) 73 ? this.PackCommandBack(command, (byte) 0, this.inputBuffer.GetBool(int32_1, int32_2).ToByteArray()) : this.PackCommandBack(command, (byte) 1, (byte[]) null);
    }
  }

  private byte[] WriteBoolByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command, (byte) 3, (byte[]) null);
    if (command[15] == (byte) 5 || command[15] == (byte) 15)
    {
      int destIndex = (int) this.ByteTransform.TransUInt16(command, 17);
      int length = (int) this.ByteTransform.TransUInt16(command, 19);
      switch (command[15])
      {
        case 5:
          if (command.Length != 21)
            return this.PackCommandBack(command, (byte) 5, (byte[]) null);
          this.yBuffer.SetBytes(new byte[1]{ command[19] }, destIndex);
          return this.PackCommandBack(command, (byte) 0, (byte[]) null);
        case 15:
          if (destIndex + length > (int) ushort.MaxValue)
            return this.PackCommandBack(command, (byte) 3, (byte[]) null);
          if (command.Length != 21 + (length + 7) / 8)
            return this.PackCommandBack(command, (byte) 5, (byte[]) null);
          this.yBuffer.SetBytes(((IEnumerable<bool>) command.RemoveBegin<byte>(21).ToBoolArray().SelectBegin<bool>(length)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : byte.MaxValue)).ToArray<byte>(), destIndex);
          return this.PackCommandBack(command, (byte) 0, (byte[]) null);
        default:
          return this.PackCommandBack(command, (byte) 1, (byte[]) null);
      }
    }
    else
    {
      if (command[15] != (byte) 79)
        return this.PackCommandBack(command, (byte) 1, (byte[]) null);
      byte num = command[18];
      int int32 = BitConverter.ToInt32(command, 20);
      int uint16 = (int) BitConverter.ToUInt16(command, 24);
      bool[] flagArray = command.RemoveBegin<byte>(28).ToBoolArray().SelectBegin<bool>(uint16);
      if (num != (byte) 77)
        return this.PackCommandBack(command, (byte) 1, (byte[]) null);
      this.rBuffer.SetBool(flagArray, int32);
      return this.PackCommandBack(command, (byte) 0, (byte[]) null);
    }
  }

  private byte[] ReadWordByCommand(byte[] command)
  {
    if (command[15] == (byte) 3 || command[15] == (byte) 4)
    {
      int num1 = (int) this.ByteTransform.TransUInt16(command, 17);
      int num2 = (int) this.ByteTransform.TransUInt16(command, 19);
      if (num1 + num2 > (int) ushort.MaxValue)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      if (command[15] == (byte) 3)
        return this.PackCommandBack(command, (byte) 0, this.rBuffer.GetBytes(num1 * 2, num2 * 2));
      if (command[15] == (byte) 4)
        return this.PackCommandBack(command, (byte) 0, this.inputBuffer.GetBytes(num1 * 2, num2 * 2));
    }
    else if (command[15] == (byte) 9 || command[15] == (byte) 10)
    {
      int uint16_1 = (int) BitConverter.ToUInt16(command, 18);
      int uint16_2 = (int) BitConverter.ToUInt16(command, 20);
      if (uint16_1 + uint16_2 > (int) ushort.MaxValue)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      if (command[15] == (byte) 9)
        return this.PackCommandBack(command, (byte) 0, SoftBasic.BytesReverseByWord(this.rExtBuffer.GetBytes(uint16_1 * 2, uint16_2 * 2)));
      if (command[15] == (byte) 10)
        return this.PackCommandBack(command, (byte) 0, SoftBasic.BytesReverseByWord(this.inputExtBuffer.GetBytes(uint16_1 * 2, uint16_2 * 2)));
    }
    else if (command[15] == (byte) 73)
    {
      byte num = command[18];
      int int32 = BitConverter.ToInt32(command, 20);
      int uint16 = (int) BitConverter.ToUInt16(command, 24);
      if (num == (byte) 77)
        return this.PackCommandBack(command, (byte) 0, SoftBasic.BytesReverseByWord(this.rBuffer.GetBytes(int32 * 2, uint16 * 2)));
      if (num == (byte) 73)
        return this.PackCommandBack(command, (byte) 0, SoftBasic.BytesReverseByWord(this.inputBuffer.GetBytes(int32 * 2, uint16 * 2)));
    }
    return this.PackCommandBack(command, (byte) 1, (byte[]) null);
  }

  private byte[] WriteWordByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command, (byte) 3, (byte[]) null);
    if (command[15] == (byte) 6 || command[15] == (byte) 16 /*0x10*/)
    {
      int num1 = (int) this.ByteTransform.TransUInt16(command, 17);
      int num2 = (int) this.ByteTransform.TransUInt16(command, 19);
      if (command[15] == (byte) 6)
      {
        if (command.Length != 21)
          return this.PackCommandBack(command, (byte) 3, (byte[]) null);
        this.rBuffer.SetBytes(command.SelectLast<byte>(2), num1 * 2);
        return this.PackCommandBack(command, (byte) 0, (byte[]) null);
      }
      if (num1 + num2 > (int) ushort.MaxValue)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      if (command.Length != 21 + num2 * 2)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      this.rBuffer.SetBytes(command.RemoveBegin<byte>(21), num1 * 2);
      return this.PackCommandBack(command, (byte) 0, (byte[]) null);
    }
    if (command[15] == (byte) 11)
    {
      int uint16_1 = (int) BitConverter.ToUInt16(command, 18);
      int uint16_2 = (int) BitConverter.ToUInt16(command, 20);
      if (uint16_1 + uint16_2 > (int) ushort.MaxValue)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      if (command.Length != 22 + uint16_2 * 2)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      this.rExtBuffer.SetBytes(SoftBasic.BytesReverseByWord(command.RemoveBegin<byte>(22)), uint16_1 * 2);
      return this.PackCommandBack(command, (byte) 0, (byte[]) null);
    }
    if (command[15] == (byte) 75)
    {
      byte num = command[18];
      int int32 = BitConverter.ToInt32(command, 20);
      int uint16 = (int) BitConverter.ToUInt16(command, 24);
      if (command.Length != 26 + uint16 * 2)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      if (num == (byte) 77)
      {
        this.rBuffer.SetBytes(SoftBasic.BytesReverseByWord(command.RemoveBegin<byte>(26)), int32 * 2);
        return this.PackCommandBack(command, (byte) 0, (byte[]) null);
      }
    }
    return this.PackCommandBack(command, (byte) 1, (byte[]) null);
  }

  private byte[] ReadRandomWordByCommand(byte[] command)
  {
    int uint16 = (int) BitConverter.ToUInt16(command, 18);
    if (command[15] == (byte) 13)
    {
      if (command.Length != 20 + uint16 * 2)
        return this.PackCommandBack(command, (byte) 3, (byte[]) null);
      byte[] result = new byte[uint16 * 2];
      for (int index = 0; index < uint16; ++index)
      {
        byte[] bytes = this.rExtBuffer.GetBytes((int) BitConverter.ToUInt16(command, 20 + index * 2) * 2, 2);
        result[index * 2] = bytes[1];
        result[index * 2 + 1] = bytes[0];
      }
      return this.PackCommandBack(command, (byte) 0, result);
    }
    if (command.Length != 20 + uint16 * 6)
      return this.PackCommandBack(command, (byte) 3, (byte[]) null);
    byte[] result1 = new byte[uint16 * 2];
    for (int index = 0; index < uint16; ++index)
    {
      byte num = command[20 + index * 6];
      int int32 = BitConverter.ToInt32(command, 22 + index * 6);
      switch (num)
      {
        case 73:
          byte[] bytes1 = this.inputBuffer.GetBytes(int32 * 2, 2);
          result1[index * 2] = bytes1[1];
          result1[index * 2 + 1] = bytes1[0];
          break;
        case 77:
          byte[] bytes2 = this.rBuffer.GetBytes(int32 * 2, 2);
          result1[index * 2] = bytes2[1];
          result1[index * 2 + 1] = bytes2[0];
          break;
      }
    }
    return this.PackCommandBack(command, (byte) 0, result1);
  }

  private byte[] WriteRandomWordByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommandBack(command, (byte) 3, (byte[]) null);
    int uint16_1 = (int) BitConverter.ToUInt16(command, 18);
    if (command.Length != 20 + uint16_1 * 4)
      return this.PackCommandBack(command, (byte) 3, (byte[]) null);
    for (int index = 0; index < uint16_1; ++index)
    {
      int uint16_2 = (int) BitConverter.ToUInt16(command, 20 + index * 4);
      this.rExtBuffer.SetValue(command[20 + index * 4 + 2], uint16_2 * 2 + 1);
      this.rExtBuffer.SetValue(command[20 + index * 4 + 3], uint16_2 * 2);
    }
    return this.PackCommandBack(command, (byte) 0, (byte[]) null);
  }

  private byte TransByteHighLow(byte value)
  {
    int num = ((int) value & 240 /*0xF0*/) >> 4;
    return (byte) (((int) value & 15) << 4 | num);
  }

  private byte[] PackCommandBack(byte[] cmds, byte err, byte[] result)
  {
    if (result == null)
      result = new byte[0];
    if (err > (byte) 0)
    {
      byte[] command = new byte[6 + result.Length];
      command[0] = (byte) 4;
      command[1] = (byte) 0;
      command[2] = cmds[14];
      command[3] = (byte) ((uint) cmds[15] + 128U /*0x80*/);
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      command[5] = err;
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 1 || cmds[15] == (byte) 2 || cmds[15] == (byte) 3 || cmds[15] == (byte) 4)
    {
      byte[] command = new byte[5 + result.Length];
      command[0] = BitConverter.GetBytes(3 + result.Length)[0];
      command[1] = BitConverter.GetBytes(3 + result.Length)[1];
      command[2] = cmds[14];
      command[3] = cmds[15];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      result.CopyTo((Array) command, 5);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 5 || cmds[15] == (byte) 6 || cmds[15] == (byte) 8)
    {
      byte[] command = cmds.RemoveBegin<byte>(12);
      command[0] = BitConverter.GetBytes(command.Length - 2)[0];
      command[1] = BitConverter.GetBytes(command.Length - 2)[1];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 9 || cmds[15] == (byte) 10 || cmds[15] == (byte) 13)
    {
      byte[] command = new byte[8 + result.Length];
      command[0] = BitConverter.GetBytes(command.Length - 2)[0];
      command[1] = BitConverter.GetBytes(command.Length - 2)[1];
      command[2] = cmds[14];
      command[3] = cmds[15];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      command[6] = BitConverter.GetBytes(result.Length / 2)[0];
      command[7] = BitConverter.GetBytes(result.Length / 2)[1];
      result.CopyTo((Array) command, 8);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 11)
    {
      byte[] command = cmds.SelectMiddle<byte>(12, 10);
      command[0] = BitConverter.GetBytes(command.Length - 2)[0];
      command[1] = BitConverter.GetBytes(command.Length - 2)[1];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 14)
    {
      byte[] command = cmds.SelectMiddle<byte>(12, 8);
      command[0] = BitConverter.GetBytes(command.Length - 2)[0];
      command[1] = BitConverter.GetBytes(command.Length - 2)[1];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 15 || cmds[15] == (byte) 16 /*0x10*/)
    {
      byte[] command = cmds.SelectMiddle<byte>(12, 9);
      command[0] = BitConverter.GetBytes(command.Length - 2)[0];
      command[1] = BitConverter.GetBytes(command.Length - 2)[1];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 65)
    {
      byte[] command = new byte[8 + result.Length];
      command[0] = BitConverter.GetBytes(command.Length - 2)[0];
      command[1] = BitConverter.GetBytes(command.Length - 2)[1];
      command[2] = cmds[14];
      command[3] = cmds[15];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      command[6] = cmds[18];
      result.CopyTo((Array) command, 8);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 73)
    {
      byte[] command = new byte[10 + result.Length];
      command[0] = BitConverter.GetBytes(8 + result.Length)[0];
      command[1] = BitConverter.GetBytes(8 + result.Length)[1];
      command[2] = cmds[14];
      command[3] = cmds[15];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      command[6] = cmds[18];
      command[8] = BitConverter.GetBytes(result.Length / 2)[0];
      command[9] = BitConverter.GetBytes(result.Length / 2)[1];
      result.CopyTo((Array) command, 10);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 75)
    {
      byte[] command = cmds.SelectMiddle<byte>(12, 14);
      command[0] = BitConverter.GetBytes(command.Length - 2)[0];
      command[1] = BitConverter.GetBytes(command.Length - 2)[1];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] == (byte) 77)
    {
      byte[] command = new byte[8 + result.Length];
      command[0] = BitConverter.GetBytes(6 + result.Length)[0];
      command[1] = BitConverter.GetBytes(6 + result.Length)[1];
      command[2] = cmds[14];
      command[3] = cmds[15];
      command[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
      command[6] = BitConverter.GetBytes(result.Length / 2)[0];
      command[7] = BitConverter.GetBytes(result.Length / 2)[1];
      result.CopyTo((Array) command, 8);
      return MemobusHelper.PackCommandWithHeader(command, (long) cmds[1]);
    }
    if (cmds[15] != (byte) 79)
      return this.PackCommandBack(cmds, (byte) 3, (byte[]) null);
    byte[] command1 = cmds.SelectMiddle<byte>(12, 16 /*0x10*/);
    command1[0] = BitConverter.GetBytes(command1.Length - 2)[0];
    command1[1] = BitConverter.GetBytes(command1.Length - 2)[1];
    command1[4] = this.TransByteHighLow(cmds[16 /*0x10*/]);
    return MemobusHelper.PackCommandWithHeader(command1, (long) cmds[1]);
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 655360 /*0x0A0000*/)
      throw new Exception("File is not correct");
    this.xBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.yBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.inputBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.rBuffer.SetBytes(content, 262144 /*0x040000*/, 0, 65536 /*0x010000*/);
    this.rExtBuffer.SetBytes(content, 393216 /*0x060000*/, 0, 65536 /*0x010000*/);
    this.inputExtBuffer.SetBytes(content, 524288 /*0x080000*/, 0, 65536 /*0x010000*/);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[655360 /*0x0A0000*/];
    Array.Copy((Array) this.xBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.yBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.inputBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.rBuffer.GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.rExtBuffer.GetBytes(), 0, (Array) destinationArray, 393216 /*0x060000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.inputExtBuffer.GetBytes(), 0, (Array) destinationArray, 524288 /*0x080000*/, 131072 /*0x020000*/);
    return destinationArray;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.xBuffer?.Dispose();
      this.yBuffer?.Dispose();
      this.inputBuffer?.Dispose();
      this.rBuffer?.Dispose();
      this.rExtBuffer?.Dispose();
      this.inputExtBuffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"MemobusTcpServer[{this.Port}]";
}
