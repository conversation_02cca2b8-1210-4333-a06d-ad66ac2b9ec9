﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.TurckReaderMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>图尔克Reader协议的消息</summary>
public class TurckReaderMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 3;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes == null || this.HeadBytes[0] == (byte) 170;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    if (this.HeadBytes[2] <= (byte) 3)
      return 0;
    int lengthByHeadBytes = (int) this.HeadBytes[2] - 3;
    if (lengthByHeadBytes < 0)
      lengthByHeadBytes = 0;
    return lengthByHeadBytes;
  }

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    return TurckReaderMessage.CheckResponseACK(receive) ? -1 : 1;
  }

  private static bool CheckResponseACK(byte[] content)
  {
    try
    {
      if (content[1] == (byte) 7 && content[2] == (byte) 7)
      {
        if (content[3] == (byte) 104 || content[3] == (byte) 105 && content[4] == (byte) 137 || content[3] == (byte) 112 /*0x70*/ || content[3] == (byte) 105 && content[4] == (byte) 129)
          return true;
      }
    }
    catch
    {
      return false;
    }
    return false;
  }
}
