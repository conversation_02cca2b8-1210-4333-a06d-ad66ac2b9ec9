﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.FileStateObject
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.IO;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>文件传送的异步对象</summary>
internal class FileStateObject : StateOneBase
{
  /// <summary>操作的流</summary>
  public Stream Stream { get; set; }
}
