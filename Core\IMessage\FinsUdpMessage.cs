﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.FinsUdpMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>FinsUdp的消息</summary>
public class FinsUdpMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => -1;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => 0;

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    if (send == null || receive == null)
      return 1;
    if (send.Length < 10 || receive.Length < 10)
      return base.CheckMessageMatch(send, receive);
    return (int) send[9] == (int) receive[9] ? 1 : -1;
  }
}
