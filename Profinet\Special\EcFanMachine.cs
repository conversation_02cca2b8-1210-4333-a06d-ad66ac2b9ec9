﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Special.EcFanMachine
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using System;

#nullable disable
namespace HslCommunication.Profinet.Special;

/// <summary>
/// FFU 用EC 风机风机通信协议，使用串口通讯，支持风机的转速控制，风机的地址设置，风机的状态读取等功能<br />
/// </summary>
public class EcFanMachine : DeviceSerialPort
{
  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new EuFunMessage();

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    if (response == null || response.Length < 8)
      return new OperateResult<byte[]>("Command is too short: " + response.ToHexString(' '));
    if (response.Length >= 9 && response[0] == byte.MaxValue && response[8] == (byte) 13)
      response = response.RemoveBegin<byte>(1);
    EcFanMachine.CalculateCheck(response);
    if (EcFanMachine.CheckCrc(response))
      return OperateResult.CreateSuccessResult<byte[]>(response);
    byte[] command = response.CopyArray<byte>();
    if (command[0] != (byte) 126)
    {
      command[0] = (byte) 126;
      if (EcFanMachine.CheckCrc(command))
        return OperateResult.CreateSuccessResult<byte[]>(response);
    }
    return new OperateResult<byte[]>("Check response crc error: " + response.ToHexString(' '));
  }

  /// <summary>获取或设置风机的地址，范围是1-30</summary>
  public byte Station { get; set; } = 1;

  /// <summary>
  /// 下发速度的命令，返回风机的状态数据<br />
  /// </summary>
  /// <param name="run">运行状态</param>
  /// <param name="emergency">应急模式设置位，True表示切换应急模式，False表示不操作</param>
  /// <param name="speed">转速</param>
  /// <returns></returns>
  public OperateResult<EcFanData> ControlSpeed(bool run, bool emergency, int speed)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(EcFanMachine.BuildCommand5A(this.Station, run, emergency, speed));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<EcFanData>((OperateResult) result) : EcFanMachine.ParseResult5A(result.Content);
  }

  /// <summary>
  /// 设置风机的地址，范围是1-30<br />
  /// </summary>
  /// <param name="id">新的地址</param>
  /// <returns>是否设置成功</returns>
  public OperateResult SetStation(byte id)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(EcFanMachine.BuildCommandA2(this.Station, id));
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    this.Station = id;
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>读取应急模式的转速</summary>
  /// <returns>转速信息</returns>
  public OperateResult<int> ReadSpeedEmergency()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(EcFanMachine.BuildReadSpeed((byte) 173, this.Station));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int>((OperateResult) result) : OperateResult.CreateSuccessResult<int>((int) BitConverter.ToUInt16(result.Content, 3));
  }

  /// <summary>读取风机的最低转速</summary>
  /// <returns>转速信息</returns>
  public OperateResult<int> ReadSpeedMin()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(EcFanMachine.BuildReadSpeed((byte) 174, this.Station));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int>((OperateResult) result) : OperateResult.CreateSuccessResult<int>((int) BitConverter.ToUInt16(result.Content, 3));
  }

  /// <summary>读取风机的最高转速</summary>
  /// <returns>转速信息</returns>
  public OperateResult<int> ReadSpeedMax()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(EcFanMachine.BuildReadSpeed((byte) 175, this.Station));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int>((OperateResult) result) : OperateResult.CreateSuccessResult<int>((int) BitConverter.ToUInt16(result.Content, 3));
  }

  /// <summary>
  /// 构建命令5A的命令数据<br />
  /// </summary>
  /// <param name="stat">站号</param>
  /// <param name="run">运行状态</param>
  /// <param name="emergency">应急模式</param>
  /// <param name="speed">速度</param>
  /// <returns>完整的命令</returns>
  public static byte[] BuildCommand5A(byte stat, bool run, bool emergency, int speed)
  {
    byte[] command = new byte[4]
    {
      (byte) 90,
      stat,
      (byte) 0,
      (byte) 0
    };
    if (run)
      command[2] |= (byte) 128 /*0x80*/;
    if (emergency)
      command[2] |= (byte) 64 /*0x40*/;
    byte[] bytes = BitConverter.GetBytes(speed);
    command[2] |= (byte) ((uint) bytes[1] & 15U);
    command[3] = bytes[0];
    return EcFanMachine.BuildEntireCommand(command);
  }

  internal static OperateResult<EcFanData> ParseResult5A(byte[] command)
  {
    try
    {
      return OperateResult.CreateSuccessResult<EcFanData>(new EcFanData()
      {
        EmergencyState = ((int) command[2] & 64 /*0x40*/) == 64 /*0x40*/,
        RunState = ((int) command[3] & 128 /*0x80*/) == 128 /*0x80*/,
        LockState = ((int) command[3] & 64 /*0x40*/) == 64 /*0x40*/,
        OverHotState = ((int) command[3] & 32 /*0x20*/) == 32 /*0x20*/,
        LostSpeedState = ((int) command[3] & 16 /*0x10*/) == 16 /*0x10*/,
        Speed = ((int) command[3] & 15) << 8 | (int) command[4]
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<EcFanData>($"Parse result error: {ex.Message}{Environment.NewLine}Source Code: {command.ToHexString(' ')}");
    }
  }

  internal static byte[] BuildCommandA2(byte stat, byte id)
  {
    return EcFanMachine.BuildEntireCommand(new byte[4]
    {
      (byte) 162,
      stat,
      id,
      byte.MaxValue
    });
  }

  internal static byte[] BuildReadSpeed(byte code, byte stat)
  {
    return EcFanMachine.BuildEntireCommand(new byte[4]
    {
      code,
      stat,
      (byte) 44,
      (byte) 1
    });
  }

  /// <summary>传入命令，返回完整的命令数据</summary>
  /// <param name="command">读写数据的命令</param>
  /// <returns></returns>
  public static byte[] BuildEntireCommand(byte[] command)
  {
    byte[] command1 = new byte[command.Length + 4];
    command1[0] = (byte) 126;
    command.CopyTo((Array) command1, 1);
    byte[] check = EcFanMachine.CalculateCheck(command1);
    command1[command.Length + 1] = check[0];
    command1[command.Length + 2] = check[1];
    command1[command.Length + 3] = (byte) 13;
    return command1;
  }

  /// <summary>
  /// 计算校验和，返回2个字节的校验和<br />
  /// </summary>
  /// <param name="command">命令报文</param>
  /// <returns>检查结果值信息</returns>
  internal static byte[] CalculateCheck(byte[] command)
  {
    int num1 = 0;
    for (int index = 0; index < command.Length - 3 && index < 5; ++index)
      num1 += (int) command[index];
    int num2 = ~num1 + 1;
    return new byte[2]
    {
      (byte) (num2 >> 8),
      (byte) (num2 & (int) byte.MaxValue)
    };
  }

  internal static bool CheckCrc(byte[] command)
  {
    if (command == null || command.Length < 8)
      return false;
    byte[] check = EcFanMachine.CalculateCheck(command);
    return (int) command[5] == (int) check[0] && (int) command[6] == (int) check[1];
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.NetMessageBase.CheckReceiveDataComplete(System.Byte[],System.IO.MemoryStream)" />
  public static bool CheckReceiveDataComplete(byte[] send, byte[] response)
  {
    return response != null && (response.Length >= 8 && response[7] == (byte) 13 || response.Length >= 9 && response[0] == byte.MaxValue && response[8] == (byte) 13);
  }
}
