﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.IMelsecFxSerial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>三菱的串口的接口类对象</summary>
public interface IMelsecFxSerial : IReadWriteNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ActivePlc(HslCommunication.Core.IReadWriteDevice)" />
  OperateResult ActivePlc();

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ActivePlc(HslCommunication.Core.IReadWriteDevice)" />
  Task<OperateResult> ActivePlcAsync();
}
