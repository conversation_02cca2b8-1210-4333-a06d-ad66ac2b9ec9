﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Gem
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Secs.Types;
using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Secs;

/// <summary>GEM相关的数据读写信息</summary>
public class Gem
{
  private ISecs secs;

  /// <summary>
  /// 使用指定的 <see cref="T:HslCommunication.Secs.Types.ISecs" /> 接口来初始化 GEM 对象，然后进行数据读写操作
  /// </summary>
  /// <param name="secs">Secs的通信对象</param>
  public Gem(ISecs secs) => this.secs = secs;

  /// <summary>S1F1的功能方法</summary>
  /// <returns>在线数据信息</returns>
  public OperateResult<OnlineData> S1F1_AreYouThere()
  {
    OperateResult<SecsMessage> result = this.secs.ReadSecsMessage((byte) 1, (byte) 1, new SecsValue(), true);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<OnlineData>((OperateResult) result) : OperateResult.CreateSuccessResult<OnlineData>((OnlineData) result.Content.GetItemValues());
  }

  /// <summary>S1F11的功能方法</summary>
  /// <returns>变量名称数组</returns>
  public OperateResult<VariableName[]> S1F11_StatusVariableNamelist()
  {
    OperateResult<SecsMessage> result = this.secs.ReadSecsMessage((byte) 1, (byte) 11, new SecsValue(), true);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<VariableName[]>((OperateResult) result) : OperateResult.CreateSuccessResult<VariableName[]>(result.Content.GetItemValues().ToVaruableNames());
  }

  /// <summary>S1F11的功能方法，带参数传递</summary>
  /// <param name="statusVaruableId"></param>
  /// <returns></returns>
  public OperateResult<VariableName[]> S1F11_StatusVariableNamelist(params int[] statusVaruableId)
  {
    OperateResult<SecsMessage> result = this.secs.ReadSecsMessage((byte) 1, (byte) 11, new SecsValue(statusVaruableId), true);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<VariableName[]>((OperateResult) result) : OperateResult.CreateSuccessResult<VariableName[]>(result.Content.GetItemValues().ToVaruableNames());
  }

  /// <summary>S1F13的功能方法，测试连接的</summary>
  /// <returns></returns>
  public OperateResult<OnlineData> S1F13_EstablishCommunications()
  {
    OperateResult<SecsMessage> result = this.secs.ReadSecsMessage((byte) 1, (byte) 13, new SecsValue(), true);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<OnlineData>((OperateResult) result);
    SecsValue itemValues = result.Content.GetItemValues();
    SecsValue[] secsValueArray = itemValues.Value as SecsValue[];
    return ((byte[]) secsValueArray[0].Value)[0] == (byte) 0 ? OperateResult.CreateSuccessResult<OnlineData>((OnlineData) secsValueArray[1]) : new OperateResult<OnlineData>($"establish communications acknowledgement denied! source: {Environment.NewLine}{itemValues.ToXElement()}");
  }

  /// <summary>S1F15的功能方法</summary>
  /// <remarks>返回值说明，0: ok, 1: refused, 2: already online</remarks>
  /// <returns>返回值说明，0: ok, 1: refused, 2: already online</returns>
  public OperateResult<byte> S1F15_OfflineRequest()
  {
    OperateResult<SecsMessage> result = this.secs.ReadSecsMessage((byte) 1, (byte) 15, new SecsValue(), true);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte>((OperateResult) result) : OperateResult.CreateSuccessResult<byte>(((byte[]) result.Content.GetItemValues().Value)[0]);
  }

  /// <summary>S1F17的功能方法</summary>
  /// <remarks>返回值说明，0: ok, 1: refused, 2: already online</remarks>
  /// <returns>返回值说明，0: ok, 1: refused, 2: already online</returns>
  public OperateResult<byte> S1F17_OnlineRequest()
  {
    OperateResult<SecsMessage> result = this.secs.ReadSecsMessage((byte) 1, (byte) 17, new SecsValue(), true);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte>((OperateResult) result) : OperateResult.CreateSuccessResult<byte>(((byte[]) result.Content.GetItemValues().Value)[0]);
  }

  /// <summary>S2F13的功能方法</summary>
  /// <param name="list"></param>
  /// <returns></returns>
  public OperateResult<SecsValue> S2F13_EquipmentConstantRequest(object[] list = null)
  {
    OperateResult<SecsMessage> result = this.secs.ReadSecsMessage((byte) 2, (byte) 13, new SecsValue((IEnumerable<object>) list), true);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<SecsValue>((OperateResult) result) : OperateResult.CreateSuccessResult<SecsValue>(result.Content.GetItemValues());
  }
}
