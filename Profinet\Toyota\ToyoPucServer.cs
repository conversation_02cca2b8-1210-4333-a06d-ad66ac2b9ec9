﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Toyota.ToyoPucServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;

#nullable disable
namespace HslCommunication.Profinet.Toyota;

/// <summary>丰田工机的PLC对象信息</summary>
public class ToyoPucServer : DeviceServer
{
  private SoftBuffer buffer1;
  private SoftBuffer buffer00;
  private SoftBuffer buffer01;
  private SoftBuffer buffer02;
  private SoftBuffer buffer03;
  private SoftBuffer buffer08;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private int station = 0;

  /// <summary>实例化一个丰田工机PLC的网口和串口服务器，支持数据读写操作</summary>
  public ToyoPucServer()
  {
    this.buffer1 = new SoftBuffer(65536 /*0x010000*/);
    this.buffer00 = new SoftBuffer(65536 /*0x010000*/);
    this.buffer01 = new SoftBuffer(65536 /*0x010000*/);
    this.buffer02 = new SoftBuffer(65536 /*0x010000*/);
    this.buffer03 = new SoftBuffer(65536 /*0x010000*/);
    this.buffer08 = new SoftBuffer(65536 /*0x010000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new ToyoPucMessage();

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] bytes = new byte[393216 /*0x060000*/];
    this.buffer1.GetBytes().CopyTo((Array) bytes, 0);
    this.buffer00.GetBytes().CopyTo((Array) bytes, 65536 /*0x010000*/);
    this.buffer01.GetBytes().CopyTo((Array) bytes, 131072 /*0x020000*/);
    this.buffer02.GetBytes().CopyTo((Array) bytes, 196608 /*0x030000*/);
    this.buffer03.GetBytes().CopyTo((Array) bytes, 262144 /*0x040000*/);
    this.buffer08.GetBytes().CopyTo((Array) bytes, 327680 /*0x050000*/);
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 393216 /*0x060000*/)
      throw new Exception("File is not correct");
    this.buffer1.SetBytes(content, 0, 65536 /*0x010000*/);
    this.buffer00.SetBytes(content, 65536 /*0x010000*/, 65536 /*0x010000*/);
    this.buffer01.SetBytes(content, 131072 /*0x020000*/, 65536 /*0x010000*/);
    this.buffer02.SetBytes(content, 196608 /*0x030000*/, 65536 /*0x010000*/);
    this.buffer03.SetBytes(content, 262144 /*0x040000*/, 65536 /*0x010000*/);
    this.buffer08.SetBytes(content, 327680 /*0x050000*/, 65536 /*0x010000*/);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, length, false);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    ToyoPucAddress content = from.Content;
    if (content.PRG < 0)
      return OperateResult.CreateSuccessResult<byte[]>(this.buffer1.GetBytes(content.AddressStart * 2, (int) length * 2));
    if (content.PRG == 0)
      return OperateResult.CreateSuccessResult<byte[]>(this.buffer00.GetBytes(content.AddressStart * 2, (int) length * 2));
    if (content.PRG == 1)
      return OperateResult.CreateSuccessResult<byte[]>(this.buffer01.GetBytes(content.AddressStart * 2, (int) length * 2));
    if (content.PRG == 2)
      return OperateResult.CreateSuccessResult<byte[]>(this.buffer02.GetBytes(content.AddressStart * 2, (int) length * 2));
    if (content.PRG == 3)
      return OperateResult.CreateSuccessResult<byte[]>(this.buffer03.GetBytes(content.AddressStart * 2, (int) length * 2));
    return content.PRG == 8 ? OperateResult.CreateSuccessResult<byte[]>(this.buffer08.GetBytes(content.AddressStart * 2, (int) length * 2)) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, (ushort) 1, false);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    ToyoPucAddress content = from.Content;
    if (content.PRG >= 0)
    {
      if (content.PRG == 0)
        this.buffer00.SetBytes(value, content.AddressStart * 2);
      else if (content.PRG == 1)
        this.buffer01.SetBytes(value, content.AddressStart * 2);
      else if (content.PRG == 2)
        this.buffer02.SetBytes(value, content.AddressStart * 2);
      else if (content.PRG == 3)
      {
        this.buffer03.SetBytes(value, content.AddressStart * 2);
      }
      else
      {
        if (content.PRG != 8)
          return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
        this.buffer08.SetBytes(value, content.AddressStart * 2);
      }
      return OperateResult.CreateSuccessResult();
    }
    this.buffer1.SetBytes(value, content.AddressStart * 2);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Toyota.ToyoPuc.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, length, true);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    ToyoPucAddress content = from.Content;
    if (content.PRG < 0)
      return OperateResult.CreateSuccessResult<bool[]>(this.buffer1.GetBool(content.AddressStart, (int) length));
    if (content.PRG == 0)
      return OperateResult.CreateSuccessResult<bool[]>(this.buffer00.GetBool(content.AddressStart, (int) length));
    if (content.PRG == 1)
      return OperateResult.CreateSuccessResult<bool[]>(this.buffer01.GetBool(content.AddressStart, (int) length));
    if (content.PRG == 2)
      return OperateResult.CreateSuccessResult<bool[]>(this.buffer02.GetBool(content.AddressStart, (int) length));
    if (content.PRG == 3)
      return OperateResult.CreateSuccessResult<bool[]>(this.buffer03.GetBool(content.AddressStart, (int) length));
    return content.PRG == 8 ? OperateResult.CreateSuccessResult<bool[]>(this.buffer08.GetBool(content.AddressStart, (int) length)) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Device.DeviceCommunication.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<ToyoPucAddress> from = ToyoPucAddress.ParseFrom(address, (ushort) 1, true);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    ToyoPucAddress content = from.Content;
    if (content.PRG >= 0)
    {
      if (content.PRG == 0)
        this.buffer00.SetBool(value, content.AddressStart);
      else if (content.PRG == 1)
        this.buffer01.SetBool(value, content.AddressStart);
      else if (content.PRG == 2)
        this.buffer02.SetBool(value, content.AddressStart);
      else if (content.PRG == 3)
      {
        this.buffer03.SetBool(value, content.AddressStart);
      }
      else
      {
        if (content.PRG != 8)
          return (OperateResult) new OperateResult<byte[]>("Not supported prg write bool");
        this.buffer08.SetBool(value, content.AddressStart);
      }
      return OperateResult.CreateSuccessResult();
    }
    this.buffer1.SetBool(value, content.AddressStart);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length < 5)
      return (OperateResult<byte[]>) null;
    try
    {
      byte[] numArray = (byte[]) null;
      if (receive[4] == (byte) 28)
        numArray = this.ReadWordByCommand(receive);
      else if (receive[4] == (byte) 32 /*0x20*/)
        numArray = this.ReadBoolByCommand(receive);
      else if (receive[4] == (byte) 29)
        numArray = this.WriteWordByCommand(receive);
      else if (receive[4] == (byte) 33)
        numArray = this.WriteBoolByCommand(receive);
      else if (receive[4] == (byte) 34)
        numArray = this.ReadRandomWord(receive);
      else if (receive[4] == (byte) 35)
        numArray = this.WriteRandomWord(receive);
      else if (receive[4] == (byte) 148)
        numArray = this.ReadExWordByCommand(receive);
      else if (receive[4] == (byte) 149)
        numArray = this.WriteExWordByCommand(receive);
      return numArray != null ? OperateResult.CreateSuccessResult<byte[]>(numArray) : OperateResult.CreateSuccessResult<byte[]>(this.CreateResponseBack(receive, (byte) 35, (byte[]) null));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  private byte[] CreateResponseBack(byte[] request, byte err, byte[] data)
  {
    if (err > (byte) 0)
      return new byte[5]
      {
        (byte) 128 /*0x80*/,
        (byte) 16 /*0x10*/,
        (byte) 1,
        (byte) 0,
        err
      };
    if (data == null)
      data = new byte[0];
    byte[] responseBack = new byte[5 + data.Length];
    responseBack[0] = (byte) 128 /*0x80*/;
    responseBack[1] = (byte) 0;
    responseBack[2] = BitConverter.GetBytes(data.Length + 1)[0];
    responseBack[3] = BitConverter.GetBytes(data.Length + 1)[1];
    responseBack[4] = request[4];
    if (data.Length != 0)
      data.CopyTo((Array) responseBack, 5);
    return responseBack;
  }

  private byte[] ReadWordByCommand(byte[] command)
  {
    ushort uint16_1 = BitConverter.ToUInt16(command, 5);
    ushort uint16_2 = BitConverter.ToUInt16(command, 7);
    return this.CreateResponseBack(command, (byte) 0, this.buffer1.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
  }

  private byte[] ReadExWordByCommand(byte[] command)
  {
    ushort uint16_1 = BitConverter.ToUInt16(command, 6);
    ushort uint16_2 = BitConverter.ToUInt16(command, 8);
    if (command[5] == (byte) 0)
      return this.CreateResponseBack(command, (byte) 0, this.buffer00.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
    if (command[5] == (byte) 1)
      return this.CreateResponseBack(command, (byte) 0, this.buffer01.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
    if (command[5] == (byte) 2)
      return this.CreateResponseBack(command, (byte) 0, this.buffer02.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
    if (command[5] == (byte) 3)
      return this.CreateResponseBack(command, (byte) 0, this.buffer03.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
    if (command[5] != (byte) 8)
      return this.CreateResponseBack(command, (byte) 52, (byte[]) null);
    return (int) uint16_1 * 2 > (int) ushort.MaxValue || (int) uint16_1 * 2 + (int) uint16_2 * 2 > 65536 /*0x010000*/ ? this.CreateResponseBack(command, (byte) 64 /*0x40*/, (byte[]) null) : this.CreateResponseBack(command, (byte) 0, this.buffer08.GetBytes((int) uint16_1 * 2, (int) uint16_2 * 2));
  }

  private byte[] ReadRandomWord(byte[] command)
  {
    int num = (command.Length - 5) / 2;
    if (num > 80 /*0x50*/)
      return this.CreateResponseBack(command, (byte) 64 /*0x40*/, (byte[]) null);
    byte[] data = new byte[num * 2];
    for (int startIndex = 5; startIndex < command.Length - 1; startIndex += 2)
      this.buffer1.GetBytes((int) BitConverter.ToUInt16(command, startIndex) * 2, 2).CopyTo((Array) data, startIndex - 5);
    return this.CreateResponseBack(command, (byte) 0, data);
  }

  private byte[] WriteRandomWord(byte[] command)
  {
    int num = (command.Length - 5) / 4;
    if (num > 80 /*0x50*/)
      return this.CreateResponseBack(command, (byte) 64 /*0x40*/, (byte[]) null);
    for (int index = 0; index < num; ++index)
    {
      ushort uint16 = BitConverter.ToUInt16(command, index * 4 + 5);
      this.buffer1.SetBytes(command.SelectMiddle<byte>(index * 4 + 7, 2), (int) uint16);
    }
    return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
  }

  private byte[] ReadBoolByCommand(byte[] command)
  {
    bool flag = this.buffer1.GetBool((int) BitConverter.ToUInt16(command, 5));
    return this.CreateResponseBack(command, (byte) 0, new byte[1]
    {
      flag ? (byte) 1 : (byte) 0
    });
  }

  private byte[] WriteWordByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.CreateResponseBack(command, (byte) 52, (byte[]) null);
    ushort uint16 = BitConverter.ToUInt16(command, 5);
    this.buffer1.SetBytes(command.RemoveBegin<byte>(7), (int) uint16 * 2);
    return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
  }

  private byte[] WriteExWordByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.CreateResponseBack(command, (byte) 52, (byte[]) null);
    ushort uint16 = BitConverter.ToUInt16(command, 6);
    if (command[5] == (byte) 0)
      this.buffer00.SetBytes(command.RemoveBegin<byte>(8), (int) uint16 * 2);
    else if (command[5] == (byte) 1)
      this.buffer01.SetBytes(command.RemoveBegin<byte>(8), (int) uint16 * 2);
    else if (command[5] == (byte) 2)
      this.buffer02.SetBytes(command.RemoveBegin<byte>(8), (int) uint16 * 2);
    else if (command[5] == (byte) 3)
    {
      this.buffer03.SetBytes(command.RemoveBegin<byte>(8), (int) uint16 * 2);
    }
    else
    {
      if (command[5] != (byte) 8)
        return this.CreateResponseBack(command, (byte) 52, (byte[]) null);
      if ((int) uint16 * 2 > (int) ushort.MaxValue)
        return this.CreateResponseBack(command, (byte) 64 /*0x40*/, (byte[]) null);
      this.buffer08.SetBytes(command.RemoveBegin<byte>(8), (int) uint16 * 2);
    }
    return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
  }

  private byte[] WriteBoolByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.CreateResponseBack(command, (byte) 52, (byte[]) null);
    ushort uint16 = BitConverter.ToUInt16(command, 5);
    this.buffer1.SetBool(command[7] > (byte) 0, (int) uint16);
    return this.CreateResponseBack(command, (byte) 0, (byte[]) null);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.buffer1.Dispose();
      this.buffer00.Dispose();
      this.buffer01.Dispose();
      this.buffer02.Dispose();
      this.buffer03.Dispose();
      this.buffer08.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"ToyoPucServer[{this.Port}]";
}
