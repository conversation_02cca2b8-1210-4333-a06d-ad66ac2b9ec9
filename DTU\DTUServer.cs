﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.DTU.DTUServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Device;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace HslCommunication.DTU;

/// <summary>
/// DTU的服务器信息，本服务器支持任意的hsl支持的网络对象，包括plc信息，modbus设备等等，通过DTU来连接，
/// 然后支持多个连接对象。如果需要支持非hsl的注册报文，需要重写相关的方法<br />
/// DTU server information, the server supports any network objects supported by hsl,
/// including plc information, modbus devices, etc., connected through DTU, and then supports multiple connection objects.
/// If you need to support non-HSL registration messages, you need to rewrite the relevant methods
/// </summary>
/// <remarks>针对异形客户端进行扩展信息</remarks>
public class DTUServer : NetworkAlienClient
{
  private Dictionary<string, DeviceCommunication> devices;

  /// <summary>
  /// 根据配置的列表信息来实例化相关的DTU服务器<br />
  /// Instantiate the relevant DTU server according to the configured list information
  /// </summary>
  /// <param name="dTUSettings">DTU的配置信息</param>
  public DTUServer(List<DTUSettingType> dTUSettings)
  {
    this.devices = new Dictionary<string, DeviceCommunication>();
    this.SetTrustClients(dTUSettings.Select<DTUSettingType, string>((Func<DTUSettingType, string>) (m => m.DtuId)).ToArray<string>());
    for (int index = 0; index < dTUSettings.Count; ++index)
    {
      this.devices.Add(dTUSettings[index].DtuId, (DeviceCommunication) dTUSettings[index].GetClient());
      this.devices[dTUSettings[index].DtuId].SetDtuPipe(new PipeDtuNet()
      {
        DTU = dTUSettings[index].DtuId
      });
    }
    this.OnClientConnected += new NetworkAlienClient.OnClientConnectedDelegate(this.DTUServer_OnClientConnected);
  }

  /// <summary>
  /// 根据配置的列表信息来实例化相关的DTU服务器<br />
  /// Instantiate the relevant DTU server according to the configured list information
  /// </summary>
  /// <param name="dtuId">Dtu信息</param>
  /// <param name="networkDevices">设备信息</param>
  public DTUServer(string[] dtuId, DeviceTcpNet[] networkDevices)
  {
    this.devices = new Dictionary<string, DeviceCommunication>();
    this.SetTrustClients(dtuId);
    for (int index = 0; index < dtuId.Length; ++index)
    {
      this.devices.Add(dtuId[index], (DeviceCommunication) networkDevices[index]);
      this.devices[dtuId[index]].SetDtuPipe(new PipeDtuNet()
      {
        DTU = dtuId[index]
      });
    }
  }

  /// <inheritdoc />
  protected override void ExtraOnClose()
  {
    foreach (KeyValuePair<string, DeviceCommunication> device in this.devices)
    {
      if (device.Value.CommunicationPipe is PipeDtuNet communicationPipe)
        communicationPipe.CloseCommunication();
    }
    base.ExtraOnClose();
  }

  /// <inheritdoc />
  public override int IsClientOnline(PipeDtuNet pipe)
  {
    return this.devices[pipe.DTU].CommunicationPipe.IsConnectError() ? 0 : 1;
  }

  private void DTUServer_OnClientConnected(PipeDtuNet dtu) => this.devices[dtu.DTU].SetDtuPipe(dtu);

  /// <summary>
  /// 根据DTU信息获取设备的连接对象<br />
  /// Obtain the connection object of the device according to the DTU information
  /// </summary>
  /// <param name="dtuId">设备的id信息</param>
  /// <returns>设备的对象</returns>
  public DeviceCommunication this[string dtuId]
  {
    get => !this.devices.ContainsKey(dtuId) ? (DeviceCommunication) null : this.devices[dtuId];
  }

  /// <summary>
  /// 获取所有的会话信息，是否在线，上线的基本信息<br />
  /// Get all the session information, whether it is online, online basic information
  /// </summary>
  /// <returns>会话列表</returns>
  public PipeDtuNet[] GetPipeSessions()
  {
    return this.devices.Values.Select<DeviceCommunication, PipeDtuNet>((Func<DeviceCommunication, PipeDtuNet>) (m => m.CommunicationPipe as PipeDtuNet)).ToArray<PipeDtuNet>();
  }

  /// <summary>
  /// 获取所有的设备的信息，可以用来读写设备的数据信息<br />
  /// Get all device information, can be used to read and write device data information
  /// </summary>
  /// <returns>设备数组</returns>
  public DeviceCommunication[] GetDevices() => this.devices.Values.ToArray<DeviceCommunication>();

  /// <inheritdoc />
  public override string ToString() => $"DTUServer[{this.Port}]";
}
