﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT698Server
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Instrument.DLT.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>DLT698的虚拟服务器实现</summary>
public class DLT698Server : DLT645Server
{
  /// <summary>实例化一个默认的对象</summary>
  public DLT698Server()
  {
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.CreateAddressTags2();
  }

  /// <inheritdoc />
  protected override void CreateAddressTags()
  {
  }

  private void CreateAddressTags2()
  {
    this.AddDltTag("00000200", this.CreateDltBuffer(new int[5]
    {
      1100,
      2200,
      3300,
      4400,
      -5500
    }), 2);
    this.AddDltTag("00100200", this.CreateDltBuffer(new uint[5]
    {
      110U,
      120U,
      130U,
      140U,
      150U
    }), 2);
    this.AddDltTag("00200200", this.CreateDltBuffer(new uint[5]
    {
      210U,
      220U,
      230U,
      240U /*0xF0*/,
      250U
    }), 2);
    this.AddDltTag("00300200", this.CreateDltBuffer(new uint[5]
    {
      310U,
      320U,
      330U,
      340U,
      350U
    }), 2);
    this.AddDltTag("00400200", this.CreateDltBuffer(new uint[5]
    {
      410U,
      420U,
      430U,
      440U,
      450U
    }), 2);
    this.AddDltTag("10000200", this.CreateDltBuffer(new uint[5]
    {
      410000U,
      420000U,
      430000U,
      440000U,
      450000U
    }), 4);
    this.AddDltTag("10100200", this.CreateDltBuffer(new uint[5]
    {
      510000U,
      520000U,
      530000U,
      540000U,
      550000U
    }), 4);
    this.AddDltTag("10200200", this.CreateDltBuffer(new uint[5]
    {
      610000U,
      620000U,
      630000U,
      640000U,
      650000U
    }), 4);
    this.AddDltTag("10300200", this.CreateDltBuffer(new uint[5]
    {
      710000U,
      720000U,
      730000U,
      740000U,
      750000U
    }), 4);
    this.AddDltTag("10400200", this.CreateDltBuffer(new uint[5]
    {
      810000U,
      820000U,
      830000U,
      840000U,
      850000U
    }), 4);
    this.AddDltTag("20000200", this.CreateDltBuffer(new ushort[3]
    {
      (ushort) 2231,
      (ushort) 2331,
      (ushort) 2431
    }), 1);
    this.AddDltTag("20010200", this.CreateDltBuffer(new int[3]
    {
      11000,
      12000,
      13000
    }), 3);
    this.AddDltTag("20020200", this.CreateDltBuffer(new ushort[3]
    {
      (ushort) 234,
      (ushort) 244,
      (ushort) 254
    }), 1);
    this.AddDltTag("20030200", this.CreateDltBuffer(new ushort[3]
    {
      (ushort) 334,
      (ushort) 344,
      (ushort) 354
    }), 1);
    this.AddDltTag("20040200", this.CreateDltBuffer(1234), 1);
    this.AddDltTag("20050200", this.CreateDltBuffer(1334), 1);
    this.AddDltTag("20060200", this.CreateDltBuffer(1434), 1);
    this.AddDltTag("200A0200", this.CreateDltBuffer((short) 12345), 3);
    this.AddDltTag("200F0200", this.CreateDltBuffer((ushort) 5012), 2);
    this.AddDltTag("20100200", this.CreateDltBuffer((short) 234), 1);
    this.AddDltTag("20110200", this.CreateDltBuffer((ushort) 567), 2);
    this.AddDltTag("20120200", this.CreateDltBuffer((ushort) 577), 2);
    this.AddDltTag("20170200", this.CreateDltBuffer(23456), 4);
    this.AddDltTag("20180200", this.CreateDltBuffer(33456), 4);
    this.AddDltTag("20190200", this.CreateDltBuffer(43456), 4);
    this.AddDltTag("201A0200", this.CreateDltBuffer(567U), 4);
    this.AddDltTag("20260200", this.CreateDltBuffer((ushort) 567), 2);
    this.AddDltTag("20270200", this.CreateDltBuffer((ushort) 667), 2);
    this.AddDltTag("20280200", this.CreateDltBuffer((ushort) 767 /*0x02FF*/), 2);
    this.AddDltTag("40000200", "1C07E00114101B0B".ToHexBytes(), -1);
    this.allDatas["40000200"].Buffer = DLT698Helper.CreateDateTimeValue(DateTime.Now);
    this.AddDltTag("40010200", "0906000000000001".ToHexBytes(), -1);
    this.AddDltTag("40020200", "0906123456789001".ToHexBytes(), -1);
    this.AddDltTag("40030200", "0906111122223333".ToHexBytes(), -1);
    this.AddDltTag("41000200", this.CreateDltBuffer((short) 1234), 0);
    this.AddDltTag("41010200", this.CreateDltBuffer((short) 2234), 0);
    this.AddDltTag("41020200", this.CreateDltBuffer((short) 100), 0);
    this.AddDltTag("41030200", this.CreateDltBuffer("1".PadLeft(32 /*0x20*/, '2')), 0);
    this.AddDltTag("41040200", this.CreateDltBuffer("5.0000"), 0);
    this.AddDltTag("41050200", this.CreateDltBuffer("5.1200"), 0);
    this.AddDltTag("41060200", this.CreateDltBuffer("10.200"), 0);
    this.AddDltTag("41070200", this.CreateDltBuffer("1230"), 0);
    this.AddDltTag("41080200", this.CreateDltBuffer("1230"), 0);
    this.AddDltTag("41090200", this.CreateDltBuffer(1234567U), 0);
    this.AddDltTag("410A0200", this.CreateDltBuffer(2234567U), 0);
    this.AddDltTag("410B0200", this.CreateDltBuffer("HslCommunication".PadRight(32 /*0x20*/, ' ')), 0);
  }

  private byte[] CreateDltBufferHelper<T>(T[] values, byte typeCode, Func<T, byte[]> func)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 1);
    ms.WriteByte((byte) values.Length);
    for (int index = 0; index < values.Length; ++index)
    {
      ms.WriteByte(typeCode);
      ms.Write(func(values[index]));
    }
    return ms.ToArray();
  }

  private byte[] CreateDltBuffer(short value)
  {
    byte[] dltBuffer = new byte[3]
    {
      (byte) 16 /*0x10*/,
      (byte) 0,
      (byte) 0
    };
    this.ByteTransform.TransByte(value).CopyTo((Array) dltBuffer, 1);
    return dltBuffer;
  }

  private byte[] CreateDltBuffer(ushort value)
  {
    byte[] dltBuffer = new byte[3]
    {
      (byte) 18,
      (byte) 0,
      (byte) 0
    };
    this.ByteTransform.TransByte(value).CopyTo((Array) dltBuffer, 1);
    return dltBuffer;
  }

  private byte[] CreateDltBuffer(int value)
  {
    byte[] dltBuffer = new byte[5]
    {
      (byte) 5,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0
    };
    this.ByteTransform.TransByte(value).CopyTo((Array) dltBuffer, 1);
    return dltBuffer;
  }

  private byte[] CreateDltBuffer(uint value)
  {
    byte[] dltBuffer = new byte[5]
    {
      (byte) 6,
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 0
    };
    this.ByteTransform.TransByte(value).CopyTo((Array) dltBuffer, 1);
    return dltBuffer;
  }

  private byte[] CreateDltBuffer(string value)
  {
    byte[] numArray = string.IsNullOrEmpty(value) ? new byte[0] : Encoding.ASCII.GetBytes(value);
    byte[] dltBuffer = new byte[numArray.Length + 2];
    dltBuffer[0] = (byte) 10;
    dltBuffer[1] = (byte) numArray.Length;
    numArray.CopyTo((Array) dltBuffer, 2);
    return dltBuffer;
  }

  private byte[] CreateDltBuffer(int[] values)
  {
    return this.CreateDltBufferHelper<int>(values, (byte) 5, new Func<int, byte[]>(this.ByteTransform.TransByte));
  }

  private byte[] CreateDltBuffer(uint[] values)
  {
    return this.CreateDltBufferHelper<uint>(values, (byte) 6, new Func<uint, byte[]>(this.ByteTransform.TransByte));
  }

  private byte[] CreateDltBuffer(ushort[] values)
  {
    return this.CreateDltBufferHelper<ushort>(values, (byte) 18, new Func<ushort, byte[]>(this.ByteTransform.TransByte));
  }

  /// <inheritdoc />
  public override string Station
  {
    get => base.Station;
    set
    {
      base.Station = value;
      value.PadLeft(12, '0').ToHexBytes().CopyTo((Array) this.allDatas["40010200"].Buffer, 2);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double)" />
  [HslMqttApi("WriteDouble", "")]
  public override OperateResult Write(string address, double value)
  {
    return this.Write(address, new double[1]{ value });
  }

  /// <inheritdoc />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    byte[] hexBytes = address.ToHexBytes();
    address = hexBytes.ToHexString();
    int scale = DLT698Helper.GetScale(hexBytes[0], hexBytes[1], hexBytes[2]);
    if (scale < 0)
      scale = -scale;
    if (this.allDatas.ContainsKey(address))
      this.SetDoubleValue(this.allDatas[address].Buffer, 0, values, scale);
    return OperateResult.CreateSuccessResult();
  }

  private void SetDoubleValue(byte[] buffer, int index, double[] values, int scale)
  {
    if (index >= buffer.Length || buffer[index] == (byte) 0)
      return;
    if (buffer[index] == (byte) 1)
    {
      int num1 = (int) buffer[index + 1];
      int num2 = 2;
      for (int index1 = 0; index1 < num1; ++index1)
      {
        if (index1 < values.Length)
        {
          int num3 = this.SetDoubleValue(buffer, index + num2, values[index1], scale);
          num2 += num3;
          if (num3 == 0)
            break;
        }
      }
    }
    else
      this.SetDoubleValue(buffer, index, values[0], scale);
  }

  private int SetDoubleValue(byte[] buffer, int index, double value, int scale)
  {
    if (buffer.Length == 0 || index >= buffer.Length)
      return 0;
    if (buffer[index] == (byte) 18)
    {
      this.ByteTransform.TransByte((ushort) (value * Math.Pow(10.0, (double) scale))).CopyTo((Array) buffer, index + 1);
      return 3;
    }
    if (buffer[index] == (byte) 5)
    {
      this.ByteTransform.TransByte((int) (value * Math.Pow(10.0, (double) scale))).CopyTo((Array) buffer, index + 1);
      return 5;
    }
    if (buffer[index] == (byte) 6)
    {
      this.ByteTransform.TransByte((uint) (value * Math.Pow(10.0, (double) scale))).CopyTo((Array) buffer, index + 1);
      return 5;
    }
    if (buffer[index] == (byte) 15)
    {
      sbyte num = (sbyte) (value * Math.Pow(10.0, (double) scale));
      buffer[index + 1] = (byte) num;
      return 2;
    }
    if (buffer[index] == (byte) 16 /*0x10*/)
    {
      this.ByteTransform.TransByte((short) (value * Math.Pow(10.0, (double) scale))).CopyTo((Array) buffer, index + 1);
      return 3;
    }
    if (buffer[index] == (byte) 17)
    {
      byte num = (byte) (value * Math.Pow(10.0, (double) scale));
      buffer[index + 1] = num;
      return 2;
    }
    if (buffer[index] == (byte) 20)
    {
      this.ByteTransform.TransByte((long) (value * Math.Pow(10.0, (double) scale))).CopyTo((Array) buffer, index + 1);
      return 9;
    }
    if (buffer[index] == (byte) 21)
    {
      this.ByteTransform.TransByte((ulong) (value * Math.Pow(10.0, (double) scale))).CopyTo((Array) buffer, index + 1);
      return 9;
    }
    if (buffer[index] == (byte) 23)
    {
      this.ByteTransform.TransByte((float) (value * Math.Pow(10.0, (double) scale))).CopyTo((Array) buffer, index + 1);
      return 5;
    }
    if (buffer[index] != (byte) 24)
      return 0;
    this.ByteTransform.TransByte(value * Math.Pow(10.0, (double) scale)).CopyTo((Array) buffer, index + 1);
    return 9;
  }

  /// <inheritdoc />
  public override OperateResult<string[]> ReadStringArray(string address)
  {
    byte[] hexBytes = address.ToHexBytes();
    address = hexBytes.ToHexString();
    if (!this.allDatas.ContainsKey(address))
      return new OperateResult<string[]>(StringResources.Language.NotSupportedDataType);
    byte[] response = SoftBasic.SpliceArray<byte>(new byte[8], this.allDatas[address].Buffer);
    hexBytes.CopyTo((Array) response, 3);
    int index = 8;
    return OperateResult.CreateSuccessResult<string[]>(DLT698Helper.ExtraStringsValues(this.ByteTransform, response, ref index));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    byte[] hexBytes = address.ToHexBytes();
    address = hexBytes.ToHexString();
    byte err = 0;
    if (this.allDatas.ContainsKey(address))
    {
      if (this.allDatas[address].Buffer[0] == (byte) 28)
      {
        err = this.WriteBufferToAddress(hexBytes, DLT698Helper.CreateDateTimeValue(DateTime.Parse(value)));
        if (err > (byte) 0)
          return new OperateResult((int) err, DLT698Helper.GetErrorText(err));
      }
      else if (this.allDatas[address].Buffer[0] == (byte) 9)
      {
        err = this.WriteBufferToAddress(hexBytes, DLT698Helper.CreateStringValueBuffer(value));
        if (err > (byte) 0)
          return new OperateResult((int) err, DLT698Helper.GetErrorText(err));
      }
      else if (this.allDatas[address].Buffer[0] == (byte) 10)
      {
        byte[] numArray = encoding.GetBytes(value);
        if (numArray.Length > this.allDatas[address].Buffer.Length - 2)
          numArray = numArray.SelectBegin<byte>(this.allDatas[address].Buffer.Length - 2);
        numArray.CopyTo((Array) this.allDatas[address].Buffer, 2);
      }
    }
    else
      err = (byte) 6;
    return err == (byte) 0 ? OperateResult.CreateSuccessResult() : new OperateResult((int) err, DLT698Helper.GetErrorText(err));
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new DLT698Message();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromDLTCore(byte[] receive)
  {
    ((IEnumerable<byte>) receive.SelectMiddle<byte>(5, ((int) receive[4] & 15) + 1)).Reverse<byte>().ToArray<byte>().ToHexString();
    int leftLength = 5 + ((int) receive[4] & 15) + 1 + 3;
    byte ca = 16 /*0x10*/;
    if (receive[3] == (byte) 129)
    {
      if (receive[leftLength] == (byte) 1)
      {
        if (receive[leftLength + 2] == (byte) 0 || receive[leftLength + 2] == (byte) 1)
          return DLT698Helper.BuildEntireCommand((byte) 1, this.Station, ca, DLT698Helper.CreatePreLogin(receive.SelectMiddle<byte>(leftLength + 5, 10)));
      }
      else if (receive[leftLength] == (byte) 2)
        return DLT698Helper.BuildEntireCommand((byte) 1, this.Station, ca, "82 00 54 4F 50 53 30 31 30 32 31 36 30 37 33 31 30 31 30 32 31 36 30 37 33 31 00 00 00 00 00 00 00 00 00 10 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF 04 00 04 00 01 04 00 00 00 00 64 00 00 00 00".ToHexBytes());
    }
    else if (receive[3] == (byte) 67)
    {
      byte[] response = receive.RemoveDouble<byte>(leftLength, 2);
      bool security = false;
      if (response[0] == (byte) 16 /*0x10*/)
      {
        security = true;
        response = response.SelectMiddle<byte>(3, (int) response[2]);
      }
      if (response[0] == (byte) 5)
      {
        if (response[1] == (byte) 1)
        {
          byte[] numArray = response.SelectMiddle<byte>(3, 4);
          string hexString = numArray.ToHexString();
          return this.allDatas.ContainsKey(hexString) ? DLT698Helper.BuildEntireCommand((byte) 3, this.Station, ca, this.CreateReadOneObject(numArray, response[2], this.allDatas[hexString].Buffer, security)) : DLT698Helper.BuildEntireCommand((byte) 3, this.Station, ca, this.CreateErrorResponse((byte) 133, numArray, response[2], (byte) 6, security));
        }
        if (response[1] == (byte) 2)
        {
          int count = (int) response[3];
          MemoryStream ms = new MemoryStream();
          for (int index = 0; index < count; ++index)
          {
            byte[] numArray = response.SelectMiddle<byte>(4 + index * 4, 4);
            string hexString = numArray.ToHexString();
            if (!this.allDatas.ContainsKey(hexString))
              return DLT698Helper.BuildEntireCommand((byte) 3, this.Station, ca, this.CreateErrorResponse((byte) 133, numArray, response[2], (byte) 6, security));
            ms.Write(numArray);
            ms.WriteByte((byte) 1);
            ms.Write(this.allDatas[hexString].Buffer);
          }
          return DLT698Helper.BuildEntireCommand((byte) 3, this.Station, ca, this.CreateReadMultiObject(response[2], count, ms.ToArray(), security));
        }
      }
      else if (response[0] == (byte) 6)
      {
        if (response[1] == (byte) 1)
        {
          byte[] numArray = response.SelectMiddle<byte>(3, 4);
          if (this.allDatas.ContainsKey(numArray.ToHexString()))
          {
            int index = 7;
            DLT698Helper.ExtraStringsValues(this.ByteTransform, response, ref index);
            byte[] buffer = response.SelectMiddle<byte>(7, index - 7);
            this.WriteBufferToAddress(numArray, buffer);
            return DLT698Helper.BuildEntireCommand((byte) 3, this.Station, ca, this.CreateErrorResponse((byte) 134, numArray, response[2], (byte) 0, security));
          }
        }
      }
      else if (response[0] != (byte) 136)
        ;
    }
    return new OperateResult<byte[]>("");
  }

  private byte WriteBufferToAddress(byte[] addressBytes, byte[] buffer)
  {
    string hexString = addressBytes.ToHexString();
    if (!this.allDatas.ContainsKey(hexString))
      return 6;
    if (buffer == null || buffer.Length == 0)
      return 9;
    if ((int) this.allDatas[hexString].Buffer[0] != (int) buffer[0])
      return 7;
    if (this.allDatas[hexString].Buffer.Length != buffer.Length)
      return 9;
    buffer.CopyTo((Array) this.allDatas[hexString].Buffer, 0);
    return 0;
  }

  private byte[] CreateErrorResponse(
    byte code,
    byte[] address,
    byte piid,
    byte error,
    bool security)
  {
    byte[] data = new byte[error != (byte) 0 ? 11 : 10];
    data[0] = code;
    data[1] = (byte) 1;
    data[2] = piid;
    address.CopyTo((Array) data, 3);
    data[7] = (byte) 0;
    if (error > (byte) 0)
      data[8] = error;
    return security ? this.CreateSecurityResponse(data) : data;
  }

  private byte[] CreateSecurityResponse(byte[] data)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 144 /*0x90*/);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) data.Length);
    ms.Write(data);
    ms.WriteByte((byte) 1);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 4);
    ms.WriteByte((byte) 18);
    ms.WriteByte((byte) 52);
    ms.WriteByte((byte) 86);
    ms.WriteByte((byte) 120);
    return ms.ToArray();
  }

  private byte[] CreateReadOneObject(byte[] address, byte piid, byte[] data, bool security)
  {
    byte[] data1 = new byte[10 + data.Length];
    data1[0] = (byte) 133;
    data1[1] = (byte) 1;
    data1[2] = piid;
    address.CopyTo((Array) data1, 3);
    data1[7] = (byte) 1;
    data.CopyTo((Array) data1, 8);
    return security ? this.CreateSecurityResponse(data1) : data1;
  }

  private byte[] CreateReadMultiObject(byte piid, int count, byte[] data, bool security)
  {
    byte[] data1 = new byte[6 + data.Length];
    data1[0] = (byte) 133;
    data1[1] = (byte) 2;
    data1[2] = piid;
    data1[3] = (byte) count;
    data.CopyTo((Array) data1, 4);
    return security ? this.CreateSecurityResponse(data1) : data1;
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    MemoryStream ms = new MemoryStream();
    ms.Write(buffer.SelectBegin<byte>(receivedLength));
    return DLT645Helper.CheckReceiveDataComplete(ms);
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (!disposing)
      ;
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"DLT698Server[{this.Port}]";
}
