﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.FinsMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>用于欧姆龙通信的Fins协议的消息解析规则</summary>
public class FinsMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 16 /*0x10*/;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    int num = BitConverter.ToInt32(new byte[4]
    {
      this.HeadBytes[7],
      this.HeadBytes[6],
      this.HeadBytes[5],
      this.HeadBytes[4]
    }, 0);
    if (num > 10000)
      num = 10000;
    if (num < 8)
      num = 8;
    return num - 8;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes == null || this.HeadBytes[0] == (byte) 70 && this.HeadBytes[1] == (byte) 73 && this.HeadBytes[2] == (byte) 78 && this.HeadBytes[3] == (byte) 83;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.PependedUselesByteLength(System.Byte[])" />
  public override int PependedUselesByteLength(byte[] headByte)
  {
    if (headByte == null)
      return 0;
    for (int index = 0; index < headByte.Length - 3; ++index)
    {
      if (headByte[index] == (byte) 70 && headByte[index + 1] == (byte) 73 && headByte[index + 2] == (byte) 78 && headByte[index + 3] == (byte) 83)
        return index;
    }
    return base.PependedUselesByteLength(headByte);
  }

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    if (send == null || receive == null)
      return 1;
    if (send.Length <= 25 || receive.Length <= 25)
      return base.CheckMessageMatch(send, receive);
    return (int) send[25] == (int) receive[25] ? 1 : -1;
  }
}
