﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecFxSerialServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Reflection;
using System;
using System.Text;
using System.Threading;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱编程口协议的虚拟服务器，属性<see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialServer.IsNewVersion" />的配置和客户端保持一致才能数据一致<br />
/// Mitsubishi Programming interface protocol virtual server, attribute <see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialServer.IsNewVersion" /> and the client configuration must be consistent data consistency
/// </summary>
public class MelsecFxSerialServer : DeviceServer
{
  private SoftBuffer softBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个虚拟的FxLinks服务器<br />
  /// Instantiate a virtual FxLinks server
  /// </summary>
  public MelsecFxSerialServer()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.LogMsgFormatBinary = false;
    this.softBuffer = new SoftBuffer(131072 /*0x020000*/);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.IsNewVersion" />
  public bool IsNewVersion { get; set; }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<ushort> wordStartAddress = MelsecFxSerialHelper.FxCalculateWordStartAddress(address, this.IsNewVersion);
    return !wordStartAddress.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) wordStartAddress) : OperateResult.CreateSuccessResult<byte[]>(this.softBuffer.GetBytes((int) wordStartAddress.Content, (int) length * 2));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<ushort> wordStartAddress = MelsecFxSerialHelper.FxCalculateWordStartAddress(address, this.IsNewVersion);
    if (!wordStartAddress.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) wordStartAddress);
    this.softBuffer.SetBytes(value, (int) wordStartAddress.Content);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<ushort, ushort, ushort> boolStartAddress = MelsecFxSerialHelper.FxCalculateBoolStartAddress(address, this.IsNewVersion);
    return !boolStartAddress.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) boolStartAddress) : OperateResult.CreateSuccessResult<bool[]>(this.softBuffer.GetBool((int) boolStartAddress.Content1 * 8 + (int) boolStartAddress.Content3, (int) length));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<ushort, ushort, ushort> boolStartAddress = MelsecFxSerialHelper.FxCalculateBoolStartAddress(address, this.IsNewVersion);
    if (!boolStartAddress.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) boolStartAddress);
    this.softBuffer.SetBool(value, (int) boolStartAddress.Content1 * 8 + (int) boolStartAddress.Content3);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) null;

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length == 1 && receive[0] == (byte) 5)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[1]
      {
        (byte) 6
      });
    if (receive[1] == (byte) 48 /*0x30*/)
      return this.ReadByCommand(receive, 2);
    if (receive[1] == (byte) 49)
      return this.WriteByCommand(receive, 2);
    if (receive[1] == (byte) 55)
      return this.WriteBitByCommand(receive, 2, true);
    if (receive[1] == (byte) 56)
      return this.WriteBitByCommand(receive, 2, false);
    if (receive[1] == (byte) 69)
    {
      if (receive[2] == (byte) 48 /*0x30*/)
        return this.ReadByCommand(receive, 4);
      if (receive[2] == (byte) 49)
        return this.WriteByCommand(receive, 4);
    }
    else if (receive[1] == (byte) 65)
    {
      session.Communication.Send(new byte[1]{ (byte) 6 });
      if (session.Communication is PipeSerialPort communication)
      {
        Thread.Sleep(20);
        if (receive[2] == (byte) 49)
          communication.GetPipe().BaudRate = 19200;
        else if (receive[2] == (byte) 50)
          communication.GetPipe().BaudRate = 38400;
        else if (receive[2] == (byte) 51)
          communication.GetPipe().BaudRate = 57600;
        else if (receive[2] == (byte) 52)
          communication.GetPipe().BaudRate = 115200;
      }
    }
    return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
  }

  private OperateResult<byte[]> ReadByCommand(byte[] cmd, int offset)
  {
    return OperateResult.CreateSuccessResult<byte[]>(this.BuildReadResponse(this.softBuffer.GetBytes(Convert.ToInt32(Encoding.ASCII.GetString(cmd, offset, 4), 16 /*0x10*/), Convert.ToInt32(Encoding.ASCII.GetString(cmd, offset + 4, 2), 16 /*0x10*/))));
  }

  private OperateResult<byte[]> WriteByCommand(byte[] cmd, int offset)
  {
    int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(cmd, offset, 4), 16 /*0x10*/);
    int int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(cmd, offset + 4, 2), 16 /*0x10*/);
    if (int32_2 * 2 != cmd.Length - offset - 9)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    byte[] data = new byte[int32_2];
    for (int index = 0; index < data.Length; ++index)
    {
      int int32_3 = Convert.ToInt32(Encoding.ASCII.GetString(cmd, 6 + offset + index * 2, 2), 16 /*0x10*/);
      data[index] = (byte) int32_3;
    }
    this.softBuffer.SetBytes(data, int32_1);
    return OperateResult.CreateSuccessResult<byte[]>(new byte[2]
    {
      (byte) 6,
      (byte) 3
    });
  }

  private OperateResult<byte[]> WriteBitByCommand(byte[] cmd, int offset, bool value)
  {
    int destIndex = Convert.ToInt32(Encoding.ASCII.GetString(new byte[4]
    {
      cmd[offset + 2],
      cmd[offset + 3],
      cmd[offset],
      cmd[offset + 1]
    }), 16 /*0x10*/);
    if (this.IsNewVersion)
    {
      if (destIndex >= 2048 /*0x0800*/ && destIndex < 10048)
        destIndex = 278528 /*0x044000*/ + (destIndex - 2048 /*0x0800*/);
      else if (destIndex >= 1024 /*0x0400*/ && destIndex < 1280 /*0x0500*/)
        destIndex = 288000 + (destIndex - 1024 /*0x0400*/);
      else if (destIndex >= 1280 /*0x0500*/ && destIndex < 1536 /*0x0600*/)
        destIndex = 286208 + (destIndex - 1280 /*0x0500*/);
      else if (destIndex >= 0 && destIndex < 1024 /*0x0400*/)
        destIndex = 288512 + destIndex;
    }
    this.softBuffer.SetBool(value, destIndex);
    return OperateResult.CreateSuccessResult<byte[]>(new byte[2]
    {
      (byte) 6,
      (byte) 3
    });
  }

  private byte[] BuildReadResponse(byte[] data)
  {
    byte[] data1 = new byte[4 + data.Length * 2];
    data1[0] = (byte) 2;
    for (int index = 0; index < data.Length; ++index)
    {
      data1[1 + 2 * index] = SoftBasic.BuildAsciiBytesFrom(data[index])[0];
      data1[2 + 2 * index] = SoftBasic.BuildAsciiBytesFrom(data[index])[1];
    }
    data1[data1.Length - 3] = (byte) 3;
    MelsecHelper.FxCalculateCRC(data1).CopyTo((Array) data1, data1.Length - 2);
    return data1;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecFxSerialServer[{this.Port}]";
}
