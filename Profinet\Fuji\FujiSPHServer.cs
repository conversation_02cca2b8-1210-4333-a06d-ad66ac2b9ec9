﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Fuji.FujiSPHServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;

#nullable disable
namespace HslCommunication.Profinet.Fuji;

/// <summary>
/// 富士的SPH虚拟的PLC，支持M1.0，M3.0，M10.0，I0，Q0的位与字的读写操作。<br />
/// </summary>
public class FujiSPHServer : DeviceServer
{
  private SoftBuffer m1Buffer;
  private SoftBuffer m3Buffer;
  private SoftBuffer m10Buffer;
  private SoftBuffer iqBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个基于SPH协议的虚拟的富士PLC对象，可以用来和<see cref="T:HslCommunication.Profinet.Fuji.FujiSPHNet" />进行通信测试。
  /// </summary>
  public FujiSPHServer()
  {
    this.m1Buffer = new SoftBuffer(131072 /*0x020000*/);
    this.m3Buffer = new SoftBuffer(131072 /*0x020000*/);
    this.m10Buffer = new SoftBuffer(131072 /*0x020000*/);
    this.iqBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] bytes = new byte[524288 /*0x080000*/];
    this.m1Buffer.GetBytes().CopyTo((Array) bytes, 0);
    this.m3Buffer.GetBytes().CopyTo((Array) bytes, 131072 /*0x020000*/);
    this.m10Buffer.GetBytes().CopyTo((Array) bytes, 262144 /*0x040000*/);
    this.iqBuffer.GetBytes().CopyTo((Array) bytes, 393216 /*0x060000*/);
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 524288 /*0x080000*/)
      throw new Exception("File is not correct");
    this.m1Buffer.SetBytes(content, 0, 131072 /*0x020000*/);
    this.m3Buffer.SetBytes(content, 131072 /*0x020000*/, 131072 /*0x020000*/);
    this.m10Buffer.SetBytes(content, 262144 /*0x040000*/, 131072 /*0x020000*/);
    this.iqBuffer.SetBytes(content, 393216 /*0x060000*/, 131072 /*0x020000*/);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPHNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<FujiSPHAddress> from = FujiSPHAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return from.ConvertFailed<byte[]>();
    switch (from.Content.TypeCode)
    {
      case 1:
        return OperateResult.CreateSuccessResult<byte[]>(this.iqBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
      case 2:
        return OperateResult.CreateSuccessResult<byte[]>(this.m1Buffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
      case 4:
        return OperateResult.CreateSuccessResult<byte[]>(this.m3Buffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
      case 8:
        return OperateResult.CreateSuccessResult<byte[]>(this.m10Buffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
      default:
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPHNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<FujiSPHAddress> from = FujiSPHAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) from.ConvertFailed<byte[]>();
    switch (from.Content.TypeCode)
    {
      case 1:
        this.iqBuffer.SetBytes(value, from.Content.AddressStart * 2);
        break;
      case 2:
        this.m1Buffer.SetBytes(value, from.Content.AddressStart * 2);
        break;
      case 4:
        this.m3Buffer.SetBytes(value, from.Content.AddressStart * 2);
        break;
      case 8:
        this.m10Buffer.SetBytes(value, from.Content.AddressStart * 2);
        break;
      default:
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<FujiSPHAddress> from = FujiSPHAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return from.ConvertFailed<bool[]>();
    int num = from.Content.BitIndex + (int) length;
    int length1 = num % 16 /*0x10*/ == 0 ? num / 16 /*0x10*/ : num / 16 /*0x10*/ + 1;
    OperateResult<byte[]> operateResult = this.Read(address, (ushort) length1);
    return !operateResult.IsSuccess ? operateResult.ConvertFailed<bool[]>() : OperateResult.CreateSuccessResult<bool[]>(operateResult.Content.ToBoolArray().SelectMiddle<bool>(from.Content.BitIndex, (int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<FujiSPHAddress> from = FujiSPHAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) from.ConvertFailed<bool[]>();
    switch (from.Content.TypeCode)
    {
      case 1:
        this.iqBuffer.SetBool(value, from.Content.AddressStart * 16 /*0x10*/ + from.Content.BitIndex);
        break;
      case 2:
        this.m1Buffer.SetBool(value, from.Content.AddressStart * 16 /*0x10*/ + from.Content.BitIndex);
        break;
      case 4:
        this.m3Buffer.SetBool(value, from.Content.AddressStart * 16 /*0x10*/ + from.Content.BitIndex);
        break;
      case 8:
        this.m10Buffer.SetBool(value, from.Content.AddressStart * 16 /*0x10*/ + from.Content.BitIndex);
        break;
      default:
        return new OperateResult(StringResources.Language.NotSupportedDataType);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new FujiSPHMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return receive[0] != (byte) 251 || receive[1] != (byte) 128 /*0x80*/ ? new OperateResult<byte[]>("Command must start with 0xfb 0x80") : OperateResult.CreateSuccessResult<byte[]>(this.ReadFromSPBCore(receive));
  }

  private byte[] PackCommand(byte[] cmd, byte err, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    byte[] destinationArray = new byte[26 + data.Length];
    destinationArray[0] = (byte) 251;
    destinationArray[1] = (byte) 128 /*0x80*/;
    destinationArray[2] = (byte) 128 /*0x80*/;
    destinationArray[3] = (byte) 0;
    destinationArray[4] = err;
    destinationArray[5] = (byte) 123;
    destinationArray[6] = cmd[6];
    destinationArray[7] = (byte) 0;
    destinationArray[8] = (byte) 17;
    destinationArray[9] = (byte) 0;
    destinationArray[10] = (byte) 0;
    destinationArray[11] = (byte) 0;
    destinationArray[12] = (byte) 0;
    destinationArray[13] = (byte) 0;
    destinationArray[14] = cmd[14];
    destinationArray[15] = cmd[15];
    destinationArray[16 /*0x10*/] = (byte) 0;
    destinationArray[17] = (byte) 1;
    destinationArray[18] = BitConverter.GetBytes(data.Length + 6)[0];
    destinationArray[19] = BitConverter.GetBytes(data.Length + 6)[1];
    Array.Copy((Array) cmd, 20, (Array) destinationArray, 20, 6);
    if (data.Length != 0)
      data.CopyTo((Array) destinationArray, 26);
    return destinationArray;
  }

  private byte[] ReadFromSPBCore(byte[] receive)
  {
    if (receive.Length < 20)
      return this.PackCommand(receive, (byte) 16 /*0x10*/, (byte[]) null);
    if (receive[14] == (byte) 0 && receive[15] == (byte) 0)
      return this.ReadByCommand(receive);
    return receive[14] == (byte) 1 && receive[15] == (byte) 0 ? this.WriteByCommand(receive) : this.PackCommand(receive, (byte) 32 /*0x20*/, (byte[]) null);
  }

  private byte[] ReadByCommand(byte[] command)
  {
    try
    {
      byte num1 = command[20];
      int num2 = (int) command[23] * 256 /*0x0100*/ * 256 /*0x0100*/ + (int) command[22] * 256 /*0x0100*/ + (int) command[21];
      int num3 = (int) command[25] * 256 /*0x0100*/ + (int) command[24];
      if (num2 + num3 > (int) ushort.MaxValue)
        return this.PackCommand(command, (byte) 69, (byte[]) null);
      switch (num1)
      {
        case 1:
          return this.PackCommand(command, (byte) 0, this.iqBuffer.GetBytes(num2 * 2, num3 * 2));
        case 2:
          return this.PackCommand(command, (byte) 0, this.m1Buffer.GetBytes(num2 * 2, num3 * 2));
        case 4:
          return this.PackCommand(command, (byte) 0, this.m3Buffer.GetBytes(num2 * 2, num3 * 2));
        case 8:
          return this.PackCommand(command, (byte) 0, this.m10Buffer.GetBytes(num2 * 2, num3 * 2));
        default:
          return this.PackCommand(command, (byte) 64 /*0x40*/, (byte[]) null);
      }
    }
    catch
    {
      return this.PackCommand(command, (byte) 164, (byte[]) null);
    }
  }

  private byte[] WriteByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommand(command, (byte) 16 /*0x10*/, (byte[]) null);
    try
    {
      byte num1 = command[20];
      int num2 = (int) command[23] * 256 /*0x0100*/ * 256 /*0x0100*/ + (int) command[22] * 256 /*0x0100*/ + (int) command[21];
      int num3 = (int) command[25] * 256 /*0x0100*/ + (int) command[24];
      byte[] data = command.RemoveBegin<byte>(26);
      if (num2 + num3 > (int) ushort.MaxValue)
        return this.PackCommand(command, (byte) 69, (byte[]) null);
      if (num3 * 2 != data.Length)
        return this.PackCommand(command, (byte) 69, (byte[]) null);
      switch (num1)
      {
        case 1:
          this.iqBuffer.SetBytes(data, num2 * 2);
          return this.PackCommand(command, (byte) 0, (byte[]) null);
        case 2:
          this.m1Buffer.SetBytes(data, num2 * 2);
          return this.PackCommand(command, (byte) 0, (byte[]) null);
        case 4:
          this.m3Buffer.SetBytes(data, num2 * 2);
          return this.PackCommand(command, (byte) 0, (byte[]) null);
        case 8:
          this.m10Buffer.SetBytes(data, num2 * 2);
          return this.PackCommand(command, (byte) 0, (byte[]) null);
        default:
          return this.PackCommand(command, (byte) 64 /*0x40*/, (byte[]) null);
      }
    }
    catch
    {
      return this.PackCommand(command, (byte) 164, (byte[]) null);
    }
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.m1Buffer.Dispose();
      this.m3Buffer.Dispose();
      this.m10Buffer.Dispose();
      this.iqBuffer.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"FujiSPHServer[{this.Port}]";
}
