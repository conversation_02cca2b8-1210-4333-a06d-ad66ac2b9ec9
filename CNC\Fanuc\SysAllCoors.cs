﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.SysAllCoors
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>系统的坐标信息</summary>
public class SysAllCoors
{
  /// <summary>绝对坐标</summary>
  public double[] Absolute { get; set; }

  /// <summary>机械坐标</summary>
  public double[] Machine { get; set; }

  /// <summary>相对坐标</summary>
  public double[] Relative { get; set; }
}
