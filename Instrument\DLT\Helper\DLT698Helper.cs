﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.Helper.DLT698Helper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.DLT.Helper;

/// <summary>698 协议的帮助类</summary>
public class DLT698Helper
{
  /// <summary>预连接请求</summary>
  public const byte LinkRequest = 1;
  /// <summary>建立应用连接请求</summary>
  public const byte ConnectRequest = 2;
  /// <summary>断开应用连接请求</summary>
  public const byte ReleaseRequest = 3;
  /// <summary>读取请求</summary>
  public const byte GetRequest = 5;
  /// <summary>设置请求</summary>
  public const byte SetRequest = 6;
  /// <summary>操作请求</summary>
  public const byte ActionRequest = 7;
  /// <summary>操作请求</summary>
  public const byte ReportRequest = 8;
  /// <summary>代理请求</summary>
  public const byte ReportResponse = 9;
  /// <summary>安全请求</summary>
  public const byte SecurityResquest = 16 /*0x10*/;
  /// <summary>预连接响应</summary>
  public const byte LinkResponse = 129;
  /// <summary>建立应用连接响应</summary>
  public const byte ConnectResponse = 130;
  /// <summary>断开应用连接响应</summary>
  public const byte ReleaseResponse = 131;
  /// <summary>断开应用连接通知</summary>
  public const byte ReleaseNotification = 132;
  /// <summary>读取响应</summary>
  public const byte GetResponse = 133;
  /// <summary>设置响应</summary>
  public const byte SetResponse = 134;
  /// <summary>操作响应</summary>
  public const byte ActionResponse = 135;
  /// <summary>上报通知</summary>
  public const byte ReportNotification = 136;
  /// <summary>代理响应</summary>
  public const byte ProxyResponse = 137;
  /// <summary>安全响应</summary>
  public const byte SecurityResponse = 144 /*0x90*/;

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.PackCommandWithHeader(System.Byte[])" />
  public static byte[] PackCommandWithHeader(IDlt698 dlt, byte[] command)
  {
    if (!dlt.EnableCodeFE)
      return command;
    return SoftBasic.SpliceArray<byte>(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, command);
  }

  /// <summary>根据地址类型，逻辑地址，实际的地址信息构建出真实的地址报文</summary>
  /// <param name="addressType">地址类型，0：单地址, 1：通配地址，2：组地址，3：广播地址</param>
  /// <param name="logicAddress">逻辑地址</param>
  /// <param name="address">地址信息</param>
  /// <param name="ca">客户机地址</param>
  /// <returns>原始字节信息</returns>
  private static byte[] CalculateAddressArea(
    int addressType,
    int logicAddress,
    string address,
    byte ca)
  {
    if (address.Length % 2 == 1)
      address += "F";
    if (logicAddress > 3)
      logicAddress = 3;
    byte[] addressArea = new byte[2 + address.Length / 2];
    addressArea[0] = (byte) (addressType << 6 | logicAddress << 4 | address.Length / 2 - 1);
    ((IEnumerable<byte>) address.ToHexBytes()).Reverse<byte>().ToArray<byte>().CopyTo((Array) addressArea, 1);
    addressArea[addressArea.Length - 1] = ca;
    return addressArea;
  }

  internal static byte[] CreateStringValueBuffer(string value)
  {
    if (value.Length % 2 == 1)
      value += "F";
    byte[] hexBytes = value.ToHexBytes();
    byte[] stringValueBuffer = new byte[hexBytes.Length + 2];
    stringValueBuffer[0] = (byte) 9;
    stringValueBuffer[1] = (byte) hexBytes.Length;
    hexBytes.CopyTo((Array) stringValueBuffer, 2);
    return stringValueBuffer;
  }

  internal static byte[] CreateDateTimeValue(DateTime time)
  {
    return new byte[8]
    {
      (byte) 28,
      BitConverter.GetBytes(time.Year)[1],
      BitConverter.GetBytes(time.Year)[0],
      (byte) time.Month,
      (byte) time.Day,
      (byte) time.Hour,
      (byte) time.Minute,
      (byte) time.Second
    };
  }

  internal static byte[] CreatePreLogin(byte[] time)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 129);
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 128 /*0x80*/);
    ms.Write(time);
    ms.Write(DLT698Helper.CreateDateTimeWithMs(DateTime.Now));
    ms.Write(DLT698Helper.CreateDateTimeWithMs(DateTime.Now));
    return ms.ToArray();
  }

  /// <summary>将指定的地址信息，控制码信息，数据域信息打包成完整的报文命令</summary>
  /// <param name="control">控制码信息</param>
  /// <param name="sa">服务器的地址</param>
  /// <param name="ca">客户机地址</param>
  /// <param name="apdu">链路用户数据</param>
  /// <returns>返回是否报文创建成功</returns>
  public static OperateResult<byte[]> BuildEntireCommand(
    byte control,
    string sa,
    byte ca,
    byte[] apdu)
  {
    int addressType = 0;
    if (sa == "AA")
      addressType = 3;
    else if (sa.Contains("A"))
      addressType = 1;
    byte[] addressArea = DLT698Helper.CalculateAddressArea(addressType, 0, sa, ca);
    int num1 = 0;
    byte[] buffer = new byte[4 + addressArea.Length + 2 + apdu.Length + 2 + 1];
    byte[] numArray1 = buffer;
    int index1 = num1;
    int num2 = index1 + 1;
    numArray1[index1] = (byte) 104;
    byte[] numArray2 = buffer;
    int index2 = num2;
    int num3 = index2 + 1;
    int num4 = (int) BitConverter.GetBytes(buffer.Length - 2)[0];
    numArray2[index2] = (byte) num4;
    byte[] numArray3 = buffer;
    int index3 = num3;
    int num5 = index3 + 1;
    int num6 = (int) BitConverter.GetBytes(buffer.Length - 2)[1];
    numArray3[index3] = (byte) num6;
    byte[] numArray4 = buffer;
    int index4 = num5;
    int index5 = index4 + 1;
    int num7 = (int) control;
    numArray4[index4] = (byte) num7;
    addressArea.CopyTo((Array) buffer, index5);
    int index6 = index5 + addressArea.Length;
    DLT698FcsHelper.CalculateFcs16(buffer, 1, index6 - 1).CopyTo((Array) buffer, index6);
    int index7 = index6 + 2;
    apdu.CopyTo((Array) buffer, index7);
    int index8 = index7 + apdu.Length;
    DLT698FcsHelper.CalculateFcs16(buffer, 1, index8 - 1).CopyTo((Array) buffer, index8);
    int index9 = index8 + 2;
    buffer[index9] = (byte) 22;
    return OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  private static byte[] CreateApduBySecurity(byte[] apdu, bool useSecurity)
  {
    if (!useSecurity)
      return apdu;
    byte[] apduBySecurity = new byte[21 + apdu.Length];
    apduBySecurity[0] = (byte) 16 /*0x10*/;
    apduBySecurity[1] = BitConverter.GetBytes(apdu.Length)[1];
    apduBySecurity[2] = BitConverter.GetBytes(apdu.Length)[0];
    apduBySecurity[apdu.Length + 3] = (byte) 1;
    apduBySecurity[apdu.Length + 4] = (byte) 16 /*0x10*/;
    apduBySecurity[apdu.Length + 5] = (byte) 17;
    apduBySecurity[apdu.Length + 6] = (byte) 34;
    apduBySecurity[apdu.Length + 7] = (byte) 51;
    apduBySecurity[apdu.Length + 8] = (byte) 68;
    apduBySecurity[apdu.Length + 9] = (byte) 85;
    apduBySecurity[apdu.Length + 10] = (byte) 102;
    apduBySecurity[apdu.Length + 11] = (byte) 119;
    apduBySecurity[apdu.Length + 12] = (byte) 136;
    apduBySecurity[apdu.Length + 13] = (byte) 153;
    apduBySecurity[apdu.Length + 14] = (byte) 0;
    apduBySecurity[apdu.Length + 15] = (byte) 170;
    apduBySecurity[apdu.Length + 16 /*0x10*/] = (byte) 187;
    apduBySecurity[apdu.Length + 17] = (byte) 204;
    apduBySecurity[apdu.Length + 18] = (byte) 221;
    apduBySecurity[apdu.Length + 19] = (byte) 238;
    apduBySecurity[apdu.Length + 20] = byte.MaxValue;
    apdu.CopyTo((Array) apduBySecurity, 3);
    return apduBySecurity;
  }

  /// <summary>构建读取单个对象的报文数据</summary>
  /// <param name="address">数据地址信息</param>
  /// <param name="station">特殊指定的站号信息</param>
  /// <param name="dlt">通信的DLT对象</param>
  /// <returns>单次读取的报文信息</returns>
  public static OperateResult<byte[]> BuildReadSingleObject(
    string address,
    string station,
    IDlt698 dlt)
  {
    bool securityResquest = dlt.UseSecurityResquest;
    if (address.IndexOf(';') > 0)
    {
      string[] strArray = address.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
      if (strArray[0].StartsWith("s="))
        station = strArray[0].Substring(2);
      address = strArray[1];
    }
    byte[] apdu = new byte[8]
    {
      (byte) 5,
      (byte) 1,
      (byte) 1,
      (byte) 0,
      (byte) 0,
      (byte) 2,
      (byte) 0,
      (byte) 0
    };
    address.ToHexBytes().CopyTo((Array) apdu, 3);
    return DLT698Helper.BuildEntireCommand((byte) 67, station, dlt.CA, DLT698Helper.CreateApduBySecurity(apdu, securityResquest));
  }

  /// <summary>构建一个读取多个地址数据的报文信息</summary>
  /// <param name="address">地址数组</param>
  /// <param name="station">站号信息</param>
  /// <param name="dlt">DLT通信对象</param>
  /// <returns>结果报文内容</returns>
  public static OperateResult<byte[]> BuildReadMultiObject(
    string[] address,
    string station,
    IDlt698 dlt)
  {
    bool securityResquest = dlt.UseSecurityResquest;
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 5);
    ms.WriteByte((byte) 2);
    ms.WriteByte((byte) 2);
    ms.WriteByte((byte) address.Length);
    for (int index = 0; index < address.Length; ++index)
    {
      string str = address[index];
      if (str.IndexOf(';') > 0)
      {
        string[] strArray = str.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
        if (strArray[0].StartsWith("s="))
          station = strArray[0].Substring(2);
        str = strArray[1];
      }
      ms.Write(str.ToHexBytes());
    }
    ms.WriteByte((byte) 0);
    return DLT698Helper.BuildEntireCommand((byte) 67, station, dlt.CA, DLT698Helper.CreateApduBySecurity(ms.ToArray(), securityResquest));
  }

  /// <summary>构建单个写得对象的数据操作</summary>
  /// <param name="address">数据地址信息</param>
  /// <param name="station">站号信息</param>
  /// <param name="data">数据信息</param>
  /// <param name="dlt">通信的DLT对象</param>
  /// <returns>最终报文</returns>
  public static OperateResult<byte[]> BuildWriteSingleObject(
    string address,
    string station,
    byte[] data,
    IDlt698 dlt)
  {
    bool securityResquest = dlt.UseSecurityResquest;
    if (address.IndexOf(';') > 0)
    {
      string[] strArray = address.Split(new char[1]{ ';' }, StringSplitOptions.RemoveEmptyEntries);
      if (strArray[0].StartsWith("s="))
        station = strArray[0].Substring(2);
      address = strArray[1];
    }
    byte[] apdu = new byte[8 + data.Length];
    apdu[0] = (byte) 6;
    apdu[1] = (byte) 1;
    apdu[2] = (byte) 2;
    apdu[3] = (byte) 0;
    apdu[4] = (byte) 0;
    apdu[5] = (byte) 2;
    apdu[6] = (byte) 0;
    data.CopyTo((Array) apdu, 7);
    apdu[7 + data.Length] = (byte) 0;
    address.ToHexBytes().CopyTo((Array) apdu, 3);
    return DLT698Helper.BuildEntireCommand((byte) 67, station, dlt.CA, DLT698Helper.CreateApduBySecurity(apdu, securityResquest));
  }

  private static byte GetDayOfWeek(DayOfWeek dayOfWeek)
  {
    switch (dayOfWeek)
    {
      case DayOfWeek.Monday:
        return 1;
      case DayOfWeek.Tuesday:
        return 2;
      case DayOfWeek.Wednesday:
        return 3;
      case DayOfWeek.Thursday:
        return 4;
      case DayOfWeek.Friday:
        return 5;
      case DayOfWeek.Saturday:
        return 6;
      default:
        return 7;
    }
  }

  /// <summary>设置一个事件时间到DLT的报文中去</summary>
  /// <param name="apdu"></param>
  /// <param name="index"></param>
  /// <param name="time"></param>
  public static void SetDltDataTime(byte[] apdu, int index, DateTime time)
  {
    apdu[index] = BitConverter.GetBytes(time.Year)[1];
    apdu[index + 1] = BitConverter.GetBytes(time.Year)[0];
    apdu[index + 2] = BitConverter.GetBytes(time.Month)[0];
    apdu[index + 3] = BitConverter.GetBytes(time.Day)[0];
    apdu[index + 4] = DLT698Helper.GetDayOfWeek(time.DayOfWeek);
    apdu[index + 5] = (byte) time.Hour;
    apdu[index + 6] = (byte) time.Minute;
    apdu[index + 7] = (byte) time.Second;
    apdu[index + 8] = BitConverter.GetBytes(time.Millisecond)[1];
    apdu[index + 9] = BitConverter.GetBytes(time.Millisecond)[0];
  }

  /// <summary>根据给定的时间信息创建一个DLT的时间报文</summary>
  /// <param name="time">时间信息</param>
  /// <returns>时间报文</returns>
  public static byte[] CreateDateTimeWithMs(DateTime time)
  {
    byte[] apdu = new byte[10];
    DLT698Helper.SetDltDataTime(apdu, 0, time);
    return apdu;
  }

  /// <summary>检查当前的反馈数据信息是否正确</summary>
  /// <param name="response">从仪表反馈的数据信息</param>
  /// <returns>是否校验成功</returns>
  public static OperateResult<byte[]> CheckResponse(byte[] response)
  {
    try
    {
      if (response.Length < 9)
        return new OperateResult<byte[]>(StringResources.Language.ReceiveDataLengthTooShort);
      int startIndex = 1;
      if ((int) BitConverter.ToUInt16(response, startIndex) != response.Length - 2)
        return new OperateResult<byte[]>("Receive length check faild, source: " + response.ToHexString(' '));
      if (!DLT698FcsHelper.CheckFcs16(response, 1, response.Length - 4))
        return new OperateResult<byte[]>("fcs 16 check failed: " + response.ToHexString(' '));
      int index1 = 5 + ((int) response[4] + 1) + 1 + 2;
      byte[] numArray;
      if (response[index1] == (byte) 144 /*0x90*/)
      {
        int index2 = index1 + 1;
        int length = (int) response[index2] * 256 /*0x0100*/ + (int) response[index2 + 1];
        int index3 = index2 + 2;
        numArray = response.SelectMiddle<byte>(index3, length);
      }
      else
      {
        if (response[index1] == (byte) 238)
          return new OperateResult<byte[]>((int) response[index1 + 2], "Current device not support request type");
        numArray = response.SelectMiddle<byte>(index1, response.Length - index1 - 3);
      }
      if (numArray[0] == (byte) 134)
      {
        if (numArray.Length >= 9 && numArray[1] == (byte) 1 && numArray[7] > (byte) 0)
          return new OperateResult<byte[]>((int) numArray[8], DLT698Helper.GetErrorText(numArray[8]));
        if (numArray.Length >= 9 && numArray[1] == (byte) 2 && numArray[8] > (byte) 0)
          return new OperateResult<byte[]>((int) numArray[8], DLT698Helper.GetErrorText(numArray[8]));
      }
      else if (numArray[0] == (byte) 133 && numArray[1] == (byte) 2)
      {
        if (numArray.Length >= 10 && numArray[8] == (byte) 0)
          return new OperateResult<byte[]>((int) numArray[9], DLT698Helper.GetErrorText(numArray[9]));
      }
      else if (numArray.Length >= 9 && numArray[7] == (byte) 0)
        return new OperateResult<byte[]>((int) numArray[8], DLT698Helper.GetErrorText(numArray[8]));
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"CheckResponse failed: {ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
    }
  }

  private static string ExtraData(
    byte[] content,
    IByteTransform byteTransform,
    ref int index,
    byte oi1,
    byte oi2,
    byte attr)
  {
    byte num1 = content[index++];
    if (num1 == (byte) 3)
      return (content[index++] > (byte) 0).ToString();
    if (num1 == (byte) 4)
    {
      byte length1 = content[index++];
      int length2 = ((int) length1 + 7) / 8;
      byte[] InBytes = content.SelectMiddle<byte>(index, length2);
      index += length2;
      return InBytes.ToBoolArray().SelectBegin<bool>((int) length1).ToArrayString<bool>();
    }
    if (num1 == (byte) 5)
    {
      int num2 = byteTransform.TransInt32(content, index);
      index += 4;
      return DLT698Helper.GetScale<int>(num2, oi1, oi2, attr);
    }
    if (num1 == (byte) 6)
    {
      uint num3 = byteTransform.TransUInt32(content, index);
      index += 4;
      return DLT698Helper.GetScale<uint>(num3, oi1, oi2, attr);
    }
    if (num1 == (byte) 9)
    {
      int length = (int) content[index++];
      string hexString = content.SelectMiddle<byte>(index, length).ToHexString();
      index += length;
      return hexString;
    }
    if (num1 == (byte) 10)
    {
      int count = (int) content[index++];
      string str = Encoding.ASCII.GetString(content, index, count);
      index += count;
      return str;
    }
    if (num1 == (byte) 10)
    {
      int count = (int) content[index++];
      string str = Encoding.UTF8.GetString(content, index, count);
      index += count;
      return str;
    }
    if (num1 == (byte) 15)
      return DLT698Helper.GetScale<sbyte>((sbyte) content[index++], oi1, oi2, attr);
    if (num1 == (byte) 16 /*0x10*/)
    {
      short num4 = byteTransform.TransInt16(content, index);
      index += 2;
      return DLT698Helper.GetScale<short>(num4, oi1, oi2, attr);
    }
    if (num1 == (byte) 17)
      return DLT698Helper.GetScale<byte>(content[index++], oi1, oi2, attr);
    if (num1 == (byte) 18)
    {
      ushort num5 = byteTransform.TransUInt16(content, index);
      index += 2;
      return DLT698Helper.GetScale<ushort>(num5, oi1, oi2, attr);
    }
    if (num1 == (byte) 20)
    {
      long num6 = byteTransform.TransInt64(content, index);
      index += 8;
      return DLT698Helper.GetScale<long>(num6, oi1, oi2, attr);
    }
    if (num1 == (byte) 21)
    {
      ulong num7 = byteTransform.TransUInt64(content, index);
      index += 8;
      return DLT698Helper.GetScale<ulong>(num7, oi1, oi2, attr);
    }
    if (num1 == (byte) 22)
      return content[index++].ToString();
    if (num1 == (byte) 23)
    {
      float num8 = byteTransform.TransSingle(content, index);
      index += 4;
      return DLT698Helper.GetScale<float>(num8, oi1, oi2, attr);
    }
    if (num1 == (byte) 24)
    {
      double num9 = byteTransform.TransDouble(content, index);
      index += 8;
      return DLT698Helper.GetScale<double>(num9, oi1, oi2, attr);
    }
    if (num1 == (byte) 25)
    {
      ushort year = byteTransform.TransUInt16(content, index);
      index += 2;
      byte month = content[index++];
      byte day = content[index++];
      ++index;
      byte hour = content[index++];
      byte minute = content[index++];
      byte second = content[index++];
      ushort millisecond = byteTransform.TransUInt16(content, index);
      index += 2;
      try
      {
        return new DateTime((int) year, (int) month, (int) day, (int) hour, (int) minute, (int) second, (int) millisecond).ToString();
      }
      catch
      {
        return $"{year}-{month}-{day} {hour}:{minute}:{second}";
      }
    }
    else if (num1 == (byte) 28)
    {
      ushort year = byteTransform.TransUInt16(content, index);
      index += 2;
      byte month = content[index++];
      byte day = content[index++];
      byte hour = content[index++];
      byte minute = content[index++];
      byte second = content[index++];
      try
      {
        return new DateTime((int) year, (int) month, (int) day, (int) hour, (int) minute, (int) second).ToString();
      }
      catch
      {
        return $"{year}-{month}-{day} {hour}:{minute}:{second}";
      }
    }
    else
    {
      if (num1 == (byte) 26)
      {
        ushort num10 = byteTransform.TransUInt16(content, index);
        index += 2;
        byte num11 = content[index++];
        byte num12 = content[index++];
        ++index;
        return $"{num10}-{num11}-{num12}";
      }
      return num1 == (byte) 27 ? new TimeSpan((int) content[index++], (int) content[index++], (int) content[index++]).ToString() : (string) null;
    }
  }

  internal static int GetScale(byte oi1, byte oi2, byte attr)
  {
    attr &= (byte) 15;
    int scale = 0;
    if (((int) oi1 & 240 /*0xF0*/) == 0)
      scale = attr != (byte) 4 ? -2 : -4;
    else if (((int) oi1 & 240 /*0xF0*/) == 16 /*0x10*/)
    {
      scale = -4;
    }
    else
    {
      switch (oi1)
      {
        case 32 /*0x20*/:
          switch (oi2)
          {
            case 0:
              scale = -1;
              break;
            case 1:
              scale = -3;
              break;
            default:
              if (oi2 < (byte) 10)
              {
                scale = -1;
                break;
              }
              if (oi2 == (byte) 10)
              {
                scale = -3;
                break;
              }
              if (oi2 < (byte) 16 /*0x10*/)
              {
                scale = -2;
                break;
              }
              if (oi2 == (byte) 16 /*0x10*/)
              {
                scale = -1;
                break;
              }
              if (oi2 < (byte) 19)
              {
                scale = -2;
                break;
              }
              if (oi2 < (byte) 23)
              {
                scale = 0;
                break;
              }
              if (oi2 < (byte) 30)
              {
                scale = -4;
                break;
              }
              if (oi2 < (byte) 38)
              {
                scale = 0;
                break;
              }
              if (oi2 < (byte) 42)
              {
                scale = -2;
                break;
              }
              if (oi2 == (byte) 49 || oi2 == (byte) 50)
              {
                scale = -2;
                break;
              }
              if (oi2 == (byte) 128 /*0x80*/ || oi2 == (byte) 144 /*0x90*/)
              {
                scale = -4;
                break;
              }
              break;
          }
          break;
        case 37:
          if (oi2 < (byte) 2)
          {
            scale = -4;
            break;
          }
          if (oi2 < (byte) 4)
          {
            scale = -2;
            break;
          }
          break;
        case 64 /*0x40*/:
          if (oi2 == (byte) 48 /*0x30*/)
          {
            scale = -1;
            break;
          }
          break;
        case 65:
          if (oi2 == (byte) 12 || oi2 == (byte) 13 || oi2 == (byte) 14 || oi2 == (byte) 15)
            scale = -3;
          break;
      }
    }
    return scale;
  }

  private static string GetScale<T>(T value, byte oi1, byte oi2, byte attr)
  {
    int scale = DLT698Helper.GetScale(oi1, oi2, attr);
    return scale == 0 ? value.ToString() : (Convert.ToDouble((object) value) * Math.Pow(10.0, (double) scale)).ToString();
  }

  internal static string[] ExtraStringsValues(
    IByteTransform byteTransform,
    byte[] response,
    ref int index)
  {
    List<string> stringList = new List<string>();
    if (response[index] == (byte) 1 || response[index] == (byte) 2)
    {
      ++index;
      int num = (int) response[index++];
      for (int index1 = 0; index1 < num; ++index1)
        stringList.AddRange((IEnumerable<string>) DLT698Helper.ExtraStringsValues(byteTransform, response, ref index));
      return stringList.ToArray();
    }
    if (response[index] == (byte) 0)
      return stringList.ToArray();
    stringList.Add(DLT698Helper.ExtraData(response, byteTransform, ref index, response[3], response[4], response[5]));
    return stringList.ToArray();
  }

  /// <summary>根据错误代码返回详细的错误文本消息</summary>
  /// <param name="err">错误代码</param>
  /// <returns>错误文本消息</returns>
  public static string GetErrorText(byte err)
  {
    switch (err)
    {
      case 1:
        return StringResources.Language.DLT698Error01;
      case 2:
        return StringResources.Language.DLT698Error02;
      case 3:
        return StringResources.Language.DLT698Error03;
      case 4:
        return StringResources.Language.DLT698Error04;
      case 5:
        return StringResources.Language.DLT698Error05;
      case 6:
        return StringResources.Language.DLT698Error06;
      case 7:
        return StringResources.Language.DLT698Error07;
      case 8:
        return StringResources.Language.DLT698Error08;
      case 9:
        return StringResources.Language.DLT698Error09;
      case 10:
        return StringResources.Language.DLT698Error10;
      case 11:
        return StringResources.Language.DLT698Error11;
      case 12:
        return StringResources.Language.DLT698Error12;
      case 13:
        return StringResources.Language.DLT698Error13;
      case 14:
        return StringResources.Language.DLT698Error14;
      case 15:
        return StringResources.Language.DLT698Error15;
      case 16 /*0x10*/:
        return StringResources.Language.DLT698Error16;
      case 17:
        return StringResources.Language.DLT698Error17;
      case 18:
        return StringResources.Language.DLT698Error18;
      case 19:
        return StringResources.Language.DLT698Error19;
      case 20:
        return StringResources.Language.DLT698Error20;
      case 21:
        return StringResources.Language.DLT698Error21;
      case 22:
        return StringResources.Language.DLT698Error22;
      case 23:
        return StringResources.Language.DLT698Error23;
      case 24:
        return StringResources.Language.DLT698Error24;
      case 25:
        return StringResources.Language.DLT698Error25;
      case 26:
        return StringResources.Language.DLT698Error26;
      case 27:
        return StringResources.Language.DLT698Error27;
      case 28:
        return StringResources.Language.DLT698Error28;
      case 29:
        return StringResources.Language.DLT698Error29;
      case 30:
        return StringResources.Language.DLT698Error30;
      case 31 /*0x1F*/:
        return StringResources.Language.DLT698Error31;
      case 32 /*0x20*/:
        return StringResources.Language.DLT698Error32;
      case 33:
        return StringResources.Language.DLT698Error33;
      case 34:
        return StringResources.Language.DLT698Error34;
      case 35:
        return StringResources.Language.DLT698Error35;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>
  /// 根据传入的APDU的命令读取原始的字节数据返回，并检查返回的字节数据是否合法<br />
  /// Read the original byte data return according to the incoming APDU command, and check whether the returned byte data is valid
  /// </summary>
  /// <param name="dlt">通信的DLT对象</param>
  /// <param name="apdu">apdu报文信息</param>
  /// <returns>原始字节数据信息</returns>
  public static OperateResult<byte[]> ReadByApdu(IDlt698 dlt, byte[] apdu)
  {
    OperateResult<byte[]> operateResult1 = DLT698Helper.BuildEntireCommand((byte) 67, dlt.Station, dlt.CA, apdu);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? operateResult2 : DLT698Helper.CheckResponse(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.ReadByApdu(HslCommunication.Instrument.DLT.Helper.IDlt698,System.Byte[])" />
  public static async Task<OperateResult<byte[]>> ReadByApduAsync(IDlt698 dlt, byte[] apdu)
  {
    OperateResult<byte[]> command = DLT698Helper.BuildEntireCommand((byte) 67, dlt.Station, dlt.CA, apdu);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await dlt.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? DLT698Helper.CheckResponse(read.Content) : read;
  }

  /// <summary>
  /// 激活设备的命令，只发送数据到设备，不等待设备数据返回<br />
  /// The command to activate the device, only send data to the device, do not wait for the device data to return
  /// </summary>
  /// <returns>是否发送成功</returns>
  public static OperateResult ActiveDeveice(IDlt698 dlt)
  {
    return (OperateResult) dlt.ReadFromCoreServer(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, false, false);
  }

  /// <summary>
  /// 根据指定的数据标识来读取相关的原始数据信息，地址标识根据手册来，从高位到地位，例如 00-00-00-00，分割符可以任意特殊字符或是没有分隔符。<br />
  /// Read the relevant original data information according to the specified data identifier. The address identifier is based on the manual,
  /// from high to position, such as 00-00-00-00. The separator can be any special character or no separator.
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;00-00-00-00" 或是 "s=100000;00-00-02-00"，关于数据域信息，需要查找手册，例如:00-01-00-00 表示： (当前)正向有功总电能
  /// </remarks>
  /// <param name="dlt">通信的DLT对象</param>
  /// <param name="address">数据标识，具体需要查找手册来对应</param>
  /// <param name="length">数据长度信息</param>
  /// <returns>结果信息</returns>
  public static OperateResult<byte[]> Read(IDlt698 dlt, string address, ushort length)
  {
    OperateResult<byte[]> operateResult1 = DLT698Helper.BuildReadSingleObject(address, dlt.Station, dlt);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = dlt.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? operateResult2 : DLT698Helper.CheckResponse(operateResult2.Content);
  }

  /// <summary>
  /// 读取指定地址的所有的字符串数据信息，一般来说，一个地址只有一个数据，当属性为数组或是结构体的时候，存在多个数据，具体几个数据，需要根据
  /// </summary>
  /// <remarks>
  /// 地址可以携带地址域信息，例如 "s=2;20-00-02-00" 或是 "s=100000;20-00-02-00"，
  /// </remarks>
  /// <param name="dlt">通信的DLT对象</param>
  /// <param name="address">数据标识，具体需要查找手册来对应</param>
  /// <returns>字符串数组信息</returns>
  public static OperateResult<string[]> ReadStringArray(IDlt698 dlt, string address)
  {
    OperateResult<byte[]> result = DLT698Helper.Read(dlt, address, (ushort) 1);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result);
    int index = 8;
    return OperateResult.CreateSuccessResult<string[]>(DLT698Helper.ExtraStringsValues(dlt.ByteTransform, result.Content, ref index));
  }

  /// <summary>
  /// 批量读取多个地址的数据信息，返回所有的数据对象信息<br />
  /// </summary>
  /// <param name="dlt">DLT通信对象</param>
  /// <param name="address">地址数组信息</param>
  /// <returns>字符串结果数据</returns>
  public static OperateResult<string[]> ReadStringArray(IDlt698 dlt, string[] address)
  {
    OperateResult<byte[]> result1 = DLT698Helper.BuildReadMultiObject(address, dlt.Station, dlt);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = dlt.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result2);
    OperateResult<byte[]> result3 = DLT698Helper.CheckResponse(result2.Content);
    if (!result3.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) result3);
    int num = (int) result3.Content[3];
    List<string> stringList = new List<string>();
    int index1 = 9;
    for (int index2 = 0; index2 < num; ++index2)
    {
      stringList.AddRange((IEnumerable<string>) DLT698Helper.ExtraStringsValues(dlt.ByteTransform, result3.Content, ref index1));
      index1 += 5;
    }
    return OperateResult.CreateSuccessResult<string[]>(stringList.ToArray());
  }

  internal static OperateResult<T[]> ReadDataAndParse<T>(
    OperateResult<string[]> read,
    ushort length,
    Func<string, T> trans)
  {
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<T[]>((OperateResult) read);
    try
    {
      return OperateResult.CreateSuccessResult<T[]>(((IEnumerable<string>) read.Content).Take<string>((int) length).Select<string, T>((Func<string, T>) (m => trans(m))).ToArray<T>());
    }
    catch (Exception ex)
    {
      return new OperateResult<T[]>($"{typeof (T).Name}.Parse failed: {ex.Message}{Environment.NewLine}Source: {read.Content.ToArrayString<string>()}");
    }
  }

  internal static OperateResult<bool[]> ReadBool(OperateResult<string[]> read, ushort length)
  {
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    try
    {
      List<bool> boolList = new List<bool>();
      for (int index = 0; index < read.Content.Length; ++index)
        boolList.AddRange((IEnumerable<bool>) read.Content[index].ToStringArray<bool>());
      return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>($"bool.Parse failed: {ex.Message}{Environment.NewLine}Source: {read.Content.ToArrayString<string>()}");
    }
  }

  /// <summary>
  /// 根据指定的数据标识来写入相关的原始数据信息，地址标识根据手册来，从高位到地位，例如 00-00-00-00，分割符可以任意特殊字符或是没有分隔符。<br />
  /// Read the relevant original data information according to the specified data identifier. The address identifier is based on the manual,
  /// from high to position, such as 00-00-00-00. The separator can be any special character or no separator.
  /// </summary>
  /// <remarks>
  /// 写入数据的时候，需要使用类型+值信息，例如写入地址 40-00-02-00  值为 1C 07 E0 01 14 10 1B 0B  表示 时间：2016-01-20 16：27：11
  /// </remarks>
  /// <param name="dlt">通信的DLT对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">写入的数据值</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(IDlt698 dlt, string address, byte[] value)
  {
    OperateResult<byte[]> result1 = DLT698Helper.BuildWriteSingleObject(address, dlt.Station, value, dlt);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = dlt.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result2) : (OperateResult) DLT698Helper.CheckResponse(result2.Content);
  }

  /// <summary>
  /// 读取设备的通信地址，仅支持点对点通讯的情况，返回地址域数据，例如：149100007290<br />
  /// Read the communication address of the device, only support point-to-point communication, and return the address field data, for example: 149100007290
  /// </summary>
  /// <param name="dlt">通信的DLT对象</param>
  /// <returns>设备的通信地址</returns>
  public static OperateResult<string> ReadAddress(IDlt698 dlt)
  {
    OperateResult<byte[]> result1 = DLT698Helper.BuildReadSingleObject("40-01-02-00", "AAAAAAAAAAAA", dlt);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = dlt.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result2);
    OperateResult<byte[]> result3 = DLT698Helper.CheckResponse(result2.Content);
    if (!result3.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result3);
    dlt.Station = result3.Content.SelectMiddle<byte>(10, (int) result3.Content[9]).ToHexString();
    return OperateResult.CreateSuccessResult<string>(dlt.Station);
  }

  /// <summary>
  /// 写入设备的地址域信息，仅支持点对点通讯的情况，需要指定地址域信息，例如：149100007290<br />
  /// Write the address domain information of the device, only support point-to-point communication,
  /// you need to specify the address domain information, for example: 149100007290
  /// </summary>
  /// <param name="dlt">通信的DLT对象</param>
  /// <param name="address">等待写入的地址域</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteAddress(IDlt698 dlt, string address)
  {
    OperateResult<byte[]> result1 = DLT698Helper.BuildWriteSingleObject("40-01-02-00", "AAAAAAAAAAAA", DLT698Helper.CreateStringValueBuffer(address), dlt);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = dlt.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result2) : (OperateResult) DLT698Helper.CheckResponse(result2.Content);
  }

  /// <summary>
  /// 写入设备的时间信息到指定的地址，返回是否成功，使用的时间类型为 0x1C, 有效数据为 年月日时分秒。<br />
  /// Write the time information of the device to the specified address, return whether it is successful, the time type used is 0x1C, and the valid data is year, month, day, hour, minute, and second.
  /// </summary>
  /// <param name="dlt">通信的DLT对象</param>
  /// <param name="address">写入的地址的信息</param>
  /// <param name="time">时间数据</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteDateTime(IDlt698 dlt, string address, DateTime time)
  {
    OperateResult<byte[]> result1 = DLT698Helper.BuildWriteSingleObject(address, dlt.Station, DLT698Helper.CreateDateTimeValue(time), dlt);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = dlt.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result2) : (OperateResult) DLT698Helper.CheckResponse(result2.Content);
  }
}
