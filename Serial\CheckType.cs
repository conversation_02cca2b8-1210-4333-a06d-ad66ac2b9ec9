﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Serial.CheckType
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Serial;

/// <summary>校验方式</summary>
public enum CheckType
{
  /// <summary>和校验</summary>
  BCC,
  /// <summary>CRC校验的方式</summary>
  CRC16,
}
