﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Keyence.KeyenceNanoServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

#nullable disable
namespace HslCommunication.Profinet.Keyence;

/// <summary>基恩士的上位链路协议的虚拟服务器</summary>
public class KeyenceNanoServer : DeviceServer
{
  private SoftBuffer rBuffer;
  private SoftBuffer bBuffer;
  private SoftBuffer mrBuffer;
  private SoftBuffer lrBuffer;
  private SoftBuffer crBuffer;
  private SoftBuffer vbBuffer;
  private SoftBuffer dmBuffer;
  private SoftBuffer emBuffer;
  private SoftBuffer wBuffer;
  private SoftBuffer atBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个基于上位链路协议的虚拟的基恩士PLC对象，可以用来和<see cref="T:HslCommunication.Profinet.Keyence.KeyenceNanoSerialOverTcp" />进行通信测试。
  /// </summary>
  public KeyenceNanoServer()
  {
    this.rBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.bBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mrBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.lrBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.crBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.vbBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dmBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.emBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.wBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.atBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.ByteTransform.IsStringReverseByteWord = true;
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] bytes = new byte[851968 /*0x0D0000*/];
    this.rBuffer.GetBytes().CopyTo((Array) bytes, 0);
    this.bBuffer.GetBytes().CopyTo((Array) bytes, 65536 /*0x010000*/);
    this.mrBuffer.GetBytes().CopyTo((Array) bytes, 131072 /*0x020000*/);
    this.lrBuffer.GetBytes().CopyTo((Array) bytes, 196608 /*0x030000*/);
    this.crBuffer.GetBytes().CopyTo((Array) bytes, 262144 /*0x040000*/);
    this.vbBuffer.GetBytes().CopyTo((Array) bytes, 327680 /*0x050000*/);
    this.dmBuffer.GetBytes().CopyTo((Array) bytes, 393216 /*0x060000*/);
    this.emBuffer.GetBytes().CopyTo((Array) bytes, 524288 /*0x080000*/);
    this.wBuffer.GetBytes().CopyTo((Array) bytes, 655360 /*0x0A0000*/);
    this.atBuffer.GetBytes().CopyTo((Array) bytes, 786432 /*0x0C0000*/);
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 851968 /*0x0D0000*/)
      throw new Exception("File is not correct");
    this.rBuffer.SetBytes(content, 0, 65536 /*0x010000*/);
    this.bBuffer.SetBytes(content, 65536 /*0x010000*/, 65536 /*0x010000*/);
    this.mrBuffer.SetBytes(content, 131072 /*0x020000*/, 65536 /*0x010000*/);
    this.lrBuffer.SetBytes(content, 196608 /*0x030000*/, 65536 /*0x010000*/);
    this.crBuffer.SetBytes(content, 262144 /*0x040000*/, 65536 /*0x010000*/);
    this.vbBuffer.SetBytes(content, 327680 /*0x050000*/, 65536 /*0x010000*/);
    this.dmBuffer.SetBytes(content, 393216 /*0x060000*/, 131072 /*0x020000*/);
    this.emBuffer.SetBytes(content, 524288 /*0x080000*/, 131072 /*0x020000*/);
    this.wBuffer.SetBytes(content, 655360 /*0x0A0000*/, 131072 /*0x020000*/);
    this.atBuffer.SetBytes(content, 786432 /*0x0C0000*/, 65536 /*0x010000*/);
  }

  private byte[] TransBoolByteToBuffer(byte[] data)
  {
    bool[] array = new bool[data.Length];
    for (int index = 0; index < array.Length; ++index)
      array[index] = data[index] > (byte) 0;
    return array.ToByteArray();
  }

  private byte[] TransBufferToBoolByte(byte[] data)
  {
    bool[] boolArray = data.ToBoolArray();
    byte[] boolByte = new byte[boolArray.Length];
    for (int index = 0; index < boolArray.Length; ++index)
      boolByte[index] = boolArray[index] ? (byte) 1 : (byte) 0;
    return boolByte;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoSerialOverTcp.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    try
    {
      if (address.StartsWith("DM"))
        return OperateResult.CreateSuccessResult<byte[]>(this.dmBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
      if (address.StartsWith("EM"))
        return OperateResult.CreateSuccessResult<byte[]>(this.emBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
      if (address.StartsWith("W"))
        return OperateResult.CreateSuccessResult<byte[]>(this.wBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
      if (address.StartsWith("AT"))
        return OperateResult.CreateSuccessResult<byte[]>(this.atBuffer.GetBytes(from.Content.AddressStart * 4, (int) length * 4));
      if (address.StartsWith("MR"))
        return OperateResult.CreateSuccessResult<byte[]>(this.TransBoolByteToBuffer(this.mrBuffer.GetBytes(from.Content.AddressStart, (int) length * 16 /*0x10*/)));
      if (address.StartsWith("LR"))
        return OperateResult.CreateSuccessResult<byte[]>(this.TransBoolByteToBuffer(this.lrBuffer.GetBytes(from.Content.AddressStart, (int) length * 16 /*0x10*/)));
      if (address.StartsWith("CR"))
        return OperateResult.CreateSuccessResult<byte[]>(this.TransBoolByteToBuffer(this.crBuffer.GetBytes(from.Content.AddressStart, (int) length * 16 /*0x10*/)));
      if (address.StartsWith("VB"))
        return OperateResult.CreateSuccessResult<byte[]>(this.TransBoolByteToBuffer(this.vbBuffer.GetBytes(from.Content.AddressStart, (int) length * 16 /*0x10*/)));
      if (address.StartsWith("B"))
        return OperateResult.CreateSuccessResult<byte[]>(this.TransBoolByteToBuffer(this.bBuffer.GetBytes(from.Content.AddressStart, (int) length * 16 /*0x10*/)));
      return address.StartsWith("R") ? OperateResult.CreateSuccessResult<byte[]>(this.TransBoolByteToBuffer(this.rBuffer.GetBytes(from.Content.AddressStart, (int) length * 16 /*0x10*/))) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"{StringResources.Language.NotSupportedDataType} Reason:{ex.Message}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Keyence.KeyenceNanoSerialOverTcp.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    try
    {
      if (address.StartsWith("DM"))
        this.dmBuffer.SetBytes(value, from.Content.AddressStart * 2);
      else if (address.StartsWith("EM"))
        this.emBuffer.SetBytes(value, from.Content.AddressStart * 2);
      else if (address.StartsWith("W"))
        this.wBuffer.SetBytes(value, from.Content.AddressStart * 2);
      else if (address.StartsWith("AT"))
        this.atBuffer.SetBytes(value, from.Content.AddressStart * 4);
      else if (address.StartsWith("MR"))
        this.mrBuffer.SetBytes(this.TransBufferToBoolByte(value), from.Content.AddressStart);
      else if (address.StartsWith("LR"))
        this.lrBuffer.SetBytes(this.TransBufferToBoolByte(value), from.Content.AddressStart);
      else if (address.StartsWith("CR"))
        this.crBuffer.SetBytes(this.TransBufferToBoolByte(value), from.Content.AddressStart);
      else if (address.StartsWith("VB"))
        this.vbBuffer.SetBytes(this.TransBufferToBoolByte(value), from.Content.AddressStart);
      else if (address.StartsWith("B"))
      {
        this.bBuffer.SetBytes(this.TransBufferToBoolByte(value), from.Content.AddressStart);
      }
      else
      {
        if (!address.StartsWith("R"))
          return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
        this.rBuffer.SetBytes(this.TransBufferToBoolByte(value), from.Content.AddressStart);
      }
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<byte[]>($"{StringResources.Language.NotSupportedDataType} Reason:{ex.Message}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    try
    {
      if (address.StartsWith("R"))
        return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.rBuffer.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
      if (address.StartsWith("B"))
        return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.bBuffer.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
      if (address.StartsWith("MR"))
        return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.mrBuffer.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
      if (address.StartsWith("LR"))
        return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.lrBuffer.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
      if (address.StartsWith("CR"))
        return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.crBuffer.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
      return address.StartsWith("VB") ? OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.vbBuffer.GetBytes(from.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>($"{StringResources.Language.NotSupportedDataType} Reason:{ex.Message}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, (ushort) 0);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    try
    {
      byte[] array = ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
      if (address.StartsWith("R"))
        this.rBuffer.SetBytes(array, from.Content.AddressStart);
      else if (address.StartsWith("B"))
        this.bBuffer.SetBytes(array, from.Content.AddressStart);
      else if (address.StartsWith("MR"))
        this.mrBuffer.SetBytes(array, from.Content.AddressStart);
      else if (address.StartsWith("LR"))
        this.lrBuffer.SetBytes(array, from.Content.AddressStart);
      else if (address.StartsWith("CR"))
      {
        this.crBuffer.SetBytes(array, from.Content.AddressStart);
      }
      else
      {
        if (!address.StartsWith("VB"))
          return (OperateResult) new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
        this.vbBuffer.SetBytes(array, from.Content.AddressStart);
      }
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<bool[]>($"{StringResources.Language.NotSupportedDataType} Reason:{ex.Message}");
    }
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return OperateResult.CreateSuccessResult<byte[]>(this.ReadFromNanoCore(receive));
  }

  private byte[] GetBoolResponseData(byte[] data)
  {
    StringBuilder stringBuilder = new StringBuilder();
    for (int index = 0; index < data.Length; ++index)
    {
      stringBuilder.Append(data[index]);
      if (index != data.Length - 1)
        stringBuilder.Append(" ");
    }
    stringBuilder.Append("\r\n");
    return Encoding.ASCII.GetBytes(stringBuilder.ToString());
  }

  private byte[] GetWordResponseData(byte[] data, string format = ".U")
  {
    StringBuilder stringBuilder = new StringBuilder();
    int num = data.Length / 2;
    if (format == ".D" || format == ".L")
      num = data.Length / 4;
    for (int index = 0; index < num; ++index)
    {
      switch (format)
      {
        case ".U":
          stringBuilder.Append(BitConverter.ToUInt16(data, index * 2).ToString("D5"));
          break;
        case ".S":
          stringBuilder.Append(BitConverter.ToInt16(data, index * 2));
          break;
        case ".D":
          stringBuilder.Append(BitConverter.ToUInt32(data, index * 4));
          break;
        case ".L":
          stringBuilder.Append(BitConverter.ToInt32(data, index * 4));
          break;
        default:
          stringBuilder.Append(BitConverter.ToUInt16(data, index * 2).ToString("X4"));
          break;
      }
      if (index != num - 1)
        stringBuilder.Append(" ");
    }
    stringBuilder.Append("\r\n");
    return Encoding.ASCII.GetBytes(stringBuilder.ToString());
  }

  private byte[] ReadFromNanoCore(byte[] receive)
  {
    string[] command = Encoding.ASCII.GetString(receive).Trim('\r', '\n').Split(new char[1]
    {
      ' '
    }, StringSplitOptions.RemoveEmptyEntries);
    if (command[0] == "CR")
      return Encoding.ASCII.GetBytes("CC\r\n");
    if (command[0] == "CQ")
      return Encoding.ASCII.GetBytes("CF\r\n");
    if (command[0] == "ER")
      return Encoding.ASCII.GetBytes("OK\r\n");
    if (command[0] == "RD" || command[0] == "RDS")
      return this.ReadByCommand(command);
    if (command[0] == "WR" || command[0] == "WRS")
      return this.WriteByCommand(command);
    if (command[0] == "ST")
      return this.WriteByCommand(new string[4]
      {
        "WRS",
        command[1],
        "1",
        "1"
      });
    if (command[0] == "RS")
      return this.WriteByCommand(new string[4]
      {
        "WRS",
        command[1],
        "1",
        "0"
      });
    if (command[0] == "?K")
      return Encoding.ASCII.GetBytes("53\r\n");
    if (command[0] == "?M")
      return Encoding.ASCII.GetBytes("1\r\n");
    return command[0] == "M0" || command[0] == "M1" || command[0] == "WRT" ? Encoding.ASCII.GetBytes("OK\r\n") : Encoding.ASCII.GetBytes("E0\r\n");
  }

  private byte[] ReadByCommand(string[] command)
  {
    try
    {
      string format = string.Empty;
      if (command[1].EndsWith(new string[5]
      {
        ".U",
        ".S",
        ".D",
        ".L",
        ".H"
      }))
      {
        format = command[1].Substring(command[1].Length - 2);
        command[1] = command[1].Remove(command[1].Length - 2);
      }
      int length = command.Length > 2 ? int.Parse(command[2]) : 1;
      if (Regex.IsMatch(command[1], "^[0-9]+$"))
        command[1] = "R" + command[1];
      OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(command[1], (ushort) length);
      if (!from.IsSuccess)
        return Encoding.ASCII.GetBytes("E0\r\n");
      KeyenceNanoAddress content = from.Content;
      if (length > 1000)
        return Encoding.ASCII.GetBytes("E0\r\n");
      SoftBuffer softBuffer;
      switch (content.DataCode)
      {
        case "":
        case "R":
          softBuffer = this.rBuffer;
          break;
        case "AT":
          return this.GetWordResponseData(this.atBuffer.GetBytes(content.AddressStart * 4, length * 4), string.IsNullOrEmpty(format) ? ".D" : format);
        case "B":
          softBuffer = this.bBuffer;
          break;
        case "CR":
          softBuffer = this.crBuffer;
          break;
        case "DM":
          return this.GetWordResponseData(this.dmBuffer.GetBytes(content.AddressStart * 2, length * 2), string.IsNullOrEmpty(format) ? ".U" : format);
        case "EM":
          return this.GetWordResponseData(this.emBuffer.GetBytes(content.AddressStart * 2, length * 2), string.IsNullOrEmpty(format) ? ".U" : format);
        case "LR":
          softBuffer = this.lrBuffer;
          break;
        case "MR":
          softBuffer = this.mrBuffer;
          break;
        case "VB":
          softBuffer = this.vbBuffer;
          break;
        case "W":
          return this.GetWordResponseData(this.wBuffer.GetBytes(content.AddressStart * 2, length * 2), string.IsNullOrEmpty(format) ? ".U" : format);
        default:
          return Encoding.ASCII.GetBytes("E0\r\n");
      }
      return string.IsNullOrEmpty(format) ? this.GetBoolResponseData(softBuffer.GetBytes(content.AddressStart, length)) : this.GetWordResponseData(((IEnumerable<byte>) softBuffer.GetBytes(content.AddressStart, length * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray(), format);
    }
    catch
    {
      return Encoding.ASCII.GetBytes("E1\r\n");
    }
  }

  private byte[] WriteByCommand(string[] command)
  {
    if (!this.EnableWrite)
      return Encoding.ASCII.GetBytes("E4\r\n");
    try
    {
      string str = string.Empty;
      if (command[1].EndsWith(new string[5]
      {
        ".U",
        ".S",
        ".D",
        ".L",
        ".H"
      }))
      {
        str = command[1].Substring(command[1].Length - 2);
        command[1] = command[1].Remove(command[1].Length - 2);
      }
      int length = command[0] == "WRS" ? int.Parse(command[2]) : 1;
      if (Regex.IsMatch(command[1], "^[0-9]+$"))
        command[1] = "R" + command[1];
      OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(command[1], (ushort) length);
      if (!from.IsSuccess)
        return Encoding.ASCII.GetBytes("E0\r\n");
      KeyenceNanoAddress content = from.Content;
      if (length > 1000)
        return Encoding.ASCII.GetBytes("E0\r\n");
      if (command[1].StartsWith("R") || command[1].StartsWith("B") || command[1].StartsWith("MR") || command[1].StartsWith("LR") || command[1].StartsWith("CR") || command[1].StartsWith("VB"))
      {
        SoftBuffer softBuffer;
        if (command[1].StartsWith("R"))
          softBuffer = this.rBuffer;
        else if (command[1].StartsWith("B"))
          softBuffer = this.bBuffer;
        else if (command[1].StartsWith("MR"))
          softBuffer = this.mrBuffer;
        else if (command[1].StartsWith("LR"))
          softBuffer = this.lrBuffer;
        else if (command[1].StartsWith("CR"))
        {
          softBuffer = this.crBuffer;
        }
        else
        {
          if (!command[1].StartsWith("VB"))
            return Encoding.ASCII.GetBytes("E0\r\n");
          softBuffer = this.vbBuffer;
        }
        string[] source = command.RemoveBegin<string>(command[0] == "WRS" ? 3 : 2);
        switch (str)
        {
          case ".U":
            byte[] array1 = ((IEnumerable<bool>) this.ByteTransform.TransByte(((IEnumerable<string>) source).Select<string, ushort>((Func<string, ushort>) (m => ushort.Parse(m))).ToArray<ushort>()).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
            softBuffer.SetBytes(array1, content.AddressStart);
            break;
          case ".S":
            byte[] array2 = ((IEnumerable<bool>) this.ByteTransform.TransByte(((IEnumerable<string>) source).Select<string, short>((Func<string, short>) (m => short.Parse(m))).ToArray<short>()).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
            softBuffer.SetBytes(array2, content.AddressStart);
            break;
          case ".D":
            byte[] array3 = ((IEnumerable<bool>) this.ByteTransform.TransByte(((IEnumerable<string>) source).Select<string, uint>((Func<string, uint>) (m => uint.Parse(m))).ToArray<uint>()).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
            softBuffer.SetBytes(array3, content.AddressStart);
            break;
          case ".L":
            byte[] array4 = ((IEnumerable<bool>) this.ByteTransform.TransByte(((IEnumerable<string>) source).Select<string, int>((Func<string, int>) (m => int.Parse(m))).ToArray<int>()).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
            softBuffer.SetBytes(array4, content.AddressStart);
            break;
          case ".H":
            byte[] array5 = ((IEnumerable<bool>) this.ByteTransform.TransByte(((IEnumerable<string>) source).Select<string, ushort>((Func<string, ushort>) (m => Convert.ToUInt16(m, 16 /*0x10*/))).ToArray<ushort>()).ToBoolArray()).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
            softBuffer.SetBytes(array5, content.AddressStart);
            break;
          default:
            byte[] array6 = ((IEnumerable<string>) source).Select<string, byte>((Func<string, byte>) (m => byte.Parse(m))).ToArray<byte>();
            softBuffer.SetBytes(array6, content.AddressStart);
            break;
        }
      }
      else
      {
        byte[] data = this.ByteTransform.TransByte(((IEnumerable<string>) command.RemoveBegin<string>(command[0] == "WRS" ? 3 : 2)).Select<string, ushort>((Func<string, ushort>) (m => ushort.Parse(m))).ToArray<ushort>());
        if (command[1].StartsWith("DM"))
          this.dmBuffer.SetBytes(data, content.AddressStart * 2);
        else if (command[1].StartsWith("EM"))
          this.emBuffer.SetBytes(data, content.AddressStart * 2);
        else if (command[1].StartsWith("W"))
        {
          this.wBuffer.SetBytes(data, content.AddressStart * 2);
        }
        else
        {
          if (!command[1].StartsWith("AT"))
            return Encoding.ASCII.GetBytes("E0\r\n");
          this.atBuffer.SetBytes(data, content.AddressStart * 4);
        }
      }
      return Encoding.ASCII.GetBytes("OK\r\n");
    }
    catch
    {
      return Encoding.ASCII.GetBytes("E1\r\n");
    }
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength >= 1 && buffer[receivedLength - 1] == (byte) 13;
  }

  /// <inheritdoc />
  public override string ToString() => $"KeyenceNanoServer[{this.Port}]";
}
