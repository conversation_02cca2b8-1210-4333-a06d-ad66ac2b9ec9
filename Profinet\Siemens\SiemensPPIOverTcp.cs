﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensPPIOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Siemens.Helper;
using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <inheritdoc cref="T:HslCommunication.Profinet.Siemens.SiemensPPI" />
public class SiemensPPIOverTcp : DeviceTcpNet, ISiemensPPI, IReadWriteNet
{
  private byte station = 2;
  private object communicationLock;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.#ctor" />
  public SiemensPPIOverTcp()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.communicationLock = new object();
  }

  /// <summary>
  /// 使用指定的ip地址和端口号来实例化对象<br />
  /// Instantiate the object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号信息</param>
  public SiemensPPIOverTcp(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new SiemensPPIMessage();

  /// <inheritdoc cref="P:HslCommunication.Profinet.Siemens.SiemensPPI.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return SiemensPPIHelper.Read((IReadWriteDevice) this, address, length, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    return SiemensPPIHelper.ReadBool((IReadWriteDevice) this, address, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return SiemensPPIHelper.ReadBool((IReadWriteDevice) this, address, length, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return SiemensPPIHelper.Write((IReadWriteDevice) this, address, value, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return SiemensPPIHelper.Write((IReadWriteDevice) this, address, value, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.ReadByte(System.String)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Write(System.String,System.Byte)" />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<bool> operateResult = await Task.Run<OperateResult<bool>>((Func<OperateResult<bool>>) (() => this.ReadBool(address)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPIOverTcp.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPIOverTcp.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Start(System.String)" />
  [HslMqttApi]
  public OperateResult Start(string parameter = "")
  {
    return SiemensPPIHelper.Start((IReadWriteDevice) this, parameter, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Stop(System.String)" />
  [HslMqttApi]
  public OperateResult Stop(string parameter = "")
  {
    return SiemensPPIHelper.Stop((IReadWriteDevice) this, parameter, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  [HslMqttApi]
  public OperateResult<string> ReadPlcType(string parameter = "")
  {
    return SiemensPPIHelper.ReadPlcType((IReadWriteDevice) this, parameter, this.Station, this.communicationLock);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Start(System.String)" />
  public async Task<OperateResult> StartAsync(string parameter = "")
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Start(parameter)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensPPI.Stop(System.String)" />
  public async Task<OperateResult> StopAsync(string parameter = "")
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Stop(parameter)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  public async Task<OperateResult<string>> ReadPlcTypeAsync(string parameter = "")
  {
    OperateResult<string> operateResult = await Task.Run<OperateResult<string>>((Func<OperateResult<string>>) (() => SiemensPPIHelper.ReadPlcType((IReadWriteDevice) this, parameter, this.Station, this.communicationLock)));
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"SiemensPPIOverTcp[{this.IpAddress}:{this.Port}]";
}
