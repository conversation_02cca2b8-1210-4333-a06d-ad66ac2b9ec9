﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeSerialPort
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.IO;
using System.IO.Ports;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>串口管道信息</summary>
public class PipeSerialPort : CommunicationPipe, IDisposable
{
  private SerialPort serialPort;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public PipeSerialPort()
  {
    this.serialPort = new SerialPort();
    this.SleepTime = 20;
  }

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  /// <param name="portName">
  /// portName 支持格式化的方式，例如输入 COM3-9600-8-N-1，COM5-19200-7-E-2，其中奇偶校验的字母可选，N:无校验，O：奇校验，E:偶校验，停止位可选 0, 1, 2, 1.5 四种选项
  /// </param>
  public PipeSerialPort(string portName)
  {
    this.serialPort = new SerialPort();
    this.SleepTime = 20;
    this.SerialPortInni(portName);
  }

  /// <summary>
  /// 获取或设置一个值，该值指示在串行通信中是否启用请求发送 (RTS) 信号。<br />
  /// Gets or sets a value indicating whether the request sending (RTS) signal is enabled in serial communication.
  /// </summary>
  public bool RtsEnable
  {
    get => this.serialPort.RtsEnable;
    set => this.serialPort.RtsEnable = value;
  }

  /// <summary>
  /// 获取或设置一个值，该值指示在串行通信中是否启用数据终端就绪 (Drt) 信号。<br />
  /// Gets or sets a value that indicates whether the Data Terminal Ready (DRT) signal is enabled in serial communication.
  /// </summary>
  public bool DtrEnable
  {
    get => this.serialPort.DtrEnable;
    set => this.serialPort.DtrEnable = value;
  }

  /// <summary>从串口中至少接收的字节长度信息，默认为1个字节</summary>
  public int AtLeastReceiveLength { get; set; } = 1;

  /// <summary>
  /// 获取或设置连续接收空的数据次数，在数据接收完成时有效，每个单位消耗的时间为<see cref="P:HslCommunication.Core.Pipe.CommunicationPipe.SleepTime" />。<br />
  /// Obtain or set the number of consecutive times to receive empty data, which is valid when the data is received, and the time consumed by each unit is <see cref="P:HslCommunication.Core.Pipe.CommunicationPipe.SleepTime" />
  /// </summary>
  [HslMqttApi(Description = "Get or set the number of consecutive empty data receptions, which is valid when data reception is completed, default is 1")]
  public int ReceiveEmptyDataCount { get; set; } = 1;

  /// <summary>
  /// 是否在发送数据前清空缓冲数据，默认是false<br />
  /// Whether to empty the buffer before sending data, the default is false
  /// </summary>
  [HslMqttApi(Description = "Whether to empty the buffer before sending data, the default is false")]
  public bool IsClearCacheBeforeRead { get; set; }

  /// <summary>
  /// 初始化串口信息，9600波特率，8位数据位，1位停止位，无奇偶校验<br />
  /// Initial serial port information, 9600 baud rate, 8 data bits, 1 stop bit, no parity
  /// </summary>
  /// <remarks>
  /// portName 支持格式化的方式，例如输入 COM3-9600-8-N-1，COM5-19200-7-E-2，其中奇偶校验的字母可选，N:无校验，O：奇校验，E:偶校验，停止位可选 0, 1, 2, 1.5 四种选项
  /// </remarks>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  public void SerialPortInni(string portName)
  {
    if (portName.Contains("-") || portName.Contains(";"))
      this.SerialPortInni((Action<SerialPort>) (sp => sp.IniSerialByFormatString(portName)));
    else
      this.SerialPortInni(portName, 9600, 8, StopBits.One, Parity.None);
  }

  /// <summary>
  /// 初始化串口信息，波特率，数据位，停止位，奇偶校验需要全部自己来指定<br />
  /// Start serial port information, baud rate, data bit, stop bit, parity all need to be specified
  /// </summary>
  /// <param name="portName">端口号信息，例如"COM3"</param>
  /// <param name="baudRate">波特率</param>
  /// <param name="dataBits">数据位</param>
  /// <param name="stopBits">停止位</param>
  /// <param name="parity">奇偶校验</param>
  public void SerialPortInni(
    string portName,
    int baudRate,
    int dataBits,
    StopBits stopBits,
    Parity parity)
  {
    if (this.serialPort.IsOpen)
      return;
    this.serialPort.PortName = portName;
    this.serialPort.BaudRate = baudRate;
    this.serialPort.DataBits = dataBits;
    this.serialPort.StopBits = stopBits;
    this.serialPort.Parity = parity;
  }

  /// <summary>
  /// 根据自定义初始化方法进行初始化串口信息<br />
  /// Initialize the serial port information according to the custom initialization method
  /// </summary>
  /// <param name="initi">初始化的委托方法</param>
  public void SerialPortInni(Action<SerialPort> initi)
  {
    if (this.serialPort.IsOpen)
      return;
    this.serialPort.PortName = "COM1";
    initi(this.serialPort);
  }

  /// <summary>
  /// 获取一个值，指示串口是否处于打开状态<br />
  /// Gets a value indicating whether the serial port is open
  /// </summary>
  /// <returns>是或否</returns>
  public bool IsOpen() => this.serialPort.IsOpen;

  /// <summary>
  /// 获取当前的串口对象信息<br />
  /// Get current serial port object information
  /// </summary>
  /// <returns>串口对象</returns>
  public SerialPort GetPipe() => this.serialPort;

  /// <summary>
  /// 清除串口缓冲区的数据，并返回该数据，如果缓冲区没有数据，返回的字节数组长度为0<br />
  /// The number sent clears the data in the serial port buffer and returns that data, or if there is no data in the buffer, the length of the byte array returned is 0
  /// </summary>
  /// <returns>是否操作成功的方法</returns>
  public OperateResult<byte[]> ClearSerialCache()
  {
    return this.SPReceived(this.serialPort, (INetMessage) null, (byte[]) null, false);
  }

  /// <inheritdoc />
  public override OperateResult<bool> OpenCommunication()
  {
    try
    {
      if (this.serialPort.IsOpen)
        return OperateResult.CreateSuccessResult<bool>(false);
      this.serialPort.Open();
      this.ResetConnectErrorCount();
      return OperateResult.CreateSuccessResult<bool>(true);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool>("OpenCommunication failed: " + ex.Message);
    }
  }

  /// <inheritdoc />
  public override OperateResult CloseCommunication()
  {
    if (this.serialPort.IsOpen)
    {
      try
      {
        this.serialPort.Close();
      }
      catch (Exception ex)
      {
        return new OperateResult(ex.Message);
      }
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult Send(byte[] data, int offset, int size)
  {
    if (data == null || data.Length == 0)
      return OperateResult.CreateSuccessResult();
    if (!Authorization.nzugaydgwadawdibbas())
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    try
    {
      this.serialPort.Write(data, offset, size);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return new OperateResult(-this.IncrConnectErrorCount(), ex.Message);
    }
  }

  /// <inheritdoc />
  public override OperateResult<int> Receive(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    if (!Authorization.nzugaydgwadawdibbas())
      return new OperateResult<int>(StringResources.Language.AuthorizationFailed);
    try
    {
      return length > 0 ? OperateResult.CreateSuccessResult<int>(this.serialPort.Read(buffer, offset, length)) : OperateResult.CreateSuccessResult<int>(this.serialPort.Read(buffer, offset, buffer.Length - offset));
    }
    catch (Exception ex)
    {
      return new OperateResult<int>(-this.IncrConnectErrorCount(), ex.Message);
    }
  }

  /// <summary>
  /// 从串口接收一串字节数据信息，直到没有数据为止，如果参数awaitData为false, 第一轮接收没有数据则返回<br />
  /// Receives a string of bytes of data information from the serial port until there is no data, and returns if the parameter awaitData is false
  /// </summary>
  /// <param name="serialPort">串口对象</param>
  /// <param name="netMessage">定义的消息体对象</param>
  /// <param name="sendValue">等待发送的数据对象</param>
  /// <param name="awaitData">是否必须要等待数据返回</param>
  /// <param name="logMessage">用于消息记录的日志信息</param>
  /// <returns>结果数据对象</returns>
  private OperateResult<byte[]> SPReceived(
    SerialPort serialPort,
    INetMessage netMessage,
    byte[] sendValue,
    bool awaitData,
    Action<byte[]> logMessage = null)
  {
    if (!Authorization.nzugaydgwadawdibbas())
      return new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    byte[] buffer;
    MemoryStream ms;
    try
    {
      buffer = new byte[1024 /*0x0400*/];
      ms = new MemoryStream();
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
    DateTime now = DateTime.Now;
    int num1 = 0;
    int num2 = 0;
label_27:
    ++num2;
    if (num2 > 1 && this.SleepTime >= 0)
      HslHelper.ThreadSleep(this.SleepTime);
    try
    {
      TimeSpan timeSpan;
      if (serialPort.BytesToRead < 1)
      {
        if (num2 != 1)
        {
          timeSpan = DateTime.Now - now;
          if (timeSpan.TotalMilliseconds > (double) this.ReceiveTimeOut)
            return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), $"Time out: {this.ReceiveTimeOut}, received: {ms.ToArray().ToHexString(' ')}");
          if (ms.Length >= (long) this.AtLeastReceiveLength)
          {
            ++num1;
            if (netMessage != null || num1 < this.ReceiveEmptyDataCount)
              goto label_27;
          }
          else if (awaitData)
            goto label_27;
        }
        else
          goto label_27;
      }
      else
      {
        num1 = 0;
        int num3 = serialPort.Read(buffer, 0, buffer.Length);
        if (num3 > 0)
        {
          ms.Write(buffer, 0, num3);
          if (logMessage != null)
            logMessage(buffer.SelectBegin<byte>(num3));
        }
        if (netMessage != null)
        {
          if (this.CheckMessageComplete(netMessage, sendValue, ref ms))
            goto label_28;
        }
        int num4;
        if (this.ReceiveTimeOut > 0)
        {
          timeSpan = DateTime.Now - now;
          num4 = timeSpan.TotalMilliseconds > (double) this.ReceiveTimeOut ? 1 : 0;
        }
        else
          num4 = 0;
        if (num4 != 0)
          return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), $"Time out: {this.ReceiveTimeOut}, received: {ms.ToArray().ToHexString(' ')}");
        goto label_27;
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), ex.Message);
    }
label_28:
    this.ResetConnectErrorCount();
    return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReceiveMessage(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    return this.UseServerActivePush ? base.ReceiveMessage(netMessage, sendValue, useActivePush, reportProgress) : this.SPReceived(this.serialPort, netMessage, sendValue, true, logMessage);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    if (this.IsClearCacheBeforeRead)
      this.ClearSerialCache();
    OperateResult<byte[]> operateResult = this.ReadFromCoreServerHelper(netMessage, sendValue, hasResponseData, 0, logMessage);
    if (operateResult.IsSuccess)
      this.ResetConnectErrorCount();
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReceiveMessageAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    OperateResult<byte[]> messageAsync = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.SPReceived(this.serialPort, netMessage, sendValue, true, logMessage))).ConfigureAwait(false);
    return messageAsync;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    OperateResult<byte[]> operateResult = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.ReadFromCoreServer(netMessage, sendValue, hasResponseData, logMessage))).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    base.Dispose(disposing);
    this.serialPort?.Dispose();
  }

  /// <inheritdoc />
  public override string ToString() => $"PipeSerialPort[{this.serialPort.ToFormatString()}]";
}
