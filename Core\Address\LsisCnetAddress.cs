﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.LsisCnetAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Linq;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>Lsis的Cnet协议的地址信息</summary>
public class LsisCnetAddress : DeviceAddressDataBase
{
  private const string CnetTypes = "PMLKFTCDSQINUZR";

  /// <summary>地址类型信息</summary>
  public string DataType { get; set; }

  /// <summary>获取报文里需要的地址格式信息</summary>
  /// <returns>报文地址</returns>
  public string GetAddressCommand() => $"%{this.DataType}B{this.AddressStart}";

  private static int CalculateByDataType(string address)
  {
    switch (address[1])
    {
      case 'B':
      case 'b':
        return Convert.ToInt32(address.Substring(2));
      case 'D':
      case 'd':
        return Convert.ToInt32(address.Substring(2)) * 4;
      case 'L':
      case 'l':
        return Convert.ToInt32(address.Substring(2)) * 8;
      case 'W':
      case 'w':
        return Convert.ToInt32(address.Substring(2)) * 2;
      case 'X':
      case 'x':
        return Convert.ToInt32(address.Substring(2)) * 2;
      default:
        return Convert.ToInt32(address.Substring(1)) * 2;
    }
  }

  /// <summary>
  /// 从实际三菱的地址里面解析出我们需要的地址类型<br />
  /// Resolve the type of address we need from the actual Mitsubishi address
  /// </summary>
  /// <param name="address">三菱的地址数据信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="isBit">是否读写bool的操作</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<LsisCnetAddress> ParseFrom(string address, ushort length, bool isBit)
  {
    if (!"PMLKFTCDSQINUZR".Contains<char>(address[0]))
      return new OperateResult<LsisCnetAddress>(StringResources.Language.NotSupportedDataType);
    LsisCnetAddress lsisCnetAddress = new LsisCnetAddress();
    lsisCnetAddress.Length = length;
    try
    {
      lsisCnetAddress.DataType = address.Substring(0, 1);
      if (lsisCnetAddress.DataType.Equals("U", StringComparison.OrdinalIgnoreCase) && address.Contains("."))
      {
        string[] strArray = address.SplitDot();
        if (strArray.Length == 2)
        {
          lsisCnetAddress.AddressStart = Convert.ToInt32(strArray[0].Substring(1), 16 /*0x10*/) * 32 /*0x20*/ + Convert.ToInt32(strArray[1]);
          lsisCnetAddress.AddressStart *= 2;
        }
        else if (strArray.Length >= 3)
        {
          lsisCnetAddress.AddressStart = Convert.ToInt32(strArray[0].Substring(1)) * 32 /*0x20*/ * 16 /*0x10*/ + Convert.ToInt32(strArray[1]) * 32 /*0x20*/ + Convert.ToInt32(strArray[2]);
          lsisCnetAddress.AddressStart *= 2;
        }
      }
      else if ((lsisCnetAddress.DataType.Equals("I", StringComparison.OrdinalIgnoreCase) || lsisCnetAddress.DataType.Equals("Q", StringComparison.OrdinalIgnoreCase)) && address.Contains("."))
      {
        string[] strArray = address.SplitDot();
        if (strArray.Length >= 3)
        {
          lsisCnetAddress.AddressStart = Convert.ToInt32(strArray[0].Substring(1)) * 16 /*0x10*/ * 4 + Convert.ToInt32(strArray[1]) * 4 + Convert.ToInt32(strArray[2]);
          lsisCnetAddress.AddressStart *= 2;
        }
      }
      else
      {
        switch (address[1])
        {
          case 'B':
          case 'b':
            lsisCnetAddress.AddressStart = Convert.ToInt32(address.Substring(2));
            break;
          case 'D':
          case 'd':
            lsisCnetAddress.AddressStart = Convert.ToInt32(address.Substring(2)) * 4;
            break;
          case 'L':
          case 'l':
            lsisCnetAddress.AddressStart = Convert.ToInt32(address.Substring(2)) * 8;
            break;
          case 'W':
          case 'w':
            lsisCnetAddress.AddressStart = Convert.ToInt32(address.Substring(2)) * 2;
            break;
          case 'X':
          case 'x':
            lsisCnetAddress.AddressStart = Convert.ToInt32(address.Substring(2)) * 2;
            break;
          default:
            lsisCnetAddress.AddressStart = Convert.ToInt32(address.Substring(1)) * 2;
            break;
        }
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<LsisCnetAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
    return OperateResult.CreateSuccessResult<LsisCnetAddress>(lsisCnetAddress);
  }
}
