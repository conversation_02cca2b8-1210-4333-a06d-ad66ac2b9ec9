﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecFxLinksServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>三菱的虚拟的FxLinks服务器</summary>
public class MelsecFxLinksServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer sBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer rBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>实例化一个虚拟的FxLinks服务器</summary>
  public MelsecFxLinksServer()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.LogMsgFormatBinary = false;
    this.xBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.yBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.mBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.sBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.dBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C.Station" />
  public byte Station { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C.SumCheck" />
  public bool SumCheck { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C.Format" />
  public int Format { get; set; } = 1;

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<MelsecFxLinksAddress> from = MelsecFxLinksAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if (from.Content.TypeCode == "X")
      return OperateResult.CreateSuccessResult<byte[]>(this.xBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.TypeCode == "Y")
      return OperateResult.CreateSuccessResult<byte[]>(this.yBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.TypeCode == "M")
      return OperateResult.CreateSuccessResult<byte[]>(this.mBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.TypeCode == "S")
      return OperateResult.CreateSuccessResult<byte[]>(this.sBuffer.GetBool(from.Content.AddressStart, (int) length * 16 /*0x10*/).ToByteArray());
    if (from.Content.TypeCode == "D")
      return OperateResult.CreateSuccessResult<byte[]>(this.dBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2));
    return from.Content.TypeCode == "R" ? OperateResult.CreateSuccessResult<byte[]>(this.rBuffer.GetBytes(from.Content.AddressStart * 2, (int) length * 2)) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<MelsecFxLinksAddress> from = MelsecFxLinksAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if (from.Content.TypeCode == "X")
      this.xBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.TypeCode == "Y")
      this.yBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.TypeCode == "M")
      this.mBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.TypeCode == "S")
      this.sBuffer.SetBool(value.ToBoolArray(), from.Content.AddressStart);
    else if (from.Content.TypeCode == "D")
    {
      this.dBuffer.SetBytes(value, from.Content.AddressStart * 2);
    }
    else
    {
      if (!(from.Content.TypeCode == "R"))
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      this.rBuffer.SetBytes(value, from.Content.AddressStart * 2);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<MelsecFxLinksAddress> from = MelsecFxLinksAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    if (from.Content.TypeCode == "X")
      return OperateResult.CreateSuccessResult<bool[]>(this.xBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.TypeCode == "Y")
      return OperateResult.CreateSuccessResult<bool[]>(this.yBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.TypeCode == "M")
      return OperateResult.CreateSuccessResult<bool[]>(this.mBuffer.GetBool(from.Content.AddressStart, (int) length));
    return from.Content.TypeCode == "S" ? OperateResult.CreateSuccessResult<bool[]>(this.sBuffer.GetBool(from.Content.AddressStart, (int) length)) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<MelsecFxLinksAddress> from = MelsecFxLinksAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if (from.Content.TypeCode == "X")
      this.xBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.TypeCode == "Y")
      this.yBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.TypeCode == "M")
    {
      this.mBuffer.SetBool(value, from.Content.AddressStart);
    }
    else
    {
      if (!(from.Content.TypeCode == "S"))
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      this.sBuffer.SetBool(value, from.Content.AddressStart);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) null;

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    OperateResult<byte[]> operateResult = this.ExtraMcCore(receive, this.Format);
    if (!operateResult.IsSuccess)
      return operateResult.ErrorCode < 256 /*0x0100*/ && operateResult.ErrorCode > 0 ? OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) operateResult.ErrorCode, (byte[]) null, this.Format)) : operateResult;
    string str = Encoding.ASCII.GetString(operateResult.Content, 0, 2);
    int num1;
    switch (str)
    {
      case "BR":
        return this.ReadBoolByCommand(operateResult.Content);
      case "WR":
        num1 = 1;
        break;
      default:
        num1 = str == "QR" ? 1 : 0;
        break;
    }
    if (num1 != 0)
      return this.ReadWordByCommand(operateResult.Content);
    int num2;
    switch (str)
    {
      case "BW":
        return this.WriteBoolByCommand(operateResult.Content);
      case "WW":
        num2 = 1;
        break;
      default:
        num2 = str == "QW" ? 1 : 0;
        break;
    }
    if (num2 != 0)
      return this.WriteWordByCommand(operateResult.Content);
    switch (str)
    {
      case "RR":
        return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, (byte[]) null, this.Format));
      case "RS":
        return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, (byte[]) null, this.Format));
      case "PC":
        return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, Encoding.ASCII.GetBytes("F3"), this.Format));
      default:
        return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 6, (byte[]) null, this.Format));
    }
  }

  private OperateResult<byte[]> ExtraMcCore(byte[] command, int format)
  {
    byte num = Convert.ToByte(Encoding.ASCII.GetString(command, 1, 2), 16 /*0x10*/);
    if ((int) this.Station != (int) num)
      return new OperateResult<byte[]>($"Station Not Match, need: {this.Station}  but: {num}");
    switch (format)
    {
      case 1:
        if (command[0] != (byte) 5)
          return new OperateResult<byte[]>("First Byte Must Start with ENQ(0x05)");
        if (!this.SumCheck)
          return OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(5, command.Length - 5));
        return !SoftLRC.CalculateAccAndCheck(command, 1, 2) ? new OperateResult<byte[]>(2, "Sum Check Failed!") : OperateResult.CreateSuccessResult<byte[]>(command.SelectMiddle<byte>(5, command.Length - 7));
      case 4:
        return command[command.Length - 1] == (byte) 10 && command[command.Length - 2] == (byte) 13 ? this.ExtraMcCore(command.RemoveLast<byte>(2), 1) : new OperateResult<byte[]>("In format 4 case, last two char must be CR(0x0d) and LF(0x0a)");
      default:
        return OperateResult.CreateSuccessResult<byte[]>(command);
    }
  }

  private int GetAddressOctOrTen(byte address)
  {
    return address == (byte) 88 || address == (byte) 89 ? 8 : 10;
  }

  private SoftBuffer GetAddressBuffer(byte address)
  {
    switch (address)
    {
      case 68:
        return this.dBuffer;
      case 77:
        return this.mBuffer;
      case 82:
        return this.rBuffer;
      case 83:
        return this.sBuffer;
      case 88:
        return this.xBuffer;
      case 89:
        return this.yBuffer;
      default:
        return (SoftBuffer) null;
    }
  }

  private OperateResult<byte[]> ReadBoolByCommand(byte[] command)
  {
    if (command[3] == (byte) 68 || command[3] == (byte) 82)
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 6, (byte[]) null, this.Format));
    int int32 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 4), this.GetAddressOctOrTen(command[3]));
    int length = Convert.ToInt32(Encoding.ASCII.GetString(command, 8, 2), 16 /*0x10*/);
    if (length == 0)
      length = 256 /*0x0100*/;
    SoftBuffer addressBuffer = this.GetAddressBuffer(command[3]);
    return addressBuffer == null ? OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 6, (byte[]) null, this.Format)) : OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, ((IEnumerable<bool>) addressBuffer.GetBool(int32, length)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>(), this.Format));
  }

  private OperateResult<byte[]> WriteBoolByCommand(byte[] command)
  {
    if (command[3] == (byte) 68 || command[3] == (byte) 82)
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 6, (byte[]) null, this.Format));
    int int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 4), this.GetAddressOctOrTen(command[3]));
    int int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(command, 8, 2), 16 /*0x10*/);
    bool[] array = ((IEnumerable<byte>) command.SelectMiddle<byte>(10, int32_2)).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)).ToArray<bool>();
    SoftBuffer addressBuffer = this.GetAddressBuffer(command[3]);
    if (addressBuffer == null)
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 6, (byte[]) null, this.Format));
    addressBuffer.SetBool(array, int32_1);
    return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, (byte[]) null, this.Format));
  }

  private OperateResult<byte[]> ReadWordByCommand(byte[] command)
  {
    if (command[3] == (byte) 88 || command[3] == (byte) 89 || command[3] == (byte) 77 || command[3] == (byte) 83)
    {
      int int32_1;
      int int32_2;
      if (command[0] == (byte) 81)
      {
        int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 6), this.GetAddressOctOrTen(command[3]));
        int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 2), 16 /*0x10*/);
      }
      else
      {
        int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 4), this.GetAddressOctOrTen(command[3]));
        int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(command, 8, 2), 16 /*0x10*/);
      }
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, Encoding.ASCII.GetBytes(this.GetAddressBuffer(command[3]).GetBool(int32_1, int32_2 * 16 /*0x10*/).ToByteArray().ToHexString()), this.Format));
    }
    if (command[3] != (byte) 68 && command[3] != (byte) 82)
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 6, (byte[]) null, this.Format));
    int int32_3;
    int int32_4;
    if (command[0] == (byte) 81)
    {
      int32_3 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 6));
      int32_4 = Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 2), 16 /*0x10*/);
    }
    else
    {
      int32_3 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 4));
      int32_4 = Convert.ToInt32(Encoding.ASCII.GetString(command, 8, 2), 16 /*0x10*/);
    }
    return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, Encoding.ASCII.GetBytes(this.GetAddressBuffer(command[3]).GetBytes(int32_3 * 2, int32_4 * 2).ToHexString()), this.Format));
  }

  private OperateResult<byte[]> WriteWordByCommand(byte[] command)
  {
    if (command[3] == (byte) 88 || command[3] == (byte) 89 || command[3] == (byte) 77 || command[3] == (byte) 83)
    {
      int int32_1;
      bool[] boolArray;
      if (command[0] == (byte) 81)
      {
        int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 6), this.GetAddressOctOrTen(command[3]));
        int int32_2 = Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 2), 16 /*0x10*/);
        boolArray = Encoding.ASCII.GetString(command, 12, int32_2 * 4).ToHexBytes().ToBoolArray();
      }
      else
      {
        int32_1 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 4), this.GetAddressOctOrTen(command[3]));
        int int32_3 = Convert.ToInt32(Encoding.ASCII.GetString(command, 8, 2), 16 /*0x10*/);
        boolArray = Encoding.ASCII.GetString(command, 10, int32_3 * 4).ToHexBytes().ToBoolArray();
      }
      this.GetAddressBuffer(command[3]).SetBool(boolArray, int32_1);
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, (byte[]) null, this.Format));
    }
    if (command[3] != (byte) 68 && command[3] != (byte) 82)
      return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 6, (byte[]) null, this.Format));
    int int32_4;
    byte[] hexBytes;
    if (command[0] == (byte) 81)
    {
      int32_4 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 6));
      int int32_5 = Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 2), 16 /*0x10*/);
      hexBytes = Encoding.ASCII.GetString(command, 12, int32_5 * 4).ToHexBytes();
    }
    else
    {
      int32_4 = Convert.ToInt32(Encoding.ASCII.GetString(command, 4, 4));
      int int32_6 = Convert.ToInt32(Encoding.ASCII.GetString(command, 8, 2), 16 /*0x10*/);
      hexBytes = Encoding.ASCII.GetString(command, 10, int32_6 * 4).ToHexBytes();
    }
    this.GetAddressBuffer(command[3]).SetBytes(hexBytes, int32_4 * 2);
    return OperateResult.CreateSuccessResult<byte[]>(this.PackCommand((byte) 0, (byte[]) null, this.Format));
  }

  /// <inheritdoc />
  protected byte[] PackCommand(byte status, byte[] data, int format)
  {
    if (data == null)
      data = new byte[0];
    if (data.Length == 0)
    {
      switch (format)
      {
        case 1:
          if (status == (byte) 0)
          {
            byte[] bytes = Encoding.ASCII.GetBytes("\u0006F9FF");
            SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes, 1);
            return bytes;
          }
          byte[] bytes1 = Encoding.ASCII.GetBytes("\u001500FF00");
          SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes1, 1);
          SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes1, 5);
          return bytes1;
        case 4:
          return SoftBasic.SpliceArray<byte>(this.PackCommand(status, data, 1), new byte[2]
          {
            (byte) 13,
            (byte) 10
          });
        default:
          return (byte[]) null;
      }
    }
    else
    {
      switch (format)
      {
        case 1:
          if (status > (byte) 0)
          {
            byte[] bytes2 = Encoding.ASCII.GetBytes("\u001500FF00");
            SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) bytes2, 1);
            SoftBasic.BuildAsciiBytesFrom(status).CopyTo((Array) bytes2, 5);
            return bytes2;
          }
          byte[] buffer = new byte[(this.SumCheck ? 8 : 6) + data.Length];
          buffer[0] = (byte) 2;
          SoftBasic.BuildAsciiBytesFrom(this.Station).CopyTo((Array) buffer, 1);
          Encoding.ASCII.GetBytes("FF").CopyTo((Array) buffer, 3);
          data.CopyTo((Array) buffer, 5);
          buffer[buffer.Length - (this.SumCheck ? 3 : 1)] = (byte) 3;
          if (this.SumCheck)
            SoftLRC.CalculateAccAndFill(buffer, 1, 2);
          return buffer;
        case 4:
          return SoftBasic.SpliceArray<byte>(this.PackCommand(status, data, 1), new byte[2]
          {
            (byte) 13,
            (byte) 10
          });
        default:
          return (byte[]) null;
      }
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecFxLinksServer[{this.Port}]";
}
