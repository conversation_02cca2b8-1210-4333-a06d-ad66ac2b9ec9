﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.CommunicationLockSimple
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Threading;

#nullable disable
namespace HslCommunication.Core;

/// <summary>用于通信的一个简单的基元混合锁</summary>
public class CommunicationLockSimple : CommunicationLockNone
{
  private int m_waiters = 0;
  private readonly AutoResetEvent m_waiterLock = new AutoResetEvent(false);

  /// <inheritdoc />
  public override OperateResult EnterLock(int timeout)
  {
    try
    {
      return Interlocked.Increment(ref this.m_waiters) == 1 ? OperateResult.CreateSuccessResult() : (this.m_waiterLock.WaitOne(timeout) ? OperateResult.CreateSuccessResult() : new OperateResult($"Enter lock failed, timeout: {timeout}"));
    }
    catch (Exception ex)
    {
      return new OperateResult("Enter lock failed, message: " + ex.Message);
    }
  }

  /// <inheritdoc />
  public override void LeaveLock()
  {
    if (Interlocked.Decrement(ref this.m_waiters) == 0)
      return;
    this.m_waiterLock.Set();
  }

  /// <summary>
  /// 获取当前锁是否在等待当中<br />
  /// Gets whether the current lock is waiting
  /// </summary>
  public bool IsWaitting => this.m_waiters != 0;

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (!disposing)
      ;
    this.m_waiterLock.Close();
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"CommunicationLockSimple[{(this.IsWaitting ? "Locking" : "Unlock")}]";
  }
}
