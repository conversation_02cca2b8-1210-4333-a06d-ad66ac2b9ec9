﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Serial.SoftLRC
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Serial;

/// <summary>
/// 用于LRC验证的类，提供了标准的验证方法<br />
/// The class used for LRC verification provides a standard verification method
/// </summary>
public class SoftLRC
{
  /// <summary>
  /// 获取对应的数据的LRC校验码<br />
  /// Class for LRC validation that provides a standard validation method
  /// </summary>
  /// <param name="value">需要校验的数据，不包含LRC字节</param>
  /// <returns>返回带LRC校验码的字节数组，可用于串口发送</returns>
  public static byte[] LRC(byte[] value)
  {
    if (value == null)
      return (byte[]) null;
    int num = 0;
    for (int index = 0; index < value.Length; ++index)
      num += (int) value[index];
    byte[] numArray = new byte[1]
    {
      (byte) (256 /*0x0100*/ - num % 256 /*0x0100*/)
    };
    return SoftBasic.SpliceArray<byte>(value, numArray);
  }

  /// <summary>
  /// 获取对应的数据的LRC校验码<br />
  /// Class for LRC validation that provides a standard validation method
  /// </summary>
  /// <param name="value">需要校验的数据，不包含LRC字节</param>
  /// <param name="left">忽略的左边的字节数量</param>
  /// <param name="right">忽略的右边的字节数量</param>
  /// <returns>返回LRC校验码</returns>
  public static byte LRC(byte[] value, int left, int right)
  {
    int num = 0;
    for (int index = left; index < value.Length - right; ++index)
      num += (int) value[index];
    return (byte) (256 /*0x0100*/ - num % 256 /*0x0100*/);
  }

  /// <summary>
  /// 检查数据是否符合LRC的验证<br />
  /// Check data for compliance with LRC validation
  /// </summary>
  /// <param name="value">等待校验的数据，是否正确</param>
  /// <returns>是否校验成功</returns>
  public static bool CheckLRC(byte[] value)
  {
    if (value == null)
      return false;
    int length = value.Length;
    byte[] destinationArray = new byte[length - 1];
    Array.Copy((Array) value, 0, (Array) destinationArray, 0, destinationArray.Length);
    return (int) SoftLRC.LRC(destinationArray)[length - 1] == (int) value[length - 1];
  }

  /// <inheritdoc cref="M:HslCommunication.Serial.SoftLRC.CalculateAcc(System.Byte[],System.Int32,System.Int32)" />
  public static int CalculateAcc(byte[] buffer) => SoftLRC.CalculateAcc(buffer, 0, 0);

  /// <summary>
  /// 根据传入的原始字节数组，计算和校验信息，可以指定起始的偏移地址和尾部的字节数量信息<br />
  /// Calculate and check the information according to the incoming original byte array, you can specify the starting offset address and the number of bytes at the end
  /// </summary>
  /// <param name="buffer">原始字节数组信息</param>
  /// <param name="headCount">起始的偏移地址信息</param>
  /// <param name="lastCount">尾部的字节数量信息</param>
  /// <returns>和校验的结果</returns>
  public static int CalculateAcc(byte[] buffer, int headCount, int lastCount)
  {
    int acc = 0;
    for (int index = headCount; index < buffer.Length - lastCount; ++index)
      acc += (int) buffer[index];
    return acc;
  }

  /// <summary>
  /// 计算数据的和校验，并且输入和校验的值信息<br />
  /// Calculate the sum check of the data, and enter the value information of the sum check
  /// </summary>
  /// <param name="buffer">原始字节数组信息</param>
  /// <param name="headCount">起始的偏移地址信息</param>
  /// <param name="lastCount">尾部的字节数量信息</param>
  public static void CalculateAccAndFill(byte[] buffer, int headCount, int lastCount)
  {
    Encoding.ASCII.GetBytes(((byte) SoftLRC.CalculateAcc(buffer, headCount, lastCount)).ToString("X2")).CopyTo((Array) buffer, buffer.Length - lastCount);
  }

  /// <summary>
  /// 计算数据的和校验，并且和当前已经存在的和校验信息进行匹配，返回是否匹配成功<br />
  /// Calculate the sum check of the data, and match it with the existing sum check information, and return whether the match is successful
  /// </summary>
  /// <param name="buffer">原始字节数组信息</param>
  /// <param name="headCount">起始的偏移地址信息</param>
  /// <param name="lastCount">尾部的字节数量信息</param>
  /// <returns>和校验是否检查通过</returns>
  public static bool CalculateAccAndCheck(byte[] buffer, int headCount, int lastCount)
  {
    return ((byte) SoftLRC.CalculateAcc(buffer, headCount, lastCount)).ToString("X2") == Encoding.ASCII.GetString(buffer, buffer.Length - lastCount, 2);
  }

  /// <summary>计算数据的异或信息，也称为 FCS，可以指定前面无用的字节数量，以及尾部无用的字节数量</summary>
  /// <param name="source">数据源信息</param>
  /// <param name="left">前面无用的字节数量</param>
  /// <param name="right">后面无用的字节数量</param>
  /// <returns>返回异或校验后的值</returns>
  public static byte[] CalculateFcs(byte[] source, int left, int right)
  {
    int num = (int) source[left];
    for (int index = left + 1; index < source.Length - right; ++index)
      num ^= (int) source[index];
    return new byte[2]
    {
      SoftBasic.BuildAsciiBytesFrom((byte) num)[0],
      SoftBasic.BuildAsciiBytesFrom((byte) num)[1]
    };
  }
}
