﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.SpecifiedCharacterMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>专门用于接收指定字符结尾的网络消息</summary>
public class SpecifiedCharacterMessage : NetMessageBase, INetMessage
{
  private int protocolHeadBytesLength = -1;

  /// <summary>
  /// 使用固定的一个字符结尾作为当前的报文接收条件，来实例化一个对象<br />
  /// Instantiate an object using a fixed end of one character as the current message reception condition
  /// </summary>
  /// <param name="endCode">结尾的字符</param>
  public SpecifiedCharacterMessage(byte endCode)
  {
    byte[] numArray = new byte[4];
    numArray[3] = (byte) ((uint) numArray[3] | 128U /*0x80*/);
    numArray[3] = (byte) ((uint) numArray[3] | 1U);
    numArray[1] = endCode;
    this.protocolHeadBytesLength = BitConverter.ToInt32(numArray, 0);
  }

  /// <summary>
  /// 使用固定的两个个字符结尾作为当前的报文接收条件，来实例化一个对象<br />
  /// Instantiate an object using a fixed two-character end as the current message reception condition
  /// </summary>
  /// <param name="endCode1">第一个结尾的字符</param>
  /// <param name="endCode2">第二个结尾的字符</param>
  public SpecifiedCharacterMessage(byte endCode1, byte endCode2)
  {
    byte[] numArray = new byte[4];
    numArray[3] = (byte) ((uint) numArray[3] | 128U /*0x80*/);
    numArray[3] = (byte) ((uint) numArray[3] | 2U);
    numArray[1] = endCode1;
    numArray[0] = endCode2;
    this.protocolHeadBytesLength = BitConverter.ToInt32(numArray, 0);
  }

  /// <summary>
  /// 获取或设置在结束字符之后剩余的固定字节长度，有些则还包含两个字节的校验码，这时该值就需要设置为2。<br />
  /// Gets or sets the remaining fixed byte length after the end character, and some also contain a two-byte check code. In this case, the value needs to be set to 2.
  /// </summary>
  public byte EndLength
  {
    get => BitConverter.GetBytes(this.protocolHeadBytesLength)[2];
    set
    {
      byte[] bytes = BitConverter.GetBytes(this.protocolHeadBytesLength);
      bytes[2] = value;
      this.protocolHeadBytesLength = BitConverter.ToInt32(bytes, 0);
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => this.protocolHeadBytesLength;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => 0;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token) => true;
}
