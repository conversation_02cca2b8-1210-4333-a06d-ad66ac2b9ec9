﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Special.EcFanData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Special;

/// <summary>数据对象</summary>
public class EcFanData
{
  /// <summary>应急状态，True表示应急状态，False表示正常</summary>
  public bool EmergencyState { get; set; } = false;

  /// <summary>运行状态，True表示运行，False表示停止</summary>
  public bool RunState { get; set; }

  /// <summary>堵转状态，True表示堵转，False表示正常</summary>
  public bool LockState { get; set; }

  /// <summary>过热状态，True表示过热，False表示正常</summary>
  public bool OverHotState { get; set; }

  /// <summary>失速状态，True表示失速，False表示正常</summary>
  public bool LostSpeedState { get; set; }

  /// <summary>实际的转速</summary>
  public int Speed { get; set; } = 0;

  /// <inheritdoc />
  public override string ToString()
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.AppendLine("应急状态: " + (this.EmergencyState ? "处于应急状态" : "正常状态"));
    stringBuilder.AppendLine("运行状态: " + (this.RunState ? "运行中" : "停止"));
    stringBuilder.AppendLine("堵转状态: " + (this.LockState ? "处于堵转中" : "正常状态"));
    stringBuilder.AppendLine("过热状态: " + (this.OverHotState ? "驱动器处于过热状态" : "驱动器处于正常状态"));
    stringBuilder.AppendLine("失速状态: " + (this.LostSpeedState ? "处于失速中" : "正常状态"));
    stringBuilder.Append("实际转速: " + this.Speed.ToString());
    return stringBuilder.ToString();
  }
}
