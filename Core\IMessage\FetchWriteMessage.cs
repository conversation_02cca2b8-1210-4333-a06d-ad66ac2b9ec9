﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.FetchWriteMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>西门子Fetch/Write消息解析协议</summary>
public class FetchWriteMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 16 /*0x10*/;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    if (this.HeadBytes[5] == (byte) 5 || this.HeadBytes[5] == (byte) 4)
      return 0;
    if (this.HeadBytes[5] == (byte) 6)
    {
      if (this.SendBytes == null || this.HeadBytes[8] > (byte) 0)
        return 0;
      return this.SendBytes[8] == (byte) 1 || this.SendBytes[8] == (byte) 6 || this.SendBytes[8] == (byte) 7 ? ((int) this.SendBytes[12] * 256 /*0x0100*/ + (int) this.SendBytes[13]) * 2 : (int) this.SendBytes[12] * 256 /*0x0100*/ + (int) this.SendBytes[13];
    }
    if (this.HeadBytes[5] != (byte) 3)
      return 0;
    return this.HeadBytes[8] == (byte) 1 || this.HeadBytes[8] == (byte) 6 || this.HeadBytes[8] == (byte) 7 ? ((int) this.HeadBytes[12] * 256 /*0x0100*/ + (int) this.HeadBytes[13]) * 2 : (int) this.HeadBytes[12] * 256 /*0x0100*/ + (int) this.HeadBytes[13];
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes != null && this.HeadBytes[0] == (byte) 83 && this.HeadBytes[1] == (byte) 53;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetHeadBytesIdentity" />
  public override int GetHeadBytesIdentity() => (int) this.HeadBytes[3];
}
