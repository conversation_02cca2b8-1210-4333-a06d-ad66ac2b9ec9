﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.HslMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>本组件系统使用的默认的消息规则，说明解析和反解析规则的</summary>
public class HslMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 32 /*0x20*/;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    if (this.HeadBytes == null)
      return false;
    byte[] headBytes = this.HeadBytes;
    return headBytes != null && headBytes.Length >= 32 /*0x20*/ && SoftBasic.IsTwoBytesEquel(this.HeadBytes, 12, token, 0, 16 /*0x10*/);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    byte[] headBytes = this.HeadBytes;
    return headBytes != null && headBytes.Length >= 32 /*0x20*/ ? BitConverter.ToInt32(this.HeadBytes, 28) : 0;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetHeadBytesIdentity" />
  public override int GetHeadBytesIdentity()
  {
    byte[] headBytes = this.HeadBytes;
    return headBytes != null && headBytes.Length >= 32 /*0x20*/ ? BitConverter.ToInt32(this.HeadBytes, 4) : 0;
  }
}
