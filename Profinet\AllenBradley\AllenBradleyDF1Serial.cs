﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyDF1Serial
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Reflection;
using HslCommunication.Serial;
using System;
using System.IO;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>
/// AB-PLC的DF1通信协议，基于串口实现，通信机制为半双工，目前适用于 Micro-Logix1000,SLC500,SLC 5/03,SLC 5/04，地址示例：N7:1
/// </summary>
public class AllenBradleyDF1Serial : DeviceSerialPort
{
  private SoftIncrementCount incrementCount;

  /// <summary>
  /// Instantiate a communication object for a Allenbradley PLC protocol
  /// </summary>
  public AllenBradleyDF1Serial()
  {
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.incrementCount = new SoftIncrementCount((long) ushort.MaxValue);
    this.CheckType = CheckType.CRC16;
  }

  /// <summary>站号信息</summary>
  public byte Station { get; set; }

  /// <summary>目标节点号</summary>
  public byte DstNode { get; set; }

  /// <summary>源节点号</summary>
  public byte SrcNode { get; set; }

  /// <summary>校验方式</summary>
  public CheckType CheckType { get; set; }

  /// <summary>
  /// 读取PLC的原始数据信息，地址示例：N7:0  可以携带站号 s=2;N7:0, 携带 dst 和 src 信息，例如 dst=1;src=2;N7:0
  /// </summary>
  /// <param name="address">PLC的地址信息，支持的类型见类型注释说明</param>
  /// <param name="length">读取的长度，单位，字节</param>
  /// <returns>是否读取成功的结果对象</returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station);
    OperateResult<byte[]> operateResult1 = AllenBradleyDF1Serial.BuildProtectedTypedLogicalReadWithThreeAddressFields((byte) HslHelper.ExtractParameter(ref address, "dst", (int) this.DstNode), (byte) HslHelper.ExtractParameter(ref address, "src", (int) this.SrcNode), (int) this.incrementCount.GetCurrentValue(), address, (int) length);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommand(parameter, operateResult1.Content));
    return !operateResult2.IsSuccess ? operateResult2 : AllenBradleyDF1Serial.ExtractActualData(operateResult2.Content);
  }

  /// <summary>
  /// 写入PLC的原始数据信息，地址示例：N7:0  可以携带站号 s=2;N7:0, 携带 dst 和 src 信息，例如 dst=1;src=2;N7:0
  /// </summary>
  /// <param name="address">PLC的地址信息，支持的类型见类型注释说明</param>
  /// <param name="value">原始的数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) this.Station);
    OperateResult<byte[]> operateResult1 = AllenBradleyDF1Serial.BuildProtectedTypedLogicalWriteWithThreeAddressFields((byte) HslHelper.ExtractParameter(ref address, "dst", (int) this.DstNode), (byte) HslHelper.ExtractParameter(ref address, "src", (int) this.SrcNode), (int) this.incrementCount.GetCurrentValue(), address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.PackCommand(parameter, operateResult1.Content));
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) AllenBradleyDF1Serial.ExtractActualData(operateResult2.Content);
  }

  private byte[] CalculateCheckResult(byte station, byte[] command)
  {
    if (this.CheckType == CheckType.BCC)
    {
      int num = (int) station;
      for (int index = 0; index < command.Length; ++index)
        num += (int) command[index];
      return new byte[1]{ (byte) ((int) (byte) ~num + 1) };
    }
    return SoftCRC16.CRC16(SoftBasic.SpliceArray<byte>(new byte[1]
    {
      station
    }, new byte[1]{ (byte) 2 }, command, new byte[1]
    {
      (byte) 3
    }), (byte) 160 /*0xA0*/, (byte) 1, (byte) 0, (byte) 0).SelectLast<byte>(2);
  }

  /// <summary>打包命令的操作，加站号进行打包成完整的数据内容，命令内容为原始命令，打包后会自动补充0x10的值</summary>
  /// <param name="station">站号信息</param>
  /// <param name="command">等待发送的命令</param>
  /// <returns>打包之后的数据内容</returns>
  public byte[] PackCommand(byte station, byte[] command)
  {
    byte[] checkResult = this.CalculateCheckResult(station, command);
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 16 /*0x10*/);
    memoryStream.WriteByte((byte) 1);
    memoryStream.WriteByte(station);
    if (station == (byte) 16 /*0x10*/)
      memoryStream.WriteByte(station);
    memoryStream.WriteByte((byte) 16 /*0x10*/);
    memoryStream.WriteByte((byte) 2);
    for (int index = 0; index < command.Length; ++index)
    {
      memoryStream.WriteByte(command[index]);
      if (command[index] == (byte) 16 /*0x10*/)
        memoryStream.WriteByte(command[index]);
    }
    memoryStream.WriteByte((byte) 16 /*0x10*/);
    memoryStream.WriteByte((byte) 3);
    memoryStream.Write(checkResult, 0, checkResult.Length);
    return memoryStream.ToArray();
  }

  private static void AddLengthToMemoryStream(MemoryStream ms, ushort value)
  {
    if (value < (ushort) byte.MaxValue)
    {
      ms.WriteByte((byte) value);
    }
    else
    {
      ms.WriteByte(byte.MaxValue);
      ms.WriteByte(BitConverter.GetBytes(value)[0]);
      ms.WriteByte(BitConverter.GetBytes(value)[1]);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyDF1Serial.BuildProtectedTypedLogicalReadWithThreeAddressFields(System.Byte,System.Byte,System.Int32,System.String,System.Int32)" />
  public static OperateResult<byte[]> BuildProtectedTypedLogicalReadWithThreeAddressFields(
    int tns,
    string address,
    int length)
  {
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 15);
    ms.WriteByte((byte) 0);
    ms.WriteByte(BitConverter.GetBytes(tns)[0]);
    ms.WriteByte(BitConverter.GetBytes(tns)[1]);
    ms.WriteByte((byte) 162);
    ms.WriteByte(BitConverter.GetBytes(length)[0]);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, from.Content.DbBlock);
    ms.WriteByte(from.Content.DataCode);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, (ushort) from.Content.AddressStart);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, (ushort) 0);
    return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
  }

  /// <summary>
  /// 构建0F-A2命令码的报文读取指令，用来读取文件数据。适用 Micro-Logix1000,SLC500,SLC 5/03,SLC 5/04, PLC-5，地址示例：N7:1<br />
  /// Construct a message read instruction of 0F-A2 command code to read file data. Applicable to Micro-Logix1000, SLC500, SLC 5/03, SLC 5/04, PLC-5, address example: N7:1
  /// </summary>
  /// <param name="dstNode">目标节点号</param>
  /// <param name="srcNode">原节点号</param>
  /// <param name="tns">消息号</param>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <returns>初步的报文信息</returns>
  /// <remarks>
  /// 对于SLC 5/01或SLC 5/02而言，一次最多读取82个字节。对于 03 或是 04 为225，236字节取决于是否应用DF1驱动
  /// </remarks>
  public static OperateResult<byte[]> BuildProtectedTypedLogicalReadWithThreeAddressFields(
    byte dstNode,
    byte srcNode,
    int tns,
    string address,
    int length)
  {
    OperateResult<byte[]> result = AllenBradleyDF1Serial.BuildProtectedTypedLogicalReadWithThreeAddressFields(tns, address, length);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(new byte[2]
    {
      dstNode,
      srcNode
    }, result.Content));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyDF1Serial.BuildProtectedTypedLogicalWriteWithThreeAddressFields(System.Byte,System.Byte,System.Int32,System.String,System.Byte[])" />
  public static OperateResult<byte[]> BuildProtectedTypedLogicalWriteWithThreeAddressFields(
    int tns,
    string address,
    byte[] data)
  {
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 15);
    ms.WriteByte((byte) 0);
    ms.WriteByte(BitConverter.GetBytes(tns)[0]);
    ms.WriteByte(BitConverter.GetBytes(tns)[1]);
    ms.WriteByte((byte) 170);
    ms.WriteByte(BitConverter.GetBytes(data.Length)[0]);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, from.Content.DbBlock);
    ms.WriteByte(from.Content.DataCode);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, (ushort) from.Content.AddressStart);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, (ushort) 0);
    ms.Write(data);
    return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
  }

  /// <summary>
  /// 构建0F-AA命令码的写入读取指令，用来写入文件数据。适用 Micro-Logix1000,SLC500,SLC 5/03,SLC 5/04, PLC-5，地址示例：N7:1<br />
  /// Construct a write and read command of 0F-AA command code to write file data. Applicable to Micro-Logix1000, SLC500, SLC 5/03, SLC 5/04, PLC-5, address example: N7:1
  /// </summary>
  /// <param name="dstNode">目标节点号</param>
  /// <param name="srcNode">原节点号</param>
  /// <param name="tns">消息号</param>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="data">写入的数据内容</param>
  /// <returns>初步的报文信息</returns>
  /// <remarks>
  /// 对于SLC 5/01或SLC 5/02而言，一次最多读取82个字节。对于 03 或是 04 为225，236字节取决于是否应用DF1驱动
  /// </remarks>
  public static OperateResult<byte[]> BuildProtectedTypedLogicalWriteWithThreeAddressFields(
    byte dstNode,
    byte srcNode,
    int tns,
    string address,
    byte[] data)
  {
    OperateResult<byte[]> result = AllenBradleyDF1Serial.BuildProtectedTypedLogicalWriteWithThreeAddressFields(tns, address, data);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(new byte[2]
    {
      dstNode,
      srcNode
    }, result.Content));
  }

  /// <summary>构建0F-AB的掩码写入的功能</summary>
  /// <param name="tns">消息号</param>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="bitIndex">位索引信息</param>
  /// <param name="value">通断值</param>
  /// <returns>命令报文</returns>
  public static OperateResult<byte[]> BuildProtectedTypedLogicalMaskWithThreeAddressFields(
    int tns,
    string address,
    int bitIndex,
    bool value)
  {
    int num = 1 << bitIndex;
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 15);
    ms.WriteByte((byte) 0);
    ms.WriteByte(BitConverter.GetBytes(tns)[0]);
    ms.WriteByte(BitConverter.GetBytes(tns)[1]);
    ms.WriteByte((byte) 171);
    ms.WriteByte((byte) 2);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, from.Content.DbBlock);
    ms.WriteByte(from.Content.DataCode);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, (ushort) from.Content.AddressStart);
    AllenBradleyDF1Serial.AddLengthToMemoryStream(ms, (ushort) 0);
    ms.WriteByte(BitConverter.GetBytes(num)[0]);
    ms.WriteByte(BitConverter.GetBytes(num)[1]);
    if (value)
    {
      ms.WriteByte(BitConverter.GetBytes(num)[0]);
      ms.WriteByte(BitConverter.GetBytes(num)[1]);
    }
    else
    {
      ms.WriteByte((byte) 0);
      ms.WriteByte((byte) 0);
    }
    return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
  }

  /// <summary>
  /// 提取返回报文的数据内容，将其转换成实际的数据内容，如果PLC返回了错误信息，则结果对象为失败。<br />
  /// Extract the data content of the returned message and convert it into the actual data content. If the PLC returns an error message, the result object is a failure.
  /// </summary>
  /// <param name="content">PLC返回的报文信息</param>
  /// <returns>结果对象内容</returns>
  public static OperateResult<byte[]> ExtractActualData(byte[] content)
  {
    try
    {
      int num = -1;
      for (int index = 0; index < content.Length; ++index)
      {
        if (content[index] == (byte) 16 /*0x10*/ && content[index + 1] == (byte) 2)
        {
          num = index + 2;
          break;
        }
      }
      if (num < 0 || num >= content.Length - 6)
        return new OperateResult<byte[]>("Message must start with '10 02', source: " + content.ToHexString(' '));
      MemoryStream memoryStream = new MemoryStream();
      for (int index = num; index < content.Length - 1; ++index)
      {
        if (content[index] == (byte) 16 /*0x10*/ && content[index + 1] == (byte) 16 /*0x10*/)
        {
          memoryStream.WriteByte(content[index]);
          ++index;
        }
        else if (content[index] != (byte) 16 /*0x10*/ || content[index + 1] != (byte) 3)
          memoryStream.WriteByte(content[index]);
        else
          break;
      }
      content = memoryStream.ToArray();
      if (content[3] == (byte) 240 /*0xF0*/)
        return new OperateResult<byte[]>(AllenBradleyDF1Serial.GetExtStatusDescription(content[6]));
      if (content[3] > (byte) 0)
        return new OperateResult<byte[]>(AllenBradleyDF1Serial.GetStatusDescription(content[3]));
      return content.Length > 6 ? OperateResult.CreateSuccessResult<byte[]>(content.RemoveBegin<byte>(6)) : OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"{ex.Message} Source:{content.ToHexString(' ')}");
    }
  }

  /// <summary>根据错误代码，来获取错误的具体描述文本</summary>
  /// <param name="code">错误的代码，非0</param>
  /// <returns>错误的描述文本信息</returns>
  public static string GetStatusDescription(byte code)
  {
    byte num1 = (byte) ((uint) code & 15U);
    byte num2 = (byte) ((uint) code & 240U /*0xF0*/);
    switch (num1)
    {
      case 1:
        return "DST node is out of buffer space";
      case 2:
        return "Cannot guarantee delivery: link layer(The remote node specified does not ACK command.)";
      case 3:
        return "Duplicate token holder detected";
      case 4:
        return "Local port is disconnected";
      case 5:
        return "Application layer timed out waiting for a response";
      case 6:
        return "Duplicate node detected";
      case 7:
        return "Station is offline";
      case 8:
        return "Hardware fault";
      default:
        switch (num2)
        {
          case 16 /*0x10*/:
            return "Illegal command or format";
          case 32 /*0x20*/:
            return "Host has a problem and will not communicate";
          case 48 /*0x30*/:
            return "Remote node host is missing, disconnected, or shut down";
          case 64 /*0x40*/:
            return "Host could not complete function due to hardware fault";
          case 80 /*0x50*/:
            return "Addressing problem or memory protect rungs";
          case 96 /*0x60*/:
            return "Function not allowed due to command protection selection";
          case 112 /*0x70*/:
            return "Processor is in Program mode";
          case 128 /*0x80*/:
            return "Compatibility mode file missing or communication zone problem";
          case 144 /*0x90*/:
            return "Remote node cannot buffer command";
          case 160 /*0xA0*/:
            return "Wait ACK (1775\u0006KA buffer full)";
          case 176 /*0xB0*/:
            return "Remote node problem due to download";
          case 192 /*0xC0*/:
            return "Wait ACK (1775\u0006KA buffer full)";
          case 240 /*0xF0*/:
            return "Error code in the EXT STS byte";
          default:
            return StringResources.Language.UnknownError;
        }
    }
  }

  /// <summary>根据错误代码，来获取错误的具体描述文本</summary>
  /// <param name="code">错误的代码，非0</param>
  /// <returns>错误的描述文本信息</returns>
  public static string GetExtStatusDescription(byte code)
  {
    switch (code)
    {
      case 1:
        return "A field has an illegal value";
      case 2:
        return "Less levels specified in address than minimum for any address";
      case 3:
        return "More levels specified in address than system supports";
      case 4:
        return "Symbol not found";
      case 5:
        return "Symbol is of improper format";
      case 6:
        return "Address doesn’t point to something usable";
      case 7:
        return "File is wrong size";
      case 8:
        return "Cannot complete request, situation has changed since the start of the command";
      case 9:
        return "Data or file is too large";
      case 10:
        return "Transaction size plus word address is too large";
      case 11:
        return "Access denied, improper privilege";
      case 12:
        return "Condition cannot be generated \u0006 resource is not available";
      case 13:
        return "Condition already exists \u0006 resource is already available";
      case 14:
        return "Command cannot be executed";
      case 15:
        return "Histogram overflow";
      case 16 /*0x10*/:
        return "No access";
      case 17:
        return "Illegal data type";
      case 18:
        return "Invalid parameter or invalid data";
      case 19:
        return "Address reference exists to deleted area";
      case 20:
        return "Command execution failure for unknown reason; possible PLC\u00063 histogram overflow";
      case 21:
        return "Data conversion error";
      case 22:
        return "Scanner not able to communicate with 1771 rack adapter";
      case 23:
        return "Type mismatch";
      case 24:
        return "1771 module response was not valid";
      case 25:
        return "Duplicated label";
      case 26:
        return "File is open; another node owns it";
      case 27:
        return "Another node is the program owner";
      case 28:
        return "Reserved";
      case 29:
        return "Reserved";
      case 30:
        return "Data table element protection violation";
      case 31 /*0x1F*/:
        return "Temporary internal problem";
      case 34:
        return "Remote rack fault";
      case 35:
        return "Timeout";
      case 36:
        return "Unknown error";
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"AllenBradleyDF1Serial[{this.PortName}:{this.BaudRate}]";
}
