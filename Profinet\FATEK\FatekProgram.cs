﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.FATEK.FatekProgram
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.FATEK.Helper;
using HslCommunication.Reflection;

#nullable disable
namespace HslCommunication.Profinet.FATEK;

/// <summary>
/// 台湾永宏公司的编程口协议，具体的地址信息请查阅api文档信息，地址允许携带站号信息，例如：s=2;D100<br />
/// The programming port protocol of Taiwan Yonghong company,
/// please refer to the api document for specific address information, The address can carry station number information, such as s=2;D100
/// </summary>
/// <remarks>
/// <inheritdoc cref="T:HslCommunication.Profinet.FATEK.FatekProgramOverTcp" path="remarks" />
/// </remarks>
public class FatekProgram : DeviceSerialPort, IFatekProgram, IReadWriteNet
{
  private byte station = 1;

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.#ctor" />
  public FatekProgram()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
    this.ReceiveEmptyDataCount = 5;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new FatekProgramMessage();

  /// <inheritdoc cref="P:HslCommunication.Profinet.FATEK.FatekProgramOverTcp.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return FatekProgramHelper.Read((IReadWriteDevice) this, this.station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return FatekProgramHelper.Write((IReadWriteDevice) this, this.station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return FatekProgramHelper.ReadBool((IReadWriteDevice) this, this.station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return FatekProgramHelper.Write((IReadWriteDevice) this, this.station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Run(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult Run(byte station)
  {
    return FatekProgramHelper.Run((IReadWriteDevice) this, station);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgram.Run(System.Byte)" />
  [HslMqttApi("Run", "使PLC处于RUN状态")]
  public OperateResult Run() => this.Run(this.Station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Stop(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult Stop(byte station)
  {
    return FatekProgramHelper.Stop((IReadWriteDevice) this, station);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgram.Stop(System.Byte)" />
  [HslMqttApi("Stop", "使PLC处于STOP状态")]
  public OperateResult Stop() => this.Stop(this.Station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.ReadStatus(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult<bool[]> ReadStatus(byte station)
  {
    return FatekProgramHelper.ReadStatus((IReadWriteDevice) this, station);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.FatekProgram.ReadStatus(System.Byte)" />
  [HslMqttApi("ReadStatus", "读取PLC基本的状态信息")]
  public OperateResult<bool[]> ReadStatus() => this.ReadStatus(this.Station);

  /// <inheritdoc />
  public override string ToString() => $"FatekProgram[{this.PortName}:{this.BaudRate}]";
}
