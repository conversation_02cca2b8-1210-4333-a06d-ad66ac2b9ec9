﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Vigor.Helper.VigorHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System.Collections.Generic;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Vigor.Helper;

/// <summary>丰炜PLC的辅助方法</summary>
public class VigorHelper
{
  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read(System.String,System.UInt16)" />
  /// <remarks>
  /// 支持字地址，单次最多读取64字节，支持D,SD,R,T,C的数据读取，同时地址支持携带站号信息，s=2;D100
  /// </remarks>
  public static OperateResult<byte[]> Read(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<List<byte[]>> result1 = VigorVsHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) station), address, length, false);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      OperateResult<byte[]> operateResult = VigorVsHelper.CheckResponseContent(result2.Content);
      if (!operateResult.IsSuccess)
        return operateResult;
      byteList.AddRange((IEnumerable<byte>) operateResult.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  /// <remarks>
  /// 需要输入位地址，最多读取1024位，支持X,Y,M,SM,S,TS(定时器触点),TC（定时器线圈）,CS(计数器触点),CC（计数器线圈）
  /// </remarks>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<List<byte[]>> result1 = VigorVsHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) station), address, length, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult<byte[]> result3 = VigorVsHelper.CheckResponseContent(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result3);
      boolList.AddRange((IEnumerable<bool>) result3.Content.ToBoolArray().SelectBegin<bool>((int) length));
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Byte[])" />
  /// <remarks>
  /// 支持字地址，单次最多读取64字节，支持D,SD,R,T,C的数据写入，其中C199~C200不能连续写入，前者是16位计数器，后者是32位计数器
  /// </remarks>
  public static OperateResult Write(
    IReadWriteDevice plc,
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> result1 = VigorVsHelper.BuildWriteWordCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) station), address, value);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result2) : (OperateResult) VigorVsHelper.CheckResponseContent(result2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  /// <remarks>
  /// 支持位地址的写入，支持X,Y,M,SM,S,TS(定时器触点),TC（定时器线圈）,CS(计数器触点),CC（计数器线圈）
  /// </remarks>
  public static OperateResult Write(
    IReadWriteDevice plc,
    byte station,
    string address,
    bool[] value)
  {
    OperateResult<byte[]> result1 = VigorVsHelper.BuildWriteBoolCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) station), address, value);
    if (!result1.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result2) : (OperateResult) VigorVsHelper.CheckResponseContent(result2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<List<byte[]>> command = VigorVsHelper.BuildReadCommand(stat, address, length, false);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> result = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      OperateResult<byte[]> check = VigorVsHelper.CheckResponseContent(read.Content);
      if (!check.IsSuccess)
        return check;
      result.AddRange((IEnumerable<byte>) check.Content);
      read = (OperateResult<byte[]>) null;
      check = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(result.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    ushort length)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<List<byte[]>> command = VigorVsHelper.BuildReadCommand(stat, address, length, true);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<bool> result = new List<bool>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult<byte[]> check = VigorVsHelper.CheckResponseContent(read.Content);
      if (!check.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) check);
      result.AddRange((IEnumerable<bool>) check.Content.ToBoolArray().SelectBegin<bool>((int) length));
      read = (OperateResult<byte[]>) null;
      check = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(result.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    byte[] value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> command = VigorVsHelper.BuildWriteWordCommand(stat, address, value);
    if (!command.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? (OperateResult) VigorVsHelper.CheckResponseContent(read.Content) : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    byte station,
    string address,
    bool[] value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<byte[]> command = VigorVsHelper.BuildWriteBoolCommand(stat, address, value);
    if (!command.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? (OperateResult) VigorVsHelper.CheckResponseContent(read.Content) : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }
}
