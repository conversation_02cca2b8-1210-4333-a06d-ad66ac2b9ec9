﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecMcAsciiNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Melsec.Helper;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱PLC通讯类，采用Qna兼容3E帧协议实现，需要在PLC侧先的以太网模块先进行配置，必须为ASCII通讯格式<br />
/// Mitsubishi PLC communication class is implemented using Qna compatible 3E frame protocol.
/// The Ethernet module on the PLC side needs to be configured first. It must be ascii communication.
/// </summary>
/// <remarks>
/// <inheritdoc cref="T:HslCommunication.Profinet.Melsec.MelsecMcNet" path="remarks" />
/// </remarks>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecAscii.cs" region="Usage" title="简单的短连接使用" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecAscii.cs" region="Usage2" title="简单的长连接使用" />
/// </example>
public class MelsecMcAsciiNet : MelsecMcNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcNet.#ctor" />
  public MelsecMcAsciiNet()
  {
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcNet.#ctor(System.String,System.Int32)" />
  public MelsecMcAsciiNet(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new MelsecQnA3EAsciiMessage();

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.McType" />
  public override McType McType => McType.MCAscii;

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return McAsciiHelper.PackMcCommand((IReadWriteMc) this, command);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    OperateResult result = McAsciiHelper.CheckResponseContent(response);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(22));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.ExtractActualData(System.Byte[],System.Boolean)" />
  public override byte[] ExtractActualData(byte[] response, bool isBit)
  {
    return McAsciiHelper.ExtractActualDataHelper(response, isBit);
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecMcAsciiNet[{this.IpAddress}:{this.Port}]";
}
