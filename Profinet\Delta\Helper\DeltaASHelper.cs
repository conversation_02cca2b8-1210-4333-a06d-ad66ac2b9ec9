﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Delta.Helper.DeltaASHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System;

#nullable disable
namespace HslCommunication.Profinet.Delta.Helper;

/// <summary>台达AS300的辅助帮助类信息</summary>
public class DeltaASHelper
{
  private static int ParseDeltaBitAddress(string address)
  {
    int length = address.IndexOf('.');
    return length > 0 ? Convert.ToInt32(address.Substring(0, length)) * 16 /*0x10*/ + HslHelper.CalculateBitStartIndex(address.Substring(length + 1)) : Convert.ToInt32(address) * 16 /*0x10*/;
  }

  /// <summary>
  /// 根据台达AS300的PLC的地址，解析出转换后的modbus协议信息，适用AS300系列，当前的地址仍然支持站号指定，例如s=2;D100<br />
  /// According to the PLC address of Delta AS300, the converted modbus protocol information is parsed,
  /// and it is applicable to AS300 series. The current address still supports station number designation, for example, s=2;D100
  /// </summary>
  /// <param name="address">台达plc的地址信息</param>
  /// <param name="modbusCode">原始的对应的modbus信息</param>
  /// <returns>还原后的modbus地址</returns>
  public static OperateResult<string> ParseDeltaASAddress(string address, byte modbusCode)
  {
    try
    {
      string str = string.Empty;
      OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
      if (parameter.IsSuccess)
        str = $"s={parameter.Content};";
      if (modbusCode == (byte) 1 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWith("SM") || address.StartsWith("sm"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 16384 /*0x4000*/).ToString());
        if (address.StartsWith("HC") || address.StartsWith("hc"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 64512).ToString());
        if (address.StartsWith("S") || address.StartsWith("s"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 20480 /*0x5000*/).ToString());
        if (address.StartsWith("X") || address.StartsWith("x"))
          return OperateResult.CreateSuccessResult<string>($"{str}x=2;{(DeltaASHelper.ParseDeltaBitAddress(address.Substring(1)) + 24576 /*0x6000*/).ToString()}");
        if (address.StartsWith("Y") || address.StartsWith("y"))
          return OperateResult.CreateSuccessResult<string>(str + (DeltaASHelper.ParseDeltaBitAddress(address.Substring(1)) + 40960 /*0xA000*/).ToString());
        if (address.StartsWith("T") || address.StartsWith("t"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 57344 /*0xE000*/).ToString());
        if (address.StartsWith("C") || address.StartsWith("c"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 61440 /*0xF000*/).ToString());
        if (address.StartsWith("M") || address.StartsWith("m"))
          return OperateResult.CreateSuccessResult<string>(str + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWith("D") && address.Contains("."))
          return OperateResult.CreateSuccessResult<string>(str + address);
      }
      else
      {
        if (address.StartsWith("SR") || address.StartsWith("sr"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 49152 /*0xC000*/).ToString());
        if (address.StartsWith("HC") || address.StartsWith("hc"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(2)) + 64512).ToString());
        if (address.StartsWith("D") || address.StartsWith("d"))
          return OperateResult.CreateSuccessResult<string>(str + Convert.ToInt32(address.Substring(1)).ToString());
        if (address.StartsWith("X") || address.StartsWith("x"))
          return OperateResult.CreateSuccessResult<string>($"{str}x=4;{(Convert.ToInt32(address.Substring(1)) + 32768 /*0x8000*/).ToString()}");
        if (address.StartsWith("Y") || address.StartsWith("y"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 40960 /*0xA000*/).ToString());
        if (address.StartsWith("C") || address.StartsWith("c"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 61440 /*0xF000*/).ToString());
        if (address.StartsWith("T") || address.StartsWith("t"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 57344 /*0xE000*/).ToString());
        if (address.StartsWith("E") || address.StartsWith("e"))
          return OperateResult.CreateSuccessResult<string>(str + (Convert.ToInt32(address.Substring(1)) + 65024).ToString());
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }
}
