﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>
/// 三菱编程口协议的辅助方法，定义了如何读写bool数据，以及读写原始字节的数据。<br />
/// The auxiliary method of Mitsubishi programming port protocol defines how to read and write bool data and read and write raw byte data.
/// </summary>
public class MelsecFxSerialHelper
{
  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.NetMessageBase.CheckReceiveDataComplete(System.Byte[],System.IO.MemoryStream)" />
  public static bool CheckReceiveDataComplete(byte[] buffer)
  {
    if (buffer.Length == 0)
      return false;
    if (buffer.Length == 1 && (buffer[0] == (byte) 21 || buffer[0] == (byte) 6) || buffer.Length == 2 && buffer[0] == (byte) 6 && buffer[1] == (byte) 3)
      return true;
    int stxIndex = MelsecFxSerialHelper.FindSTXIndex(buffer);
    return stxIndex > 0 ? MelsecFxSerialHelper.CheckReceiveDataCompleteHelper(buffer.RemoveBegin<byte>(stxIndex)) : MelsecFxSerialHelper.CheckReceiveDataCompleteHelper(buffer);
  }

  private static bool CheckReceiveDataCompleteHelper(byte[] buffer)
  {
    return buffer[0] == (byte) 2 && buffer.Length >= 5 && buffer[buffer.Length - 3] == (byte) 3 && MelsecHelper.CheckCRC(buffer);
  }

  internal static int FindSTXIndex(byte[] buffer)
  {
    int stxIndex = 0;
    for (int index = 0; index < buffer.Length; ++index)
    {
      if (buffer[index] != (byte) 63 /*0x3F*/)
      {
        stxIndex = buffer[index] != (byte) 2 ? 0 : index;
        break;
      }
    }
    return stxIndex;
  }

  /// <summary>
  /// 根据指定的地址及长度信息从三菱PLC中读取原始的字节数据，根据PLC中实际定义的规则，可以解析出任何类的数据信息<br />
  /// Read the original byte data from the Mitsubishi PLC according to the specified address and length information.
  /// According to the rules actually defined in the PLC, any type of data information can be parsed
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">读取地址，，支持的类型参考文档说明</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>带成功标志的结果数据对象</returns>
  /// <example>
  /// 假设起始地址为D100，D100存储了温度，100.6℃值为1006，D101存储了压力，1.23Mpa值为123，D102，D103存储了产量计数，读取如下：
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecFxSerial.cs" region="ReadExample2" title="Read示例" />
  /// 以下是读取不同类型数据的示例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecFxSerial.cs" region="ReadExample1" title="Read示例" />
  /// </example>
  public static OperateResult<byte[]> Read(
    IReadWriteDevice plc,
    string address,
    ushort length,
    bool isNewVersion)
  {
    OperateResult<List<byte[]>> result1 = MelsecFxSerialHelper.BuildReadWordCommand(address, length, isNewVersion);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      OperateResult result3 = MelsecFxSerialHelper.CheckPlcReadResponse(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result3);
      OperateResult<byte[]> actualData = MelsecFxSerialHelper.ExtractActualData(result2.Content);
      if (!actualData.IsSuccess)
        return actualData;
      byteList.AddRange((IEnumerable<byte>) actualData.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>
  /// 从三菱PLC中批量读取位软元件，返回读取结果，该读取地址最好从0，16，32...等开始读取，这样可以读取比较长的数据数组<br />
  /// Read bit devices in batches from Mitsubishi PLC and return the read results.
  /// The read address should preferably be read from 0, 16, 32... etc., so that a relatively long data array can be read
  /// </summary>
  /// <param name="plc">PLC的通信对象</param>
  /// <param name="address">起始地址</param>
  /// <param name="length">读取的长度</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>带成功标志的结果数据对象</returns>
  /// <example>
  ///  <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecFxSerial.cs" region="ReadBool" title="Bool类型示例" />
  /// </example>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice plc,
    string address,
    ushort length,
    bool isNewVersion)
  {
    OperateResult<List<byte[]>, int> result1 = MelsecFxSerialHelper.BuildReadBoolCommand(address, length, isNewVersion);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content1.Count; ++index)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content1[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult result3 = MelsecFxSerialHelper.CheckPlcReadResponse(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(result3);
      OperateResult<byte[]> actualData = MelsecFxSerialHelper.ExtractActualData(result2.Content);
      if (!actualData.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) actualData);
      byteList.AddRange((IEnumerable<byte>) actualData.Content);
    }
    return OperateResult.CreateSuccessResult<bool[]>(byteList.ToArray().ToBoolArray().SelectMiddle<bool>(result1.Content2, (int) length));
  }

  /// <summary>
  /// 根据指定的地址向PLC写入数据，数据格式为原始的字节类型<br />
  /// Write data to the PLC according to the specified address, the data format is the original byte type
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">初始地址，支持的类型参考文档说明</param>
  /// <param name="value">原始的字节数据</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <example>
  /// 假设起始地址为D100，D100存储了温度，100.6℃值为1006，D101存储了压力，1.23Mpa值为123，D102，D103存储了产量计数，写入如下：
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecFxSerial.cs" region="WriteExample2" title="Write示例" />
  /// 以下是读取不同类型数据的示例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecFxSerial.cs" region="WriteExample1" title="Write示例" />
  /// </example>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    string address,
    byte[] value,
    bool isNewVersion)
  {
    OperateResult<byte[]> operateResult1 = MelsecFxSerialHelper.BuildWriteWordCommand(address, value, isNewVersion);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : MelsecFxSerialHelper.CheckPlcWriteResponse(operateResult2.Content);
  }

  /// <summary>根据指定的地址向PLC写入bool数组，数组的长度需要为16的倍数，以及地址的偏移地址也需要是16的倍数</summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">初始地址，支持的类型参考文档说明</param>
  /// <param name="value">bool数组值</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(
    IReadWriteDevice plc,
    string address,
    bool[] value,
    bool isNewVersion)
  {
    if (value == null)
      value = new bool[0];
    return value.Length % 16 /*0x10*/ != 0 ? new OperateResult(StringResources.Language.MelsecFxBoolLength16) : MelsecFxSerialHelper.Write(plc, address, value.ToByteArray(), isNewVersion);
  }

  /// <summary>
  /// 强制写入位数据的通断，支持的类型参考文档说明<br />
  /// The on-off of the forced write bit data, please refer to the document description for the supported types
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">是否为通</param>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult Write(IReadWriteDevice plc, string address, bool value)
  {
    OperateResult<byte[]> operateResult1 = MelsecFxSerialHelper.BuildWriteBoolPacket(address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : MelsecFxSerialHelper.CheckPlcWriteResponse(operateResult2.Content);
  }

  /// <summary>
  /// 激活PLC的接收状态，需要再和PLC交互之前进行调用，之后就需要再调用了。<br />
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <returns>是否激活成功</returns>
  public static OperateResult ActivePlc(IReadWriteDevice plc)
  {
    OperateResult<byte[]> operateResult1 = plc.ReadFromCoreServer(new byte[1]
    {
      (byte) 5
    });
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    if (operateResult1.Content[0] != (byte) 6)
      return new OperateResult("Send ENQ(0x05), Check Receive ACK(0x06) failed");
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(new byte[11]
    {
      (byte) 2,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 69,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 3,
      (byte) 54,
      (byte) 67
    });
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    return (OperateResult) plc.ReadFromCoreServer(new byte[11]
    {
      (byte) 2,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 69,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 3,
      (byte) 54,
      (byte) 67
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.Read(HslCommunication.Core.IReadWriteDevice,System.String,System.UInt16,System.Boolean)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice plc,
    string address,
    ushort length,
    bool isNewVersion)
  {
    OperateResult<List<byte[]>> command = MelsecFxSerialHelper.BuildReadWordCommand(address, length, isNewVersion);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> array = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      OperateResult ackResult = MelsecFxSerialHelper.CheckPlcReadResponse(read.Content);
      if (!ackResult.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(ackResult);
      OperateResult<byte[]> extra = MelsecFxSerialHelper.ExtractActualData(read.Content);
      if (!extra.IsSuccess)
        return extra;
      array.AddRange((IEnumerable<byte>) extra.Content);
      read = (OperateResult<byte[]>) null;
      ackResult = (OperateResult) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(array.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.String,System.UInt16,System.Boolean)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice plc,
    string address,
    ushort length,
    bool isNewVersion)
  {
    OperateResult<List<byte[]>, int> command = MelsecFxSerialHelper.BuildReadBoolCommand(address, length, isNewVersion);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<byte> array = new List<byte>();
    for (int i = 0; i < command.Content1.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content1[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult ackResult = MelsecFxSerialHelper.CheckPlcReadResponse(read.Content);
      if (!ackResult.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>(ackResult);
      OperateResult<byte[]> extra = MelsecFxSerialHelper.ExtractActualData(read.Content);
      if (!extra.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) extra);
      array.AddRange((IEnumerable<byte>) extra.Content);
      read = (OperateResult<byte[]>) null;
      ackResult = (OperateResult) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(array.ToArray().ToBoolArray().SelectMiddle<bool>(command.Content2, (int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.Write(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte[],System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    string address,
    byte[] value,
    bool isNewVersion)
  {
    OperateResult<byte[]> command = MelsecFxSerialHelper.BuildWriteWordCommand(address, value, isNewVersion);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? MelsecFxSerialHelper.CheckPlcWriteResponse(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.Write(HslCommunication.Core.IReadWriteDevice,System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice plc,
    string address,
    bool value)
  {
    OperateResult<byte[]> command = MelsecFxSerialHelper.BuildWriteBoolPacket(address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? MelsecFxSerialHelper.CheckPlcWriteResponse(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ActivePlc(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult> ActivePlcAsync(IReadWriteDevice plc)
  {
    OperateResult<byte[]> read1 = await plc.ReadFromCoreServerAsync(new byte[1]
    {
      (byte) 5
    });
    if (!read1.IsSuccess)
      return (OperateResult) read1;
    if (read1.Content[0] != (byte) 6)
      return new OperateResult("Send ENQ(0x05), Check Receive ACK(0x06) failed");
    OperateResult<byte[]> read2 = await plc.ReadFromCoreServerAsync(new byte[11]
    {
      (byte) 2,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 69,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 3,
      (byte) 54,
      (byte) 67
    });
    if (!read2.IsSuccess)
      return (OperateResult) read2;
    return (OperateResult) plc.ReadFromCoreServer(new byte[11]
    {
      (byte) 2,
      (byte) 48 /*0x30*/,
      (byte) 48 /*0x30*/,
      (byte) 69,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 48 /*0x30*/,
      (byte) 50,
      (byte) 3,
      (byte) 54,
      (byte) 67
    });
  }

  /// <summary>检查PLC返回的读取数据是否是正常的</summary>
  /// <param name="ack">Plc反馈的数据信息</param>
  /// <returns>检查结果</returns>
  public static OperateResult CheckPlcReadResponse(byte[] ack)
  {
    if (ack.Length == 0)
      return new OperateResult(StringResources.Language.MelsecFxReceiveZero);
    if (ack[0] == (byte) 21)
      return new OperateResult($"{StringResources.Language.MelsecFxAckNagative} Actual: {SoftBasic.ByteToHexString(ack, ' ')}");
    if (ack[0] != (byte) 2)
      return new OperateResult($"{StringResources.Language.MelsecFxAckWrong}{ack[0].ToString()} Actual: {SoftBasic.ByteToHexString(ack, ' ')}");
    try
    {
      if (!MelsecHelper.CheckCRC(ack))
        return new OperateResult($"{StringResources.Language.MelsecFxCrcCheckFailed} Actual: {SoftBasic.ByteToHexString(ack, ' ')}");
    }
    catch (Exception ex)
    {
      return new OperateResult($"{StringResources.Language.MelsecFxCrcCheckFailed}{ex.Message}{Environment.NewLine}Actual: {SoftBasic.ByteToHexString(ack, ' ')}");
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>检查PLC返回的写入的数据是否是正常的</summary>
  /// <param name="ack">Plc反馈的数据信息</param>
  /// <returns>检查结果</returns>
  public static OperateResult CheckPlcWriteResponse(byte[] ack)
  {
    if (ack.Length == 0)
      return new OperateResult(StringResources.Language.MelsecFxReceiveZero);
    if (ack[0] == (byte) 21)
      return new OperateResult($"{StringResources.Language.MelsecFxAckNagative} Actual: {SoftBasic.ByteToHexString(ack, ' ')}");
    return ack[0] != (byte) 6 ? new OperateResult($"{StringResources.Language.MelsecFxAckWrong}{ack[0].ToString()} Actual: {SoftBasic.ByteToHexString(ack, ' ')}") : OperateResult.CreateSuccessResult();
  }

  /// <summary>生成位写入的数据报文信息，该报文可直接用于发送串口给PLC</summary>
  /// <param name="address">地址信息，每个地址存在一定的范围，需要谨慎传入数据。举例：M10,S10,X5,Y10,C10,T10</param>
  /// <param name="value"><c>True</c>或是<c>False</c></param>
  /// <returns>带报文信息的结果对象</returns>
  public static OperateResult<byte[]> BuildWriteBoolPacket(string address, bool value)
  {
    OperateResult<MelsecMcDataType, ushort> result = MelsecFxSerialHelper.FxAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    ushort content2 = result.Content2;
    ushort num;
    if (result.Content1 == MelsecMcDataType.M)
      num = content2 < (ushort) 8000 ? (ushort) ((uint) content2 + 2048U /*0x0800*/) : (ushort) ((int) content2 - 8000 + 3840 /*0x0F00*/);
    else if (result.Content1 == MelsecMcDataType.S)
      num = content2;
    else if (result.Content1 == MelsecMcDataType.X)
      num = (ushort) ((uint) content2 + 1024U /*0x0400*/);
    else if (result.Content1 == MelsecMcDataType.Y)
      num = (ushort) ((uint) content2 + 1280U /*0x0500*/);
    else if (result.Content1 == MelsecMcDataType.CS)
      num = (ushort) ((uint) content2 + 448U);
    else if (result.Content1 == MelsecMcDataType.CC)
      num = (ushort) ((uint) content2 + 960U);
    else if (result.Content1 == MelsecMcDataType.CN)
      num = (ushort) ((uint) content2 + 3584U /*0x0E00*/);
    else if (result.Content1 == MelsecMcDataType.TS)
      num = (ushort) ((uint) content2 + 192U /*0xC0*/);
    else if (result.Content1 == MelsecMcDataType.TC)
    {
      num = (ushort) ((uint) content2 + 704U);
    }
    else
    {
      if (result.Content1 != MelsecMcDataType.TN)
        return new OperateResult<byte[]>(StringResources.Language.MelsecCurrentTypeNotSupportedBitOperate);
      num = (ushort) ((uint) content2 + 1536U /*0x0600*/);
    }
    byte[] data = new byte[9]
    {
      (byte) 2,
      value ? (byte) 55 : (byte) 56,
      SoftBasic.BuildAsciiBytesFrom(num)[2],
      SoftBasic.BuildAsciiBytesFrom(num)[3],
      SoftBasic.BuildAsciiBytesFrom(num)[0],
      SoftBasic.BuildAsciiBytesFrom(num)[1],
      (byte) 3,
      (byte) 0,
      (byte) 0
    };
    MelsecHelper.FxCalculateCRC(data).CopyTo((Array) data, 7);
    return OperateResult.CreateSuccessResult<byte[]>(data);
  }

  /// <summary>根据类型地址长度确认需要读取的指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">长度</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<List<byte[]>> BuildReadWordCommand(
    string address,
    ushort length,
    bool isNewVersion)
  {
    OperateResult<ushort> wordStartAddress = MelsecFxSerialHelper.FxCalculateWordStartAddress(address, isNewVersion);
    if (!wordStartAddress.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) wordStartAddress);
    length *= (ushort) 2;
    ushort content = wordStartAddress.Content;
    int[] array = SoftBasic.SplitIntegerToArray((int) length, 254);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      if (isNewVersion)
      {
        byte[] data = new byte[13]
        {
          (byte) 2,
          (byte) 69,
          (byte) 48 /*0x30*/,
          (byte) 48 /*0x30*/,
          SoftBasic.BuildAsciiBytesFrom(content)[0],
          SoftBasic.BuildAsciiBytesFrom(content)[1],
          SoftBasic.BuildAsciiBytesFrom(content)[2],
          SoftBasic.BuildAsciiBytesFrom(content)[3],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[0],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[1],
          (byte) 3,
          (byte) 0,
          (byte) 0
        };
        MelsecHelper.FxCalculateCRC(data).CopyTo((Array) data, 11);
        numArrayList.Add(data);
        content += (ushort) array[index];
      }
      else
      {
        byte[] data = new byte[11]
        {
          (byte) 2,
          (byte) 48 /*0x30*/,
          SoftBasic.BuildAsciiBytesFrom(content)[0],
          SoftBasic.BuildAsciiBytesFrom(content)[1],
          SoftBasic.BuildAsciiBytesFrom(content)[2],
          SoftBasic.BuildAsciiBytesFrom(content)[3],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[0],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[1],
          (byte) 3,
          (byte) 0,
          (byte) 0
        };
        MelsecHelper.FxCalculateCRC(data).CopyTo((Array) data, 9);
        numArrayList.Add(data);
        content += (ushort) array[index];
      }
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>根据类型地址长度确认需要读取的指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">bool数组长度</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<List<byte[]>, int> BuildReadBoolCommand(
    string address,
    ushort length,
    bool isNewVersion)
  {
    OperateResult<ushort, ushort, ushort> boolStartAddress = MelsecFxSerialHelper.FxCalculateBoolStartAddress(address, isNewVersion);
    if (!boolStartAddress.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>, int>((OperateResult) boolStartAddress);
    ushort occupyLength = (ushort) HslHelper.CalculateOccupyLength((int) boolStartAddress.Content2, (int) length);
    ushort content1 = boolStartAddress.Content1;
    int[] array = SoftBasic.SplitIntegerToArray((int) occupyLength, 254);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      if (isNewVersion)
      {
        byte[] data = new byte[13]
        {
          (byte) 2,
          (byte) 69,
          (byte) 48 /*0x30*/,
          (byte) 48 /*0x30*/,
          SoftBasic.BuildAsciiBytesFrom(content1)[0],
          SoftBasic.BuildAsciiBytesFrom(content1)[1],
          SoftBasic.BuildAsciiBytesFrom(content1)[2],
          SoftBasic.BuildAsciiBytesFrom(content1)[3],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[0],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[1],
          (byte) 3,
          (byte) 0,
          (byte) 0
        };
        MelsecHelper.FxCalculateCRC(data).CopyTo((Array) data, 11);
        numArrayList.Add(data);
      }
      else
      {
        byte[] data = new byte[11]
        {
          (byte) 2,
          (byte) 48 /*0x30*/,
          SoftBasic.BuildAsciiBytesFrom(content1)[0],
          SoftBasic.BuildAsciiBytesFrom(content1)[1],
          SoftBasic.BuildAsciiBytesFrom(content1)[2],
          SoftBasic.BuildAsciiBytesFrom(content1)[3],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[0],
          SoftBasic.BuildAsciiBytesFrom((byte) array[index])[1],
          (byte) 3,
          (byte) 0,
          (byte) 0
        };
        MelsecHelper.FxCalculateCRC(data).CopyTo((Array) data, 9);
        numArrayList.Add(data);
      }
      content1 += (ushort) array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>, int>(numArrayList, (int) boolStartAddress.Content3);
  }

  /// <summary>根据类型地址以及需要写入的数据来生成指令头</summary>
  /// <param name="address">起始地址</param>
  /// <param name="value">实际的数据信息</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>带有成功标志的指令数据</returns>
  public static OperateResult<byte[]> BuildWriteWordCommand(
    string address,
    byte[] value,
    bool isNewVersion)
  {
    OperateResult<ushort> wordStartAddress = MelsecFxSerialHelper.FxCalculateWordStartAddress(address, isNewVersion);
    if (!wordStartAddress.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) wordStartAddress);
    if (value != null)
      value = SoftBasic.BuildAsciiBytesFrom(value);
    ushort content = wordStartAddress.Content;
    if (isNewVersion)
    {
      byte[] numArray = new byte[13 + value.Length];
      numArray[0] = (byte) 2;
      numArray[1] = (byte) 69;
      numArray[2] = (byte) 49;
      numArray[3] = (byte) 48 /*0x30*/;
      numArray[4] = SoftBasic.BuildAsciiBytesFrom(content)[0];
      numArray[5] = SoftBasic.BuildAsciiBytesFrom(content)[1];
      numArray[6] = SoftBasic.BuildAsciiBytesFrom(content)[2];
      numArray[7] = SoftBasic.BuildAsciiBytesFrom(content)[3];
      numArray[8] = SoftBasic.BuildAsciiBytesFrom((byte) (value.Length / 2))[0];
      numArray[9] = SoftBasic.BuildAsciiBytesFrom((byte) (value.Length / 2))[1];
      Array.Copy((Array) value, 0, (Array) numArray, 10, value.Length);
      numArray[numArray.Length - 3] = (byte) 3;
      MelsecHelper.FxCalculateCRC(numArray).CopyTo((Array) numArray, numArray.Length - 2);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    byte[] numArray1 = new byte[11 + value.Length];
    numArray1[0] = (byte) 2;
    numArray1[1] = (byte) 49;
    numArray1[2] = SoftBasic.BuildAsciiBytesFrom(content)[0];
    numArray1[3] = SoftBasic.BuildAsciiBytesFrom(content)[1];
    numArray1[4] = SoftBasic.BuildAsciiBytesFrom(content)[2];
    numArray1[5] = SoftBasic.BuildAsciiBytesFrom(content)[3];
    numArray1[6] = SoftBasic.BuildAsciiBytesFrom((byte) (value.Length / 2))[0];
    numArray1[7] = SoftBasic.BuildAsciiBytesFrom((byte) (value.Length / 2))[1];
    Array.Copy((Array) value, 0, (Array) numArray1, 8, value.Length);
    numArray1[numArray1.Length - 3] = (byte) 3;
    MelsecHelper.FxCalculateCRC(numArray1).CopyTo((Array) numArray1, numArray1.Length - 2);
    return OperateResult.CreateSuccessResult<byte[]>(numArray1);
  }

  /// <summary>从PLC反馈的数据进行提炼操作</summary>
  /// <param name="response">PLC反馈的真实数据</param>
  /// <returns>数据提炼后的真实数据</returns>
  public static OperateResult<byte[]> ExtractActualData(byte[] response)
  {
    try
    {
      byte[] numArray = new byte[(response.Length - 4) / 2];
      for (int index = 0; index < numArray.Length; ++index)
      {
        byte[] bytes = new byte[2]
        {
          response[index * 2 + 1],
          response[index * 2 + 2]
        };
        numArray[index] = Convert.ToByte(Encoding.ASCII.GetString(bytes), 16 /*0x10*/);
      }
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      OperateResult<byte[]> actualData = new OperateResult<byte[]>();
      actualData.Message = $"Extract Msg：{ex.Message}{Environment.NewLine}Data: {SoftBasic.ByteToHexString(response)}";
      return actualData;
    }
  }

  /// <summary>从PLC反馈的数据进行提炼bool数组操作</summary>
  /// <param name="response">PLC反馈的真实数据</param>
  /// <param name="start">起始提取的点信息</param>
  /// <param name="length">bool数组的长度</param>
  /// <returns>数据提炼后的真实数据</returns>
  public static OperateResult<bool[]> ExtractActualBoolData(byte[] response, int start, int length)
  {
    OperateResult<byte[]> actualData = MelsecFxSerialHelper.ExtractActualData(response);
    if (!actualData.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) actualData);
    try
    {
      bool[] flagArray = new bool[length];
      bool[] boolArray = SoftBasic.ByteToBoolArray(actualData.Content, actualData.Content.Length * 8);
      for (int index = 0; index < length; ++index)
        flagArray[index] = boolArray[index + start];
      return OperateResult.CreateSuccessResult<bool[]>(flagArray);
    }
    catch (Exception ex)
    {
      OperateResult<bool[]> actualBoolData = new OperateResult<bool[]>();
      actualBoolData.Message = $"Extract Msg：{ex.Message}{Environment.NewLine}Data: {SoftBasic.ByteToHexString(response)}";
      return actualBoolData;
    }
  }

  /// <summary>解析数据地址成不同的三菱地址类型</summary>
  /// <param name="address">数据地址</param>
  /// <returns>地址结果对象</returns>
  public static OperateResult<MelsecMcDataType, ushort> FxAnalysisAddress(string address)
  {
    OperateResult<MelsecMcDataType, ushort> operateResult = new OperateResult<MelsecMcDataType, ushort>();
    try
    {
      switch (address[0])
      {
        case 'C':
        case 'c':
          if (address[1] == 'N' || address[1] == 'n')
          {
            operateResult.Content1 = MelsecMcDataType.CN;
            operateResult.Content2 = Convert.ToUInt16(address.Substring(2), MelsecMcDataType.CN.FromBase);
            break;
          }
          if (address[1] == 'S' || address[1] == 's')
          {
            operateResult.Content1 = MelsecMcDataType.CS;
            operateResult.Content2 = Convert.ToUInt16(address.Substring(2), MelsecMcDataType.CS.FromBase);
            break;
          }
          if (address[1] != 'C' && address[1] != 'c')
            throw new Exception(StringResources.Language.NotSupportedDataType);
          operateResult.Content1 = MelsecMcDataType.CC;
          operateResult.Content2 = Convert.ToUInt16(address.Substring(2), MelsecMcDataType.CC.FromBase);
          break;
        case 'D':
        case 'd':
          operateResult.Content1 = MelsecMcDataType.D;
          operateResult.Content2 = Convert.ToUInt16(address.Substring(1), MelsecMcDataType.D.FromBase);
          break;
        case 'M':
        case 'm':
          operateResult.Content1 = MelsecMcDataType.M;
          operateResult.Content2 = Convert.ToUInt16(address.Substring(1), MelsecMcDataType.M.FromBase);
          break;
        case 'S':
        case 's':
          operateResult.Content1 = MelsecMcDataType.S;
          operateResult.Content2 = Convert.ToUInt16(address.Substring(1), MelsecMcDataType.S.FromBase);
          break;
        case 'T':
        case 't':
          if (address[1] == 'N' || address[1] == 'n')
          {
            operateResult.Content1 = MelsecMcDataType.TN;
            operateResult.Content2 = Convert.ToUInt16(address.Substring(2), MelsecMcDataType.TN.FromBase);
            break;
          }
          if (address[1] == 'S' || address[1] == 's')
          {
            operateResult.Content1 = MelsecMcDataType.TS;
            operateResult.Content2 = Convert.ToUInt16(address.Substring(2), MelsecMcDataType.TS.FromBase);
            break;
          }
          if (address[1] != 'C' && address[1] != 'c')
            throw new Exception(StringResources.Language.NotSupportedDataType);
          operateResult.Content1 = MelsecMcDataType.TC;
          operateResult.Content2 = Convert.ToUInt16(address.Substring(2), MelsecMcDataType.TC.FromBase);
          break;
        case 'X':
        case 'x':
          operateResult.Content1 = MelsecMcDataType.X;
          operateResult.Content2 = Convert.ToUInt16(address.Substring(1), 8);
          break;
        case 'Y':
        case 'y':
          operateResult.Content1 = MelsecMcDataType.Y;
          operateResult.Content2 = Convert.ToUInt16(address.Substring(1), 8);
          break;
        default:
          throw new Exception(StringResources.Language.NotSupportedDataType);
      }
    }
    catch (Exception ex)
    {
      operateResult.Message = ex.Message;
      return operateResult;
    }
    operateResult.IsSuccess = true;
    return operateResult;
  }

  /// <summary>判断是否是位地址信息</summary>
  /// <param name="dataType"></param>
  /// <returns></returns>
  private static bool CheckAddressBool(MelsecMcDataType dataType)
  {
    return dataType == MelsecMcDataType.X || dataType == MelsecMcDataType.Y || dataType == MelsecMcDataType.M || dataType == MelsecMcDataType.S || dataType == MelsecMcDataType.CS || dataType == MelsecMcDataType.CC || dataType == MelsecMcDataType.TS || dataType == MelsecMcDataType.TC;
  }

  private static ushort CalculateBoolStartAddress(
    MelsecMcDataType dataType,
    ushort startAddress,
    bool isNewVersion)
  {
    if (dataType == MelsecMcDataType.M)
      startAddress = !isNewVersion ? (startAddress < (ushort) 8000 ? (ushort) ((int) startAddress / 8 + 256 /*0x0100*/) : (ushort) (((int) startAddress - 8000) / 8 + 480)) : (startAddress < (ushort) 8000 ? (ushort) ((int) startAddress / 8 + 34816) : (ushort) (((int) startAddress - 8000) / 8 + 35840));
    else if (dataType == MelsecMcDataType.X)
      startAddress = (ushort) ((int) startAddress / 8 + (isNewVersion ? 36000 : 128 /*0x80*/));
    else if (dataType == MelsecMcDataType.Y)
      startAddress = (ushort) ((int) startAddress / 8 + (isNewVersion ? 35776 : 160 /*0xA0*/));
    else if (dataType == MelsecMcDataType.S)
      startAddress = (ushort) ((int) startAddress / 8 + (isNewVersion ? 36064 : 0));
    else if (dataType == MelsecMcDataType.CS)
      startAddress = (ushort) ((int) startAddress / 8 + (isNewVersion ? 37696 : 448));
    else if (dataType == MelsecMcDataType.CC)
      startAddress = (ushort) ((int) startAddress / 8 + (isNewVersion ? 37600 : 960));
    else if (dataType == MelsecMcDataType.TS)
      startAddress = (ushort) ((int) startAddress / 8 + (isNewVersion ? 37728 : 192 /*0xC0*/));
    else if (dataType == MelsecMcDataType.TC)
      startAddress = (ushort) ((int) startAddress / 8 + (isNewVersion ? 37632 : 704));
    return startAddress;
  }

  /// <summary>返回读取的地址及长度信息</summary>
  /// <param name="address">读取的地址信息</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>带起始地址的结果对象</returns>
  internal static OperateResult<ushort> FxCalculateWordStartAddress(
    string address,
    bool isNewVersion)
  {
    OperateResult<MelsecMcDataType, ushort> result = MelsecFxSerialHelper.FxAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<ushort>((OperateResult) result);
    ushort content2 = result.Content2;
    ushort num;
    if (result.Content1 == MelsecMcDataType.D)
      num = content2 < (ushort) 8000 ? (isNewVersion ? (ushort) ((int) content2 * 2 + 16384 /*0x4000*/) : (ushort) ((int) content2 * 2 + 4096 /*0x1000*/)) : (ushort) (((int) content2 - 8000) * 2 + (isNewVersion ? 32768 /*0x8000*/ : 3584 /*0x0E00*/));
    else if (result.Content1 == MelsecMcDataType.CN)
      num = content2 < (ushort) 200 ? (ushort) ((int) content2 * 2 + 2560 /*0x0A00*/) : (ushort) (((int) content2 - 200) * 4 + 3072 /*0x0C00*/);
    else if (result.Content1 == MelsecMcDataType.TN)
    {
      num = !isNewVersion ? (ushort) ((int) content2 * 2 + 2048 /*0x0800*/) : (ushort) ((int) content2 * 2 + 4096 /*0x1000*/);
    }
    else
    {
      if (!MelsecFxSerialHelper.CheckAddressBool(result.Content1))
        return new OperateResult<ushort>(StringResources.Language.MelsecCurrentTypeNotSupportedWordOperate);
      return (uint) result.Content2 % 16U /*0x10*/ > 0U ? new OperateResult<ushort>(StringResources.Language.MelsecFxAddressStartWith16) : OperateResult.CreateSuccessResult<ushort>(MelsecFxSerialHelper.CalculateBoolStartAddress(result.Content1, content2, isNewVersion));
    }
    return OperateResult.CreateSuccessResult<ushort>(num);
  }

  /// <summary>返回读取的实际的字节地址，相对位置，以及当前的位偏置信息</summary>
  /// <param name="address">读取的地址信息</param>
  /// <param name="isNewVersion">是否是新版的串口访问类</param>
  /// <returns>带起始地址的结果对象</returns>
  public static OperateResult<ushort, ushort, ushort> FxCalculateBoolStartAddress(
    string address,
    bool isNewVersion)
  {
    OperateResult<MelsecMcDataType, ushort> result = MelsecFxSerialHelper.FxAnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<ushort, ushort, ushort>((OperateResult) result);
    ushort content2 = result.Content2;
    return MelsecFxSerialHelper.CheckAddressBool(result.Content1) ? OperateResult.CreateSuccessResult<ushort, ushort, ushort>(MelsecFxSerialHelper.CalculateBoolStartAddress(result.Content1, content2, isNewVersion), result.Content2, (ushort) ((uint) result.Content2 % 8U)) : new OperateResult<ushort, ushort, ushort>(StringResources.Language.MelsecCurrentTypeNotSupportedBitOperate);
  }
}
