﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.NetworkWebApiBase
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.LogNet;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>
/// 基于webapi的数据访问的基类，提供了基本的http接口的交互功能<br />
/// A base class for data access based on webapi that provides basic HTTP interface interaction
/// </summary>
/// <remarks>
/// 当前的基类在.net framework2.0上存在问题，在.net framework4.5及.net standard上运行稳定而且正常
/// </remarks>
public class NetworkWebApiBase
{
  private string host = "127.0.0.1";
  private string ipAddress = "127.0.0.1";
  private int port = 80 /*0x50*/;
  private string name = string.Empty;
  private string password = string.Empty;
  private HttpClient httpClient;

  /// <summary>
  /// 使用指定的ip地址来初始化对象<br />
  /// Initializes the object using the specified IP address
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  public NetworkWebApiBase(string ipAddress)
    : this(ipAddress, 80 /*0x50*/, string.Empty, string.Empty)
  {
  }

  /// <summary>
  /// 使用指定的ip地址及端口号来初始化对象<br />
  /// Initializes the object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号信息</param>
  public NetworkWebApiBase(string ipAddress, int port)
    : this(ipAddress, port, string.Empty, string.Empty)
  {
  }

  /// <summary>
  /// 使用指定的ip地址，端口号，用户名，密码来初始化对象<br />
  /// Initialize the object with the specified IP address, port number, username, and password
  /// </summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号信息</param>
  /// <param name="name">用户名</param>
  /// <param name="password">密码</param>
  public NetworkWebApiBase(string ipAddress, int port, string name, string password)
  {
    this.host = ipAddress;
    this.ipAddress = HslHelper.GetIpAddressFromInput(ipAddress);
    this.port = port;
    this.name = name;
    this.password = password;
    if (!string.IsNullOrEmpty(name))
    {
      HttpClientHandler handler = new HttpClientHandler()
      {
        Credentials = (ICredentials) new NetworkCredential(name, password)
      };
      handler.Proxy = (IWebProxy) null;
      handler.UseProxy = false;
      ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(this.TrustAllValidationCallback);
      this.httpClient = new HttpClient((HttpMessageHandler) handler);
      ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
    }
    else
    {
      ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(this.TrustAllValidationCallback);
      this.httpClient = new HttpClient();
      ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
    }
  }

  private bool TrustAllValidationCallback(
    object sender,
    X509Certificate certificate,
    X509Chain chain,
    SslPolicyErrors errors)
  {
    return true;
  }

  /// <summary>针对请求的头信息进行额外的处理，可以重写用来实现一些特殊的信息添加到请求头中</summary>
  /// <param name="headers">头信息</param>
  protected virtual void AddRequestHeaders(HttpContentHeaders headers)
  {
  }

  /// <summary>针对请求的头信息进行额外的处理</summary>
  public Action<HttpContentHeaders> AddRequestHeadersAction { get; set; }

  private string GetEntireUrl(string url)
  {
    if (url.StartsWith("http://", StringComparison.OrdinalIgnoreCase) || url.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
      return url;
    return $"{(this.UseHttps ? (object) "https" : (object) "http")}://{this.host}:{this.port}/{(url.StartsWith("/") ? (object) url.Substring(1) : (object) url)}";
  }

  /// <summary>使用GET操作从网络中获取到数据信息，地址</summary>
  /// <param name="rawUrl">除去ip地址和端口的地址</param>
  /// <returns>返回的数据内容</returns>
  public OperateResult<string> Get(string rawUrl)
  {
    string entireUrl = this.GetEntireUrl(rawUrl);
    try
    {
      using (HttpResponseMessage result = this.httpClient.GetAsync(entireUrl).Result)
      {
        using (HttpContent content = result.Content)
        {
          result.EnsureSuccessStatusCode();
          if (!this.UseEncodingISO)
            return OperateResult.CreateSuccessResult<string>(content.ReadAsStringAsync().Result);
          using (StreamReader streamReader = new StreamReader(content.ReadAsStreamAsync().Result, Encoding.GetEncoding("iso-8859-1")))
            return OperateResult.CreateSuccessResult<string>(streamReader.ReadToEnd());
        }
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  /// <summary>使用POST命令去提交数据内容，然后返回相关的数据信息</summary>
  /// <param name="rawUrl">已经去除ip地址，端口号的api信息</param>
  /// <param name="body">数据内容</param>
  /// <returns>从服务器返回的内容</returns>
  public OperateResult<string> Post(string rawUrl, string body)
  {
    string entireUrl = this.GetEntireUrl(rawUrl);
    try
    {
      using (StringContent content1 = new StringContent(body))
      {
        if (!string.IsNullOrEmpty(this.DefaultContentType))
          content1.Headers.ContentType = new MediaTypeHeaderValue(this.DefaultContentType);
        this.AddRequestHeaders(content1.Headers);
        Action<HttpContentHeaders> requestHeadersAction = this.AddRequestHeadersAction;
        if (requestHeadersAction != null)
          requestHeadersAction(content1.Headers);
        using (HttpResponseMessage result = this.httpClient.PostAsync(entireUrl, (HttpContent) content1).Result)
        {
          using (HttpContent content2 = result.Content)
          {
            result.EnsureSuccessStatusCode();
            if (!this.UseEncodingISO)
              return OperateResult.CreateSuccessResult<string>(content2.ReadAsStringAsync().Result);
            using (StreamReader streamReader = new StreamReader(content2.ReadAsStreamAsync().Result, Encoding.GetEncoding("iso-8859-1")))
              return OperateResult.CreateSuccessResult<string>(streamReader.ReadToEnd());
          }
        }
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<string>($"{ex.Message}{Environment.NewLine}Url: {entireUrl}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkWebApiBase.Get(System.String)" />
  public async Task<OperateResult<string>> GetAsync(string rawUrl)
  {
    string url = this.GetEntireUrl(rawUrl);
    try
    {
      using (HttpResponseMessage response = await this.httpClient.GetAsync(url))
      {
        using (HttpContent content = response.Content)
        {
          response.EnsureSuccessStatusCode();
          if (this.UseEncodingISO)
          {
            Stream stream = await content.ReadAsStreamAsync();
            StreamReader sr = new StreamReader(stream, Encoding.GetEncoding("iso-8859-1"));
            stream = (Stream) null;
            try
            {
              return OperateResult.CreateSuccessResult<string>(sr.ReadToEnd());
            }
            finally
            {
              sr?.Dispose();
            }
          }
          else
          {
            string str = await content.ReadAsStringAsync();
            return OperateResult.CreateSuccessResult<string>(str);
          }
        }
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Net.NetworkWebApiBase.Post(System.String,System.String)" />
  public async Task<OperateResult<string>> PostAsync(string rawUrl, string body)
  {
    string url = this.GetEntireUrl(rawUrl);
    try
    {
      using (StringContent stringContent = new StringContent(body))
      {
        if (!string.IsNullOrEmpty(this.DefaultContentType))
          stringContent.Headers.ContentType = new MediaTypeHeaderValue(this.DefaultContentType);
        this.AddRequestHeaders(stringContent.Headers);
        Action<HttpContentHeaders> requestHeadersAction = this.AddRequestHeadersAction;
        if (requestHeadersAction != null)
          requestHeadersAction(stringContent.Headers);
        using (HttpResponseMessage response = await this.httpClient.PostAsync(url, (HttpContent) stringContent))
        {
          using (HttpContent content = response.Content)
          {
            response.EnsureSuccessStatusCode();
            if (this.UseEncodingISO)
            {
              Stream stream = await content.ReadAsStreamAsync();
              StreamReader sr = new StreamReader(stream, Encoding.GetEncoding("iso-8859-1"));
              stream = (Stream) null;
              try
              {
                return OperateResult.CreateSuccessResult<string>(sr.ReadToEnd());
              }
              finally
              {
                sr?.Dispose();
              }
            }
            else
            {
              string str = await content.ReadAsStringAsync();
              return OperateResult.CreateSuccessResult<string>(str);
            }
          }
        }
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  /// <summary>
  /// 获取或设置远程服务器的IP地址<br />
  /// Gets or sets the IP address of the remote server
  /// </summary>
  public string IpAddress
  {
    get => this.ipAddress;
    set
    {
      this.host = value;
      this.ipAddress = HslHelper.GetIpAddressFromInput(value);
    }
  }

  /// <summary>
  /// 获取或设置远程服务器的端口号信息<br />
  /// Gets or sets the port number information for the remote server
  /// </summary>
  public int Port
  {
    get => this.port;
    set => this.port = value;
  }

  /// <summary>
  /// 获取或设置当前的用户名<br />
  /// Get or set the current username
  /// </summary>
  public string UserName
  {
    get => this.name;
    set => this.name = value;
  }

  /// <summary>
  /// 获取或设置当前的密码<br />
  /// Get or set the current password
  /// </summary>
  public string Password
  {
    get => this.password;
    set => this.password = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.NetworkBase.LogNet" />
  public ILogNet LogNet { get; set; }

  /// <summary>
  /// 是否启用Https的协议访问，对于Https来说，端口号默认为 443<br />
  /// Whether to enable Https protocol access, for Https, the port number defaults to 443
  /// </summary>
  public bool UseHttps { get; set; }

  /// <summary>
  /// 默认的内容类型，如果为空，则不进行设置操作。例如设置为 "text/plain", "application/json", "text/html" 等等。<br />
  /// The default content type, if it is empty, no setting operation will be performed. For example, set to "text/plain", "application/json", "text/html" and so on.
  /// </summary>
  public string DefaultContentType { get; set; }

  /// <summary>
  /// 获取或设置是否使用ISO的编码信息，默认为 False<br />
  /// Get or set whether to use ISO encoding information, the default is False
  /// </summary>
  /// <remarks>
  /// 在访问某些特殊的API的时候，会发生异常"The character set provided in ContentType is invalid...."，这时候，只需要将本属性设置为 True 即可。
  /// </remarks>
  public bool UseEncodingISO { get; set; } = false;

  /// <summary>
  /// 获取当前的HttpClinet的客户端<br />
  /// Get the current HttpClinet client
  /// </summary>
  public HttpClient Client => this.httpClient;

  /// <summary>
  /// 获取当前的远程服务器的地址，可能是ip，也可能是网址。<br />
  /// Get the address of the current remote server, which may be an IP address or a web address.
  /// </summary>
  public string Host => this.host;

  /// <inheritdoc />
  public override string ToString() => $"NetworkWebApiBase[{this.ipAddress}:{this.port}]";
}
