﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.OpenProtocolSession
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.Net;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

internal class OpenProtocolSession : PipeSession
{
  public bool MID0051Subscribe { get; set; } = false;

  public bool MID0060Subscribe { get; set; } = false;

  public bool MID0034Subscribe { get; set; } = false;

  public bool MID0070Subscribe { get; set; } = false;

  public bool MID0014Subscribe { get; set; } = false;

  public bool MID0105Subscribe { get; set; } = false;
}
