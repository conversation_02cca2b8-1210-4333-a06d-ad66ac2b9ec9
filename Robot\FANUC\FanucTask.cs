﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.FANUC.FanucTask
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Robot.FANUC;

/// <summary>Fanuc机器人的任务类</summary>
public class FanucTask
{
  /// <summary>ProgramName</summary>
  public string ProgramName { get; set; }

  /// <summary>LineNumber</summary>
  public short LineNumber { get; set; }

  /// <summary>State</summary>
  public short State { get; set; }

  /// <summary>ParentProgramName</summary>
  public string ParentProgramName { get; set; }

  /// <summary>从原始的数据对象加载数据信息</summary>
  /// <param name="byteTransform">字节变换</param>
  /// <param name="content">原始的字节数据</param>
  /// <param name="index">索引信息</param>
  /// <param name="encoding">编码</param>
  public void LoadByContent(
    IByteTransform byteTransform,
    byte[] content,
    int index,
    Encoding encoding)
  {
    this.ProgramName = encoding.GetString(content, index, 16 /*0x10*/).Trim(new char[1]);
    this.LineNumber = BitConverter.ToInt16(content, index + 16 /*0x10*/);
    this.State = BitConverter.ToInt16(content, index + 18);
    this.ParentProgramName = encoding.GetString(content, index + 20, 16 /*0x10*/).Trim(new char[1]);
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"ProgramName[{this.ProgramName}] LineNumber[{this.LineNumber}] State[{this.State}] ParentProgramName[{this.ParentProgramName}]";
  }

  /// <summary>从原始的数据信息初始化一个任务对象</summary>
  /// <param name="byteTransform">字节变换</param>
  /// <param name="content">原始的字节数据</param>
  /// <param name="index">索引信息</param>
  /// <param name="encoding">编码</param>
  /// <returns>任务对象</returns>
  public static FanucTask ParseFrom(
    IByteTransform byteTransform,
    byte[] content,
    int index,
    Encoding encoding)
  {
    FanucTask from = new FanucTask();
    from.LoadByContent(byteTransform, content, index, encoding);
    return from;
  }
}
