﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.ISoftFileSaveBase
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>支持字符串信息加载存储的接口，定义了几个通用的方法</summary>
public interface ISoftFileSaveBase
{
  /// <summary>获取需要保存的数据，需要重写实现</summary>
  /// <returns>需要存储的信息</returns>
  string ToSaveString();

  /// <summary>从字符串加载数据，需要重写实现</summary>
  /// <param name="content">字符串数据</param>
  void LoadByString(string content);

  /// <summary>不使用解密方法从文件读取数据</summary>
  void LoadByFile();

  /// <summary>不使用加密方法保存数据到文件</summary>
  void SaveToFile();

  /// <summary>文件路径的存储</summary>
  string FileSavePath { get; set; }
}
