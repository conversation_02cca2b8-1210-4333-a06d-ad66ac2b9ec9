﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.NetMessageBase
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.IO;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>消息类的基类</summary>
public class NetMessageBase
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.HeadBytes" />
  public byte[] HeadBytes { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ContentBytes" />
  public byte[] ContentBytes { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.SendBytes" />
  public byte[] SendBytes { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.PependedUselesByteLength(System.Byte[])" />
  public virtual int PependedUselesByteLength(byte[] headByte) => 0;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetHeadBytesIdentity" />
  public virtual int GetHeadBytesIdentity() => 0;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public virtual bool CheckHeadBytesLegal(byte[] token) => true;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckReceiveDataComplete(System.Byte[],System.IO.MemoryStream)" />
  public virtual bool CheckReceiveDataComplete(byte[] send, MemoryStream ms) => true;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckMessageMatch(System.Byte[],System.Byte[])" />
  public virtual int CheckMessageMatch(byte[] send, byte[] receive) => 1;

  /// <inheritdoc />
  public override string ToString() => this.GetType().Name ?? "";
}
