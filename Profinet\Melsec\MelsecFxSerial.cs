﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecFxSerial
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱的串口通信的对象，适用于读取FX系列的串口数据，支持的类型参考文档说明<br />
/// Mitsubishi's serial communication object is suitable for reading serial data of the FX series. Refer to the documentation for the supported types.
/// </summary>
/// <remarks>
/// 一般老旧的型号，例如FX2N之类的，需要将<see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerial.IsNewVersion" />设置为<c>False</c>，如果是FX3U新的型号，则需要将<see cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerial.IsNewVersion" />设置为<c>True</c>
/// </remarks>
/// <example>
/// <inheritdoc cref="T:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp" path="remarks" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\MelsecFxSerial.cs" region="Usage" title="简单的使用" />
/// </example>
public class MelsecFxSerial : DeviceSerialPort, IMelsecFxSerial, IReadWriteNet
{
  /// <summary>实例化一个默认的对象</summary>
  public MelsecFxSerial()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
    this.IsNewVersion = true;
    this.ByteTransform.IsStringReverseByteWord = true;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new MelsecFxSerialMessage();

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    int stxIndex = MelsecFxSerialHelper.FindSTXIndex(response);
    return stxIndex > 0 ? OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(stxIndex)) : OperateResult.CreateSuccessResult<byte[]>(response);
  }

  /// <inheritdoc />
  public override OperateResult Open()
  {
    if (!(this.CommunicationPipe is PipeSerialPort communicationPipe))
      return new OperateResult("PipeSerialPort get failed");
    int baudRate = communicationPipe.GetPipe().BaudRate;
    if (this.AutoChangeBaudRate && baudRate != 9600)
    {
      communicationPipe.GetPipe().BaudRate = 9600;
      OperateResult operateResult1 = (OperateResult) communicationPipe.OpenCommunication();
      if (!operateResult1.IsSuccess)
        return operateResult1;
      for (int index = 0; index < 3; ++index)
      {
        OperateResult<byte[]> operateResult2 = communicationPipe.ReadFromCoreServer(this.GetNewNetMessage(), new byte[1]
        {
          (byte) 5
        }, true, (Action<byte[]>) null);
        if (!operateResult2.IsSuccess)
          return (OperateResult) operateResult2;
        if (operateResult2.Content.Length < 1 || operateResult2.Content[0] != (byte) 6)
        {
          if (index == 2)
            return new OperateResult("check 0x06 back before send data failed!");
        }
        else
          break;
      }
      byte[] numArray;
      switch (baudRate)
      {
        case 19200:
          numArray = new byte[6]
          {
            (byte) 2,
            (byte) 65,
            (byte) 49,
            (byte) 3,
            (byte) 55,
            (byte) 53
          };
          break;
        case 38400:
          numArray = new byte[6]
          {
            (byte) 2,
            (byte) 65,
            (byte) 50,
            (byte) 3,
            (byte) 55,
            (byte) 54
          };
          break;
        case 57600:
          numArray = new byte[6]
          {
            (byte) 2,
            (byte) 65,
            (byte) 51,
            (byte) 3,
            (byte) 55,
            (byte) 55
          };
          break;
        case 115200:
          numArray = new byte[6]
          {
            (byte) 2,
            (byte) 65,
            (byte) 53,
            (byte) 3,
            (byte) 55,
            (byte) 57
          };
          break;
        default:
          numArray = new byte[6]
          {
            (byte) 2,
            (byte) 65,
            (byte) 53,
            (byte) 3,
            (byte) 55,
            (byte) 57
          };
          break;
      }
      byte[] sendValue = numArray;
      OperateResult<byte[]> operateResult3 = communicationPipe.ReadFromCoreServer(this.GetNewNetMessage(), sendValue, true, (Action<byte[]>) null);
      if (!operateResult3.IsSuccess)
        return (OperateResult) operateResult3;
      if (operateResult3.Content.Length < 1 || operateResult3.Content[0] != (byte) 6)
        return new OperateResult("check 0x06 back after send data failed!");
      communicationPipe.CloseCommunication();
      communicationPipe.GetPipe().BaudRate = baudRate;
    }
    return base.Open();
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    if (!this.AutoChangeBaudRate)
      return base.InitializationOnConnect();
    return (OperateResult) this.CommunicationPipe.ReadFromCoreServer(this.GetNewNetMessage(), new byte[1]
    {
      (byte) 5
    }, true);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.IsNewVersion" />
  public bool IsNewVersion { get; set; }

  /// <summary>
  /// 获取或设置是否动态修改PLC的波特率，如果为 <c>True</c>，那么如果本对象设置了波特率 115200，就会自动修改PLC的波特率到 115200，因为三菱PLC再重启后都会使用默认的波特率9600 <br />
  /// Get or set whether to dynamically modify the baud rate of the PLC. If it is <c>True</c>, then if the baud rate of this object is set to 115200,
  /// the baud rate of the PLC will be automatically modified to 115200, because the Mitsubishi PLC is not After restart, the default baud rate of 9600 will be used
  /// </summary>
  public bool AutoChangeBaudRate { get; set; } = false;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return MelsecFxSerialHelper.Read((IReadWriteDevice) this, address, length, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return MelsecFxSerialHelper.Write((IReadWriteDevice) this, address, value, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return MelsecFxSerialHelper.ReadBool((IReadWriteDevice) this, address, length, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxSerialOverTcp.Write(System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return MelsecFxSerialHelper.Write((IReadWriteDevice) this, address, value);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return MelsecFxSerialHelper.Write((IReadWriteDevice) this, address, value, this.IsNewVersion);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ActivePlc(HslCommunication.Core.IReadWriteDevice)" />
  [HslMqttApi]
  public OperateResult ActivePlc() => MelsecFxSerialHelper.ActivePlc((IReadWriteDevice) this);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxSerialHelper.ActivePlc(HslCommunication.Core.IReadWriteDevice)" />
  public async Task<OperateResult> ActivePlcAsync()
  {
    OperateResult operateResult = await MelsecFxSerialHelper.ActivePlcAsync((IReadWriteDevice) this);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecFxSerial[{this.CommunicationPipe}]";
}
