﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Types.SecsValue
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Secs.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

#nullable disable
namespace HslCommunication.Secs.Types;

/// <summary>
/// SECS数据的对象信息，可以用来表示层级及嵌套的数据内容，如果需要显示，只需要<see cref="M:HslCommunication.Secs.Types.SecsValue.ToString" /> 方法即可，
/// 如果需要发送SECS设备，只需要 <see cref="M:HslCommunication.Secs.Types.SecsValue.ToSourceBytes" />，并支持反序列化操作 <see cref="M:HslCommunication.Secs.Types.SecsValue.ParseFromSource(System.Byte[],System.Text.Encoding)" />，无论是XML元素还是byte[]类型。<br />
/// SECS data object information, can be used to represent the hierarchy and nested data content, if you need to display, just need to <see cref="M:HslCommunication.Secs.Types.SecsValue.ToString" /> method can be.
/// If you need to send SECS equipment, only need <see cref="M:HslCommunication.Secs.Types.SecsValue.ToSourceBytes" />, and support the deserialization operation <see cref="M:HslCommunication.Secs.Types.SecsValue.ParseFromSource(System.Byte[],System.Text.Encoding)" />. Whether it's an XML element or byte[] type.
/// </summary>
/// <remarks>
/// XML序列化，反序列化例子：<br />
/// SecsValue value = new SecsValue( new object[]{ 1.23f, "ABC" } );<br />
/// XElement xml = value.ToXElement( ); <br />
/// SecsValue value2 = new SecsValue(xml);
/// </remarks>
/// <example>
/// 关于<see cref="T:HslCommunication.Secs.Types.SecsValue" />类型，可以非常灵活的实例化，参考下面的示例代码
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Secs\SecsGemSample.cs" region="Sample2" title="SecsValue说明" />
/// </example>
public class SecsValue
{
  private object obj = (object) null;
  private int length = 1;

  /// <summary>实例化一个空的SECS对象</summary>
  public SecsValue() => this.ItemType = SecsItemType.None;

  /// <summary>从一个字符串对象初始化数据信息</summary>
  /// <param name="value">字符串信息</param>
  public SecsValue(string value)
    : this(SecsItemType.ASCII, (object) value)
  {
  }

  /// <summary>从一个字符串数组对象初始化数据信息</summary>
  /// <param name="value">字符串数组</param>
  public SecsValue(string[] value)
  {
    this.ItemType = SecsItemType.List;
    List<SecsValue> secsValueList = new List<SecsValue>();
    if (value == null)
      value = new string[0];
    foreach (string str in value)
      secsValueList.Add(new SecsValue(str));
    this.Value = (object) secsValueList.ToArray();
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.SByte" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(sbyte value)
    : this(SecsItemType.SByte, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.SByte)" />
  public SecsValue(sbyte[] value)
    : this(SecsItemType.SByte, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Byte" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(byte value)
    : this(SecsItemType.Byte, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Int16" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(short value)
    : this(SecsItemType.Int16, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Int32" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(short[] value)
    : this(SecsItemType.Int16, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.UInt16" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(ushort value)
    : this(SecsItemType.UInt16, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.UInt16)" />
  public SecsValue(ushort[] value)
    : this(SecsItemType.UInt16, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Int32" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(int value)
    : this(SecsItemType.Int32, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Int32" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(int[] value)
    : this(SecsItemType.Int32, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.UInt32" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(uint value)
    : this(SecsItemType.UInt32, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.UInt32)" />
  public SecsValue(uint[] value)
    : this(SecsItemType.UInt32, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Int64" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(long value)
    : this(SecsItemType.Int64, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.Int64)" />
  public SecsValue(long[] value)
    : this(SecsItemType.Int64, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.UInt64" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(ulong value)
    : this(SecsItemType.UInt64, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.UInt64)" />
  public SecsValue(ulong[] value)
    : this(SecsItemType.UInt64, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Single" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(float value)
    : this(SecsItemType.Single, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.Single)" />
  public SecsValue(float[] value)
    : this(SecsItemType.Single, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Double" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(double value)
    : this(SecsItemType.Double, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.Double)" />
  public SecsValue(double[] value)
    : this(SecsItemType.Double, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Byte" /> 数组的对象初始化数据，需要指定 <see cref="T:HslCommunication.Secs.Types.SecsItemType" /> 来表示二进制还是byte数组类型
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(byte[] value)
  {
    this.ItemType = SecsItemType.Binary;
    this.Value = (object) value;
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Boolean" /> 的对象初始化数据
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(bool value)
    : this(SecsItemType.Bool, (object) value)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(System.Boolean)" />
  public SecsValue(bool[] value)
    : this(SecsItemType.Bool, (object) value)
  {
  }

  /// <summary>
  /// 从一个类型为 <see cref="T:System.Object" /> 数组的对象初始化数据，初始化后，本对象为 <see cref="F:HslCommunication.Secs.Types.SecsItemType.List" /> 类型
  /// </summary>
  /// <param name="value">数据值信息</param>
  public SecsValue(IEnumerable<object> value)
  {
    this.ItemType = SecsItemType.List;
    List<SecsValue> secsValueList = new List<SecsValue>();
    if (value == null)
      value = (IEnumerable<object>) new object[0];
    foreach (object obj in value)
    {
      Type type = obj.GetType();
      if (type == typeof (SecsValue))
        secsValueList.Add((SecsValue) obj);
      if (type == typeof (bool))
        secsValueList.Add(new SecsValue((bool) obj));
      if (type == typeof (bool[]))
        secsValueList.Add(new SecsValue((bool[]) obj));
      if (type == typeof (sbyte))
        secsValueList.Add(new SecsValue((sbyte) obj));
      if (type == typeof (sbyte[]))
        secsValueList.Add(new SecsValue((sbyte[]) obj));
      if (type == typeof (byte))
        secsValueList.Add(new SecsValue((byte) obj));
      if (type == typeof (short))
        secsValueList.Add(new SecsValue((short) obj));
      if (type == typeof (short[]))
        secsValueList.Add(new SecsValue((short[]) obj));
      if (type == typeof (ushort))
        secsValueList.Add(new SecsValue((ushort) obj));
      if (type == typeof (ushort[]))
        secsValueList.Add(new SecsValue((ushort[]) obj));
      if (type == typeof (int))
        secsValueList.Add(new SecsValue((int) obj));
      if (type == typeof (int[]))
        secsValueList.Add(new SecsValue((int[]) obj));
      if (type == typeof (uint))
        secsValueList.Add(new SecsValue((uint) obj));
      if (type == typeof (uint[]))
        secsValueList.Add(new SecsValue((uint[]) obj));
      if (type == typeof (long))
        secsValueList.Add(new SecsValue((long) obj));
      if (type == typeof (long[]))
        secsValueList.Add(new SecsValue((long[]) obj));
      if (type == typeof (ulong))
        secsValueList.Add(new SecsValue((ulong) obj));
      if (type == typeof (ulong[]))
        secsValueList.Add(new SecsValue((ulong[]) obj));
      if (type == typeof (float))
        secsValueList.Add(new SecsValue((float) obj));
      if (type == typeof (float[]))
        secsValueList.Add(new SecsValue((float[]) obj));
      if (type == typeof (double))
        secsValueList.Add(new SecsValue((double) obj));
      if (type == typeof (double[]))
        secsValueList.Add(new SecsValue((double[]) obj));
      if (type == typeof (string))
        secsValueList.Add(new SecsValue((string) obj));
      if (type == typeof (string[]))
        secsValueList.Add(new SecsValue((string[]) obj));
      if (type == typeof (byte[]))
        secsValueList.Add(new SecsValue((byte[]) obj));
      if (type == typeof (object[]))
        secsValueList.Add(new SecsValue((IEnumerable<object>) (object[]) obj));
      if (type == typeof (List<object>))
        secsValueList.Add(new SecsValue((IEnumerable<object>) obj));
    }
    this.Value = (object) secsValueList.ToArray();
  }

  /// <inheritdoc cref="M:HslCommunication.Secs.Types.SecsValue.#ctor(HslCommunication.Secs.Types.SecsItemType,System.Object,System.Int32)" />
  public SecsValue(SecsItemType type, object value)
  {
    this.ItemType = type;
    this.Value = value;
  }

  /// <summary>
  /// 通过指定的参数类型及值信息，来实例化一个对象<br />
  /// Instantiate an object by specifying the parameter type and value information
  /// </summary>
  /// <param name="type">数据的类型信息</param>
  /// <param name="value">数据值信息，当是<see cref="F:HslCommunication.Secs.Types.SecsItemType.List" />类型时，本值为空 </param>
  /// <param name="length">长度参数信息</param>
  public SecsValue(SecsItemType type, object value, int length)
  {
    this.ItemType = type;
    this.Value = value;
    this.length = length;
  }

  /// <summary>从完整的XML元素进行实例化一个对象</summary>
  /// <param name="element">符合SECS的XML数据表示元素</param>
  /// <exception cref="T:System.ArgumentException">解析失败的异常</exception>
  public SecsValue(XElement element)
  {
    if (element.Name == (XName) "List")
    {
      this.ItemType = SecsItemType.List;
      List<SecsValue> secsValueList = new List<SecsValue>();
      foreach (XElement element1 in element.Elements())
      {
        SecsValue secsValue = new SecsValue(element1);
        if (secsValue != null)
          secsValueList.Add(secsValue);
      }
      this.Value = (object) secsValueList.ToArray();
    }
    else if (element.Name == (XName) "SByte")
    {
      this.ItemType = SecsItemType.SByte;
      this.Value = SecsValue.GetObjectValue<sbyte>(element, new Func<string, sbyte>(sbyte.Parse));
    }
    else if (element.Name == (XName) "Byte")
    {
      this.ItemType = SecsItemType.Byte;
      this.Value = SecsValue.GetObjectValue<byte>(element, new Func<string, byte>(byte.Parse));
    }
    else if (element.Name == (XName) "Int16")
    {
      this.ItemType = SecsItemType.Int16;
      this.Value = SecsValue.GetObjectValue<short>(element, new Func<string, short>(short.Parse));
    }
    else if (element.Name == (XName) "UInt16")
    {
      this.ItemType = SecsItemType.UInt16;
      this.Value = SecsValue.GetObjectValue<ushort>(element, new Func<string, ushort>(ushort.Parse));
    }
    else if (element.Name == (XName) "Int32")
    {
      this.ItemType = SecsItemType.Int32;
      this.Value = SecsValue.GetObjectValue<int>(element, new Func<string, int>(int.Parse));
    }
    else if (element.Name == (XName) "UInt32")
    {
      this.ItemType = SecsItemType.UInt32;
      this.Value = SecsValue.GetObjectValue<uint>(element, new Func<string, uint>(uint.Parse));
    }
    else if (element.Name == (XName) "Int64")
    {
      this.ItemType = SecsItemType.Int64;
      this.Value = SecsValue.GetObjectValue<long>(element, new Func<string, long>(long.Parse));
    }
    else if (element.Name == (XName) "UInt64")
    {
      this.ItemType = SecsItemType.UInt64;
      this.Value = SecsValue.GetObjectValue<ulong>(element, new Func<string, ulong>(ulong.Parse));
    }
    else if (element.Name == (XName) "Single")
    {
      this.ItemType = SecsItemType.Single;
      this.Value = SecsValue.GetObjectValue<float>(element, new Func<string, float>(float.Parse));
    }
    else if (element.Name == (XName) "Double")
    {
      this.ItemType = SecsItemType.Double;
      this.Value = SecsValue.GetObjectValue<double>(element, new Func<string, double>(double.Parse));
    }
    else if (element.Name == (XName) "Bool")
    {
      this.ItemType = SecsItemType.Bool;
      this.Value = SecsValue.GetObjectValue<bool>(element, new Func<string, bool>(bool.Parse));
    }
    else if (element.Name == (XName) "ASCII")
    {
      this.ItemType = SecsItemType.ASCII;
      this.Value = (object) SecsValue.GetAttribute<string>(element, nameof (Value), (string) null, (Func<string, string>) (m => m));
    }
    else if (element.Name == (XName) "Binary")
    {
      this.ItemType = SecsItemType.Binary;
      this.Value = (object) SecsValue.GetAttribute<byte[]>(element, nameof (Value), new byte[0], (Func<string, byte[]>) (m => m.ToHexBytes()));
    }
    else if (element.Name == (XName) "JIS8")
    {
      this.ItemType = SecsItemType.JIS8;
      this.Value = (object) SecsValue.GetAttribute<byte[]>(element, nameof (Value), new byte[0], (Func<string, byte[]>) (m => m.ToHexBytes()));
    }
    else
    {
      if (!(element.Name == (XName) "None"))
        throw new ArgumentException(nameof (element));
      this.ItemType = SecsItemType.None;
      this.Value = (object) SecsValue.GetAttribute<string>(element, nameof (Value), (string) null, (Func<string, string>) (m => m));
    }
  }

  /// <summary>类型信息</summary>
  public SecsItemType ItemType { get; set; }

  /// <summary>
  /// 字节长度信息，如果是 <see cref="F:HslCommunication.Secs.Types.SecsItemType.List" /> 类型的话，就是数组长度，如果如 <see cref="F:HslCommunication.Secs.Types.SecsItemType.ASCII" /> 类型，就是字符串的字节长度，其他类型都是表示数据个数<br />
  /// Byte length information, if it is of type <see cref="F:HslCommunication.Secs.Types.SecsItemType.List" />, it is the length of the array, if it is of type <see cref="F:HslCommunication.Secs.Types.SecsItemType.ASCII" />,
  /// it is the byte length of the string, other types are the number of data
  /// </summary>
  public int Length => this.length;

  /// <summary>
  /// 数据值信息，也可以是 <see cref="T:HslCommunication.Secs.Types.SecsValue" /> 的列表信息，在设置列表之前，必须先设置类型
  /// </summary>
  public object Value
  {
    get => this.obj;
    set
    {
      this.obj = this.ItemType != SecsItemType.None || value == null ? value : throw new ArgumentException("Must set ItemType before set value.", nameof (value));
      this.length = SecsValue.GetValueLength(this);
    }
  }

  /// <summary>获取当前数值的XML表示形式</summary>
  /// <returns>XML元素信息</returns>
  public XElement ToXElement()
  {
    if (this.ItemType == SecsItemType.List)
    {
      XElement xelement = new XElement((XName) "List");
      if (this.Value is IEnumerable<SecsValue> source)
      {
        xelement.SetAttributeValue((XName) "Length", (object) source.Count<SecsValue>());
        foreach (SecsValue secsValue in source)
          xelement.Add((object) secsValue.ToXElement());
      }
      else
        xelement.SetAttributeValue((XName) "Length", (object) 0);
      return xelement;
    }
    XElement xelement1 = new XElement((XName) this.ItemType.ToString());
    xelement1.SetAttributeValue((XName) "Length", (object) this.Length);
    if (this.ItemType == SecsItemType.Binary || this.ItemType == SecsItemType.JIS8)
      xelement1.SetAttributeValue((XName) "Value", (object) (this.Value as byte[]).ToHexString());
    else if (this.ItemType == SecsItemType.ASCII)
      xelement1.SetAttributeValue((XName) "Value", this.Value);
    else if (this.ItemType != SecsItemType.None)
    {
      if (this.Value is Array array)
      {
        StringBuilder stringBuilder = new StringBuilder("[");
        for (int index = 0; index < array.Length; ++index)
        {
          stringBuilder.Append(array.GetValue(index).ToString());
          if (index != array.Length - 1)
            stringBuilder.Append(",");
        }
        stringBuilder.Append("]");
        xelement1.SetAttributeValue((XName) "Value", (object) stringBuilder.ToString());
      }
      else
        xelement1.SetAttributeValue((XName) "Value", this.Value);
    }
    return xelement1;
  }

  /// <summary>当前的对象信息转换回实际的原始字节信息，方便写入操作</summary>
  /// <returns>原始字节数据</returns>
  public byte[] ToSourceBytes() => this.ToSourceBytes(Encoding.Default);

  /// <summary>使用指定的编码将当前的对象信息转换回实际的原始字节信息，方便写入操作</summary>
  /// <param name="encoding">编码信息</param>
  /// <returns>原始字节数据</returns>
  public byte[] ToSourceBytes(Encoding encoding)
  {
    if (this.ItemType == SecsItemType.None)
      return new byte[0];
    List<byte> bytes = new List<byte>();
    if (this.ItemType == SecsItemType.List)
    {
      Secs2.AddCodeAndValueSource(bytes, this, encoding);
      if (this.Value is SecsValue[] secsValueArray)
      {
        for (int index = 0; index < secsValueArray.Length; ++index)
        {
          SecsValue secsValue = secsValueArray[index];
          bytes.AddRange((IEnumerable<byte>) secsValue.ToSourceBytes(encoding));
        }
      }
    }
    else
      Secs2.AddCodeAndValueSource(bytes, this, encoding);
    return bytes.ToArray();
  }

  private static string getSpace(int spaceDegree, bool format)
  {
    return !format || spaceDegree == 0 ? string.Empty : "".PadLeft(spaceDegree * 4, ' ');
  }

  private static string getSourceCode(SecsValue secsValue, bool format = true, int spaceDegree = 0)
  {
    if (secsValue.ItemType == SecsItemType.List)
    {
      StringBuilder stringBuilder = new StringBuilder("new object[] { ");
      if (format)
        stringBuilder.Append(Environment.NewLine);
      if (secsValue.Value is IEnumerable<SecsValue> secsValues)
      {
        foreach (SecsValue secsValue1 in secsValues)
        {
          stringBuilder.Append(SecsValue.getSpace(spaceDegree + 1, format) + SecsValue.getSourceCode(secsValue1, format, spaceDegree + 1));
          stringBuilder.Append(",");
          if (format)
            stringBuilder.Append(Environment.NewLine);
          else
            stringBuilder.Append(" ");
        }
      }
      stringBuilder.Append((format ? SecsValue.getSpace(spaceDegree, format) : " ") + "}");
      return stringBuilder.ToString();
    }
    if (secsValue.ItemType == SecsItemType.Binary || secsValue.ItemType == SecsItemType.JIS8)
      return $"\"{(secsValue.Value as byte[]).ToHexString()}\".ToHexBytes( )";
    if (secsValue.ItemType == SecsItemType.ASCII)
      return $"\"{secsValue.Value}\"";
    return secsValue.ItemType == SecsItemType.Int16 ? (secsValue.Value is Array array1 ? $"new short[]{{ {SecsValue.getArrayString(array1)} }}" : $"(short){secsValue.Value}") : (secsValue.ItemType == SecsItemType.Bool ? (secsValue.Value is Array array2 ? $"new bool[]{{ {SecsValue.getArrayString(array2)} }}" : secsValue.Value.ToString().ToLower() ?? "") : (secsValue.ItemType == SecsItemType.UInt16 ? (secsValue.Value is Array array3 ? $"new ushort[]{{ {SecsValue.getArrayString(array3)} }}" : $"(ushort){secsValue.Value}") : (secsValue.ItemType == SecsItemType.Int32 ? (secsValue.Value is Array array4 ? $"new int[]{{ {SecsValue.getArrayString(array4)} }}" : $"{secsValue.Value}") : (secsValue.ItemType == SecsItemType.UInt32 ? (secsValue.Value is Array array5 ? $"new uint[]{{ {SecsValue.getArrayString(array5)} }}" : $"(uint){secsValue.Value}") : (secsValue.ItemType == SecsItemType.Int64 ? (secsValue.Value is Array array6 ? $"new long[]{{ {SecsValue.getArrayString(array6)} }}" : $"{secsValue.Value}L") : (secsValue.ItemType == SecsItemType.UInt64 ? (secsValue.Value is Array array7 ? $"new ulong[]{{ {SecsValue.getArrayString(array7)} }}" : $"{secsValue.Value}UL") : (secsValue.ItemType == SecsItemType.Single ? (secsValue.Value is Array array8 ? $"new float[]{{ {SecsValue.getArrayString(array8, "f")} }}" : $"{secsValue.Value}f") : (secsValue.ItemType == SecsItemType.Double ? (secsValue.Value is Array array9 ? $"new double[]{{ {SecsValue.getArrayString(array9, "d")} }}" : $"{secsValue.Value}d") : (secsValue.ItemType == SecsItemType.Byte ? (secsValue.Value is Array array10 ? $"new byte[]{{ {SecsValue.getArrayString(array10)} }}" : $"(byte){secsValue.Value}") : (secsValue.ItemType == SecsItemType.SByte ? (secsValue.Value is Array array11 ? $"new sbyte[]{{ {SecsValue.getArrayString(array11)} }}" : $"(sbyte){secsValue.Value}") : (secsValue.Value is Array ? "Unkonw data" : secsValue.Value.ToString())))))))))));
  }

  private static string getArrayString(Array array, string tail = "")
  {
    StringBuilder stringBuilder = new StringBuilder("");
    for (int index = 0; index < array.Length; ++index)
    {
      stringBuilder.Append(array.GetValue(index).ToString());
      if (!string.IsNullOrEmpty(tail))
        stringBuilder.Append(tail);
      if (index != array.Length - 1)
        stringBuilder.Append(",");
    }
    return stringBuilder.ToString();
  }

  /// <summary>
  /// 获取当前 <see cref="T:HslCommunication.Secs.Types.SecsValue" /> 对象的源代码表示方式，可以直接复制生成同等对象<br />
  /// Obtain the source code representation of the current <see cref="T:HslCommunication.Secs.Types.SecsValue" /> object, and you can directly copy and generate an equivalent object
  /// </summary>
  /// <param name="format">是否带换行符的格式化输出</param>
  /// <returns>对象的源代码表示方式</returns>
  public string ToSourceCode(bool format = false)
  {
    if (this.ItemType == SecsItemType.None)
      return "SecsValue.EmptySecsValue( )";
    StringBuilder stringBuilder = new StringBuilder("new SecsValue( ");
    stringBuilder.Append(SecsValue.getSourceCode(this, format));
    stringBuilder.Append(")");
    return stringBuilder.ToString();
  }

  private string getSMLString(SecsValue secsValue, bool format = true, int spaceDegree = 0)
  {
    if (secsValue.ItemType == SecsItemType.List)
    {
      StringBuilder stringBuilder = new StringBuilder("<L ");
      if (secsValue.Value is IEnumerable<SecsValue> source)
        stringBuilder.Append($"[{source.Count<SecsValue>()}]");
      else
        stringBuilder.Append("[0]");
      if (format)
        stringBuilder.Append(Environment.NewLine);
      if (source != null)
      {
        foreach (SecsValue secsValue1 in source)
        {
          stringBuilder.Append(SecsValue.getSpace(spaceDegree + 1, format) + this.getSMLString(secsValue1, format, spaceDegree + 1));
          if (format)
            stringBuilder.Append(Environment.NewLine);
        }
      }
      stringBuilder.Append((format ? SecsValue.getSpace(spaceDegree, format) : " ") + ">");
      return stringBuilder.ToString();
    }
    if (secsValue.ItemType == SecsItemType.Binary)
      return $"<B  \"{(secsValue.Value as byte[]).ToHexString()}\">";
    if (secsValue.ItemType == SecsItemType.JIS8)
      return $"<J  \"{(secsValue.Value as byte[]).ToHexString()}\">";
    if (secsValue.ItemType == SecsItemType.ASCII)
      return $"<A [{secsValue.Length}] \"{secsValue.Value}\">";
    if (secsValue.ItemType == SecsItemType.Int16)
      return secsValue.Value is Array array1 ? $"<I2 [{array1.Length}] {SecsValue.getArrayString(array1)}>" : $"<I2  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.Bool)
      return secsValue.Value is Array array2 ? $"<Boolean [{array2.Length}] {SecsValue.getArrayString(array2)}>" : $"<Boolean  {secsValue.Value.ToString().ToLower()}>";
    if (secsValue.ItemType == SecsItemType.UInt16)
      return secsValue.Value is Array array3 ? $"<U2 [{array3.Length}] {SecsValue.getArrayString(array3)}>" : $"<U2  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.Int32)
      return secsValue.Value is Array array4 ? $"<I4 [{array4.Length}] {SecsValue.getArrayString(array4)}>" : $"<I4  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.UInt32)
      return secsValue.Value is Array array5 ? $"<U4 [{array5.Length}] {SecsValue.getArrayString(array5)}>" : $"<U4  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.Int64)
      return secsValue.Value is Array array6 ? $"<I8 [{array6.Length}] {SecsValue.getArrayString(array6)}>" : $"<I8  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.UInt64)
      return secsValue.Value is Array array7 ? $"<8 [{array7.Length}] {SecsValue.getArrayString(array7)}>" : $"<U8  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.Single)
      return secsValue.Value is Array array8 ? $"<F4 [{array8.Length}] {SecsValue.getArrayString(array8, "f")}>" : $"<F4  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.Double)
      return secsValue.Value is Array array9 ? $"<F8 [{array9.Length}] {SecsValue.getArrayString(array9, "d")}>" : $"<F8  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.Byte)
      return secsValue.Value is Array array10 ? $"<U1 [{array10.Length}] {SecsValue.getArrayString(array10)}>" : $"<U1  {secsValue.Value}>";
    if (secsValue.ItemType == SecsItemType.SByte)
      return secsValue.Value is Array array11 ? $"<I1 [{array11.Length}] {SecsValue.getArrayString(array11)}>" : $"<I1  {secsValue.Value}>";
    if (secsValue.Value is Array)
      return "Unkonw data";
    return secsValue.Value == null ? "<None>" : secsValue.Value.ToString();
  }

  /// <summary>转为SML格式的字符串信息</summary>
  /// <returns>字符串信息</returns>
  public string ToSMLString()
  {
    if (this.ItemType == SecsItemType.None)
      return string.Empty;
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(this.getSMLString(this));
    return stringBuilder.ToString();
  }

  /// <inheritdoc />
  public override string ToString() => this.ToXElement().ToString();

  /// <summary>
  /// 将当前的对象转为 <see cref="T:HslCommunication.Secs.Types.VariableName" /> 数组对象信息，也就是标签名列表
  /// </summary>
  /// <returns><see cref="T:HslCommunication.Secs.Types.VariableName" />数组对象</returns>
  /// <exception cref="T:System.InvalidCastException"></exception>
  public VariableName[] ToVaruableNames()
  {
    TypeHelper.TypeListCheck(this);
    List<VariableName> variableNameList = new List<VariableName>();
    if (this.Value is SecsValue[] secsValueArray)
    {
      for (int index = 0; index < secsValueArray.Length; ++index)
      {
        SecsValue secsItem = secsValueArray[index];
        TypeHelper.TypeListCheck(secsItem);
        variableNameList.Add((VariableName) secsItem);
      }
    }
    return variableNameList.ToArray();
  }

  /// <summary>从一个对象数组里创建一个secsvalue对象</summary>
  /// <param name="objs">实际的数据数组</param>
  /// <returns>secs对象信息</returns>
  public static SecsValue CreateListSecsValue(params object[] objs)
  {
    return new SecsValue((IEnumerable<object>) objs);
  }

  /// <summary>
  /// 从原始的字节数据中解析出实际的 <see cref="T:HslCommunication.Secs.Types.SecsValue" /> 对象内容。
  /// </summary>
  /// <param name="source">原始字节数据</param>
  /// <param name="encoding">编码信息</param>
  /// <returns>SecsItemValue对象</returns>
  public static SecsValue ParseFromSource(byte[] source, Encoding encoding)
  {
    return Secs2.ExtraToSecsItemValue(source, encoding);
  }

  /// <summary>获取空的列表信息</summary>
  /// <returns>secs数据对象</returns>
  public static SecsValue EmptyListValue() => new SecsValue(SecsItemType.List, (object) null);

  /// <summary>获取空的对象信息</summary>
  /// <returns>secs数据对象</returns>
  public static SecsValue EmptySecsValue() => new SecsValue(SecsItemType.None, (object) null);

  /// <summary>
  /// 获取当前的 <see cref="T:HslCommunication.Secs.Types.SecsValue" /> 的数据长度信息
  /// </summary>
  /// <param name="secsValue">secs值</param>
  /// <returns>数据长度信息</returns>
  public static int GetValueLength(SecsValue secsValue)
  {
    if (secsValue.ItemType == SecsItemType.None)
      return 0;
    if (secsValue.ItemType == SecsItemType.List)
      return !(secsValue.Value is IEnumerable<SecsValue> source) ? 0 : source.Count<SecsValue>();
    if (secsValue.Value == null)
      return 0;
    if (secsValue.ItemType == SecsItemType.SByte)
      return secsValue.Value.GetType() == typeof (sbyte) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Byte)
      return secsValue.Value.GetType() == typeof (byte) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Int16)
      return secsValue.Value.GetType() == typeof (short) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.UInt16)
      return secsValue.Value.GetType() == typeof (ushort) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Int32)
      return secsValue.Value.GetType() == typeof (int) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.UInt32)
      return secsValue.Value.GetType() == typeof (uint) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Int64)
      return secsValue.Value.GetType() == typeof (long) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.UInt64)
      return secsValue.Value.GetType() == typeof (ulong) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Single)
      return secsValue.Value.GetType() == typeof (float) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Double)
      return secsValue.Value.GetType() == typeof (double) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Bool)
      return secsValue.Value.GetType() == typeof (bool) ? 1 : (secsValue.Value as Array).Length;
    if (secsValue.ItemType == SecsItemType.Binary)
      return (secsValue.Value as byte[]).Length;
    if (secsValue.ItemType == SecsItemType.JIS8)
      return (secsValue.Value as byte[]).Length;
    return secsValue.ItemType == SecsItemType.ASCII ? secsValue.Value.ToString().Length : 0;
  }

  private static object GetObjectValue<T>(XElement element, Func<string, T> trans)
  {
    string attribute = SecsValue.GetAttribute<string>(element, "Value", "", (Func<string, string>) (m => m));
    return !attribute.Contains(",") ? (object) trans(attribute) : (object) attribute.ToStringArray<T>(trans);
  }

  private static T GetAttribute<T>(
    XElement element,
    string name,
    T defaultValue,
    Func<string, T> trans)
  {
    XAttribute xattribute = element.Attribute((XName) name);
    return xattribute == null ? defaultValue : trans(xattribute.Value);
  }
}
