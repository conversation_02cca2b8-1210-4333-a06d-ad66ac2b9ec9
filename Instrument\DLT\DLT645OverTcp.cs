﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT645OverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Instrument.DLT.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>
/// 基于多功能电能表通信协议实现的通讯类，参考的文档是DLT645-2007，主要实现了对电表数据的读取和一些功能方法，
/// 在点对点模式下，需要在连接后调用 <see cref="M:HslCommunication.Instrument.DLT.DLT645OverTcp.ReadAddress" /> 方法，数据标识格式为 00-00-00-00，具体参照文档手册。<br />
/// The communication type based on the communication protocol of the multifunctional electric energy meter.
/// The reference document is DLT645-2007, which mainly realizes the reading of the electric meter data and some functional methods.
/// In the point-to-point mode, you need to call <see cref="M:HslCommunication.Instrument.DLT.DLT645OverTcp.ReadAddress" /> method after connect the device.
/// the data identification format is 00-00-00-00, refer to the documentation manual for details.
/// </summary>
/// <remarks>
/// 如果一对多的模式，地址可以携带地址域访问，例如 "s=2;00-00-00-00"，主要使用 <see cref="M:HslCommunication.Instrument.DLT.DLT645OverTcp.ReadDouble(System.String,System.UInt16)" /> 方法来读取浮点数，
/// <see cref="M:HslCommunication.Core.Device.DeviceCommunication.ReadString(System.String,System.UInt16)" /> 方法来读取字符串
/// </remarks>
/// <example>
/// <inheritdoc cref="T:HslCommunication.Instrument.DLT.DLT645" path="example" />
/// </example>
public class DLT645OverTcp : DeviceTcpNet, IDlt645, IReadWriteDevice, IReadWriteNet
{
  private string station = "1";
  private string password = "00000000";
  private string opCode = "00000000";

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.#ctor" />
  public DLT645OverTcp()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.password = "00000000";
    this.opCode = "00000000";
    this.WordLength = (ushort) 1;
  }

  /// <summary>
  /// 指定IP地址，端口，地址域，密码，操作者代码来实例化一个对象<br />
  /// Specify the IP address, port, address field, password, and operator code to instantiate an object
  /// </summary>
  /// <param name="ipAddress">TcpServer的IP地址</param>
  /// <param name="port">TcpServer的端口</param>
  /// <param name="station">设备的站号信息</param>
  /// <param name="password">密码，写入的时候进行验证的信息</param>
  /// <param name="opCode">操作者代码</param>
  public DLT645OverTcp(
    string ipAddress,
    int port = 502,
    string station = "1",
    string password = "",
    string opCode = "")
  {
    this.IpAddress = ipAddress;
    this.Port = port;
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.station = station;
    this.password = string.IsNullOrEmpty(password) ? "00000000" : password;
    this.opCode = string.IsNullOrEmpty(opCode) ? "00000000" : opCode;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new DLT645Message(this.CheckDataId);
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    if (!this.EnableCodeFE)
      return base.PackCommandWithHeader(command);
    return SoftBasic.SpliceArray<byte>(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, command);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ActiveDeveice" />
  public OperateResult ActiveDeveice()
  {
    return (OperateResult) this.ReadFromCoreServer(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, false, true);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return DLT645Helper.Read((IDlt645) this, address, length);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return DLT645Helper.ReadDouble((IDlt645) this, address, length);
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromArray<string>(this.ReadStringArray(address));
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ReadStringArray(System.String)" />
  public OperateResult<string[]> ReadStringArray(string address)
  {
    return DLT645Helper.ReadStringArray((IDlt645) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645OverTcp.Trip(System.String,System.DateTime)" />
  public OperateResult Trip(DateTime validTime) => this.Trip(this.Station, validTime);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.Trip(System.String,System.DateTime)" />
  public OperateResult Trip(string station, DateTime validTime)
  {
    return DLT645Helper.Function1C((IDlt645) this, this.password, this.opCode, station, (byte) 26, validTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645OverTcp.SwitchingOn(System.String,System.DateTime)" />
  public OperateResult SwitchingOn(DateTime validTime) => this.SwitchingOn(this.Station, validTime);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.SwitchingOn(System.String,System.DateTime)" />
  public OperateResult SwitchingOn(string station, DateTime validTime)
  {
    return DLT645Helper.Function1C((IDlt645) this, this.password, this.opCode, station, (byte) 27, validTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ActiveDeveice" />
  public async Task<OperateResult> ActiveDeveiceAsync()
  {
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, false, false);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await DLT645Helper.ReadAsync((IDlt645) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645OverTcp.ReadDouble(System.String,System.UInt16)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<double[]> operateResult = await DLT645Helper.ReadDoubleAsync((IDlt645) this, address, length);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<string[]> result = await this.ReadStringArrayAsync(address);
    return ByteTransformHelper.GetResultFromArray<string>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ReadStringArray(System.String)" />
  public async Task<OperateResult<string[]>> ReadStringArrayAsync(string address)
  {
    OperateResult<string[]> operateResult = await DLT645Helper.ReadStringArrayAsync((IDlt645) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.Write(System.String,System.Byte[])" />
  public override OperateResult Write(string address, byte[] value)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override OperateResult Write(string address, short[] values)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<short>) values).Select<short, string>((Func<short, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override OperateResult Write(string address, ushort[] values)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<ushort>) values).Select<ushort, string>((Func<ushort, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override OperateResult Write(string address, int[] values)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<int>) values).Select<int, string>((Func<int, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override OperateResult Write(string address, uint[] values)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<uint>) values).Select<uint, string>((Func<uint, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override OperateResult Write(string address, float[] values)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<float>) values).Select<float, string>((Func<float, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override OperateResult Write(string address, double[] values)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<double>) values).Select<double, string>((Func<double, string>) (m => m.ToString())).ToArray<string>());
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    return DLT645Helper.Write((IDlt645) this, this.password, this.opCode, address, new string[1]
    {
      value
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ReadAddress" />
  public OperateResult<string> ReadAddress() => DLT645Helper.ReadAddress((IDlt645) this);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.WriteAddress(System.String)" />
  public OperateResult WriteAddress(string address)
  {
    return DLT645Helper.WriteAddress((IDlt645) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.BroadcastTime(System.DateTime)" />
  public OperateResult BroadcastTime(DateTime dateTime)
  {
    return DLT645Helper.BroadcastTime((IDlt645) this, dateTime);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.FreezeCommand(System.String)" />
  public OperateResult FreezeCommand(string dataArea)
  {
    return DLT645Helper.FreezeCommand((IDlt645) this, dataArea);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT645.ChangeBaudRate(System.String)" />
  public OperateResult ChangeBaudRate(string baudRate)
  {
    return DLT645Helper.ChangeBaudRate((IDlt645) this, baudRate);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override async Task<OperateResult> WriteAsync(string address, short[] values)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<short>) values).Select<short, string>((Func<short, string>) (m => m.ToString())).ToArray<string>());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override async Task<OperateResult> WriteAsync(string address, ushort[] values)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<ushort>) values).Select<ushort, string>((Func<ushort, string>) (m => m.ToString())).ToArray<string>());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<int>) values).Select<int, string>((Func<int, string>) (m => m.ToString())).ToArray<string>());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<uint>) values).Select<uint, string>((Func<uint, string>) (m => m.ToString())).ToArray<string>());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<float>) values).Select<float, string>((Func<float, string>) (m => m.ToString())).ToArray<string>());
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String,System.String,System.String,System.String[])" />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, ((IEnumerable<double>) values).Select<double, string>((Func<double, string>) (m => m.ToString())).ToArray<string>());
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    OperateResult operateResult = await DLT645Helper.WriteAsync((IDlt645) this, this.password, this.opCode, address, new string[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.ReadAddress(HslCommunication.Instrument.DLT.Helper.IDlt645)" />
  public async Task<OperateResult<string>> ReadAddressAsync()
  {
    OperateResult<string> operateResult = await DLT645Helper.ReadAddressAsync((IDlt645) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.WriteAddress(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public async Task<OperateResult> WriteAddressAsync(string address)
  {
    OperateResult operateResult = await DLT645Helper.WriteAddressAsync((IDlt645) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.BroadcastTime(HslCommunication.Instrument.DLT.Helper.IDlt645,System.DateTime)" />
  public async Task<OperateResult> BroadcastTimeAsync(DateTime dateTime)
  {
    OperateResult operateResult = await DLT645Helper.BroadcastTimeAsync((IDlt645) this, dateTime, new Func<byte[], bool, bool, Task<OperateResult<byte[]>>>(((BinaryCommunication) this).ReadFromCoreServerAsync));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.FreezeCommand(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public async Task<OperateResult> FreezeCommandAsync(string dataArea)
  {
    OperateResult operateResult = await DLT645Helper.FreezeCommandAsync(this, dataArea);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT645Helper.ChangeBaudRate(HslCommunication.Instrument.DLT.Helper.IDlt645,System.String)" />
  public async Task<OperateResult> ChangeBaudRateAsync(string baudRate)
  {
    OperateResult operateResult = await DLT645Helper.ChangeBaudRateAsync((IDlt645) this, baudRate);
    return operateResult;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.DLT645.Station" />
  public string Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.DLT645.EnableCodeFE" />
  public bool EnableCodeFE { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.DLTType" />
  public DLT645Type DLTType { get; } = DLT645Type.DLT2007;

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.Password" />
  public string Password
  {
    get => this.password;
    set => this.password = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.OpCode" />
  public string OpCode
  {
    get => this.opCode;
    set => this.opCode = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.CheckDataId" />
  /// "/&gt;
  public bool CheckDataId { get; set; } = true;

  /// <inheritdoc />
  public override string ToString() => $"DLT645OverTcp[{this.IpAddress}:{this.Port}]";
}
