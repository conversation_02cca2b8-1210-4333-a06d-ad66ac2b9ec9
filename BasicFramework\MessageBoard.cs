﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.MessageBoard
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>系统的消息类，用来发送消息，和确认消息的</summary>
public class MessageBoard
{
  /// <summary>发送方名称</summary>
  public string NameSend { get; set; } = "";

  /// <summary>接收方名称</summary>
  public string NameReceive { get; set; } = "";

  /// <summary>发送时间</summary>
  public DateTime SendTime { get; set; } = DateTime.Now;

  /// <summary>发送的消息内容</summary>
  public string Content { get; set; } = "";

  /// <summary>消息是否已经被查看</summary>
  public bool HasViewed { get; set; }
}
