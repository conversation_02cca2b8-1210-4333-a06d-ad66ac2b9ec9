﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Beckhoff.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Beckhoff;

/// <summary>
/// 倍福的ADS协议，支持读取倍福的地址数据，关于端口号的选择，TwinCAT2，端口号801；TwinCAT3，端口号为851，NETID可以选择手动输入，自动输入方式，具体参考API文档的示例代码<br />
/// Beckhoff's ADS protocol supports reading Beckhoff address data. Regarding the choice of port number, TwinCAT2, port number is 801; TwinCAT3, port number is 851, NETID can be input manually or automatically.
/// For details, please refer to the example of API documentation code
/// </summary>
/// <remarks>
/// 支持的地址格式分四种，第一种是绝对的地址表示，比如M100，I100，Q100；第二种是字符串地址，采用s=aaaa;的表示方式；第三种是绝对内存地址采用i=1000000;的表示方式，第四种是自定义的index group, IG=0xF020;0 的地址<br />
/// There are four supported address formats, the first is absolute address representation, such as M100, I100, Q100; the second is string address, using s=aaaa; representation;
/// the third is absolute memory address using i =1000000; representation, the fourth is the custom index group, the address of IG=0xF020;0
/// <br />
/// <note type="important">
/// 在实际的测试中，由于打开了VS软件对倍福PLC进行编程操作，会导致HslCommunicationDemo读取PLC发生间歇性读写失败的问题，此时需要关闭Visual Studio软件对倍福的连接，之后HslCommunicationDemo就会读写成功，感谢QQ：1813782515 提供的解决思路。
/// </note>
/// </remarks>
/// <example>
/// 地址既支持 M100, I100，Q100 ，读取bool时，支持输入 M100.0,  也支持符号的地址，s=MAIN.a  ,也支持绝对地址的形式， i=1235467;<br />
/// 下面是实例化的例子，可选两种方式
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\BeckhoffAdsNetSample.cs" region="Sample1" title="实例化" />
/// 实例化之后，就可以连接操作了
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\BeckhoffAdsNetSample.cs" region="Sample2" title="连接" />
/// 连接成功之后，就可以进行读写操作了
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\BeckhoffAdsNetSample.cs" region="Sample3" title="读写示例" />
/// 也可以高级的批量读取，需要自己手动解析下数据
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\BeckhoffAdsNetSample.cs" region="Sample4" title="批量读取" />
/// 当然，还可以进一步，既实现了批量的高性能读取，又自动解析。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\BeckhoffAdsNetSample.cs" region="Sample5" title="类型读取" />
/// </example>
public class BeckhoffAdsNet : DeviceTcpNet
{
  private byte[] targetAMSNetId = new byte[8];
  private byte[] sourceAMSNetId = new byte[8];
  private string senderAMSNetId = string.Empty;
  private string _targetAmsNetID = string.Empty;
  private bool useAutoAmsNetID = false;
  private bool useTagCache = false;
  private readonly Dictionary<string, uint> tagCaches = new Dictionary<string, uint>();
  private readonly object tagLock = new object();
  private readonly SoftIncrementCount incrementCount = new SoftIncrementCount((long) int.MaxValue, 1L);

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public BeckhoffAdsNet()
  {
    this.WordLength = (ushort) 2;
    this.targetAMSNetId[4] = (byte) 1;
    this.targetAMSNetId[5] = (byte) 1;
    this.targetAMSNetId[6] = (byte) 83;
    this.targetAMSNetId[7] = (byte) 3;
    this.sourceAMSNetId[4] = (byte) 1;
    this.sourceAMSNetId[5] = (byte) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.CommunicationPipe.UseServerActivePush = true;
  }

  /// <summary>
  /// 通过指定的ip地址以及端口号实例化一个默认的对象<br />
  /// Instantiate a default object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号</param>
  public BeckhoffAdsNet(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new AdsNetMessage();

  /// <inheritdoc />
  [HslMqttApi(HttpMethod = "GET", Description = "Get or set the IP address of the remote server. If it is a local test, then it needs to be set to 127.0.0.1")]
  public override string IpAddress
  {
    get => base.IpAddress;
    set
    {
      base.IpAddress = value;
      string[] strArray = base.IpAddress.Split(new char[1]
      {
        '.'
      }, StringSplitOptions.RemoveEmptyEntries);
      for (int index = 0; index < strArray.Length; ++index)
        this.targetAMSNetId[index] = byte.Parse(strArray[index]);
    }
  }

  /// <summary>
  /// 是否使用标签的名称缓存功能，默认为 <c>False</c><br />
  /// Whether to use tag name caching. The default is <c>False</c>
  /// </summary>
  public bool UseTagCache
  {
    get => this.useTagCache;
    set => this.useTagCache = value;
  }

  /// <summary>
  /// 是否使用服务器自动的NETID信息，默认手动设置<br />
  /// Whether to use the server's automatic NETID information, manually set by default
  /// </summary>
  public bool UseAutoAmsNetID
  {
    get => this.useAutoAmsNetID;
    set => this.useAutoAmsNetID = value;
  }

  /// <summary>
  /// 获取或设置Ams的端口号信息，TwinCAT2，端口号801,811,821,831；TwinCAT3，端口号为851,852,853<br />
  /// Get or set the port number information of Ams, TwinCAT2, the port number is 801, 811, 821, 831; TwinCAT3, the port number is 851, 852, 853
  /// </summary>
  public int AmsPort
  {
    get => (int) BitConverter.ToUInt16(this.targetAMSNetId, 6);
    set
    {
      this.targetAMSNetId[6] = BitConverter.GetBytes(value)[0];
      this.targetAMSNetId[7] = BitConverter.GetBytes(value)[1];
    }
  }

  /// <summary>
  /// 目标的地址，举例 ***********.1.1；也可以是带端口号 ***********.1.1:801<br />
  /// The address of the destination, for example ***********.1.1; it can also be the port number ***********.1.1: 801
  /// </summary>
  /// <remarks>
  /// Port：1: AMS Router; 2: AMS Debugger; 800: Ring 0 TC2 PLC; 801: TC2 PLC Runtime System 1; 811: TC2 PLC Runtime System 2; <br />
  /// 821: TC2 PLC Runtime System 3; 831: TC2 PLC Runtime System 4; 850: Ring 0 TC3 PLC; 851: TC3 PLC Runtime System 1<br />
  /// 852: TC3 PLC Runtime System 2; 853: TC3 PLC Runtime System 3; 854: TC3 PLC Runtime System 4; ...
  /// </remarks>
  /// <param name="amsNetId">AMSNet Id地址</param>
  public void SetTargetAMSNetId(string amsNetId)
  {
    if (string.IsNullOrEmpty(amsNetId))
      return;
    AdsHelper.StrToAMSNetId(amsNetId).CopyTo((Array) this.targetAMSNetId, 0);
    this._targetAmsNetID = amsNetId;
  }

  /// <summary>
  /// 设置原目标地址 举例 *************.1.1；也可以是带端口号 *************.1.1:34567<br />
  /// Set the original destination address Example: *************.1.1; it can also be the port number *************.1.1: 34567
  /// </summary>
  /// <param name="amsNetId">原地址</param>
  public void SetSenderAMSNetId(string amsNetId)
  {
    if (string.IsNullOrEmpty(amsNetId))
      return;
    AdsHelper.StrToAMSNetId(amsNetId).CopyTo((Array) this.sourceAMSNetId, 0);
    this.senderAMSNetId = amsNetId;
  }

  /// <summary>获取当前发送的AMS的网络ID信息</summary>
  /// <returns>AMS发送信息</returns>
  public string GetSenderAMSNetId() => AdsHelper.GetAmsNetIdString(this.sourceAMSNetId, 0);

  /// <summary>获取当前目标的AMS网络的ID信息</summary>
  /// <returns>AMS目标信息</returns>
  public string GetTargetAMSNetId() => AdsHelper.GetAmsNetIdString(this.targetAMSNetId, 0);

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    uint currentValue = (uint) this.incrementCount.GetCurrentValue();
    this.targetAMSNetId.CopyTo((Array) command, 6);
    this.sourceAMSNetId.CopyTo((Array) command, 14);
    command[34] = BitConverter.GetBytes(currentValue)[0];
    command[35] = BitConverter.GetBytes(currentValue)[1];
    command[36] = BitConverter.GetBytes(currentValue)[2];
    command[37] = BitConverter.GetBytes(currentValue)[3];
    return base.PackCommandWithHeader(command);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    if (response.Length >= 38)
    {
      ushort num = this.ByteTransform.TransUInt16(response, 22);
      OperateResult result = (OperateResult) AdsHelper.CheckResponse(response);
      if (!result.IsSuccess)
      {
        if (result.ErrorCode == 1809 && (num == (ushort) 2 || num == (ushort) 3))
        {
          lock (this.tagLock)
            this.tagCaches.Clear();
        }
        return OperateResult.CreateFailedResult<byte[]>(result);
      }
      try
      {
        switch (num)
        {
          case 1:
            return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(42));
          case 2:
            return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(46));
          case 3:
            return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
          case 4:
            return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(42));
          case 5:
            return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(42));
          case 6:
            return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(42));
          case 7:
            return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
          case 9:
            return OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(46));
        }
      }
      catch (Exception ex)
      {
        return new OperateResult<byte[]>($"UnpackResponseContent failed: {ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
      }
    }
    return base.UnpackResponseContent(send, response);
  }

  /// <inheritdoc />
  protected override void ExtraAfterReadFromCoreServer(OperateResult read)
  {
    if (!read.IsSuccess && read.ErrorCode < 0 && this.useTagCache)
    {
      lock (this.tagLock)
        this.tagCaches.Clear();
    }
    base.ExtraAfterReadFromCoreServer(read);
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    if (string.IsNullOrEmpty(this.senderAMSNetId) && string.IsNullOrEmpty(this._targetAmsNetID))
      this.useAutoAmsNetID = true;
    if (this.useAutoAmsNetID)
    {
      OperateResult<byte[]> localNetId = this.GetLocalNetId();
      if (!localNetId.IsSuccess)
        return (OperateResult) localNetId;
      if (localNetId.Content.Length >= 12)
        Array.Copy((Array) localNetId.Content, 6, (Array) this.targetAMSNetId, 0, 6);
      OperateResult operateResult = this.CommunicationPipe.Send(AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.PortConnect, new byte[2]));
      if (!operateResult.IsSuccess)
        return operateResult;
      OperateResult<byte[]> message = this.CommunicationPipe.ReceiveMessage(this.GetNewNetMessage(), (byte[]) null, false);
      if (!message.IsSuccess)
        return (OperateResult) message;
      if (message.Content.Length >= 14)
        Array.Copy((Array) message.Content, 6, (Array) this.sourceAMSNetId, 0, 8);
    }
    else if (string.IsNullOrEmpty(this.senderAMSNetId) && this.CommunicationPipe is PipeTcpNet communicationPipe)
    {
      IPEndPoint localEndPoint = (IPEndPoint) communicationPipe.Socket.LocalEndPoint;
      this.sourceAMSNetId[6] = BitConverter.GetBytes(localEndPoint.Port)[0];
      this.sourceAMSNetId[7] = BitConverter.GetBytes(localEndPoint.Port)[1];
      localEndPoint.Address.GetAddressBytes().CopyTo((Array) this.sourceAMSNetId, 0);
    }
    if (this.useTagCache)
    {
      lock (this.tagLock)
        this.tagCaches.Clear();
    }
    this.CommunicationPipe.UseServerActivePush = true;
    return base.InitializationOnConnect();
  }

  private OperateResult<byte[]> GetLocalNetId()
  {
    PipeTcpNet pipeTcpNet1;
    if (this.CommunicationPipe.GetType() == typeof (PipeSslNet))
    {
      PipeSslNet pipeSslNet = new PipeSslNet(this.IpAddress, this.Port, false);
      pipeSslNet.ConnectTimeOut = this.ConnectTimeOut;
      pipeSslNet.ReceiveTimeOut = this.ReceiveTimeOut;
      pipeTcpNet1 = (PipeTcpNet) pipeSslNet;
    }
    else
    {
      PipeTcpNet pipeTcpNet2 = new PipeTcpNet(this.IpAddress, this.Port);
      pipeTcpNet2.ConnectTimeOut = this.ConnectTimeOut;
      pipeTcpNet2.ReceiveTimeOut = this.ReceiveTimeOut;
      pipeTcpNet1 = pipeTcpNet2;
    }
    OperateResult<bool> result1 = pipeTcpNet1.OpenCommunication();
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    OperateResult result2 = pipeTcpNet1.Send(AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.GetLocalNetId, new byte[4]));
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result2);
    OperateResult<byte[]> message = pipeTcpNet1.ReceiveMessage(this.GetNewNetMessage(), (byte[]) null);
    if (!message.IsSuccess)
      return message;
    pipeTcpNet1.CloseCommunication();
    return message;
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    if (string.IsNullOrEmpty(this.senderAMSNetId) && string.IsNullOrEmpty(this._targetAmsNetID))
      this.useAutoAmsNetID = true;
    if (this.useAutoAmsNetID)
    {
      OperateResult<byte[]> read1 = await this.GetLocalNetIdAsync();
      if (!read1.IsSuccess)
        return (OperateResult) read1;
      if (read1.Content.Length >= 12)
        Array.Copy((Array) read1.Content, 6, (Array) this.targetAMSNetId, 0, 6);
      OperateResult send2 = await this.CommunicationPipe.SendAsync(AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.PortConnect, new byte[2])).ConfigureAwait(false);
      if (!send2.IsSuccess)
        return send2;
      OperateResult<byte[]> read2 = await this.CommunicationPipe.ReceiveMessageAsync(this.GetNewNetMessage(), (byte[]) null, false).ConfigureAwait(false);
      if (!read2.IsSuccess)
        return (OperateResult) read2;
      if (read2.Content.Length >= 14)
        Array.Copy((Array) read2.Content, 6, (Array) this.sourceAMSNetId, 0, 8);
      read1 = (OperateResult<byte[]>) null;
      send2 = (OperateResult) null;
      read2 = (OperateResult<byte[]>) null;
    }
    else if (string.IsNullOrEmpty(this.senderAMSNetId))
    {
      if (this.CommunicationPipe is PipeTcpNet pipe)
      {
        IPEndPoint iPEndPoint = (IPEndPoint) pipe.Socket.LocalEndPoint;
        this.sourceAMSNetId[6] = BitConverter.GetBytes(iPEndPoint.Port)[0];
        this.sourceAMSNetId[7] = BitConverter.GetBytes(iPEndPoint.Port)[1];
        iPEndPoint.Address.GetAddressBytes().CopyTo((Array) this.sourceAMSNetId, 0);
        iPEndPoint = (IPEndPoint) null;
      }
      pipe = (PipeTcpNet) null;
    }
    if (this.useTagCache)
    {
      lock (this.tagLock)
        this.tagCaches.Clear();
    }
    this.CommunicationPipe.UseServerActivePush = true;
    OperateResult operateResult = await base.InitializationOnConnectAsync();
    return operateResult;
  }

  private async Task<OperateResult<byte[]>> GetLocalNetIdAsync()
  {
    PipeTcpNet pipeTcpNet = (PipeTcpNet) null;
    if (this.CommunicationPipe.GetType() == typeof (PipeSslNet))
    {
      PipeSslNet pipeSslNet = new PipeSslNet(this.IpAddress, this.Port, false);
      pipeSslNet.ConnectTimeOut = this.ConnectTimeOut;
      pipeSslNet.ReceiveTimeOut = this.ReceiveTimeOut;
      pipeTcpNet = (PipeTcpNet) pipeSslNet;
    }
    else
    {
      PipeTcpNet pipeTcpNet1 = new PipeTcpNet(this.IpAddress, this.Port);
      pipeTcpNet1.ConnectTimeOut = this.ConnectTimeOut;
      pipeTcpNet1.ReceiveTimeOut = this.ReceiveTimeOut;
      pipeTcpNet = pipeTcpNet1;
    }
    OperateResult<bool> opSocket = await pipeTcpNet.OpenCommunicationAsync().ConfigureAwait(false);
    if (!opSocket.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) opSocket);
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = pipeTcpNet.SendAsync(AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.GetLocalNetId, new byte[4])).ConfigureAwait(false);
    OperateResult send = await configuredTaskAwaitable;
    if (!send.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(send);
    OperateResult<byte[]> read = await pipeTcpNet.ReceiveMessageAsync(this.GetNewNetMessage(), (byte[]) null).ConfigureAwait(false);
    if (!read.IsSuccess)
      return read;
    configuredTaskAwaitable = pipeTcpNet.CloseCommunicationAsync().ConfigureAwait(false);
    OperateResult operateResult = await configuredTaskAwaitable;
    return read;
  }

  /// <inheritdoc />
  protected override bool DecideWhetherQAMessage(
    CommunicationPipe pipe,
    OperateResult<byte[]> receive)
  {
    if (!receive.IsSuccess)
    {
      if (this.useTagCache)
      {
        lock (this.tagLock)
          this.tagCaches.Clear();
      }
      return false;
    }
    byte[] content = receive.Content;
    return content.Length >= 2 && BitConverter.ToUInt16(content, 0) == (ushort) 0 && (content.Length < 24 || this.ByteTransform.TransUInt16(content, 22) != (ushort) 8);
  }

  /// <summary>
  /// 根据当前标签的地址获取到内存偏移地址<br />
  /// Get the memory offset address based on the address of the current label
  /// </summary>
  /// <param name="address">带标签的地址信息，例如s=A,那么标签就是A</param>
  /// <returns>内存偏移地址</returns>
  public OperateResult<uint> ReadValueHandle(string address)
  {
    if (!address.StartsWith("s="))
      return new OperateResult<uint>("When read valueHandle, address must startwith 's=', forexample: s=MAIN.A");
    OperateResult<byte[]> result1 = AdsHelper.BuildReadWriteCommand(address, 4, false, AdsHelper.StrToAdsBytes(address.Substring(2)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<uint>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<uint>((OperateResult) result2) : OperateResult.CreateSuccessResult<uint>(BitConverter.ToUInt32(result2.Content, 0));
  }

  /// <summary>
  /// 将字符串的地址转换为内存的地址，其他地址则不操作<br />
  /// Converts the address of a string to the address of a memory, other addresses do not operate
  /// </summary>
  /// <param name="address">地址信息，s=A的地址转换为i=100000的形式</param>
  /// <returns>地址</returns>
  public OperateResult<string> TransValueHandle(string address)
  {
    if (!address.StartsWith("s=") && !address.StartsWith("S="))
      return OperateResult.CreateSuccessResult<string>(address);
    if (this.useTagCache)
    {
      lock (this.tagLock)
      {
        if (this.tagCaches.ContainsKey(address))
          return OperateResult.CreateSuccessResult<string>($"i={this.tagCaches[address]}");
      }
    }
    OperateResult<uint> result = this.ReadValueHandle(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    if (this.useTagCache)
    {
      lock (this.tagLock)
      {
        if (!this.tagCaches.ContainsKey(address))
          this.tagCaches.Add(address, result.Content);
      }
    }
    return OperateResult.CreateSuccessResult<string>($"i={result.Content}");
  }

  /// <summary>
  /// 读取Ads设备的设备信息。主要是版本号，设备名称<br />
  /// Read the device information of the Ads device. Mainly version number, device name
  /// </summary>
  /// <returns>设备信息</returns>
  [HslMqttApi("ReadAdsDeviceInfo", "读取Ads设备的设备信息。主要是版本号，设备名称")]
  public OperateResult<AdsDeviceInfo> ReadAdsDeviceInfo()
  {
    OperateResult<byte[]> result1 = AdsHelper.BuildReadDeviceInfoCommand();
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<AdsDeviceInfo>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<AdsDeviceInfo>((OperateResult) result2) : OperateResult.CreateSuccessResult<AdsDeviceInfo>(new AdsDeviceInfo(result2.Content));
  }

  /// <summary>
  /// 读取Ads设备的状态信息，其中<see cref="P:HslCommunication.OperateResult`2.Content1" />是Ads State，<see cref="P:HslCommunication.OperateResult`2.Content2" />是Device State<br />
  /// Read the status information of the Ads device, where <see cref="P:HslCommunication.OperateResult`2.Content1" /> is the Ads State, and <see cref="P:HslCommunication.OperateResult`2.Content2" /> is the Device State
  /// </summary>
  /// <returns>设备状态信息</returns>
  [HslMqttApi("ReadAdsState", "读取Ads设备的状态信息")]
  public OperateResult<ushort, ushort> ReadAdsState()
  {
    OperateResult<byte[]> result1 = AdsHelper.BuildReadStateCommand();
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<ushort, ushort>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<ushort, ushort>((OperateResult) result2) : OperateResult.CreateSuccessResult<ushort, ushort>(BitConverter.ToUInt16(result2.Content, 0), BitConverter.ToUInt16(result2.Content, 2));
  }

  /// <summary>
  /// 写入Ads的状态，可以携带数据信息，数据可以为空<br />
  /// Write the status of Ads, can carry data information, and the data can be empty
  /// </summary>
  /// <param name="state">ads state</param>
  /// <param name="deviceState">device state</param>
  /// <param name="data">数据信息</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteAdsState", "写入Ads的状态，可以携带数据信息，数据可以为空")]
  public OperateResult WriteAdsState(short state, short deviceState, byte[] data)
  {
    OperateResult<byte[]> operateResult = AdsHelper.BuildWriteControlCommand(state, deviceState, data);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <summary>
  /// 释放当前的系统句柄，该句柄是通过<see cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.ReadValueHandle(System.String)" />获取的
  /// </summary>
  /// <param name="handle">句柄</param>
  /// <returns>是否释放成功</returns>
  public OperateResult ReleaseSystemHandle(uint handle)
  {
    OperateResult<byte[]> operateResult = AdsHelper.BuildReleaseSystemHandle(handle);
    return !operateResult.IsSuccess ? (OperateResult) operateResult : (OperateResult) this.ReadFromCoreServer(operateResult.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.ReadValueHandle(System.String)" />
  public async Task<OperateResult<uint>> ReadValueHandleAsync(string address)
  {
    if (!address.StartsWith("s="))
      return new OperateResult<uint>("When read valueHandle, address must startwith 's=', forexample: s=MAIN.A");
    OperateResult<byte[]> build = AdsHelper.BuildReadWriteCommand(address, 4, false, AdsHelper.StrToAdsBytes(address.Substring(2)));
    if (!build.IsSuccess)
      return OperateResult.CreateFailedResult<uint>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<uint>(BitConverter.ToUInt32(read.Content, 0)) : OperateResult.CreateFailedResult<uint>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.TransValueHandle(System.String)" />
  public async Task<OperateResult<string>> TransValueHandleAsync(string address)
  {
    if (!address.StartsWith("s=") && !address.StartsWith("S="))
      return OperateResult.CreateSuccessResult<string>(address);
    if (this.useTagCache)
    {
      lock (this.tagLock)
      {
        if (this.tagCaches.ContainsKey(address))
          return OperateResult.CreateSuccessResult<string>($"i={this.tagCaches[address]}");
      }
    }
    OperateResult<uint> read = await this.ReadValueHandleAsync(address);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    if (this.useTagCache)
    {
      lock (this.tagLock)
      {
        if (!this.tagCaches.ContainsKey(address))
          this.tagCaches.Add(address, read.Content);
      }
    }
    return OperateResult.CreateSuccessResult<string>($"i={read.Content}");
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.ReadAdsDeviceInfo" />
  public async Task<OperateResult<AdsDeviceInfo>> ReadAdsDeviceInfoAsync()
  {
    OperateResult<byte[]> build = AdsHelper.BuildReadDeviceInfoCommand();
    if (!build.IsSuccess)
      return OperateResult.CreateFailedResult<AdsDeviceInfo>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<AdsDeviceInfo>(new AdsDeviceInfo(read.Content)) : OperateResult.CreateFailedResult<AdsDeviceInfo>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.ReadAdsState" />
  public async Task<OperateResult<ushort, ushort>> ReadAdsStateAsync()
  {
    OperateResult<byte[]> build = AdsHelper.BuildReadStateCommand();
    if (!build.IsSuccess)
      return OperateResult.CreateFailedResult<ushort, ushort>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<ushort, ushort>(BitConverter.ToUInt16(read.Content, 0), BitConverter.ToUInt16(read.Content, 2)) : OperateResult.CreateFailedResult<ushort, ushort>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.WriteAdsState(System.Int16,System.Int16,System.Byte[])" />
  public async Task<OperateResult> WriteAdsStateAsync(short state, short deviceState, byte[] data)
  {
    OperateResult<byte[]> build = AdsHelper.BuildWriteControlCommand(state, deviceState, data);
    if (!build.IsSuccess)
      return (OperateResult) build;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(build.Content);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.ReleaseSystemHandle(System.UInt32)" />
  public async Task<OperateResult> ReleaseSystemHandleAsync(uint handle)
  {
    OperateResult<byte[]> build = AdsHelper.BuildReleaseSystemHandle(handle);
    if (!build.IsSuccess)
      return (OperateResult) build;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(build.Content);
    return (OperateResult) operateResult;
  }

  /// <summary>
  /// 读取PLC的数据，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A<br />
  /// Read PLC data, there are three formats of address, one: I, Q, M data information, such as M0, M100; two: memory address, i = 100000; three: tag address, s = A
  /// </summary>
  /// <param name="address">地址信息，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A</param>
  /// <param name="length">长度</param>
  /// <returns>包含是否成功的结果对象</returns>
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<string> result = this.TransValueHandle(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    address = result.Content;
    OperateResult<byte[]> operateResult = AdsHelper.BuildReadCommand(address, (int) length, false);
    return !operateResult.IsSuccess ? operateResult : this.ReadFromCoreServer(operateResult.Content);
  }

  private void AddLengthAndOffset(List<ushort> length, List<int> offset, ref int index, int len)
  {
    length.Add((ushort) len);
    offset.Add(index);
    index += len;
  }

  /// <inheritdoc />
  public override OperateResult<T> Read<T>()
  {
    Type valueType = typeof (T);
    object instance = valueType.Assembly.CreateInstance(valueType.FullName);
    List<HslAddressProperty> hslPropertyInfos = HslReflectionHelper.GetHslPropertyInfos(valueType, this.GetType(), (object) null, this.ByteTransform);
    OperateResult<byte[]> result = this.Read(hslPropertyInfos.Select<HslAddressProperty, string>((Func<HslAddressProperty, string>) (m => m.DeviceAddressAttribute.Address)).ToArray<string>(), hslPropertyInfos.Select<HslAddressProperty, ushort>((Func<HslAddressProperty, ushort>) (m => (ushort) m.ByteLength)).ToArray<ushort>());
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) result);
    HslReflectionHelper.SetPropertyValueFrom(this.ByteTransform, instance, hslPropertyInfos, result.Content);
    return OperateResult.CreateSuccessResult<T>((T) instance);
  }

  /// <summary>
  /// 读取结构体的信息，传入结构体的类型，以及结构体的起始地址<br />
  /// Read the information of the structure, the type of the incoming structure, and the start address of the structure
  /// </summary>
  /// <typeparam name="T">结构体类型</typeparam>
  /// <param name="address">结构体的地址信息</param>
  /// <returns>是否读取成功</returns>
  public OperateResult<T> ReadStruct<T>(string address) where T : struct
  {
    T structure1 = new T();
    OperateResult<byte[]> result = this.Read(address, (ushort) Marshal.SizeOf<T>(structure1));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) result);
    GCHandle gcHandle = GCHandle.Alloc((object) result.Content, GCHandleType.Pinned);
    T structure2 = (T) Marshal.PtrToStructure(gcHandle.AddrOfPinnedObject(), typeof (T));
    gcHandle.Free();
    return OperateResult.CreateSuccessResult<T>(structure2);
  }

  /// <summary>
  /// 将一个结构体写入到指定的地址中去，需要指定写入的起始地址<br />
  /// To write a structure to a specified address, you need to specify the start address of the write
  /// </summary>
  /// <typeparam name="T">结构体类型</typeparam>
  /// <param name="address">起始地址</param>
  /// <param name="value">结构体的值</param>
  /// <returns>是否写入成功</returns>
  public OperateResult WriteStruct<T>(string address, T value) where T : struct
  {
    byte[] numArray = new byte[Marshal.SizeOf<T>(value)];
    GCHandle gcHandle = GCHandle.Alloc((object) numArray, GCHandleType.Pinned);
    Marshal.StructureToPtr<T>(value, gcHandle.AddrOfPinnedObject(), false);
    gcHandle.Free();
    return this.Write(address, numArray);
  }

  /// <summary>
  /// 批量读取PLC的数据，需要传入地址数组，以及读取的长度数组信息，长度单位为字节单位，如果是读取bool变量的，则以bool为单位，统一返回一串字节数据信息，需要进行二次解析的操作。<br />
  /// To read PLC data in batches, you need to pass in the address array and the read length array information. The unit of length is in bytes. If you read a bool variable,
  /// it will return a string of byte data information in units of bool. , which requires a secondary parsing operation.
  /// </summary>
  /// <remarks>
  /// 关于二次解析的参考信息，可以参考API文档，地址：http://api.hslcommunication.cn<br />
  /// For reference information about secondary parsing, you can refer to the API documentation, address: http://api.hslcommunication.cn
  /// </remarks>
  /// <param name="address">地址数组信息</param>
  /// <param name="length">读取的长度数组信息</param>
  /// <returns>原始字节数组的结果对象</returns>
  [HslMqttApi("ReadBatchByte", "To read PLC data in batches, you need to pass in the address array and the read length array information.")]
  public OperateResult<byte[]> Read(string[] address, ushort[] length)
  {
    if (address.Length != length.Length)
      return new OperateResult<byte[]>(StringResources.Language.TwoParametersLengthIsNotSame);
    for (int index = 0; index < address.Length; ++index)
    {
      OperateResult<string> result = this.TransValueHandle(address[index]);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
      address[index] = result.Content;
    }
    OperateResult<byte[]> operateResult = AdsHelper.BuildReadCommand(address, length);
    return !operateResult.IsSuccess ? operateResult : this.ReadFromCoreServer(operateResult.Content);
  }

  /// <summary>
  /// 写入PLC的数据，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A<br />
  /// There are three formats for the data written into the PLC. One: I, Q, M data information, such as M0, M100; two: memory address, i = 100000; three: tag address, s = A
  /// </summary>
  /// <param name="address">地址信息，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<string> operateResult1 = this.TransValueHandle(address);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    address = operateResult1.Content;
    OperateResult<byte[]> operateResult2 = AdsHelper.BuildWriteCommand(address, value, false);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) this.ReadFromCoreServer(operateResult2.Content);
  }

  /// <summary>
  /// 读取PLC的数据，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A<br />
  /// Read PLC data, there are three formats of address, one: I, Q, M data information, such as M0, M100; two: memory address, i = 100000; three: tag address, s = A
  /// </summary>
  /// <param name="address">PLC的地址信息，例如 M10</param>
  /// <param name="length">数据长度</param>
  /// <returns>包含是否成功的结果对象</returns>
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (Regex.IsMatch(address, "^[MIQ][0-9]+\\.[0-7]$", RegexOptions.IgnoreCase) && length > (ushort) 1)
      return HslHelper.ReadBool((IReadWriteNet) this, address, length, 8);
    OperateResult<string> result1 = this.TransValueHandle(address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    address = result1.Content;
    OperateResult<byte[]> result2 = AdsHelper.BuildReadCommand(address, (int) length, true);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
    OperateResult<byte[]> result3 = this.ReadFromCoreServer(result2.Content);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result3) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) result3.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
  }

  /// <summary>
  /// 写入PLC的数据，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A<br />
  /// There are three formats for the data written into the PLC. One: I, Q, M data information, such as M0, M100; two: memory address, i = 100000; three: tag address, s = A
  /// </summary>
  /// <param name="address">地址信息</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<string> operateResult1 = this.TransValueHandle(address);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    address = operateResult1.Content;
    OperateResult<byte[]> operateResult2 = AdsHelper.BuildWriteCommand(address, value, true);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) this.ReadFromCoreServer(operateResult2.Content);
  }

  /// <summary>
  /// 读取PLC的数据，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A<br />
  /// Read PLC data, there are three formats of address, one: I, Q, M data information, such as M0, M100; two: memory address, i = 100000; three: tag address, s = A
  /// </summary>
  /// <param name="address">地址信息</param>
  /// <returns>包含是否成功的结果对象</returns>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>
  /// 写入PLC的数据，地址共有三种格式，一：I,Q,M数据信息，举例M0,M100；二：内存地址，i=100000；三：标签地址，s=A<br />
  /// There are three formats for the data written into the PLC. One: I, Q, M data information, such as M0, M100; two: memory address, i = 100000; three: tag address, s = A
  /// </summary>
  /// <param name="address">地址信息</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc />
  public override async Task<OperateResult<T>> ReadAsync<T>()
  {
    Type type = typeof (T);
    object obj = type.Assembly.CreateInstance(type.FullName);
    List<HslAddressProperty> array = HslReflectionHelper.GetHslPropertyInfos(type, this.GetType(), (object) null, this.ByteTransform);
    string[] address = array.Select<HslAddressProperty, string>((Func<HslAddressProperty, string>) (m => m.DeviceAddressAttribute.Address)).ToArray<string>();
    ushort[] length = array.Select<HslAddressProperty, ushort>((Func<HslAddressProperty, ushort>) (m => (ushort) m.ByteLength)).ToArray<ushort>();
    OperateResult<byte[]> read = await this.ReadAsync(address, length).ConfigureAwait(false);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<T>((OperateResult) read);
    HslReflectionHelper.SetPropertyValueFrom(this.ByteTransform, obj, array, read.Content);
    return OperateResult.CreateSuccessResult<T>((T) obj);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<string> addressCheck = await this.TransValueHandleAsync(address);
    if (!addressCheck.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressCheck);
    address = addressCheck.Content;
    OperateResult<byte[]> build = AdsHelper.BuildReadCommand(address, (int) length, false);
    if (!build.IsSuccess)
      return build;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.Read(System.String[],System.UInt16[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address, ushort[] length)
  {
    if (address.Length != length.Length)
      return new OperateResult<byte[]>(StringResources.Language.TwoParametersLengthIsNotSame);
    for (int i = 0; i < address.Length; ++i)
    {
      OperateResult<string> addressCheck = this.TransValueHandle(address[i]);
      if (!addressCheck.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) addressCheck);
      address[i] = addressCheck.Content;
      addressCheck = (OperateResult<string>) null;
    }
    OperateResult<byte[]> build = AdsHelper.BuildReadCommand(address, length);
    if (!build.IsSuccess)
      return build;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<string> addressCheck = await this.TransValueHandleAsync(address);
    if (!addressCheck.IsSuccess)
      return (OperateResult) addressCheck;
    address = addressCheck.Content;
    OperateResult<byte[]> build = AdsHelper.BuildWriteCommand(address, value, false);
    if (!build.IsSuccess)
      return (OperateResult) build;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    if (Regex.IsMatch(address, "^[MIQ][0-9]+\\.[0-7]$", RegexOptions.IgnoreCase) && length > (ushort) 1)
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) this, address, length, 8).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<string> addressCheck = await this.TransValueHandleAsync(address).ConfigureAwait(false);
    if (!addressCheck.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) addressCheck);
    address = addressCheck.Content;
    OperateResult<byte[]> build = AdsHelper.BuildReadCommand(address, (int) length, true);
    if (!build.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return read.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) read.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult<string> addressCheck = await this.TransValueHandleAsync(address).ConfigureAwait(false);
    if (!addressCheck.IsSuccess)
      return (OperateResult) addressCheck;
    address = addressCheck.Content;
    OperateResult<byte[]> build = AdsHelper.BuildWriteCommand(address, value, true);
    if (!build.IsSuccess)
      return (OperateResult) build;
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1).ConfigureAwait(false);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    }).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"BeckhoffAdsNet[{this.IpAddress}:{this.Port}]";
}
