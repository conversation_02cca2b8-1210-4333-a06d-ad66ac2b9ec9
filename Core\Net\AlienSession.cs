﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.AlienSession
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Pipe;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>异形客户端的连接对象</summary>
public class AlienSession
{
  /// <summary>实例化一个默认的参数</summary>
  public AlienSession()
  {
    this.IsStatusOk = true;
    this.OnlineTime = DateTime.Now;
    this.OfflineTime = DateTime.MinValue;
  }

  /// <summary>网络套接字</summary>
  public PipeTcpNet Pipe { get; set; }

  /// <summary>唯一的标识</summary>
  public string DTU { get; set; }

  /// <summary>密码信息</summary>
  public string Pwd { get; set; }

  /// <summary>指示当前的网络状态</summary>
  public bool IsStatusOk { get; set; }

  /// <summary>上线时间</summary>
  public DateTime OnlineTime { get; set; }

  /// <summary>最后一次下线的时间</summary>
  public DateTime OfflineTime { get; set; }

  /// <summary>进行下线操作</summary>
  public void Offline()
  {
    if (!this.IsStatusOk)
      return;
    this.IsStatusOk = false;
    this.OfflineTime = DateTime.Now;
  }

  /// <inheritdoc />
  public override string ToString()
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append($"DtuSession[{this.DTU}] [{(this.IsStatusOk ? "Online" : "Offline")}]");
    if (this.IsStatusOk)
      stringBuilder.Append($" [{SoftBasic.GetTimeSpanDescription(DateTime.Now - this.OnlineTime)}]");
    else if (this.OfflineTime == DateTime.MinValue)
      stringBuilder.Append(" [----]");
    else
      stringBuilder.Append($" [{SoftBasic.GetTimeSpanDescription(DateTime.Now - this.OfflineTime)}]");
    return stringBuilder.ToString();
  }
}
