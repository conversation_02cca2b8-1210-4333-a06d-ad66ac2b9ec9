﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.XINJE.XinJEServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.ModBus;
using HslCommunication.Reflection;
using System;

#nullable disable
namespace HslCommunication.Profinet.XINJE;

/// <summary>
/// 信捷内部TCP的虚拟服务器类，基于Modbus的虚拟服务器扩展而来，从服务上只支持modbus地址读写，从客户端额外支持M,D,SD,SM,HD 五个数据区<br />
/// <PERSON><PERSON><PERSON>'s internal TCP virtual server class, based on Modbus virtual server extension,
/// only supports modbus address reading and writing from the service, and additionally supports M, D, SD, SM, HD five data areas from the client
/// </summary>
public class XinJEServer : ModbusTcpServer
{
  private SoftBuffer mBuffer;
  private SoftBuffer smBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer sdBuffer;
  private SoftBuffer hdBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个默认参数的mc协议的服务器<br />
  /// Instantiate a mc protocol server with default parameters
  /// </summary>
  public XinJEServer()
  {
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.smBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dBuffer = new SoftBuffer(1000000);
    this.sdBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.hdBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJETcpNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    try
    {
      if (address.StartsWithAndNumber("D"))
        return OperateResult.CreateSuccessResult<byte[]>(this.dBuffer.GetBytes(Convert.ToInt32(address.Substring(1)) * 2, (int) length * 2));
      if (address.StartsWithAndNumber("SD"))
        return OperateResult.CreateSuccessResult<byte[]>(this.sdBuffer.GetBytes(Convert.ToInt32(address.Substring(2)) * 2, (int) length * 2));
      return address.StartsWithAndNumber("HD") ? OperateResult.CreateSuccessResult<byte[]>(this.hdBuffer.GetBytes(Convert.ToInt32(address.Substring(2)) * 2, (int) length * 2)) : base.Read(address, length);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJETcpNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    try
    {
      if (address.StartsWithAndNumber("D"))
      {
        this.dBuffer.SetBytes(value, Convert.ToInt32(address.Substring(1)) * 2);
        return OperateResult.CreateSuccessResult();
      }
      if (address.StartsWithAndNumber("SD"))
      {
        this.sdBuffer.SetBytes(value, Convert.ToInt32(address.Substring(2)) * 2);
        return OperateResult.CreateSuccessResult();
      }
      if (!address.StartsWithAndNumber("HD"))
        return base.Write(address, value);
      this.hdBuffer.SetBytes(value, Convert.ToInt32(address.Substring(2)) * 2);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJETcpNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    try
    {
      if (address.StartsWithAndNumber("M"))
        return OperateResult.CreateSuccessResult<bool[]>(this.mBuffer.GetBool(Convert.ToInt32(address.Substring(1)), (int) length));
      return address.StartsWithAndNumber("SM") ? OperateResult.CreateSuccessResult<bool[]>(this.smBuffer.GetBool(Convert.ToInt32(address.Substring(2)), (int) length)) : base.ReadBool(address, length);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.XINJE.XinJETcpNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    try
    {
      if (address.StartsWithAndNumber("M"))
      {
        this.mBuffer.SetBool(value, Convert.ToInt32(address.Substring(1)));
        return OperateResult.CreateSuccessResult();
      }
      if (!address.StartsWithAndNumber("SM"))
        return base.Write(address, value);
      this.smBuffer.SetBool(value, Convert.ToInt32(address.Substring(2)));
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return (OperateResult) new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive[7] == (byte) 32 /*0x20*/ || receive[7] == (byte) 30)
      return !this.StationDataIsolation && this.StationCheck && (int) receive[6] != (int) this.Station ? new OperateResult<byte[]>($"Station not match XinJe, Need {this.Station} actual {receive[6]}") : OperateResult.CreateSuccessResult<byte[]>(this.ReadByCommand(receive));
    if (receive[7] != (byte) 33 && receive[7] != (byte) 31 /*0x1F*/)
      return base.ReadFromCoreServer(session, receive);
    return !this.StationDataIsolation && this.StationCheck && (int) receive[6] != (int) this.Station ? new OperateResult<byte[]>($"Station not match XinJe, Need {this.Station} actual {receive[6]}") : OperateResult.CreateSuccessResult<byte[]>(this.WriteByMessage(receive));
  }

  /// <summary>将状态码，数据打包成一个完成的回复报文信息</summary>
  /// <param name="command">原始的命令数据</param>
  /// <param name="status">状态信息</param>
  /// <param name="data">数据</param>
  /// <returns>状态信息</returns>
  private byte[] PackCommand(byte[] command, ushort status, byte[] data)
  {
    if (data == null)
    {
      byte[] numArray = command.SelectBegin<byte>(14);
      numArray[4] = (byte) 0;
      numArray[5] = (byte) 8;
      if (status == (ushort) 0)
        return numArray;
      numArray[7] = (byte) ((uint) numArray[7] + 128U /*0x80*/ + (uint) status);
      return numArray;
    }
    byte[] destinationArray = new byte[9 + data.Length];
    Array.Copy((Array) command, 0, (Array) destinationArray, 0, 8);
    destinationArray[4] = (byte) 0;
    destinationArray[5] = (byte) (destinationArray.Length - 6);
    destinationArray[8] = (byte) data.Length;
    data.CopyTo((Array) destinationArray, 9);
    return destinationArray;
  }

  private byte[] ReadByCommand(byte[] command)
  {
    ushort length = this.ByteTransform.TransUInt16(command, 12);
    int destIndex = (int) command[9] * 65536 /*0x010000*/ + (int) command[10] * 256 /*0x0100*/ + (int) command[11];
    byte num = command[8];
    if (command[7] == (byte) 32 /*0x20*/)
    {
      if (length > (ushort) 125)
        return this.PackCommand(command, (ushort) 1, (byte[]) null);
      switch (num)
      {
        case 128 /*0x80*/:
          return this.PackCommand(command, (ushort) 0, this.dBuffer.GetBytes(destIndex * 2, (int) length * 2));
        case 131:
          return this.PackCommand(command, (ushort) 0, this.sdBuffer.GetBytes(destIndex * 2, (int) length * 2));
        case 136:
          return this.PackCommand(command, (ushort) 0, this.hdBuffer.GetBytes(destIndex * 2, (int) length * 2));
        default:
          return this.PackCommand(command, (ushort) 1, (byte[]) null);
      }
    }
    else
    {
      if (command[7] != (byte) 30)
        return this.PackCommand(command, (ushort) 1, (byte[]) null);
      if (length > (ushort) 2000)
        return this.PackCommand(command, (ushort) 1, (byte[]) null);
      if (num == (byte) 3)
        return this.PackCommand(command, (ushort) 0, this.mBuffer.GetBool(destIndex, (int) length).ToByteArray());
      return num == (byte) 13 ? this.PackCommand(command, (ushort) 0, this.smBuffer.GetBool(destIndex, (int) length).ToByteArray()) : this.PackCommand(command, (ushort) 1, (byte[]) null);
    }
  }

  private byte[] WriteByMessage(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommand(command, (ushort) 1, (byte[]) null);
    ushort length = this.ByteTransform.TransUInt16(command, 12);
    int destIndex = (int) command[9] * 65536 /*0x010000*/ + (int) command[10] * 256 /*0x0100*/ + (int) command[11];
    byte num = command[8];
    if (command[7] == (byte) 33)
    {
      byte[] data = command.SelectMiddle<byte>(15, (int) command[14]);
      switch (num)
      {
        case 128 /*0x80*/:
          this.dBuffer.SetBytes(data, destIndex * 2);
          return this.PackCommand(command, (ushort) 0, (byte[]) null);
        case 131:
          this.sdBuffer.SetBytes(data, destIndex * 2);
          return this.PackCommand(command, (ushort) 0, (byte[]) null);
        case 136:
          this.hdBuffer.SetBytes(data, destIndex * 2);
          return this.PackCommand(command, (ushort) 0, (byte[]) null);
        default:
          return this.PackCommand(command, (ushort) 1, (byte[]) null);
      }
    }
    else
    {
      if (command[7] != (byte) 31 /*0x1F*/)
        return this.PackCommand(command, (ushort) 1, (byte[]) null);
      bool[] flagArray = command.SelectMiddle<byte>(15, (int) command[14]).ToBoolArray().SelectBegin<bool>((int) length);
      if (num == (byte) 3)
      {
        this.mBuffer.SetBool(flagArray, destIndex);
        return this.PackCommand(command, (ushort) 0, (byte[]) null);
      }
      if (num != (byte) 13)
        return this.PackCommand(command, (ushort) 1, (byte[]) null);
      this.smBuffer.SetBool(flagArray, destIndex);
      return this.PackCommand(command, (ushort) 0, (byte[]) null);
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"XinJEServer[{this.Port}]";
}
