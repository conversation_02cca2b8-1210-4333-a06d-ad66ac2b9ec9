﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecMcRNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱的R系列的MC协议，支持的地址类型和 <see cref="T:HslCommunication.Profinet.Melsec.MelsecMcNet" /> 有区别，详细请查看对应的API文档说明
/// </summary>
public class MelsecMcRNet : DeviceTcpNet, IReadWriteMc, IReadWriteDevice, IReadWriteNet
{
  /// <summary>
  /// 实例化三菱R系列的Qna兼容3E帧协议的通讯对象<br />
  /// Instantiate the communication object of Mitsubishi's Qna compatible 3E frame protocol
  /// </summary>
  public MelsecMcRNet()
  {
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>
  /// 指定ip地址和端口号来实例化一个默认的对象<br />
  /// Specify the IP address and port number to instantiate a default object
  /// </summary>
  /// <param name="ipAddress">PLC的Ip地址</param>
  /// <param name="port">PLC的端口</param>
  public MelsecMcRNet(string ipAddress, int port)
  {
    this.WordLength = (ushort) 1;
    this.IpAddress = ipAddress;
    this.Port = port;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new MelsecQnA3EBinaryMessage();

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.McType" />
  public McType McType => McType.McRBinary;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecMcNet.NetworkNumber" />
  public byte NetworkNumber { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecMcNet.NetworkStationNumber" />
  public byte NetworkStationNumber { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.PLCNumber" />
  public byte PLCNumber { get; set; } = byte.MaxValue;

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.EnableWriteBitToWordRegister" />
  public bool EnableWriteBitToWordRegister { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.TargetIOStation" />
  public ushort TargetIOStation { get; set; } = 1023 /*0x03FF*/;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.McAnalysisAddress(System.String,System.UInt16,System.Boolean)" />
  public virtual OperateResult<McAddressData> McAnalysisAddress(
    string address,
    ushort length,
    bool isBit)
  {
    return McAddressData.ParseMelsecRFrom(address, length, isBit);
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return McBinaryHelper.PackMcCommand((IReadWriteMc) this, command);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    OperateResult result = McBinaryHelper.CheckResponseContentHelper(response);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult<byte[]>(response.RemoveBegin<byte>(11));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.ExtractActualData(System.Byte[],System.Boolean)" />
  public byte[] ExtractActualData(byte[] response, bool isBit)
  {
    return McBinaryHelper.ExtractActualDataHelper(response, isBit);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return McHelper.Read((IReadWriteMc) this, address, length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return McHelper.Write((IReadWriteMc) this, address, value);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await McHelper.ReadAsync((IReadWriteMc) this, address, length);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await McHelper.WriteAsync((IReadWriteMc) this, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandom(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[])" />
  [HslMqttApi("随机读取PLC的数据信息，可以跨地址，跨类型组合，但是每个地址只能读取一个word，也就是2个字节的内容。收到结果后，需要自行解析数据")]
  public OperateResult<byte[]> ReadRandom(string[] address)
  {
    return McHelper.ReadRandom((IReadWriteMc) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandom(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[],System.UInt16[])" />
  [HslMqttApi(ApiTopic = "ReadRandoms", Description = "随机读取PLC的数据信息，可以跨地址，跨类型组合，每个地址是任意的长度。收到结果后，需要自行解析数据，目前只支持字地址，比如D区，W区，R区，不支持X，Y，M，B，L等等")]
  public OperateResult<byte[]> ReadRandom(string[] address, ushort[] length)
  {
    return McHelper.ReadRandom((IReadWriteMc) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandomInt16(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[])" />
  public OperateResult<short[]> ReadRandomInt16(string[] address)
  {
    return McHelper.ReadRandomInt16((IReadWriteMc) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadRandomUInt16(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc,System.String[])" />
  public OperateResult<ushort[]> ReadRandomUInt16(string[] address)
  {
    return McHelper.ReadRandomUInt16((IReadWriteMc) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.ReadRandom(System.String[])" />
  public async Task<OperateResult<byte[]>> ReadRandomAsync(string[] address)
  {
    OperateResult<byte[]> operateResult = await McHelper.ReadRandomAsync((IReadWriteMc) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.ReadRandom(System.String[],System.UInt16[])" />
  public async Task<OperateResult<byte[]>> ReadRandomAsync(string[] address, ushort[] length)
  {
    OperateResult<byte[]> operateResult = await McHelper.ReadRandomAsync((IReadWriteMc) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.ReadRandomInt16(System.String[])" />
  public async Task<OperateResult<short[]>> ReadRandomInt16Async(string[] address)
  {
    OperateResult<short[]> operateResult = await McHelper.ReadRandomInt16Async((IReadWriteMc) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.ReadRandomUInt16(System.String[])" />
  public async Task<OperateResult<ushort[]>> ReadRandomUInt16Async(string[] address)
  {
    OperateResult<ushort[]> operateResult = await McHelper.ReadRandomUInt16Async((IReadWriteMc) this, address);
    return operateResult;
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return McHelper.ReadBool((IReadWriteMc) this, address, length);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    return McHelper.Write((IReadWriteMc) this, address, values);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await McHelper.ReadBoolAsync((IReadWriteMc) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.Write(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] values)
  {
    OperateResult operateResult = await McHelper.WriteAsync((IReadWriteMc) this, address, values);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.RemoteRun(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  [HslMqttApi(ApiTopic = "RemoteRun", Description = "远程Run操作")]
  public OperateResult RemoteRun() => McHelper.RemoteRun((IReadWriteMc) this);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.RemoteStop(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  [HslMqttApi(ApiTopic = "RemoteStop", Description = "远程Stop操作")]
  public OperateResult RemoteStop() => McHelper.RemoteStop((IReadWriteMc) this);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.RemoteReset(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  [HslMqttApi(ApiTopic = "RemoteReset", Description = "LED 熄灭 出错代码初始化")]
  public OperateResult RemoteReset() => McHelper.RemoteReset((IReadWriteMc) this);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ReadPlcType(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  [HslMqttApi(ApiTopic = "ReadPlcType", Description = "读取PLC的型号信息，例如 Q02HCPU")]
  public OperateResult<string> ReadPlcType() => McHelper.ReadPlcType((IReadWriteMc) this);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.McHelper.ErrorStateReset(HslCommunication.Profinet.Melsec.Helper.IReadWriteMc)" />
  [HslMqttApi(ApiTopic = "ErrorStateReset", Description = "LED 熄灭 出错代码初始化")]
  public OperateResult ErrorStateReset() => McHelper.ErrorStateReset((IReadWriteMc) this);

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.RemoteRun" />
  public async Task<OperateResult> RemoteRunAsync()
  {
    OperateResult operateResult = await McHelper.RemoteRunAsync((IReadWriteMc) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.RemoteStop" />
  public async Task<OperateResult> RemoteStopAsync()
  {
    OperateResult operateResult = await McHelper.RemoteStopAsync((IReadWriteMc) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.RemoteReset" />
  public async Task<OperateResult> RemoteResetAsync()
  {
    OperateResult operateResult = await McHelper.RemoteResetAsync((IReadWriteMc) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.ReadPlcType" />
  public async Task<OperateResult<string>> ReadPlcTypeAsync()
  {
    OperateResult<string> operateResult = await McHelper.ReadPlcTypeAsync((IReadWriteMc) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecMcRNet.ErrorStateReset" />
  public async Task<OperateResult> ErrorStateResetAsync()
  {
    OperateResult operateResult = await McHelper.ErrorStateResetAsync((IReadWriteMc) this);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecMcRNet[{this.IpAddress}:{this.Port}]";

  /// <summary>分析三菱R系列的地址，并返回解析后的数据对象</summary>
  /// <param name="address">字符串地址</param>
  /// <returns>是否解析成功</returns>
  public static OperateResult<MelsecMcDataType, int> AnalysisAddress(string address)
  {
    try
    {
      if (address.StartsWith("LSTS"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LSTS, Convert.ToInt32(address.Substring(4), MelsecMcDataType.R_LSTS.FromBase));
      if (address.StartsWith("LSTC"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LSTC, Convert.ToInt32(address.Substring(4), MelsecMcDataType.R_LSTC.FromBase));
      if (address.StartsWith("LSTN"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LSTN, Convert.ToInt32(address.Substring(4), MelsecMcDataType.R_LSTN.FromBase));
      if (address.StartsWith("STS"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_STS, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_STS.FromBase));
      if (address.StartsWith("STC"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_STC, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_STC.FromBase));
      if (address.StartsWith("STN"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_STN, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_STN.FromBase));
      if (address.StartsWith("LTS"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LTS, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_LTS.FromBase));
      if (address.StartsWith("LTC"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LTC, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_LTC.FromBase));
      if (address.StartsWith("LTN"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LTN, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_LTN.FromBase));
      if (address.StartsWith("LCS"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LCS, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_LCS.FromBase));
      if (address.StartsWith("LCC"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LCC, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_LCC.FromBase));
      if (address.StartsWith("LCN"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_LCN, Convert.ToInt32(address.Substring(3), MelsecMcDataType.R_LCN.FromBase));
      if (address.StartsWith("TS"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_TS, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_TS.FromBase));
      if (address.StartsWith("TC"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_TC, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_TC.FromBase));
      if (address.StartsWith("TN"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_TN, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_TN.FromBase));
      if (address.StartsWith("CS"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_CS, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_CS.FromBase));
      if (address.StartsWith("CC"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_CC, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_CC.FromBase));
      if (address.StartsWith("CN"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_CN, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_CN.FromBase));
      if (address.StartsWith("SM"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_SM, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_SM.FromBase));
      if (address.StartsWith("SB"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_SB, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_SB.FromBase));
      if (address.StartsWith("DX"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_DX, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_DX.FromBase));
      if (address.StartsWith("DY"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_DY, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_DY.FromBase));
      if (address.StartsWith("SD"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_SD, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_SD.FromBase));
      if (address.StartsWith("SW"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_SW, Convert.ToInt32(address.Substring(2), MelsecMcDataType.R_SW.FromBase));
      if (address.StartsWith("X"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_X, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_X.FromBase));
      if (address.StartsWith("Y"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_Y, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_Y.FromBase));
      if (address.StartsWith("M"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_M, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_M.FromBase));
      if (address.StartsWith("L"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_L, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_L.FromBase));
      if (address.StartsWith("F"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_F, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_F.FromBase));
      if (address.StartsWith("V"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_V, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_V.FromBase));
      if (address.StartsWith("S"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_S, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_S.FromBase));
      if (address.StartsWith("B"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_B, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_B.FromBase));
      if (address.StartsWith("D"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_D, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_D.FromBase));
      if (address.StartsWith("W"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_W, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_W.FromBase));
      if (address.StartsWith("R"))
        return OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_R, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_R.FromBase));
      return address.StartsWith("Z") ? OperateResult.CreateSuccessResult<MelsecMcDataType, int>(MelsecMcDataType.R_Z, Convert.ToInt32(address.Substring(1), MelsecMcDataType.R_Z.FromBase)) : new OperateResult<MelsecMcDataType, int>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<MelsecMcDataType, int>(ex.Message);
    }
  }

  /// <summary>从三菱地址，是否位读取进行创建读取的MC的核心报文</summary>
  /// <param name="address">地址数据</param>
  /// <param name="isBit">是否进行了位读取操作</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildReadMcCoreCommand(McAddressData address, bool isBit)
  {
    return new byte[12]
    {
      (byte) 1,
      (byte) 4,
      isBit ? (byte) 3 : (byte) 2,
      (byte) 0,
      BitConverter.GetBytes(address.AddressStart)[0],
      BitConverter.GetBytes(address.AddressStart)[1],
      BitConverter.GetBytes(address.AddressStart)[2],
      BitConverter.GetBytes(address.AddressStart)[3],
      BitConverter.GetBytes(address.McDataType.DataCode)[0],
      BitConverter.GetBytes(address.McDataType.DataCode)[1],
      (byte) ((uint) address.Length % 256U /*0x0100*/),
      (byte) ((uint) address.Length / 256U /*0x0100*/)
    };
  }

  /// <summary>以字为单位，创建数据写入的核心报文</summary>
  /// <param name="address">三菱的数据地址</param>
  /// <param name="value">实际的原始数据信息</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildWriteWordCoreCommand(McAddressData address, byte[] value)
  {
    if (value == null)
      value = new byte[0];
    byte[] numArray = new byte[12 + value.Length];
    numArray[0] = (byte) 1;
    numArray[1] = (byte) 20;
    numArray[2] = (byte) 2;
    numArray[3] = (byte) 0;
    numArray[4] = BitConverter.GetBytes(address.AddressStart)[0];
    numArray[5] = BitConverter.GetBytes(address.AddressStart)[1];
    numArray[6] = BitConverter.GetBytes(address.AddressStart)[2];
    numArray[7] = BitConverter.GetBytes(address.AddressStart)[3];
    numArray[8] = BitConverter.GetBytes(address.McDataType.DataCode)[0];
    numArray[9] = BitConverter.GetBytes(address.McDataType.DataCode)[1];
    numArray[10] = (byte) (value.Length / 2 % 256 /*0x0100*/);
    numArray[11] = (byte) (value.Length / 2 / 256 /*0x0100*/);
    value.CopyTo((Array) numArray, 12);
    return numArray;
  }

  /// <summary>以位为单位，创建数据写入的核心报文</summary>
  /// <param name="address">三菱的地址信息</param>
  /// <param name="value">原始的bool数组数据</param>
  /// <returns>带有成功标识的报文对象</returns>
  public static byte[] BuildWriteBitCoreCommand(McAddressData address, bool[] value)
  {
    if (value == null)
      value = new bool[0];
    byte[] byteData = MelsecHelper.TransBoolArrayToByteData(value);
    byte[] numArray = new byte[12 + byteData.Length];
    numArray[0] = (byte) 1;
    numArray[1] = (byte) 20;
    numArray[2] = (byte) 3;
    numArray[3] = (byte) 0;
    numArray[4] = BitConverter.GetBytes(address.AddressStart)[0];
    numArray[5] = BitConverter.GetBytes(address.AddressStart)[1];
    numArray[6] = BitConverter.GetBytes(address.AddressStart)[2];
    numArray[7] = BitConverter.GetBytes(address.AddressStart)[3];
    numArray[8] = BitConverter.GetBytes(address.McDataType.DataCode)[0];
    numArray[9] = BitConverter.GetBytes(address.McDataType.DataCode)[1];
    numArray[10] = (byte) (value.Length % 256 /*0x0100*/);
    numArray[11] = (byte) (value.Length / 256 /*0x0100*/);
    byteData.CopyTo((Array) numArray, 12);
    return numArray;
  }
}
