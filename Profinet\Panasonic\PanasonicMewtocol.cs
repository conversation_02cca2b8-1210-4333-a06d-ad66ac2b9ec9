﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Panasonic.PanasonicMewtocol
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Panasonic.Helper;
using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Panasonic;

/// <summary>
/// 松下PLC的数据交互协议，采用Mewtocol协议通讯，支持的地址列表参考api文档<br />
/// The data exchange protocol of Panasonic PLC adopts Mewtocol protocol for communication. For the list of supported addresses, refer to the api document.
/// </summary>
/// <remarks>地址支持携带站号的访问方式，例如：s=2;D100</remarks>
public class PanasonicMewtocol : DeviceSerialPort
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.#ctor(System.Byte)" />
  public PanasonicMewtocol(byte station = 238)
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.Station = station;
    this.ByteTransform.DataFormat = DataFormat.DCBA;
    this.WordLength = (ushort) 1;
    this.ReceiveEmptyDataCount = 5;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.Station" />
  public byte Station { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return MewtocolHelper.Read((IReadWriteDevice) this, this.Station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return MewtocolHelper.Write((IReadWriteDevice) this, this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return MewtocolHelper.ReadBool((IReadWriteDevice) this, this.Station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String[])" />
  public OperateResult<bool[]> ReadBool(string[] address)
  {
    return MewtocolHelper.ReadBool((IReadWriteDevice) this, this.Station, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.ReadBool(System.String)" />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    return MewtocolHelper.ReadBool((IReadWriteDevice) this, this.Station, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    return MewtocolHelper.Write((IReadWriteDevice) this, this.Station, address, values);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocolOverTcp.Write(System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return MewtocolHelper.Write((IReadWriteDevice) this, this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String[],System.Boolean[])" />
  public OperateResult Write(string[] address, bool[] value)
  {
    return MewtocolHelper.Write((IReadWriteDevice) this, this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.ReadBool(System.String)" />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<bool> operateResult = await Task.Run<OperateResult<bool>>((Func<OperateResult<bool>>) (() => this.ReadBool(address)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.ReadBool(System.String[])" />
  public async Task<OperateResult<bool[]>> ReadBoolAsync(string[] address)
  {
    OperateResult<bool[]> operateResult = await MewtocolHelper.ReadBoolAsync((IReadWriteDevice) this, this.Station, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.PanasonicMewtocol.Write(System.String[],System.Boolean[])" />
  public async Task<OperateResult> WriteAsync(string[] address, bool[] value)
  {
    OperateResult operateResult = await MewtocolHelper.WriteAsync((IReadWriteDevice) this, this.Station, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Panasonic.Helper.MewtocolHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  public OperateResult<string> ReadPlcType()
  {
    return MewtocolHelper.ReadPlcType((IReadWriteDevice) this, this.Station);
  }

  /// <inheritdoc />
  public override string ToString() => $"PanasonicMewtocol[{this.PortName}:{this.BaudRate}]";
}
