﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT698OverTcp
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Pipe;
using HslCommunication.Instrument.DLT.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>
/// 698.45协议的串口转网口透传通信类(不是TCP通信)，面向对象的用电信息数据交换协议，使用明文的通信方式。支持读取功率，总功，电压，电流，频率，功率因数等数据。<br />
/// 698.45 protocol serial port to network port transparent transmission communication (not TCP communication),
/// object-oriented power consumption information data exchange protocol, using plaintext communication. Support reading power,
/// total power, voltage, current, frequency, power factor and other data.
/// </summary>
/// <remarks>
/// <inheritdoc cref="T:HslCommunication.Instrument.DLT.DLT698" path="remarks" />
/// </remarks>
/// <example>
/// <inheritdoc cref="T:HslCommunication.Instrument.DLT.DLT698" path="example" />
/// /// </example>
public class DLT698OverTcp : DeviceTcpNet, IDlt698, IReadWriteDevice, IReadWriteNet
{
  private string station = "1";

  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.#ctor" />
  public DLT698OverTcp() => this.ByteTransform = (IByteTransform) new ReverseBytesTransform();

  /// <summary>通过指定设备站号来初始化一个通信对象信息</summary>
  /// <param name="station">设备的地址信息，通常是一个12字符的BCD码</param>
  public DLT698OverTcp(string station)
    : this()
  {
    this.station = station;
  }

  /// <summary>通过指定IP地址，端口号，设备站号来初始化一个通信对象信息</summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号信息</param>
  /// <param name="station">设备站号信息</param>
  public DLT698OverTcp(string ipAddress, int port, string station)
    : this(station)
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new DLT698Message();

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return DLT698Helper.PackCommandWithHeader((IDlt698) this, command);
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt698.UseSecurityResquest" />
  public bool UseSecurityResquest { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt698.CA" />
  public byte CA { get; set; } = 0;

  /// <summary>
  /// 获取或设置是否启用服务器主动推送功能，默认为不启用，如果某些设备支持主动推送功能，可以设置为True，然后在事件中接收数据。<br />
  /// Gets or sets whether active push is enabled on the server. By default, it is disabled. If some devices support active push, it can be set to True and then receive data in the event.
  /// </summary>
  public bool IsServerActivePush { get; set; } = false;

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    if (this.IsServerActivePush)
      this.CommunicationPipe.UseServerActivePush = true;
    return base.InitializationOnConnect();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    if (this.IsServerActivePush)
      this.CommunicationPipe.UseServerActivePush = true;
    OperateResult operateResult = await base.InitializationOnConnectAsync();
    return operateResult;
  }

  /// <inheritdoc />
  protected override bool DecideWhetherQAMessage(
    CommunicationPipe pipe,
    OperateResult<byte[]> receive)
  {
    if (this.IsServerActivePush)
    {
      if (!receive.IsSuccess)
        return base.DecideWhetherQAMessage(pipe, receive);
      try
      {
        byte[] content1 = receive.Content;
        ((IEnumerable<byte>) content1.SelectMiddle<byte>(5, ((int) content1[4] & 15) + 1)).Reverse<byte>().ToArray<byte>().ToHexString();
        int leftLength = 5 + ((int) content1[4] & 15) + 1 + 3;
        byte ca = 16 /*0x10*/;
        if (content1[3] == (byte) 129)
        {
          if (content1[leftLength] == (byte) 1)
          {
            if (content1[leftLength + 2] == (byte) 0 || content1[leftLength + 2] == (byte) 1)
            {
              this.LogRevcMessage(receive.Content);
              byte[] content2 = DLT698Helper.BuildEntireCommand((byte) 1, this.Station, ca, DLT698Helper.CreatePreLogin(content1.SelectMiddle<byte>(leftLength + 5, 10))).Content;
              this.LogSendMessage(content2);
              pipe.Send(content2);
              return false;
            }
          }
        }
        else if (content1[3] == (byte) 67)
        {
          byte[] numArray = content1.RemoveDouble<byte>(leftLength, 2);
          if (numArray[0] == (byte) 16 /*0x10*/)
            numArray = numArray.SelectMiddle<byte>(3, (int) numArray[2]);
          if (numArray[0] == (byte) 136)
          {
            this.LogRevcMessage(receive.Content);
            DLT698OverTcp.DataReportingDelegate onDataReporting = this.OnDataReporting;
            if (onDataReporting != null)
              onDataReporting(pipe, receive);
            return false;
          }
        }
      }
      catch
      {
        return base.DecideWhetherQAMessage(pipe, receive);
      }
    }
    return base.DecideWhetherQAMessage(pipe, receive);
  }

  /// <summary>
  /// 数据上报的事件，当设置了<see cref="P:HslCommunication.Instrument.DLT.DLT698OverTcp.IsServerActivePush" />设置为 <c>True</c> 时才会生效。<br />
  /// The data report event takes effect only when <see cref="P:HslCommunication.Instrument.DLT.DLT698OverTcp.IsServerActivePush" /> is set to <c>True</c>.
  /// </summary>
  public event DLT698OverTcp.DataReportingDelegate OnDataReporting;

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.Read(HslCommunication.Instrument.DLT.Helper.IDlt698,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return DLT698Helper.Read((IDlt698) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.Write(HslCommunication.Instrument.DLT.Helper.IDlt698,System.String,System.Byte[])" />
  public override OperateResult Write(string address, byte[] value)
  {
    return DLT698Helper.Write((IDlt698) this, address, value);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return DLT698Helper.ReadBool(this.ReadStringArray(address), length);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> command = DLT698Helper.BuildReadSingleObject(address, this.station, (IDlt698) this);
    if (!command.IsSuccess)
      return command;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(command.Content).ConfigureAwait(false);
    return read.IsSuccess ? DLT698Helper.CheckResponse(read.Content) : read;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<string[]> read = await this.ReadStringArrayAsync(address);
    return DLT698Helper.ReadBool(read, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> build = DLT698Helper.BuildWriteSingleObject(address, this.station, value, (IDlt698) this);
    if (!build.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content).ConfigureAwait(false);
    return read.IsSuccess ? (OperateResult) DLT698Helper.CheckResponse(read.Content) : (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) read);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.ReadByApdu(HslCommunication.Instrument.DLT.Helper.IDlt698,System.Byte[])" />
  public OperateResult<byte[]> ReadByApdu(byte[] apdu)
  {
    return DLT698Helper.ReadByApdu((IDlt698) this, apdu);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.ActiveDeveice(HslCommunication.Instrument.DLT.Helper.IDlt698)" />
  public OperateResult ActiveDeveice() => DLT698Helper.ActiveDeveice((IDlt698) this);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.ReadStringArray(HslCommunication.Instrument.DLT.Helper.IDlt698,System.String)" />
  public OperateResult<string[]> ReadStringArray(string address)
  {
    return DLT698Helper.ReadStringArray((IDlt698) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.ReadStringArray(HslCommunication.Instrument.DLT.Helper.IDlt698,System.String[])" />
  public OperateResult<string[]> ReadStringArray(string[] address)
  {
    return DLT698Helper.ReadStringArray((IDlt698) this, address);
  }

  private OperateResult<T[]> ReadDataAndParse<T>(
    string address,
    ushort length,
    Func<string, T> trans)
  {
    return DLT698Helper.ReadDataAndParse<T>(this.ReadStringArray(address), length, trans);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.ReadAddress(HslCommunication.Instrument.DLT.Helper.IDlt698)" />
  public OperateResult<string> ReadAddress() => DLT698Helper.ReadAddress((IDlt698) this);

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.WriteAddress(HslCommunication.Instrument.DLT.Helper.IDlt698,System.String)" />
  public OperateResult WriteAddress(string address)
  {
    return DLT698Helper.WriteAddress((IDlt698) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.Helper.DLT698Helper.WriteDateTime(HslCommunication.Instrument.DLT.Helper.IDlt698,System.String,System.DateTime)" />
  public OperateResult WriteDateTime(string address, DateTime time)
  {
    return DLT698Helper.WriteDateTime((IDlt698) this, address, time);
  }

  private async Task<OperateResult<T[]>> ReadDataAndParseAsync<T>(
    string address,
    ushort length,
    Func<string, T> trans)
  {
    OperateResult<string[]> read = await this.ReadStringArrayAsync(address);
    return DLT698Helper.ReadDataAndParse<T>(read, length, trans);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.ReadByApdu(System.Byte[])" />
  public async Task<OperateResult<byte[]>> ReadByApduAsync(byte[] apdu)
  {
    OperateResult<byte[]> operateResult = await DLT698Helper.ReadByApduAsync((IDlt698) this, apdu);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.ActiveDeveice" />
  public async Task<OperateResult> ActiveDeveiceAsync()
  {
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(new byte[4]
    {
      (byte) 254,
      (byte) 254,
      (byte) 254,
      (byte) 254
    }, false, true);
    return (OperateResult) operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.ReadStringArray(System.String)" />
  public async Task<OperateResult<string[]>> ReadStringArrayAsync(string address)
  {
    OperateResult<byte[]> read = await this.ReadAsync(address, (ushort) 1).ConfigureAwait(false);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>((OperateResult) read);
    int index = 8;
    return OperateResult.CreateSuccessResult<string[]>(DLT698Helper.ExtraStringsValues(this.ByteTransform, read.Content, ref index));
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.ReadAddress" />
  public async Task<OperateResult<string>> ReadAddressAsync()
  {
    OperateResult<byte[]> build = DLT698Helper.BuildReadSingleObject("40-01-02-00", "AAAAAAAAAAAA", (IDlt698) this);
    if (!build.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    OperateResult<byte[]> extra = DLT698Helper.CheckResponse(read.Content);
    if (!extra.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) extra);
    this.station = extra.Content.SelectMiddle<byte>(10, (int) extra.Content[9]).ToHexString();
    return OperateResult.CreateSuccessResult<string>(this.station);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.WriteAddress(System.String)" />
  public async Task<OperateResult> WriteAddressAsync(string address)
  {
    OperateResult<byte[]> build = DLT698Helper.BuildWriteSingleObject("40-01-02-00", "AAAAAAAAAAAA", DLT698Helper.CreateStringValueBuffer(address), (IDlt698) this);
    if (!build.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) build);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(build.Content);
    return read.IsSuccess ? (OperateResult) DLT698Helper.CheckResponse(read.Content) : (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) read);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt16Array", "")]
  public override OperateResult<short[]> ReadInt16(string address, ushort length)
  {
    return this.ReadDataAndParse<short>(address, length, new Func<string, short>(short.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt16Array", "")]
  public override OperateResult<ushort[]> ReadUInt16(string address, ushort length)
  {
    return this.ReadDataAndParse<ushort>(address, length, new Func<string, ushort>(ushort.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt32Array", "")]
  public override OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    return this.ReadDataAndParse<int>(address, length, new Func<string, int>(int.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt32Array", "")]
  public override OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    return this.ReadDataAndParse<uint>(address, length, new Func<string, uint>(uint.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadInt64Array", "")]
  public override OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    return this.ReadDataAndParse<long>(address, length, new Func<string, long>(long.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadUInt64Array", "")]
  public override OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    return this.ReadDataAndParse<ulong>(address, length, new Func<string, ulong>(ulong.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadFloatArray", "")]
  public override OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    return this.ReadDataAndParse<float>(address, length, new Func<string, float>(float.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return this.ReadDataAndParse<double>(address, length, new Func<string, double>(double.Parse));
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromArray<string>(this.ReadStringArray(address));
  }

  /// <inheritdoc />
  public override async Task<OperateResult<short[]>> ReadInt16Async(string address, ushort length)
  {
    OperateResult<short[]> async = await this.ReadDataAndParseAsync<short>(address, length, new Func<string, short>(short.Parse));
    return async;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ushort[]>> ReadUInt16Async(string address, ushort length)
  {
    OperateResult<ushort[]> async = await this.ReadDataAndParseAsync<ushort>(address, length, new Func<string, ushort>(ushort.Parse));
    return async;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    OperateResult<int[]> async = await this.ReadDataAndParseAsync<int>(address, length, new Func<string, int>(int.Parse));
    return async;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    OperateResult<uint[]> async = await this.ReadDataAndParseAsync<uint>(address, length, new Func<string, uint>(uint.Parse));
    return async;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    OperateResult<long[]> async = await this.ReadDataAndParseAsync<long>(address, length, new Func<string, long>(long.Parse));
    return async;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    OperateResult<ulong[]> async = await this.ReadDataAndParseAsync<ulong>(address, length, new Func<string, ulong>(ulong.Parse));
    return async;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    OperateResult<float[]> async = await this.ReadDataAndParseAsync<float>(address, length, new Func<string, float>(float.Parse));
    return async;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.ReadDouble(System.String,System.UInt16)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<double[]> async = await this.ReadDataAndParseAsync<double>(address, length, new Func<string, double>(double.Parse));
    return async;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<string[]> result = await this.ReadStringArrayAsync(address);
    return ByteTransformHelper.GetResultFromArray<string>(result);
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.DLT698.Station" />
  public string Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.DLT645.EnableCodeFE" />
  public bool EnableCodeFE { get; set; }

  /// <inheritdoc />
  public override string ToString() => $"DLT698OverTcp[{this.IpAddress}:{this.Port}]";

  /// <summary>数据上报时的方法委托</summary>
  /// <param name="pipe">管道信息</param>
  /// <param name="receive">收到的二进制报文</param>
  public delegate void DataReportingDelegate(CommunicationPipe pipe, OperateResult<byte[]> receive);
}
