﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.HslReadWriteLock
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Diagnostics;
using System.Globalization;
using System.Threading;

#nullable disable
namespace HslCommunication.Core;

/// <summary>一个高性能的读写锁，支持写锁定，读灵活，读时写锁定，写时读锁定</summary>
public sealed class HslReadWriteLock : IDisposable
{
  private const int c_lsStateStartBit = 0;
  private const int c_lsReadersReadingStartBit = 3;
  private const int c_lsReadersWaitingStartBit = 12;
  private const int c_lsWritersWaitingStartBit = 21;
  private const int c_lsStateMask = 7;
  private const int c_lsReadersReadingMask = 4088;
  private const int c_lsReadersWaitingMask = 2093056;
  private const int c_lsWritersWaitingMask = ********** /*0x3FE00000*/;
  private const int c_lsAnyWaitingMask = **********;
  private const int c_ls1ReaderReading = 8;
  private const int c_ls1ReaderWaiting = 4096 /*0x1000*/;
  private const int c_ls1WriterWaiting = 2097152 /*0x200000*/;
  private int m_LockState = 0;
  private Semaphore m_ReadersLock = new Semaphore(0, int.MaxValue);
  private Semaphore m_WritersLock = new Semaphore(0, int.MaxValue);
  private bool disposedValue = false;
  private bool m_exclusive;

  private static HslReadWriteLock.OneManyLockStates State(int ls)
  {
    return (HslReadWriteLock.OneManyLockStates) (ls & 7);
  }

  private static void SetState(ref int ls, HslReadWriteLock.OneManyLockStates newState)
  {
    ls = (int) ((HslReadWriteLock.OneManyLockStates) (ls & -8) | newState);
  }

  private static int NumReadersReading(int ls) => (ls & 4088) >> 3;

  private static void AddReadersReading(ref int ls, int amount) => ls += 8 * amount;

  private static int NumReadersWaiting(int ls) => (ls & 2093056) >> 12;

  private static void AddReadersWaiting(ref int ls, int amount) => ls += 4096 /*0x1000*/ * amount;

  private static int NumWritersWaiting(int ls) => (ls & ********** /*0x3FE00000*/) >> 21;

  private static void AddWritersWaiting(ref int ls, int amount)
  {
    ls += 2097152 /*0x200000*/ * amount;
  }

  private static bool AnyWaiters(int ls) => (ls & **********) != 0;

  private static string DebugState(int ls)
  {
    return string.Format((IFormatProvider) CultureInfo.InvariantCulture, "State={0}, RR={1}, RW={2}, WW={3}", (object) HslReadWriteLock.State(ls), (object) HslReadWriteLock.NumReadersReading(ls), (object) HslReadWriteLock.NumReadersWaiting(ls), (object) HslReadWriteLock.NumWritersWaiting(ls));
  }

  /// <summary>返回本对象的描述字符串</summary>
  /// <returns>对象的描述字符串</returns>
  public override string ToString() => HslReadWriteLock.DebugState(this.m_LockState);

  private void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (!disposing)
      ;
    this.m_WritersLock.Close();
    this.m_WritersLock = (Semaphore) null;
    this.m_ReadersLock.Close();
    this.m_ReadersLock = (Semaphore) null;
    this.disposedValue = true;
  }

  /// <summary>释放资源</summary>
  public void Dispose() => this.Dispose(true);

  /// <summary>根据读写情况请求锁</summary>
  /// <param name="exclusive">True为写请求，False为读请求</param>
  public void Enter(bool exclusive)
  {
    if (exclusive)
    {
      while (HslReadWriteLock.WaitToWrite(ref this.m_LockState))
        this.m_WritersLock.WaitOne();
    }
    else
    {
      while (HslReadWriteLock.WaitToRead(ref this.m_LockState))
        this.m_ReadersLock.WaitOne();
    }
    this.m_exclusive = exclusive;
  }

  private static bool WaitToWrite(ref int target)
  {
    int num = target;
    int comparand;
    bool write;
    do
    {
      comparand = num;
      int ls = comparand;
      write = false;
      switch (HslReadWriteLock.State(ls))
      {
        case HslReadWriteLock.OneManyLockStates.Free:
        case HslReadWriteLock.OneManyLockStates.ReservedForWriter:
          HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.OwnedByWriter);
          break;
        case HslReadWriteLock.OneManyLockStates.OwnedByWriter:
          HslReadWriteLock.AddWritersWaiting(ref ls, 1);
          write = true;
          break;
        case HslReadWriteLock.OneManyLockStates.OwnedByReaders:
        case HslReadWriteLock.OneManyLockStates.OwnedByReadersAndWriterPending:
          HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.OwnedByReadersAndWriterPending);
          HslReadWriteLock.AddWritersWaiting(ref ls, 1);
          write = true;
          break;
        default:
          Debug.Assert(false, "Invalid Lock state");
          break;
      }
      num = Interlocked.CompareExchange(ref target, ls, comparand);
    }
    while (comparand != num);
    return write;
  }

  /// <summary>释放锁，将根据锁状态自动区分读写锁</summary>
  public void Leave()
  {
    int releaseCount;
    if (this.m_exclusive)
    {
      Debug.Assert(HslReadWriteLock.State(this.m_LockState) == HslReadWriteLock.OneManyLockStates.OwnedByWriter && HslReadWriteLock.NumReadersReading(this.m_LockState) == 0);
      releaseCount = HslReadWriteLock.DoneWriting(ref this.m_LockState);
    }
    else
    {
      HslReadWriteLock.State(this.m_LockState);
      Debug.Assert(HslReadWriteLock.State(this.m_LockState) == HslReadWriteLock.OneManyLockStates.OwnedByReaders || HslReadWriteLock.State(this.m_LockState) == HslReadWriteLock.OneManyLockStates.OwnedByReadersAndWriterPending);
      releaseCount = HslReadWriteLock.DoneReading(ref this.m_LockState);
    }
    if (releaseCount == -1)
    {
      this.m_WritersLock.Release();
    }
    else
    {
      if (releaseCount <= 0)
        return;
      this.m_ReadersLock.Release(releaseCount);
    }
  }

  private static int DoneWriting(ref int target)
  {
    int num1 = target;
    int comparand;
    int num2;
    do
    {
      int ls = comparand = num1;
      if (!HslReadWriteLock.AnyWaiters(ls))
      {
        HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.Free);
        num2 = 0;
      }
      else if (HslReadWriteLock.NumWritersWaiting(ls) > 0)
      {
        HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.ReservedForWriter);
        HslReadWriteLock.AddWritersWaiting(ref ls, -1);
        num2 = -1;
      }
      else
      {
        num2 = HslReadWriteLock.NumReadersWaiting(ls);
        Debug.Assert(num2 > 0);
        HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.OwnedByReaders);
        HslReadWriteLock.AddReadersWaiting(ref ls, -num2);
      }
      num1 = Interlocked.CompareExchange(ref target, ls, comparand);
    }
    while (comparand != num1);
    return num2;
  }

  private static bool WaitToRead(ref int target)
  {
    int num = target;
    int comparand;
    bool read;
    do
    {
      int ls = comparand = num;
      read = false;
      switch (HslReadWriteLock.State(ls))
      {
        case HslReadWriteLock.OneManyLockStates.Free:
          HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.OwnedByReaders);
          HslReadWriteLock.AddReadersReading(ref ls, 1);
          break;
        case HslReadWriteLock.OneManyLockStates.OwnedByWriter:
        case HslReadWriteLock.OneManyLockStates.OwnedByReadersAndWriterPending:
        case HslReadWriteLock.OneManyLockStates.ReservedForWriter:
          HslReadWriteLock.AddReadersWaiting(ref ls, 1);
          read = true;
          break;
        case HslReadWriteLock.OneManyLockStates.OwnedByReaders:
          HslReadWriteLock.AddReadersReading(ref ls, 1);
          break;
        default:
          Debug.Assert(false, "Invalid Lock state");
          break;
      }
      num = Interlocked.CompareExchange(ref target, ls, comparand);
    }
    while (comparand != num);
    return read;
  }

  private static int DoneReading(ref int target)
  {
    int num1 = target;
    int comparand;
    int num2;
    do
    {
      int ls = comparand = num1;
      HslReadWriteLock.AddReadersReading(ref ls, -1);
      if (HslReadWriteLock.NumReadersReading(ls) > 0)
        num2 = 0;
      else if (!HslReadWriteLock.AnyWaiters(ls))
      {
        HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.Free);
        num2 = 0;
      }
      else
      {
        Debug.Assert(HslReadWriteLock.NumWritersWaiting(ls) > 0);
        HslReadWriteLock.SetState(ref ls, HslReadWriteLock.OneManyLockStates.ReservedForWriter);
        HslReadWriteLock.AddWritersWaiting(ref ls, -1);
        num2 = -1;
      }
      num1 = Interlocked.CompareExchange(ref target, ls, comparand);
    }
    while (comparand != num1);
    return num2;
  }

  private enum OneManyLockStates
  {
    Free,
    OwnedByWriter,
    OwnedByReaders,
    OwnedByReadersAndWriterPending,
    ReservedForWriter,
  }
}
