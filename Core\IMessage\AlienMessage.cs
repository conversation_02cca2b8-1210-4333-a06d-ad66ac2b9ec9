﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.AlienMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>异形消息对象，用于异形客户端的注册包接收以及验证使用</summary>
public class AlienMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 5;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes == null || this.HeadBytes[0] == (byte) 72;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    return this.HeadBytes[3] >= (byte) 48 /*0x30*/ && this.HeadBytes[3] <= (byte) 70 && this.HeadBytes[4] >= (byte) 48 /*0x30*/ && this.HeadBytes[4] <= (byte) 70 ? Convert.ToInt32(Encoding.ASCII.GetString(this.HeadBytes, 3, 2), 16 /*0x10*/) : (int) this.HeadBytes[3] * 256 /*0x0100*/ + (int) this.HeadBytes[4];
  }
}
