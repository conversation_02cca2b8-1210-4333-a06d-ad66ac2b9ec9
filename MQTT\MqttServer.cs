﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Core.Security;
using HslCommunication.LogNet;
using HslCommunication.Reflection;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// 一个Mqtt的服务器类对象，本服务器支持发布订阅操作，支持从服务器强制推送数据，支持往指定的客户端推送，支持基于一问一答的远程过程调用（RPC）的数据交互，支持文件上传下载。根据这些功能从而定制化出满足各个场景的服务器，详细的使用说明可以参见代码api文档示例。<br />
/// An Mqtt server class object. This server supports publish and subscribe operations, supports forced push data from the server,
/// supports push to designated clients, supports data interaction based on one-question-one-answer remote procedure calls (RPC),
/// and supports file upload and download . According to these functions, the server can be customized to meet various scenarios.
/// For detailed instructions, please refer to the code api document example.
/// </summary>
/// <remarks>
/// 本MQTT服务器功能丰富，可以同时实现，用户名密码验证，在线客户端的管理，数据订阅推送，单纯的数据收发，心跳检测，订阅通配符，同步数据访问，文件上传，下载，删除，遍历，详细参照下面的示例说明<br />
/// 通配符请查看<see cref="P:HslCommunication.MQTT.MqttServer.TopicWildcard" />属性，规则参考：http://public.dhe.ibm.com/software/dw/webservices/ws-mqtt/mqtt-v3r1.html#appendix-a
/// </remarks>
/// <example>
/// 最简单的使用，就是实例化，启动服务即可
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample1" title="简单的实例化" />
/// 当然了，我们可以稍微的复杂一点，加一个功能，验证连接的客户端操作
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample2" title="增加验证" />
/// 我们可以对ClientID，用户名，密码进行验证，那么我们可以动态修改client id么？比如用户名密码验证成功后，client ID我想设置为权限等级。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample2_1" title="动态修改Client ID" />
/// 如果我想强制该客户端不能主动发布主题，可以这么操作。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample2_2" title="禁止发布主题" />
/// 你也可以对clientid进行过滤验证，只要结果返回不是0，就可以了。接下来我们实现一个功能，所有客户端的发布的消息在控制台打印出来,
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample3" title="打印所有发布" />
/// 捕获客户端刚刚上线的时候，方便我们进行一些额外的操作信息。下面的意思就是返回一个数据，将数据发送到指定的会话内容上去
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample4" title="客户端上线信息" />
/// 下面演示如何从服务器端发布数据信息，包括多种发布的方法，消息是否驻留，详细看说明即可
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample5" title="服务器发布" />
/// 下面演示如何支持同步网络访问，当客户端是同步网络访问时，协议内容会变成HUSL，即被视为同步客户端，进行相关的操作，主要进行远程调用RPC，以及查询MQTT的主题列表。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample6" title="同步访问支持" />
/// 如果需要查看在线信息，可以随时获取<see cref="P:HslCommunication.MQTT.MqttServer.OnlineCount" />属性，如果需要查看报文信息，可以实例化日志，参考日志的说明即可。<br /><br />
/// 针对上面同步网络访问，虽然比较灵活，但是什么都要自己控制，无疑增加了代码的复杂度，举个例子，当你的topic分类很多的时候，已经客户端协议多个参数的时候，需要大量的手动解析的代码，
/// 影响代码美观，而且让代码更加的杂乱，除此之外，还有个巨大的麻烦，服务器提供了很多的topic处理程序（可以换个称呼，暴露的API接口），
/// 客户端没法清晰的浏览到，需要查找服务器代码才能知晓，而且服务器更新了接口，客户端有需要同步查看服务器的代码才行，以及做权限控制也很麻烦。<br />
/// 所以在Hsl里面的MQTT服务器，提供了注册API接口的功能，只需要一行注册代码，你的类的方法自动就会变为API解析，所有的参数都是同步解析的，如果你返回的是
/// OperateResult&lt;T&gt;类型对象，还支持是否成功的结果报告，否则一律视为json字符串，返回给调用方。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample7" title="基于MQTT的RPC接口实现" />
/// 如果需要查看在线信息，可以随时获取<see cref="P:HslCommunication.MQTT.MqttServer.OnlineCount" />属性，如果需要查看报文信息，可以实例化日志，参考日志的说明即可。<br /><br />
/// 最后介绍一下文件管理服务是如何启动的，在启动了文件管理服务之后，其匹配的客户端 <see cref="T:HslCommunication.MQTT.MqttSyncClient" /> 就可以上传下载，遍历文件了。
/// 而服务器端做的就是启用服务，如果你需要一些更加自由的权限控制，比如某个账户只能下载，不能其他操作，都是可以实现的。更加多的示例参考DEMO程序。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\MQTT\MqttServerSample.cs" region="Sample8" title="基于MQTT的文件管理服务启动" />
/// </example>
public class MqttServer : CommunicationTcpServer, IDisposable
{
  private Dictionary<string, MqttRpcApiInfo> apiTopicServiceDict;
  private object rpcApiLock;
  private readonly Dictionary<string, FileMarkId> dictionaryFilesMarks;
  private readonly object dictHybirdLock;
  private string filesDirectoryPath = (string) null;
  private bool fileServerEnabled = false;
  private Dictionary<string, GroupFileContainer> m_dictionary_group_marks = new Dictionary<string, GroupFileContainer>();
  private SimpleHybirdLock group_marks_lock = new SimpleHybirdLock();
  private MqttFileMonitor fileMonitor = new MqttFileMonitor();
  private readonly Dictionary<string, MqttClientApplicationMessage> retainKeys;
  private readonly object keysLock;
  private readonly List<MqttSession> mqttSessions = new List<MqttSession>();
  private readonly object sessionsLock = new object();
  private System.Threading.Timer timerHeart;
  private LogStatisticsDict statisticsDict;
  private bool disposedValue;
  private RSACryptoServiceProvider providerMqttServer = (RSACryptoServiceProvider) null;
  private bool topicWildcard = false;
  private HslCommunication.LogNet.LogStatistics topicStatistics;
  private AsyncCallback beginReceiveCallback;

  /// <summary>
  /// 实例化一个MQTT协议的服务器<br />
  /// Instantiate a MQTT protocol server
  /// </summary>
  /// <param name="providerServer">
  /// RSA秘钥对象，默认为空，表示使用随机生成的秘钥信息，如果需要自定义的RSA密钥，则需要实例化当前参数对象，具体通信的时候加密与否，取决于客户端连接的方式。<br />
  /// The RSA key object, which is empty by default, indicates that the randomly generated key information is used. If a custom RSA key is required,
  /// the current parameter object needs to be instantiated. Whether the specific communication is encrypted or not depends on the way the client connects. .
  /// </param>
  public MqttServer(RSACryptoServiceProvider providerServer = null)
  {
    this.beginReceiveCallback = new AsyncCallback(this.SocketReceiveCallback);
    this.statisticsDict = new LogStatisticsDict(GenerateMode.ByEveryDay, 60);
    this.retainKeys = new Dictionary<string, MqttClientApplicationMessage>();
    this.apiTopicServiceDict = new Dictionary<string, MqttRpcApiInfo>();
    this.keysLock = new object();
    this.rpcApiLock = new object();
    this.timerHeart = new System.Threading.Timer(new TimerCallback(this.ThreadTimerHeartCheck), (object) null, 2000, 10000);
    this.dictionaryFilesMarks = new Dictionary<string, FileMarkId>();
    this.dictHybirdLock = new object();
    this.providerMqttServer = providerServer;
    this.topicStatistics = new HslCommunication.LogNet.LogStatistics(GenerateMode.ByEveryDay, 30);
  }

  /// <inheritdoc />
  protected override async void ThreadPoolLogin(PipeTcpNet pipe, IPEndPoint endPoint)
  {
    OperateResult<byte, byte[]> readMqtt = await MqttHelper.ReceiveMqttMessageAsync((CommunicationPipe) pipe, 10000);
    RSACryptoServiceProvider clientKey;
    RSACryptoServiceProvider serverKey;
    if (!readMqtt.IsSuccess)
    {
      readMqtt = (OperateResult<byte, byte[]>) null;
      clientKey = (RSACryptoServiceProvider) null;
      serverKey = (RSACryptoServiceProvider) null;
    }
    else
    {
      clientKey = (RSACryptoServiceProvider) null;
      serverKey = (RSACryptoServiceProvider) null;
      if (readMqtt.Content1 == byte.MaxValue)
      {
        try
        {
          serverKey = this.providerMqttServer ?? new RSACryptoServiceProvider();
          clientKey = RSAHelper.CreateRsaProviderFromPublicKey(HslSecurity.ByteDecrypt(readMqtt.Content2));
          OperateResult send = pipe.Send(MqttHelper.BuildMqttCommand(byte.MaxValue, (byte[]) null, HslSecurity.ByteEncrypt(clientKey.EncryptLargeData(serverKey.GetPEMPublicKey()))).Content);
          if (!send.IsSuccess)
          {
            readMqtt = (OperateResult<byte, byte[]>) null;
            clientKey = (RSACryptoServiceProvider) null;
            serverKey = (RSACryptoServiceProvider) null;
            return;
          }
          send = (OperateResult) null;
        }
        catch (Exception ex)
        {
          this.LogNet?.WriteError("创建客户端的公钥发生了异常！" + ex.Message);
          PipeTcpNet pipeTcpNet = pipe;
          if (pipeTcpNet == null)
          {
            readMqtt = (OperateResult<byte, byte[]>) null;
            clientKey = (RSACryptoServiceProvider) null;
            serverKey = (RSACryptoServiceProvider) null;
            return;
          }
          pipeTcpNet.CloseCommunication();
          readMqtt = (OperateResult<byte, byte[]>) null;
          clientKey = (RSACryptoServiceProvider) null;
          serverKey = (RSACryptoServiceProvider) null;
          return;
        }
        readMqtt = await MqttHelper.ReceiveMqttMessageAsync((CommunicationPipe) pipe, 10000);
        if (!readMqtt.IsSuccess)
        {
          readMqtt = (OperateResult<byte, byte[]>) null;
          clientKey = (RSACryptoServiceProvider) null;
          serverKey = (RSACryptoServiceProvider) null;
          return;
        }
      }
      this.HandleMqttConnection(pipe, endPoint, readMqtt, clientKey, serverKey);
      readMqtt = (OperateResult<byte, byte[]>) null;
      clientKey = (RSACryptoServiceProvider) null;
      serverKey = (RSACryptoServiceProvider) null;
    }
  }

  private async void SocketReceiveCallback(IAsyncResult ar)
  {
    MqttSession mqttSession = ar.AsyncState as MqttSession;
    if (mqttSession == null)
      ;
    else
    {
      try
      {
        mqttSession.MqttPipe.Socket.EndReceive(ar);
      }
      catch (Exception ex)
      {
        this.RemoveAndCloseSession(mqttSession, "Socket EndReceive -> " + ex.Message);
        return;
      }
      if (mqttSession.Protocol == "FILE")
      {
        if (this.fileServerEnabled)
          await this.HandleFileMessageAsync(mqttSession);
        this.RemoveAndCloseSession(mqttSession, string.Empty);
      }
      else
      {
        OperateResult<byte, byte[]> readMqtt = (OperateResult<byte, byte[]>) null;
        if (mqttSession.Protocol == "MQTT")
          readMqtt = await MqttHelper.ReceiveMqttMessageAsync((CommunicationPipe) mqttSession.MqttPipe, 60000);
        else
          readMqtt = await MqttHelper.ReceiveMqttMessageAsync((CommunicationPipe) mqttSession.MqttPipe, 60000, (Action<long, long>) ((already, total) => this.SyncMqttReceiveProgressBack(mqttSession.MqttPipe, already, total)));
        await this.HandleWithReceiveMqtt(mqttSession, readMqtt);
        readMqtt = (OperateResult<byte, byte[]>) null;
      }
    }
  }

  private void SyncMqttReceiveProgressBack(PipeTcpNet pipe, long already, long total)
  {
    string message = total > 0L ? (already * 100L / total).ToString() : "100";
    byte[] payLoad = new byte[16 /*0x10*/];
    BitConverter.GetBytes(already).CopyTo((Array) payLoad, 0);
    BitConverter.GetBytes(total).CopyTo((Array) payLoad, 8);
    pipe.Send(MqttHelper.BuildMqttCommand((byte) 15, (byte) 0, MqttHelper.BuildSegCommandByString(message), payLoad).Content);
  }

  private void HandleMqttConnection(
    PipeTcpNet pipe,
    IPEndPoint endPoint,
    OperateResult<byte, byte[]> readMqtt,
    RSACryptoServiceProvider providerClient,
    RSACryptoServiceProvider providerServer)
  {
    if (!readMqtt.IsSuccess)
      return;
    byte[] numArray = readMqtt.Content2;
    if (providerClient != null)
    {
      try
      {
        numArray = providerServer.DecryptLargeData(numArray);
      }
      catch (Exception ex)
      {
        this.LogNet?.WriteError(this.ToString(), $"[{endPoint}] Decrypt the client's logon data exception！" + ex.Message);
        pipe?.CloseCommunication();
        return;
      }
    }
    OperateResult<int, MqttSession> operateResult = this.CheckMqttConnection(readMqtt.Content1, numArray, pipe, endPoint);
    if (!operateResult.IsSuccess)
    {
      this.LogNet?.WriteInfo(this.ToString(), $"[{endPoint}] Check client login failure: " + operateResult.Message);
      pipe?.CloseCommunication();
    }
    else if (operateResult.Content1 != 0)
    {
      pipe.Send(MqttHelper.BuildMqttCommand((byte) 2, (byte) 0, (byte[]) null, new byte[2]
      {
        (byte) 0,
        (byte) operateResult.Content1
      }).Content);
      pipe?.CloseCommunication();
    }
    else
    {
      if (providerClient == null)
      {
        pipe.Send(MqttHelper.BuildMqttCommand((byte) 2, (byte) 0, (byte[]) null, new byte[2]).Content);
      }
      else
      {
        operateResult.Content2.AesCryptography = new AesCryptography(HslHelper.HslRandom.GetBytes(16 /*0x10*/).ToHexString());
        byte[] payLoad = providerClient.Encrypt(Encoding.UTF8.GetBytes(operateResult.Content2.AesCryptography.Key), false);
        pipe.Send(MqttHelper.BuildMqttCommand((byte) 2, (byte) 0, new byte[2], payLoad).Content);
      }
      try
      {
        pipe.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) operateResult.Content2);
        this.AddMqttSession(operateResult.Content2);
      }
      catch (Exception ex)
      {
        this.LogNet?.WriteDebug(this.ToString(), "Client Online Exception : " + ex.Message);
        return;
      }
      if (!(operateResult.Content2.Protocol == "MQTT"))
        return;
      MqttServer.OnClientConnectedDelegate onClientConnected = this.OnClientConnected;
      if (onClientConnected == null)
        return;
      onClientConnected(operateResult.Content2);
    }
  }

  private OperateResult<int, MqttSession> CheckMqttConnection(
    byte mqttCode,
    byte[] content,
    PipeTcpNet pipe,
    IPEndPoint endPoint)
  {
    if ((int) mqttCode >> 4 != 1)
      return new OperateResult<int, MqttSession>("Client Send Faied, And Close!");
    if (content.Length < 10)
      return new OperateResult<int, MqttSession>("Receive Data Too Short:" + SoftBasic.ByteToHexString(content, ' '));
    string protocol = Encoding.ASCII.GetString(content, 2, 4);
    if (!(protocol == "MQTT") && !(protocol == "HUSL") && !(protocol == "FILE"))
      return new OperateResult<int, MqttSession>("Not Mqtt Client Connection");
    try
    {
      int index = 10;
      string clientId = MqttHelper.ExtraMsgFromBytes(content, ref index);
      string str = ((int) content[7] & 4) == 4 ? MqttHelper.ExtraMsgFromBytes(content, ref index) : string.Empty;
      string s = ((int) content[7] & 4) == 4 ? MqttHelper.ExtraMsgFromBytes(content, ref index) : string.Empty;
      string userName = ((int) content[7] & 128 /*0x80*/) == 128 /*0x80*/ ? MqttHelper.ExtraMsgFromBytes(content, ref index) : string.Empty;
      string password = ((int) content[7] & 64 /*0x40*/) == 64 /*0x40*/ ? MqttHelper.ExtraMsgFromBytes(content, ref index) : string.Empty;
      int num1 = (int) content[8] * 256 /*0x0100*/ + (int) content[9];
      MqttSession mqttSession = new MqttSession(endPoint, protocol)
      {
        MqttPipe = pipe,
        ClientId = clientId,
        UserName = userName,
        WillTopic = str,
        WillMessage = Encoding.UTF8.GetBytes(s)
      };
      if (protocol == "MQTT")
        mqttSession.DeveloperPermissions = false;
      else if (string.Equals(mqttSession.UserName, "admin", StringComparison.OrdinalIgnoreCase))
        mqttSession.DeveloperPermissions = true;
      int num2 = this.ClientVerification != null ? this.ClientVerification(mqttSession, clientId, userName, password) : 0;
      if (num1 > 0)
        mqttSession.ActiveTimeSpan = TimeSpan.FromSeconds((double) num1 * 1.5);
      return OperateResult.CreateSuccessResult<int, MqttSession>(num2, mqttSession);
    }
    catch (Exception ex)
    {
      return new OperateResult<int, MqttSession>("Client Online Exception : " + ex.Message);
    }
  }

  private async Task HandleWithReceiveMqtt(
    MqttSession mqttSession,
    OperateResult<byte, byte[]> readMqtt)
  {
    byte[] data;
    if (!readMqtt.IsSuccess)
    {
      this.RemoveAndCloseSession(mqttSession, readMqtt.Message);
      data = (byte[]) null;
    }
    else
    {
      byte code = readMqtt.Content1;
      data = readMqtt.Content2;
      try
      {
        if ((int) code >> 4 != 14)
        {
          mqttSession.MqttPipe.Socket.BeginReceive(new byte[0], 0, 0, SocketFlags.None, this.beginReceiveCallback, (object) mqttSession);
        }
        else
        {
          this.RemoveAndCloseSession(mqttSession, string.Empty);
          data = (byte[]) null;
          return;
        }
      }
      catch (Exception ex)
      {
        this.RemoveAndCloseSession(mqttSession, "HandleWithReceiveMqtt exception:" + ex.Message);
        data = (byte[]) null;
        return;
      }
      mqttSession.ActiveTime = DateTime.Now;
      if (mqttSession.Protocol != "MQTT")
      {
        await this.DealWithPublish(mqttSession, code, data);
        data = (byte[]) null;
      }
      else
      {
        if ((int) code >> 4 == 3)
          await this.DealWithPublish(mqttSession, code, data);
        else if ((int) code >> 4 != 4 && (int) code >> 4 != 5)
        {
          if ((int) code >> 4 == 6)
            mqttSession.MqttPipe.Send(MqttHelper.BuildMqttCommand((byte) 7, (byte) 0, (byte[]) null, data).Content);
          else if ((int) code >> 4 == 8)
            this.DealWithSubscribe(mqttSession, code, data);
          else if ((int) code >> 4 == 10)
            this.DealWithUnSubscribe(mqttSession, code, data);
          else if ((int) code >> 4 == 12)
            mqttSession.MqttPipe.Send(MqttHelper.BuildMqttCommand((byte) 13, (byte) 0, (byte[]) null, (byte[]) null).Content);
        }
        data = (byte[]) null;
      }
    }
  }

  /// <inheritdoc />
  protected override void ExtraOnStart() => base.ExtraOnStart();

  /// <inheritdoc />
  protected override void ExtraOnClose()
  {
    base.ExtraOnClose();
    lock (this.sessionsLock)
    {
      for (int index = 0; index < this.mqttSessions.Count; ++index)
        this.mqttSessions[index].MqttPipe?.CloseCommunication();
      this.mqttSessions.Clear();
    }
  }

  private void ThreadTimerHeartCheck(object obj)
  {
    MqttSession[] mqttSessionArray = (MqttSession[]) null;
    lock (this.sessionsLock)
      mqttSessionArray = this.mqttSessions.ToArray();
    if (mqttSessionArray == null || mqttSessionArray.Length == 0)
      return;
    for (int index = 0; index < mqttSessionArray.Length; ++index)
    {
      if (mqttSessionArray[index].Protocol == "MQTT" && DateTime.Now - mqttSessionArray[index].ActiveTime > mqttSessionArray[index].ActiveTimeSpan)
        this.RemoveAndCloseSession(mqttSessionArray[index], "Thread Timer Heart Check failed:" + SoftBasic.GetTimeSpanDescription(DateTime.Now - mqttSessionArray[index].ActiveTime));
    }
  }

  private async Task DealWithPublish(MqttSession session, byte code, byte[] data)
  {
    OperateResult<MqttClientApplicationMessage> messageResult = MqttHelper.ParseMqttClientApplicationMessage(session, code, data);
    MqttClientApplicationMessage mqttClientApplicationMessage;
    if (!messageResult.IsSuccess)
    {
      this.RemoveAndCloseSession(session, messageResult.Message);
      messageResult = (OperateResult<MqttClientApplicationMessage>) null;
      mqttClientApplicationMessage = (MqttClientApplicationMessage) null;
    }
    else
    {
      mqttClientApplicationMessage = messageResult.Content;
      if (session.Protocol == "MQTT")
      {
        MqttQualityOfServiceLevel mqttQuality = mqttClientApplicationMessage.QualityOfServiceLevel;
        switch (mqttQuality)
        {
          case MqttQualityOfServiceLevel.AtLeastOnce:
            session.SendMqttCommand((byte) 4, (byte) 0, (byte[]) null, MqttHelper.BuildIntBytes(mqttClientApplicationMessage.MsgID));
            break;
          case MqttQualityOfServiceLevel.ExactlyOnce:
            session.SendMqttCommand((byte) 5, (byte) 0, (byte[]) null, MqttHelper.BuildIntBytes(mqttClientApplicationMessage.MsgID));
            break;
        }
        if (session.ForbidPublishTopic)
        {
          messageResult = (OperateResult<MqttClientApplicationMessage>) null;
          mqttClientApplicationMessage = (MqttClientApplicationMessage) null;
        }
        else
        {
          MqttServer.OnClientApplicationMessageReceiveDelegate applicationMessageReceive = this.OnClientApplicationMessageReceive;
          if (applicationMessageReceive != null)
            applicationMessageReceive(session, mqttClientApplicationMessage);
          if (mqttQuality == MqttQualityOfServiceLevel.OnlyTransfer || mqttClientApplicationMessage.IsCancelPublish)
          {
            messageResult = (OperateResult<MqttClientApplicationMessage>) null;
            mqttClientApplicationMessage = (MqttClientApplicationMessage) null;
          }
          else
          {
            this.PublishTopicPayload(mqttClientApplicationMessage.Topic, mqttClientApplicationMessage.Payload, false);
            if (mqttClientApplicationMessage.Retain)
              this.RetainTopicPayload(mqttClientApplicationMessage.Topic, mqttClientApplicationMessage);
            this.topicStatistics.StatisticsAdd();
            messageResult = (OperateResult<MqttClientApplicationMessage>) null;
            mqttClientApplicationMessage = (MqttClientApplicationMessage) null;
          }
        }
      }
      else
      {
        if ((int) code >> 4 == 3)
        {
          string apiName = mqttClientApplicationMessage.Topic.Trim('/');
          MqttRpcApiInfo apiInformation = this.GetMqttRpcApiInfo(apiName);
          if (apiInformation == null)
          {
            MqttServer.OnClientApplicationMessageReceiveDelegate applicationMessageReceive = this.OnClientApplicationMessageReceive;
            if (applicationMessageReceive != null)
              applicationMessageReceive(session, mqttClientApplicationMessage);
          }
          else
          {
            DateTime dateTime = DateTime.Now;
            OperateResult<string> result = await MqttHelper.HandleObjectMethod(session, mqttClientApplicationMessage, apiInformation);
            double timeSpend = Math.Round((DateTime.Now - dateTime).TotalSeconds, 5);
            apiInformation.CalledCountAddOne((long) (timeSpend * 100000.0));
            this.statisticsDict.StatisticsAdd(apiInformation.ApiTopic);
            this.LogNet?.WriteDebug(this.ToString(), $"{session} RPC: [{mqttClientApplicationMessage.Topic}] Spend:[{timeSpend * 1000.0:F2} ms] Count:[{apiInformation.CalledCount}] Return:[{result.IsSuccess}]");
            this.ReportOperateResult(session, result);
            result = (OperateResult<string>) null;
          }
          apiName = (string) null;
          apiInformation = (MqttRpcApiInfo) null;
        }
        else if ((int) code >> 4 == 8)
        {
          if (session.DeveloperPermissions)
            this.ReportOperateResult(session, OperateResult.CreateSuccessResult<string>(JArray.FromObject((object) this.GetAllMqttRpcApiInfo()).ToString()));
          else
            this.ReportOperateResult(session, StringResources.Language.DeveloperPrivileges);
        }
        else if ((int) code >> 4 == 4)
        {
          if (session.DeveloperPermissions)
            this.PublishTopicPayload(session, "", HslProtocol.PackStringArrayToByte(this.GetAllRetainTopics()));
          else
            this.ReportOperateResult(session, StringResources.Language.DeveloperPrivileges);
        }
        else if ((int) code >> 4 == 6)
        {
          if (session.DeveloperPermissions)
          {
            long[] logs = string.IsNullOrEmpty(mqttClientApplicationMessage.Topic) ? this.LogStatistics.LogStat.GetStatisticsSnapshot() : this.LogStatistics.GetStatisticsSnapshot(mqttClientApplicationMessage.Topic);
            if (logs == null)
              this.ReportOperateResult(session, new OperateResult<string>($"{session} RPC:{mqttClientApplicationMessage.Topic} has no data or not exist."));
            else
              this.ReportOperateResult(session, OperateResult.CreateSuccessResult<string>(logs.ToArrayString<long>()));
            logs = (long[]) null;
          }
          else
            this.ReportOperateResult(session, StringResources.Language.DeveloperPrivileges);
        }
        else if ((int) code >> 4 == 11)
        {
          if (session.DeveloperPermissions)
            this.ReportOperateResult(session, OperateResult.CreateSuccessResult<string>(JArray.FromObject((object) ((IEnumerable<MqttSession>) this.OnlineSessions).Select<MqttSession, MqttSessionInfo>((Func<MqttSession, MqttSessionInfo>) (m => m.GetSessionInfo()))).ToString()));
          else
            this.ReportOperateResult(session, StringResources.Language.DeveloperPrivileges);
        }
        else if ((int) code >> 4 == 5)
        {
          if (session.DeveloperPermissions)
          {
            lock (this.keysLock)
            {
              if (this.retainKeys.ContainsKey(mqttClientApplicationMessage.Topic))
              {
                byte[] responsePayload = Encoding.UTF8.GetBytes(this.retainKeys[mqttClientApplicationMessage.Topic].ToJsonString());
                this.PublishTopicPayload(session, mqttClientApplicationMessage.Topic, responsePayload);
                responsePayload = (byte[]) null;
              }
              else
                this.ReportOperateResult(session, StringResources.Language.KeyIsNotExist);
            }
          }
          else
            this.ReportOperateResult(session, StringResources.Language.DeveloperPrivileges);
        }
        messageResult = (OperateResult<MqttClientApplicationMessage>) null;
        mqttClientApplicationMessage = (MqttClientApplicationMessage) null;
      }
    }
  }

  /// <summary>将消息进行驻留到内存词典，方便进行其他的功能操作。</summary>
  /// <param name="topic">消息的主题</param>
  /// <param name="payload">当前的数据负载</param>
  private void RetainTopicPayload(string topic, byte[] payload)
  {
    MqttClientApplicationMessage applicationMessage1 = new MqttClientApplicationMessage();
    applicationMessage1.ClientId = nameof (MqttServer);
    applicationMessage1.QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce;
    applicationMessage1.Retain = true;
    applicationMessage1.Topic = topic;
    applicationMessage1.UserName = nameof (MqttServer);
    applicationMessage1.Payload = payload;
    MqttClientApplicationMessage applicationMessage2 = applicationMessage1;
    lock (this.keysLock)
    {
      if (this.retainKeys.ContainsKey(topic))
        this.retainKeys[topic] = applicationMessage2;
      else
        this.retainKeys.Add(topic, applicationMessage2);
    }
  }

  /// <summary>将消息进行驻留到内存词典，方便进行其他的功能操作。</summary>
  /// <param name="topic">消息的主题</param>
  /// <param name="message">当前的Mqtt消息</param>
  private void RetainTopicPayload(string topic, MqttClientApplicationMessage message)
  {
    lock (this.keysLock)
    {
      if (this.retainKeys.ContainsKey(topic))
        this.retainKeys[topic] = message;
      else
        this.retainKeys.Add(topic, message);
    }
  }

  private void DealWithSubscribe(MqttSession session, byte code, byte[] data)
  {
    int index1 = 0;
    int data1 = MqttHelper.ExtraIntFromBytes(data, ref index1);
    List<string> topics = new List<string>();
    List<byte> qosLevels = new List<byte>();
    try
    {
      while (index1 < data.Length - 1)
        MqttHelper.ExtraSubscribeMsgFromBytes(data, ref index1, topics, qosLevels);
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteError(this.ToString(), $"{$"{session} DealWithSubscribe exception: "}{ex.Message} Source: {data.ToHexString(' ')}");
      return;
    }
    session.SendMqttCommand((byte) 9, (byte) 0, MqttHelper.BuildIntBytes(data1), qosLevels.ToArray());
    lock (this.keysLock)
    {
      if (this.topicWildcard)
      {
        foreach (KeyValuePair<string, MqttClientApplicationMessage> retainKey in this.retainKeys)
        {
          for (int index2 = 0; index2 < topics.Count; ++index2)
          {
            if (MqttHelper.CheckMqttTopicWildcards(retainKey.Key, topics[index2]))
              session.MqttPipe.Send(MqttHelper.BuildPublishMqttCommand(retainKey.Key, retainKey.Value.Payload, true, session.IsAesCryptography ? session.AesCryptography : (AesCryptography) null).Content);
          }
        }
      }
      else
      {
        for (int index3 = 0; index3 < topics.Count; ++index3)
        {
          if (this.retainKeys.ContainsKey(topics[index3]))
            session.MqttPipe.Send(MqttHelper.BuildPublishMqttCommand(topics[index3], this.retainKeys[topics[index3]].Payload, true, session.IsAesCryptography ? session.AesCryptography : (AesCryptography) null).Content);
        }
      }
    }
    session.AddSubscribe(topics.ToArray());
    this.LogNet?.WriteDebug(this.ToString(), $"{session.ToString()} Subscribe: {topics.ToArray().ToArrayString<string>()}");
  }

  private void DealWithUnSubscribe(MqttSession session, byte code, byte[] data)
  {
    int index = 0;
    int data1 = MqttHelper.ExtraIntFromBytes(data, ref index);
    List<string> stringList = new List<string>();
    while (index < data.Length)
      stringList.Add(MqttHelper.ExtraMsgFromBytes(data, ref index));
    session.SendMqttCommand((byte) 11, (byte) 0, (byte[]) null, MqttHelper.BuildIntBytes(data1));
    session.RemoveSubscribe(stringList.ToArray());
    this.LogNet?.WriteDebug(this.ToString(), $"{session.ToString()} UnSubscribe: {stringList.ToArray().ToArrayString<string>()}");
  }

  /// <summary>
  /// 向指定的客户端发送主题及负载数据<br />
  /// Sends the topic and payload data to the specified client
  /// </summary>
  /// <param name="session">会话内容</param>
  /// <param name="topic">主题</param>
  /// <param name="payload">消息内容</param>
  public void PublishTopicPayload(MqttSession session, string topic, byte[] payload)
  {
    OperateResult operateResult = session.MqttPipe.Send(MqttHelper.BuildPublishMqttCommand(topic, payload, aesCryptography: session.IsAesCryptography ? session.AesCryptography : (AesCryptography) null).Content);
    if (!operateResult.IsSuccess)
      this.LogNet?.WriteError(this.ToString(), $"{session} PublishTopicPayload Failed:" + operateResult.Message);
    this.topicStatistics.StatisticsAdd();
  }

  /// <summary>
  /// 使用指定的规则向客户端发布主题及负载数据，可以根据会话的登录用户名，客户端ID信息进行筛选，例如只发布用户名admin的账户：( session ) =&gt; session.UserName == "admin"<br />
  /// Use the specified rules to publish topic and load data to the client, which can be filtered according to the session login user name and client ID information.
  /// For example, only the account with the user name admin is published: ( session ) =&gt; session.UserName == "admin"
  /// </summary>
  /// <param name="topic">主题</param>
  /// <param name="payload">消息内容</param>
  /// <param name="retain">是否在服务器驻留</param>
  /// <param name="check">会话的检查委托</param>
  public void PublishTopicPayload(
    string topic,
    byte[] payload,
    bool retain,
    Func<MqttSession, bool> check)
  {
    lock (this.sessionsLock)
    {
      byte[] numArray1 = (byte[]) null;
      for (int index = 0; index < this.mqttSessions.Count; ++index)
      {
        MqttSession mqttSession = this.mqttSessions[index];
        byte[] numArray2 = (byte[]) null;
        if (mqttSession.Protocol == "MQTT" && check(mqttSession))
        {
          if (mqttSession.IsAesCryptography)
          {
            if (numArray2 == null)
              numArray2 = MqttHelper.BuildPublishMqttCommand(topic, payload, aesCryptography: mqttSession.AesCryptography).Content;
          }
          else if (numArray1 == null)
            numArray1 = MqttHelper.BuildPublishMqttCommand(topic, payload).Content;
          OperateResult operateResult = mqttSession.MqttPipe.Send(mqttSession.IsAesCryptography ? numArray2 : numArray1);
          if (!operateResult.IsSuccess)
            this.LogNet?.WriteError(this.ToString(), $"{mqttSession} PublishTopicPayload Failed:" + operateResult.Message);
        }
      }
    }
    if (retain)
      this.RetainTopicPayload(topic, payload);
    this.topicStatistics.StatisticsAdd();
  }

  /// <summary>
  /// 从服务器向订阅了指定的主题的客户端发送消息，默认消息不驻留<br />
  /// Sends a message from the server to a client that subscribes to the specified topic; the default message does not retain
  /// </summary>
  /// <param name="topic">主题</param>
  /// <param name="payload">消息内容</param>
  /// <param name="retain">指示消息是否驻留</param>
  public void PublishTopicPayload(string topic, byte[] payload, bool retain = true)
  {
    this.PublishTopicPayload(topic, payload, retain, (Func<MqttSession, bool>) (session => session.IsClientSubscribe(topic, this.topicWildcard)));
  }

  /// <summary>
  /// 向所有的客户端强制发送主题及负载数据，默认消息不驻留<br />
  /// Send subject and payload data to all clients compulsively, and the default message does not retain
  /// </summary>
  /// <param name="topic">主题</param>
  /// <param name="payload">消息内容</param>
  /// <param name="retain">指示消息是否驻留</param>
  public void PublishAllClientTopicPayload(string topic, byte[] payload, bool retain = false)
  {
    this.PublishTopicPayload(topic, payload, retain, (Func<MqttSession, bool>) (session => true));
  }

  /// <summary>
  /// 向指定的客户端ID强制发送消息，默认消息不驻留<br />
  /// Forces a message to the specified client ID, and the default message does not retain
  /// </summary>
  /// <param name="clientId">指定的客户端ID信息</param>
  /// <param name="topic">主题</param>
  /// <param name="payload">消息内容</param>
  /// <param name="retain">指示消息是否驻留</param>
  public void PublishTopicPayload(string clientId, string topic, byte[] payload, bool retain = false)
  {
    this.PublishTopicPayload(topic, payload, retain, (Func<MqttSession, bool>) (session => session.ClientId == clientId));
  }

  /// <summary>
  /// 向客户端发布一个进度报告的信息，仅用于同步网络的时候才支持进度报告，将进度及消息发送给客户端，比如你的服务器需要分成5个部分完成，可以按照百分比提示给客户端当前服务器发生了什么<br />
  /// Publish the information of a progress report to the client. The progress report is only supported when the network is synchronized.
  /// The progress and the message are sent to the client. For example, your server needs to be divided into 5 parts to complete.
  /// You can prompt the client according to the percentage. What happened to the server
  /// </summary>
  /// <param name="session">当前的网络会话</param>
  /// <param name="topic">回发客户端的关键数据，可以是百分比字符串，甚至是自定义的任意功能</param>
  /// <param name="payload">数据消息</param>
  public void ReportProgress(MqttSession session, string topic, string payload)
  {
    if (!(session.Protocol == "HUSL"))
      throw new Exception("ReportProgress only support sync communication");
    payload = payload ?? string.Empty;
    OperateResult operateResult = session.SendMqttCommand((byte) 15, (byte) 0, MqttHelper.BuildSegCommandByString(topic), Encoding.UTF8.GetBytes(payload));
    if (operateResult.IsSuccess)
      return;
    this.LogNet?.WriteError(this.ToString(), $"{session} PublishTopicPayload Failed:" + operateResult.Message);
  }

  /// <summary>
  /// 向客户端发布一个失败的操作信息，仅用于同步网络的时候反馈失败结果，将错误的信息反馈回客户端，客户端就知道服务器发生了什么，为什么反馈失败。<br />
  /// Publish a failed operation information to the client, which is only used to feed back the failure result when synchronizing the network.
  /// If the error information is fed back to the client, the client will know what happened to the server and why the feedback failed.
  /// </summary>
  /// <param name="session">当前的网络会话</param>
  /// <param name="message">错误的消息文本信息</param>
  public void ReportOperateResult(MqttSession session, string message)
  {
    this.ReportOperateResult(session, new OperateResult<string>(message));
  }

  /// <summary>
  /// 向客户端发布一个操作结果的信息，仅用于同步网络的时候反馈操作结果，该操作可能成功，可能失败，客户端就知道服务器发生了什么，以及结果如何。<br />
  /// Publish an operation result information to the client, which is only used to feed back the operation result when synchronizing the network.
  /// The operation may succeed or fail, and the client knows what happened to the server and the result.
  /// </summary>
  /// <param name="session">当前的网络会话</param>
  /// <param name="result">结果对象内容</param>
  public void ReportOperateResult(MqttSession session, OperateResult<string> result)
  {
    if (!(session.Protocol == "HUSL"))
      throw new Exception("Report Result Message only support sync communication, client is MqttSyncClient");
    if (result.IsSuccess)
    {
      byte[] payload = string.IsNullOrEmpty(result.Content) ? new byte[0] : Encoding.UTF8.GetBytes(result.Content);
      this.PublishTopicPayload(session, result.ErrorCode.ToString(), payload);
    }
    else
    {
      OperateResult operateResult = session.SendMqttCommand((byte) 0, (byte) 0, MqttHelper.BuildSegCommandByString(result.ErrorCode.ToString()), string.IsNullOrEmpty(result.Message) ? new byte[0] : Encoding.UTF8.GetBytes(result.Message), session.IsAesCryptography ? session.AesCryptography : (AesCryptography) null);
      if (!operateResult.IsSuccess)
        this.LogNet?.WriteError(this.ToString(), $"{session} PublishTopicPayload Failed:" + operateResult.Message);
    }
  }

  /// <summary>
  /// 使用指定的对象来返回网络的API接口，前提是传入的数据为json参数，返回的数据为 <c>OperateResult&lt;string&gt;</c> 数据，详细参照说明<br />
  /// Use the specified object to return the API interface of the network,
  /// provided that the incoming data is json parameters and the returned data is <c>OperateResult&lt;string&gt;</c> data,
  /// please refer to the description for details
  /// </summary>
  /// <param name="session">当前的会话内容</param>
  /// <param name="message">客户端发送的消息，其中的payload将会解析为一个json字符串，然后提取参数信息。</param>
  /// <param name="apiObject">当前的对象的内容信息</param>
  public async Task ReportObjectApiMethod(
    MqttSession session,
    MqttClientApplicationMessage message,
    object apiObject)
  {
    MqttSession session1 = session.Protocol == "HUSL" ? session : throw new Exception("Report Result Message only support sync communication, client is MqttSyncClient");
    OperateResult<string> result = await MqttHelper.HandleObjectMethod(session, message, apiObject);
    this.ReportOperateResult(session1, result);
    session1 = (MqttSession) null;
    result = (OperateResult<string>) null;
  }

  private void MqttRpcAdd(string apiTopic, MqttRpcApiInfo apiInfo)
  {
    if (this.apiTopicServiceDict.ContainsKey(apiTopic))
      this.apiTopicServiceDict[apiTopic] = apiInfo;
    else
      this.apiTopicServiceDict.Add(apiTopic, apiInfo);
  }

  private bool MqttRpcRemove(string apiTopic)
  {
    return this.apiTopicServiceDict.ContainsKey(apiTopic) && this.apiTopicServiceDict.Remove(apiTopic);
  }

  private MqttRpcApiInfo GetMqttRpcApiInfo(string apiTopic)
  {
    MqttRpcApiInfo mqttRpcApiInfo = (MqttRpcApiInfo) null;
    lock (this.rpcApiLock)
    {
      if (this.apiTopicServiceDict.ContainsKey(apiTopic))
        mqttRpcApiInfo = this.apiTopicServiceDict[apiTopic];
    }
    return mqttRpcApiInfo;
  }

  /// <summary>
  /// 获取当前所有注册的RPC接口信息，将返回一个数据列表。<br />
  /// Get all currently registered RPC interface information, and a data list will be returned.
  /// </summary>
  /// <returns>信息列表</returns>
  public MqttRpcApiInfo[] GetAllMqttRpcApiInfo()
  {
    MqttRpcApiInfo[] allMqttRpcApiInfo = (MqttRpcApiInfo[]) null;
    lock (this.rpcApiLock)
      allMqttRpcApiInfo = this.apiTopicServiceDict.Values.ToArray<MqttRpcApiInfo>();
    return allMqttRpcApiInfo;
  }

  /// <summary>
  /// 注册一个RPC的服务接口，可以指定当前的控制器名称，以及提供RPC服务的原始对象，指定统一的权限控制。<br />
  /// Register an RPC service interface, you can specify the current controller name, and the original object that provides the RPC service, Specify unified access control
  /// </summary>
  /// <remarks>
  /// 如果设置了<paramref name="permissionAttribute" />的参数信息，那么请确保调用时获取了企业商业授权的权限，否则，只能使用24小时，然后因为权限问题报错。
  /// </remarks>
  /// <param name="api">前置的接口信息，可以理解为MVC模式的控制器</param>
  /// <param name="obj">原始对象信息，也可以是类型对象本身</param>
  /// <param name="permissionAttribute">统一的权限访问配置，将会覆盖单个方法的权限控制。</param>
  public void RegisterMqttRpcApi(
    string api,
    object obj,
    HslMqttPermissionAttribute permissionAttribute)
  {
    lock (this.rpcApiLock)
    {
      foreach (MqttRpcApiInfo apiInfo in MqttHelper.GetSyncServicesApiInformationFromObject(api, obj, permissionAttribute))
        this.MqttRpcAdd(apiInfo.ApiTopic, apiInfo);
    }
  }

  /// <summary>
  /// 注册一个RPC的服务接口，可以指定当前的控制器名称，以及提供RPC服务的原始对象<br />
  /// Register an RPC service interface, you can specify the current controller name, and the original object that provides the RPC service
  /// </summary>
  /// <param name="api">前置的接口信息，可以理解为MVC模式的控制器</param>
  /// <param name="obj">原始对象信息，也可以是类型对象本身</param>
  public void RegisterMqttRpcApi(string api, object obj)
  {
    lock (this.rpcApiLock)
    {
      foreach (MqttRpcApiInfo apiInfo in MqttHelper.GetSyncServicesApiInformationFromObject(api, obj))
        this.MqttRpcAdd(apiInfo.ApiTopic, apiInfo);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttServer.RegisterMqttRpcApi(System.String,System.Object)" />
  public void RegisterMqttRpcApi(object obj)
  {
    lock (this.rpcApiLock)
    {
      foreach (MqttRpcApiInfo apiInfo in MqttHelper.GetSyncServicesApiInformationFromObject(obj))
        this.MqttRpcAdd(apiInfo.ApiTopic, apiInfo);
    }
  }

  /// <summary>
  /// 卸载一个已经注册的RPC接口，想要卸载指定的接口，此处就需要传入注册时一样的参数<br />
  /// Uninstall a registered RPC interface. If you want to uninstall the specified interface, you need to pass in the same parameters as the registration
  /// </summary>
  /// <param name="api">前置的接口信息，可以理解为MVC模式的控制器</param>
  /// <param name="obj">原始对象信息，也可以是类型对象本身</param>
  public void UnRegisterMqttRpcApi(string api, object obj)
  {
    lock (this.rpcApiLock)
    {
      foreach (MqttRpcApiInfo mqttRpcApiInfo in MqttHelper.GetSyncServicesApiInformationFromObject(api, obj))
        this.MqttRpcRemove(mqttRpcApiInfo.ApiTopic);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttServer.UnRegisterMqttRpcApi(System.String,System.Object)" />
  public void UnRegisterMqttRpcApi(object obj)
  {
    lock (this.rpcApiLock)
    {
      foreach (MqttRpcApiInfo mqttRpcApiInfo in MqttHelper.GetSyncServicesApiInformationFromObject(obj))
        this.MqttRpcRemove(mqttRpcApiInfo.ApiTopic);
    }
  }

  /// <summary>
  /// 卸载一个指定的RPC接口，需要指定唯一的接口名称，如果接口重命名了，需要使用实际访问的时候的接口名称，返回是否成功卸载接口<br />
  /// To uninstall a specified RPC interface, you need to specify a unique interface name. If the interface is renamed,
  /// you need to use the interface name during the actual access, and return whether the interface is successfully uninstalled
  /// </summary>
  /// <param name="apiTopic">接口的唯一名称</param>
  /// <returns>是否卸载成功</returns>
  public bool UnRegisterHttpRpcApiSingle(string apiTopic)
  {
    lock (this.rpcApiLock)
      return this.MqttRpcRemove(apiTopic);
  }

  /// <summary>
  /// 启动文件服务功能，协议头为FILE，需要指定服务器存储的文件路径<br />
  /// Start the file service function, the protocol header is FILE, you need to specify the file path stored by the server
  /// </summary>
  /// <param name="filePath">文件的存储路径</param>
  public void UseFileServer(string filePath)
  {
    this.filesDirectoryPath = filePath;
    this.fileServerEnabled = true;
    this.CheckFolderAndCreate();
  }

  /// <summary>关闭文件服务功能</summary>
  public void CloseFileServer() => this.fileServerEnabled = false;

  /// <summary>
  /// 获取当前的针对文件夹的文件管理容器的数量<br />
  /// Get the current number of file management containers for the folder
  /// </summary>
  [HslMqttApi(Description = "Get the current number of file management containers for the folder")]
  public int GroupFileContainerCount() => this.m_dictionary_group_marks.Count;

  /// <summary>
  /// 获取当前实时的文件上传下载的监控信息，操作的客户端信息，文件分类，文件名，上传或下载的速度等<br />
  /// Obtain current real-time file upload and download monitoring information, operating client information, file classification, file name, upload or download speed, etc.
  /// </summary>
  /// <returns>文件的监控信息</returns>
  [HslMqttApi(Description = "Obtain current real-time file upload and download monitoring information, operating client information, file classification, file name, upload or download speed, etc.")]
  public MqttFileMonitorItem[] GetMonitorItemsSnapShoot()
  {
    return this.fileMonitor.GetMonitorItemsSnapShoot();
  }

  /// <summary>
  /// 当客户端进行文件操作时，校验客户端合法性的事件，操作码具体查看<seealso cref="T:HslCommunication.MQTT.MqttControlMessage" />的常量值<br />
  /// When client performing file operations, it is an event to verify the legitimacy of the client. For the operation code, check the constant value of <seealso cref="T:HslCommunication.MQTT.MqttControlMessage" />
  /// </summary>
  public event MqttServer.FileOperateVerificationDelegate FileOperateVerification;

  private bool CheckPathAndFilenameLegal(string input) => Regex.IsMatch(input, "[:?*/\\<>|\"]");

  /// <summary>
  /// 可以自定义重新指向需要下载的文件路径信息，第一个参数是下载文件名称，第个参数是实际映射名称，需要返回新的文件路径和下载完成是否删除的信息<br />
  /// You can customize the redirection of the file path that needs to be downloaded. The first parameter is the name of the downloaded file,
  /// </summary>
  public Func<MqttSession, string, string, OperateResult<string, bool>> DownloadFileRedirect { get; set; }

  private async Task HandleFileMessageAsync(MqttSession session)
  {
    OperateResult<byte, byte[]> receiveGroupInfo = await MqttHelper.ReceiveMqttMessageAsync((CommunicationPipe) session.MqttPipe, 60000);
    string[] groupInfo;
    OperateResult<byte, byte[]> receiveFileNames;
    string[] fileNames;
    OperateResult opLegal;
    OperateResult sendLegal;
    string relativeName;
    if (!receiveGroupInfo.IsSuccess)
    {
      receiveGroupInfo = (OperateResult<byte, byte[]>) null;
      groupInfo = (string[]) null;
      receiveFileNames = (OperateResult<byte, byte[]>) null;
      fileNames = (string[]) null;
      opLegal = (OperateResult) null;
      sendLegal = (OperateResult) null;
      relativeName = (string) null;
    }
    else
    {
      groupInfo = HslProtocol.UnPackStringArrayFromByte(receiveGroupInfo.Content2);
      receiveFileNames = await MqttHelper.ReceiveMqttMessageAsync((CommunicationPipe) session.MqttPipe, 60000);
      if (!receiveFileNames.IsSuccess)
      {
        receiveGroupInfo = (OperateResult<byte, byte[]>) null;
        groupInfo = (string[]) null;
        receiveFileNames = (OperateResult<byte, byte[]>) null;
        fileNames = (string[]) null;
        opLegal = (OperateResult) null;
        sendLegal = (OperateResult) null;
        relativeName = (string) null;
      }
      else
      {
        fileNames = HslProtocol.UnPackStringArrayFromByte(receiveFileNames.Content2);
        for (int i = 0; i < groupInfo.Length; ++i)
        {
          if (this.CheckPathAndFilenameLegal(groupInfo[i]))
          {
            session.SendMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes("Path Invalid, not include '\\/:*?\"<>|'"));
            this.RemoveAndCloseSession(session, "CheckPathAndFilenameLegal:" + groupInfo[i]);
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
            return;
          }
        }
        for (int i = 0; i < fileNames.Length; ++i)
        {
          if (receiveFileNames.Content1 != (byte) 111)
          {
            if (this.CheckPathAndFilenameLegal(fileNames[i]))
            {
              session.SendMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes("FileName Invalid, not include '\\/:*?\"<>|'"));
              this.RemoveAndCloseSession(session, "CheckPathAndFilenameLegal:" + fileNames[i]);
              receiveGroupInfo = (OperateResult<byte, byte[]>) null;
              groupInfo = (string[]) null;
              receiveFileNames = (OperateResult<byte, byte[]>) null;
              fileNames = (string[]) null;
              opLegal = (OperateResult) null;
              sendLegal = (OperateResult) null;
              relativeName = (string) null;
              return;
            }
          }
          else if (Regex.IsMatch(fileNames[i], "[:?*<>|\"]"))
          {
            session.SendMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes("FileName Invalid, not include ':*?\"<>|'"));
            this.RemoveAndCloseSession(session, "CheckPathAndFilenameLegal:" + fileNames[i]);
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
            return;
          }
        }
        MqttServer.FileOperateVerificationDelegate operateVerification = this.FileOperateVerification;
        opLegal = (operateVerification != null ? operateVerification(session, receiveFileNames.Content1, groupInfo, fileNames) : (OperateResult) null) ?? OperateResult.CreateSuccessResult();
        sendLegal = await session.SendMqttCommandAsync(opLegal.IsSuccess ? (byte) 100 : (byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(opLegal.Message));
        if (!opLegal.IsSuccess)
        {
          this.RemoveAndCloseSession(session, "FileOperateVerification:" + opLegal.Message);
          receiveGroupInfo = (OperateResult<byte, byte[]>) null;
          groupInfo = (string[]) null;
          receiveFileNames = (OperateResult<byte, byte[]>) null;
          fileNames = (string[]) null;
          opLegal = (OperateResult) null;
          sendLegal = (OperateResult) null;
          relativeName = (string) null;
        }
        else if (!sendLegal.IsSuccess)
        {
          this.RemoveAndCloseSession(session, "FileOperate SendLegal:" + sendLegal.Message);
          receiveGroupInfo = (OperateResult<byte, byte[]>) null;
          groupInfo = (string[]) null;
          receiveFileNames = (OperateResult<byte, byte[]>) null;
          fileNames = (string[]) null;
          opLegal = (OperateResult) null;
          sendLegal = (OperateResult) null;
          relativeName = (string) null;
        }
        else
        {
          string[] groups = groupInfo;
          string[] strArray1 = fileNames;
          string fileName1 = (strArray1 != null ? (strArray1.Length != 0 ? 1 : 0) : 0) != 0 ? fileNames[0] : string.Empty;
          relativeName = this.GetRelativeFileName(groups, fileName1);
          if (receiveFileNames.Content1 == (byte) 101)
          {
            string fileName = fileNames[0];
            string guidName = this.TransformFactFileName(groupInfo, fileName);
            FileMarkId fileMarkId = this.GetFileMarksFromDictionaryWithFileName(guidName);
            fileMarkId.EnterReadOperator();
            DateTime dateTimeStart = DateTime.Now;
            MqttFileMonitorItem monitorItem = new MqttFileMonitorItem()
            {
              EndPoint = session.EndPoint,
              ClientId = session.ClientId,
              UserName = session.UserName,
              FileName = fileName,
              MappingName = guidName,
              Operate = "Download",
              Groups = HslHelper.PathCombine(groupInfo)
            };
            this.fileMonitor.Add(monitorItem);
            string fullDownloadFileName = this.ReturnAbsoluteFileName(groupInfo, guidName);
            bool deleteFile = false;
            if (this.DownloadFileRedirect != null)
            {
              OperateResult<string, bool> redirect = this.DownloadFileRedirect(session, fileName, fullDownloadFileName);
              if (redirect.IsSuccess)
              {
                fullDownloadFileName = redirect.Content1;
                deleteFile = redirect.Content2;
              }
              redirect = (OperateResult<string, bool>) null;
            }
            OperateResult send = await MqttHelper.SendMqttFileAsync((CommunicationPipe) session.MqttPipe, fullDownloadFileName, fileName, "", new Action<long, long>(monitorItem.UpdateProgress), session.IsAesCryptography ? session.AesCryptography : (AesCryptography) null);
            fileMarkId.LeaveReadOperator();
            this.fileMonitor.Remove(monitorItem.UniqueId);
            MqttServer.FileChangedDelegate fileChangedEvent = this.OnFileChangedEvent;
            if (fileChangedEvent != null)
              fileChangedEvent(session, new MqttFileOperateInfo()
              {
                Groups = HslHelper.PathCombine(groupInfo),
                FileNames = fileNames,
                MappingNames = new string[1]{ guidName },
                Operate = "Download",
                TimeCost = DateTime.Now - dateTimeStart
              });
            if (deleteFile)
              this.DeleteFileByName(fullDownloadFileName);
            if (!send.IsSuccess)
              this.LogNet?.WriteError(this.ToString(), $"{$"{session} {StringResources.Language.FileDownloadFailed}[{send.Message}]:{relativeName} Name:{session.UserName}"} Spend:{SoftBasic.GetTimeSpanDescription(DateTime.Now - dateTimeStart)}");
            else
              this.LogNet?.WriteInfo(this.ToString(), $"{session} {StringResources.Language.FileDownloadSuccess}:{relativeName} Spend:{SoftBasic.GetTimeSpanDescription(DateTime.Now - dateTimeStart)}");
            fileName = (string) null;
            guidName = (string) null;
            fileMarkId = (FileMarkId) null;
            monitorItem = (MqttFileMonitorItem) null;
            fullDownloadFileName = (string) null;
            send = (OperateResult) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 102)
          {
            string fileName = fileNames[0];
            string fullFileName = this.ReturnAbsoluteFileName(groupInfo, fileName);
            this.CheckFolderAndCreate();
            FileInfo info = new FileInfo(fullFileName);
            try
            {
              if (!Directory.Exists(info.DirectoryName))
                Directory.CreateDirectory(info.DirectoryName);
            }
            catch (Exception ex)
            {
              ILogNet logNet = this.LogNet;
              if (logNet == null)
              {
                receiveGroupInfo = (OperateResult<byte, byte[]>) null;
                groupInfo = (string[]) null;
                receiveFileNames = (OperateResult<byte, byte[]>) null;
                fileNames = (string[]) null;
                opLegal = (OperateResult) null;
                sendLegal = (OperateResult) null;
                relativeName = (string) null;
                return;
              }
              logNet.WriteException(this.ToString(), StringResources.Language.FilePathCreateFailed + fullFileName, ex);
              receiveGroupInfo = (OperateResult<byte, byte[]>) null;
              groupInfo = (string[]) null;
              receiveFileNames = (OperateResult<byte, byte[]>) null;
              fileNames = (string[]) null;
              opLegal = (OperateResult) null;
              sendLegal = (OperateResult) null;
              relativeName = (string) null;
              return;
            }
            string guidName = SoftBasic.GetUniqueStringByGuidAndRandom();
            DateTime dateTimeStart = DateTime.Now;
            MqttFileMonitorItem monitorItem = new MqttFileMonitorItem()
            {
              EndPoint = session.EndPoint,
              ClientId = session.ClientId,
              UserName = session.UserName,
              FileName = fileName,
              MappingName = guidName,
              Operate = "Upload",
              Groups = HslHelper.PathCombine(groupInfo)
            };
            this.fileMonitor.Add(monitorItem);
            OperateResult<FileBaseInfo> receive = await this.ReceiveMqttFileAndUpdateGroupAsync(session, info, guidName, new Action<long, long>(monitorItem.UpdateProgress));
            this.fileMonitor.Remove(monitorItem.UniqueId);
            if (receive.IsSuccess)
            {
              MqttServer.FileChangedDelegate fileChangedEvent = this.OnFileChangedEvent;
              if (fileChangedEvent != null)
                fileChangedEvent(session, new MqttFileOperateInfo()
                {
                  Groups = HslHelper.PathCombine(groupInfo),
                  FileNames = fileNames,
                  MappingNames = new string[1]{ guidName },
                  Operate = "Upload",
                  TimeCost = DateTime.Now - dateTimeStart
                });
              this.LogNet?.WriteInfo(this.ToString(), $"{session} {StringResources.Language.FileUploadSuccess}:{relativeName} Spend:{SoftBasic.GetTimeSpanDescription(DateTime.Now - dateTimeStart)}");
            }
            else
              this.LogNet?.WriteError(this.ToString(), $"{session} {StringResources.Language.FileUploadFailed}[{receive.Message}]:{relativeName} Spend:{SoftBasic.GetTimeSpanDescription(DateTime.Now - dateTimeStart)}");
            fileName = (string) null;
            fullFileName = (string) null;
            info = (FileInfo) null;
            guidName = (string) null;
            monitorItem = (MqttFileMonitorItem) null;
            receive = (OperateResult<FileBaseInfo>) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 103)
          {
            DateTime dateTimeStart = DateTime.Now;
            List<string> mappings = new List<string>();
            string[] strArray = fileNames;
            for (int index = 0; index < strArray.Length; ++index)
            {
              string item = strArray[index];
              string fullFileName = this.ReturnAbsoluteFileName(groupInfo, item);
              FileInfo info = new FileInfo(fullFileName);
              GroupFileContainer fileManagment = this.GetGroupFromFilePath(info.DirectoryName);
              string guid = fileManagment.DeleteFile(info.Name);
              this.DeleteExsistingFile(info.DirectoryName, guid);
              mappings.Add(guid);
              relativeName = this.GetRelativeFileName(groupInfo, item);
              this.LogNet?.WriteInfo(this.ToString(), $"{session} {StringResources.Language.FileDeleteSuccess}:{relativeName}");
              fullFileName = (string) null;
              info = (FileInfo) null;
              fileManagment = (GroupFileContainer) null;
              guid = (string) null;
              item = (string) null;
            }
            strArray = (string[]) null;
            OperateResult operateResult = await session.SendMqttCommandAsync((byte) 103, (byte[]) null, (byte[]) null);
            MqttServer.FileChangedDelegate fileChangedEvent = this.OnFileChangedEvent;
            if (fileChangedEvent != null)
              fileChangedEvent(session, new MqttFileOperateInfo()
              {
                Groups = HslHelper.PathCombine(groupInfo),
                FileNames = fileNames,
                MappingNames = mappings.ToArray(),
                Operate = "Delete",
                TimeCost = DateTime.Now - dateTimeStart
              });
            mappings = (List<string>) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 110)
          {
            DateTime dateTimeStart = DateTime.Now;
            string fullFileName = this.ReturnAbsoluteFileName(groupInfo, "123.txt");
            FileInfo info = new FileInfo(fullFileName);
            GroupFileContainer fileManagment = this.GetGroupFromFilePath(info.DirectoryName);
            List<string> file_names;
            List<string> guid_names = fileManagment.ClearAllFiles(out file_names);
            this.DeleteExsistingFile(info.DirectoryName, guid_names);
            OperateResult operateResult = await session.SendMqttCommandAsync((byte) 110, (byte[]) null, (byte[]) null);
            MqttServer.FileChangedDelegate fileChangedEvent = this.OnFileChangedEvent;
            if (fileChangedEvent != null)
              fileChangedEvent(session, new MqttFileOperateInfo()
              {
                Groups = HslHelper.PathCombine(groupInfo),
                FileNames = file_names.ToArray(),
                MappingNames = guid_names.ToArray(),
                Operate = "ClearFolder",
                TimeCost = DateTime.Now - dateTimeStart
              });
            this.LogNet?.WriteInfo(this.ToString(), $"{session.ToString()}ClearFolder : {relativeName}");
            fullFileName = (string) null;
            info = (FileInfo) null;
            fileManagment = (GroupFileContainer) null;
            guid_names = (List<string>) null;
            file_names = (List<string>) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 104)
          {
            DateTime dateTimeStart = DateTime.Now;
            string fullFileName = this.ReturnAbsoluteFileName(groupInfo, "123.txt");
            FileInfo info = new FileInfo(fullFileName);
            GroupFileContainer fileManagment = this.GetGroupFromFilePath(info.DirectoryName);
            List<string> file_names;
            List<string> guid_names = fileManagment.GetAllFiles(out file_names);
            this.DeleteGroupFileContainer(info.DirectoryName);
            OperateResult operateResult = await session.SendMqttCommandAsync((byte) 104, (byte[]) null, (byte[]) null);
            MqttServer.FileChangedDelegate fileChangedEvent = this.OnFileChangedEvent;
            if (fileChangedEvent != null)
              fileChangedEvent(session, new MqttFileOperateInfo()
              {
                Groups = HslHelper.PathCombine(groupInfo),
                FileNames = file_names.ToArray(),
                MappingNames = guid_names.ToArray(),
                Operate = "DeleteFolder",
                TimeCost = DateTime.Now - dateTimeStart
              });
            this.LogNet?.WriteInfo(this.ToString(), $"{session.ToString()} FolderDelete : {relativeName}");
            fullFileName = (string) null;
            info = (FileInfo) null;
            fileManagment = (GroupFileContainer) null;
            guid_names = (List<string>) null;
            file_names = (List<string>) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 111)
          {
            DateTime dateTimeStart = DateTime.Now;
            string fullFileName = this.ReturnAbsoluteFileName(groupInfo, "123.txt");
            FileInfo info = new FileInfo(fullFileName);
            string[] groups_new = fileNames[0].Split('/', '\\');
            string newPath = Path.Combine(this.filesDirectoryPath, HslHelper.PathCombine(groups_new));
            OperateResult rename = this.RenameGroupFileContainer(info.DirectoryName, newPath);
            if (!rename.IsSuccess)
            {
              this.LogNet?.WriteInfo(this.ToString(), $"{session.ToString()} RenameFolder : {HslHelper.PathCombine(groupInfo)} -> {fileNames[0]} failed: {rename.Message}");
              session.SendMqttCommand((byte) 0, (byte[]) null, Encoding.UTF8.GetBytes("File path already exist"));
              receiveGroupInfo = (OperateResult<byte, byte[]>) null;
              groupInfo = (string[]) null;
              receiveFileNames = (OperateResult<byte, byte[]>) null;
              fileNames = (string[]) null;
              opLegal = (OperateResult) null;
              sendLegal = (OperateResult) null;
              relativeName = (string) null;
            }
            else
            {
              OperateResult operateResult = await session.SendMqttCommandAsync((byte) 111, (byte[]) null, (byte[]) null);
              MqttServer.FileChangedDelegate fileChangedEvent = this.OnFileChangedEvent;
              if (fileChangedEvent != null)
                fileChangedEvent(session, new MqttFileOperateInfo()
                {
                  Groups = HslHelper.PathCombine(groupInfo),
                  FileNames = (string[]) null,
                  Operate = "RenameFolder",
                  TimeCost = DateTime.Now - dateTimeStart
                });
              this.LogNet?.WriteInfo(this.ToString(), $"{session.ToString()} RenameFolder : {HslHelper.PathCombine(groupInfo)} -> {fileNames[0]}");
              fullFileName = (string) null;
              info = (FileInfo) null;
              groups_new = (string[]) null;
              newPath = (string) null;
              rename = (OperateResult) null;
              receiveGroupInfo = (OperateResult<byte, byte[]>) null;
              groupInfo = (string[]) null;
              receiveFileNames = (OperateResult<byte, byte[]>) null;
              fileNames = (string[]) null;
              opLegal = (OperateResult) null;
              sendLegal = (OperateResult) null;
              relativeName = (string) null;
            }
          }
          else if (receiveFileNames.Content1 == (byte) 105)
          {
            GroupFileContainer fileManagment = this.GetGroupFromFilePath(this.ReturnAbsoluteFilePath(groupInfo));
            OperateResult operateResult = await session.SendMqttCommandAsync((byte) 104, (byte[]) null, Encoding.UTF8.GetBytes(fileManagment.JsonArrayContent));
            fileManagment = (GroupFileContainer) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 108)
          {
            GroupFileContainer fileManagment = this.GetGroupFromFilePath(this.ReturnAbsoluteFilePath(groupInfo));
            OperateResult operateResult = await session.SendMqttCommandAsync((byte) 108, (byte[]) null, Encoding.UTF8.GetBytes(fileManagment.GetGroupFileInfo(true).ToJsonString()));
            fileManagment = (GroupFileContainer) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 109)
          {
            string[] strArray2 = fileNames;
            bool withLastFileInfo = (strArray2 != null ? (strArray2.Length != 0 ? 1 : 0) : 0) != 0 && fileNames[0] == "1";
            List<GroupFileInfo> folders = new List<GroupFileInfo>();
            string[] strArray = this.GetDirectories(groupInfo);
            for (int index = 0; index < strArray.Length; ++index)
            {
              string m = strArray[index];
              DirectoryInfo directory = new DirectoryInfo(m);
              List<string> path = new List<string>((IEnumerable<string>) groupInfo);
              path.Add(directory.Name);
              GroupFileContainer fileManagment = this.GetGroupFromFilePath(this.ReturnAbsoluteFilePath(path.ToArray()));
              GroupFileInfo groupFileInfo = fileManagment.GetGroupFileInfo(withLastFileInfo);
              groupFileInfo.PathName = directory.Name;
              folders.Add(groupFileInfo);
              directory = (DirectoryInfo) null;
              path = (List<string>) null;
              fileManagment = (GroupFileContainer) null;
              groupFileInfo = (GroupFileInfo) null;
              m = (string) null;
            }
            strArray = (string[]) null;
            OperateResult operateResult = await session.SendMqttCommandAsync((byte) 108, (byte[]) null, Encoding.UTF8.GetBytes(folders.ToJsonString()));
            folders = (List<GroupFileInfo>) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 == (byte) 106)
          {
            List<string> folders = new List<string>();
            string[] strArray = this.GetDirectories(groupInfo);
            for (int index = 0; index < strArray.Length; ++index)
            {
              string m = strArray[index];
              DirectoryInfo directory = new DirectoryInfo(m);
              folders.Add(directory.Name);
              directory = (DirectoryInfo) null;
              m = (string) null;
            }
            strArray = (string[]) null;
            JArray jArray = JArray.FromObject((object) folders.ToArray());
            OperateResult operateResult = await session.SendMqttCommandAsync((byte) 106, (byte[]) null, Encoding.UTF8.GetBytes(jArray.ToString()));
            folders = (List<string>) null;
            jArray = (JArray) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else if (receiveFileNames.Content1 != (byte) 107)
          {
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
          else
          {
            string fileName = fileNames[0];
            string fullPath = this.ReturnAbsoluteFilePath(groupInfo);
            GroupFileContainer fileManagment = this.GetGroupFromFilePath(fullPath);
            bool isExists = fileManagment.FileExists(fileName);
            OperateResult operateResult = await session.SendMqttCommandAsync(isExists ? (byte) 1 : (byte) 0, (byte[]) null, Encoding.UTF8.GetBytes(StringResources.Language.FileNotExist));
            fileName = (string) null;
            fullPath = (string) null;
            fileManagment = (GroupFileContainer) null;
            receiveGroupInfo = (OperateResult<byte, byte[]>) null;
            groupInfo = (string[]) null;
            receiveFileNames = (OperateResult<byte, byte[]>) null;
            fileNames = (string[]) null;
            opLegal = (OperateResult) null;
            sendLegal = (OperateResult) null;
            relativeName = (string) null;
          }
        }
      }
    }
  }

  /// <summary>从套接字接收文件并保存，更新文件列表</summary>
  /// <param name="session">当前的会话信息</param>
  /// <param name="info">保存的信息</param>
  /// <param name="guidName">映射的文件名称</param>
  /// <param name="reportProgress">当前的委托信息</param>
  /// <returns>是否成功的结果对象</returns>
  private async Task<OperateResult<FileBaseInfo>> ReceiveMqttFileAndUpdateGroupAsync(
    MqttSession session,
    FileInfo info,
    string guidName,
    Action<long, long> reportProgress)
  {
    string fileName = Path.Combine(info.DirectoryName, guidName);
    OperateResult<FileBaseInfo> receive = await MqttHelper.ReceiveMqttFileAsync((CommunicationPipe) session.MqttPipe, (object) fileName, reportProgress, session.IsAesCryptography ? session.AesCryptography : (AesCryptography) null);
    if (!receive.IsSuccess)
    {
      this.DeleteFileByName(fileName);
      return receive;
    }
    GroupFileContainer fileManagment = this.GetGroupFromFilePath(info.DirectoryName);
    string oldName = fileManagment.UpdateFileMappingName(info.Name, receive.Content.Size, guidName, session.UserName, receive.Content.Tag);
    this.DeleteExsistingFile(info.DirectoryName, oldName);
    OperateResult sendBack = await session.SendMqttCommandAsync((byte) 100, (byte[]) null, Encoding.UTF8.GetBytes(StringResources.Language.SuccessText));
    return sendBack.IsSuccess ? OperateResult.CreateSuccessResult<FileBaseInfo>(receive.Content) : OperateResult.CreateFailedResult<FileBaseInfo>(sendBack);
  }

  /// <summary>返回相对路径的名称</summary>
  /// <param name="groups">文件的分类路径信息</param>
  /// <param name="fileName">文件名</param>
  /// <returns>是否成功的结果对象</returns>
  private string GetRelativeFileName(string[] groups, string fileName)
  {
    string path1 = "";
    for (int index = 0; index < groups.Length; ++index)
    {
      if (!string.IsNullOrEmpty(groups[index]))
        path1 = Path.Combine(path1, groups[index]);
    }
    return Path.Combine(path1, fileName);
  }

  /// <summary>返回服务器的绝对路径，包含根目录的信息  [Root Dir][A][B][C]... 信息</summary>
  /// <param name="groups">文件的路径分类信息</param>
  /// <returns>是否成功的结果对象</returns>
  private string ReturnAbsoluteFilePath(string[] groups)
  {
    return Path.Combine(this.filesDirectoryPath, Path.Combine(groups));
  }

  /// <summary>
  /// 返回服务器的绝对路径，包含根目录的信息  [Root Dir][A][B][C]...[FileName] 信息
  /// </summary>
  /// <param name="groups">路径分类信息</param>
  /// <param name="fileName">文件名</param>
  /// <returns>是否成功的结果对象</returns>
  protected string ReturnAbsoluteFileName(string[] groups, string fileName)
  {
    return Path.Combine(this.ReturnAbsoluteFilePath(groups), fileName);
  }

  /// <summary>
  /// 根据文件的显示名称转化为真实存储的名称，例如 123.txt 获取到在文件服务器里映射的文件名称，例如返回 b35a11ec533147ca80c7f7d1713f015b7909
  /// </summary>
  /// <param name="groups">文件的分类信息</param>
  /// <param name="fileName">文件显示名称</param>
  /// <returns>是否成功的结果对象</returns>
  private string TransformFactFileName(string[] groups, string fileName)
  {
    return this.GetGroupFromFilePath(this.ReturnAbsoluteFilePath(groups)).GetCurrentFileMappingName(fileName);
  }

  /// <summary>
  /// 获取当前目录的文件列表管理容器，如果没有会自动创建，通过该容器可以实现对当前目录的文件进行访问<br />
  /// Get the file list management container of the current directory. If not, it will be created automatically.
  /// Through this container, you can access files in the current directory.
  /// </summary>
  /// <param name="filePath">路径信息</param>
  /// <param name="create">是否创建容器，当没有发现该路径的容器的情况</param>
  /// <returns>文件管理容器信息</returns>
  private GroupFileContainer GetGroupFromFilePath(string filePath, bool create = true)
  {
    GroupFileContainer groupFromFilePath = (GroupFileContainer) null;
    filePath = filePath.ToUpper();
    this.group_marks_lock.Enter();
    if (this.m_dictionary_group_marks.ContainsKey(filePath))
      groupFromFilePath = this.m_dictionary_group_marks[filePath];
    else if (create)
    {
      groupFromFilePath = new GroupFileContainer(this.LogNet, filePath);
      this.m_dictionary_group_marks.Add(filePath, groupFromFilePath);
    }
    this.group_marks_lock.Leave();
    return groupFromFilePath;
  }

  /// <summary>
  /// 彻底删除当前的目录信息，整个目录及其所有的子文件夹，子文件信息<br />
  /// Completely delete the current directory information, the entire directory and all its subfolders, subfile information
  /// </summary>
  /// <param name="filePath">文件路径信息</param>
  private void DeleteGroupFileContainer(string filePath)
  {
    filePath = filePath.ToUpper();
    this.group_marks_lock.Enter();
    if (this.m_dictionary_group_marks.ContainsKey(filePath))
    {
      this.m_dictionary_group_marks[filePath].DeleteFolder();
      this.m_dictionary_group_marks.Remove(filePath);
    }
    this.group_marks_lock.Leave();
  }

  /// <summary>重命名当前的目录文件信息，如果修改后的名称还是存在，则返回错误信息</summary>
  /// <param name="filePath">旧的路径信息</param>
  /// <param name="newPath">新的路径信息</param>
  /// <returns>是否成功</returns>
  private OperateResult RenameGroupFileContainer(string filePath, string newPath)
  {
    filePath = filePath.ToUpper();
    string upper = newPath.ToUpper();
    OperateResult operateResult = OperateResult.CreateSuccessResult();
    this.group_marks_lock.Enter();
    if (this.m_dictionary_group_marks.ContainsKey(upper) || Directory.Exists(upper))
      operateResult = new OperateResult("修改的目录名称已经存在！");
    else if (this.m_dictionary_group_marks.ContainsKey(filePath))
    {
      GroupFileContainer dictionaryGroupMark = this.m_dictionary_group_marks[filePath];
      dictionaryGroupMark.RenameFolder(newPath);
      this.m_dictionary_group_marks.Remove(filePath);
      this.m_dictionary_group_marks.Add(upper, dictionaryGroupMark);
    }
    this.group_marks_lock.Leave();
    return operateResult;
  }

  /// <summary>
  /// 根据路径信息获取到文件列表管理容器，如果返回空，表示不存在。<br />
  /// The file list management container is obtained according to the path information. If the return is empty, it means that it does not exist.
  /// </summary>
  /// <param name="groups">文件路径信息</param>
  /// <returns>文件列表管理容器</returns>
  public GroupFileContainer GetGroupFromFilePath(string[] groups)
  {
    return this.GetGroupFromFilePath(this.ReturnAbsoluteFilePath(groups), false);
  }

  /// <summary>获取文件夹的所有文件夹列表</summary>
  /// <param name="groups">分类信息</param>
  /// <returns>文件夹列表</returns>
  private string[] GetDirectories(string[] groups)
  {
    if (string.IsNullOrEmpty(this.filesDirectoryPath))
      return new string[0];
    string path = this.ReturnAbsoluteFilePath(groups);
    return !Directory.Exists(path) ? new string[0] : Directory.GetDirectories(path);
  }

  /// <summary>
  /// 获取当前文件的读写锁，如果没有会自动创建，文件名应该是guid文件名，例如 b35a11ec533147ca80c7f7d1713f015b7909<br />
  /// Acquire the read-write lock of the current file. If not, it will be created automatically.
  /// The file name should be the guid file name, for example, b35a11ec533147ca80c7f7d1713f015b7909
  /// </summary>
  /// <param name="fileName">完整的文件路径</param>
  /// <returns>返回携带文件信息的读写锁</returns>
  private FileMarkId GetFileMarksFromDictionaryWithFileName(string fileName)
  {
    FileMarkId dictionaryWithFileName;
    lock (this.dictHybirdLock)
    {
      if (this.dictionaryFilesMarks.ContainsKey(fileName))
      {
        dictionaryWithFileName = this.dictionaryFilesMarks[fileName];
      }
      else
      {
        dictionaryWithFileName = new FileMarkId(this.LogNet, fileName);
        this.dictionaryFilesMarks.Add(fileName, dictionaryWithFileName);
      }
    }
    return dictionaryWithFileName;
  }

  /// <summary>检查文件夹是否存在，不存在就创建</summary>
  private void CheckFolderAndCreate()
  {
    if (Directory.Exists(this.filesDirectoryPath))
      return;
    Directory.CreateDirectory(this.filesDirectoryPath);
  }

  /// <summary>
  /// 删除已经存在的文件信息，文件的名称需要是guid名称，例如 b35a11ec533147ca80c7f7d1713f015b7909
  /// </summary>
  /// <param name="path">文件的路径</param>
  /// <param name="fileName">文件的guid名称，例如 b35a11ec533147ca80c7f7d1713f015b7909</param>
  private void DeleteExsistingFile(string path, string fileName)
  {
    this.DeleteExsistingFile(path, new List<string>()
    {
      fileName
    });
  }

  /// <summary>
  /// 删除已经存在的文件信息，文件的名称需要是guid名称，例如 b35a11ec533147ca80c7f7d1713f015b7909
  /// </summary>
  /// <param name="path">文件的路径</param>
  /// <param name="fileNames">文件的guid名称，例如 b35a11ec533147ca80c7f7d1713f015b7909</param>
  private void DeleteExsistingFile(string path, List<string> fileNames)
  {
    foreach (string fileName in fileNames)
    {
      if (!string.IsNullOrEmpty(fileName))
      {
        string fileUltimatePath = Path.Combine(path, fileName);
        this.GetFileMarksFromDictionaryWithFileName(fileName).AddOperation((Action) (() =>
        {
          if (!this.DeleteFileByName(fileUltimatePath))
            this.LogNet?.WriteInfo(this.ToString(), StringResources.Language.FileDeleteFailed + fileUltimatePath);
          else
            this.LogNet?.WriteInfo(this.ToString(), StringResources.Language.FileDeleteSuccess + fileUltimatePath);
        }));
      }
    }
  }

  /// <summary>
  /// 文件变化的事件，当文件上传的时候，文件下载的时候，文件被删除的时候触发。<br />
  /// The file change event is triggered when the file is uploaded, when the file is downloaded, or when the file is deleted.
  /// </summary>
  public event MqttServer.FileChangedDelegate OnFileChangedEvent;

  private void AddMqttSession(MqttSession session)
  {
    lock (this.sessionsLock)
      this.mqttSessions.Add(session);
    this.LogNet?.WriteDebug(this.ToString(), $"{session} Online");
  }

  /// <summary>让MQTT客户端正常下线，调用本方法即可自由控制会话客户端强制下线操作。</summary>
  /// <param name="session">当前的会话信息</param>
  /// <param name="reason">当前下线的原因，如果没有，代表正常下线</param>
  public void RemoveAndCloseSession(MqttSession session, string reason)
  {
    bool flag = false;
    lock (this.sessionsLock)
      flag = this.mqttSessions.Remove(session);
    if (!flag)
      return;
    NetSupport.CloseSocket(session.MqttPipe.Socket);
    this.LogNet?.WriteDebug(this.ToString(), $"{session} Offline {reason}");
    if (session.Protocol == "MQTT")
    {
      MqttServer.OnClientConnectedDelegate clientDisConnected = this.OnClientDisConnected;
      if (clientDisConnected != null)
        clientDisConnected(session);
      if (!string.IsNullOrEmpty(reason) && !string.IsNullOrEmpty(session.WillTopic))
        this.PublishTopicPayload(session.WillTopic, session.WillMessage);
    }
  }

  /// <summary>
  ///  当收到客户端发来的<see cref="T:HslCommunication.MQTT.MqttClientApplicationMessage" />消息时触发<br />
  ///  Triggered when a <see cref="T:HslCommunication.MQTT.MqttClientApplicationMessage" /> message is received from the client
  /// </summary>
  public event MqttServer.OnClientApplicationMessageReceiveDelegate OnClientApplicationMessageReceive;

  /// <summary>
  /// Mqtt的客户端连接上来时触发<br />
  /// Triggered when Mqtt client connects
  /// </summary>
  public event MqttServer.OnClientConnectedDelegate OnClientConnected;

  /// <summary>
  /// Mqtt的客户端下线时触发<br />
  /// Triggered when Mqtt client connects
  /// </summary>
  public event MqttServer.OnClientConnectedDelegate OnClientDisConnected;

  /// <summary>
  /// 当客户端连接时，触发的验证事件<br />
  /// Validation event triggered when the client connects
  /// </summary>
  public event MqttServer.ClientVerificationDelegate ClientVerification;

  /// <inheritdoc cref="P:HslCommunication.Enthernet.HttpServer.LogStatistics" />
  public LogStatisticsDict LogStatistics => this.statisticsDict;

  /// <summary>
  /// 获取或设置是否启用订阅主题通配符的功能，默认为 False<br />
  /// Gets or sets whether to enable the function of subscribing to the topic wildcard, the default is False
  /// </summary>
  /// <remarks>
  /// 启动之后，通配符示例：finance/stock/ibm/#; finance/+; '#' 是匹配所有主题，'+' 是匹配一级主题树。<br />
  /// 通配符的规则参考如下的网址：http://public.dhe.ibm.com/software/dw/webservices/ws-mqtt/mqtt-v3r1.html#appendix-a
  /// </remarks>
  public bool TopicWildcard
  {
    get => this.topicWildcard;
    set => this.topicWildcard = value;
  }

  /// <summary>
  /// 获取当前的在线的客户端数量<br />
  /// Gets the number of clients currently online
  /// </summary>
  public int OnlineCount => this.mqttSessions.Count;

  /// <summary>
  /// 获得当前所有的在线的MQTT客户端信息，包括异步的客户端及同步请求的客户端。<br />
  /// Obtain all current online MQTT client information, including asynchronous client and synchronous request client.
  /// </summary>
  public MqttSession[] OnlineSessions
  {
    get
    {
      MqttSession[] onlineSessions = (MqttSession[]) null;
      lock (this.sessionsLock)
        onlineSessions = this.mqttSessions.ToArray();
      return onlineSessions;
    }
  }

  /// <summary>
  /// 获得当前异步客户端在线的MQTT客户端信息。<br />
  /// Get the MQTT client information of the current asynchronous client online.
  /// </summary>
  public MqttSession[] MqttOnlineSessions
  {
    get
    {
      MqttSession[] mqttOnlineSessions = (MqttSession[]) null;
      lock (this.sessionsLock)
        mqttOnlineSessions = this.mqttSessions.Where<MqttSession>((Func<MqttSession, bool>) (m => m.Protocol == "MQTT")).ToArray<MqttSession>();
      return mqttOnlineSessions;
    }
  }

  /// <summary>
  /// 获得当前同步客户端在线的MQTT客户端信息，如果客户端是短连接，将难以捕获在在线信息。<br />
  /// Obtain the MQTT client information of the current synchronization client online. If the client is a short connection, it will be difficult to capture the online information. <br />
  /// </summary>
  public MqttSession[] SyncOnlineSessions
  {
    get
    {
      MqttSession[] syncOnlineSessions = (MqttSession[]) null;
      lock (this.sessionsLock)
        syncOnlineSessions = this.mqttSessions.Where<MqttSession>((Func<MqttSession, bool>) (m => m.Protocol == "HUSL")).ToArray<MqttSession>();
      return syncOnlineSessions;
    }
  }

  /// <summary>
  /// 获取或设置当前的主题统计信息，默认按照天进行统计，连续统计30天。<br />
  /// You can obtain or set the statistics of the current topic. By default, the statistics are collected every day for 30 consecutive days.
  /// </summary>
  public HslCommunication.LogNet.LogStatistics TopicStatistics
  {
    get => this.topicStatistics;
    set => this.topicStatistics = value;
  }

  /// <summary>
  /// 删除服务器里的指定主题的驻留消息。<br />
  /// Delete the resident message of the specified topic in the server.
  /// </summary>
  /// <param name="topic">等待删除的主题关键字</param>
  public void DeleteRetainTopic(string topic)
  {
    lock (this.keysLock)
    {
      if (!this.retainKeys.ContainsKey(topic))
        return;
      this.retainKeys.Remove(topic);
    }
  }

  /// <summary>
  /// 获取所有的驻留的消息的主题，如果消息发布的时候没有使用Retain属性，就无法通过本方法查到<br />
  /// Get the subject of all resident messages. If the Retain attribute is not used when the message is published, it cannot be found by this method
  /// </summary>
  /// <returns>主题的数组</returns>
  public string[] GetAllRetainTopics()
  {
    string[] allRetainTopics = (string[]) null;
    lock (this.keysLock)
      allRetainTopics = this.retainKeys.Select<KeyValuePair<string, MqttClientApplicationMessage>, string>((Func<KeyValuePair<string, MqttClientApplicationMessage>, string>) (m => m.Key)).ToArray<string>();
    return allRetainTopics;
  }

  /// <summary>
  /// 获取订阅了某个主题的所有的会话列表信息<br />
  /// Get all the conversation list information subscribed to a topic
  /// </summary>
  /// <param name="topic">主题信息</param>
  /// <returns>会话列表</returns>
  public MqttSession[] GetMqttSessionsByTopic(string topic)
  {
    MqttSession[] mqttSessionsByTopic = (MqttSession[]) null;
    lock (this.sessionsLock)
      mqttSessionsByTopic = this.mqttSessions.Where<MqttSession>((Func<MqttSession, bool>) (m => m.Protocol == "MQTT" && m.IsClientSubscribe(topic, this.topicWildcard))).ToArray<MqttSession>();
    return mqttSessionsByTopic;
  }

  /// <summary>释放当前的对象</summary>
  /// <param name="disposing"></param>
  protected virtual void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (disposing)
    {
      this.timerHeart?.Dispose();
      this.group_marks_lock?.Dispose();
      this.ClientVerification = (MqttServer.ClientVerificationDelegate) null;
      this.FileOperateVerification = (MqttServer.FileOperateVerificationDelegate) null;
      this.OnClientApplicationMessageReceive = (MqttServer.OnClientApplicationMessageReceiveDelegate) null;
      this.OnClientConnected = (MqttServer.OnClientConnectedDelegate) null;
      this.OnClientDisConnected = (MqttServer.OnClientConnectedDelegate) null;
      this.OnFileChangedEvent = (MqttServer.FileChangedDelegate) null;
    }
    this.disposedValue = true;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    this.Dispose(true);
    GC.SuppressFinalize((object) this);
  }

  /// <summary>
  /// 删除一个指定的文件，如果文件不存在，直接返回 <c>True</c>，如果文件存在则直接删除，删除成功返回 <c>True</c>，如果发生了异常，返回<c>False</c><br />
  /// Delete a specified file, if the file does not exist, return <c>True</c> directly, if the file exists, delete it directly,
  /// if the deletion is successful, return <c>True</c>, if an exception occurs, return <c> False</c>
  /// </summary>
  /// <param name="fileName">完整的文件路径</param>
  /// <returns>是否删除成功</returns>
  protected bool DeleteFileByName(string fileName)
  {
    try
    {
      if (!System.IO.File.Exists(fileName))
        return true;
      System.IO.File.Delete(fileName);
      return true;
    }
    catch (Exception ex)
    {
      this.LogNet?.WriteException(this.ToString(), $"delete file [{fileName}] failed: ", ex);
      return false;
    }
  }

  /// <inheritdoc />
  public override string ToString() => $"MqttServer[{this.Port}]";

  /// <summary>
  /// 当客户端进行文件操作时，校验客户端合法性的委托，操作码具体查看<seealso cref="T:HslCommunication.MQTT.MqttControlMessage" />的常量值<br />
  /// When client performing file operations, verify the legitimacy of the client, and check the constant value of <seealso cref="T:HslCommunication.MQTT.MqttControlMessage" /> for the operation code.
  /// </summary>
  /// <param name="session">会话状态</param>
  /// <param name="code">操作码</param>
  /// <param name="groups">分类信息</param>
  /// <param name="fileNames">文件名</param>
  /// <returns>是否成功</returns>
  public delegate OperateResult FileOperateVerificationDelegate(
    MqttSession session,
    byte code,
    string[] groups,
    string[] fileNames);

  /// <summary>文件变化的委托信息</summary>
  /// <param name="session">当前的会话信息，包含用户的基本信息</param>
  /// <param name="operateInfo">当前的文件操作信息，具体指示上传，下载，删除操作</param>
  public delegate void FileChangedDelegate(MqttSession session, MqttFileOperateInfo operateInfo);

  /// <summary>Mqtt的消息收到委托</summary>
  /// <param name="session">当前会话的内容</param>
  /// <param name="message">Mqtt的消息</param>
  public delegate void OnClientApplicationMessageReceiveDelegate(
    MqttSession session,
    MqttClientApplicationMessage message);

  /// <summary>当前mqtt客户端连接上服务器的事件委托</summary>
  /// <param name="session">当前的会话对象</param>
  public delegate void OnClientConnectedDelegate(MqttSession session);

  /// <summary>验证的委托</summary>
  /// <param name="mqttSession">当前的MQTT的会话内容</param>
  /// <param name="clientId">客户端的id</param>
  /// <param name="userName">用户名</param>
  /// <param name="password">密码</param>
  /// <returns>0则是通过，否则，就是连接失败</returns>
  public delegate int ClientVerificationDelegate(
    MqttSession mqttSession,
    string clientId,
    string userName,
    string password);
}
