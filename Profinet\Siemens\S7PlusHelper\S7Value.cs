﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.S7PlusHelper.S7Value
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System;
using System.IO;

#nullable disable
namespace HslCommunication.Profinet.Siemens.S7PlusHelper;

/// <summary>S7对象的类，表示一个对象信息</summary>
public class S7Value
{
  /// <summary>类型代号</summary>
  public byte TypeCode { get; set; }

  /// <summary>内存数据</summary>
  public byte[] Buffer { get; set; }

  /// <summary>值对象</summary>
  public object Value { get; set; }

  /// <summary>标记信息</summary>
  public byte Flag { get; set; }

  /// <summary>关联的结构体的标识信息</summary>
  public uint StructID { get; set; }

  internal static byte[] GetBufferBool(IByteTransform byteTransform, bool value)
  {
    return new byte[3]
    {
      (byte) 0,
      (byte) 1,
      !value ? (byte) 0 : (byte) 1
    };
  }

  internal static byte[] GetBufferInt8(IByteTransform byteTransform, sbyte value)
  {
    return new byte[3]{ (byte) 0, (byte) 6, (byte) value };
  }

  internal static byte[] GetBufferUInt8(IByteTransform byteTransform, byte value)
  {
    return new byte[3]{ (byte) 0, (byte) 10, value };
  }

  internal static byte[] GetBufferUInt8(IByteTransform byteTransform, byte[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 10);
    S7Object.WriteUint32(ms, (uint) value.Length);
    ms.Write(value);
    return ms.ToArray();
  }

  internal static byte[] GetBufferInt16(IByteTransform byteTransform, short value)
  {
    byte[] bufferInt16 = new byte[4]
    {
      (byte) 0,
      (byte) 7,
      (byte) 0,
      (byte) 0
    };
    byteTransform.TransByte(value).CopyTo((Array) bufferInt16, 2);
    return bufferInt16;
  }

  internal static byte[] GetBufferInt16(IByteTransform byteTransform, short[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 7);
    S7Object.WriteUint32(ms, (uint) value.Length);
    ms.Write(byteTransform.TransByte(value));
    return ms.ToArray();
  }

  internal static byte[] GetBufferUInt16(IByteTransform byteTransform, ushort value)
  {
    byte[] bufferUint16 = new byte[4]
    {
      (byte) 0,
      (byte) 11,
      (byte) 0,
      (byte) 0
    };
    byteTransform.TransByte(value).CopyTo((Array) bufferUint16, 2);
    return bufferUint16;
  }

  internal static byte[] GetBufferUInt16(IByteTransform byteTransform, ushort[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 11);
    S7Object.WriteUint32(ms, (uint) value.Length);
    ms.Write(byteTransform.TransByte(value));
    return ms.ToArray();
  }

  internal static byte[] GetBufferInt32(IByteTransform byteTransform, int value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 8);
    S7Object.WriteUint32(ms, (uint) value);
    return ms.ToArray();
  }

  internal static byte[] GetBufferInt32(IByteTransform byteTransform, int[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 8);
    S7Object.WriteUint32(ms, (uint) value.Length);
    for (int index = 0; index < value.Length; ++index)
      S7Object.WriteUint32(ms, (uint) value[index]);
    return ms.ToArray();
  }

  internal static byte[] GetBufferUInt32(IByteTransform byteTransform, uint value)
  {
    byte[] bufferUint32 = new byte[6];
    bufferUint32[1] = (byte) 12;
    byteTransform.TransByte(value).CopyTo((Array) bufferUint32, 2);
    return bufferUint32;
  }

  internal static byte[] GetBufferUInt32(IByteTransform byteTransform, uint[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 12);
    S7Object.WriteUint32(ms, (uint) value.Length);
    ms.Write(byteTransform.TransByte(value));
    return ms.ToArray();
  }

  internal static byte[] GetBufferInt64(IByteTransform byteTransform, long value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 0);
    ms.WriteByte((byte) 9);
    S7Object.WriteUint64(ms, (ulong) value);
    return ms.ToArray();
  }

  internal static byte[] GetBufferInt64(IByteTransform byteTransform, long[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 9);
    S7Object.WriteUint32(ms, (uint) value.Length);
    for (int index = 0; index < value.Length; ++index)
      S7Object.WriteUint64(ms, (ulong) value[index]);
    return ms.ToArray();
  }

  internal static byte[] GetBufferUInt64(IByteTransform byteTransform, ulong value)
  {
    byte[] bufferUint64 = new byte[10];
    bufferUint64[1] = (byte) 13;
    byteTransform.TransByte(value).CopyTo((Array) bufferUint64, 2);
    return bufferUint64;
  }

  internal static byte[] GetBufferUInt64(IByteTransform byteTransform, ulong[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 13);
    S7Object.WriteUint32(ms, (uint) value.Length);
    ms.Write(byteTransform.TransByte(value));
    return ms.ToArray();
  }

  internal static byte[] GetBufferFloat(IByteTransform byteTransform, float value)
  {
    byte[] bufferFloat = new byte[6];
    bufferFloat[1] = (byte) 14;
    byteTransform.TransByte(value).CopyTo((Array) bufferFloat, 2);
    return bufferFloat;
  }

  internal static byte[] GetBufferFloat(IByteTransform byteTransform, float[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 14);
    S7Object.WriteUint32(ms, (uint) value.Length);
    ms.Write(byteTransform.TransByte(value));
    return ms.ToArray();
  }

  internal static byte[] GetBufferDouble(IByteTransform byteTransform, double value)
  {
    byte[] bufferDouble = new byte[10];
    bufferDouble[1] = (byte) 15;
    byteTransform.TransByte(value).CopyTo((Array) bufferDouble, 2);
    return bufferDouble;
  }

  internal static byte[] GetBufferDouble(IByteTransform byteTransform, double[] value)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 15);
    S7Object.WriteUint32(ms, (uint) value.Length);
    ms.Write(byteTransform.TransByte(value));
    return ms.ToArray();
  }
}
