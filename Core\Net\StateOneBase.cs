﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Net.StateOneBase
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Threading;

#nullable disable
namespace HslCommunication.Core.Net;

/// <summary>异步消息的对象</summary>
internal class StateOneBase
{
  /// <summary>本次接收或是发送的数据长度</summary>
  public int DataLength { get; set; } = 32 /*0x20*/;

  /// <summary>已经处理的字节长度</summary>
  public int AlreadyDealLength { get; set; }

  /// <summary>操作完成的信号</summary>
  public ManualResetEvent WaitDone { get; set; }

  /// <summary>缓存器</summary>
  public byte[] Buffer { get; set; }

  /// <summary>是否发生了错误</summary>
  public bool IsError { get; set; }

  /// <summary>错误消息</summary>
  public string ErrerMsg { get; set; }
}
