﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.SiemensS7Server
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Profinet.Siemens.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>
/// 西门子S7协议的虚拟服务器，支持TCP协议，模拟的是1200的PLC进行通信，在客户端进行操作操作的时候，最好是选择1200的客户端对象进行通信。<br />
/// The virtual server of Siemens S7 protocol supports TCP protocol. It simulates 1200 PLC for communication. When the client is operating, it is best to select the 1200 client object for communication.
/// </summary>
/// <remarks>
/// 本西门子的虚拟PLC仅限商业授权用户使用，感谢支持。
/// <note type="important">对于200smartPLC的V区，就是DB1.X，例如，V100=DB1.100</note>
/// </remarks>
/// <example>
/// 地址支持的列表如下：
/// <list type="table">
///   <listheader>
///     <term>地址名称</term>
///     <term>地址代号</term>
///     <term>示例</term>
///     <term>地址进制</term>
///     <term>字操作</term>
///     <term>位操作</term>
///     <term>备注</term>
///   </listheader>
///   <item>
///     <term>中间寄存器</term>
///     <term>M</term>
///     <term>M100,M200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>√</term>
///     <term></term>
///   </item>
///   <item>
///     <term>输入寄存器</term>
///     <term>I</term>
///     <term>I100,I200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>√</term>
///     <term></term>
///   </item>
///   <item>
///     <term>输出寄存器</term>
///     <term>Q</term>
///     <term>Q100,Q200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>√</term>
///     <term></term>
///   </item>
///   <item>
///     <term>DB块寄存器</term>
///     <term>DB</term>
///     <term>DB1.100,DB1.200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>√</term>
///     <term></term>
///   </item>
///   <item>
///     <term>V寄存器</term>
///     <term>V</term>
///     <term>V100,V200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>√</term>
///     <term>V寄存器本质就是DB块1</term>
///   </item>
///   <item>
///     <term>定时器的值</term>
///     <term>T</term>
///     <term>T100,T200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>√</term>
///     <term>未测试通过</term>
///   </item>
///   <item>
///     <term>计数器的值</term>
///     <term>C</term>
///     <term>C100,C200</term>
///     <term>10</term>
///     <term>√</term>
///     <term>√</term>
///     <term>未测试通过</term>
///   </item>
/// </list>
/// 你可以很快速并且简单的创建一个虚拟的s7服务器
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7ServerExample.cs" region="UseExample1" title="简单的创建服务器" />
/// 当然如果需要高级的服务器，指定日志，限制客户端的IP地址，获取客户端发送的信息，在服务器初始化的时候就要参照下面的代码：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7ServerExample.cs" region="UseExample4" title="定制服务器" />
/// 服务器创建好之后，我们就可以对服务器进行一些读写的操作了，下面的代码是基础的BCL类型的读写操作。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7ServerExample.cs" region="ReadWriteExample" title="基础的读写示例" />
/// 高级的对于byte数组类型的数据进行批量化的读写操作如下：
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\SiemensS7ServerExample.cs" region="BytesReadWrite" title="字节的读写示例" />
/// 更高级操作请参见源代码。
/// </example>
public class SiemensS7Server : DeviceServer
{
  private SoftBuffer systemBuffer;
  private SoftBuffer inputBuffer;
  private SoftBuffer outputBuffer;
  private SoftBuffer memeryBuffer;
  private SoftBuffer countBuffer;
  private SoftBuffer timerBuffer;
  private SoftBuffer aiBuffer;
  private SoftBuffer aqBuffer;
  private Dictionary<int, SoftBuffer> dbBlockBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个S7协议的服务器，支持I，Q，M，DB1.X, DB2.X, DB3.X 数据区块的读写操作<br />
  /// Instantiate a server with S7 protocol, support I, Q, M, DB1.X data block read and write operations
  /// </summary>
  public SiemensS7Server()
  {
    this.inputBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.outputBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.memeryBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.countBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.timerBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.aiBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.aqBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.systemBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.systemBuffer.SetBytes("43 50 55 20 32 32 36 20 43 4E 20 20 20 20 20 20 30 32 30 31".ToHexBytes(), 0);
    this.WordLength = (ushort) 2;
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.dbBlockBuffer = new Dictionary<int, SoftBuffer>();
    this.dbBlockBuffer.Add(1, new SoftBuffer(65536 /*0x010000*/));
    this.dbBlockBuffer.Add(2, new SoftBuffer(65536 /*0x010000*/));
    this.dbBlockBuffer.Add(3, new SoftBuffer(65536 /*0x010000*/));
  }

  /// <summary>根据S7格式的地址，获取当前的数据缓存类对象</summary>
  /// <param name="s7Address">S7格式的数据地址信息</param>
  /// <returns>内存缓存对象</returns>
  private OperateResult<SoftBuffer> GetDataAreaFromS7Address(S7AddressData s7Address)
  {
    switch (s7Address.DataCode)
    {
      case 3:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.systemBuffer);
      case 6:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.aiBuffer);
      case 7:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.aqBuffer);
      case 30:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.countBuffer);
      case 31 /*0x1F*/:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.timerBuffer);
      case 129:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.inputBuffer);
      case 130:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.outputBuffer);
      case 131:
        return OperateResult.CreateSuccessResult<SoftBuffer>(this.memeryBuffer);
      case 132:
        return this.dbBlockBuffer.ContainsKey((int) s7Address.DbBlock) ? OperateResult.CreateSuccessResult<SoftBuffer>(this.dbBlockBuffer[(int) s7Address.DbBlock]) : new OperateResult<SoftBuffer>(10, StringResources.Language.SiemensError000A);
      default:
        return new OperateResult<SoftBuffer>(6, StringResources.Language.SiemensError0006);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address, length);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(from.Content);
    if (!areaFromS7Address.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) areaFromS7Address);
    return from.Content.DataCode == (byte) 30 || from.Content.DataCode == (byte) 31 /*0x1F*/ ? OperateResult.CreateSuccessResult<byte[]>(areaFromS7Address.Content.GetBytes(from.Content.AddressStart * 2, (int) length * 2)) : OperateResult.CreateSuccessResult<byte[]>(areaFromS7Address.Content.GetBytes(from.Content.AddressStart / 8, (int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(from.Content);
    if (!areaFromS7Address.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) areaFromS7Address);
    if (from.Content.DataCode == (byte) 30 || from.Content.DataCode == (byte) 31 /*0x1F*/)
      areaFromS7Address.Content.SetBytes(value, from.Content.AddressStart * 2);
    else
      areaFromS7Address.Content.SetBytes(value, from.Content.AddressStart / 8);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadByte(System.String)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Byte)" />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(from.Content);
    return !areaFromS7Address.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) areaFromS7Address) : OperateResult.CreateSuccessResult<bool[]>(areaFromS7Address.Content.GetBool(from.Content.AddressStart, (int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Net.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<S7AddressData> from = S7AddressData.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) from;
    OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(from.Content);
    if (!areaFromS7Address.IsSuccess)
      return (OperateResult) areaFromS7Address;
    areaFromS7Address.Content.SetBool(value, from.Content.AddressStart);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    return SiemensS7Helper.Write((IReadWriteNet) this, SiemensPLCS.S1200, address, value, encoding);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.WriteWString(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.String)" />
  [HslMqttApi(ApiTopic = "WriteWString", Description = "写入unicode编码的字符串，支持中文")]
  public OperateResult WriteWString(string address, string value)
  {
    return SiemensS7Helper.WriteWString((IReadWriteNet) this, SiemensPLCS.S1200, address, value);
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return length != (ushort) 0 ? base.ReadString(address, length, encoding) : this.ReadString(address, encoding);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Server.ReadString(System.String,System.Text.Encoding)" />
  [HslMqttApi("ReadS7String", "读取S7格式的字符串")]
  public OperateResult<string> ReadString(string address)
  {
    return this.ReadString(address, Encoding.ASCII);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.ReadString(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.Text.Encoding)" />
  public OperateResult<string> ReadString(string address, Encoding encoding)
  {
    return SiemensS7Helper.ReadString((IReadWriteNet) this, SiemensPLCS.S1200, address, encoding);
  }

  /// <summary>
  /// 读取西门子的地址的字符串信息，这个信息是和西门子绑定在一起，长度随西门子的信息动态变化的<br />
  /// Read the Siemens address string information. This information is bound to Siemens and its length changes dynamically with the Siemens information
  /// </summary>
  /// <param name="address">数据地址，具体的格式需要参照类的说明文档</param>
  /// <returns>带有是否成功的字符串结果类对象</returns>
  [HslMqttApi("ReadWString", "读取S7格式的双字节字符串")]
  public OperateResult<string> ReadWString(string address)
  {
    return SiemensS7Helper.ReadWString((IReadWriteNet) this, SiemensPLCS.S1200, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensS7Helper.Write(HslCommunication.Core.IReadWriteNet,HslCommunication.Profinet.Siemens.SiemensPLCS,System.String,System.String,System.Text.Encoding)" />
  public override async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    OperateResult operateResult = await SiemensS7Helper.WriteAsync((IReadWriteNet) this, SiemensPLCS.S1200, address, value, encoding);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Server.WriteWString(System.String,System.String)" />
  public async Task<OperateResult> WriteWStringAsync(string address, string value)
  {
    OperateResult operateResult = await SiemensS7Helper.WriteWStringAsync((IReadWriteNet) this, SiemensPLCS.S1200, address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<string> operateResult;
    if (length == (ushort) 0)
      operateResult = await this.ReadStringAsync(address, encoding);
    else
      operateResult = await base.ReadStringAsync(address, length, encoding);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Server.ReadString(System.String)" />
  public async Task<OperateResult<string>> ReadStringAsync(string address)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, Encoding.ASCII);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Server.ReadString(System.String,System.Text.Encoding)" />
  public async Task<OperateResult<string>> ReadStringAsync(string address, Encoding encoding)
  {
    OperateResult<string> operateResult = await SiemensS7Helper.ReadStringAsync((IReadWriteNet) this, SiemensPLCS.S1200, address, encoding);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.SiemensS7Server.ReadWString(System.String)" />
  public async Task<OperateResult<string>> ReadWStringAsync(string address)
  {
    OperateResult<string> operateResult = await SiemensS7Helper.ReadWStringAsync((IReadWriteNet) this, SiemensPLCS.S1200, address);
    return operateResult;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new S7Message();

  /// <inheritdoc />
  protected override OperateResult ThreadPoolLoginAfterClientCheck(
    PipeSession session,
    IPEndPoint endPoint)
  {
    if (this.IsNeedShakeHands())
    {
      CommunicationPipe communication = session.Communication;
      OperateResult<byte[]> message1 = communication.ReceiveMessage((INetMessage) new S7Message(), (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
      if (!message1.IsSuccess)
        return (OperateResult) message1;
      if (message1.Content == null || message1.Content.Length < 10)
        return new OperateResult("Data is too short: " + message1.Content.ToHexString(' '));
      message1.Content[5] = (byte) 208 /*0xD0*/;
      message1.Content[6] = message1.Content[8];
      message1.Content[7] = message1.Content[9];
      message1.Content[8] = (byte) 0;
      message1.Content[9] = (byte) 12;
      this.LogSendMessage(message1.Content, session);
      OperateResult operateResult1 = communication.Send(message1.Content);
      if (!operateResult1.IsSuccess)
        return operateResult1;
      OperateResult<byte[]> message2 = communication.ReceiveMessage((INetMessage) new S7Message(), (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
      if (!message2.IsSuccess)
        return (OperateResult) message2;
      byte[] bytes = SoftBasic.HexStringToBytes("03 00 00 1B 02 f0 80 32 03 00 00 00 00 00 08 00 00 00 00 f0 01 00 01 00 f0 00 f0");
      if (message2.Content.Length > 14)
      {
        bytes[11] = message2.Content[11];
        bytes[12] = message2.Content[12];
      }
      this.LogSendMessage(bytes, session);
      OperateResult operateResult2 = communication.Send(bytes);
      if (!operateResult2.IsSuccess)
        return operateResult2;
    }
    return base.ThreadPoolLoginAfterClientCheck(session, endPoint);
  }

  /// <summary>获取是否需要进行握手报文信息</summary>
  /// <returns>是否需要进行握手操作</returns>
  protected virtual bool IsNeedShakeHands() => true;

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    try
    {
      byte[] numArray;
      if (receive[17] == (byte) 4)
        numArray = this.ReadByMessage(receive);
      else if (receive[17] == (byte) 5)
      {
        numArray = this.WriteByMessage(receive);
      }
      else
      {
        if (receive[17] != (byte) 0)
          return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
        numArray = this.ReadPlcType();
      }
      receive.SelectMiddle<byte>(11, 2).CopyTo((Array) numArray, 11);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  private byte[] ReadPlcType()
  {
    return SoftBasic.HexStringToBytes("03 00 00 7D 02 F0 80 32 07 00 00 00 01 00 0C 00 60 00 01 12 08 12 84 01 01 00 00 00 00 FF 09 00 5C 00 11 00 00 00 1C 00 03 00 01 36 45 53 37 20 32 31 35 2D 31 41 47 34 30 2D 30 58 42 30 20 00 00 00 06 20 20 00 06 36 45 53 37 20 32 31 35 2D 31 41 47 34 30 2D 30 58 42 30 20 00 00 00 06 20 20 00 07 36 45 53 37 20 32 31 35 2D 31 41 47 34 30 2D 30 58 42 30 20 00 00 56 04 02 01");
  }

  /// <summary>将读取的结果数据内容进行打包，返回客户端读取</summary>
  /// <param name="command">接收的命令信息</param>
  /// <param name="content">读取的原始字节信息</param>
  /// <returns>返回客户端的原始报文信息</returns>
  protected virtual byte[] PackReadBack(byte[] command, List<byte> content)
  {
    if (content.Count > 226)
      content = new List<byte>((IEnumerable<byte>) this.PackReadWordCommandBack((short) 5, (byte[]) null));
    byte[] array = new byte[21 + content.Count];
    SoftBasic.HexStringToBytes("03 00 00 1A 02 F0 80 32 03 00 00 00 01 00 02 00 05 00 00 04 01").CopyTo((Array) array, 0);
    array[2] = (byte) (array.Length / 256 /*0x0100*/);
    array[3] = (byte) (array.Length % 256 /*0x0100*/);
    array[15] = (byte) (content.Count / 256 /*0x0100*/);
    array[16 /*0x10*/] = (byte) (content.Count % 256 /*0x0100*/);
    array[20] = command[18];
    content.CopyTo(array, 21);
    return array;
  }

  private byte[] ReadByMessage(byte[] packCommand)
  {
    List<byte> content = new List<byte>();
    int num1 = (int) packCommand[18];
    int index1 = 19;
    for (int index2 = 0; index2 < num1; ++index2)
    {
      byte num2 = packCommand[index1 + 1];
      byte[] command = packCommand.SelectMiddle<byte>(index1, (int) num2 + 2);
      index1 += (int) num2 + 2;
      content.AddRange((IEnumerable<byte>) this.ReadByCommand(command));
    }
    return this.PackReadBack(packCommand, content);
  }

  private byte[] ReadByCommand(byte[] command)
  {
    if (command[3] == (byte) 1)
    {
      int destIndex = (int) command[9] * 65536 /*0x010000*/ + (int) command[10] * 256 /*0x0100*/ + (int) command[11];
      ushort num = this.ByteTransform.TransUInt16(command, 6);
      this.ByteTransform.TransUInt16(command, 4);
      S7AddressData s7Address = new S7AddressData();
      s7Address.AddressStart = destIndex;
      s7Address.DataCode = command[8];
      s7Address.DbBlock = num;
      s7Address.Length = (ushort) 1;
      OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(s7Address);
      if (!areaFromS7Address.IsSuccess)
        throw new Exception(areaFromS7Address.Message);
      return this.PackReadBitCommandBack(areaFromS7Address.Content.GetBool(destIndex));
    }
    if (command[3] == (byte) 30 || command[3] == (byte) 31 /*0x1F*/)
    {
      ushort num1 = this.ByteTransform.TransUInt16(command, 4);
      int num2 = (int) command[9] * 65536 /*0x010000*/ + (int) command[10] * 256 /*0x0100*/ + (int) command[11];
      S7AddressData s7Address = new S7AddressData();
      s7Address.AddressStart = num2;
      s7Address.DataCode = command[8];
      s7Address.DbBlock = (ushort) 0;
      s7Address.Length = num1;
      OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(s7Address);
      if (!areaFromS7Address.IsSuccess)
        throw new Exception(areaFromS7Address.Message);
      return this.PackReadCTCommandBack(areaFromS7Address.Content.GetBytes(num2 * 2, (int) num1 * 2), command[3] == (byte) 30 ? 3 : 5);
    }
    ushort length = this.ByteTransform.TransUInt16(command, 4);
    if (command[3] == (byte) 4)
      length *= (ushort) 2;
    ushort num3 = this.ByteTransform.TransUInt16(command, 6);
    int index = ((int) command[9] * 65536 /*0x010000*/ + (int) command[10] * 256 /*0x0100*/ + (int) command[11]) / 8;
    S7AddressData s7Address1 = new S7AddressData();
    s7Address1.AddressStart = index;
    s7Address1.DataCode = command[8];
    s7Address1.DbBlock = num3;
    s7Address1.Length = length;
    OperateResult<SoftBuffer> areaFromS7Address1 = this.GetDataAreaFromS7Address(s7Address1);
    return !areaFromS7Address1.IsSuccess ? this.PackReadWordCommandBack((short) areaFromS7Address1.ErrorCode, (byte[]) null) : this.PackReadWordCommandBack((short) 0, areaFromS7Address1.Content.GetBytes(index, (int) length));
  }

  private byte[] PackReadWordCommandBack(short err, byte[] result)
  {
    if (err > (short) 0)
    {
      byte[] numArray = new byte[4];
      BitConverter.GetBytes(err).CopyTo((Array) numArray, 0);
      return numArray;
    }
    byte[] numArray1 = new byte[4 + result.Length];
    numArray1[0] = byte.MaxValue;
    numArray1[1] = (byte) 4;
    this.ByteTransform.TransByte((ushort) (result.Length * 8)).CopyTo((Array) numArray1, 2);
    result.CopyTo((Array) numArray1, 4);
    return numArray1;
  }

  private byte[] PackReadCTCommandBack(byte[] result, int dataLength)
  {
    byte[] numArray = new byte[4 + result.Length * dataLength / 2];
    numArray[0] = byte.MaxValue;
    numArray[1] = (byte) 9;
    this.ByteTransform.TransByte((ushort) (numArray.Length - 4)).CopyTo((Array) numArray, 2);
    for (int index = 0; index < result.Length / 2; ++index)
      result.SelectMiddle<byte>(index * 2, 2).CopyTo((Array) numArray, 4 + dataLength - 2 + index * dataLength);
    return numArray;
  }

  private byte[] PackReadBitCommandBack(bool value)
  {
    return new byte[5]
    {
      byte.MaxValue,
      (byte) 3,
      (byte) 0,
      (byte) 1,
      value ? (byte) 1 : (byte) 0
    };
  }

  private byte[] PackWriteBack(byte[] packCommand, byte status)
  {
    return this.PackWriteBack(packCommand, new byte[1]
    {
      status
    });
  }

  /// <summary>创建返回的报文信息</summary>
  /// <param name="packCommand">接收到的报文命令</param>
  /// <param name="status">返回的状态信息</param>
  /// <returns>返回的原始字节数组</returns>
  protected virtual byte[] PackWriteBack(byte[] packCommand, byte[] status)
  {
    byte[] numArray = new byte[21 + status.Length];
    SoftBasic.HexStringToBytes("03 00 00 16 02 F0 80 32 03 00 00 00 01 00 02 00 01 00 00 05 01").CopyTo((Array) numArray, 0);
    numArray[20] = (byte) status.Length;
    status.CopyTo((Array) numArray, 21);
    numArray[2] = BitConverter.GetBytes(numArray.Length)[1];
    numArray[3] = BitConverter.GetBytes(numArray.Length)[0];
    return numArray;
  }

  private byte[] WriteByMessage(byte[] packCommand)
  {
    if (!this.EnableWrite)
      return this.PackWriteBack(packCommand, (byte) 4);
    int length1 = (int) packCommand[18];
    byte[] status = new byte[length1];
    int num1 = 19 + length1 * 12;
    for (int index = 0; index < length1; ++index)
    {
      if (packCommand[22 + 12 * index] == (byte) 2 || packCommand[22 + 12 * index] == (byte) 4)
      {
        ushort num2 = this.ByteTransform.TransUInt16(packCommand, 25 + 12 * index);
        int length2 = (int) this.ByteTransform.TransInt16(packCommand, 23 + 12 * index);
        if (packCommand[22 + 12 * index] == (byte) 4)
          length2 *= 2;
        int destIndex = packCommand[27 + 12 * index] < (byte) 28 || packCommand[27 + 12 * index] > (byte) 31 /*0x1F*/ ? ((int) packCommand[28 + 12 * index] * 65536 /*0x010000*/ + (int) packCommand[29 + 12 * index] * 256 /*0x0100*/ + (int) packCommand[30 + 12 * index]) / 8 : ((int) packCommand[28 + 12 * index] * 65536 /*0x010000*/ + (int) packCommand[29 + 12 * index] * 256 /*0x0100*/ + (int) packCommand[30 + 12 * index]) * 2;
        byte[] data = this.ByteTransform.TransByte(packCommand, num1 + 4, length2);
        num1 += 4 + length2;
        if (index < length1 - 1 && length2 % 2 == 1)
          ++num1;
        S7AddressData s7Address = new S7AddressData();
        s7Address.DataCode = packCommand[27 + 12 * index];
        s7Address.DbBlock = num2;
        s7Address.Length = (ushort) 1;
        OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(s7Address);
        if (!areaFromS7Address.IsSuccess)
        {
          status[index] = (byte) areaFromS7Address.ErrorCode;
        }
        else
        {
          areaFromS7Address.Content.SetBytes(data, destIndex);
          status[index] = byte.MaxValue;
        }
      }
      else
      {
        ushort num3 = this.ByteTransform.TransUInt16(packCommand, 25 + 12 * index);
        int destIndex = (int) packCommand[28 + 12 * index] * 65536 /*0x010000*/ + (int) packCommand[29 + 12 * index] * 256 /*0x0100*/ + (int) packCommand[30 + 12 * index];
        bool flag = packCommand[num1 + 4] > (byte) 0;
        num1 += 5;
        if (index < length1 - 1)
          ++num1;
        S7AddressData s7Address = new S7AddressData();
        s7Address.DataCode = packCommand[27 + 12 * index];
        s7Address.DbBlock = num3;
        s7Address.Length = (ushort) 1;
        OperateResult<SoftBuffer> areaFromS7Address = this.GetDataAreaFromS7Address(s7Address);
        if (!areaFromS7Address.IsSuccess)
        {
          status[index] = (byte) areaFromS7Address.ErrorCode;
        }
        else
        {
          areaFromS7Address.Content.SetBool(flag, destIndex);
          status[index] = byte.MaxValue;
        }
      }
    }
    return this.PackWriteBack(packCommand, status);
  }

  /// <summary>
  /// 新增一个独立的DB块数据区，如果这个DB块已经存在，则新增无效。<br />
  /// Add a separate DB block data area, if the DB block already exists, the new one is invalid.
  /// </summary>
  /// <param name="db">DB块ID信息</param>
  public void AddDbBlock(int db)
  {
    if (this.dbBlockBuffer.ContainsKey(db))
      return;
    this.dbBlockBuffer.Add(db, new SoftBuffer(65536 /*0x010000*/));
  }

  /// <summary>
  /// 移除指定的DB块数据区，如果这个DB块不存在的话，操作无效，本方法无法移除1，2，3的DB块<br />
  /// Remove the specified DB block data area, if the DB block does not exist, the operation is invalid, and this method cannot remove the DB block of 1, 2, 3
  /// </summary>
  /// <param name="db">指定的ID信息</param>
  public void RemoveDbBlock(int db)
  {
    if (db == 1 || db == 2 || db == 3 || !this.dbBlockBuffer.ContainsKey(db))
      return;
    this.dbBlockBuffer.Remove(db);
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 458752 /*0x070000*/)
      throw new Exception("File is not correct");
    this.inputBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.outputBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.memeryBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.dbBlockBuffer[1].SetBytes(content, 196608 /*0x030000*/, 0, 65536 /*0x010000*/);
    this.dbBlockBuffer[2].SetBytes(content, 262144 /*0x040000*/, 0, 65536 /*0x010000*/);
    this.dbBlockBuffer[3].SetBytes(content, 327680 /*0x050000*/, 0, 65536 /*0x010000*/);
    if (content.Length < 720896 /*0x0B0000*/)
      return;
    this.countBuffer.SetBytes(content, 458752 /*0x070000*/, 0, 131072 /*0x020000*/);
    this.timerBuffer.SetBytes(content, 589824 /*0x090000*/, 0, 131072 /*0x020000*/);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[720896 /*0x0B0000*/];
    Array.Copy((Array) this.inputBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.outputBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.memeryBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dbBlockBuffer[1].GetBytes(), 0, (Array) destinationArray, 196608 /*0x030000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dbBlockBuffer[2].GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dbBlockBuffer[3].GetBytes(), 0, (Array) destinationArray, 327680 /*0x050000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.countBuffer.GetBytes(), 0, (Array) destinationArray, 458752 /*0x070000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.timerBuffer.GetBytes(), 0, (Array) destinationArray, 589824 /*0x090000*/, 131072 /*0x020000*/);
    return destinationArray;
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.systemBuffer?.Dispose();
      this.inputBuffer?.Dispose();
      this.outputBuffer?.Dispose();
      this.memeryBuffer?.Dispose();
      this.countBuffer?.Dispose();
      this.timerBuffer?.Dispose();
      foreach (SoftBuffer softBuffer in this.dbBlockBuffer.Values)
        softBuffer?.Dispose();
      this.aiBuffer?.Dispose();
      this.aqBuffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"SiemensS7Server[{this.Port}]";
}
