﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.DLT.DLT698TcpNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Instrument.DLT.Helper;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.DLT;

/// <summary>
/// 698.45协议的TCP通信类(不是串口透传通信)，面向对象的用电信息数据交换协议，使用明文的通信方式。支持读取功率，总功，电压，电流，频率，功率因数等数据。<br />
/// The TCP communication class of the 698.45 protocol (not the serial port transparent transmission communication), the object-oriented power consumption information data exchange protocol,
/// uses the clear text communication method. Support reading power, total power, voltage, current, frequency, power factor and other data.
/// </summary>
/// <remarks>
/// <inheritdoc cref="T:HslCommunication.Instrument.DLT.DLT698" path="remarks" />
/// </remarks>
/// <example>
/// <inheritdoc cref="T:HslCommunication.Instrument.DLT.DLT698" path="example" />
/// </example>
public class DLT698TcpNet : DLT698OverTcp, IDlt698, IReadWriteDevice, IReadWriteNet
{
  /// <inheritdoc cref="M:HslCommunication.Core.Net.BinaryCommunication.#ctor" />
  public DLT698TcpNet()
  {
  }

  /// <summary>
  /// 指定地址域，密码，操作者代码来实例化一个对象，密码及操作者代码在写入操作的时候进行验证<br />
  /// Specify the address field, password, and operator code to instantiate an object, and the password and operator code are validated during write operations,
  /// which address field is a 12-character BCD code, for example: 149100007290
  /// </summary>
  /// <param name="station">设备的地址信息，通常是一个12字符的BCD码</param>
  public DLT698TcpNet(string station = "AAAAAAAAAAAA")
    : base(station)
  {
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.DLT.DLT698OverTcp.#ctor(System.String,System.Int32,System.String)" />
  public DLT698TcpNet(string ipAddress, int port, string station = "AAAAAAAAAAAA")
    : base(ipAddress, port, station)
  {
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect()
  {
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(this.CommunicationPipe, DLT698Helper.BuildEntireCommand((byte) 129, this.Station, this.CA, DLT698TcpNet.CreateLoginApdu()).Content, true, true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(this.CommunicationPipe, DLT698Helper.BuildEntireCommand((byte) 129, this.Station, this.CA, DLT698TcpNet.CreateConnectApdu()).Content, true, true);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : base.InitializationOnConnect();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync()
  {
    OperateResult<byte[]> read1 = await this.ReadFromCoreServerAsync(this.CommunicationPipe, DLT698Helper.BuildEntireCommand((byte) 129, this.Station, this.CA, DLT698TcpNet.CreateLoginApdu()).Content, true, true);
    if (!read1.IsSuccess)
      return (OperateResult) read1;
    OperateResult<byte[]> read2 = await this.ReadFromCoreServerAsync(this.CommunicationPipe, DLT698Helper.BuildEntireCommand((byte) 129, this.Station, this.CA, DLT698TcpNet.CreateConnectApdu()).Content, true, true);
    if (!read2.IsSuccess)
      return (OperateResult) read2;
    OperateResult operateResult = await base.InitializationOnConnectAsync();
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"DLT698TcpNet[{this.IpAddress}:{this.Port}]";

  internal static byte[] CreateLoginApdu(byte services = 1, byte piid = 0, byte type = 0)
  {
    byte[] apdu = new byte[15];
    apdu[0] = services;
    apdu[1] = piid;
    apdu[2] = type;
    apdu[3] = (byte) 0;
    apdu[4] = (byte) 132;
    DLT698Helper.SetDltDataTime(apdu, 5, DateTime.Now);
    return apdu;
  }

  internal static byte[] CreateConnectApdu()
  {
    return "02 00 00 10 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF 04 00 04 00 01 04 00 00 00 00 64 00 00".ToHexBytes();
  }
}
