﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.HslHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Ports;
using System.Net;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// HslCommunication的一些静态辅助方法<br />
/// Some static auxiliary methods of HslCommunication
/// </summary>
public class HslHelper
{
  /// <summary>
  /// 本通讯项目的随机数信息<br />
  /// Random number information for this newsletter
  /// </summary>
  public static Random HslRandom { get; private set; } = new Random();

  /// <summary>
  /// 本通讯项目单个通信对象最多的锁累积次数，超过该次数，将直接返回失败。<br />
  /// The maximum number of lock accumulations for a single communication object in this communication item, beyond which the number will be returned as a failure.
  /// </summary>
  /// <remarks>默认为 1000 次</remarks>
  public static int LockLimit { get; set; } = 1000;

  /// <summary>
  /// 本通信库的单个通信对象在异步通信的时候是否使用异步锁，默认<b>True</b>，适用于winform，wpf等UI程序(有效防止UI上同时读写PLC时发生死锁问题)，如果是控制台程序或是纯后台线程采集的程序，适合配置<b>False</b><br />
  /// Whether a single communication object of this communication library uses asynchronous locks during asynchronous communication, the default is <b>True</b>, which is suitable for UI programs such as winform,
  /// wpf and so on (effectively preventing deadlock problems when reading and writing PLCs at the same time on the UI), and if it is a console program or a program collected by pure background threads, it is suitable to configure <b>False</b>
  /// </summary>
  public static bool UseAsyncLock { get; set; } = true;

  /// <summary>
  /// 解析地址的附加参数方法，比如你的地址是s=100;D100，可以提取出"s"的值的同时，修改地址本身，如果"s"不存在的话，返回给定的默认值<br />
  /// The method of parsing additional parameters of the address, for example, if your address is s=100;D100, you can extract the value of "s" and modify the address itself. If "s" does not exist, return the given default value
  /// </summary>
  /// <param name="address">复杂的地址格式，比如：s=100;D100</param>
  /// <param name="paraName">等待提取的参数名称</param>
  /// <param name="defaultValue">如果提取的参数信息不存在，返回的默认值信息</param>
  /// <returns>解析后的新的数据值或是默认的给定的数据值</returns>
  public static int ExtractParameter(ref string address, string paraName, int defaultValue)
  {
    OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, paraName);
    return parameter.IsSuccess ? parameter.Content : defaultValue;
  }

  /// <summary>
  /// 解析地址的附加Bool类型参数方法，比如你的地址是s=true;D100，可以提取出"s"的值的同时，修改地址本身，如果"s"不存在的话，返回给定的默认值<br />
  /// The method of parsing additional parameters of the address, for example, if your address is s=true;D100, you can extract the value of "s" and modify the address itself. If "s" does not exist, return the given default value
  /// </summary>
  /// <param name="address">复杂的地址格式，比如：s=true;D100</param>
  /// <param name="paraName">等待提取的参数名称</param>
  /// <param name="defaultValue">如果提取的参数信息不存在，返回的默认值信息</param>
  /// <returns>解析后的新的数据值或是默认的给定的数据值</returns>
  public static bool ExtractBooleanParameter(
    ref string address,
    string paraName,
    bool defaultValue)
  {
    OperateResult<bool> booleanParameter = HslHelper.ExtractBooleanParameter(ref address, paraName);
    return booleanParameter.IsSuccess ? booleanParameter.Content : defaultValue;
  }

  /// <summary>
  /// 解析地址的附加参数方法，比如你的地址是s=100;D100，可以提取出"s"的值的同时，修改地址本身，如果"s"不存在的话，返回错误的消息内容<br />
  /// The method of parsing additional parameters of the address, for example, if your address is s=100;D100, you can extract the value of "s" and modify the address itself.
  /// If "s" does not exist, return the wrong message content
  /// </summary>
  /// <param name="address">复杂的地址格式，比如：s=100;D100</param>
  /// <param name="paraName">等待提取的参数名称</param>
  /// <returns>解析后的参数结果内容</returns>
  public static OperateResult<int> ExtractParameter(ref string address, string paraName)
  {
    try
    {
      Match match = Regex.Match(address, paraName + "=[0-9A-Fa-fxX]+;", RegexOptions.IgnoreCase);
      if (!match.Success)
        return new OperateResult<int>($"Address [{address}] can't find [{paraName}] Parameters. for example : {paraName}=1;100");
      string str = match.Value.Substring(paraName.Length + 1, match.Value.Length - paraName.Length - 2);
      int num = str.StartsWith("0x") || str.StartsWith("0X") ? Convert.ToInt32(str.Substring(2), 16 /*0x10*/) : (str.StartsWith("0") ? Convert.ToInt32(str, 8) : Convert.ToInt32(str));
      address = address.Replace(match.Value, "");
      return OperateResult.CreateSuccessResult<int>(num);
    }
    catch (Exception ex)
    {
      return new OperateResult<int>($"Address [{address}] Get [{paraName}] Parameters failed: {ex.Message}");
    }
  }

  /// <summary>
  /// 解析地址的附加bool参数方法，比如你的地址是s=true;D100，可以提取出"s"的值的同时，修改地址本身，如果"s"不存在的话，返回错误的消息内容<br />
  /// The method of parsing additional parameters of the address, for example, if your address is s=true;D100, you can extract the value of "s" and modify the address itself.
  /// If "s" does not exist, return the wrong message content
  /// </summary>
  /// <param name="address">复杂的地址格式，比如：s=true;D100</param>
  /// <param name="paraName">等待提取的参数名称</param>
  /// <returns>解析后的参数结果内容</returns>
  public static OperateResult<bool> ExtractBooleanParameter(ref string address, string paraName)
  {
    try
    {
      Match match = Regex.Match(address, paraName + "=[0-1A-Za-z]+;");
      if (!match.Success)
        return new OperateResult<bool>($"Address [{address}] can't find [{paraName}] Parameters. for example : {paraName}=True;100");
      string input = match.Value.Substring(paraName.Length + 1, match.Value.Length - paraName.Length - 2);
      bool flag = !Regex.IsMatch(input, "^[0-1]+$") ? Convert.ToBoolean(input) : Convert.ToInt32(input) != 0;
      address = address.Replace(match.Value, "");
      return OperateResult.CreateSuccessResult<bool>(flag);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool>($"Address [{address}] Get [{paraName}] Parameters failed: {ex.Message}");
    }
  }

  /// <summary>
  /// 解析地址的起始地址的方法，比如你的地址是 A[1] , 那么将会返回 1，地址修改为 A，如果不存在起始地址，那么就不修改地址，返回 -1<br />
  /// The method of parsing the starting address of the address, for example, if your address is A[1], then it will return 1,
  /// and the address will be changed to A. If the starting address does not exist, then the address will not be changed and return -1
  /// </summary>
  /// <param name="address">复杂的地址格式，比如：A[0] </param>
  /// <returns>如果存在，就起始位置，不存在就返回 -1</returns>
  public static int ExtractStartIndex(ref string address)
  {
    try
    {
      Match match = Regex.Match(address, "\\[[0-9]+\\]$");
      if (!match.Success)
        return -1;
      int int32 = Convert.ToInt32(match.Value.Substring(1, match.Value.Length - 2));
      address = address.Remove(address.Length - match.Value.Length);
      return int32;
    }
    catch
    {
      return -1;
    }
  }

  /// <summary>
  /// 解析地址的附加<see cref="T:HslCommunication.Core.DataFormat" />参数方法，比如你的地址是format=ABCD;D100，可以提取出"format"的值的同时，修改地址本身，如果"format"不存在的话，返回默认的<see cref="T:HslCommunication.Core.IByteTransform" />对象<br />
  /// Parse the additional <see cref="T:HslCommunication.Core.DataFormat" /> parameter method of the address. For example, if your address is format=ABCD;D100,
  /// you can extract the value of "format" and modify the address itself. If "format" does not exist,
  /// Return the default <see cref="T:HslCommunication.Core.IByteTransform" /> object
  /// </summary>
  /// <param name="address">复杂的地址格式，比如：format=ABCD;D100</param>
  /// <param name="defaultTransform">默认的数据转换信息</param>
  /// <returns>解析后的参数结果内容</returns>
  public static IByteTransform ExtractTransformParameter(
    ref string address,
    IByteTransform defaultTransform)
  {
    try
    {
      string str1 = "format";
      Match match = Regex.Match(address, str1 + "=(ABCD|BADC|DCBA|CDAB);", RegexOptions.IgnoreCase);
      if (!match.Success)
        return defaultTransform;
      string str2 = match.Value.Substring(str1.Length + 1, match.Value.Length - str1.Length - 2);
      DataFormat dataFormat = defaultTransform.DataFormat;
      switch (str2.ToUpper())
      {
        case "ABCD":
          dataFormat = DataFormat.ABCD;
          break;
        case "BADC":
          dataFormat = DataFormat.BADC;
          break;
        case "DCBA":
          dataFormat = DataFormat.DCBA;
          break;
        case "CDAB":
          dataFormat = DataFormat.CDAB;
          break;
      }
      address = address.Replace(match.Value, "");
      return dataFormat != defaultTransform.DataFormat ? defaultTransform.CreateByDateFormat(dataFormat) : defaultTransform;
    }
    catch
    {
      throw;
    }
  }

  /// <summary>
  /// 切割当前的地址数据信息，根据读取的长度来分割成多次不同的读取内容，需要指定地址，总的读取长度，切割读取长度<br />
  /// Cut the current address data information, and divide it into multiple different read contents according to the read length.
  /// You need to specify the address, the total read length, and the cut read length
  /// </summary>
  /// <param name="address">整数的地址信息</param>
  /// <param name="length">读取长度信息</param>
  /// <param name="segment">切割长度信息</param>
  /// <returns>切割结果</returns>
  public static OperateResult<int[], int[]> SplitReadLength(int address, int length, int segment)
  {
    int[] array = SoftBasic.SplitIntegerToArray(length, segment);
    int[] numArray = new int[array.Length];
    for (int index = 0; index < numArray.Length; ++index)
      numArray[index] = index != 0 ? numArray[index - 1] + array[index - 1] : address;
    return OperateResult.CreateSuccessResult<int[], int[]>(numArray, array);
  }

  /// <summary>根据指定的长度切割数据数组，返回地址偏移量信息和数据分割信息</summary>
  /// <typeparam name="T">数组类型</typeparam>
  /// <param name="address">起始的地址</param>
  /// <param name="value">实际的数据信息</param>
  /// <param name="segment">分割的基本长度</param>
  /// <param name="addressLength">一个地址代表的数据长度</param>
  /// <returns>切割结果内容</returns>
  public static OperateResult<int[], List<T[]>> SplitWriteData<T>(
    int address,
    T[] value,
    ushort segment,
    int addressLength)
  {
    List<T[]> objArrayList = SoftBasic.ArraySplitByLength<T>(value, (int) segment * addressLength);
    int[] numArray = new int[objArrayList.Count];
    for (int index = 0; index < numArray.Length; ++index)
      numArray[index] = index != 0 ? numArray[index - 1] + objArrayList[index - 1].Length / addressLength : address;
    return OperateResult.CreateSuccessResult<int[], List<T[]>>(numArray, objArrayList);
  }

  /// <summary>获取地址信息的位索引，在地址最后一个小数点的位置</summary>
  /// <param name="address">地址信息</param>
  /// <returns>位索引的位置</returns>
  public static int GetBitIndexInformation(ref string address)
  {
    int indexInformation = 0;
    int length = address.LastIndexOf('.');
    if (length > 0 && length < address.Length - 1)
    {
      string str = address.Substring(length + 1);
      indexInformation = !str.Contains(new string[6]
      {
        "A",
        "B",
        "C",
        "D",
        "E",
        "F"
      }) ? Convert.ToInt32(str) : Convert.ToInt32(str, 16 /*0x10*/);
      address = address.Substring(0, length);
    }
    return indexInformation;
  }

  /// <summary>
  /// 从当前的字符串信息获取IP地址数据，如果是ip地址直接返回，如果是域名，会自动解析IP地址，否则抛出异常<br />
  /// Get the IP address data from the current string information, if it is an ip address, return directly,
  /// if it is a domain name, it will automatically resolve the IP address, otherwise an exception will be thrown
  /// </summary>
  /// <param name="value">输入的字符串信息</param>
  /// <returns>真实的IP地址信息</returns>
  public static string GetIpAddressFromInput(string value)
  {
    if (!string.IsNullOrEmpty(value))
    {
      if (!value.EndsWith(new string[6]
      {
        ".com",
        ".cn",
        ".net",
        ".top",
        ".vip",
        ".club"
      }) && IPAddress.TryParse(value, out IPAddress _))
        return value;
      IPAddress[] addressList = Dns.GetHostEntry(value).AddressList;
      if (addressList.Length != 0)
        return addressList[0].ToString();
    }
    return "127.0.0.1";
  }

  /// <summary>从流中接收指定长度的字节数组</summary>
  /// <param name="stream">流</param>
  /// <param name="length">数据长度</param>
  /// <returns>二进制的字节数组</returns>
  public static byte[] ReadSpecifiedLengthFromStream(Stream stream, int length)
  {
    byte[] buffer = new byte[length];
    int offset = 0;
    while (offset < length)
    {
      int num = stream.Read(buffer, offset, buffer.Length - offset);
      offset += num;
      if (num == 0)
        break;
    }
    return buffer;
  }

  /// <summary>将字符串的内容写入到流中去</summary>
  /// <param name="stream">数据流</param>
  /// <param name="value">字符串内容</param>
  public static void WriteStringToStream(Stream stream, string value)
  {
    byte[] numArray = string.IsNullOrEmpty(value) ? new byte[0] : Encoding.UTF8.GetBytes(value);
    HslHelper.WriteBinaryToStream(stream, numArray);
  }

  /// <summary>从流中读取一个字符串内容</summary>
  /// <param name="stream">数据流</param>
  /// <returns>字符串信息</returns>
  public static string ReadStringFromStream(Stream stream)
  {
    return Encoding.UTF8.GetString(HslHelper.ReadBinaryFromStream(stream));
  }

  /// <summary>将二进制的内容写入到数据流之中</summary>
  /// <param name="stream">数据流</param>
  /// <param name="value">原始字节数组</param>
  public static void WriteBinaryToStream(Stream stream, byte[] value)
  {
    stream.Write(BitConverter.GetBytes(value.Length), 0, 4);
    stream.Write(value, 0, value.Length);
  }

  /// <summary>从流中读取二进制的内容</summary>
  /// <param name="stream">数据流</param>
  /// <returns>字节数组</returns>
  public static byte[] ReadBinaryFromStream(Stream stream)
  {
    int int32 = BitConverter.ToInt32(HslHelper.ReadSpecifiedLengthFromStream(stream, 4), 0);
    return int32 <= 0 ? new byte[0] : HslHelper.ReadSpecifiedLengthFromStream(stream, int32);
  }

  /// <summary>从字符串的内容提取UTF8编码的字节，加了对空的校验</summary>
  /// <param name="message">字符串内容</param>
  /// <returns>结果</returns>
  public static byte[] GetUTF8Bytes(string message)
  {
    return string.IsNullOrEmpty(message) ? new byte[0] : Encoding.UTF8.GetBytes(message);
  }

  /// <summary>休眠指定的时间，时间单位为毫秒</summary>
  /// <param name="millisecondsTimeout">毫秒的时间值</param>
  public static void ThreadSleep(int millisecondsTimeout)
  {
    try
    {
      Thread.Sleep(millisecondsTimeout);
    }
    catch
    {
    }
  }

  /// <summary>将多个路径合成一个更完整的路径，这个方法是多平台适用的</summary>
  /// <param name="paths">路径的集合</param>
  /// <returns>总路径信息</returns>
  public static string PathCombine(params string[] paths) => Path.Combine(paths);

  /// <summary>
  /// <b>[商业授权]</b> 将原始的字节数组，转换成实际的结构体对象，需要事先定义好结构体内容，否则会转换失败<br />
  /// <b>[Authorization]</b> To convert the original byte array into an actual structure object,
  /// the structure content needs to be defined in advance, otherwise the conversion will fail
  /// </summary>
  /// <typeparam name="T">自定义的结构体</typeparam>
  /// <param name="content">原始的字节内容</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<T> ByteArrayToStruct<T>(byte[] content) where T : struct
  {
    if (!HslCommunication.Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<T>(StringResources.Language.InsufficientPrivileges);
    int num1 = Marshal.SizeOf(typeof (T));
    IntPtr num2 = Marshal.AllocHGlobal(num1);
    try
    {
      Marshal.Copy(content, 0, num2, num1);
      T structure = Marshal.PtrToStructure<T>(num2);
      Marshal.FreeHGlobal(num2);
      return OperateResult.CreateSuccessResult<T>(structure);
    }
    catch (Exception ex)
    {
      Marshal.FreeHGlobal(num2);
      return new OperateResult<T>(ex.Message);
    }
  }

  /// <summary>根据当前的位偏移地址及读取位长度信息，计算出实际的字节索引，字节数，字节位偏移</summary>
  /// <param name="addressStart">起始地址</param>
  /// <param name="length">读取的长度</param>
  /// <param name="newStart">返回的新的字节的索引，仍然按照位单位</param>
  /// <param name="byteLength">字节长度</param>
  /// <param name="offset">当前偏移的信息</param>
  public static void CalculateStartBitIndexAndLength(
    int addressStart,
    ushort length,
    out int newStart,
    out ushort byteLength,
    out int offset)
  {
    byteLength = (ushort) ((addressStart + (int) length - 1) / 8 - addressStart / 8 + 1);
    offset = addressStart % 8;
    newStart = addressStart - offset;
  }

  /// <summary>根据字符串内容，获取当前的位索引地址，例如输入 6,返回6，输入15，返回15，输入B，返回11</summary>
  /// <param name="bit">位字符串</param>
  /// <returns>结束数据</returns>
  public static int CalculateBitStartIndex(string bit)
  {
    return Regex.IsMatch(bit, "[ABCDEF]", RegexOptions.IgnoreCase) ? Convert.ToInt32(bit, 16 /*0x10*/) : Convert.ToInt32(bit);
  }

  /// <summary>将一个一维数组中的所有数据按照行列信息拷贝到二维数组里，返回当前的二维数组</summary>
  /// <typeparam name="T">数组的类型对象</typeparam>
  /// <param name="array">一维数组信息</param>
  /// <param name="row">行</param>
  /// <param name="col">列</param>
  public static T[,] CreateTwoArrayFromOneArray<T>(T[] array, int row, int col)
  {
    T[,] arrayFromOneArray = new T[row, col];
    int index1 = 0;
    for (int index2 = 0; index2 < row; ++index2)
    {
      for (int index3 = 0; index3 < col; ++index3)
      {
        arrayFromOneArray[index2, index3] = array[index1];
        ++index1;
      }
    }
    return arrayFromOneArray;
  }

  /// <summary>判断当前的字符串表示的地址，是否以索引为结束</summary>
  /// <param name="address">PLC的字符串地址信息</param>
  /// <returns>是否以索引结束</returns>
  public static bool IsAddressEndWithIndex(string address)
  {
    return Regex.IsMatch(address, "\\[[0-9]+\\]$");
  }

  /// <summary>根据位偏移的地址，长度信息，计算出实际的地址占用长度</summary>
  /// <param name="address">偏移地址</param>
  /// <param name="length">长度信息</param>
  /// <param name="hex">地址的进制信息，一般为8 或是 16</param>
  /// <returns>占用的地址长度信息</returns>
  public static int CalculateOccupyLength(int address, int length, int hex = 8)
  {
    return (address + length - 1) / hex - address / hex + 1;
  }

  /// <summary>根据地址的临界条件来切割读取地址的方法，支持bool地址的切割，支持字地址的切割</summary>
  /// <typeparam name="T">数据的类型信息</typeparam>
  /// <param name="readFunc">读取的功能方法</param>
  /// <param name="cuttings">切割的地址信息</param>
  /// <param name="address">实际的数据地址</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>使用底层的读取机制来实现真正的读取操作</returns>
  public static OperateResult<T[]> ReadCuttingHelper<T>(
    Func<string, ushort, OperateResult<T[]>> readFunc,
    List<CuttingAddress> cuttings,
    string address,
    ushort length)
  {
    string str = string.Empty;
    OperateResult<int> parameter = HslHelper.ExtractParameter(ref address, "s");
    if (parameter.IsSuccess)
      str = $"s={parameter.Content};";
    foreach (CuttingAddress cutting in cuttings)
    {
      if (address.StartsWith(cutting.DataType, StringComparison.OrdinalIgnoreCase))
      {
        int int32;
        try
        {
          int32 = Convert.ToInt32(address.Substring(cutting.DataType.Length), cutting.FromBase);
        }
        catch
        {
          break;
        }
        if (int32 < cutting.Address && int32 + (int) length > cutting.Address)
        {
          ushort num1 = (ushort) (cutting.Address - int32);
          ushort num2 = (ushort) ((uint) length - (uint) num1);
          OperateResult<T[]> operateResult1 = readFunc(str + address, num1);
          if (!operateResult1.IsSuccess)
            return operateResult1;
          OperateResult<T[]> operateResult2 = readFunc(str + cutting.DataType + Convert.ToString(cutting.Address, cutting.FromBase), num2);
          if (!operateResult2.IsSuccess)
            return operateResult2;
          return OperateResult.CreateSuccessResult<T[]>(SoftBasic.SpliceArray<T>(operateResult1.Content, operateResult2.Content));
        }
        break;
      }
    }
    return readFunc(address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.HslHelper.ReadCuttingHelper``1(System.Func{System.String,System.UInt16,HslCommunication.OperateResult{``0[]}},System.Collections.Generic.List{HslCommunication.Core.CuttingAddress},System.String,System.UInt16)" />
  public static async Task<OperateResult<T[]>> ReadCuttingAsyncHelper<T>(
    Func<string, ushort, Task<OperateResult<T[]>>> readFunc,
    List<CuttingAddress> cuttings,
    string address,
    ushort length)
  {
    string station = string.Empty;
    OperateResult<int> stationPara = HslHelper.ExtractParameter(ref address, "s");
    if (stationPara.IsSuccess)
      station = $"s={stationPara.Content};";
    foreach (CuttingAddress cutting in cuttings)
    {
      CuttingAddress item = cutting;
      if (address.StartsWith(item.DataType, StringComparison.OrdinalIgnoreCase))
      {
        int add = 0;
        try
        {
          add = Convert.ToInt32(address.Substring(item.DataType.Length), item.FromBase);
        }
        catch
        {
          break;
        }
        if (add < item.Address && add + (int) length > item.Address)
        {
          ushort len1 = (ushort) (item.Address - add);
          ushort len2 = (ushort) ((uint) length - (uint) len1);
          OperateResult<T[]> read1 = await readFunc(station + address, len1);
          if (!read1.IsSuccess)
            return read1;
          OperateResult<T[]> read2 = await readFunc(station + item.DataType + Convert.ToString(item.Address, item.FromBase), len2);
          if (!read2.IsSuccess)
            return read2;
          return OperateResult.CreateSuccessResult<T[]>(SoftBasic.SpliceArray<T>(read1.Content, read2.Content));
        }
        break;
      }
      item = (CuttingAddress) null;
    }
    OperateResult<T[]> operateResult = await readFunc(address, length);
    return operateResult;
  }

  private static string[] SplitsAddressDot(string address)
  {
    int length = address.LastIndexOf(".");
    return length > 0 && length < address.Length - 1 ? new string[2]
    {
      address.Substring(0, length),
      address.Substring(length + 1)
    } : new string[1]{ address };
  }

  /// <summary>
  /// 按照位为单位从设备中批量读取bool数组，如果地址中包含了小数点，则使用字的方式读取数据，然后解析出位数据<br />
  /// The BOOL array is read in batches from the device in bits, and if the address contains decimal points, the data is read in a word manner, and then the bit data is parsed
  /// </summary>
  /// <param name="device">设备的通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">读取的位长度信息</param>
  /// <param name="addressLength">单位地址的占位长度信息</param>
  /// <param name="reverseByWord">是否根据字进行反转操作</param>
  /// <returns>bool数组的结果对象</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteNet device,
    string address,
    ushort length,
    int addressLength = 16 /*0x10*/,
    bool reverseByWord = false)
  {
    if (address.IndexOf('.') <= 0)
      return device.ReadBool(address, length);
    string[] strArray = HslHelper.SplitsAddressDot(address);
    int bitStartIndex;
    try
    {
      bitStartIndex = HslHelper.CalculateBitStartIndex(strArray[1]);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>("Bit Index format wrong, " + ex.Message);
    }
    ushort length1 = (ushort) (((int) length + bitStartIndex + addressLength - 1) / addressLength);
    OperateResult<byte[]> result = device.Read(strArray[0], length1);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
    return reverseByWord ? OperateResult.CreateSuccessResult<bool[]>(result.Content.ReverseByWord().ToBoolArray().SelectMiddle<bool>(bitStartIndex, (int) length)) : OperateResult.CreateSuccessResult<bool[]>(result.Content.ToBoolArray().SelectMiddle<bool>(bitStartIndex, (int) length));
  }

  /// <summary>
  /// 将bool数组写入到设备中去，如果地址中包含了小数点，则使用字的方式读取数据，然后解析出位数据，再修改其中的位数据，然后写入到设备中去，解决那些没有位读写的功能码的设备的问题<br />
  /// Write the bool array into the device. If the address contains a decimal point, read the data in word form, then parse out the bit data, modify the bit data within it,
  /// and then write it back into the device to solve the problem of devices without bit read and write function codes
  /// </summary>
  /// <param name="device">设备的通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">实际写入的bool数据信息</param>
  /// <param name="addressLength">单位地址的占位长度信息</param>
  /// <param name="reverseByWord">是否根据字进行反转操作</param>
  /// <param name="insertPoint">当地址里缺失小数点的时候，是否进行额外补充小数点</param>
  /// <returns>bool数组的结果对象</returns>
  public static OperateResult WriteBool(
    IReadWriteNet device,
    string address,
    bool[] value,
    int addressLength = 16 /*0x10*/,
    bool reverseByWord = false,
    bool insertPoint = false)
  {
    if (address.IndexOf('.') > 0)
    {
      string[] strArray = HslHelper.SplitsAddressDot(address);
      int bitStartIndex;
      try
      {
        bitStartIndex = HslHelper.CalculateBitStartIndex(strArray[1]);
      }
      catch (Exception ex)
      {
        return (OperateResult) new OperateResult<bool[]>("Bit Index format wrong, " + ex.Message);
      }
      ushort length = (ushort) ((value.Length + bitStartIndex + addressLength - 1) / addressLength);
      OperateResult<byte[]> result = device.Read(strArray[0], length);
      if (!result.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
      bool[] array = reverseByWord ? result.Content.ReverseByWord().ToBoolArray() : result.Content.ToBoolArray();
      value.CopyTo((Array) array, bitStartIndex);
      byte[] numArray = reverseByWord ? array.ToByteArray().ReverseByWord() : array.ToByteArray();
      return device.Write(strArray[0], numArray);
    }
    if (!insertPoint || address.Length <= 1)
      return device.Write(address, value);
    string address1 = HslHelper.AddressAddPoint(address);
    return HslHelper.WriteBool(device, address1, value, addressLength, reverseByWord, insertPoint);
  }

  private static string AddressAddPoint(string address)
  {
    return $"{address.Substring(0, address.Length - 1)}.{address[address.Length - 1].ToString()}";
  }

  /// <inheritdoc cref="M:HslCommunication.Core.HslHelper.ReadBool(HslCommunication.Core.IReadWriteNet,System.String,System.UInt16,System.Int32,System.Boolean)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteNet device,
    string address,
    ushort length,
    int addressLength = 16 /*0x10*/,
    bool reverseByWord = false)
  {
    if (address.IndexOf('.') > 0)
    {
      string[] addressSplits = HslHelper.SplitsAddressDot(address);
      int bitIndex = 0;
      try
      {
        bitIndex = HslHelper.CalculateBitStartIndex(addressSplits[1]);
      }
      catch (Exception ex)
      {
        return new OperateResult<bool[]>("Bit Index format wrong, " + ex.Message);
      }
      ushort len = (ushort) (((int) length + bitIndex + addressLength - 1) / addressLength);
      OperateResult<byte[]> read = await device.ReadAsync(addressSplits[0], len);
      return read.IsSuccess ? (!reverseByWord ? OperateResult.CreateSuccessResult<bool[]>(read.Content.ToBoolArray().SelectMiddle<bool>(bitIndex, (int) length)) : OperateResult.CreateSuccessResult<bool[]>(read.Content.ReverseByWord().ToBoolArray().SelectMiddle<bool>(bitIndex, (int) length))) : OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    }
    OperateResult<bool[]> operateResult = await device.ReadBoolAsync(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.HslHelper.WriteBool(HslCommunication.Core.IReadWriteNet,System.String,System.Boolean[],System.Int32,System.Boolean,System.Boolean)" />
  public static async Task<OperateResult> WriteBoolAsync(
    IReadWriteNet device,
    string address,
    bool[] value,
    int addressLength = 16 /*0x10*/,
    bool reverseByWord = false,
    bool insertPoint = false)
  {
    if (address.IndexOf('.') > 0)
    {
      string[] addressSplits = HslHelper.SplitsAddressDot(address);
      int bitIndex = 0;
      try
      {
        bitIndex = HslHelper.CalculateBitStartIndex(addressSplits[1]);
      }
      catch (Exception ex)
      {
        return (OperateResult) new OperateResult<bool[]>("Bit Index format wrong, " + ex.Message);
      }
      ushort len = (ushort) ((value.Length + bitIndex + addressLength - 1) / addressLength);
      OperateResult<byte[]> read = await device.ReadAsync(addressSplits[0], len);
      if (!read.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      bool[] boolArray = reverseByWord ? read.Content.ReverseByWord().ToBoolArray() : read.Content.ToBoolArray();
      value.CopyTo((Array) boolArray, bitIndex);
      byte[] write = reverseByWord ? boolArray.ToByteArray().ReverseByWord() : boolArray.ToByteArray();
      OperateResult operateResult = await device.WriteAsync(addressSplits[0], write);
      return operateResult;
    }
    if (!insertPoint || address.Length <= 1)
      return device.Write(address, value);
    string addressNew = HslHelper.AddressAddPoint(address);
    OperateResult operateResult1 = await HslHelper.WriteBoolAsync(device, addressNew, value, addressLength, reverseByWord, insertPoint);
    return operateResult1;
  }

  /// <summary>将串口的一些参数，变成一个统一的格式化的字符串，例如 COM3-9600-8-N-1</summary>
  /// <param name="portName">端口号</param>
  /// <param name="baudRate">波特率</param>
  /// <param name="dataBits">数据位</param>
  /// <param name="parity">奇偶校验位</param>
  /// <param name="stopBits">停止位</param>
  /// <returns>格式化的字符串</returns>
  public static string ToFormatString(
    string portName,
    int baudRate,
    int dataBits,
    Parity parity,
    StopBits stopBits)
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(portName);
    stringBuilder.Append("-");
    stringBuilder.Append(baudRate.ToString());
    stringBuilder.Append("-");
    stringBuilder.Append(dataBits.ToString());
    stringBuilder.Append("-");
    switch (parity)
    {
      case Parity.None:
        stringBuilder.Append("N");
        break;
      case Parity.Odd:
        stringBuilder.Append("O");
        break;
      case Parity.Even:
        stringBuilder.Append("E");
        break;
      case Parity.Space:
        stringBuilder.Append("S");
        break;
      default:
        stringBuilder.Append("M");
        break;
    }
    stringBuilder.Append("-");
    switch (stopBits)
    {
      case StopBits.None:
        stringBuilder.Append("0");
        break;
      case StopBits.One:
        stringBuilder.Append("1");
        break;
      case StopBits.Two:
        stringBuilder.Append("2");
        break;
      default:
        stringBuilder.Append("1.5");
        break;
    }
    return stringBuilder.ToString();
  }
}
