﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.ModbusTcpMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>Modbus-Tcp协议支持的消息解析类</summary>
public class ModbusTcpMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 8;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    int? length = this.HeadBytes?.Length;
    int protocolHeadBytesLength = this.ProtocolHeadBytesLength;
    if (!(length.GetValueOrDefault() >= protocolHeadBytesLength & length.HasValue))
      return 0;
    int num1 = (int) this.HeadBytes[4] * 256 /*0x0100*/ + (int) this.HeadBytes[5];
    int lengthByHeadBytes;
    if (num1 == 0)
    {
      this.HeadBytes = this.HeadBytes.RemoveBegin<byte>(1);
      lengthByHeadBytes = (int) this.HeadBytes[4] * 256 /*0x0100*/ + (int) this.HeadBytes[5] - 1;
    }
    else
      lengthByHeadBytes = Math.Min(num1 - 2, 300);
    if (this.SendBytes != null && this.SendBytes.Length == 12)
    {
      if (this.SendBytes[7] == (byte) 3 || this.SendBytes[7] == (byte) 4)
      {
        int num2 = (int) this.SendBytes[10] * 256 /*0x0100*/ + (int) this.SendBytes[11];
        if (num2 > 0 && num2 <= (int) sbyte.MaxValue)
        {
          int num3 = num2 * 2 + 1;
          if (num3 != lengthByHeadBytes && num1 == 6)
            lengthByHeadBytes = num3;
        }
      }
      else if (this.SendBytes[7] == (byte) 1 || this.SendBytes[7] == (byte) 2)
      {
        int num4 = (int) this.SendBytes[10] * 256 /*0x0100*/ + (int) this.SendBytes[11];
        if (num4 > 0 && num4 <= 2040)
        {
          int num5 = (num4 - 1) / 8 + 1 + 1;
          if (num5 != lengthByHeadBytes && num1 == 6)
            lengthByHeadBytes = num5;
        }
      }
    }
    return lengthByHeadBytes;
  }

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    return send == null || receive == null || send.Length < 8 || receive.Length < 8 || (!this.IsCheckMessageId || (int) send[0] == (int) receive[0] && (int) send[1] == (int) receive[1]) && (!this.StationCheckMatch || (int) send[6] == (int) receive[6]) ? 1 : -1;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetHeadBytesIdentity" />
  public override int GetHeadBytesIdentity()
  {
    return (int) this.HeadBytes[0] * 256 /*0x0100*/ + (int) this.HeadBytes[1];
  }

  /// <summary>
  /// 获取或设置是否进行检查返回的消息ID和发送的消息ID是否一致，默认为true，也就是检查<br />
  /// Get or set whether to check whether the returned message ID is consistent with the sent message ID, the default is true, that is, check
  /// </summary>
  public bool IsCheckMessageId { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusRtu.StationCheckMacth" />
  public bool StationCheckMatch { get; set; } = true;
}
