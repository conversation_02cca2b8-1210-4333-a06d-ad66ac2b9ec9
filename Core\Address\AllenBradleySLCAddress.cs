﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.AllenBradleySLCAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>罗克韦尔PLC的地址信息</summary>
public class AllenBradleySLCAddress : DeviceAddressDataBase
{
  /// <summary>
  /// 获取或设置等待读取的数据的代码<br />
  /// Get or set the code of the data waiting to be read
  /// </summary>
  public byte DataCode { get; set; }

  /// <summary>
  /// 获取或设置PLC的DB块数据信息<br />
  /// Get or set PLC DB data information
  /// </summary>
  public ushort DbBlock { get; set; }

  /// <summary>从指定的地址信息解析成真正的设备地址信息</summary>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  public override void Parse(string address, ushort length)
  {
    OperateResult<AllenBradleySLCAddress> from = AllenBradleySLCAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return;
    this.AddressStart = from.Content.AddressStart;
    this.Length = from.Content.Length;
    this.DataCode = from.Content.DataCode;
    this.DbBlock = from.Content.DbBlock;
  }

  /// <inheritdoc />
  public override string ToString()
  {
    switch (this.DataCode)
    {
      case 130:
        return $"O{this.DbBlock}:{this.AddressStart}";
      case 131:
        return $"I{this.DbBlock}:{this.AddressStart}";
      case 132:
        return $"S{this.DbBlock}:{this.AddressStart}";
      case 133:
        return $"B{this.DbBlock}:{this.AddressStart}";
      case 134:
        return $"T{this.DbBlock}:{this.AddressStart}";
      case 135:
        return $"C{this.DbBlock}:{this.AddressStart}";
      case 136:
        return $"R{this.DbBlock}:{this.AddressStart}";
      case 137:
        return $"N{this.DbBlock}:{this.AddressStart}";
      case 138:
        return $"F{this.DbBlock}:{this.AddressStart}";
      case 141:
        return $"ST{this.DbBlock}:{this.AddressStart}";
      case 142:
        return $"A{this.DbBlock}:{this.AddressStart}";
      case 145:
        return $"L{this.DbBlock}:{this.AddressStart}";
      default:
        return this.AddressStart.ToString();
    }
  }

  /// <summary>
  /// 从实际的罗克韦尔的地址里面解析出地址对象，例如 A9:0<br />
  /// Parse the address object from the actual Rockwell address, such as A9:0
  /// </summary>
  /// <param name="address">实际的地址数据信息，例如 A9:0</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<AllenBradleySLCAddress> ParseFrom(string address)
  {
    return AllenBradleySLCAddress.ParseFrom(address, (ushort) 0);
  }

  /// <summary>
  /// 从实际的罗克韦尔的地址里面解析出地址对象，例如 A9:0<br />
  /// Parse the address object from the actual Rockwell address, such as A9:0
  /// </summary>
  /// <param name="address">实际的地址数据信息，例如 A9:0</param>
  /// <param name="length">读取的数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<AllenBradleySLCAddress> ParseFrom(string address, ushort length)
  {
    if (!address.Contains(":"))
      return new OperateResult<AllenBradleySLCAddress>("Address can't find ':', example : A9:0");
    string[] strArray = address.Split(':');
    try
    {
      AllenBradleySLCAddress bradleySlcAddress = new AllenBradleySLCAddress();
      switch (strArray[0][0])
      {
        case 'A':
          bradleySlcAddress.DataCode = (byte) 142;
          break;
        case 'B':
          bradleySlcAddress.DataCode = (byte) 133;
          break;
        case 'C':
          bradleySlcAddress.DataCode = (byte) 135;
          break;
        case 'F':
          bradleySlcAddress.DataCode = (byte) 138;
          break;
        case 'I':
          bradleySlcAddress.DataCode = (byte) 131;
          break;
        case 'L':
          bradleySlcAddress.DataCode = (byte) 145;
          break;
        case 'N':
          bradleySlcAddress.DataCode = (byte) 137;
          break;
        case 'O':
          bradleySlcAddress.DataCode = (byte) 130;
          break;
        case 'R':
          bradleySlcAddress.DataCode = (byte) 136;
          break;
        case 'S':
          int num = strArray[0].Length <= 1 ? 0 : (strArray[0][1] == 'T' ? 1 : 0);
          bradleySlcAddress.DataCode = num == 0 ? (byte) 132 : (byte) 141;
          break;
        case 'T':
          bradleySlcAddress.DataCode = (byte) 134;
          break;
        default:
          throw new Exception("Address code wrong, must be A,B,N,F,S,C,I,O,R,T,ST,L");
      }
      switch (bradleySlcAddress.DataCode)
      {
        case 130:
          bradleySlcAddress.DbBlock = strArray[0].Length == 1 ? (ushort) 0 : ushort.Parse(strArray[0].Substring(1));
          break;
        case 131:
          bradleySlcAddress.DbBlock = strArray[0].Length == 1 ? (ushort) 1 : ushort.Parse(strArray[0].Substring(1));
          break;
        case 132:
          bradleySlcAddress.DbBlock = strArray[0].Length == 1 ? (ushort) 2 : ushort.Parse(strArray[0].Substring(1));
          break;
        case 141:
          bradleySlcAddress.DbBlock = strArray[0].Length == 2 ? (ushort) 1 : ushort.Parse(strArray[0].Substring(2));
          break;
        default:
          bradleySlcAddress.DbBlock = ushort.Parse(strArray[0].Substring(1));
          break;
      }
      bradleySlcAddress.AddressStart = (int) ushort.Parse(strArray[1]);
      return OperateResult.CreateSuccessResult<AllenBradleySLCAddress>(bradleySlcAddress);
    }
    catch (Exception ex)
    {
      return new OperateResult<AllenBradleySLCAddress>("Wrong Address format: " + ex.Message);
    }
  }
}
