﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.OpenProtocolMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>OpenProtocol协议的消息</summary>
public class OpenProtocolMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 4;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    try
    {
      byte[] headBytes = this.HeadBytes;
      if (headBytes == null || headBytes.Length < 4)
        return 0;
      int num = Convert.ToInt32(Encoding.ASCII.GetString(this.HeadBytes, 0, 4)) - 4 + 1;
      return num < 0 ? 0 : num;
    }
    catch
    {
      return 17;
    }
  }
}
