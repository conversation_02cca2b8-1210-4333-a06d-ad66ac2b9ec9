﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.Estun.EstunTcpNet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.ModBus;
using System.Text;
using System.Threading;

#nullable disable
namespace HslCommunication.Robot.Estun;

/// <summary>
/// 一个埃斯顿的机器人的通信类，底层使用的是ModbusTCP协议，支持读取简单机器人数据，并且支持对机器人进行一些操作。<br />
/// A communication class of <PERSON><PERSON><PERSON>'s robot, the bottom layer uses the ModbusTCP protocol, supports reading simple robot data, and supports some operations on the robot.
/// </summary>
public class EstunTcpNet : ModbusTcpNet
{
  private Timer timer;

  /// <summary>
  /// 实例化一个Modbus-Tcp协议的客户端对象<br />
  /// Instantiate a client object of the Modbus-Tcp protocol
  /// </summary>
  public EstunTcpNet()
  {
    this.timer = new Timer(new TimerCallback(this.ThreadTimerTick), (object) null, 3000, 10000);
    this.ByteTransform.DataFormat = DataFormat.CDAB;
  }

  /// <summary>
  /// 指定服务器地址，端口号，客户端自己的站号来初始化<br />
  /// Specify the server address, port number, and client's own station number to initialize
  /// </summary>
  /// <param name="ipAddress">服务器的Ip地址</param>
  /// <param name="port">服务器的端口号</param>
  /// <param name="station">客户端自身的站号</param>
  public EstunTcpNet(string ipAddress, int port = 502, byte station = 1)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  private void ThreadTimerTick(object obj)
  {
    if (!this.ReadUInt16("0").IsSuccess)
      ;
  }

  /// <summary>读取埃斯顿的机器人的数据</summary>
  /// <returns>机器人数据</returns>
  public OperateResult<EstunData> ReadRobotData()
  {
    OperateResult<byte[]> result = this.Read("0", (ushort) 100);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<EstunData>((OperateResult) result) : OperateResult.CreateSuccessResult<EstunData>(new EstunData(result.Content, this.ByteTransform));
  }

  private OperateResult ExecuteCommand(short command)
  {
    OperateResult<short> operateResult1 = this.ReadInt16("99");
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<short> operateResult2 = this.ReadInt16("51");
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    if (operateResult1.Content != (short) 0)
      return new OperateResult("Step1: check 40100 value 0 failed, actual is " + operateResult1.Content.ToString());
    if (operateResult2.Content != (short) 0)
      return new OperateResult("Step1: check 40052 value 0 failed, actual is " + operateResult2.Content.ToString());
    OperateResult operateResult3 = this.Write("99", (short) 17);
    if (!operateResult3.IsSuccess)
      return new OperateResult("Step2: write 40100 0x11 failed, " + operateResult3.Message);
    int num = 0;
    OperateResult<short> operateResult4;
    while (true)
    {
      operateResult4 = this.ReadInt16("18");
      if (operateResult4.IsSuccess)
      {
        if (operateResult4.Content != (short) 2049)
        {
          ++num;
          if (num < 20)
            HslHelper.ThreadSleep(100);
          else
            goto label_14;
        }
        else
          goto label_17;
      }
      else
        break;
    }
    return new OperateResult("Step3: read 40019 failed, " + operateResult4.Message);
label_14:
    return new OperateResult("Step3: wait 40019 0x801 timeout, timeout is 2s");
label_17:
    OperateResult operateResult5 = this.Write("51", command);
    if (!operateResult5.IsSuccess)
      return new OperateResult("Step4: write cmd to 40052 failed, " + operateResult5.Message);
    HslHelper.ThreadSleep(100);
    OperateResult<short> operateResult6 = this.ReadInt16("18");
    if (!operateResult6.IsSuccess)
      return new OperateResult("Step5: read cmd status failed, " + operateResult6.Message);
    OperateResult operateResult7 = this.Write("99", (short) 0);
    if (!operateResult7.IsSuccess)
      return new OperateResult("Step6: clear 40100 failed, " + operateResult7.Message);
    OperateResult operateResult8 = this.Write("51", (short) 0);
    return !operateResult8.IsSuccess ? new OperateResult("Step6: clear 40052 failed, " + operateResult8.Message) : OperateResult.CreateSuccessResult();
  }

  /// <summary>机器人程序启动</summary>
  /// <returns>是否启动成功</returns>
  public OperateResult RobotStartPrograme() => this.ExecuteCommand((short) 4);

  /// <summary>机器人程序停止</summary>
  /// <returns>是否停止成功</returns>
  public OperateResult RobotStopPrograme() => this.ExecuteCommand((short) 8);

  /// <summary>机器人的错误进行复位</summary>
  /// <returns>是否重置了错误</returns>
  public OperateResult RobotResetError() => this.ExecuteCommand((short) 16 /*0x10*/);

  /// <summary>机器人重新装载程序名</summary>
  /// <param name="projectName">程序的名称</param>
  /// <returns>是否装载成功</returns>
  public OperateResult RobotLoadProject(string projectName)
  {
    OperateResult operateResult = this.Write("53", SoftBasic.ArrayExpandToLength<byte>(Encoding.ASCII.GetBytes(projectName), 20));
    return !operateResult.IsSuccess ? operateResult : this.ExecuteCommand((short) 128 /*0x80*/);
  }

  /// <summary>机器人卸载程序名</summary>
  /// <returns>是否卸载成功</returns>
  public OperateResult RobotUnregisterProject() => this.ExecuteCommand((short) 256 /*0x0100*/);

  /// <summary>机器人设置全局速度值</summary>
  /// <param name="value">全局速度值</param>
  /// <returns>是否设置成功</returns>
  public OperateResult RobotSetGlobalSpeedValue(short value)
  {
    OperateResult operateResult = this.Write("52", value);
    return !operateResult.IsSuccess ? operateResult : this.ExecuteCommand((short) 512 /*0x0200*/);
  }

  /// <summary>重置机器人的命令状态</summary>
  /// <returns>是否操作成功</returns>
  public OperateResult RobotCommandStatusRestart() => this.ExecuteCommand((short) 1024 /*0x0400*/);

  /// <inheritdoc />
  public override string ToString() => $"EstunTcpNet[{this.IpAddress}:{this.Port}]";
}
