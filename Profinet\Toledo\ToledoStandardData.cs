﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Toledo.ToledoStandardData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using Newtonsoft.Json;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Toledo;

/// <summary>托利多标准格式的数据类对象</summary>
public class ToledoStandardData
{
  /// <summary>实例化一个默认的对象</summary>
  public ToledoStandardData()
  {
  }

  /// <summary>从缓存里加载一个标准格式的对象</summary>
  /// <param name="buffer">缓存</param>
  public ToledoStandardData(byte[] buffer)
  {
    if (buffer[0] == (byte) 2)
    {
      this.ParseFromStandardOutput(buffer);
    }
    else
    {
      if (buffer[0] != (byte) 1)
        return;
      this.ParseFromExpandOutput(buffer);
    }
  }

  /// <summary>为 True 则是净重，为 False 则为毛重</summary>
  public bool Suttle { get; set; }

  /// <summary>为 True 则是正，为 False 则为负</summary>
  public bool Symbol { get; set; }

  /// <summary>是否在范围之外</summary>
  public bool BeyondScope { get; set; }

  /// <summary>是否为动态，为 True 则是动态，为 False 则为稳态</summary>
  public bool DynamicState { get; set; }

  /// <summary>单位</summary>
  public string Unit { get; set; }

  /// <summary>是否打印</summary>
  public bool IsPrint { get; set; }

  /// <summary>是否10被扩展</summary>
  public bool IsTenExtend { get; set; }

  /// <summary>重量</summary>
  public float Weight { get; set; }

  /// <summary>皮重</summary>
  public float Tare { get; set; }

  /// <summary>皮重类型，0: 无皮重; 1: 按键去皮; 2: 预置去皮; 3: 皮重内存，仅在扩展输出下有效</summary>
  public int TareType { get; set; }

  /// <summary>数据是否有效</summary>
  public bool DataValid { get; set; } = true;

  /// <summary>是否属于扩展输出模式</summary>
  public bool IsExpandOutput { get; set; }

  /// <summary>解析数据的原始字节</summary>
  [JsonIgnore]
  public byte[] SourceData { get; set; }

  /// <inheritdoc />
  public override string ToString() => $"ToledoStandardData[{this.Weight}]";

  /// <summary>从连续标准的模式里解析出真实的数据信息</summary>
  /// <param name="buffer">接收到的数据缓存</param>
  /// <returns>托利多的数据对象</returns>
  private void ParseFromStandardOutput(byte[] buffer)
  {
    this.Weight = float.Parse(Encoding.ASCII.GetString(buffer, 4, 6));
    this.Tare = float.Parse(Encoding.ASCII.GetString(buffer, 10, 6));
    switch ((int) buffer[1] & 7)
    {
      case 0:
        this.Weight *= 100f;
        this.Tare *= 100f;
        break;
      case 1:
        this.Weight *= 10f;
        this.Tare *= 10f;
        break;
      case 3:
        this.Weight /= 10f;
        this.Tare /= 10f;
        break;
      case 4:
        this.Weight /= 100f;
        this.Tare /= 100f;
        break;
      case 5:
        this.Weight /= 1000f;
        this.Tare /= 1000f;
        break;
      case 6:
        this.Weight /= 10000f;
        this.Tare /= 10000f;
        break;
      case 7:
        this.Weight /= 100000f;
        this.Tare /= 100000f;
        break;
    }
    this.Suttle = SoftBasic.BoolOnByteIndex(buffer[2], 0);
    this.Symbol = SoftBasic.BoolOnByteIndex(buffer[2], 1);
    this.BeyondScope = SoftBasic.BoolOnByteIndex(buffer[2], 2);
    this.DynamicState = SoftBasic.BoolOnByteIndex(buffer[2], 3);
    switch ((int) buffer[3] & 7)
    {
      case 0:
        this.Unit = SoftBasic.BoolOnByteIndex(buffer[2], 4) ? "kg" : "lb";
        break;
      case 1:
        this.Unit = "g";
        break;
      case 2:
        this.Unit = "t";
        break;
      case 3:
        this.Unit = "oz";
        break;
      case 4:
        this.Unit = "ozt";
        break;
      case 5:
        this.Unit = "dwt";
        break;
      case 6:
        this.Unit = "ton";
        break;
      case 7:
        this.Unit = "newton";
        break;
    }
    this.IsPrint = SoftBasic.BoolOnByteIndex(buffer[3], 3);
    this.IsTenExtend = SoftBasic.BoolOnByteIndex(buffer[3], 4);
    this.SourceData = buffer;
  }

  private void ParseFromExpandOutput(byte[] buffer)
  {
    this.IsExpandOutput = true;
    string s1 = Encoding.ASCII.GetString(buffer, 6, 9).Replace(" ", "");
    if (!string.IsNullOrEmpty(s1))
      this.Weight = float.Parse(s1);
    string s2 = Encoding.ASCII.GetString(buffer, 15, 8).Replace(" ", "");
    if (!string.IsNullOrEmpty(s2))
      this.Tare = float.Parse(s2);
    switch ((int) buffer[2] & 15)
    {
      case 0:
        this.Unit = "None";
        break;
      case 1:
        this.Unit = "lb";
        break;
      case 2:
        this.Unit = "kg";
        break;
      case 3:
        this.Unit = "g";
        break;
      case 4:
        this.Unit = "t";
        break;
      case 5:
        this.Unit = "ton";
        break;
      case 8:
        this.Unit = "oz";
        break;
      case 9:
        this.Unit = "newton";
        break;
    }
    this.DynamicState = SoftBasic.BoolOnByteIndex(buffer[2], 6);
    this.Suttle = SoftBasic.BoolOnByteIndex(buffer[3], 0);
    this.TareType = ((int) buffer[3] & 6) >> 1;
    this.BeyondScope = SoftBasic.BoolOnByteIndex(buffer[4], 1) || SoftBasic.BoolOnByteIndex(buffer[4], 2);
    this.IsPrint = SoftBasic.BoolOnByteIndex(buffer[4], 4);
    this.DataValid = SoftBasic.BoolOnByteIndex(buffer[4], 0);
    this.SourceData = buffer;
  }
}
