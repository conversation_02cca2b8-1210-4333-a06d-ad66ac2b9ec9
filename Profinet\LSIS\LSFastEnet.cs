﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.LSFastEnet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Collections;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.LSIS;

/// <summary>
/// XGB Fast Enet I/F module supports open Ethernet. It provides network configuration that is to connect LSIS and other company PLC, PC on network
/// </summary>
public class LSFastEnet : DeviceTcpNet
{
  /// <summary>所有支持的地址信息</summary>
  public const string AddressTypes = "PMLKFTCDSQINUZR";

  /// <summary>Instantiate a Default object</summary>
  public LSFastEnet()
  {
    this.WordLength = (ushort) 2;
    this.IpAddress = "127.0.0.1";
    this.Port = 2004;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
  }

  /// <summary>Instantiate a object by ipaddress and port</summary>
  /// <param name="ipAddress">the ip address of the plc</param>
  /// <param name="port">the port of the plc, default is 2004</param>
  public LSFastEnet(string ipAddress, int port)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <summary>
  /// Instantiate a object by ipaddress, port, cpuType, slotNo
  /// </summary>
  /// <param name="CpuType">CpuType</param>
  /// <param name="ipAddress">the ip address of the plc</param>
  /// <param name="port">he port of the plc, default is 2004</param>
  /// <param name="slotNo">slot number</param>
  public LSFastEnet(string CpuType, string ipAddress, int port, byte slotNo)
    : this(ipAddress, port)
  {
    this.SetCpuType = CpuType;
    this.SlotNo = slotNo;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new LsisFastEnetMessage();

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    byte[] numArray = new byte[command.Length + 20];
    Encoding.ASCII.GetBytes(this.CompanyID).CopyTo((Array) numArray, 0);
    switch (this.CpuInfo)
    {
      case LSCpuInfo.XGK:
        numArray[12] = (byte) 160 /*0xA0*/;
        break;
      case LSCpuInfo.XGI:
        numArray[12] = (byte) 164;
        break;
      case LSCpuInfo.XGR:
        numArray[12] = (byte) 168;
        break;
      case LSCpuInfo.XGB_MK:
        numArray[12] = (byte) 176 /*0xB0*/;
        break;
      case LSCpuInfo.XGB_IEC:
        numArray[12] = (byte) 180;
        break;
    }
    numArray[13] = (byte) 51;
    BitConverter.GetBytes((short) command.Length).CopyTo((Array) numArray, 16 /*0x10*/);
    numArray[18] = (byte) ((uint) this.BaseNo * 16U /*0x10*/ + (uint) this.SlotNo);
    int num = 0;
    for (int index = 0; index < 19; ++index)
      num += (int) numArray[index];
    numArray[19] = (byte) num;
    command.CopyTo((Array) numArray, 20);
    return numArray;
  }

  /// <summary>set plc</summary>
  public string SetCpuType { get; set; }

  /// <summary>CPU TYPE</summary>
  public string CpuType { get; private set; }

  /// <summary>Cpu is error</summary>
  public bool CpuError { get; private set; }

  /// <summary>RUN, STOP, ERROR, DEBUG</summary>
  public LSCpuStatus LSCpuStatus { get; private set; }

  /// <summary>FEnet I/F module’s Base No.</summary>
  public byte BaseNo { get; set; } = 0;

  /// <summary>FEnet I/F module’s Slot No.</summary>
  public byte SlotNo { get; set; } = 3;

  /// <summary>
  /// 
  /// </summary>
  public LSCpuInfo CpuInfo { get; set; } = LSCpuInfo.XGK;

  /// <summary>
  /// 
  /// </summary>
  public string CompanyID { get; set; } = "LSIS-XGT";

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = LSFastEnet.BuildReadByteCommand(address, length);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte[]> result = this.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : this.ExtractActualData(result.Content);
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = this.BuildWriteByteCommand(address, value);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    OperateResult<byte[]> result = this.ReadFromCoreServer(operateResult.Content);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : (OperateResult) this.ExtractActualData(result.Content);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> coreResult = LSFastEnet.BuildReadByteCommand(address, length);
    if (!coreResult.IsSuccess)
      return coreResult;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(coreResult.Content).ConfigureAwait(false);
    return read.IsSuccess ? this.ExtractActualData(read.Content) : OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult<byte[]> coreResult = this.BuildWriteByteCommand(address, value);
    if (!coreResult.IsSuccess)
      return (OperateResult) coreResult;
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(coreResult.Content).ConfigureAwait(false);
    return read.IsSuccess ? (OperateResult) this.ExtractActualData(read.Content) : (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<byte[]> result1 = LSFastEnet.BuildReadByteCommand(address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
    OperateResult<byte[]> actualData = this.ExtractActualData(result2.Content);
    return !actualData.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) actualData) : OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(actualData.Content, (int) length));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    OperateResult<byte[]> result1 = LSFastEnet.BuildReadIndividualCommand((byte) 0, address);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) result2);
    OperateResult<byte[]> actualData = this.ExtractActualData(result2.Content);
    return !actualData.IsSuccess ? OperateResult.CreateFailedResult<bool>((OperateResult) actualData) : OperateResult.CreateSuccessResult<bool>(SoftBasic.ByteToBoolArray(actualData.Content, 1)[0]);
  }

  /// <summary>ReadCoil</summary>
  /// <param name="address">Start address</param>
  /// <returns>Whether to read the successful</returns>
  public OperateResult<bool> ReadCoil(string address) => this.ReadBool(address);

  /// <summary>ReadCoil</summary>
  /// <param name="address">Start address</param>
  /// <param name="length">read address length</param>
  /// <returns>Whether to read the successful</returns>
  public OperateResult<bool[]> ReadCoil(string address, ushort length)
  {
    return this.ReadBool(address, length);
  }

  /// <summary>Read single byte value from plc</summary>
  /// <param name="address">Start address</param>
  /// <returns>Whether to write the successful</returns>
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <summary>Write single byte value to plc</summary>
  /// <param name="address">Start address</param>
  /// <param name="value">value</param>
  /// <returns>Whether to write the successful</returns>
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <summary>WriteCoil</summary>
  /// <param name="address">Start address</param>
  /// <param name="value">bool value</param>
  /// <returns>Whether to write the successful</returns>
  public OperateResult WriteCoil(string address, bool value)
  {
    return this.Write(address, new byte[2]
    {
      value ? (byte) 1 : (byte) 0,
      (byte) 0
    });
  }

  /// <summary>WriteCoil</summary>
  /// <param name="address">Start address</param>
  /// <param name="value">bool value</param>
  /// <returns>Whether to write the successful</returns>
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value) => this.WriteCoil(address, value);

  /// <inheritdoc />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<byte[]> coreResult = LSFastEnet.BuildReadByteCommand(address, length);
    if (!coreResult.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) coreResult);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(coreResult.Content).ConfigureAwait(false);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    OperateResult<byte[]> extract = this.ExtractActualData(read.Content);
    return extract.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(SoftBasic.ByteToBoolArray(extract.Content, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) extract);
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<byte[]> coreResult = LSFastEnet.BuildReadIndividualCommand((byte) 0, address);
    if (!coreResult.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) coreResult);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(coreResult.Content).ConfigureAwait(false);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool>((OperateResult) read);
    OperateResult<byte[]> extract = this.ExtractActualData(read.Content);
    return extract.IsSuccess ? OperateResult.CreateSuccessResult<bool>(SoftBasic.ByteToBoolArray(extract.Content, 1)[0]) : OperateResult.CreateFailedResult<bool>((OperateResult) extract);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.ReadCoil(System.String)" />
  public async Task<OperateResult<bool>> ReadCoilAsync(string address)
  {
    OperateResult<bool> operateResult = await this.ReadBoolAsync(address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.ReadCoil(System.String,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadCoilAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.ReadByte(System.String)" />
  public async Task<OperateResult<byte>> ReadByteAsync(string address)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<byte>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.Write(System.String,System.Byte)" />
  public async Task<OperateResult> WriteAsync(string address, byte value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.WriteCoil(System.String,System.Boolean)" />
  public async Task<OperateResult> WriteCoilAsync(string address, bool value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new byte[2]
    {
      value ? (byte) 1 : (byte) 0,
      (byte) 0
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await this.WriteCoilAsync(address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"LSFastEnet[{this.IpAddress}:{this.Port}]";

  /// <summary>GetAddressOfU IX0.0.0 QX0.0.0</summary>
  /// <param name="Address"></param>
  /// <param name="IsWrite"></param>
  /// <returns></returns>
  public static string GetAddressOfU_Q_I(string Address, bool IsWrite = false)
  {
    string[] strArray = Address.Split('.');
    object obj1 = (object) 0;
    object obj2;
    if (strArray.Length >= 3)
    {
      int int32 = Convert.ToInt32(strArray[2].Last<char>().ToString(), 16 /*0x10*/);
      obj2 = !(LSFastEnet.IsHex(strArray[2]) & IsWrite) ? (object) ((int.Parse(strArray[0]) * 32 /*0x20*/ + int.Parse(strArray[1])) * 10 + int32) : (object) ((int.Parse(strArray[0]) * 32 /*0x20*/ + int.Parse(strArray[1])).ToString() + strArray[2]);
    }
    else
      obj2 = (object) (int.Parse(strArray[0]) * 32 /*0x20*/ + int.Parse(strArray[1]));
    return $"{obj2}";
  }

  /// <summary>GetAddressOfBitTo_XGK  MX1.0  DX1.0 to PLC LS XGK</summary>
  /// <param name="address"></param>
  /// <param name="startIndex"></param>
  /// <param name="IsWrite"></param>
  /// <returns></returns>
  public static string GetAddressOfBitTo_XGK(string address, int startIndex, bool IsWrite = false)
  {
    string addressOfBitToXgk = address.Substring(startIndex);
    if (addressOfBitToXgk.IndexOf('.') < 0)
    {
      if (IsWrite)
        return addressOfBitToXgk;
      string str1 = addressOfBitToXgk.Substring(0, addressOfBitToXgk.Length - 1);
      string str2 = addressOfBitToXgk.Substring(addressOfBitToXgk.Length - 1);
      int num = !str2.Contains(new string[6]
      {
        "A",
        "B",
        "C",
        "D",
        "E",
        "F"
      }) ? Convert.ToInt32(str2) : Convert.ToInt32(str2, 16 /*0x10*/);
      return $"{Convert.ToInt32(str1) * 16 /*0x10*/ + num}";
    }
    string[] strArray = addressOfBitToXgk.Split('.');
    int indexInformation = HslHelper.GetBitIndexInformation(ref address);
    return LSFastEnet.IsHex(strArray[1]) & IsWrite ? address.Substring(2) + indexInformation.ToString("X1") : $"{Convert.ToInt32(strArray[0]) * 16 /*0x10*/ + indexInformation}";
  }

  /// <summary>NumberStyles HexNumber</summary>
  /// <param name="value"></param>
  /// <returns></returns>
  public static bool IsHex(string value)
  {
    if (string.IsNullOrEmpty(value))
      return false;
    bool flag = false;
    for (int index = 0; index < value.Length; ++index)
    {
      switch (value[index])
      {
        case 'A':
        case 'B':
        case 'C':
        case 'D':
        case 'E':
        case 'F':
        case 'a':
        case 'b':
        case 'c':
        case 'd':
        case 'e':
        case 'f':
          flag = true;
          break;
      }
    }
    return flag;
  }

  /// <summary>AnalysisAddress</summary>
  /// <param name="address">start address</param>
  /// <param name="IsWrite"></param>
  /// <returns>analysis result</returns>
  public static OperateResult<string> AnalysisAddress(string address, bool IsWrite = false)
  {
    bool flag1 = false;
    StringBuilder stringBuilder = new StringBuilder();
    try
    {
      stringBuilder.Append("%");
      if (address.IndexOf('.') > 0)
        flag1 = true;
      bool flag2 = false;
      for (int index = 0; index < "PMLKFTCDSQINUZR".Length; ++index)
      {
        if ((int) "PMLKFTCDSQINUZR"[index] == (int) address[0])
        {
          stringBuilder.Append("PMLKFTCDSQINUZR"[index]);
          if (address[1] == 'X')
          {
            stringBuilder.Append("X");
            if (flag1)
            {
              if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
              {
                int indexInformation = HslHelper.GetBitIndexInformation(ref address);
                stringBuilder.Append(address.Substring(2));
                stringBuilder.Append(indexInformation.ToString("X1"));
              }
              else
                stringBuilder.Append(LSFastEnet.GetAddressOfU_Q_I(address.Substring(2), IsWrite));
            }
            else
              stringBuilder.Append(address.Substring(2));
          }
          else
          {
            string str1 = string.Empty;
            if (address[1] == 'B')
            {
              stringBuilder.Append(flag1 ? "X" : "B");
              if (flag1)
              {
                if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
                {
                  int indexInformation = HslHelper.GetBitIndexInformation(ref address);
                  stringBuilder.Append(address.Substring(2));
                  stringBuilder.Append(indexInformation.ToString("X1"));
                }
                else
                  stringBuilder.Append(LSFastEnet.GetAddressOfU_Q_I(address.Substring(2)));
              }
              else
                str1 = address[0] != 'I' && address[0] != 'Q' ? $"{Convert.ToInt32(address.Substring(2))}" : address.Substring(2);
              stringBuilder.Append(str1);
            }
            else if (address[1] == 'W')
            {
              stringBuilder.Append(flag1 ? "X" : "W");
              if (flag1)
              {
                if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
                {
                  int indexInformation = HslHelper.GetBitIndexInformation(ref address);
                  stringBuilder.Append(address.Substring(2));
                  stringBuilder.Append(indexInformation.ToString("X1"));
                }
                else
                  stringBuilder.Append(LSFastEnet.GetAddressOfU_Q_I(address.Substring(2)));
              }
              else
                str1 = address[0] != 'I' && address[0] != 'Q' ? $"{Convert.ToInt32(address.Substring(2)) * 2}" : address.Substring(2);
              stringBuilder.Append(str1);
            }
            else if (address[1] == 'D')
            {
              stringBuilder.Append(flag1 ? "X" : "D");
              if (flag1)
              {
                if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
                {
                  int indexInformation = HslHelper.GetBitIndexInformation(ref address);
                  stringBuilder.Append(address.Substring(2));
                  stringBuilder.Append(indexInformation.ToString("X1"));
                }
                else
                  stringBuilder.Append(LSFastEnet.GetAddressOfU_Q_I(address.Substring(2)));
              }
              else if (address[0] == 'I' || address[0] == 'Q')
              {
                stringBuilder.Append(address.Substring(2));
              }
              else
              {
                string str2 = $"{Convert.ToInt32(address.Substring(2)) * 4}";
                stringBuilder.Append(str2);
              }
            }
            else if (address[1] == 'L')
            {
              stringBuilder.Append(flag1 ? "X" : "L");
              if (flag1)
              {
                if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
                {
                  int indexInformation = HslHelper.GetBitIndexInformation(ref address);
                  stringBuilder.Append(address.Substring(2));
                  stringBuilder.Append(indexInformation.ToString("X1"));
                }
                else
                  stringBuilder.Append(LSFastEnet.GetAddressOfU_Q_I(address.Substring(2)));
              }
              else if (address[0] == 'I' || address[0] == 'Q')
              {
                stringBuilder.Append(address.Substring(2));
              }
              else
              {
                string str3 = $"{Convert.ToInt32(address.Substring(2)) * 8}";
                stringBuilder.Append(str3);
              }
            }
            else
            {
              stringBuilder.Append(flag1 ? "X" : "B");
              if (flag1)
              {
                if (address[0] != 'U' || address[0] != 'I' || address[0] != 'Q')
                {
                  int indexInformation = HslHelper.GetBitIndexInformation(ref address);
                  stringBuilder.Append(address.Substring(1));
                  stringBuilder.Append(indexInformation.ToString("X1"));
                }
                else
                  stringBuilder.Append(LSFastEnet.GetAddressOfU_Q_I(address.Substring(1)));
              }
              else if (address[0] == 'I' || address[0] == 'Q')
                stringBuilder.Append(address.Substring(1));
              else if (LSFastEnet.IsHex(address.Substring(1)))
                stringBuilder.Append(address.Substring(1));
              else
                stringBuilder.Append($"{Convert.ToInt32(address.Substring(1)) * 2}");
            }
          }
          flag2 = true;
          break;
        }
      }
      if (!flag2)
        throw new Exception(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
    return OperateResult.CreateSuccessResult<string>(stringBuilder.ToString());
  }

  /// <summary>Get DataType to Address</summary>
  /// <param name="address">address</param>
  /// <returns>dataType</returns>
  public static OperateResult<string> GetDataTypeToAddress(string address)
  {
    string str = string.Empty;
    try
    {
      bool flag = false;
      for (int index = 0; index < "PMLKFTCDSQINUZR".Length; ++index)
      {
        if ((int) "PMLKFTCDSQINUZR"[index] == (int) address[0])
        {
          str = address[1] != 'X' ? (address[1] != 'W' ? (address[1] != 'D' ? (address[1] != 'L' ? (address[1] != 'B' ? (address.IndexOf('.') <= 0 ? "Continuous" : "Bit") : "Continuous") : "LWord") : "DWord") : "Word") : "Bit";
          flag = true;
          break;
        }
      }
      if (!flag)
        throw new Exception(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
    return OperateResult.CreateSuccessResult<string>(str);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSFastEnet.BuildReadIndividualCommand(System.Byte,System.String[])" />
  public static OperateResult<byte[]> BuildReadIndividualCommand(byte dataType, string address)
  {
    return LSFastEnet.BuildReadIndividualCommand(dataType, new string[1]
    {
      address
    });
  }

  /// <summary>Multi reading address Type of Read Individual</summary>
  /// <param name="dataType">dataType bit:0x04, byte:0x01, word:0x02, dword:0x03, lword:0x04, continuous:0x14</param>
  /// <param name="addresses">address, for example: MX100, PX100</param>
  /// <returns>Read Individual Command</returns>
  public static OperateResult<byte[]> BuildReadIndividualCommand(byte dataType, string[] addresses)
  {
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 84);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte(dataType);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) addresses.Length);
    memoryStream.WriteByte((byte) 0);
    foreach (string address in addresses)
    {
      OperateResult<string> result = LSFastEnet.AnalysisAddress(address);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
      memoryStream.WriteByte((byte) result.Content.Length);
      memoryStream.WriteByte((byte) 0);
      byte[] bytes = Encoding.ASCII.GetBytes(result.Content);
      memoryStream.Write(bytes, 0, bytes.Length);
    }
    return OperateResult.CreateSuccessResult<byte[]>(memoryStream.ToArray());
  }

  private static OperateResult<byte[]> BuildReadByteCommand(string address, ushort length)
  {
    OperateResult<string> result = LSFastEnet.AnalysisAddress(address);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    OperateResult<string> dataTypeToAddress = LSFastEnet.GetDataTypeToAddress(address);
    if (!dataTypeToAddress.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) dataTypeToAddress);
    byte[] numArray = new byte[12 + result.Content.Length];
    numArray[0] = (byte) 84;
    numArray[1] = (byte) 0;
    switch (dataTypeToAddress.Content)
    {
      case "Bit":
        numArray[2] = (byte) 0;
        break;
      case "Byte":
        numArray[2] = (byte) 1;
        break;
      case "Word":
        numArray[2] = (byte) 2;
        break;
      case "DWord":
        numArray[2] = (byte) 3;
        break;
      case "LWord":
        numArray[2] = (byte) 4;
        break;
      case "Continuous":
        numArray[2] = (byte) 20;
        break;
    }
    numArray[3] = (byte) 0;
    numArray[4] = (byte) 0;
    numArray[5] = (byte) 0;
    numArray[6] = (byte) 1;
    numArray[7] = (byte) 0;
    numArray[8] = (byte) result.Content.Length;
    numArray[9] = (byte) 0;
    Encoding.ASCII.GetBytes(result.Content).CopyTo((Array) numArray, 10);
    BitConverter.GetBytes(length).CopyTo((Array) numArray, numArray.Length - 2);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  private OperateResult<byte[]> BuildWriteByteCommand(string address, byte[] data)
  {
    OperateResult<string> result = LSFastEnet.AnalysisAddress(address, true);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    OperateResult<string> dataTypeToAddress = LSFastEnet.GetDataTypeToAddress(address);
    if (!dataTypeToAddress.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) dataTypeToAddress);
    byte[] numArray = new byte[12 + result.Content.Length + data.Length];
    switch (dataTypeToAddress.Content)
    {
      case "Bit":
        numArray[2] = (byte) 0;
        break;
      case "Byte":
        numArray[2] = (byte) 1;
        break;
      case "Word":
        numArray[2] = (byte) 2;
        break;
      case "DWord":
        numArray[2] = (byte) 3;
        break;
      case "LWord":
        numArray[2] = (byte) 4;
        break;
      case "Continuous":
        numArray[2] = (byte) 20;
        break;
    }
    numArray[0] = (byte) 88;
    numArray[1] = (byte) 0;
    numArray[3] = (byte) 0;
    numArray[4] = (byte) 0;
    numArray[5] = (byte) 0;
    numArray[6] = (byte) 1;
    numArray[7] = (byte) 0;
    numArray[8] = (byte) result.Content.Length;
    numArray[9] = (byte) 0;
    Encoding.ASCII.GetBytes(result.Content).CopyTo((Array) numArray, 10);
    BitConverter.GetBytes(data.Length).CopyTo((Array) numArray, numArray.Length - 2 - data.Length);
    data.CopyTo((Array) numArray, numArray.Length - data.Length);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>
  /// Returns true data content, supports read and write returns
  /// </summary>
  /// <param name="response">response data</param>
  /// <returns>real data</returns>
  public OperateResult<byte[]> ExtractActualData(byte[] response)
  {
    if (response.Length < 20)
      return new OperateResult<byte[]>("Length is less than 20:" + SoftBasic.ByteToHexString(response));
    try
    {
      ushort uint16_1 = BitConverter.ToUInt16(response, 10);
      BitArray bitArray = new BitArray(BitConverter.GetBytes(uint16_1));
      int num = (int) uint16_1 % 32 /*0x20*/;
      switch ((int) uint16_1 % 32 /*0x20*/)
      {
        case 1:
          this.CpuType = "XGK/R-CPUH";
          break;
        case 2:
          this.CpuType = "XGB/XBCU";
          break;
        case 4:
          this.CpuType = "XGK-CPUE";
          break;
        case 5:
          this.CpuType = "XGK/R-CPUH";
          break;
      }
      this.CpuError = bitArray[7];
      if (bitArray[8])
        this.LSCpuStatus = LSCpuStatus.RUN;
      if (bitArray[9])
        this.LSCpuStatus = LSCpuStatus.STOP;
      if (bitArray[10])
        this.LSCpuStatus = LSCpuStatus.ERROR;
      if (bitArray[11])
        this.LSCpuStatus = LSCpuStatus.DEBUG;
      if (response.Length < 28)
        return new OperateResult<byte[]>("Length is less than 28:" + SoftBasic.ByteToHexString(response));
      if (BitConverter.ToUInt16(response, 26) > (ushort) 0)
        return new OperateResult<byte[]>((int) response[28], "Error:" + LSFastEnet.GetErrorDesciption(response[28]));
      if (response[20] == (byte) 89)
        return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      if (response[20] != (byte) 85)
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
      try
      {
        ushort uint16_2 = BitConverter.ToUInt16(response, 30);
        byte[] destinationArray = new byte[(int) uint16_2];
        Array.Copy((Array) response, 32 /*0x20*/, (Array) destinationArray, 0, (int) uint16_2);
        return OperateResult.CreateSuccessResult<byte[]>(destinationArray);
      }
      catch (Exception ex)
      {
        return new OperateResult<byte[]>(ex.Message);
      }
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"ExtractActualData failed: {ex.Message} Souce: {response.ToHexString(' ')}");
    }
  }

  /// <summary>get the description of the error code meanning</summary>
  /// <param name="code">code value</param>
  /// <returns>string information</returns>
  public static string GetErrorDesciption(byte code)
  {
    switch (code)
    {
      case 0:
        return "Normal";
      case 1:
        return "Physical layer error (TX, RX unavailable)";
      case 3:
        return "There is no identifier of Function Block to receive in communication channel";
      case 4:
        return "Mismatch of data type";
      case 5:
        return "Reset is received from partner station";
      case 6:
        return "Communication instruction of partner station is not ready status";
      case 7:
        return "Device status of remote station is not desirable status";
      case 8:
        return "Access to some target is not available";
      case 9:
        return "Can’ t deal with communication instruction of partner station by too many reception";
      case 10:
        return "Time Out error";
      case 11:
        return "Structure error";
      case 12:
        return "Abort";
      case 13:
        return "Reject(local/remote)";
      case 14:
        return "Communication channel establishment error (Connect/Disconnect)";
      case 15:
        return "High speed communication and connection service error";
      case 33:
        return "Can’t find variable identifier";
      case 34:
        return "Address error";
      case 50:
        return "Response error";
      case 113:
        return "Object Access Unsupported";
      case 187:
        return "Unknown error code (communication code of other company) is received";
      default:
        return "Unknown error";
    }
  }
}
