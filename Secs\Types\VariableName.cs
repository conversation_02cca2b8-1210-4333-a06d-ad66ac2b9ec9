﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Types.VariableName
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Secs.Types;

/// <summary>变量名称类对象</summary>
public class VariableName
{
  /// <summary>变量的ID信息</summary>
  public long ID { get; set; }

  /// <summary>变量的名称信息</summary>
  public string Name { get; set; }

  /// <summary>变量的单位信息</summary>
  public string Units { get; set; }

  /// <inheritdoc />
  public override string ToString() => this.Name;

  /// <summary>
  /// 赋值操作，可以直接赋值 <see cref="T:HslCommunication.Secs.Types.OnlineData" /> 数据
  /// </summary>
  /// <param name="value"><see cref="T:HslCommunication.Secs.Types.SecsValue" /> 数值</param>
  /// <returns>等值的消息对象</returns>
  public static implicit operator VariableName(SecsValue value)
  {
    TypeHelper.TypeListCheck(value);
    if (!(value.Value is SecsValue[] secsValueArray))
      return (VariableName) null;
    return new VariableName()
    {
      ID = Convert.ToInt64(secsValueArray[0].Value),
      Name = secsValueArray[1].Value as string,
      Units = secsValueArray[2].Value as string
    };
  }

  /// <summary>
  /// 也可以赋值给<see cref="T:HslCommunication.Secs.Types.SecsValue" /> 数据
  /// </summary>
  /// <param name="value"><see cref="T:HslCommunication.Secs.Types.SecsValue" /> 对象</param>
  /// <returns>等值的消息对象</returns>
  public static implicit operator SecsValue(VariableName value)
  {
    return new SecsValue((IEnumerable<object>) new object[3]
    {
      (object) value.ID,
      (object) value.Name,
      (object) value.Units
    });
  }
}
