﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.PipeUdpNet
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using System;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// 基于UDP/IT通信的管道信息<br />
/// Pipeline information based on UDP/IT communication
/// </summary>
public class PipeUdpNet : PipeTcpNet
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public PipeUdpNet()
  {
  }

  /// <summary>
  /// 通过指定的IP地址和端口号来实例化一个对象<br />
  /// Instantiate an object with the specified IP address and port number
  /// </summary>
  /// <param name="ipAddress">IP地址信息</param>
  /// <param name="port">端口号</param>
  public PipeUdpNet(string ipAddress, int port)
  {
    this.IpAddress = ipAddress;
    this.Port = port;
  }

  /// <summary>
  /// 获取或设置一次接收时的数据长度，默认2KB数据长度，特殊情况的时候需要调整<br />
  /// Gets or sets the length of data received at a time. The default length is 2KB
  /// </summary>
  public int ReceiveCacheLength { get; set; } = 2048 /*0x0800*/;

  /// <inheritdoc />
  public override OperateResult<bool> OpenCommunication()
  {
    if (!this.IsConnectError())
      return OperateResult.CreateSuccessResult<bool>(false);
    try
    {
      Socket socket = new Socket(this.GetConnectIPEndPoint().AddressFamily, SocketType.Dgram, ProtocolType.Udp);
      if (this.LocalBinding != null)
        socket.Bind((EndPoint) this.LocalBinding);
      this.Socket = socket;
      this.ResetConnectErrorCount();
      return OperateResult.CreateSuccessResult<bool>(true);
    }
    catch (Exception ex)
    {
      this.CloseCommunication();
      return new OperateResult<bool>(-this.IncrConnectErrorCount(), ex.Message);
    }
  }

  /// <inheritdoc />
  public override async Task<OperateResult<bool>> OpenCommunicationAsync()
  {
    OperateResult<bool> operateResult = await Task.Run<OperateResult<bool>>((Func<OperateResult<bool>>) (() => this.OpenCommunication())).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc />
  public override OperateResult Send(byte[] data, int offset, int size)
  {
    IPEndPoint remoteEP = new IPEndPoint(IPAddress.Parse(this.IpAddress), this.Port);
    try
    {
      this.Socket.SendTo(data, offset, size, SocketFlags.None, (EndPoint) remoteEP);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      this.CloseCommunication();
      return (OperateResult) new OperateResult<byte[]>(-this.IncrConnectErrorCount(), ex.Message);
    }
  }

  /// <inheritdoc />
  public override async Task<OperateResult> SendAsync(byte[] data, int offset, int size)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Send(data, offset, size))).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc />
  public override OperateResult<int> Receive(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    try
    {
      this.Socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReceiveTimeout, this.ReceiveTimeOut);
      EndPoint remoteEP = (EndPoint) new IPEndPoint(new IPEndPoint(IPAddress.Parse(this.IpAddress), this.Port).AddressFamily == AddressFamily.InterNetworkV6 ? IPAddress.IPv6Any : IPAddress.Any, 0);
      return length > 0 ? OperateResult.CreateSuccessResult<int>(this.Socket.ReceiveFrom(buffer, offset, length, SocketFlags.None, ref remoteEP)) : OperateResult.CreateSuccessResult<int>(this.Socket.ReceiveFrom(buffer, offset, buffer.Length - offset, SocketFlags.None, ref remoteEP));
    }
    catch (Exception ex)
    {
      this.CloseCommunication();
      return new OperateResult<int>(-this.IncrConnectErrorCount(), "Socket Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc />
  public override async Task<OperateResult<int>> ReceiveAsync(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    OperateResult<int> async = await Task.Run<OperateResult<int>>((Func<OperateResult<int>>) (() => this.Receive(buffer, offset, length, timeOut, reportProgress))).ConfigureAwait(false);
    return async;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReceiveMessage(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    return this.UseServerActivePush ? base.ReceiveMessage(netMessage, sendValue, useActivePush, reportProgress, logMessage) : this.ReceiveMessage(netMessage, sendValue, (byte[]) null, logMessage);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.PipeUdpNet.ReceiveMessage(HslCommunication.Core.IMessage.INetMessage,System.Byte[],System.Boolean,System.Action{System.Int64,System.Int64},System.Action{System.Byte[]})" />
  /// <param name="alreadyReceive">已经接收的数据信息</param>
  public OperateResult<byte[]> ReceiveMessage(
    INetMessage netMessage,
    byte[] sendValue,
    byte[] alreadyReceive,
    Action<byte[]> logMessage = null,
    bool closeOnException = true)
  {
    try
    {
      MemoryStream ms = new MemoryStream();
      if (alreadyReceive != null && alreadyReceive.Length != 0)
      {
        ms.Write(alreadyReceive);
        if (this.CheckMessageComplete(netMessage, sendValue, ref ms))
          return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
      }
      this.Socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReceiveTimeout, this.ReceiveTimeOut);
      EndPoint remoteEP = (EndPoint) new IPEndPoint(new IPEndPoint(IPAddress.Parse(this.IpAddress), this.Port).AddressFamily == AddressFamily.InterNetworkV6 ? IPAddress.IPv6Any : IPAddress.Any, 0);
      OperateResult<byte[]> receiveBuffer = NetSupport.CreateReceiveBuffer(this.ReceiveCacheLength);
      if (!receiveBuffer.IsSuccess)
        return receiveBuffer;
      DateTime now = DateTime.Now;
      do
      {
        int from = this.Socket.ReceiveFrom(receiveBuffer.Content, ref remoteEP);
        byte[] buffer = receiveBuffer.Content.SelectBegin<byte>(from);
        ms.Write(buffer);
        if (logMessage != null)
          logMessage(buffer);
        if (netMessage == null || this.CheckMessageComplete(netMessage, sendValue, ref ms))
          goto label_12;
      }
      while (this.ReceiveTimeOut < 0 || (DateTime.Now - now).TotalMilliseconds <= (double) this.ReceiveTimeOut);
      return new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataTimeout}{this.ReceiveTimeOut.ToString()} Received: {ms.ToArray().ToHexString(' ')}");
label_12:
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
    }
    catch (Exception ex)
    {
      if (closeOnException)
        this.CloseCommunication();
      return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), "Socket Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc />
  public override async Task<OperateResult<byte[]>> ReceiveMessageAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    OperateResult<byte[]> messageAsync = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.ReceiveMessage(netMessage, sendValue, useActivePush, reportProgress, logMessage))).ConfigureAwait(false);
    return messageAsync;
  }

  /// <inheritdoc />
  public override string ToString() => $"PipeUdpNet[{this.IpAddress}:{this.Port}]";
}
