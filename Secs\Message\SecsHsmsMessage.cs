﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Message.SecsHsmsMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core.IMessage;
using System;

#nullable disable
namespace HslCommunication.Secs.Message;

/// <summary>Hsms协议的消息定义</summary>
public class SecsHsmsMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 4;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    int int32 = BitConverter.ToInt32(new byte[4]
    {
      this.HeadBytes[3],
      this.HeadBytes[2],
      this.HeadBytes[1],
      this.HeadBytes[0]
    }, 0);
    return int32 < 0 ? 0 : int32;
  }

  /// <inheritdoc />
  public override bool CheckHeadBytesLegal(byte[] token) => true;
}
