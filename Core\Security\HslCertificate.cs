﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Security.HslCertificate
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;

#nullable disable
namespace HslCommunication.Core.Security;

/// <summary>
/// 基于RSA加密模型的证书，支持自定义的颁发证书，以及校验证书合法性<br />
/// Certificates based on RSA encryption model support custom issuance certificates and verify certificate legitimacy
/// </summary>
/// <remarks>
/// 证书可以用于接口的权限认证，不需要修改接口源代码或是配置文件，颁发证书就可以修改用户的权限，而且只要保密好私钥，那么证书本身就无法伪造，具有极高的安全性，具体用法参考示例代码。<br />
/// The certificate can be used for the permission authentication of the interface, no need to modify the interface source code or configuration file,
/// the issuance of the certificate can modify the user's permissions, and as long as the private key is kept secret, then the certificate itself cannot be forged,
/// with extremely high security, the specific usage refer to the sample code.
/// </remarks>
/// <example>
/// 证书这部分的功能主要分为，制作证书，以及校验证书，至于为什么不使用<see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />证书的形式，
/// 因为这种证书都是要授信机构颁发的，自己颁发的证书验签不了，所以在本库里提供一个用于自己颁发，自己验签的证书。<br />
/// 假设我们有一些API接口需要使用证书来控制权限，有调用时间检验的，或是按接口名称校验的，或是按调用次数来校验的，接口见下面的代码。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\HslCertificateSample.cs" region="Example1" title="接口的权限控制示例" />
/// 当然我们还可以自己颁发证书，注意，这时候的私钥就非常有用了，私钥丢了，就发不了证书了。如果要重新生成公私钥，那么之前发出去的证书都失效了。
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\HslCertificateSample.cs" region="Example2" title="颁发证书的例子" />
/// </example>
public class HslCertificate
{
  private RSACryptoServiceProvider privateRsa;
  private RSACryptoServiceProvider publicRsa;

  /// <summary>
  /// 实例化一个默认的对象<br />
  /// Instantiate a default object
  /// </summary>
  public HslCertificate() => this.CreateTime = DateTime.Now;

  /// <summary>
  /// 使用指定的公钥，私钥来实例化一个的对象<br />
  /// An object is instantiated using the specified public key and private key
  /// </summary>
  /// <param name="pubKey">公钥的对象</param>
  /// <param name="priKey">私钥的对象</param>
  public HslCertificate(RSACryptoServiceProvider pubKey, RSACryptoServiceProvider priKey)
    : this()
  {
    this.publicRsa = pubKey;
    this.privateRsa = priKey;
    this.PublicKey = RSAHelper.GetPublicKeyFromRSA(pubKey);
  }

  /// <summary>
  /// 使用指定的公钥，私钥来实例化一个的对象<br />
  /// An object is instantiated using the specified public key and private key
  /// </summary>
  /// <param name="pubKey">公钥的二进制数据</param>
  /// <param name="priKey">私钥的二进制数据</param>
  public HslCertificate(byte[] pubKey, byte[] priKey)
    : this()
  {
    if (pubKey != null)
      this.publicRsa = RSAHelper.CreateRsaProviderFromPublicKey(pubKey);
    if (priKey != null)
      this.privateRsa = RSAHelper.CreateRsaProviderFromPrivateKey(priKey);
    this.PublicKey = pubKey;
  }

  /// <summary>从文件的二进制数据中加载相关的参数</summary>
  /// <param name="hslCertificate">证书信息</param>
  public void LoadFrom(byte[] hslCertificate)
  {
    int index1 = 4;
    this.PublicKey = this.ExtraBytes(hslCertificate, ref index1);
    this.From = this.ExtraString(hslCertificate, ref index1);
    this.To = this.ExtraString(hslCertificate, ref index1);
    this.NotBefore = this.ExtraDateTime(hslCertificate, ref index1);
    this.NotAfter = this.ExtraDateTime(hslCertificate, ref index1);
    this.CreateTime = this.ExtraDateTime(hslCertificate, ref index1);
    this.KeyWord = this.ExtraString(hslCertificate, ref index1);
    this.UniqueID = this.ExtraString(hslCertificate, ref index1);
    this.EffectiveHours = BitConverter.ToInt32(hslCertificate, index1);
    int index2 = index1 + 4;
    int num = (int) this.ExtraShort(hslCertificate, ref index2);
    this.Descriptions = new Dictionary<string, string>();
    for (int index3 = 0; index3 < num; ++index3)
      this.Descriptions.Add(this.ExtraString(hslCertificate, ref index2), this.ExtraString(hslCertificate, ref index2));
  }

  /// <summary>证书的颁发者</summary>
  public string From { get; set; }

  /// <summary>证书的持有者</summary>
  public string To { get; set; }

  /// <summary>证书有效的起始时间</summary>
  public DateTime NotBefore { get; set; }

  /// <summary>证书有效的截止时间</summary>
  public DateTime NotAfter { get; set; }

  /// <summary>证书的公钥信息</summary>
  public byte[] PublicKey { get; set; }

  /// <summary>发证日期</summary>
  public DateTime CreateTime { get; set; }

  /// <summary>获取或设置当前证书的关键字，可以用来给证书做分类</summary>
  public string KeyWord { get; set; }

  /// <summary>获取或设置当前证书的唯一编号信息</summary>
  public string UniqueID { get; set; }

  /// <summary>有效小时数，小于等于0 表示无期限</summary>
  public int EffectiveHours { get; set; }

  /// <summary>证书的其他描述信息</summary>
  public Dictionary<string, string> Descriptions { get; set; }

  private void AddDateTime(MemoryStream ms, DateTime data)
  {
    byte[] bytes = BitConverter.GetBytes(data.Ticks);
    this.AddBytes(ms, bytes);
  }

  private void AddBytes(MemoryStream ms, ushort data)
  {
    byte[] bytes = BitConverter.GetBytes(data);
    ms.Write(bytes);
  }

  private void AddString(MemoryStream ms, string data)
  {
    byte[] bytes = string.IsNullOrEmpty(data) ? (byte[]) null : Encoding.UTF8.GetBytes(data);
    this.AddBytes(ms, bytes);
  }

  private void AddBytes(MemoryStream ms, byte[] data)
  {
    int length = data == null ? 0 : data.Length;
    ms.Write(BitConverter.GetBytes((short) length), 0, 2);
    if (data == null || length <= 0)
      return;
    ms.Write(data, 0, data.Length);
  }

  private DateTime ExtraDateTime(byte[] buffer, ref int index)
  {
    return new DateTime(BitConverter.ToInt64(this.ExtraBytes(buffer, ref index), 0));
  }

  private ushort ExtraShort(byte[] buffer, ref int index)
  {
    ushort uint16 = BitConverter.ToUInt16(buffer, index);
    index += 2;
    return uint16;
  }

  private byte[] ExtraBytes(byte[] buffer, ref int index)
  {
    int uint16 = (int) BitConverter.ToUInt16(buffer, index);
    index += 2;
    if (uint16 <= 0)
      return new byte[0];
    byte[] numArray = buffer.SelectMiddle<byte>(index, uint16);
    index += uint16;
    return numArray;
  }

  private string ExtraString(byte[] buffer, ref int index)
  {
    byte[] bytes = this.ExtraBytes(buffer, ref index);
    return bytes == null || bytes.Length == 0 ? string.Empty : Encoding.UTF8.GetString(bytes);
  }

  /// <summary>
  /// 获取当前证书的原始字节信息，可以存储到文件中，必须提供私钥信息，否则无法进行签名的操作<br />
  /// Gets the raw byte information of the current certificate, which can be stored in a file, and the private key information must be provided, otherwise the signing operation cannot be performed
  /// </summary>
  /// <returns>原始字节数据</returns>
  public byte[] GetSaveBytes()
  {
    MemoryStream ms = new MemoryStream();
    this.AddBytes(ms, (ushort) 0);
    this.AddBytes(ms, (ushort) 0);
    this.AddBytes(ms, this.PublicKey);
    this.AddString(ms, this.From);
    this.AddString(ms, this.To);
    this.AddDateTime(ms, this.NotBefore);
    this.AddDateTime(ms, this.NotAfter);
    this.AddDateTime(ms, this.CreateTime);
    this.AddString(ms, this.KeyWord);
    this.AddString(ms, this.UniqueID);
    ms.Write(BitConverter.GetBytes(this.EffectiveHours));
    if (this.Descriptions == null)
    {
      this.AddBytes(ms, (ushort) 0);
    }
    else
    {
      this.AddBytes(ms, (ushort) this.Descriptions.Count);
      foreach (KeyValuePair<string, string> description in this.Descriptions)
      {
        this.AddString(ms, description.Key);
        this.AddString(ms, description.Value);
      }
    }
    byte[] array1 = ms.ToArray();
    byte[] data = this.privateRsa.SignData(array1, 4, array1.Length - 4, (object) new SHA1CryptoServiceProvider());
    int num = array1.Length - 4;
    this.AddBytes(ms, data);
    byte[] array2 = ms.ToArray();
    array2[0] = BitConverter.GetBytes(num)[0];
    array2[1] = BitConverter.GetBytes(num)[1];
    return array2;
  }

  /// <summary>
  /// 使用给定的公钥，校验当前的证书是否合法的，如果公钥为 null，则直接校验证书本身是否合法。<br />
  /// Use the given public key to verify whether the current certificate is valid, and if the public key is null, directly verify whether the certificate itself is valid.
  /// </summary>
  /// <param name="publicKey">公钥信息，如果不为空，则校验公钥是否一致</param>
  /// <param name="hslCertificate">证书信息</param>
  /// <returns>是否合法</returns>
  public static bool VerifyCer(byte[] publicKey, byte[] hslCertificate)
  {
    if (hslCertificate == null)
      return false;
    int uint16_1 = (int) BitConverter.ToUInt16(hslCertificate, 4);
    if (publicKey != null)
    {
      if (publicKey.Length != uint16_1)
        return false;
      for (int index = 0; index < publicKey.Length; ++index)
      {
        if ((int) publicKey[index] != (int) hslCertificate[index + 6])
          return false;
      }
    }
    int uint16_2 = (int) BitConverter.ToUInt16(hslCertificate, 0);
    int uint16_3 = (int) BitConverter.ToUInt16(hslCertificate, uint16_2 + 4);
    return RSAHelper.CreateRsaProviderFromPublicKey(hslCertificate.SelectMiddle<byte>(6, uint16_1)).VerifyData(hslCertificate.SelectMiddle<byte>(4, uint16_2), (object) new SHA1CryptoServiceProvider(), hslCertificate.SelectMiddle<byte>(uint16_2 + 6, uint16_3));
  }

  /// <summary>
  /// 从证书的原始字节创建一个<see cref="T:HslCommunication.Core.Security.HslCertificate" />对象，方便浏览证书的基本信息。<br />
  /// Create a <see cref="T:HslCommunication.Core.Security.HslCertificate" /> object from the original bytes of the certificate to facilitate browsing the basic information of the certificate.
  /// </summary>
  /// <param name="hslCertificate">证书信息</param>
  /// <param name="pubKey">公钥对象</param>
  /// <param name="priKey">私钥对象</param>
  /// <returns>证书的可描述对象</returns>
  public static HslCertificate CreateFrom(byte[] hslCertificate, byte[] pubKey = null, byte[] priKey = null)
  {
    HslCertificate from = new HslCertificate(pubKey, priKey);
    from.LoadFrom(hslCertificate);
    return from;
  }
}
