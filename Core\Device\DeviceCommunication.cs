﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Device.DeviceCommunication
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Device;

/// <summary>所有设备的基类信息</summary>
public class DeviceCommunication : BinaryCommunication, IReadWriteDevice, IReadWriteNet, IDisposable
{
  private bool disposedValue = false;
  private IByteTransform byteTransform = (IByteTransform) new RegularByteTransform();

  /// <summary>
  /// 一个字单位的数据表示的地址长度，西门子为2，三菱，欧姆龙，modbusTcp就为1，AB PLC无效<br />
  /// The address length represented by one word of data, Siemens is 2, Mitsubishi, Omron, modbusTcp is 1, AB PLC is invalid
  /// </summary>
  /// <remarks>
  /// 对设备来说，一个地址的数据对应的字节数，或是1个字节或是2个字节，4个字节，通常是这四个选择，当设置为0时，则表示4字节的地址长度信息<br />
  /// For the device, the number of bytes corresponding to the data of an address, either 1 byte or 2 bytes, 4 bytes, usually these four choices, when set to 0, it means 4 words Section address length information
  /// </remarks>
  protected ushort WordLength { get; set; } = 1;

  /// <summary>
  /// 一个字单位的数据表示的地址长度，西门子为2，三菱，欧姆龙，modbusTcp就为1，AB PLC无效<br />
  /// The address length represented by one word of data, Siemens is 2, Mitsubishi, Omron, modbusTcp is 1, AB PLC is invalid
  /// </summary>
  /// <remarks>
  /// 对设备来说，一个地址的数据对应的字节数，或是1个字节或是2个字节，通常是这两个选择。<br />
  /// 当前也可以重写来根据不同的地址动态控制不同的地址长度，比如有的地址是一个地址一个字节的，有的地址是一个地址两个字节的
  /// </remarks>
  /// <param name="address">读取的设备的地址信息</param>
  /// <param name="length">读取的数据长度信息</param>
  /// <param name="dataTypeLength">数据类型的字节长度信息，比如short, 就是2，int,float就是4</param>
  protected virtual ushort GetWordLength(string address, int length, int dataTypeLength)
  {
    if (this.WordLength != (ushort) 0)
      return (ushort) ((int) this.WordLength * length * dataTypeLength);
    int num = length * dataTypeLength * 2 / 4;
    return num == 0 ? (ushort) 1 : (ushort) num;
  }

  /// <summary>
  /// 当前的数据变换机制，当你需要从字节数据转换类型数据的时候需要。<br />
  /// The current data transformation mechanism is required when you need to convert type data from byte data.
  /// </summary>
  /// <remarks>
  /// 在HSL里提供了三种数据变换机制，分别是 <see cref="T:HslCommunication.Core.RegularByteTransform" />, <see cref="T:HslCommunication.Core.ReverseBytesTransform" />,
  /// <see cref="T:HslCommunication.Core.ReverseWordTransform" />，各自的<see cref="T:HslCommunication.Core.DataFormat" />属性也可以自定调整，基本满足所有的情况使用。<br />
  /// Three data transformation mechanisms are provided in HSL, namely <see cref="T:HslCommunication.Core.RegularByteTransform" />, <see cref="T:HslCommunication.Core.ReverseBytesTransform" />,
  /// <see cref="T:HslCommunication.Core.ReverseWordTransform" />, and their respective <see cref="T:HslCommunication.Core.DataFormat" /> property can also be adjusted by itself, basically satisfying all situations.
  /// </remarks>
  /// <example>
  /// 主要是用来转换数据类型的，下面仅仅演示了2个方法，其他的类型转换，类似处理。
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkDoubleBase.cs" region="ByteTransform" title="ByteTransform示例" />
  /// </example>
  public IByteTransform ByteTransform
  {
    get => this.byteTransform;
    set => this.byteTransform = value;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public virtual OperateResult<byte[]> Read(string address, ushort length)
  {
    return new OperateResult<byte[]>(StringResources.Language.NotSupportedFunction);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public virtual OperateResult Write(string address, byte[] value)
  {
    return new OperateResult(StringResources.Language.NotSupportedFunction);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public virtual OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return new OperateResult<bool[]>(StringResources.Language.NotSupportedFunction);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String)" />
  [HslMqttApi("ReadBool", "")]
  public virtual OperateResult<bool> ReadBool(string address)
  {
    return ByteTransformHelper.GetResultFromArray<bool>(this.ReadBool(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public virtual OperateResult Write(string address, bool[] value)
  {
    return new OperateResult(StringResources.Language.NotSupportedFunction);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public virtual OperateResult Write(string address, bool value)
  {
    return this.Write(address, new bool[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String)" />
  public OperateResult<T> ReadCustomer<T>(string address) where T : IDataTransfer, new()
  {
    return ReadWriteNetHelper.ReadCustomer<T>((IReadWriteNet) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomer``1(System.String,``0)" />
  public OperateResult<T> ReadCustomer<T>(string address, T obj) where T : IDataTransfer, new()
  {
    return ReadWriteNetHelper.ReadCustomer<T>((IReadWriteNet) this, address, obj);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteCustomer``1(System.String,``0)" />
  public OperateResult WriteCustomer<T>(string address, T data) where T : IDataTransfer, new()
  {
    return ReadWriteNetHelper.WriteCustomer<T>((IReadWriteNet) this, address, data);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Read``1" />
  public virtual OperateResult<T> Read<T>() where T : class, new()
  {
    return HslReflectionHelper.Read<T>((IReadWriteNet) this);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write``1(``0)" />
  public virtual OperateResult Write<T>(T data) where T : class, new()
  {
    return HslReflectionHelper.Write<T>(data, (IReadWriteNet) this);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStruct``1(System.String,System.UInt16)" />
  public virtual OperateResult<T> ReadStruct<T>(string address, ushort length) where T : class, new()
  {
    return ReadWriteNetHelper.ReadStruct<T>((IReadWriteNet) this, address, length, this.ByteTransform);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16(System.String)" />
  [HslMqttApi("ReadInt16", "")]
  public OperateResult<short> ReadInt16(string address)
  {
    return ByteTransformHelper.GetResultFromArray<short>(this.ReadInt16(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt16Array", "")]
  public virtual OperateResult<short[]> ReadInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<short[]>(this.Read(address, this.GetWordLength(address, (int) length, 1)), (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16(System.String)" />
  [HslMqttApi("ReadUInt16", "")]
  public OperateResult<ushort> ReadUInt16(string address)
  {
    return ByteTransformHelper.GetResultFromArray<ushort>(this.ReadUInt16(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt16Array", "")]
  public virtual OperateResult<ushort[]> ReadUInt16(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(this.Read(address, this.GetWordLength(address, (int) length, 1)), (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32(System.String)" />
  [HslMqttApi("ReadInt32", "")]
  public OperateResult<int> ReadInt32(string address)
  {
    return ByteTransformHelper.GetResultFromArray<int>(this.ReadInt32(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt32Array", "")]
  public virtual OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<int[]>(this.Read(address, this.GetWordLength(address, (int) length, 2)), (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32(System.String)" />
  [HslMqttApi("ReadUInt32", "")]
  public OperateResult<uint> ReadUInt32(string address)
  {
    return ByteTransformHelper.GetResultFromArray<uint>(this.ReadUInt32(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt32Array", "")]
  public virtual OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<uint[]>(this.Read(address, this.GetWordLength(address, (int) length, 2)), (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloat(System.String)" />
  [HslMqttApi("ReadFloat", "")]
  public OperateResult<float> ReadFloat(string address)
  {
    return ByteTransformHelper.GetResultFromArray<float>(this.ReadFloat(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloat(System.String,System.UInt16)" />
  [HslMqttApi("ReadFloatArray", "")]
  public virtual OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<float[]>(this.Read(address, this.GetWordLength(address, (int) length, 2)), (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64(System.String)" />
  [HslMqttApi("ReadInt64", "")]
  public OperateResult<long> ReadInt64(string address)
  {
    return ByteTransformHelper.GetResultFromArray<long>(this.ReadInt64(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt64Array", "")]
  public virtual OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<long[]>(this.Read(address, this.GetWordLength(address, (int) length, 4)), (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64(System.String)" />
  [HslMqttApi("ReadUInt64", "")]
  public OperateResult<ulong> ReadUInt64(string address)
  {
    return ByteTransformHelper.GetResultFromArray<ulong>(this.ReadUInt64(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt64Array", "")]
  public virtual OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(this.Read(address, this.GetWordLength(address, (int) length, 4)), (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDouble(System.String)" />
  [HslMqttApi("ReadDouble", "")]
  public OperateResult<double> ReadDouble(string address)
  {
    return ByteTransformHelper.GetResultFromArray<double>(this.ReadDouble(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDouble(System.String,System.UInt16)" />
  [HslMqttApi("ReadDoubleArray", "")]
  public virtual OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return ByteTransformHelper.GetResultFromBytes<double[]>(this.Read(address, this.GetWordLength(address, (int) length, 4)), (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadString(System.String,System.UInt16)" />
  [HslMqttApi("ReadString", "")]
  public virtual OperateResult<string> ReadString(string address, ushort length)
  {
    return this.ReadString(address, length, Encoding.ASCII);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadString(System.String,System.UInt16,System.Text.Encoding)" />
  public virtual OperateResult<string> ReadString(string address, ushort length, Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromBytes<string>(this.Read(address, length), (Func<byte[], string>) (m => this.ByteTransform.TransString(m, 0, m.Length, encoding)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int16[])" />
  [HslMqttApi("WriteInt16Array", "")]
  public virtual OperateResult Write(string address, short[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int16)" />
  [HslMqttApi("WriteInt16", "")]
  public virtual OperateResult Write(string address, short value)
  {
    return this.Write(address, new short[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt16[])" />
  [HslMqttApi("WriteUInt16Array", "")]
  public virtual OperateResult Write(string address, ushort[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt16)" />
  [HslMqttApi("WriteUInt16", "")]
  public virtual OperateResult Write(string address, ushort value)
  {
    return this.Write(address, new ushort[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int32[])" />
  [HslMqttApi("WriteInt32Array", "")]
  public virtual OperateResult Write(string address, int[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int32)" />
  [HslMqttApi("WriteInt32", "")]
  public virtual OperateResult Write(string address, int value)
  {
    return this.Write(address, new int[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt32[])" />
  [HslMqttApi("WriteUInt32Array", "")]
  public virtual OperateResult Write(string address, uint[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt32)" />
  [HslMqttApi("WriteUInt32", "")]
  public virtual OperateResult Write(string address, uint value)
  {
    return this.Write(address, new uint[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Single[])" />
  [HslMqttApi("WriteFloatArray", "")]
  public virtual OperateResult Write(string address, float[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Single)" />
  [HslMqttApi("WriteFloat", "")]
  public virtual OperateResult Write(string address, float value)
  {
    return this.Write(address, new float[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int64[])" />
  [HslMqttApi("WriteInt64Array", "")]
  public virtual OperateResult Write(string address, long[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int64)" />
  [HslMqttApi("WriteInt64", "")]
  public virtual OperateResult Write(string address, long value)
  {
    return this.Write(address, new long[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt64[])" />
  [HslMqttApi("WriteUInt64Array", "")]
  public virtual OperateResult Write(string address, ulong[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt64)" />
  [HslMqttApi("WriteUInt64", "")]
  public virtual OperateResult Write(string address, ulong value)
  {
    return this.Write(address, new ulong[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double[])" />
  [HslMqttApi("WriteDoubleArray", "")]
  public virtual OperateResult Write(string address, double[] values)
  {
    return this.Write(address, this.ByteTransform.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double)" />
  [HslMqttApi("WriteDouble", "")]
  public virtual OperateResult Write(string address, double value)
  {
    return this.Write(address, new double[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String)" />
  [HslMqttApi("WriteString", "")]
  public virtual OperateResult Write(string address, string value)
  {
    return this.Write(address, value, Encoding.ASCII);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String,System.Int32)" />
  public virtual OperateResult Write(string address, string value, int length)
  {
    return this.Write(address, value, length, Encoding.ASCII);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String,System.Text.Encoding)" />
  public virtual OperateResult Write(string address, string value, Encoding encoding)
  {
    byte[] data = this.ByteTransform.TransByte(value, encoding);
    if (this.WordLength == (ushort) 1)
      data = SoftBasic.ArrayExpandToLengthEven<byte>(data);
    return this.Write(address, data);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.String,System.Int32,System.Text.Encoding)" />
  public virtual OperateResult Write(string address, string value, int length, Encoding encoding)
  {
    byte[] data = this.ByteTransform.TransByte(value, encoding);
    if (this.WordLength == (ushort) 1)
      data = SoftBasic.ArrayExpandToLengthEven<byte>(data);
    byte[] length1 = SoftBasic.ArrayExpandToLength<byte>(data, length);
    return this.Write(address, length1);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Boolean,System.Int32,System.Int32)" />
  [HslMqttApi("WaitBool", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    bool waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int16,System.Int32,System.Int32)" />
  [HslMqttApi("WaitInt16", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    short waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt16,System.Int32,System.Int32)" />
  [HslMqttApi("WaitUInt16", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    ushort waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int32,System.Int32,System.Int32)" />
  [HslMqttApi("WaitInt32", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    int waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt32,System.Int32,System.Int32)" />
  [HslMqttApi("WaitUInt32", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    uint waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int64,System.Int32,System.Int32)" />
  [HslMqttApi("WaitInt64", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    long waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt64,System.Int32,System.Int32)" />
  [HslMqttApi("WaitUInt64", "")]
  public OperateResult<TimeSpan> Wait(
    string address,
    ulong waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    return ReadWriteNetHelper.Wait((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Boolean,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    bool waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int16,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    short waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt16,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    ushort waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int32,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    int waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt32,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    uint waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.Int64,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    long waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Wait(System.String,System.UInt64,System.Int32,System.Int32)" />
  public async Task<OperateResult<TimeSpan>> WaitAsync(
    string address,
    ulong waitValue,
    int readInterval = 100,
    int waitTimeout = -1)
  {
    OperateResult<TimeSpan> operateResult = await ReadWriteNetHelper.WaitAsync((IReadWriteNet) this, address, waitValue, readInterval, waitTimeout);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await Task.Run<OperateResult<byte[]>>((Func<OperateResult<byte[]>>) (() => this.Read(address, length)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Byte[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBoolAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await Task.Run<OperateResult<bool[]>>((Func<OperateResult<bool[]>>) (() => this.ReadBool(address, length)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBoolAsync(System.String)" />
  public virtual async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<bool[]> result = await this.ReadBoolAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<bool>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Boolean[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, bool[] value)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Write(address, value)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Boolean)" />
  public virtual async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new bool[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomerAsync``1(System.String)" />
  public async Task<OperateResult<T>> ReadCustomerAsync<T>(string address) where T : IDataTransfer, new()
  {
    OperateResult<T> operateResult = await ReadWriteNetHelper.ReadCustomerAsync<T>((IReadWriteNet) this, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadCustomerAsync``1(System.String,``0)" />
  public async Task<OperateResult<T>> ReadCustomerAsync<T>(string address, T obj) where T : IDataTransfer, new()
  {
    OperateResult<T> operateResult = await ReadWriteNetHelper.ReadCustomerAsync<T>((IReadWriteNet) this, address, obj);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteCustomerAsync``1(System.String,``0)" />
  public async Task<OperateResult> WriteCustomerAsync<T>(string address, T data) where T : IDataTransfer, new()
  {
    OperateResult operateResult = await ReadWriteNetHelper.WriteCustomerAsync<T>((IReadWriteNet) this, address, data);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadAsync``1" />
  public virtual async Task<OperateResult<T>> ReadAsync<T>() where T : class, new()
  {
    OperateResult<T> operateResult = await HslReflectionHelper.ReadAsync<T>((IReadWriteNet) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync``1(``0)" />
  public virtual async Task<OperateResult> WriteAsync<T>(T data) where T : class, new()
  {
    OperateResult operateResult = await HslReflectionHelper.WriteAsync<T>(data, (IReadWriteNet) this);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStruct``1(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<T>> ReadStructAsync<T>(string address, ushort length) where T : class, new()
  {
    OperateResult<T> operateResult = await ReadWriteNetHelper.ReadStructAsync<T>((IReadWriteNet) this, address, length, this.ByteTransform);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16Async(System.String)" />
  public async Task<OperateResult<short>> ReadInt16Async(string address)
  {
    OperateResult<short[]> result = await this.ReadInt16Async(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<short>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt16Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<short[]>> ReadInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 1));
    return ByteTransformHelper.GetResultFromBytes<short[]>(result, (Func<byte[], short[]>) (m => this.ByteTransform.TransInt16(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16Async(System.String)" />
  public async Task<OperateResult<ushort>> ReadUInt16Async(string address)
  {
    OperateResult<ushort[]> result = await this.ReadUInt16Async(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<ushort>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt16Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<ushort[]>> ReadUInt16Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 1));
    return ByteTransformHelper.GetResultFromBytes<ushort[]>(result, (Func<byte[], ushort[]>) (m => this.ByteTransform.TransUInt16(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32Async(System.String)" />
  public async Task<OperateResult<int>> ReadInt32Async(string address)
  {
    OperateResult<int[]> result = await this.ReadInt32Async(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<int>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 2));
    return ByteTransformHelper.GetResultFromBytes<int[]>(result, (Func<byte[], int[]>) (m => this.ByteTransform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32Async(System.String)" />
  public async Task<OperateResult<uint>> ReadUInt32Async(string address)
  {
    OperateResult<uint[]> result = await this.ReadUInt32Async(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<uint>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 2));
    return ByteTransformHelper.GetResultFromBytes<uint[]>(result, (Func<byte[], uint[]>) (m => this.ByteTransform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloatAsync(System.String)" />
  public async Task<OperateResult<float>> ReadFloatAsync(string address)
  {
    OperateResult<float[]> result = await this.ReadFloatAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<float>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloatAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 2));
    return ByteTransformHelper.GetResultFromBytes<float[]>(result, (Func<byte[], float[]>) (m => this.ByteTransform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64Async(System.String)" />
  public async Task<OperateResult<long>> ReadInt64Async(string address)
  {
    OperateResult<long[]> result = await this.ReadInt64Async(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<long>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 4));
    return ByteTransformHelper.GetResultFromBytes<long[]>(result, (Func<byte[], long[]>) (m => this.ByteTransform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64Async(System.String)" />
  public async Task<OperateResult<ulong>> ReadUInt64Async(string address)
  {
    OperateResult<ulong[]> result = await this.ReadUInt64Async(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<ulong>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64Async(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 4));
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(result, (Func<byte[], ulong[]>) (m => this.ByteTransform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDoubleAsync(System.String)" />
  public async Task<OperateResult<double>> ReadDoubleAsync(string address)
  {
    OperateResult<double[]> result = await this.ReadDoubleAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<double>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDoubleAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 4));
    return ByteTransformHelper.GetResultFromBytes<double[]>(result, (Func<byte[], double[]>) (m => this.ByteTransform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStringAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult<string>> ReadStringAsync(string address, ushort length)
  {
    OperateResult<string> operateResult = await this.ReadStringAsync(address, length, Encoding.ASCII);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadStringAsync(System.String,System.UInt16,System.Text.Encoding)" />
  public virtual async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> result = await this.ReadAsync(address, length);
    return ByteTransformHelper.GetResultFromBytes<string>(result, (Func<byte[], string>) (m => this.ByteTransform.TransString(m, 0, m.Length, encoding)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int16[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, short[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int16)" />
  public virtual async Task<OperateResult> WriteAsync(string address, short value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new short[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt16[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, ushort[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt16)" />
  public virtual async Task<OperateResult> WriteAsync(string address, ushort value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new ushort[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int32[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int32)" />
  public virtual async Task<OperateResult> WriteAsync(string address, int value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new int[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt32[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt32)" />
  public virtual async Task<OperateResult> WriteAsync(string address, uint value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new uint[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Single[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Single)" />
  public virtual async Task<OperateResult> WriteAsync(string address, float value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new float[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int64[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, long[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int64)" />
  public virtual async Task<OperateResult> WriteAsync(string address, long value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new long[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt64[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, ulong[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt64)" />
  public virtual async Task<OperateResult> WriteAsync(string address, ulong value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new ulong[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Double[])" />
  public virtual async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    OperateResult operateResult = await this.WriteAsync(address, this.ByteTransform.TransByte(values));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Double)" />
  public virtual async Task<OperateResult> WriteAsync(string address, double value)
  {
    OperateResult operateResult = await this.WriteAsync(address, new double[1]
    {
      value
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String)" />
  public virtual async Task<OperateResult> WriteAsync(string address, string value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value, Encoding.ASCII);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String,System.Text.Encoding)" />
  public virtual async Task<OperateResult> WriteAsync(
    string address,
    string value,
    Encoding encoding)
  {
    byte[] temp = this.ByteTransform.TransByte(value, encoding);
    if (this.WordLength == (ushort) 1)
      temp = SoftBasic.ArrayExpandToLengthEven<byte>(temp);
    OperateResult operateResult = await this.WriteAsync(address, temp);
    temp = (byte[]) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String,System.Int32)" />
  public virtual async Task<OperateResult> WriteAsync(string address, string value, int length)
  {
    OperateResult operateResult = await this.WriteAsync(address, value, length, Encoding.ASCII);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.String,System.Int32,System.Text.Encoding)" />
  public virtual async Task<OperateResult> WriteAsync(
    string address,
    string value,
    int length,
    Encoding encoding)
  {
    byte[] temp = this.ByteTransform.TransByte(value, encoding);
    if (this.WordLength == (ushort) 1)
      temp = SoftBasic.ArrayExpandToLengthEven<byte>(temp);
    temp = SoftBasic.ArrayExpandToLength<byte>(temp, length);
    OperateResult operateResult = await this.WriteAsync(address, temp);
    temp = (byte[]) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  protected virtual void Dispose(bool disposing)
  {
    if (!disposing)
      return;
    this.CommunicationPipe?.CloseCommunication();
    this.CommunicationPipe?.Dispose();
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    if (this.disposedValue)
      return;
    this.Dispose(true);
    this.disposedValue = true;
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"DeviceCommunication<{this.byteTransform}>{{{this.CommunicationPipe}}}";
  }
}
