﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Fuji.FujiSPBHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Fuji;

/// <summary>富士SPB的辅助类</summary>
public class FujiSPBHelper
{
  /// <summary>将int数据转换成SPB可识别的标准的数据内容，例如 2转换为0200 , 200转换为0002</summary>
  /// <param name="address">等待转换的数据内容</param>
  /// <returns>转换之后的数据内容</returns>
  public static string AnalysisIntegerAddress(int address)
  {
    string str = address.ToString("D4");
    return str.Substring(2) + str.Substring(0, 2);
  }

  /// <summary>计算指令的和校验码</summary>
  /// <param name="data">指令</param>
  /// <returns>校验之后的信息</returns>
  public static string CalculateAcc(string data)
  {
    byte[] bytes = Encoding.ASCII.GetBytes(data);
    int num = 0;
    for (int index = 0; index < bytes.Length; ++index)
      num += (int) bytes[index];
    return num.ToString("X4").Substring(2);
  }

  /// <summary>创建一条读取的指令信息，需要指定一些参数，单次读取最大105个字</summary>
  /// <param name="station">PLC的站号</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<byte[]> BuildReadCommand(byte station, string address, ushort length)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FujiSPBAddress> from = FujiSPBAddress.ParseFrom(address);
    return !from.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) from) : FujiSPBHelper.BuildReadCommand(station, from.Content, length);
  }

  /// <summary>创建一条读取的指令信息，需要指定一些参数，单次读取最大105个字</summary>
  /// <param name="station">PLC的站号</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<byte[]> BuildReadCommand(
    byte station,
    FujiSPBAddress address,
    ushort length)
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(':');
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("09");
    stringBuilder.Append("FFFF");
    stringBuilder.Append("00");
    stringBuilder.Append("00");
    stringBuilder.Append(address.GetWordAddress());
    stringBuilder.Append(FujiSPBHelper.AnalysisIntegerAddress((int) length));
    stringBuilder.Append("\r\n");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>创建一条读取多个地址的指令信息，需要指定一些参数，单次读取最大105个字</summary>
  /// <param name="station">PLC的站号</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <param name="isBool">是否位读取</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<byte[]> BuildReadCommand(
    byte station,
    string[] address,
    ushort[] length,
    bool isBool)
  {
    if (address == null || length == null)
      return new OperateResult<byte[]>("Parameter address or length can't be null");
    if (address.Length != length.Length)
      return new OperateResult<byte[]>(StringResources.Language.TwoParametersLengthIsNotSame);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(':');
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append((6 + address.Length * 4).ToString("X2"));
    stringBuilder.Append("FFFF");
    stringBuilder.Append("00");
    stringBuilder.Append("04");
    stringBuilder.Append("00");
    stringBuilder.Append(address.Length.ToString("X2"));
    for (int index = 0; index < address.Length; ++index)
    {
      station = (byte) HslHelper.ExtractParameter(ref address[index], "s", (int) station);
      OperateResult<FujiSPBAddress> from = FujiSPBAddress.ParseFrom(address[index]);
      if (!from.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
      stringBuilder.Append(from.Content.TypeCode);
      stringBuilder.Append(length[index].ToString("X2"));
      stringBuilder.Append(FujiSPBHelper.AnalysisIntegerAddress(from.Content.AddressStart));
    }
    stringBuilder[1] = station.ToString("X2")[0];
    stringBuilder[2] = station.ToString("X2")[1];
    stringBuilder.Append("\r\n");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>创建一条别入byte数据的指令信息，需要指定一些参数，按照字单位，单次写入最大103个字</summary>
  /// <param name="station">站号</param>
  /// <param name="address">地址</param>
  /// <param name="value">数组值</param>
  /// <returns>是否创建成功</returns>
  public static OperateResult<byte[]> BuildWriteByteCommand(
    byte station,
    string address,
    byte[] value)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FujiSPBAddress> from = FujiSPBAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(':');
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("00");
    stringBuilder.Append("FFFF");
    stringBuilder.Append("01");
    stringBuilder.Append("00");
    stringBuilder.Append(from.Content.GetWordAddress());
    stringBuilder.Append(FujiSPBHelper.AnalysisIntegerAddress(value.Length / 2));
    stringBuilder.Append(value.ToHexString());
    stringBuilder[3] = ((stringBuilder.Length - 5) / 2).ToString("X2")[0];
    stringBuilder[4] = ((stringBuilder.Length - 5) / 2).ToString("X2")[1];
    stringBuilder.Append("\r\n");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>创建一条别入byte数据的指令信息，需要指定一些参数，按照字单位，单次写入最大103个字</summary>
  /// <param name="station">站号</param>
  /// <param name="address">地址</param>
  /// <param name="value">数组值</param>
  /// <returns>是否创建成功</returns>
  public static OperateResult<byte[]> BuildWriteBoolCommand(
    byte station,
    string address,
    bool value)
  {
    station = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FujiSPBAddress> from = FujiSPBAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    if ((address.StartsWith("X") || address.StartsWith("Y") || address.StartsWith("M") || address.StartsWith("L") || address.StartsWith("TC") || address.StartsWith("CC")) && address.IndexOf('.') < 0)
    {
      from.Content.BitIndex = from.Content.AddressStart % 16 /*0x10*/;
      from.Content.AddressStart = (int) (ushort) (from.Content.AddressStart / 16 /*0x10*/);
    }
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(':');
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("00");
    stringBuilder.Append("FFFF");
    stringBuilder.Append("01");
    stringBuilder.Append("02");
    stringBuilder.Append(from.Content.GetWriteBoolAddress());
    stringBuilder.Append(value ? "01" : "00");
    stringBuilder[3] = ((stringBuilder.Length - 5) / 2).ToString("X2")[0];
    stringBuilder[4] = ((stringBuilder.Length - 5) / 2).ToString("X2")[1];
    stringBuilder.Append("\r\n");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>检查反馈的数据信息，是否包含了错误码，如果没有包含，则返回成功</summary>
  /// <param name="content">原始的报文返回</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<byte[]> CheckResponseData(byte[] content)
  {
    try
    {
      if (content[0] != (byte) 58)
        return new OperateResult<byte[]>((int) content[0], "Read Faild:" + SoftBasic.ByteToHexString(content, ' '));
      string code = Encoding.ASCII.GetString(content, 9, 2);
      if (code != "00")
        return new OperateResult<byte[]>(Convert.ToInt32(code, 16 /*0x10*/), FujiSPBHelper.GetErrorDescriptionFromCode(code));
      if (content[content.Length - 2] == (byte) 13 && content[content.Length - 1] == (byte) 10)
        content = content.RemoveLast<byte>(2);
      return OperateResult.CreateSuccessResult<byte[]>(content.RemoveBegin<byte>(11));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"CheckResponseData failed: {ex.Message}{Environment.NewLine}Source: {content.ToHexString(' ')}");
    }
  }

  /// <summary>根据错误码获取到真实的文本信息</summary>
  /// <param name="code">错误码</param>
  /// <returns>错误的文本描述</returns>
  public static string GetErrorDescriptionFromCode(string code)
  {
    switch (code)
    {
      case "01":
        return StringResources.Language.FujiSpbStatus01;
      case "02":
        return StringResources.Language.FujiSpbStatus02;
      case "03":
        return StringResources.Language.FujiSpbStatus03;
      case "04":
        return StringResources.Language.FujiSpbStatus04;
      case "05":
        return StringResources.Language.FujiSpbStatus05;
      case "06":
        return StringResources.Language.FujiSpbStatus06;
      case "07":
        return StringResources.Language.FujiSpbStatus07;
      case "09":
        return StringResources.Language.FujiSpbStatus09;
      case "0C":
        return StringResources.Language.FujiSpbStatus0C;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>
  /// 批量读取PLC的数据，以字为单位，支持读取X,Y,L,M,D,TN,CN,TC,CC,R,W具体的地址范围需要根据PLC型号来确认，地址可以携带站号信息，例如：s=2;D100<br />
  /// Read PLC data in batches, in units of words. Supports reading X, Y, L, M, D, TN, CN, TC, CC, R, W.
  /// The specific address range needs to be confirmed according to the PLC model, The address can carry station number information, for example: s=2;D100
  /// </summary>
  /// <param name="device">PLC设备通信对象</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>读取结果信息</returns>
  /// <remarks>单次读取的最大的字数为105，如果读取的字数超过这个值，请分批次读取。</remarks>
  public static OperateResult<byte[]> Read(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<byte[]> result1 = FujiSPBHelper.BuildReadCommand(station, address, length);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = device.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
    OperateResult<byte[]> result3 = FujiSPBHelper.CheckResponseData(result2.Content);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result3) : OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetString(result3.Content.RemoveBegin<byte>(4)).ToHexBytes());
  }

  /// <summary>
  /// 批量写入PLC的数据，以字为单位，也就是说最少2个字节信息，支持读取X,Y,L,M,D,TN,CN,TC,CC,R具体的地址范围需要根据PLC型号来确认，地址可以携带站号信息，例如：s=2;D100<br />
  /// The data written to the PLC in batches, in units of words, that is, a minimum of 2 bytes of information. It supports reading X, Y, L, M, D, TN, CN, TC, CC, and R.
  /// The specific address range needs to be based on PLC model to confirm, The address can carry station number information, for example: s=2;D100
  /// </summary>
  /// <param name="device">PLC设备通信对象</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="address">地址信息，举例，D100，R200，TN100，CN200</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  /// <remarks>单次写入的最大的字数为103个字，如果写入的数据超过这个长度，请分批次写入</remarks>
  public static OperateResult Write(
    IReadWriteDevice device,
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> operateResult1 = FujiSPBHelper.BuildWriteByteCommand(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = device.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) FujiSPBHelper.CheckResponseData(operateResult2.Content);
  }

  /// <summary>
  /// 批量读取PLC的Bool数据，以位为单位，支持读取X,Y,L,M,D,TN,CN,TC,CC,R,W，例如 M100, 如果是寄存器地址，可以使用D10.12来访问第10个字的12位，地址可以携带站号信息，例如：s=2;M100<br />
  /// Read PLC's Bool data in batches, in units of bits, support reading X, Y, L, M, D, TN, CN, TC, CC, R, W, such as M100, if it is a register address,
  /// you can use D10. 12 to access the 12 bits of the 10th word, the address can carry station number information, for example: s=2;M100
  /// </summary>
  /// <param name="device">PLC设备通信对象</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="address">地址信息，举例：M100, D10.12</param>
  /// <param name="length">读取的bool长度信息</param>
  /// <returns>Bool[]的结果对象</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    byte parameter = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FujiSPBAddress> from = FujiSPBAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    if ((address.StartsWith("X") || address.StartsWith("Y") || address.StartsWith("M") || address.StartsWith("L") || address.StartsWith("TC") || address.StartsWith("CC")) && address.IndexOf('.') < 0)
    {
      from.Content.BitIndex = from.Content.AddressStart % 16 /*0x10*/;
      from.Content.AddressStart = (int) (ushort) (from.Content.AddressStart / 16 /*0x10*/);
    }
    ushort length1 = (ushort) ((from.Content.GetBitIndex() + (int) length - 1) / 16 /*0x10*/ - from.Content.GetBitIndex() / 16 /*0x10*/ + 1);
    OperateResult<byte[]> result1 = FujiSPBHelper.BuildReadCommand(parameter, from.Content, length1);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = device.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
    OperateResult<byte[]> result3 = FujiSPBHelper.CheckResponseData(result2.Content);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result3) : OperateResult.CreateSuccessResult<bool[]>(Encoding.ASCII.GetString(result3.Content.RemoveBegin<byte>(4)).ToHexBytes().ToBoolArray().SelectMiddle<bool>(from.Content.BitIndex, (int) length));
  }

  /// <summary>
  /// 写入一个Bool值到一个地址里，地址可以是线圈地址，也可以是寄存器地址，例如：M100, D10.12，地址可以携带站号信息，例如：s=2;D10.12<br />
  /// Write a Bool value to an address. The address can be a coil address or a register address, for example: M100, D10.12.
  /// The address can carry station number information, for example: s=2;D10.12
  /// </summary>
  /// <param name="device">PLC设备通信对象</param>
  /// <param name="station">当前的站号信息</param>
  /// <param name="address">地址信息，举例：M100, D10.12</param>
  /// <param name="value">写入的bool值</param>
  /// <returns>是否写入成功的结果对象</returns>
  public static OperateResult Write(
    IReadWriteDevice device,
    byte station,
    string address,
    bool value)
  {
    OperateResult<byte[]> operateResult1 = FujiSPBHelper.BuildWriteBoolCommand(station, address, value);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = device.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : (OperateResult) FujiSPBHelper.CheckResponseData(operateResult2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    OperateResult<byte[]> command = FujiSPBHelper.BuildReadCommand(station, address, length);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
    OperateResult<byte[]> check = FujiSPBHelper.CheckResponseData(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetString(check.Content.RemoveBegin<byte>(4)).ToHexBytes()) : OperateResult.CreateFailedResult<byte[]>((OperateResult) check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> command = FujiSPBHelper.BuildWriteByteCommand(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? (OperateResult) FujiSPBHelper.CheckResponseData(read.Content) : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    ushort length)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) station);
    OperateResult<FujiSPBAddress> addressAnalysis = FujiSPBAddress.ParseFrom(address);
    if (!addressAnalysis.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) addressAnalysis);
    if ((address.StartsWith("X") || address.StartsWith("Y") || address.StartsWith("M") || address.StartsWith("L") || address.StartsWith("TC") || address.StartsWith("CC")) && address.IndexOf('.') < 0)
    {
      addressAnalysis.Content.BitIndex = addressAnalysis.Content.AddressStart % 16 /*0x10*/;
      addressAnalysis.Content.AddressStart = (int) (ushort) (addressAnalysis.Content.AddressStart / 16 /*0x10*/);
    }
    ushort len = (ushort) ((addressAnalysis.Content.GetBitIndex() + (int) length - 1) / 16 /*0x10*/ - addressAnalysis.Content.GetBitIndex() / 16 /*0x10*/ + 1);
    OperateResult<byte[]> command = FujiSPBHelper.BuildReadCommand(stat, addressAnalysis.Content, len);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
    OperateResult<byte[]> check = FujiSPBHelper.CheckResponseData(read.Content);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<bool[]>(Encoding.ASCII.GetString(check.Content.RemoveBegin<byte>(4)).ToHexBytes().ToBoolArray().SelectMiddle<bool>(addressAnalysis.Content.BitIndex, (int) length)) : OperateResult.CreateFailedResult<bool[]>((OperateResult) check);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Fuji.FujiSPBHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean)" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteDevice device,
    byte station,
    string address,
    bool value)
  {
    OperateResult<byte[]> command = FujiSPBHelper.BuildWriteBoolCommand(station, address, value);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await device.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? (OperateResult) FujiSPBHelper.CheckResponseData(read.Content) : (OperateResult) read;
  }
}
