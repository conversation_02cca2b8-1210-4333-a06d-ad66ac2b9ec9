﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Properties.Resources
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

#nullable disable
namespace HslCommunication.Properties;

/// <summary>一个强类型的资源类，用于查找本地化的字符串等。</summary>
[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[DebuggerNonUserCode]
[CompilerGenerated]
internal class Resources
{
  private static ResourceManager resourceMan;
  private static CultureInfo resourceCulture;

  internal Resources()
  {
  }

  /// <summary>返回此类使用的缓存的 ResourceManager 实例。</summary>
  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal static ResourceManager ResourceManager
  {
    get
    {
      if (HslCommunication.Properties.Resources.resourceMan == null)
        HslCommunication.Properties.Resources.resourceMan = new ResourceManager("HslCommunication.Properties.Resources", typeof (HslCommunication.Properties.Resources).Assembly);
      return HslCommunication.Properties.Resources.resourceMan;
    }
  }

  /// <summary>
  ///   重写当前线程的 CurrentUICulture 属性，对
  ///   使用此强类型资源类的所有资源查找执行重写。
  /// </summary>
  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal static CultureInfo Culture
  {
    get => HslCommunication.Properties.Resources.resourceCulture;
    set => HslCommunication.Properties.Resources.resourceCulture = value;
  }
}
