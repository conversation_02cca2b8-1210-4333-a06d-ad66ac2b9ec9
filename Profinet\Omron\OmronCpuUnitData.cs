﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronCpuUnitData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>欧姆的Cpu的单元信息数据类</summary>
public class OmronCpuUnitData
{
  /// <summary>实例化一个默认的对象</summary>
  public OmronCpuUnitData()
  {
  }

  /// <summary>根据原始的数据来实例化相关的CPU单元信息</summary>
  /// <param name="data">原始字节数</param>
  public OmronCpuUnitData(byte[] data)
  {
    this.Model = Encoding.ASCII.GetString(data, 0, 20).Trim(' ');
    this.Version = Encoding.ASCII.GetString(data, 20, 10).Trim(' ', char.MinValue);
    this.LargestEMNumber = (int) data[41];
    this.ProgramAreaSize = (int) data[80 /*0x50*/] * 256 /*0x0100*/ + (int) data[81];
    this.IOMSize = (int) data[82] * 1024 /*0x0400*/;
    this.DMSize = (int) data[83] * 256 /*0x0100*/ + (int) data[84];
    this.TCSize = (int) data[85] * 1024 /*0x0400*/;
    this.EMSize = (int) data[86];
  }

  /// <summary>Cpu unit Model</summary>
  public string Model { get; set; }

  /// <summary>CPU Unit internal system version</summary>
  public string Version { get; set; }

  /// <summary>Largest number, 0 to 19, in CPU Unit’s EM area.</summary>
  public int LargestEMNumber { get; set; }

  /// <summary>Maximum size of usable program area，unit: k words</summary>
  public int ProgramAreaSize { get; set; }

  /// <summary>
  /// The size of the area (CIO, WR, HR, AR, timer/counter completion flags, TN) in which bit commands can be used( always 23). unit: bytes
  /// </summary>
  public int IOMSize { get; set; }

  /// <summary>Total words in the DM area (always 32,768)</summary>
  public int DMSize { get; set; }

  /// <summary>
  /// Among the banks in the EM area, the number of banks(0 to D ) without file memory
  /// </summary>
  /// <remarks>Banks (1 bank = 32,768 words)</remarks>
  public int EMSize { get; set; }

  /// <summary>
  /// Maximum number of timers/counters available (always 8)
  /// </summary>
  public int TCSize { get; set; }
}
