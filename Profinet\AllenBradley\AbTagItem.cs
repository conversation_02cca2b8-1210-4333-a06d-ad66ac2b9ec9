﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AbTagItem
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>
/// AB PLC的数据标签实体类<br />
/// Data tag entity class of AB PLC
/// </summary>
public class AbTagItem
{
  /// <summary>
  /// 实例化一个默认的对象<br />
  /// instantiate a default object
  /// </summary>
  public AbTagItem()
  {
    this.ArrayLength = new int[3]{ -1, -1, -1 };
  }

  /// <summary>
  /// 实例ID<br />
  /// instance ID
  /// </summary>
  public uint InstanceID { get; set; }

  /// <summary>
  /// 当前标签的名字<br />
  /// the name of the current label
  /// </summary>
  public string Name { get; set; }

  /// <summary>
  /// 当前标签的类型代号，例如 0x0C1 表示bool类型，如果当前的标签的<see cref="P:HslCommunication.Profinet.AllenBradley.AbTagItem.IsStruct" />为 <c>True</c>，那么本属性表示结构体的实例ID<br />
  /// The type code of the current tag, for example 0x0C1 means bool type, if the current tag's <see cref="P:HslCommunication.Profinet.AllenBradley.AbTagItem.IsStruct" /> is <c>True</c>,
  /// then this attribute indicates the instance ID of the structure
  /// </summary>
  public ushort SymbolType { get; set; }

  /// <summary>
  /// 数据的维度信息，默认是0，标量数据，1表示一维数组，2表示二维数组<br />
  /// The dimension information of the data, the default is 0, scalar data, 1 means a one-dimensional array, 2 means a two-dimensional array
  /// </summary>
  public int ArrayDimension { get; set; }

  /// <summary>
  /// 当前的标签是否结构体数据<br />
  /// Whether the current label is structured data
  /// </summary>
  public bool IsStruct { get; set; }

  /// <summary>当前如果是数组，表示数组的长度，仅在读取结构体的变量信息时有效，为-1则是无效。</summary>
  public int[] ArrayLength { get; set; }

  /// <summary>如果当前的标签是结构体的标签，则表示为结构体的成员信息</summary>
  [JsonIgnore]
  public AbTagItem[] Members { get; set; }

  /// <summary>用户自定义的额外的对象</summary>
  [JsonIgnore]
  public object Tag { get; set; }

  /// <summary>
  /// 获取或设置本属性实际数据在结构体中的偏移位置信息<br />
  /// Get or set the offset position information of the actual data of this property in the structure
  /// </summary>
  public int ByteOffset { get; set; }

  /// <summary>获取类型的文本描述信息</summary>
  /// <returns>文本信息</returns>
  public string GetTypeText()
  {
    string str = string.Empty;
    if (this.ArrayDimension == 1)
      str = this.ArrayLength[0] >= 0 ? $"[{this.ArrayLength[0]}]" : "[]";
    else if (this.ArrayDimension == 2)
      str = $"[{this.ArrayLength[0]},{this.ArrayLength[1]}]";
    else if (this.ArrayDimension == 3)
      str = $"[{this.ArrayLength[0]},{this.ArrayLength[1]},{this.ArrayLength[2]}]";
    if (this.IsStruct)
      return "struct" + str;
    if (this.SymbolType == (ushort) 8)
      return "date" + str;
    if (this.SymbolType == (ushort) 9)
      return "time" + str;
    if (this.SymbolType == (ushort) 10)
      return "timeAndDate" + str;
    if (this.SymbolType == (ushort) 11)
      return "timeOfDate" + str;
    if (this.SymbolType == (ushort) 193)
      return "bool" + str;
    if (this.SymbolType == (ushort) 194)
      return "sbyte" + str;
    if (this.SymbolType == (ushort) 195)
      return "short" + str;
    if (this.SymbolType == (ushort) 196)
      return "int" + str;
    if (this.SymbolType == (ushort) 197)
      return "long" + str;
    if (this.SymbolType == (ushort) 198)
      return "byte" + str;
    if (this.SymbolType == (ushort) 199)
      return "ushort" + str;
    if (this.SymbolType == (ushort) 200)
      return "uint" + str;
    if (this.SymbolType == (ushort) 201)
      return "ulong" + str;
    if (this.SymbolType == (ushort) 202)
      return "float" + str;
    if (this.SymbolType == (ushort) 203)
      return "double" + str;
    if (this.SymbolType == (ushort) 204)
      return "struct";
    if (this.SymbolType == (ushort) 208 /*0xD0*/)
      return "string";
    if (this.SymbolType == (ushort) 209)
      return "byte-str";
    if (this.SymbolType == (ushort) 210)
      return "word-str";
    if (this.SymbolType == (ushort) 211)
    {
      if (this.ArrayDimension == 0)
        return "bool[32]";
      return this.ArrayDimension == 1 ? "bool" + $"[{this.ArrayLength[0] * 32 /*0x20*/}]" : "bool-str" + str;
    }
    return ((int) this.SymbolType | 3840 /*0x0F00*/) == 4033 ? "bool" : "";
  }

  /// <inheritdoc />
  public override string ToString() => this.Name;

  private void SetSymbolType(ushort value)
  {
    this.ArrayDimension = ((int) value & 16384 /*0x4000*/) == 16384 /*0x4000*/ ? 2 : (((int) value & 8192 /*0x2000*/) == 8192 /*0x2000*/ ? 1 : 0);
    this.IsStruct = ((int) value & 32768 /*0x8000*/) == 32768 /*0x8000*/;
    this.SymbolType = (ushort) ((uint) value & 4095U /*0x0FFF*/);
  }

  /// <summary>克隆单个的标签数据信息</summary>
  /// <param name="abTagItem">标签信息</param>
  /// <returns>新的实例的标签</returns>
  public static AbTagItem CloneBy(AbTagItem abTagItem)
  {
    if (abTagItem == null)
      return (AbTagItem) null;
    AbTagItem abTagItem1 = new AbTagItem();
    abTagItem1.InstanceID = abTagItem.InstanceID;
    abTagItem1.Name = abTagItem.Name;
    abTagItem1.ByteOffset = abTagItem.ByteOffset;
    abTagItem1.SymbolType = abTagItem.SymbolType;
    abTagItem1.ArrayDimension = abTagItem.ArrayDimension;
    abTagItem1.ArrayLength[0] = abTagItem.ArrayLength[0];
    abTagItem1.ArrayLength[1] = abTagItem.ArrayLength[1];
    abTagItem1.ArrayLength[2] = abTagItem.ArrayLength[2];
    abTagItem1.IsStruct = abTagItem.IsStruct;
    return abTagItem1;
  }

  /// <summary>克隆整个的标签数组信息</summary>
  /// <param name="abTagItems">标签数组信息</param>
  /// <returns>标签数组</returns>
  public static AbTagItem[] CloneBy(AbTagItem[] abTagItems)
  {
    AbTagItem[] abTagItemArray = new AbTagItem[abTagItems.Length];
    for (int index = 0; index < abTagItems.Length; ++index)
      abTagItemArray[index] = AbTagItem.CloneBy(abTagItems[index]);
    return abTagItemArray;
  }

  /// <summary>从指定的原始字节的数据中，解析出实际的节点信息</summary>
  /// <param name="source">原始字节数据</param>
  /// <param name="index">起始的索引</param>
  /// <returns>标签信息</returns>
  public static AbTagItem PraseAbTagItem(byte[] source, ref int index)
  {
    AbTagItem abTagItem = new AbTagItem();
    abTagItem.InstanceID = BitConverter.ToUInt32(source, index);
    index += 4;
    ushort uint16 = BitConverter.ToUInt16(source, index);
    index += 2;
    abTagItem.Name = Encoding.ASCII.GetString(source, index, (int) uint16);
    index += (int) uint16;
    abTagItem.SetSymbolType(BitConverter.ToUInt16(source, index));
    index += 2;
    abTagItem.ArrayLength[0] = BitConverter.ToInt32(source, index);
    index += 4;
    abTagItem.ArrayLength[1] = BitConverter.ToInt32(source, index);
    index += 4;
    abTagItem.ArrayLength[2] = BitConverter.ToInt32(source, index);
    index += 4;
    return abTagItem;
  }

  /// <summary>从指定的原始字节的数据中，解析出实际的标签数组，如果是系统保留的数组，或是__开头的，则自动忽略。</summary>
  /// <param name="source">原始字节数据</param>
  /// <param name="index">起始的索引</param>
  /// <param name="isGlobalVariable">是否局部变量</param>
  /// <param name="instance">输出最后一个标签的实例ID</param>
  /// <returns>标签信息</returns>
  public static List<AbTagItem> PraseAbTagItems(
    byte[] source,
    int index,
    bool isGlobalVariable,
    out uint instance)
  {
    List<AbTagItem> abTagItemList = new List<AbTagItem>();
    instance = 0U;
    while (index < source.Length)
    {
      AbTagItem abTagItem = AbTagItem.PraseAbTagItem(source, ref index);
      instance = abTagItem.InstanceID;
      if (((int) abTagItem.SymbolType & 4096 /*0x1000*/) != 4096 /*0x1000*/ && !abTagItem.Name.StartsWith("__") && !abTagItem.Name.Contains(":"))
      {
        if (!isGlobalVariable)
          abTagItem.Name = "Program:MainProgram." + abTagItem.Name;
        abTagItemList.Add(abTagItem);
      }
    }
    return abTagItemList;
  }

  /// <summary>计算到达指定的字节的长度信息，可以用来计算固定分割符得字节长度</summary>
  /// <param name="source">原始字节数据</param>
  /// <param name="index">索引位置</param>
  /// <param name="value">等待判断的字节</param>
  /// <returns>字符串长度，如果不存在，返回-1</returns>
  private static int CalculatesSpecifiedCharacterLength(byte[] source, int index, byte value)
  {
    for (int index1 = index; index1 < source.Length; ++index1)
    {
      if ((int) source[index1] == (int) value)
        return index1 - index;
    }
    return -1;
  }

  private static string CalculatesString(byte[] source, ref int index, byte value)
  {
    if (index >= source.Length)
      return string.Empty;
    int count = AbTagItem.CalculatesSpecifiedCharacterLength(source, index, value);
    if (count < 0)
    {
      index = source.Length;
      return string.Empty;
    }
    string str = Encoding.ASCII.GetString(source, index, count);
    index += count + 1;
    return str;
  }

  /// <summary>从结构体的数据中解析出实际的子标签信息</summary>
  /// <param name="source">原始字节</param>
  /// <param name="index">偏移索引</param>
  /// <param name="structHandle">结构体句柄</param>
  /// <returns>结果内容</returns>
  public static List<AbTagItem> PraseAbTagItemsFromStruct(
    byte[] source,
    int index,
    AbStructHandle structHandle)
  {
    List<AbTagItem> abTagItemList = new List<AbTagItem>();
    int index1 = (int) structHandle.MemberCount * 8 + index;
    AbTagItem.CalculatesString(source, ref index1, (byte) 0);
    for (int index2 = 0; index2 < (int) structHandle.MemberCount; ++index2)
    {
      AbTagItem abTagItem = new AbTagItem();
      abTagItem.ArrayLength[0] = (int) BitConverter.ToUInt16(source, 8 * index2 + index);
      abTagItem.SetSymbolType(BitConverter.ToUInt16(source, 8 * index2 + index + 2));
      abTagItem.ByteOffset = BitConverter.ToInt32(source, 8 * index2 + index + 4) + 2;
      abTagItem.Name = AbTagItem.CalculatesString(source, ref index1, (byte) 0);
      abTagItemList.Add(abTagItem);
    }
    return abTagItemList;
  }
}
