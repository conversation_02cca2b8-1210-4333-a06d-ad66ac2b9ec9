﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>AB PLC的辅助类，用来辅助生成基本的指令信息</summary>
public class AllenBradleyHelper
{
  /// <summary>CIP命令中PCCC命令相关的信息</summary>
  public const byte CIP_Execute_PCCC = 75;
  /// <summary>CIP命令中的读取数据的服务</summary>
  public const byte CIP_READ_DATA = 76;
  /// <summary>CIP命令中的写数据的服务</summary>
  public const int CIP_WRITE_DATA = 77;
  /// <summary>CIP命令中的读并写的数据服务</summary>
  public const int CIP_READ_WRITE_DATA = 78;
  /// <summary>CIP命令中的读片段的数据服务</summary>
  public const int CIP_READ_FRAGMENT = 82;
  /// <summary>CIP命令中的写片段的数据服务</summary>
  public const int CIP_WRITE_FRAGMENT = 83;
  /// <summary>CIP指令中读取数据的列表</summary>
  public const byte CIP_READ_LIST = 85;
  /// <summary>CIP命令中的对数据读取服务</summary>
  public const int CIP_MULTIREAD_DATA = 4096 /*0x1000*/;
  /// <summary>日期的格式</summary>
  public const ushort CIP_Type_DATE = 8;
  /// <summary>时间的格式</summary>
  public const ushort CIP_Type_TIME = 9;
  /// <summary>日期时间格式，最完整的时间格式</summary>
  public const ushort CIP_Type_TimeAndDate = 10;
  /// <summary>一天中的时间格式</summary>
  public const ushort CIP_Type_TimeOfDate = 11;
  /// <summary>bool型数据，一个字节长度</summary>
  public const ushort CIP_Type_Bool = 193;
  /// <summary>byte型数据，一个字节长度，SINT</summary>
  public const ushort CIP_Type_Byte = 194;
  /// <summary>整型，两个字节长度，INT</summary>
  public const ushort CIP_Type_Word = 195;
  /// <summary>长整型，四个字节长度，DINT</summary>
  public const ushort CIP_Type_DWord = 196;
  /// <summary>特长整型，8个字节，LINT</summary>
  public const ushort CIP_Type_LInt = 197;
  /// <summary>Unsigned 8-bit integer, USINT</summary>
  public const ushort CIP_Type_USInt = 198;
  /// <summary>Unsigned 16-bit integer, UINT</summary>
  public const ushort CIP_Type_UInt = 199;
  /// <summary>Unsigned 32-bit integer, UDINT</summary>
  public const ushort CIP_Type_UDint = 200;
  /// <summary>Unsigned 64-bit integer, ULINT</summary>
  public const ushort CIP_Type_ULint = 201;
  /// <summary>实数数据，四个字节长度</summary>
  public const ushort CIP_Type_Real = 202;
  /// <summary>实数数据，八个字节的长度</summary>
  public const ushort CIP_Type_Double = 203;
  /// <summary>结构体数据，不定长度</summary>
  public const ushort CIP_Type_Struct = 204;
  /// <summary>字符串数据内容</summary>
  public const ushort CIP_Type_String = 208 /*0xD0*/;
  /// <summary>Bit string, 8 bits, BYTE,</summary>
  public const ushort CIP_Type_D1 = 209;
  /// <summary>Bit string, 16-bits, WORD</summary>
  public const ushort CIP_Type_D2 = 210;
  /// <summary>Bit string, 32 bits, DWORD</summary>
  public const ushort CIP_Type_D3 = 211;
  /// <summary>Bit string, 64 bits LWORD</summary>
  public const ushort CIP_Type_D4 = 212;
  /// <summary>二进制数据内容</summary>
  public const ushort CIP_Type_BitArray = 211;
  /// <summary>连接方的厂商标识</summary>
  public const ushort OriginatorVendorID = 4105;
  /// <summary>连接方的序列号</summary>
  public const uint OriginatorSerialNumber = 3248834059;

  private static byte[] BuildRequestPathCommand(string address, bool isConnectedAddress = false)
  {
    using (MemoryStream ms = new MemoryStream())
    {
      int parameter = HslHelper.ExtractParameter(ref address, "class", -1);
      if (parameter != -1)
      {
        int num = address.StartsWith("0x", StringComparison.OrdinalIgnoreCase) ? Convert.ToInt32(address.Substring(2), 16 /*0x10*/) : Convert.ToInt32(address);
        if (parameter < 256 /*0x0100*/)
        {
          ms.WriteByte((byte) 32 /*0x20*/);
          ms.WriteByte((byte) parameter);
        }
        else
        {
          ms.WriteByte((byte) 33);
          ms.WriteByte((byte) 0);
          ms.WriteByte(BitConverter.GetBytes(parameter)[0]);
          ms.WriteByte(BitConverter.GetBytes(parameter)[1]);
        }
        if (num < 256 /*0x0100*/)
        {
          ms.WriteByte((byte) 36);
          ms.WriteByte((byte) num);
        }
        else
        {
          ms.WriteByte((byte) 37);
          ms.WriteByte((byte) 0);
          ms.WriteByte(BitConverter.GetBytes(num)[0]);
          ms.WriteByte(BitConverter.GetBytes(num)[1]);
        }
      }
      else
      {
        string[] strArray = address.Split(new char[1]{ '.' }, StringSplitOptions.RemoveEmptyEntries);
        for (int index = 0; index < strArray.Length; ++index)
        {
          string str1 = string.Empty;
          int length = strArray[index].LastIndexOf('[');
          int num = strArray[index].LastIndexOf(']');
          if (length > 0 && num > 0 && num > length)
          {
            str1 = strArray[index].Substring(length + 1, num - length - 1);
            strArray[index] = strArray[index].Substring(0, length);
          }
          ms.WriteByte((byte) 145);
          byte[] bytes = Encoding.UTF8.GetBytes(strArray[index]);
          ms.WriteByte((byte) bytes.Length);
          ms.Write(bytes, 0, bytes.Length);
          if (bytes.Length % 2 == 1)
            ms.WriteByte((byte) 0);
          if (!string.IsNullOrEmpty(str1))
          {
            string str2 = str1;
            char[] separator = new char[1]{ ',' };
            foreach (string str3 in str2.Split(separator, StringSplitOptions.RemoveEmptyEntries))
            {
              int int32 = Convert.ToInt32(str3);
              if (int32 < 256 /*0x0100*/ && !isConnectedAddress)
              {
                ms.WriteByte((byte) 40);
                ms.WriteByte((byte) int32);
              }
              else if (int32 < 65536 /*0x010000*/)
              {
                ms.WriteByte((byte) 41);
                ms.WriteByte((byte) 0);
                ms.WriteByte(BitConverter.GetBytes(int32)[0]);
                ms.WriteByte(BitConverter.GetBytes(int32)[1]);
              }
              else
              {
                ms.WriteByte((byte) 42);
                ms.WriteByte((byte) 0);
                ms.Write(BitConverter.GetBytes(int32));
              }
            }
          }
        }
      }
      return ms.ToArray();
    }
  }

  /// <summary>从生成的报文里面反解出实际的数据地址，不支持结构体嵌套，仅支持数据，一维数组，不支持多维数据</summary>
  /// <param name="pathCommand">地址路径报文</param>
  /// <returns>实际的地址</returns>
  public static string ParseRequestPathCommand(byte[] pathCommand)
  {
    StringBuilder stringBuilder = new StringBuilder();
    for (int index = 0; index < pathCommand.Length; ++index)
    {
      if (pathCommand[index] == (byte) 145)
      {
        string str = Encoding.UTF8.GetString(pathCommand, index + 2, (int) pathCommand[index + 1]).TrimEnd(new char[1]);
        stringBuilder.Append(str);
        int num = 2 + str.Length;
        if (str.Length % 2 == 1)
          ++num;
        if (pathCommand.Length > num + index)
        {
          if (pathCommand[index + num] == (byte) 40)
            stringBuilder.Append($"[{pathCommand[index + num + 1]}]");
          else if (pathCommand[index + num] == (byte) 41)
            stringBuilder.Append($"[{BitConverter.ToUInt16(pathCommand, index + num + 2)}]");
        }
        stringBuilder.Append(".");
      }
    }
    if (stringBuilder[stringBuilder.Length - 1] == '.')
      stringBuilder.Remove(stringBuilder.Length - 1, 1);
    return stringBuilder.ToString();
  }

  /// <summary>从生成的报文里面，反解出实际的 Symbol Class ID，以及  Instance ID 信息</summary>
  /// <param name="pathCommand"></param>
  /// <returns></returns>
  public static OperateResult<int, int> ParseRequestPathSymbolInstanceAddressing(byte[] pathCommand)
  {
    int index1 = 0;
    int index2;
    int uint16_1;
    if (pathCommand[index1] == (byte) 32 /*0x20*/)
    {
      int num = index1 + 1;
      byte[] numArray = pathCommand;
      int index3 = num;
      index2 = index3 + 1;
      uint16_1 = (int) numArray[index3];
    }
    else
    {
      if (pathCommand[index1] != (byte) 33)
        return new OperateResult<int, int>();
      int startIndex = index1 + 2;
      uint16_1 = (int) BitConverter.ToUInt16(pathCommand, startIndex);
      index2 = startIndex + 2;
    }
    int num1;
    int uint16_2;
    if (pathCommand[index2] == (byte) 36)
    {
      int num2 = index2 + 1;
      byte[] numArray = pathCommand;
      int index4 = num2;
      num1 = index4 + 1;
      uint16_2 = (int) numArray[index4];
    }
    else
    {
      if (pathCommand[index2] != (byte) 37)
        return new OperateResult<int, int>();
      int startIndex = index2 + 2;
      uint16_2 = (int) BitConverter.ToUInt16(pathCommand, startIndex);
      num1 = startIndex + 2;
    }
    return OperateResult.CreateSuccessResult<int, int>(uint16_1, uint16_2);
  }

  /// <summary>获取枚举PLC数据信息的指令</summary>
  /// <param name="startInstance">实例的起始地址</param>
  /// <returns>结果数据</returns>
  public static byte[] BuildEnumeratorCommand(uint startInstance)
  {
    return new byte[16 /*0x10*/]
    {
      (byte) 85,
      (byte) 3,
      (byte) 32 /*0x20*/,
      (byte) 107,
      (byte) 37,
      (byte) 0,
      BitConverter.GetBytes(startInstance)[0],
      BitConverter.GetBytes(startInstance)[1],
      (byte) 3,
      (byte) 0,
      (byte) 1,
      (byte) 0,
      (byte) 2,
      (byte) 0,
      (byte) 8,
      (byte) 0
    };
  }

  /// <summary>获取枚举PLC的局部变量的数据信息的指令</summary>
  /// <param name="startInstance">实例的起始地址</param>
  /// <returns>结果命令数据</returns>
  public static byte[] BuildEnumeratorProgrameMainCommand(uint startInstance)
  {
    byte[] numArray = new byte[38];
    numArray[0] = (byte) 85;
    numArray[1] = (byte) 14;
    numArray[2] = (byte) 145;
    numArray[3] = (byte) 19;
    Encoding.ASCII.GetBytes("Program:MainProgram").CopyTo((Array) numArray, 4);
    numArray[23] = (byte) 0;
    numArray[24] = (byte) 32 /*0x20*/;
    numArray[25] = (byte) 107;
    numArray[26] = (byte) 37;
    numArray[27] = (byte) 0;
    numArray[28] = BitConverter.GetBytes(startInstance)[0];
    numArray[29] = BitConverter.GetBytes(startInstance)[1];
    numArray[30] = (byte) 3;
    numArray[31 /*0x1F*/] = (byte) 0;
    numArray[32 /*0x20*/] = (byte) 1;
    numArray[33] = (byte) 0;
    numArray[34] = (byte) 2;
    numArray[35] = (byte) 0;
    numArray[36] = (byte) 8;
    numArray[37] = (byte) 0;
    return numArray;
  }

  /// <summary>获取获得结构体句柄的命令</summary>
  /// <param name="symbolType">包含地址的信息</param>
  /// <returns>命令数据</returns>
  public static byte[] GetStructHandleCommand(ushort symbolType)
  {
    byte[] structHandleCommand = new byte[18];
    symbolType &= (ushort) 4095 /*0x0FFF*/;
    structHandleCommand[0] = (byte) 3;
    structHandleCommand[1] = (byte) 3;
    structHandleCommand[2] = (byte) 32 /*0x20*/;
    structHandleCommand[3] = (byte) 108;
    structHandleCommand[4] = (byte) 37;
    structHandleCommand[5] = (byte) 0;
    structHandleCommand[6] = BitConverter.GetBytes(symbolType)[0];
    structHandleCommand[7] = BitConverter.GetBytes(symbolType)[1];
    structHandleCommand[8] = (byte) 4;
    structHandleCommand[9] = (byte) 0;
    structHandleCommand[10] = (byte) 4;
    structHandleCommand[11] = (byte) 0;
    structHandleCommand[12] = (byte) 5;
    structHandleCommand[13] = (byte) 0;
    structHandleCommand[14] = (byte) 2;
    structHandleCommand[15] = (byte) 0;
    structHandleCommand[16 /*0x10*/] = (byte) 1;
    structHandleCommand[17] = (byte) 0;
    return structHandleCommand;
  }

  /// <summary>获取结构体内部数据结构的方法</summary>
  /// <param name="symbolType">地址</param>
  /// <param name="structHandle">句柄</param>
  /// <param name="offset">偏移量地址</param>
  /// <returns>指令</returns>
  public static byte[] GetStructItemNameType(
    ushort symbolType,
    AbStructHandle structHandle,
    int offset)
  {
    byte[] structItemNameType = new byte[14];
    symbolType &= (ushort) 4095 /*0x0FFF*/;
    byte[] bytes = BitConverter.GetBytes((uint) ((int) structHandle.TemplateObjectDefinitionSize * 4 - 21));
    structItemNameType[0] = (byte) 76;
    structItemNameType[1] = (byte) 3;
    structItemNameType[2] = (byte) 32 /*0x20*/;
    structItemNameType[3] = (byte) 108;
    structItemNameType[4] = (byte) 37;
    structItemNameType[5] = (byte) 0;
    structItemNameType[6] = BitConverter.GetBytes(symbolType)[0];
    structItemNameType[7] = BitConverter.GetBytes(symbolType)[1];
    structItemNameType[8] = BitConverter.GetBytes(offset)[0];
    structItemNameType[9] = BitConverter.GetBytes(offset)[1];
    structItemNameType[10] = BitConverter.GetBytes(offset)[2];
    structItemNameType[11] = BitConverter.GetBytes(offset)[3];
    structItemNameType[12] = bytes[0];
    structItemNameType[13] = bytes[1];
    return structItemNameType;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.PackRequestHeader(System.UInt16,System.UInt32,System.UInt32,System.Byte[],System.Byte[])" />
  public static byte[] PackRequestHeader(
    ushort command,
    uint session,
    byte[] commandSpecificData,
    byte[] senderContext = null)
  {
    if (commandSpecificData == null)
      commandSpecificData = new byte[0];
    byte[] destinationArray = new byte[commandSpecificData.Length + 24];
    Array.Copy((Array) commandSpecificData, 0, (Array) destinationArray, 24, commandSpecificData.Length);
    BitConverter.GetBytes(command).CopyTo((Array) destinationArray, 0);
    BitConverter.GetBytes(session).CopyTo((Array) destinationArray, 4);
    if (senderContext != null && senderContext.Length <= 8)
      senderContext.CopyTo((Array) destinationArray, 12);
    BitConverter.GetBytes((ushort) commandSpecificData.Length).CopyTo((Array) destinationArray, 2);
    return destinationArray;
  }

  /// <summary>将CommandSpecificData的命令，打包成可发送的数据指令</summary>
  /// <param name="command">实际的命令暗号</param>
  /// <param name="error">错误号信息</param>
  /// <param name="session">当前会话的id</param>
  /// <param name="commandSpecificData">CommandSpecificData命令</param>
  /// <param name="senderContext">发送的上下文内容</param>
  /// <returns>最终可发送的数据命令</returns>
  public static byte[] PackRequestHeader(
    ushort command,
    uint error,
    uint session,
    byte[] commandSpecificData,
    byte[] senderContext = null)
  {
    byte[] numArray = AllenBradleyHelper.PackRequestHeader(command, session, commandSpecificData, senderContext);
    BitConverter.GetBytes(error).CopyTo((Array) numArray, 8);
    return numArray;
  }

  private static byte[] PackExecutePCCC(byte[] pccc)
  {
    MemoryStream ms = new MemoryStream();
    ms.WriteByte((byte) 75);
    ms.WriteByte((byte) 2);
    ms.WriteByte((byte) 32 /*0x20*/);
    ms.WriteByte((byte) 103);
    ms.WriteByte((byte) 36);
    ms.WriteByte((byte) 1);
    ms.WriteByte((byte) 7);
    ms.WriteByte((byte) 9);
    ms.WriteByte((byte) 16 /*0x10*/);
    ms.WriteByte((byte) 11);
    ms.WriteByte((byte) 70);
    ms.WriteByte((byte) 165);
    ms.WriteByte((byte) 193);
    ms.Write(pccc);
    byte[] array = ms.ToArray();
    BitConverter.GetBytes((ushort) 4105).CopyTo((Array) array, 7);
    BitConverter.GetBytes(3248834059U).CopyTo((Array) array, 9);
    return array;
  }

  /// <summary>打包一个PCCC的读取的命令报文</summary>
  /// <param name="tns">请求序号信息</param>
  /// <param name="address">请求的文件地址，地址示例：N7:1</param>
  /// <param name="length">请求的字节长度</param>
  /// <returns>PCCC的读取报文信息</returns>
  public static OperateResult<byte[]> PackExecutePCCCRead(int tns, string address, int length)
  {
    OperateResult<byte[]> operateResult = AllenBradleyDF1Serial.BuildProtectedTypedLogicalReadWithThreeAddressFields(tns, address, length);
    return !operateResult.IsSuccess ? operateResult : OperateResult.CreateSuccessResult<byte[]>(AllenBradleyHelper.PackExecutePCCC(operateResult.Content));
  }

  /// <summary>打包一个PCCC的写入的命令报文</summary>
  /// <param name="tns">请求序号信息</param>
  /// <param name="address">请求的文件地址，地址示例：N7:1</param>
  /// <param name="value">写入的原始数据信息</param>
  /// <returns>PCCC的写入报文信息</returns>
  public static OperateResult<byte[]> PackExecutePCCCWrite(int tns, string address, byte[] value)
  {
    OperateResult<byte[]> operateResult = AllenBradleyDF1Serial.BuildProtectedTypedLogicalWriteWithThreeAddressFields(tns, address, value);
    return !operateResult.IsSuccess ? operateResult : OperateResult.CreateSuccessResult<byte[]>(AllenBradleyHelper.PackExecutePCCC(operateResult.Content));
  }

  internal static OperateResult<byte[]> PackExecutePCCCWrite(
    int tns,
    string address,
    int bitIndex,
    bool value)
  {
    OperateResult<byte[]> operateResult = AllenBradleyDF1Serial.BuildProtectedTypedLogicalMaskWithThreeAddressFields(tns, address, bitIndex, value);
    return !operateResult.IsSuccess ? operateResult : OperateResult.CreateSuccessResult<byte[]>(AllenBradleyHelper.PackExecutePCCC(operateResult.Content));
  }

  /// <summary>打包生成一个请求读取数据的节点信息，CIP指令信息</summary>
  /// <param name="address">地址</param>
  /// <param name="length">指代数组的长度</param>
  /// <param name="isConnectedAddress">是否是连接模式下的地址，默认为false</param>
  /// <returns>CIP的指令信息</returns>
  public static byte[] PackRequsetRead(string address, int length, bool isConnectedAddress = false)
  {
    byte[] sourceArray = new byte[1024 /*0x0400*/];
    int num1 = 0;
    byte[] numArray1 = sourceArray;
    int index1 = num1;
    int num2 = index1 + 1;
    numArray1[index1] = (byte) 76;
    int index2 = num2 + 1;
    byte[] numArray2 = AllenBradleyHelper.BuildRequestPathCommand(address, isConnectedAddress);
    numArray2.CopyTo((Array) sourceArray, index2);
    int num3 = index2 + numArray2.Length;
    sourceArray[1] = (byte) ((num3 - 2) / 2);
    byte[] numArray3 = sourceArray;
    int index3 = num3;
    int num4 = index3 + 1;
    int num5 = (int) BitConverter.GetBytes(length)[0];
    numArray3[index3] = (byte) num5;
    byte[] numArray4 = sourceArray;
    int index4 = num4;
    int length1 = index4 + 1;
    int num6 = (int) BitConverter.GetBytes(length)[1];
    numArray4[index4] = (byte) num6;
    byte[] destinationArray = new byte[length1];
    Array.Copy((Array) sourceArray, 0, (Array) destinationArray, 0, length1);
    return destinationArray;
  }

  /// <summary>打包生成一个请求读取数据片段的节点信息，CIP指令信息</summary>
  /// <param name="address">节点的名称 -&gt; Tag Name</param>
  /// <param name="startIndex">起始的索引位置，以字节为单位 -&gt; The initial index position, in bytes</param>
  /// <param name="length">读取的数据长度，一次通讯总计490个字节 -&gt; Length of read data, a total of 490 bytes of communication</param>
  /// <returns>CIP的指令信息</returns>
  public static byte[] PackRequestReadSegment(string address, int startIndex, int length)
  {
    byte[] sourceArray = new byte[1024 /*0x0400*/];
    int num1 = 0;
    byte[] numArray1 = sourceArray;
    int index1 = num1;
    int num2 = index1 + 1;
    numArray1[index1] = (byte) 82;
    int index2 = num2 + 1;
    byte[] numArray2 = AllenBradleyHelper.BuildRequestPathCommand(address);
    numArray2.CopyTo((Array) sourceArray, index2);
    int num3 = index2 + numArray2.Length;
    sourceArray[1] = (byte) ((num3 - 2) / 2);
    byte[] numArray3 = sourceArray;
    int index3 = num3;
    int num4 = index3 + 1;
    int num5 = (int) BitConverter.GetBytes(length)[0];
    numArray3[index3] = (byte) num5;
    byte[] numArray4 = sourceArray;
    int index4 = num4;
    int num6 = index4 + 1;
    int num7 = (int) BitConverter.GetBytes(length)[1];
    numArray4[index4] = (byte) num7;
    byte[] numArray5 = sourceArray;
    int index5 = num6;
    int num8 = index5 + 1;
    int num9 = (int) BitConverter.GetBytes(startIndex)[0];
    numArray5[index5] = (byte) num9;
    byte[] numArray6 = sourceArray;
    int index6 = num8;
    int num10 = index6 + 1;
    int num11 = (int) BitConverter.GetBytes(startIndex)[1];
    numArray6[index6] = (byte) num11;
    byte[] numArray7 = sourceArray;
    int index7 = num10;
    int num12 = index7 + 1;
    int num13 = (int) BitConverter.GetBytes(startIndex)[2];
    numArray7[index7] = (byte) num13;
    byte[] numArray8 = sourceArray;
    int index8 = num12;
    int length1 = index8 + 1;
    int num14 = (int) BitConverter.GetBytes(startIndex)[3];
    numArray8[index8] = (byte) num14;
    byte[] destinationArray = new byte[length1];
    Array.Copy((Array) sourceArray, 0, (Array) destinationArray, 0, length1);
    return destinationArray;
  }

  /// <summary>根据指定的数据和类型，生成对应的数据</summary>
  /// <param name="address">地址信息</param>
  /// <param name="typeCode">数据类型</param>
  /// <param name="value">字节值</param>
  /// <param name="length">如果节点为数组，就是数组长度</param>
  /// <param name="isConnectedAddress">是否为连接模式的地址</param>
  /// <param name="paddingTail">是否需要在尾巴上补上一个尾巴字节，达到对齐的效果</param>
  /// <returns>CIP的指令信息</returns>
  public static byte[] PackRequestWrite(
    string address,
    ushort typeCode,
    byte[] value,
    int length = 1,
    bool isConnectedAddress = false,
    bool paddingTail = false)
  {
    byte[] sourceArray = new byte[1024 /*0x0400*/];
    int num1 = 0;
    byte[] numArray1 = sourceArray;
    int index1 = num1;
    int num2 = index1 + 1;
    numArray1[index1] = (byte) 77;
    int index2 = num2 + 1;
    byte[] numArray2 = AllenBradleyHelper.BuildRequestPathCommand(address, isConnectedAddress);
    numArray2.CopyTo((Array) sourceArray, index2);
    int num3 = index2 + numArray2.Length;
    sourceArray[1] = (byte) ((num3 - 2) / 2);
    byte[] numArray3 = sourceArray;
    int index3 = num3;
    int num4 = index3 + 1;
    int num5 = (int) BitConverter.GetBytes(typeCode)[0];
    numArray3[index3] = (byte) num5;
    byte[] numArray4 = sourceArray;
    int index4 = num4;
    int num6 = index4 + 1;
    int num7 = (int) BitConverter.GetBytes(typeCode)[1];
    numArray4[index4] = (byte) num7;
    byte[] numArray5 = sourceArray;
    int index5 = num6;
    int num8 = index5 + 1;
    int num9 = (int) BitConverter.GetBytes(length)[0];
    numArray5[index5] = (byte) num9;
    byte[] numArray6 = sourceArray;
    int index6 = num8;
    int num10 = index6 + 1;
    int num11 = (int) BitConverter.GetBytes(length)[1];
    numArray6[index6] = (byte) num11;
    if (value == null)
      value = new byte[0];
    int num12 = 0;
    if (paddingTail && typeCode == (ushort) 193 && length == 1 && value.Length % 2 > 0)
      num12 = 1;
    byte[] destinationArray = new byte[value.Length + num10 + num12];
    Array.Copy((Array) sourceArray, 0, (Array) destinationArray, 0, num10);
    value.CopyTo((Array) destinationArray, num10);
    return destinationArray;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.PackRequestWrite(System.String,System.UInt16,System.Byte[],System.Int32,System.Boolean,System.Boolean)" />
  public static byte[] PackRequestWriteSegment(
    string address,
    ushort typeCode,
    byte[] value,
    int startIndex,
    int length = 1,
    bool isConnectedAddress = false)
  {
    byte[] sourceArray = new byte[1024 /*0x0400*/];
    int num1 = 0;
    byte[] numArray1 = sourceArray;
    int index1 = num1;
    int num2 = index1 + 1;
    numArray1[index1] = (byte) 83;
    int index2 = num2 + 1;
    byte[] numArray2 = AllenBradleyHelper.BuildRequestPathCommand(address, isConnectedAddress);
    numArray2.CopyTo((Array) sourceArray, index2);
    int num3 = index2 + numArray2.Length;
    sourceArray[1] = (byte) ((num3 - 2) / 2);
    byte[] numArray3 = sourceArray;
    int index3 = num3;
    int num4 = index3 + 1;
    int num5 = (int) BitConverter.GetBytes(typeCode)[0];
    numArray3[index3] = (byte) num5;
    byte[] numArray4 = sourceArray;
    int index4 = num4;
    int num6 = index4 + 1;
    int num7 = (int) BitConverter.GetBytes(typeCode)[1];
    numArray4[index4] = (byte) num7;
    byte[] numArray5 = sourceArray;
    int index5 = num6;
    int num8 = index5 + 1;
    int num9 = (int) BitConverter.GetBytes(length)[0];
    numArray5[index5] = (byte) num9;
    byte[] numArray6 = sourceArray;
    int index6 = num8;
    int num10 = index6 + 1;
    int num11 = (int) BitConverter.GetBytes(length)[1];
    numArray6[index6] = (byte) num11;
    byte[] numArray7 = sourceArray;
    int index7 = num10;
    int num12 = index7 + 1;
    int num13 = (int) BitConverter.GetBytes(startIndex)[0];
    numArray7[index7] = (byte) num13;
    byte[] numArray8 = sourceArray;
    int index8 = num12;
    int num14 = index8 + 1;
    int num15 = (int) BitConverter.GetBytes(startIndex)[1];
    numArray8[index8] = (byte) num15;
    byte[] numArray9 = sourceArray;
    int index9 = num14;
    int num16 = index9 + 1;
    int num17 = (int) BitConverter.GetBytes(startIndex)[2];
    numArray9[index9] = (byte) num17;
    byte[] numArray10 = sourceArray;
    int index10 = num16;
    int num18 = index10 + 1;
    int num19 = (int) BitConverter.GetBytes(startIndex)[3];
    numArray10[index10] = (byte) num19;
    if (value == null)
      value = new byte[0];
    byte[] destinationArray = new byte[value.Length + num18];
    Array.Copy((Array) sourceArray, 0, (Array) destinationArray, 0, num18);
    value.CopyTo((Array) destinationArray, num18);
    return destinationArray;
  }

  /// <summary>根据传入的地址，或掩码，和掩码来创建一个读-修改-写的请求报文信息</summary>
  /// <param name="address">标签的地址信息</param>
  /// <param name="orMask">或的掩码</param>
  /// <param name="andMask">和的掩码</param>
  /// <param name="isConnectedAddress">是否为连接模式的地址</param>
  /// <returns>打包之后的CIP指令信息</returns>
  public static byte[] PackRequestReadModifyWrite(
    string address,
    uint orMask,
    uint andMask,
    bool isConnectedAddress = false)
  {
    byte[] numArray1 = new byte[1024 /*0x0400*/];
    int num1 = 0;
    byte[] numArray2 = numArray1;
    int index1 = num1;
    int num2 = index1 + 1;
    numArray2[index1] = (byte) 78;
    int index2 = num2 + 1;
    byte[] numArray3 = AllenBradleyHelper.BuildRequestPathCommand(address, isConnectedAddress);
    numArray3.CopyTo((Array) numArray1, index2);
    int num3 = index2 + numArray3.Length;
    numArray1[1] = (byte) ((num3 - 2) / 2);
    byte[] numArray4 = numArray1;
    int index3 = num3;
    int num4 = index3 + 1;
    numArray4[index3] = (byte) 4;
    byte[] numArray5 = numArray1;
    int index4 = num4;
    int index5 = index4 + 1;
    numArray5[index4] = (byte) 0;
    BitConverter.GetBytes(orMask).CopyTo((Array) numArray1, index5);
    int index6 = index5 + 4;
    BitConverter.GetBytes(andMask).CopyTo((Array) numArray1, index6);
    int length = index6 + 4;
    return numArray1.SelectBegin<byte>(length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.PackRequestReadModifyWrite(System.String,System.UInt32,System.UInt32,System.Boolean)" />
  public static byte[] PackRequestReadModifyWrite(
    string address,
    int index,
    bool value,
    bool isConnectedAddress = false)
  {
    if (index / 32 /*0x20*/ != 0)
      address += $"[{index / 32 /*0x20*/}]";
    index %= 32 /*0x20*/;
    if (value)
    {
      uint orMask = (uint) (1 << index);
      return AllenBradleyHelper.PackRequestReadModifyWrite(address, orMask, uint.MaxValue, isConnectedAddress);
    }
    uint andMask = (uint) ~(1 << index);
    return AllenBradleyHelper.PackRequestReadModifyWrite(address, 0U, andMask, isConnectedAddress);
  }

  /// <summary>分析地址数据信息里的位索引的信息，例如a[10]  返回 a 和 10 索引，如果没有指定索引，就默认为0</summary>
  /// <param name="address">数据地址</param>
  /// <param name="arrayIndex">位索引</param>
  /// <returns>地址信息</returns>
  public static string AnalysisArrayIndex(string address, out int arrayIndex)
  {
    arrayIndex = 0;
    if (!address.EndsWith("]"))
      return address;
    int length = address.LastIndexOf('[');
    if (length < 0)
      return address;
    address = address.Remove(address.Length - 1);
    try
    {
      arrayIndex = int.Parse(address.Substring(length + 1));
      address = address.Substring(0, length);
      return address;
    }
    catch
    {
      return address;
    }
  }

  /// <summary>写入Bool数据的基本指令信息</summary>
  /// <param name="address">地址</param>
  /// <param name="value">值</param>
  /// <returns>报文信息</returns>
  public static byte[] PackRequestWrite(string address, bool value)
  {
    int arrayIndex;
    address = AllenBradleyHelper.AnalysisArrayIndex(address, out arrayIndex);
    return AllenBradleyHelper.PackRequestReadModifyWrite(address, arrayIndex, value);
  }

  /// <summary>将所有的cip指定进行打包操作。</summary>
  /// <param name="portSlot">PLC所在的面板槽号</param>
  /// <param name="cips">所有的cip打包指令信息</param>
  /// <returns>包含服务的信息</returns>
  public static byte[] PackCommandService(byte[] portSlot, params byte[][] cips)
  {
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 178);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 82);
    memoryStream.WriteByte((byte) 2);
    memoryStream.WriteByte((byte) 32 /*0x20*/);
    memoryStream.WriteByte((byte) 6);
    memoryStream.WriteByte((byte) 36);
    memoryStream.WriteByte((byte) 1);
    memoryStream.WriteByte((byte) 10);
    memoryStream.WriteByte((byte) 240 /*0xF0*/);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    int num1 = 0;
    int num2;
    if (cips.Length == 1)
    {
      memoryStream.Write(cips[0], 0, cips[0].Length);
      num2 = num1 + cips[0].Length;
      if (cips[0].Length % 2 == 1)
        memoryStream.WriteByte((byte) 0);
    }
    else
    {
      memoryStream.WriteByte((byte) 10);
      memoryStream.WriteByte((byte) 2);
      memoryStream.WriteByte((byte) 32 /*0x20*/);
      memoryStream.WriteByte((byte) 2);
      memoryStream.WriteByte((byte) 36);
      memoryStream.WriteByte((byte) 1);
      int num3 = num1 + 8;
      memoryStream.Write(BitConverter.GetBytes((ushort) cips.Length), 0, 2);
      ushort num4 = (ushort) (2 + 2 * cips.Length);
      num2 = num3 + 2 * cips.Length;
      for (int index = 0; index < cips.Length; ++index)
      {
        memoryStream.Write(BitConverter.GetBytes(num4), 0, 2);
        num4 += (ushort) cips[index].Length;
      }
      for (int index = 0; index < cips.Length; ++index)
      {
        memoryStream.Write(cips[index], 0, cips[index].Length);
        num2 += cips[index].Length;
      }
    }
    if (portSlot != null)
    {
      memoryStream.WriteByte((byte) ((portSlot.Length + 1) / 2));
      memoryStream.WriteByte((byte) 0);
      memoryStream.Write(portSlot, 0, portSlot.Length);
      if (portSlot.Length % 2 == 1)
        memoryStream.WriteByte((byte) 0);
    }
    byte[] array = memoryStream.ToArray();
    BitConverter.GetBytes((short) num2).CopyTo((Array) array, 12);
    BitConverter.GetBytes((short) (array.Length - 4)).CopyTo((Array) array, 2);
    return array;
  }

  /// <summary>将所有的cip指定进行打包操作。</summary>
  /// <param name="portSlot">PLC所在的面板槽号</param>
  /// <param name="cips">所有的cip打包指令信息</param>
  /// <returns>包含服务的信息</returns>
  public static byte[] PackCleanCommandService(byte[] portSlot, params byte[][] cips)
  {
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 178);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    if (cips.Length == 1)
    {
      memoryStream.Write(cips[0], 0, cips[0].Length);
    }
    else
    {
      memoryStream.WriteByte((byte) 10);
      memoryStream.WriteByte((byte) 2);
      memoryStream.WriteByte((byte) 32 /*0x20*/);
      memoryStream.WriteByte((byte) 2);
      memoryStream.WriteByte((byte) 36);
      memoryStream.WriteByte((byte) 1);
      memoryStream.Write(BitConverter.GetBytes((ushort) cips.Length), 0, 2);
      ushort num = (ushort) (2 + 2 * cips.Length);
      for (int index = 0; index < cips.Length; ++index)
      {
        memoryStream.Write(BitConverter.GetBytes(num), 0, 2);
        num += (ushort) cips[index].Length;
      }
      for (int index = 0; index < cips.Length; ++index)
        memoryStream.Write(cips[index], 0, cips[index].Length);
    }
    memoryStream.WriteByte((byte) ((portSlot.Length + 1) / 2));
    memoryStream.WriteByte((byte) 0);
    memoryStream.Write(portSlot, 0, portSlot.Length);
    if (portSlot.Length % 2 == 1)
      memoryStream.WriteByte((byte) 0);
    byte[] array = memoryStream.ToArray();
    BitConverter.GetBytes((short) (array.Length - 4)).CopyTo((Array) array, 2);
    return array;
  }

  /// <summary>打包一个读取所有特性数据的报文信息，需要传入slot</summary>
  /// <param name="portSlot">站号信息</param>
  /// <param name="sessionHandle">会话的ID信息</param>
  /// <returns>最终发送的报文数据</returns>
  public static byte[] PackCommandGetAttributesAll(byte[] portSlot, uint sessionHandle)
  {
    byte[] commandSpecificData = AllenBradleyHelper.PackCommandSpecificData(new byte[4], AllenBradleyHelper.PackCommandService(portSlot, new byte[6]
    {
      (byte) 1,
      (byte) 2,
      (byte) 32 /*0x20*/,
      (byte) 1,
      (byte) 36,
      (byte) 1
    }));
    return AllenBradleyHelper.PackRequestHeader((ushort) 111, sessionHandle, commandSpecificData);
  }

  /// <summary>根据数据创建反馈的数据信息</summary>
  /// <param name="data">反馈的数据信息</param>
  /// <param name="isRead">是否是读取</param>
  /// <returns>数据</returns>
  public static byte[] PackCommandResponse(byte[] data, bool isRead)
  {
    if (data == null)
    {
      byte[] numArray = new byte[6];
      numArray[2] = (byte) 4;
      return numArray;
    }
    byte[][] numArray1 = new byte[2][];
    byte[] numArray2 = new byte[6];
    numArray2[0] = isRead ? (byte) 204 : (byte) 205;
    numArray1[0] = numArray2;
    numArray1[1] = data;
    return SoftBasic.SpliceArray<byte>(numArray1);
  }

  /// <summary>生成读取直接节点数据信息的内容</summary>
  /// <param name="service">cip指令内容</param>
  /// <returns>最终的指令值</returns>
  public static byte[] PackCommandSpecificData(params byte[][] service)
  {
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte((byte) 10);
    memoryStream.WriteByte((byte) 0);
    memoryStream.WriteByte(BitConverter.GetBytes(service.Length)[0]);
    memoryStream.WriteByte(BitConverter.GetBytes(service.Length)[1]);
    for (int index = 0; index < service.Length; ++index)
      memoryStream.Write(service[index], 0, service[index].Length);
    return memoryStream.ToArray();
  }

  /// <summary>将所有的cip指定进行打包操作。</summary>
  /// <param name="command">指令信息</param>
  /// <param name="code">服务器的代号信息</param>
  /// <param name="isConnected">是否基于连接的服务</param>
  /// <param name="sequence">当使用了基于连接的服务时，当前CIP消息号信息</param>
  /// <returns>包含服务的信息</returns>
  public static byte[] PackCommandSingleService(
    byte[] command,
    ushort code = 178,
    bool isConnected = false,
    ushort sequence = 0)
  {
    if (command == null)
      command = new byte[0];
    byte[] numArray = isConnected ? new byte[6 + command.Length] : new byte[4 + command.Length];
    numArray[0] = BitConverter.GetBytes(code)[0];
    numArray[1] = BitConverter.GetBytes(code)[1];
    numArray[2] = BitConverter.GetBytes(numArray.Length - 4)[0];
    numArray[3] = BitConverter.GetBytes(numArray.Length - 4)[1];
    command.CopyTo((Array) numArray, isConnected ? 6 : 4);
    if (isConnected)
      BitConverter.GetBytes(sequence).CopyTo((Array) numArray, 4);
    return numArray;
  }

  /// <summary>
  /// 向PLC注册会话ID的报文<br />
  /// Register a message with the PLC for the session ID
  /// </summary>
  /// <param name="senderContext">发送的上下文信息</param>
  /// <returns>报文信息 -&gt; Message information </returns>
  public static byte[] RegisterSessionHandle(byte[] senderContext = null)
  {
    return AllenBradleyHelper.PackRequestHeader((ushort) 101, 0U, new byte[4]
    {
      (byte) 1,
      (byte) 0,
      (byte) 0,
      (byte) 0
    }, senderContext);
  }

  /// <summary>
  /// 获取卸载一个已注册的会话的报文<br />
  /// Get a message to uninstall a registered session
  /// </summary>
  /// <param name="sessionHandle">当前会话的ID信息</param>
  /// <returns>字节报文信息 -&gt; BYTE message information </returns>
  public static byte[] UnRegisterSessionHandle(uint sessionHandle)
  {
    return AllenBradleyHelper.PackRequestHeader((ushort) 102, sessionHandle, new byte[0]);
  }

  /// <summary>
  /// 初步检查返回的CIP协议的报文是否正确<br />
  /// Initially check whether the returned CIP protocol message is correct
  /// </summary>
  /// <param name="response">CIP的报文信息</param>
  /// <returns>是否正确的结果信息</returns>
  public static OperateResult CheckResponse(byte[] response)
  {
    try
    {
      int int32 = BitConverter.ToInt32(response, 8);
      if (int32 == 0)
        return OperateResult.CreateSuccessResult();
      string empty = string.Empty;
      string msg;
      switch (int32)
      {
        case 1:
          msg = StringResources.Language.AllenBradleySessionStatus01;
          break;
        case 2:
          msg = StringResources.Language.AllenBradleySessionStatus02;
          break;
        case 3:
          msg = StringResources.Language.AllenBradleySessionStatus03;
          break;
        case 100:
          msg = StringResources.Language.AllenBradleySessionStatus64;
          break;
        case 101:
          msg = StringResources.Language.AllenBradleySessionStatus65;
          break;
        case 105:
          msg = StringResources.Language.AllenBradleySessionStatus69;
          break;
        default:
          msg = StringResources.Language.UnknownError;
          break;
      }
      return new OperateResult(int32, msg);
    }
    catch (Exception ex)
    {
      return new OperateResult($"CheckResponse failed: {ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
    }
  }

  /// <summary>从PLC反馈的数据解析，返回解析后的数据内容，数据类型（在多项数据返回中无效），以及是否有更多的数据</summary>
  /// <param name="response">PLC的反馈数据</param>
  /// <param name="isRead">是否是返回的操作</param>
  /// <returns>带有结果标识的最终数据</returns>
  public static OperateResult<byte[], ushort, bool> ExtractActualData(byte[] response, bool isRead)
  {
    List<byte> byteList = new List<byte>();
    try
    {
      int num1 = 38;
      bool flag = false;
      ushort num2 = 0;
      ushort uint16_1 = BitConverter.ToUInt16(response, 38);
      if (BitConverter.ToInt32(response, 40) == 138)
      {
        int startIndex = 44;
        int uint16_2 = (int) BitConverter.ToUInt16(response, startIndex);
        for (int index1 = 0; index1 < uint16_2; ++index1)
        {
          int num3 = (int) BitConverter.ToUInt16(response, startIndex + 2 + index1 * 2) + startIndex;
          int num4 = index1 == uint16_2 - 1 ? response.Length : (int) BitConverter.ToUInt16(response, startIndex + 4 + index1 * 2) + startIndex;
          ushort uint16_3 = BitConverter.ToUInt16(response, num3 + 2);
          switch (uint16_3)
          {
            case 0:
              if (isRead)
              {
                for (int index2 = num3 + 6; index2 < num4; ++index2)
                  byteList.Add(response[index2]);
                continue;
              }
              continue;
            case 4:
              OperateResult<byte[], ushort, bool> actualData1 = new OperateResult<byte[], ushort, bool>();
              actualData1.ErrorCode = (int) uint16_3;
              actualData1.Message = StringResources.Language.AllenBradley04;
              return actualData1;
            case 5:
              OperateResult<byte[], ushort, bool> actualData2 = new OperateResult<byte[], ushort, bool>();
              actualData2.ErrorCode = (int) uint16_3;
              actualData2.Message = StringResources.Language.AllenBradley05;
              return actualData2;
            case 6:
              if (response[startIndex + 2] == (byte) 210 || response[startIndex + 2] == (byte) 204)
              {
                OperateResult<byte[], ushort, bool> actualData3 = new OperateResult<byte[], ushort, bool>();
                actualData3.ErrorCode = (int) uint16_3;
                actualData3.Message = StringResources.Language.AllenBradley06;
                return actualData3;
              }
              goto case 0;
            case 10:
              OperateResult<byte[], ushort, bool> actualData4 = new OperateResult<byte[], ushort, bool>();
              actualData4.ErrorCode = (int) uint16_3;
              actualData4.Message = StringResources.Language.AllenBradley0A;
              return actualData4;
            case 12:
              OperateResult<byte[], ushort, bool> actualData5 = new OperateResult<byte[], ushort, bool>();
              actualData5.ErrorCode = (int) uint16_3;
              actualData5.Message = StringResources.Language.AllenBradley0C;
              return actualData5;
            case 19:
              OperateResult<byte[], ushort, bool> actualData6 = new OperateResult<byte[], ushort, bool>();
              actualData6.ErrorCode = (int) uint16_3;
              actualData6.Message = StringResources.Language.AllenBradley13;
              return actualData6;
            case 28:
              OperateResult<byte[], ushort, bool> actualData7 = new OperateResult<byte[], ushort, bool>();
              actualData7.ErrorCode = (int) uint16_3;
              actualData7.Message = StringResources.Language.AllenBradley1C;
              return actualData7;
            case 30:
              OperateResult<byte[], ushort, bool> actualData8 = new OperateResult<byte[], ushort, bool>();
              actualData8.ErrorCode = (int) uint16_3;
              actualData8.Message = StringResources.Language.AllenBradley1E;
              return actualData8;
            case 38:
              OperateResult<byte[], ushort, bool> actualData9 = new OperateResult<byte[], ushort, bool>();
              actualData9.ErrorCode = (int) uint16_3;
              actualData9.Message = StringResources.Language.AllenBradley26;
              return actualData9;
            default:
              OperateResult<byte[], ushort, bool> actualData10 = new OperateResult<byte[], ushort, bool>();
              actualData10.ErrorCode = (int) uint16_3;
              actualData10.Message = StringResources.Language.UnknownError;
              return actualData10;
          }
        }
      }
      else
      {
        byte num5 = response[num1 + 4];
        switch (num5)
        {
          case 0:
            if (response[num1 + 2] == (byte) 205 || response[num1 + 2] == (byte) 211)
              return OperateResult.CreateSuccessResult<byte[], ushort, bool>(byteList.ToArray(), num2, flag);
            if (response[num1 + 2] == (byte) 204 || response[num1 + 2] == (byte) 210)
            {
              for (int index = num1 + 8; index < num1 + 2 + (int) uint16_1; ++index)
                byteList.Add(response[index]);
              num2 = BitConverter.ToUInt16(response, num1 + 6);
            }
            else if (response[num1 + 2] == (byte) 213)
            {
              for (int index = num1 + 6; index < num1 + 2 + (int) uint16_1; ++index)
                byteList.Add(response[index]);
            }
            break;
          case 4:
            OperateResult<byte[], ushort, bool> actualData11 = new OperateResult<byte[], ushort, bool>();
            actualData11.ErrorCode = (int) num5;
            actualData11.Message = StringResources.Language.AllenBradley04;
            return actualData11;
          case 5:
            OperateResult<byte[], ushort, bool> actualData12 = new OperateResult<byte[], ushort, bool>();
            actualData12.ErrorCode = (int) num5;
            actualData12.Message = StringResources.Language.AllenBradley05;
            return actualData12;
          case 6:
            flag = true;
            goto case 0;
          case 10:
            OperateResult<byte[], ushort, bool> actualData13 = new OperateResult<byte[], ushort, bool>();
            actualData13.ErrorCode = (int) num5;
            actualData13.Message = StringResources.Language.AllenBradley0A;
            return actualData13;
          case 12:
            OperateResult<byte[], ushort, bool> actualData14 = new OperateResult<byte[], ushort, bool>();
            actualData14.ErrorCode = (int) num5;
            actualData14.Message = StringResources.Language.AllenBradley0C;
            return actualData14;
          case 19:
            OperateResult<byte[], ushort, bool> actualData15 = new OperateResult<byte[], ushort, bool>();
            actualData15.ErrorCode = (int) num5;
            actualData15.Message = StringResources.Language.AllenBradley13;
            return actualData15;
          case 28:
            OperateResult<byte[], ushort, bool> actualData16 = new OperateResult<byte[], ushort, bool>();
            actualData16.ErrorCode = (int) num5;
            actualData16.Message = StringResources.Language.AllenBradley1C;
            return actualData16;
          case 30:
            OperateResult<byte[], ushort, bool> actualData17 = new OperateResult<byte[], ushort, bool>();
            actualData17.ErrorCode = (int) num5;
            actualData17.Message = StringResources.Language.AllenBradley1E;
            return actualData17;
          case 32 /*0x20*/:
            OperateResult<byte[], ushort, bool> actualData18 = new OperateResult<byte[], ushort, bool>();
            actualData18.ErrorCode = (int) num5;
            actualData18.Message = StringResources.Language.AllenBradley20;
            return actualData18;
          case 38:
            OperateResult<byte[], ushort, bool> actualData19 = new OperateResult<byte[], ushort, bool>();
            actualData19.ErrorCode = (int) num5;
            actualData19.Message = StringResources.Language.AllenBradley26;
            return actualData19;
          default:
            OperateResult<byte[], ushort, bool> actualData20 = new OperateResult<byte[], ushort, bool>();
            actualData20.ErrorCode = (int) num5;
            actualData20.Message = StringResources.Language.UnknownError;
            return actualData20;
        }
      }
      return OperateResult.CreateSuccessResult<byte[], ushort, bool>(byteList.ToArray(), num2, flag);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[], ushort, bool>($"ExtractActualData failed: {ex.Message}{Environment.NewLine}{response.ToHexString(' ')}");
    }
  }

  internal static OperateResult<string> ExtractActualString(
    OperateResult<byte[], ushort, bool> read,
    IByteTransform byteTransform,
    Encoding encoding)
  {
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    try
    {
      if (read.Content2 == (ushort) 218)
      {
        if (read.Content1.Length < 1)
          return OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content1));
        if (read.Content1[0] == (byte) 0)
          return OperateResult.CreateSuccessResult<string>(string.Empty);
        return (int) read.Content1[0] >= read.Content1.Length ? OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content1)) : OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content1, 1, (int) read.Content1[0]));
      }
      if (read.Content1.Length < 6)
        return OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content1));
      int count = byteTransform.TransInt32(read.Content1, 2);
      return count == 0 ? OperateResult.CreateSuccessResult<string>(string.Empty) : OperateResult.CreateSuccessResult<string>(encoding.GetString(read.Content1, 6, count));
    }
    catch (Exception ex)
    {
      return new OperateResult<string>($"{ex.Message} Source: {read.Content1.ToHexString(' ')}");
    }
  }

  /// <summary>
  /// 从PLC里读取当前PLC的型号信息<br />
  /// Read the current PLC model information from the PLC
  /// </summary>
  /// <param name="plc">PLC对象</param>
  /// <returns>型号数据信息</returns>
  public static OperateResult<string> ReadPlcType(IReadWriteDevice plc)
  {
    byte[] hexBytes = "00 00 00 00 00 00 02 00 00 00 00 00 b2 00 06 00 01 02 20 01 24 01".ToHexBytes();
    OperateResult<byte[]> result = plc.ReadFromCoreServer(hexBytes);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    return result.Content.Length > 59 && result.Content.Length >= 59 + (int) result.Content[58] ? OperateResult.CreateSuccessResult<string>(Encoding.UTF8.GetString(result.Content, 59, (int) result.Content[58])) : new OperateResult<string>("Data is too short: " + result.Content.ToHexString(' '));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult<string>> ReadPlcTypeAsync(IReadWriteDevice plc)
  {
    byte[] buffer = "00 00 00 00 00 00 02 00 00 00 00 00 b2 00 06 00 01 02 20 01 24 01".ToHexBytes();
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(buffer);
    OperateResult<string> operateResult = read.IsSuccess ? (read.Content.Length <= 59 || read.Content.Length < 59 + (int) read.Content[58] ? new OperateResult<string>("Data is too short: " + read.Content.ToHexString(' ')) : OperateResult.CreateSuccessResult<string>(Encoding.UTF8.GetString(read.Content, 59, (int) read.Content[58]))) : OperateResult.CreateFailedResult<string>((OperateResult) read);
    buffer = (byte[]) null;
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <summary>
  /// 读取指定地址的日期数据，最小日期为 1970年1月1日，当PLC的变量类型为 "Date" 和 "TimeAndDate" 时，都可以用本方法读取。<br />
  /// Read the date data of the specified address. The minimum date is January 1, 1970. When the PLC variable type is "Date" and "TimeAndDate", this method can be used to read.
  /// </summary>
  /// <param name="plc">当前的通信对象信息</param>
  /// <param name="address">PLC里变量的地址</param>
  /// <returns>日期结果对象</returns>
  public static OperateResult<DateTime> ReadDate(IReadWriteCip plc, string address)
  {
    OperateResult<long> result = plc.ReadInt64(address);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<DateTime>((OperateResult) result) : OperateResult.CreateSuccessResult<DateTime>(new DateTime(1970, 1, 1).AddTicks(result.Content / 100L));
  }

  /// <summary>
  /// 使用日期格式（Date）将指定的数据写入到指定的地址里，PLC的地址类型变量必须为 "Date"，否则写入失败。<br />
  /// Use the date format (Date) to write the specified data to the specified address. The PLC address type variable must be "Date", otherwise the writing will fail.
  /// </summary>
  /// <param name="plc">当前的通信对象信息</param>
  /// <param name="address">PLC里变量的地址</param>
  /// <param name="date">时间信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteDate(IReadWriteCip plc, string address, DateTime date)
  {
    long num = (date.Date - new DateTime(1970, 1, 1)).Ticks * 100L;
    return plc.WriteTag(address, (ushort) 8, plc.ByteTransform.TransByte(num));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.DateTime)" />
  public static OperateResult WriteTimeAndDate(IReadWriteCip plc, string address, DateTime date)
  {
    long num = (date - new DateTime(1970, 1, 1)).Ticks * 100L;
    return plc.WriteTag(address, (ushort) 10, plc.ByteTransform.TransByte(num));
  }

  /// <summary>
  /// 读取指定地址的时间数据，最小时间为 0，如果获取秒，可以访问 <see cref="P:System.TimeSpan.TotalSeconds" />，当PLC的变量类型为 "Time" 和 "TimeOfDate" 时，都可以用本方法读取。<br />
  /// Read the time data of the specified address. The minimum time is 0. If you get seconds, you can access <see cref="P:System.TimeSpan.TotalSeconds" />.
  /// When the PLC variable type is "Time" and "TimeOfDate", you can use this Method to read.
  /// </summary>
  /// <param name="plc">当前的通信对象信息</param>
  /// <param name="address">PLC里变量的地址</param>
  /// <returns>时间的结果对象</returns>
  public static OperateResult<TimeSpan> ReadTime(IReadWriteCip plc, string address)
  {
    OperateResult<long> result = plc.ReadInt64(address);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<TimeSpan>((OperateResult) result) : OperateResult.CreateSuccessResult<TimeSpan>(TimeSpan.FromTicks(result.Content / 100L));
  }

  /// <summary>
  /// 使用时间格式（TIME）将时间数据写入到PLC中指定的地址里去，PLC的地址类型变量必须为 "TIME"，否则写入失败。<br />
  /// Use the time format (TIME) to write the time data to the address specified in the PLC. The PLC address type variable must be "TIME", otherwise the writing will fail.
  /// </summary>
  /// <param name="plc">当前的通信对象信息</param>
  /// <param name="address">PLC里变量的地址</param>
  /// <param name="time">时间参数变量</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteTime(IReadWriteCip plc, string address, TimeSpan time)
  {
    return plc.WriteTag(address, (ushort) 9, plc.ByteTransform.TransByte(time.Ticks * 100L));
  }

  /// <summary>
  /// 使用时间格式（TimeOfDate）将时间数据写入到PLC中指定的地址里去，PLC的地址类型变量必须为 "TimeOfDate"，否则写入失败。<br />
  /// Use the time format (TimeOfDate) to write the time data to the address specified in the PLC. The PLC address type variable must be "TimeOfDate", otherwise the writing will fail.
  /// </summary>
  /// <param name="plc">当前的通信对象信息</param>
  /// <param name="address">PLC里变量的地址</param>
  /// <param name="timeOfDate">时间参数变量</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult WriteTimeOfDate(
    IReadWriteCip plc,
    string address,
    TimeSpan timeOfDate)
  {
    return plc.WriteTag(address, (ushort) 11, plc.ByteTransform.TransByte(timeOfDate.Ticks * 100L));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String)" />
  public static async Task<OperateResult<DateTime>> ReadDateAsync(IReadWriteCip plc, string address)
  {
    OperateResult<long> read = await plc.ReadInt64Async(address);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<DateTime>((OperateResult) read);
    long tick = read.Content / 100L;
    DateTime dateTime = new DateTime(1970, 1, 1);
    return OperateResult.CreateSuccessResult<DateTime>(dateTime.AddTicks(tick));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.DateTime)" />
  public static async Task<OperateResult> WriteDateAsync(
    IReadWriteCip plc,
    string address,
    DateTime date)
  {
    long tick = (date.Date - new DateTime(1970, 1, 1)).Ticks * 100L;
    OperateResult operateResult = await plc.WriteTagAsync(address, (ushort) 8, plc.ByteTransform.TransByte(tick));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteTimeAndDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.DateTime)" />
  public static async Task<OperateResult> WriteTimeAndDateAsync(
    IReadWriteCip plc,
    string address,
    DateTime date)
  {
    long tick = (date - new DateTime(1970, 1, 1)).Ticks * 100L;
    OperateResult operateResult = await plc.WriteTagAsync(address, (ushort) 10, plc.ByteTransform.TransByte(tick));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.ReadTime(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String)" />
  public static async Task<OperateResult<TimeSpan>> ReadTimeAsync(IReadWriteCip plc, string address)
  {
    OperateResult<long> read = await plc.ReadInt64Async(address);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<TimeSpan>((OperateResult) read);
    long tick = read.Content / 100L;
    return OperateResult.CreateSuccessResult<TimeSpan>(TimeSpan.FromTicks(tick));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteTime(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.TimeSpan)" />
  public static async Task<OperateResult> WriteTimeAsync(
    IReadWriteCip plc,
    string address,
    TimeSpan time)
  {
    OperateResult operateResult = await plc.WriteTagAsync(address, (ushort) 9, plc.ByteTransform.TransByte(time.Ticks * 100L));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyHelper.WriteTimeOfDate(HslCommunication.Profinet.AllenBradley.IReadWriteCip,System.String,System.TimeSpan)" />
  public static async Task<OperateResult> WriteTimeOfDateAsync(
    IReadWriteCip plc,
    string address,
    TimeSpan timeOfDate)
  {
    OperateResult operateResult = await plc.WriteTagAsync(address, (ushort) 11, plc.ByteTransform.TransByte(timeOfDate.Ticks * 100L));
    return operateResult;
  }
}
