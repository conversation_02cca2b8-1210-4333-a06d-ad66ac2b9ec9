﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.XINJE.XinJESerialOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.ModBus;

#nullable disable
namespace HslCommunication.Profinet.XINJE;

/// <summary>
/// 信捷PLC的XC,XD,XL系列的串口转网口通讯类，虽然硬件层走的是TCP协议，但是底层使用ModbusRtu协议实现，每个系列支持的地址类型及范围不一样，详细参考Demo程序<br />
/// Xinje PLC's XC, XD, XL series serial port to network port communication type, although the hardware layer uses TCP protocol,
/// but the bottom layer is implemented by ModbusRtu protocol. The address types and ranges supported by each series are different.
/// Please refer to the DEMO for details.
/// </summary>
/// <remarks>
/// 对于XC系列适用于XC1/XC2/XC3/XC5/XCM/XCC系列，线圈支持X,Y,S,M,T,C，寄存器支持D,F,E,T,C<br />
/// 对于XD,XL系列适用于XD1/XD2/XD3/XD5/XDM/XDC/XD5E/XDME/XDH/XL1/XL3/XL5/XL5E/XLME，
/// 线圈支持X,Y,S,M,SM,T,C,ET,SEM,HM,HS,HT,HC,HSC 寄存器支持D,ID,QD,SD,TD,CD,ETD,HD,HSD,HTD,HCD,HSCD,FD,SFD,FS<br />
/// </remarks>
public class XinJESerialOverTcp : ModbusRtuOverTcp
{
  /// <summary>实例化一个默认的对象</summary>
  public XinJESerialOverTcp() => this.Series = XinJESeries.XC;

  /// <summary>通过指定站号，ip地址，端口号来实例化一个新的对象</summary>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="station">站号信息</param>
  public XinJESerialOverTcp(string ipAddress, int port = 502, byte station = 1)
    : base(ipAddress, port, station)
  {
    this.Series = XinJESeries.XC;
  }

  /// <summary>
  /// 通过指定站号，IP地址，端口以及PLC的系列来实例化一个新的对象<br />
  /// Instantiate a new object by specifying the station number and PLC series
  /// </summary>
  /// <param name="series">PLC的系列</param>
  /// <param name="ipAddress">Ip地址</param>
  /// <param name="port">端口号</param>
  /// <param name="station">站号信息</param>
  public XinJESerialOverTcp(XinJESeries series, string ipAddress, int port = 502, byte station = 1)
    : base(ipAddress, port, station)
  {
    this.Series = series;
  }

  /// <summary>获取或设置当前的信捷PLC的系列，默认XC系列</summary>
  public XinJESeries Series { get; set; }

  /// <inheritdoc />
  public override OperateResult<string> TranslateToModbusAddress(string address, byte modbusCode)
  {
    return XinJEHelper.PraseXinJEAddress(this.Series, address, modbusCode);
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"XinJESerialOverTcp<{this.Series}>[{this.IpAddress}:{this.Port}]";
  }
}
