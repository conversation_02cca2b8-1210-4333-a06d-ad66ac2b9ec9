﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.FormLogNetView
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>
/// 日志查看器的窗口类，用于分析统计日志数据，实例化的时候可以直接日志文件，然后直接显示出文件内容出来，然后可以根据日志的等级，或是关键字进行搜索信息<br />
/// The window class of the log viewer is used to analyze the statistical log data, and when instantiating, you can directly log the file,
/// and then directly display the file content, and then you can search for information according to the level of the log or keyword
/// </summary>
public class FormLogNetView : Form
{
  private string logFilePath = string.Empty;
  /// <summary>Required designer variable.</summary>
  private IContainer components = (IContainer) null;
  private LogNetAnalysisControl logNetAnalysisControl1;
  private Label label1;
  private TextBox textBox1;
  private Button userButton1;
  private StatusStrip statusStrip1;
  private ToolStripStatusLabel toolStripStatusLabel1;

  /// <summary>
  /// 实例化一个默认的日志查看器的窗口<br />
  /// Instantiates a default log viewer window
  /// </summary>
  public FormLogNetView() => this.InitializeComponent();

  /// <summary>指定一个日志路径实例化一个日志查看界面</summary>
  /// <param name="log">日志的路径</param>
  public FormLogNetView(string log)
  {
    this.InitializeComponent();
    this.logFilePath = log;
  }

  /// <summary>
  /// 获取或设置当前日志选择窗口的默认的路径信息<br />
  /// Get or set the default path information of the current log selection window
  /// </summary>
  public string OpenDialogDefaultPath { get; set; }

  /// <inheritdoc />
  protected override void OnShown(EventArgs e)
  {
    base.OnShown(e);
    if (!string.IsNullOrEmpty(this.logFilePath))
    {
      this.textBox1.Text = this.logFilePath;
      this.DealWithFileName(this.logFilePath);
    }
    if (string.IsNullOrEmpty(this.OpenDialogDefaultPath) || !string.IsNullOrEmpty(this.logFilePath))
      return;
    this.textBox1.Text = this.OpenDialogDefaultPath;
  }

  private void FormLogNetView_Load(object sender, EventArgs e)
  {
    if (Authorization.nzugaydgwadawdibbas())
      this.toolStripStatusLabel1.Visible = false;
    this.label1.Text = StringResources.Language.LogNetFilePath;
    this.userButton1.Text = StringResources.Language.LogNetFileSelect;
    this.Text = StringResources.Language.LogNetViewer;
  }

  private void userButton1_Click(object sender, EventArgs e)
  {
    using (OpenFileDialog openFileDialog = new OpenFileDialog())
    {
      if (!string.IsNullOrEmpty(this.OpenDialogDefaultPath))
        openFileDialog.InitialDirectory = this.OpenDialogDefaultPath;
      openFileDialog.Filter = StringResources.Language.LogNetFilter;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      this.textBox1.Text = openFileDialog.FileName;
      this.DealWithFileName(openFileDialog.FileName);
    }
  }

  private void DealWithFileName(string fileName)
  {
    if (string.IsNullOrEmpty(fileName))
      return;
    if (!File.Exists(fileName))
    {
      int num = (int) MessageBox.Show(StringResources.Language.FileNotExist);
    }
    else
    {
      try
      {
        using (StreamReader streamReader = new StreamReader(fileName, Encoding.UTF8))
        {
          try
          {
            this.logNetAnalysisControl1.SetLogNetSource(streamReader.ReadToEnd());
          }
          catch (Exception ex)
          {
            SoftBasic.ShowExceptionMessage(ex);
          }
        }
      }
      catch (Exception ex)
      {
        SoftBasic.ShowExceptionMessage(ex);
      }
    }
  }

  private void logNetAnalysisControl1_Load(object sender, EventArgs e)
  {
  }

  private void toolStripStatusLabel2_Click(object sender, EventArgs e)
  {
    try
    {
      Process.Start("explorer.exe", "https://github.com/dathlin/C-S-");
    }
    catch
    {
    }
  }

  private void textBox1_KeyDown(object sender, KeyEventArgs e)
  {
  }

  /// <summary>Clean up any resources being used.</summary>
  /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  /// <summary>
  /// Required method for Designer support - do not modify
  /// the contents of this method with the code editor.
  /// </summary>
  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (FormLogNetView));
    this.label1 = new Label();
    this.textBox1 = new TextBox();
    this.statusStrip1 = new StatusStrip();
    this.toolStripStatusLabel1 = new ToolStripStatusLabel();
    this.userButton1 = new Button();
    this.logNetAnalysisControl1 = new LogNetAnalysisControl();
    this.statusStrip1.SuspendLayout();
    this.SuspendLayout();
    this.label1.AutoSize = true;
    this.label1.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.label1.Location = new Point(9, 9);
    this.label1.Name = "label1";
    this.label1.Size = new Size(68, 17);
    this.label1.TabIndex = 1;
    this.label1.Text = "文件路径：";
    this.textBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
    this.textBox1.Font = new Font("宋体", 10.5f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.textBox1.Location = new Point(83, 6);
    this.textBox1.Name = "textBox1";
    this.textBox1.Size = new Size(613, 23);
    this.textBox1.TabIndex = 2;
    this.statusStrip1.Items.AddRange(new ToolStripItem[1]
    {
      (ToolStripItem) this.toolStripStatusLabel1
    });
    this.statusStrip1.Location = new Point(0, 555);
    this.statusStrip1.Name = "statusStrip1";
    this.statusStrip1.Size = new Size(824, 22);
    this.statusStrip1.TabIndex = 4;
    this.statusStrip1.Text = "statusStrip1";
    this.toolStripStatusLabel1.ForeColor = Color.FromArgb(64 /*0x40*/, 64 /*0x40*/, 64 /*0x40*/);
    this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
    this.toolStripStatusLabel1.Size = new Size(248, 17);
    this.toolStripStatusLabel1.Text = "本日志查看器由HslCommunication提供支持";
    this.userButton1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
    this.userButton1.BackColor = Color.Transparent;
    this.userButton1.Font = new Font("微软雅黑", 9f);
    this.userButton1.Location = new Point(717, 6);
    this.userButton1.Margin = new Padding(3, 4, 3, 4);
    this.userButton1.Name = "userButton1";
    this.userButton1.Size = new Size(95, 25);
    this.userButton1.TabIndex = 3;
    this.userButton1.Text = "文件选择";
    this.userButton1.UseVisualStyleBackColor = false;
    this.userButton1.Click += new EventHandler(this.userButton1_Click);
    this.logNetAnalysisControl1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
    this.logNetAnalysisControl1.Location = new Point(6, 30);
    this.logNetAnalysisControl1.Name = "logNetAnalysisControl1";
    this.logNetAnalysisControl1.Size = new Size(818, 522);
    this.logNetAnalysisControl1.TabIndex = 0;
    this.logNetAnalysisControl1.Load += new EventHandler(this.logNetAnalysisControl1_Load);
    this.AutoScaleMode = AutoScaleMode.None;
    this.ClientSize = new Size(824, 577);
    this.Controls.Add((Control) this.statusStrip1);
    this.Controls.Add((Control) this.userButton1);
    this.Controls.Add((Control) this.textBox1);
    this.Controls.Add((Control) this.label1);
    this.Controls.Add((Control) this.logNetAnalysisControl1);
    this.Icon = (Icon) componentResourceManager.GetObject("$this.Icon");
    this.Name = nameof (FormLogNetView);
    this.StartPosition = FormStartPosition.CenterParent;
    this.Text = "日志查看器";
    this.Load += new EventHandler(this.FormLogNetView_Load);
    this.statusStrip1.ResumeLayout(false);
    this.statusStrip1.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
