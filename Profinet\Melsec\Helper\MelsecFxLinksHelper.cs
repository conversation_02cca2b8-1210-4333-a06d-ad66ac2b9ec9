﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Serial;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>三菱的FxLinks的辅助方法信息</summary>
public class MelsecFxLinksHelper
{
  /// <summary>将当前的报文进行打包，根据和校验的方式以及格式信息来实现打包操作</summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="command">原始的命令数据</param>
  /// <returns>打包后的命令</returns>
  public static byte[] PackCommandWithHeader(IReadWriteFxLinks plc, byte[] command)
  {
    if (command.Length > 3 && command[0] == (byte) 5)
      return command;
    byte[] buffer = command;
    if (plc.SumCheck)
    {
      buffer = new byte[command.Length + 2];
      command.CopyTo((Array) buffer, 0);
      SoftLRC.CalculateAccAndFill(buffer, 0, 2);
    }
    return plc.Format == 1 ? SoftBasic.SpliceArray<byte>(new byte[1]
    {
      (byte) 5
    }, buffer) : (plc.Format == 4 ? SoftBasic.SpliceArray<byte>(new byte[1]
    {
      (byte) 5
    }, buffer, new byte[2]{ (byte) 13, (byte) 10 }) : SoftBasic.SpliceArray<byte>(new byte[1]
    {
      (byte) 5
    }, buffer));
  }

  /// <summary>创建一条读取的指令信息，需要指定一些参数</summary>
  /// <param name="station">PLC的站号</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <param name="isBool">是否位读取</param>
  /// <param name="waitTime">等待时间</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<List<byte[]>> BuildReadCommand(
    byte station,
    string address,
    ushort length,
    bool isBool,
    byte waitTime = 0)
  {
    OperateResult<MelsecFxLinksAddress> from = MelsecFxLinksAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from);
    int[] array = SoftBasic.SplitIntegerToArray((int) length, isBool ? 256 /*0x0100*/ : 64 /*0x40*/);
    List<byte[]> numArrayList = new List<byte[]>();
    for (int index = 0; index < array.Length; ++index)
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append(station.ToString("X2"));
      stringBuilder.Append("FF");
      if (isBool)
        stringBuilder.Append("BR");
      else if (from.Content.AddressStart >= 10000)
        stringBuilder.Append("QR");
      else
        stringBuilder.Append("WR");
      stringBuilder.Append(waitTime.ToString("X"));
      stringBuilder.Append(from.Content.ToString());
      if (array[index] == 256 /*0x0100*/)
        stringBuilder.Append("00");
      else
        stringBuilder.Append(array[index].ToString("X2"));
      numArrayList.Add(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
      from.Content.AddressStart += array[index];
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>创建一条别入bool数据的指令信息，需要指定一些参数</summary>
  /// <param name="station">站号</param>
  /// <param name="address">地址</param>
  /// <param name="value">数组值</param>
  /// <param name="waitTime">等待时间</param>
  /// <returns>是否创建成功</returns>
  public static OperateResult<byte[]> BuildWriteBoolCommand(
    byte station,
    string address,
    bool[] value,
    byte waitTime = 0)
  {
    OperateResult<MelsecFxLinksAddress> from = MelsecFxLinksAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("FF");
    stringBuilder.Append("BW");
    stringBuilder.Append(waitTime.ToString("X"));
    stringBuilder.Append(from.Content.ToString());
    stringBuilder.Append(value.Length.ToString("X2"));
    for (int index = 0; index < value.Length; ++index)
      stringBuilder.Append(value[index] ? "1" : "0");
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>创建一条别入byte数据的指令信息，需要指定一些参数，按照字单位</summary>
  /// <param name="station">站号</param>
  /// <param name="address">地址</param>
  /// <param name="value">数组值</param>
  /// <param name="waitTime">等待时间</param>
  /// <returns>命令报文的结果内容对象</returns>
  public static OperateResult<byte[]> BuildWriteByteCommand(
    byte station,
    string address,
    byte[] value,
    byte waitTime = 0)
  {
    OperateResult<MelsecFxLinksAddress> from = MelsecFxLinksAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("FF");
    if (from.Content.AddressStart >= 10000)
      stringBuilder.Append("QW");
    else
      stringBuilder.Append("WW");
    stringBuilder.Append(waitTime.ToString("X"));
    stringBuilder.Append(from.Content.ToString());
    stringBuilder.Append((value.Length / 2).ToString("X2"));
    byte[] bytes = new byte[value.Length * 2];
    for (int index = 0; index < value.Length / 2; ++index)
      SoftBasic.BuildAsciiBytesFrom(BitConverter.ToUInt16(value, index * 2)).CopyTo((Array) bytes, 4 * index);
    stringBuilder.Append(Encoding.ASCII.GetString(bytes));
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>创建启动PLC的报文信息</summary>
  /// <param name="station">站号信息</param>
  /// <param name="waitTime">等待时间</param>
  /// <returns>命令报文的结果内容对象</returns>
  public static OperateResult<byte[]> BuildStart(byte station, byte waitTime = 0)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("FF");
    stringBuilder.Append("RR");
    stringBuilder.Append(waitTime.ToString("X"));
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>创建启动PLC的报文信息</summary>
  /// <param name="station">站号信息</param>
  /// <param name="waitTime">等待时间</param>
  /// <returns>命令报文的结果内容对象</returns>
  public static OperateResult<byte[]> BuildStop(byte station, byte waitTime = 0)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("FF");
    stringBuilder.Append("RS");
    stringBuilder.Append(waitTime.ToString("X"));
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>创建读取PLC类型的命令报文</summary>
  /// <param name="station">站号信息</param>
  /// <param name="waitTime">等待实际</param>
  /// <returns>命令报文的结果内容对象</returns>
  public static OperateResult<byte[]> BuildReadPlcType(byte station, byte waitTime = 0)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<byte[]>(StringResources.Language.InsufficientPrivileges);
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(station.ToString("X2"));
    stringBuilder.Append("FF");
    stringBuilder.Append("PC");
    stringBuilder.Append(waitTime.ToString("X"));
    return OperateResult.CreateSuccessResult<byte[]>(Encoding.ASCII.GetBytes(stringBuilder.ToString()));
  }

  /// <summary>从编码中提取PLC的型号信息</summary>
  /// <param name="code">编码</param>
  /// <returns>PLC的型号信息</returns>
  public static OperateResult<string> GetPlcTypeFromCode(string code)
  {
    switch (code)
    {
      case "82":
        return OperateResult.CreateSuccessResult<string>("A2USCPU");
      case "83":
        return OperateResult.CreateSuccessResult<string>("A2CPU-S1/A2USCPU-S1");
      case "84":
        return OperateResult.CreateSuccessResult<string>("A3UCPU");
      case "85":
        return OperateResult.CreateSuccessResult<string>("A4UCPU");
      case "8B":
        return OperateResult.CreateSuccessResult<string>("AJ72LP25/BR15");
      case "8D":
        return OperateResult.CreateSuccessResult<string>("FX2/FX2C");
      case "8E":
        return OperateResult.CreateSuccessResult<string>("FX0N");
      case "92":
        return OperateResult.CreateSuccessResult<string>("A2ACPU");
      case "93":
        return OperateResult.CreateSuccessResult<string>("A2ACPU-S1");
      case "94":
        return OperateResult.CreateSuccessResult<string>("A3ACPU");
      case "98":
        return OperateResult.CreateSuccessResult<string>("A0J2HCPU");
      case "9A":
        return OperateResult.CreateSuccessResult<string>("A2CCPU");
      case "9D":
        return OperateResult.CreateSuccessResult<string>("FX2N/FX2NC");
      case "9E":
        return OperateResult.CreateSuccessResult<string>("FX1N/FX1NC");
      case "A1":
        return OperateResult.CreateSuccessResult<string>("A1CPU /A1NCPU");
      case "A2":
        return OperateResult.CreateSuccessResult<string>("A2CPU/A2NCPU/A2SCPU");
      case "A3":
        return OperateResult.CreateSuccessResult<string>("A3CPU/A3NCPU");
      case "A4":
        return OperateResult.CreateSuccessResult<string>("A3HCPU/A3MCPU");
      case "AB":
        return OperateResult.CreateSuccessResult<string>("AJ72P25/R25");
      case "F2":
        return OperateResult.CreateSuccessResult<string>("FX1S");
      case "F3":
        return OperateResult.CreateSuccessResult<string>("FX3U/FX3UC");
      case "F4":
        return OperateResult.CreateSuccessResult<string>("FX3G");
      default:
        return new OperateResult<string>($"{StringResources.Language.NotSupportedDataType} Code:{code}");
    }
  }

  private static string GetErrorText(int error)
  {
    switch (error)
    {
      case 2:
        return StringResources.Language.MelsecFxLinksError02;
      case 3:
        return StringResources.Language.MelsecFxLinksError03;
      case 6:
        return StringResources.Language.MelsecFxLinksError06;
      case 7:
        return StringResources.Language.MelsecFxLinksError07;
      case 10:
        return StringResources.Language.MelsecFxLinksError0A;
      case 16 /*0x10*/:
        return StringResources.Language.MelsecFxLinksError10;
      case 24:
        return StringResources.Language.MelsecFxLinksError18;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>
  /// 检查PLC的消息反馈是否合法，合法则提取当前的数据信息，当时写入的命令消息时，无任何的数据返回<br />
  /// Check whether the PLC's message feedback is legal. If it is legal, extract the current data information. When the command message is written at that time, no data is returned.
  /// </summary>
  /// <param name="response">从PLC反馈的数据消息</param>
  /// <returns>检查的结果消息</returns>
  public static OperateResult<byte[]> CheckPlcResponse(byte[] response)
  {
    try
    {
      if (response[0] == (byte) 21)
      {
        int int32 = Convert.ToInt32(Encoding.ASCII.GetString(response, 5, 2), 16 /*0x10*/);
        return new OperateResult<byte[]>(int32, MelsecFxLinksHelper.GetErrorText(int32));
      }
      if (response[0] != (byte) 2 && response[0] != (byte) 6)
        return new OperateResult<byte[]>((int) response[0], "Check command failed: " + SoftBasic.GetAsciiStringRender(response));
      if (response[0] == (byte) 6)
        return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      int num = -1;
      for (int index = 5; index < response.Length; ++index)
      {
        if (response[index] == (byte) 3)
        {
          num = index;
          break;
        }
      }
      if (num == -1)
        num = response.Length;
      return OperateResult.CreateSuccessResult<byte[]>(response.SelectMiddle<byte>(5, num - 5));
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"Check Plc Response failed Error: {ex.Message} Source: {SoftBasic.GetAsciiStringRender(response)}");
    }
  }

  private static OperateResult<byte[]> ExtraResponse(byte[] response)
  {
    try
    {
      byte[] numArray = new byte[response.Length / 2];
      for (int index = 0; index < numArray.Length / 2; ++index)
        BitConverter.GetBytes(Convert.ToUInt16(Encoding.ASCII.GetString(response, index * 4, 4), 16 /*0x10*/)).CopyTo((Array) numArray, index * 2);
      return OperateResult.CreateSuccessResult<byte[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>($"Extra source data failed: {ex.Message}{Environment.NewLine}Source: {response.ToHexString(' ')}");
    }
  }

  /// <summary>
  /// 批量读取PLC的数据，以字为单位，支持读取X,Y,M,S,D,T,C，具体的地址范围需要根据PLC型号来确认，地址支持动态指定站号，例如：s=2;D100<br />
  /// Read PLC data in batches, in units of words, supports reading X, Y, M, S, D, T, C.
  /// The specific address range needs to be confirmed according to the PLC model,
  /// The address supports dynamically specifying the station number, for example: s=2;D100
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  /// <returns>读取结果信息</returns>
  public static OperateResult<byte[]> Read(IReadWriteFxLinks plc, string address, ushort length)
  {
    OperateResult<List<byte[]>> result1 = MelsecFxLinksHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station), address, length, false, plc.WaittingTime);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      OperateResult<byte[]> operateResult1 = MelsecFxLinksHelper.CheckPlcResponse(result2.Content);
      if (!operateResult1.IsSuccess)
        return operateResult1;
      OperateResult<byte[]> operateResult2 = MelsecFxLinksHelper.ExtraResponse(operateResult1.Content);
      if (!operateResult2.IsSuccess)
        return operateResult2;
      byteList.AddRange((IEnumerable<byte>) operateResult2.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>
  /// 批量写入PLC的数据，以字为单位，也就是说最少2个字节信息，支持X,Y,M,S,D,T,C，具体的地址范围需要根据PLC型号来确认，地址支持动态指定站号，例如：s=2;D100<br />
  /// The data written to the PLC in batches is in units of words, that is, at least 2 bytes of information.
  /// It supports X, Y, M, S, D, T, and C. The specific address range needs to be confirmed according to the PLC model,
  /// The address supports dynamically specifying the station number, for example: s=2;D100
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">地址信息</param>
  /// <param name="value">数据值</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(IReadWriteFxLinks plc, string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = MelsecFxLinksHelper.BuildWriteByteCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station), address, value, plc.WaittingTime);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult<byte[]> operateResult3 = MelsecFxLinksHelper.CheckPlcResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? (OperateResult) operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper.Read(HslCommunication.Profinet.Melsec.Helper.IReadWriteFxLinks,System.String,System.UInt16)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IReadWriteFxLinks plc,
    string address,
    ushort length)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<List<byte[]>> command = MelsecFxLinksHelper.BuildReadCommand(stat, address, length, false, plc.WaittingTime);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> result = new List<byte>();
    for (int j = 0; j < command.Content.Count; ++j)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[j]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      OperateResult<byte[]> extra = MelsecFxLinksHelper.CheckPlcResponse(read.Content);
      if (!extra.IsSuccess)
        return extra;
      OperateResult<byte[]> content = MelsecFxLinksHelper.ExtraResponse(extra.Content);
      if (!content.IsSuccess)
        return content;
      result.AddRange((IEnumerable<byte>) content.Content);
      read = (OperateResult<byte[]>) null;
      extra = (OperateResult<byte[]>) null;
      content = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(result.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper.Write(HslCommunication.Profinet.Melsec.Helper.IReadWriteFxLinks,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteFxLinks plc,
    string address,
    byte[] value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<byte[]> command = MelsecFxLinksHelper.BuildWriteByteCommand(stat, address, value, plc.WaittingTime);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult<byte[]> extra = MelsecFxLinksHelper.CheckPlcResponse(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) extra;
  }

  /// <summary>
  /// 批量读取bool类型数据，支持的类型为X,Y,S,T,C，具体的地址范围取决于PLC的类型，地址支持动态指定站号，例如：s=2;D100<br />
  /// Read bool data in batches. The supported types are X, Y, S, T, C. The specific address range depends on the type of PLC,
  /// The address supports dynamically specifying the station number, for example: s=2;D100
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">地址信息，比如X10,Y17，注意X，Y的地址是8进制的</param>
  /// <param name="length">读取的长度</param>
  /// <returns>读取结果信息</returns>
  public static OperateResult<bool[]> ReadBool(
    IReadWriteFxLinks plc,
    string address,
    ushort length)
  {
    if (address.IndexOf('.') > 0)
      return HslHelper.ReadBool((IReadWriteNet) plc, address, length);
    OperateResult<List<byte[]>> result1 = MelsecFxLinksHelper.BuildReadCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station), address, length, true, plc.WaittingTime);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    List<bool> boolList = new List<bool>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
      OperateResult<byte[]> result3 = MelsecFxLinksHelper.CheckPlcResponse(result2.Content);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result3);
      boolList.AddRange((IEnumerable<bool>) ((IEnumerable<byte>) result3.Content).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)).ToArray<bool>());
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <summary>
  /// 批量写入bool类型的数组，支持的类型为X,Y,S,T,C，具体的地址范围取决于PLC的类型，地址支持动态指定站号，例如：s=2;D100<br />
  /// Write arrays of type bool in batches. The supported types are X, Y, S, T, C. The specific address range depends on the type of PLC,
  /// The address supports dynamically specifying the station number, for example: s=2;D100
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="address">PLC的地址信息</param>
  /// <param name="value">数据信息</param>
  /// <returns>是否写入成功</returns>
  public static OperateResult Write(IReadWriteFxLinks plc, string address, bool[] value)
  {
    OperateResult<byte[]> operateResult1 = MelsecFxLinksHelper.BuildWriteBoolCommand((byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station), address, value, plc.WaittingTime);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult<byte[]> operateResult3 = MelsecFxLinksHelper.CheckPlcResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? (OperateResult) operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper.ReadBool(HslCommunication.Profinet.Melsec.Helper.IReadWriteFxLinks,System.String,System.UInt16)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IReadWriteFxLinks plc,
    string address,
    ushort length)
  {
    if (address.IndexOf('.') > 0)
    {
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) plc, address, length);
      return operateResult;
    }
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<List<byte[]>> command = MelsecFxLinksHelper.BuildReadCommand(stat, address, length, true, plc.WaittingTime);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    List<bool> result = new List<bool>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      OperateResult<byte[]> extra = MelsecFxLinksHelper.CheckPlcResponse(read.Content);
      if (!extra.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) extra);
      result.AddRange((IEnumerable<bool>) ((IEnumerable<byte>) extra.Content).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 49)).ToArray<bool>());
      read = (OperateResult<byte[]>) null;
      extra = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(result.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper.Write(HslCommunication.Profinet.Melsec.Helper.IReadWriteFxLinks,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IReadWriteFxLinks plc,
    string address,
    bool[] value)
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref address, "s", (int) plc.Station);
    OperateResult<byte[]> command = MelsecFxLinksHelper.BuildWriteBoolCommand(stat, address, value, plc.WaittingTime);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult<byte[]> extra = MelsecFxLinksHelper.CheckPlcResponse(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) extra;
  }

  /// <summary>
  /// <b>[商业授权]</b> 启动PLC的操作，可以携带额外的参数信息，指定站号。举例：s=2; 注意：分号是必须的。<br />
  /// <b>[Authorization]</b> Start the PLC operation, you can carry additional parameter information and specify the station number. Example: s=2; Note: The semicolon is required.
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="parameter">允许携带的参数信息，例如s=2; 也可以为空</param>
  /// <returns>是否启动成功</returns>
  public static OperateResult StartPLC(IReadWriteFxLinks plc, string parameter = "")
  {
    OperateResult<byte[]> operateResult1 = MelsecFxLinksHelper.BuildStart((byte) HslHelper.ExtractParameter(ref parameter, "s", (int) plc.Station), plc.WaittingTime);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult<byte[]> operateResult3 = MelsecFxLinksHelper.CheckPlcResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? (OperateResult) operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// <b>[商业授权]</b> 停止PLC的操作，可以携带额外的参数信息，指定站号。举例：s=2; 注意：分号是必须的。<br />
  /// <b>[Authorization]</b> Stop PLC operation, you can carry additional parameter information and specify the station number. Example: s=2; Note: The semicolon is required.
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="parameter">允许携带的参数信息，例如s=2; 也可以为空</param>
  /// <returns>是否停止成功</returns>
  public static OperateResult StopPLC(IReadWriteFxLinks plc, string parameter = "")
  {
    OperateResult<byte[]> operateResult1 = MelsecFxLinksHelper.BuildStop((byte) HslHelper.ExtractParameter(ref parameter, "s", (int) plc.Station), plc.WaittingTime);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = plc.ReadFromCoreServer(operateResult1.Content);
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    OperateResult<byte[]> operateResult3 = MelsecFxLinksHelper.CheckPlcResponse(operateResult2.Content);
    return !operateResult3.IsSuccess ? (OperateResult) operateResult3 : OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取PLC的型号信息，可以携带额外的参数信息，指定站号。举例：s=2; 注意：分号是必须的。<br />
  /// <b>[Authorization]</b> Read the PLC model information, you can carry additional parameter information, and specify the station number. Example: s=2; Note: The semicolon is required.
  /// </summary>
  /// <param name="plc">PLC通信对象</param>
  /// <param name="parameter">允许携带的参数信息，例如s=2; 也可以为空</param>
  /// <returns>带PLC型号的结果信息</returns>
  public static OperateResult<string> ReadPlcType(IReadWriteFxLinks plc, string parameter = "")
  {
    OperateResult<byte[]> result1 = MelsecFxLinksHelper.BuildReadPlcType((byte) HslHelper.ExtractParameter(ref parameter, "s", (int) plc.Station), plc.WaittingTime);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = plc.ReadFromCoreServer(result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result2);
    OperateResult<byte[]> result3 = MelsecFxLinksHelper.CheckPlcResponse(result2.Content);
    return !result3.IsSuccess ? OperateResult.CreateFailedResult<string>((OperateResult) result3) : MelsecFxLinksHelper.GetPlcTypeFromCode(Encoding.ASCII.GetString(result2.Content, 5, 2));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper.StartPLC(HslCommunication.Profinet.Melsec.Helper.IReadWriteFxLinks,System.String)" />
  public static async Task<OperateResult> StartPLCAsync(IReadWriteFxLinks plc, string parameter = "")
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref parameter, "s", (int) plc.Station);
    OperateResult<byte[]> command = MelsecFxLinksHelper.BuildStart(stat, plc.WaittingTime);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult<byte[]> extra = MelsecFxLinksHelper.CheckPlcResponse(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) extra;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper.StopPLC(HslCommunication.Profinet.Melsec.Helper.IReadWriteFxLinks,System.String)" />
  public static async Task<OperateResult> StopPLCAsync(IReadWriteFxLinks plc, string parameter = "")
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref parameter, "s", (int) plc.Station);
    OperateResult<byte[]> command = MelsecFxLinksHelper.BuildStop(stat, plc.WaittingTime);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return (OperateResult) read;
    OperateResult<byte[]> extra = MelsecFxLinksHelper.CheckPlcResponse(read.Content);
    return extra.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) extra;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.Helper.MelsecFxLinksHelper.ReadPlcType(HslCommunication.Profinet.Melsec.Helper.IReadWriteFxLinks,System.String)" />
  public static async Task<OperateResult<string>> ReadPlcTypeAsync(
    IReadWriteFxLinks plc,
    string parameter = "")
  {
    byte stat = (byte) HslHelper.ExtractParameter(ref parameter, "s", (int) plc.Station);
    OperateResult<byte[]> command = MelsecFxLinksHelper.BuildReadPlcType(stat, plc.WaittingTime);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) command);
    OperateResult<byte[]> read = await plc.ReadFromCoreServerAsync(command.Content);
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    OperateResult<byte[]> extra = MelsecFxLinksHelper.CheckPlcResponse(read.Content);
    return extra.IsSuccess ? MelsecFxLinksHelper.GetPlcTypeFromCode(Encoding.ASCII.GetString(read.Content, 5, 2)) : OperateResult.CreateFailedResult<string>((OperateResult) extra);
  }
}
