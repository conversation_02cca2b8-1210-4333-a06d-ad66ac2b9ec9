﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.ModBus.ModbusRtuOverTcp
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.ModBus;

/// <inheritdoc cref="T:HslCommunication.ModBus.ModbusRtu" />
public class ModbusRtuOverTcp : DeviceTcpNet, IModbus, IReadWriteDevice, IReadWriteNet
{
  private byte station = 1;
  private bool isAddressStartWithZero = true;
  private Func<string, byte, OperateResult<string>> addressMapping = (Func<string, byte, OperateResult<string>>) ((address, modbusCode) => OperateResult.CreateSuccessResult<string>(address));

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtu.#ctor" />
  public ModbusRtuOverTcp()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
    this.WordLength = (ushort) 1;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.#ctor(System.String,System.Int32,System.Byte)" />
  public ModbusRtuOverTcp(string ipAddress, int port = 502, byte station = 1)
    : this()
  {
    this.IpAddress = ipAddress;
    this.Port = port;
    this.station = station;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new ModbusRtuMessage(this.StationCheckMacth);
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.AddressStartWithZero" />
  public bool AddressStartWithZero
  {
    get => this.isAddressStartWithZero;
    set => this.isAddressStartWithZero = value;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.DataFormat" />
  public DataFormat DataFormat
  {
    get => this.ByteTransform.DataFormat;
    set => this.ByteTransform.DataFormat = value;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusTcpNet.IsStringReverse" />
  public bool IsStringReverse
  {
    get => this.ByteTransform.IsStringReverseByteWord;
    set => this.ByteTransform.IsStringReverseByteWord = value;
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusRtu.Crc16CheckEnable" />
  public bool Crc16CheckEnable { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusRtu.StationCheckMacth" />
  public bool StationCheckMacth { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.ModBus.IModbus.EnableWriteMaskCode" />
  public bool EnableWriteMaskCode { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.ModBus.IModbus.BroadcastStation" />
  public int BroadcastStation { get; set; } = -1;

  /// <inheritdoc cref="P:HslCommunication.ModBus.IModbus.WordReadBatchLength" />
  public int WordReadBatchLength { get; set; } = 120;

  /// <inheritdoc cref="M:HslCommunication.ModBus.IModbus.TranslateToModbusAddress(System.String,System.Byte)" />
  public virtual OperateResult<string> TranslateToModbusAddress(string address, byte modbusCode)
  {
    return this.addressMapping != null ? this.addressMapping(address, modbusCode) : OperateResult.CreateSuccessResult<string>(address);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.IModbus.RegisteredAddressMapping(System.Func{System.String,System.Byte,HslCommunication.OperateResult{System.String}})" />
  public void RegisteredAddressMapping(Func<string, byte, OperateResult<string>> mapping)
  {
    this.addressMapping = mapping;
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return ModbusInfo.PackCommandToRtu(command);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return ModbusHelper.ExtraRtuResponseContent(send, response, this.Crc16CheckEnable, this.BroadcastStation);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(byte[] send)
  {
    return this.BroadcastStation >= 0 && (int) send[0] == this.BroadcastStation ? this.ReadFromCoreServer(send, false, true) : base.ReadFromCoreServer(send);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtuOverTcp.ReadFromCoreServer(System.Byte[])" />
  public override async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(byte[] send)
  {
    if (this.BroadcastStation >= 0 && (int) send[0] == this.BroadcastStation)
      return this.ReadFromCoreServer(send, false, true);
    OperateResult<byte[]> operateResult = await base.ReadFromCoreServerAsync(send);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadCoil(System.String)" />
  public OperateResult<bool> ReadCoil(string address) => this.ReadBool(address);

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadCoil(System.String,System.UInt16)" />
  public OperateResult<bool[]> ReadCoil(string address, ushort length)
  {
    return this.ReadBool(address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadDiscrete(System.String)" />
  public OperateResult<bool> ReadDiscrete(string address)
  {
    return ByteTransformHelper.GetResultFromArray<bool>(this.ReadDiscrete(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadDiscrete(System.String,System.UInt16)" />
  public OperateResult<bool[]> ReadDiscrete(string address, ushort length)
  {
    return ModbusHelper.ReadBoolHelper((IModbus) this, address, length, (byte) 2);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return ModbusHelper.Read((IModbus) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.ReadWrite(HslCommunication.ModBus.IModbus,System.String,System.UInt16,System.String,System.Byte[])" />
  [HslMqttApi("ReadWrite", "Use 0x17 function code to write and read data at the same time, and use a message to implement it")]
  public OperateResult<byte[]> ReadWrite(
    string readAddress,
    ushort length,
    string writeAddress,
    byte[] value)
  {
    return ModbusHelper.ReadWrite((IModbus) this, readAddress, length, writeAddress, value);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return ModbusHelper.Write((IModbus) this, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Int16)" />
  [HslMqttApi("WriteInt16", "")]
  public override OperateResult Write(string address, short value)
  {
    return ModbusHelper.Write((IModbus) this, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.UInt16)" />
  [HslMqttApi("WriteUInt16", "")]
  public override OperateResult Write(string address, ushort value)
  {
    return ModbusHelper.Write((IModbus) this, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.WriteMask(System.String,System.UInt16,System.UInt16)" />
  [HslMqttApi("WriteMask", "")]
  public OperateResult WriteMask(string address, ushort andMask, ushort orMask)
  {
    return ModbusHelper.WriteMask((IModbus) this, address, andMask, orMask);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.ReadFile(HslCommunication.ModBus.IModbus,System.Byte,System.UInt16,System.UInt16,System.UInt16)" />
  [HslMqttApi("ReadFile", "")]
  public OperateResult<byte[]> ReadFile(ushort fileNumber, ushort address, ushort length)
  {
    return ModbusHelper.ReadFile((IModbus) this, this.Station, fileNumber, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusHelper.WriteFile(HslCommunication.ModBus.IModbus,System.Byte,System.UInt16,System.UInt16,System.Byte[])" />
  /// "/&gt;
  [HslMqttApi("WriteFile", "")]
  public OperateResult WriteFile(ushort fileNumber, ushort address, byte[] data)
  {
    return ModbusHelper.WriteFile((IModbus) this, this.Station, fileNumber, address, data);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtuOverTcp.Write(System.String,System.Int16)" />
  public OperateResult WriteOneRegister(string address, short value) => this.Write(address, value);

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtuOverTcp.Write(System.String,System.UInt16)" />
  public OperateResult WriteOneRegister(string address, ushort value) => this.Write(address, value);

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadCoilAsync(System.String)" />
  public async Task<OperateResult<bool>> ReadCoilAsync(string address)
  {
    OperateResult<bool> operateResult = await this.ReadBoolAsync(address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadCoilAsync(System.String,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadCoilAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadDiscreteAsync(System.String)" />
  public async Task<OperateResult<bool>> ReadDiscreteAsync(string address)
  {
    OperateResult<bool[]> result = await this.ReadDiscreteAsync(address, (ushort) 1);
    return ByteTransformHelper.GetResultFromArray<bool>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadDiscreteAsync(System.String,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadDiscreteAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolHelperAsync(address, length, (byte) 2);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadAsync(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await ModbusHelper.ReadAsync((IModbus) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.WriteAsync(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await ModbusHelper.WriteAsync((IModbus) this, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtuOverTcp.Write(System.String,System.Int16)" />
  public override async Task<OperateResult> WriteAsync(string address, short value)
  {
    OperateResult operateResult = await ModbusHelper.WriteAsync((IModbus) this, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtuOverTcp.Write(System.String,System.UInt16)" />
  public override async Task<OperateResult> WriteAsync(string address, ushort value)
  {
    OperateResult operateResult = await ModbusHelper.WriteAsync((IModbus) this, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.WriteOneRegisterAsync(System.String,System.Int16)" />
  public async Task<OperateResult> WriteOneRegisterAsync(string address, short value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.WriteOneRegisterAsync(System.String,System.UInt16)" />
  public async Task<OperateResult> WriteOneRegisterAsync(string address, ushort value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusRtuOverTcp.WriteMask(System.String,System.UInt16,System.UInt16)" />
  public async Task<OperateResult> WriteMaskAsync(string address, ushort andMask, ushort orMask)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.WriteMask(address, andMask, orMask)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return ModbusHelper.ReadBoolHelper((IModbus) this, address, length, (byte) 1);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] values)
  {
    return ModbusHelper.Write((IModbus) this, address, values);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.Write(System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return ModbusHelper.Write((IModbus) this, address, value);
  }

  private async Task<OperateResult<bool[]>> ReadBoolHelperAsync(
    string address,
    ushort length,
    byte function)
  {
    OperateResult<bool[]> operateResult = await ModbusHelper.ReadBoolHelperAsync((IModbus) this, address, length, function);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.ReadBoolAsync(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolHelperAsync(address, length, (byte) 1);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.WriteAsync(System.String,System.Boolean[])" />
  public override async Task<OperateResult> WriteAsync(string address, bool[] values)
  {
    OperateResult operateResult = await ModbusHelper.WriteAsync((IModbus) this, address, values);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpNet.WriteAsync(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await ModbusHelper.WriteAsync((IModbus) this, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt32Array", "")]
  public override OperateResult<int[]> ReadInt32(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<int[]>(this.Read(address, this.GetWordLength(address, (int) length, 2)), (Func<byte[], int[]>) (m => transform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt32Array", "")]
  public override OperateResult<uint[]> ReadUInt32(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<uint[]>(this.Read(address, this.GetWordLength(address, (int) length, 2)), (Func<byte[], uint[]>) (m => transform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloat(System.String,System.UInt16)" />
  [HslMqttApi("ReadFloatArray", "")]
  public override OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<float[]>(this.Read(address, this.GetWordLength(address, (int) length, 2)), (Func<byte[], float[]>) (m => transform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadInt64Array", "")]
  public override OperateResult<long[]> ReadInt64(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<long[]>(this.Read(address, this.GetWordLength(address, (int) length, 4)), (Func<byte[], long[]>) (m => transform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64(System.String,System.UInt16)" />
  [HslMqttApi("ReadUInt64Array", "")]
  public override OperateResult<ulong[]> ReadUInt64(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(this.Read(address, this.GetWordLength(address, (int) length, 4)), (Func<byte[], ulong[]>) (m => transform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDouble(System.String,System.UInt16)" />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return ByteTransformHelper.GetResultFromBytes<double[]>(this.Read(address, this.GetWordLength(address, (int) length, 4)), (Func<byte[], double[]>) (m => transform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int32[])" />
  [HslMqttApi("WriteInt32Array", "")]
  public override OperateResult Write(string address, int[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt32[])" />
  [HslMqttApi("WriteUInt32Array", "")]
  public override OperateResult Write(string address, uint[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Single[])" />
  [HslMqttApi("WriteFloatArray", "")]
  public override OperateResult Write(string address, float[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Int64[])" />
  [HslMqttApi("WriteInt64Array", "")]
  public override OperateResult Write(string address, long[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.UInt64[])" />
  [HslMqttApi("WriteUInt64Array", "")]
  public override OperateResult Write(string address, ulong[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Double[])" />
  [HslMqttApi("WriteDoubleArray", "")]
  public override OperateResult Write(string address, double[] values)
  {
    IByteTransform transformParameter = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    return this.Write(address, transformParameter.TransByte(values));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt32Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<int[]>> ReadInt32Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 2));
    return ByteTransformHelper.GetResultFromBytes<int[]>(result, (Func<byte[], int[]>) (m => transform.TransInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt32Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<uint[]>> ReadUInt32Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 2));
    return ByteTransformHelper.GetResultFromBytes<uint[]>(result, (Func<byte[], uint[]>) (m => transform.TransUInt32(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadFloatAsync(System.String,System.UInt16)" />
  public override async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 2));
    return ByteTransformHelper.GetResultFromBytes<float[]>(result, (Func<byte[], float[]>) (m => transform.TransSingle(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadInt64Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<long[]>> ReadInt64Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 4));
    return ByteTransformHelper.GetResultFromBytes<long[]>(result, (Func<byte[], long[]>) (m => transform.TransInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadUInt64Async(System.String,System.UInt16)" />
  public override async Task<OperateResult<ulong[]>> ReadUInt64Async(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 4));
    return ByteTransformHelper.GetResultFromBytes<ulong[]>(result, (Func<byte[], ulong[]>) (m => transform.TransUInt64(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadDoubleAsync(System.String,System.UInt16)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult<byte[]> result = await this.ReadAsync(address, this.GetWordLength(address, (int) length, 4));
    return ByteTransformHelper.GetResultFromBytes<double[]>(result, (Func<byte[], double[]>) (m => transform.TransDouble(m, 0, (int) length)));
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int32[])" />
  public override async Task<OperateResult> WriteAsync(string address, int[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt32[])" />
  public override async Task<OperateResult> WriteAsync(string address, uint[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Single[])" />
  public override async Task<OperateResult> WriteAsync(string address, float[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Int64[])" />
  public override async Task<OperateResult> WriteAsync(string address, long[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.UInt64[])" />
  public override async Task<OperateResult> WriteAsync(string address, ulong[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.WriteAsync(System.String,System.Double[])" />
  public override async Task<OperateResult> WriteAsync(string address, double[] values)
  {
    IByteTransform transform = HslHelper.ExtractTransformParameter(ref address, this.ByteTransform);
    OperateResult operateResult = await this.WriteAsync(address, transform.TransByte(values));
    transform = (IByteTransform) null;
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"ModbusRtuOverTcp[{this.IpAddress}:{this.Port}]";
}
