﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecMcServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱MC协议的虚拟服务器，支持M,X,Y,D,W的数据池读写操作，支持二进制及ASCII格式进行读写操作，需要在实例化的时候指定。<br />
/// The Mitsubishi MC protocol virtual server supports M, X, Y, D, W data pool read and write operations,
/// and supports binary and ASCII format read and write operations, which need to be specified during instantiation.
/// </summary>
/// <remarks>
/// 如果你没有可以测试的三菱PLC，想要测试自己开发的上位机软件，或是想要在本机实现虚拟PLC，然后进行IO的输入输出练习，都可以使用本类来实现，支持的地址示例参考 Demo 程序测试界面
/// </remarks>
public class MelsecMcServer : DeviceServer
{
  private SoftBuffer xBuffer;
  private SoftBuffer yBuffer;
  private SoftBuffer mBuffer;
  private SoftBuffer lBuffer;
  private SoftBuffer dBuffer;
  private SoftBuffer wBuffer;
  private SoftBuffer bBuffer;
  private SoftBuffer sBuffer;
  private SoftBuffer fBuffer;
  private SoftBuffer rBuffer;
  private SoftBuffer zrBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private bool isBinary = true;

  /// <summary>
  /// 实例化一个默认参数的mc协议的服务器<br />
  /// Instantiate a mc protocol server with default parameters
  /// </summary>
  /// <param name="isBinary">是否是二进制，默认是二进制，否则是ASCII格式</param>
  public MelsecMcServer(bool isBinary = true)
  {
    this.xBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.yBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.lBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.dBuffer = new SoftBuffer(262144 /*0x040000*/);
    this.wBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.bBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.rBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.zrBuffer = new SoftBuffer(262144 /*0x040000*/);
    this.sBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.fBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.isBinary = isBinary;
    this.LogMsgFormatBinary = isBinary;
  }

  /// <inheritdoc />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, length, false);
    if (!melsecFrom.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) melsecFrom);
    if (melsecFrom.Content.McDataType.DataType == (byte) 1)
    {
      OperateResult<SoftBuffer> bufferByDataCode = this.GetBoolSoftBufferByDataCode(melsecFrom.Content.McDataType.DataCode);
      return !bufferByDataCode.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) bufferByDataCode) : OperateResult.CreateSuccessResult<byte[]>(SoftBasic.BoolArrayToByte(((IEnumerable<byte>) bufferByDataCode.Content.GetBytes(melsecFrom.Content.AddressStart, (int) length * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()));
    }
    OperateResult<SoftBuffer> bufferByDataCode1 = this.GetWordSoftBufferByDataCode(melsecFrom.Content.McDataType.DataCode);
    return !bufferByDataCode1.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) bufferByDataCode1) : OperateResult.CreateSuccessResult<byte[]>(bufferByDataCode1.Content.GetBytes(melsecFrom.Content.AddressStart * 2, (int) length * 2));
  }

  /// <inheritdoc />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, (ushort) 0, false);
    if (!melsecFrom.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) melsecFrom);
    if (melsecFrom.Content.McDataType.DataType == (byte) 1)
    {
      OperateResult<SoftBuffer> bufferByDataCode = this.GetBoolSoftBufferByDataCode(melsecFrom.Content.McDataType.DataCode);
      if (!bufferByDataCode.IsSuccess)
        return (OperateResult) bufferByDataCode;
      byte[] array = ((IEnumerable<bool>) SoftBasic.ByteToBoolArray(value)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
      bufferByDataCode.Content.SetBytes(array, melsecFrom.Content.AddressStart);
      return OperateResult.CreateSuccessResult();
    }
    OperateResult<SoftBuffer> bufferByDataCode1 = this.GetWordSoftBufferByDataCode(melsecFrom.Content.McDataType.DataCode);
    if (!bufferByDataCode1.IsSuccess)
      return (OperateResult) bufferByDataCode1;
    bufferByDataCode1.Content.SetBytes(value, melsecFrom.Content.AddressStart * 2);
    return OperateResult.CreateSuccessResult();
  }

  private OperateResult<SoftBuffer> GetBoolSoftBufferByDataCode(ushort dataCode)
  {
    if ((int) dataCode == (int) MelsecMcDataType.M.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.mBuffer);
    if ((int) dataCode == (int) MelsecMcDataType.X.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.xBuffer);
    if ((int) dataCode == (int) MelsecMcDataType.Y.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.yBuffer);
    if ((int) dataCode == (int) MelsecMcDataType.L.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.lBuffer);
    if ((int) dataCode == (int) MelsecMcDataType.B.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.bBuffer);
    if ((int) dataCode == (int) MelsecMcDataType.S.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.sBuffer);
    return (int) dataCode == (int) MelsecMcDataType.F.DataCode ? OperateResult.CreateSuccessResult<SoftBuffer>(this.fBuffer) : new OperateResult<SoftBuffer>(StringResources.Language.NotSupportedDataType);
  }

  private OperateResult<SoftBuffer> GetWordSoftBufferByDataCode(ushort dataCode)
  {
    if ((int) dataCode == (int) MelsecMcDataType.D.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.dBuffer);
    if ((int) dataCode == (int) MelsecMcDataType.W.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.wBuffer);
    if ((int) dataCode == (int) MelsecMcDataType.R.DataCode)
      return OperateResult.CreateSuccessResult<SoftBuffer>(this.rBuffer);
    return (int) dataCode == (int) MelsecMcDataType.ZR.DataCode ? OperateResult.CreateSuccessResult<SoftBuffer>(this.zrBuffer) : new OperateResult<SoftBuffer>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (address.Contains("."))
      return HslHelper.ReadBool((IReadWriteNet) this, address, length);
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, (ushort) 0, true);
    if (!melsecFrom.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) melsecFrom);
    if (melsecFrom.Content.McDataType.DataType == (byte) 0)
      return new OperateResult<bool[]>(StringResources.Language.MelsecCurrentTypeNotSupportedWordOperate);
    OperateResult<SoftBuffer> bufferByDataCode = this.GetBoolSoftBufferByDataCode(melsecFrom.Content.McDataType.DataCode);
    return !bufferByDataCode.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) bufferByDataCode) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) bufferByDataCode.Content.GetBytes(melsecFrom.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
  }

  /// <inheritdoc />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    int num = -1;
    if (address.Contains("."))
    {
      num = HslHelper.CalculateBitStartIndex(address.Substring(address.IndexOf('.') + 1));
      address = address.Substring(0, address.IndexOf('.'));
    }
    OperateResult<McAddressData> melsecFrom = McAddressData.ParseMelsecFrom(address, (ushort) 0, true);
    if (!melsecFrom.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) melsecFrom);
    if (num >= 0)
    {
      if (melsecFrom.Content.McDataType.DataType == (byte) 1)
        return (OperateResult) new OperateResult<bool[]>(StringResources.Language.MelsecCurrentTypeNotSupportedBitOperate);
      OperateResult<SoftBuffer> bufferByDataCode = this.GetWordSoftBufferByDataCode(melsecFrom.Content.McDataType.DataCode);
      if (!bufferByDataCode.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) bufferByDataCode);
      bufferByDataCode.Content.SetBool(value, melsecFrom.Content.AddressStart * 16 /*0x10*/ + num);
    }
    else
    {
      if (melsecFrom.Content.McDataType.DataType == (byte) 0)
        return (OperateResult) new OperateResult<bool[]>(StringResources.Language.MelsecCurrentTypeNotSupportedWordOperate);
      OperateResult<SoftBuffer> bufferByDataCode = this.GetBoolSoftBufferByDataCode(melsecFrom.Content.McDataType.DataCode);
      if (!bufferByDataCode.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) bufferByDataCode);
      bufferByDataCode.Content.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), melsecFrom.Content.AddressStart);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return this.isBinary ? (INetMessage) new MelsecQnA3EBinaryMessage() : (INetMessage) new MelsecQnA3EAsciiMessage();
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return this.isBinary ? OperateResult.CreateSuccessResult<byte[]>(this.ReadFromMcCore(receive.RemoveBegin<byte>(11))) : OperateResult.CreateSuccessResult<byte[]>(this.ReadFromMcAsciiCore(receive.RemoveBegin<byte>(22)));
  }

  /// <summary>
  /// 当收到mc协议的报文的时候应该触发的方法，允许继承重写，来实现自定义的返回，或是数据监听。<br />
  /// The method that should be triggered when a message of the mc protocol is received,
  /// allowing inheritance to be rewritten to implement custom return or data monitoring.
  /// </summary>
  /// <param name="mcCore">mc报文</param>
  /// <returns>返回的报文信息</returns>
  protected virtual byte[] ReadFromMcCore(byte[] mcCore)
  {
    if (mcCore[0] == (byte) 1 && mcCore[1] == (byte) 1)
      return this.PackCommand((ushort) 0, Encoding.ASCII.GetBytes("L02CPU          \u0005A"));
    if (mcCore[0] == (byte) 1 && mcCore[1] == (byte) 4)
      return this.ReadByCommand(mcCore);
    if (mcCore[0] == (byte) 1 && mcCore[1] == (byte) 16 /*0x10*/)
      return this.PackCommand((ushort) 0, (byte[]) null);
    if (mcCore[0] == (byte) 1 && mcCore[1] == (byte) 20)
      return !this.EnableWrite ? this.PackCommand((ushort) 49250, (byte[]) null) : this.PackCommand((ushort) 0, this.WriteByMessage(mcCore));
    if (mcCore[0] == (byte) 2 && mcCore[1] == (byte) 16 /*0x10*/)
      return this.PackCommand((ushort) 0, (byte[]) null);
    if (mcCore[0] == (byte) 3 && mcCore[1] == (byte) 4)
      return this.ReadRandomByCommand(mcCore);
    if (mcCore[0] == (byte) 6 && mcCore[1] == (byte) 4)
      return this.ReadBlockByCommand(mcCore);
    if (mcCore[0] == (byte) 6 && mcCore[1] == (byte) 16 /*0x10*/)
      return this.PackCommand((ushort) 0, (byte[]) null);
    return mcCore[0] == (byte) 23 && mcCore[1] == (byte) 22 ? this.PackCommand((ushort) 0, (byte[]) null) : (byte[]) null;
  }

  /// <summary>
  /// 当收到mc协议的报文的时候应该触发的方法，允许继承重写，来实现自定义的返回，或是数据监听。<br />
  /// The method that should be triggered when a message of the mc protocol is received,
  /// allowing inheritance to be rewritten to implement custom return or data monitoring.
  /// </summary>
  /// <param name="mcCore">mc报文</param>
  /// <returns>返回的报文信息</returns>
  protected virtual byte[] ReadFromMcAsciiCore(byte[] mcCore)
  {
    if (mcCore[0] == (byte) 48 /*0x30*/ && mcCore[1] == (byte) 52 && mcCore[2] == (byte) 48 /*0x30*/ && mcCore[3] == (byte) 49)
      return this.ReadAsciiByCommand(mcCore);
    if (mcCore[0] != (byte) 49 || mcCore[1] != (byte) 52 || mcCore[2] != (byte) 48 /*0x30*/ || mcCore[3] != (byte) 49)
      return (byte[]) null;
    return !this.EnableWrite ? this.PackCommand((ushort) 49250, (byte[]) null) : this.PackCommand((ushort) 0, this.WriteAsciiByMessage(mcCore));
  }

  /// <summary>将状态码，数据打包成一个完成的回复报文信息</summary>
  /// <param name="status">状态信息</param>
  /// <param name="data">数据</param>
  /// <returns>状态信息</returns>
  protected virtual byte[] PackCommand(ushort status, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    if (this.isBinary)
    {
      byte[] numArray = new byte[11 + data.Length];
      SoftBasic.HexStringToBytes("D0 00 00 FF FF 03 00 00 00 00 00").CopyTo((Array) numArray, 0);
      if (data.Length != 0)
        data.CopyTo((Array) numArray, 11);
      BitConverter.GetBytes((short) (data.Length + 2)).CopyTo((Array) numArray, 7);
      BitConverter.GetBytes(status).CopyTo((Array) numArray, 9);
      return numArray;
    }
    byte[] numArray1 = new byte[22 + data.Length];
    Encoding.ASCII.GetBytes("D00000FF03FF0000000000").CopyTo((Array) numArray1, 0);
    if (data.Length != 0)
      data.CopyTo((Array) numArray1, 22);
    Encoding.ASCII.GetBytes((data.Length + 4).ToString("X4")).CopyTo((Array) numArray1, 14);
    Encoding.ASCII.GetBytes(status.ToString("X4")).CopyTo((Array) numArray1, 18);
    return numArray1;
  }

  private byte[] ReadByCommand(byte[] command)
  {
    ushort length = this.ByteTransform.TransUInt16(command, 8);
    int index = (int) command[6] * 65536 /*0x010000*/ + (int) command[5] * 256 /*0x0100*/ + (int) command[4];
    if (command[2] == (byte) 1)
    {
      if (length > (ushort) 7168)
        return this.PackCommand((ushort) 49233, (byte[]) null);
      OperateResult<SoftBuffer> bufferByDataCode = this.GetBoolSoftBufferByDataCode((ushort) command[7]);
      return !bufferByDataCode.IsSuccess ? this.PackCommand((ushort) 49242, (byte[]) null) : this.PackCommand((ushort) 0, MelsecHelper.TransBoolArrayToByteData(bufferByDataCode.Content.GetBytes(index, (int) length)));
    }
    if (length > (ushort) 960)
      return this.PackCommand((ushort) 49233, (byte[]) null);
    OperateResult<SoftBuffer> bufferByDataCode1 = this.GetBoolSoftBufferByDataCode((ushort) command[7]);
    if (bufferByDataCode1.IsSuccess)
      return this.PackCommand((ushort) 0, ((IEnumerable<byte>) bufferByDataCode1.Content.GetBytes(index, (int) length * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
    OperateResult<SoftBuffer> bufferByDataCode2 = this.GetWordSoftBufferByDataCode((ushort) command[7]);
    return bufferByDataCode2.IsSuccess ? this.PackCommand((ushort) 0, bufferByDataCode2.Content.GetBytes(index * 2, (int) length * 2)) : this.PackCommand((ushort) 49242, (byte[]) null);
  }

  private byte[] ReadRandomByCommand(byte[] command)
  {
    int num = (int) command[4];
    byte[] data = new byte[num * 2];
    for (int index1 = 0; index1 < num; ++index1)
    {
      int index2 = (int) command[8 + 4 * index1] * 65536 /*0x010000*/ + (int) command[7 + 4 * index1] * 256 /*0x0100*/ + (int) command[6 + 4 * index1];
      if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.M.DataCode)
        ((IEnumerable<byte>) this.mBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.X.DataCode)
        ((IEnumerable<byte>) this.xBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.Y.DataCode)
        ((IEnumerable<byte>) this.yBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.B.DataCode)
        ((IEnumerable<byte>) this.bBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.L.DataCode)
        ((IEnumerable<byte>) this.lBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.S.DataCode)
        ((IEnumerable<byte>) this.sBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.F.DataCode)
        ((IEnumerable<byte>) this.fBuffer.GetBytes(index2, 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray().CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.D.DataCode)
        this.dBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.W.DataCode)
        this.wBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.R.DataCode)
        this.rBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) data, index1 * 2);
      else if ((int) command[9 + 4 * index1] == (int) MelsecMcDataType.ZR.DataCode)
        this.zrBuffer.GetBytes(index2 * 2, 2).CopyTo((Array) data, index1 * 2);
    }
    return this.PackCommand((ushort) 0, data);
  }

  private byte[] ReadBlockByCommand(byte[] command)
  {
    int num1 = (int) command[4];
    MemoryStream ms = new MemoryStream();
    for (int index1 = 0; index1 < num1; ++index1)
    {
      int index2 = (int) command[8 + 6 * index1] * 65536 /*0x010000*/ + (int) command[7 + 6 * index1] * 256 /*0x0100*/ + (int) command[6 + 6 * index1];
      ushort num2 = this.ByteTransform.TransUInt16(command, 10 + 6 * index1);
      if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.M.DataCode)
        ms.Write(((IEnumerable<byte>) this.mBuffer.GetBytes(index2, (int) num2 * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.X.DataCode)
        ms.Write(((IEnumerable<byte>) this.xBuffer.GetBytes(index2, (int) num2 * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.Y.DataCode)
        ms.Write(((IEnumerable<byte>) this.yBuffer.GetBytes(index2, (int) num2 * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.B.DataCode)
        ms.Write(((IEnumerable<byte>) this.bBuffer.GetBytes(index2, (int) num2 * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.L.DataCode)
        ms.Write(((IEnumerable<byte>) this.lBuffer.GetBytes(index2, (int) num2 * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.S.DataCode)
        ms.Write(((IEnumerable<byte>) this.sBuffer.GetBytes(index2, (int) num2 * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.F.DataCode)
        ms.Write(((IEnumerable<byte>) this.fBuffer.GetBytes(index2, (int) num2 * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>().ToByteArray());
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.D.DataCode)
        ms.Write(this.dBuffer.GetBytes(index2 * 2, (int) num2 * 2));
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.W.DataCode)
        ms.Write(this.wBuffer.GetBytes(index2 * 2, (int) num2 * 2));
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.R.DataCode)
        ms.Write(this.rBuffer.GetBytes(index2 * 2, (int) num2 * 2));
      else if ((int) command[9 + 6 * index1] == (int) MelsecMcDataType.ZR.DataCode)
        ms.Write(this.zrBuffer.GetBytes(index2 * 2, (int) num2 * 2));
    }
    return this.PackCommand((ushort) 0, ms.ToArray());
  }

  private byte[] ReadAsciiPackCommand(
    SoftBuffer softBuffer,
    int startIndex,
    ushort length,
    bool isBool)
  {
    return isBool ? this.PackCommand((ushort) 0, MelsecHelper.TransByteArrayToAsciiByteArray(SoftBasic.BoolArrayToByte(((IEnumerable<byte>) softBuffer.GetBytes(startIndex, (int) length * 16 /*0x10*/)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()))) : this.PackCommand((ushort) 0, MelsecHelper.TransByteArrayToAsciiByteArray(softBuffer.GetBytes(startIndex * 2, (int) length * 2)));
  }

  private byte[] ReadAsciiByCommand(byte[] command)
  {
    ushort uint16 = Convert.ToUInt16(Encoding.ASCII.GetString(command, 16 /*0x10*/, 4), 16 /*0x10*/);
    string str = Encoding.ASCII.GetString(command, 8, 2);
    int num = !(str == MelsecMcDataType.X.AsciiCode) && !(str == MelsecMcDataType.Y.AsciiCode) && !(str == MelsecMcDataType.W.AsciiCode) && !(str == MelsecMcDataType.B.AsciiCode) && !(str == MelsecMcDataType.L.AsciiCode) ? Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 6)) : Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 6), 16 /*0x10*/);
    if (command[7] == (byte) 49)
    {
      if (uint16 > (ushort) 3584 /*0x0E00*/)
        return this.PackCommand((ushort) 49233, (byte[]) null);
      if (str == MelsecMcDataType.M.AsciiCode)
        return this.PackCommand((ushort) 0, ((IEnumerable<byte>) this.mBuffer.GetBytes(num, (int) uint16)).Select<byte, byte>((Func<byte, byte>) (m => m == (byte) 0 ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
      if (str == MelsecMcDataType.X.AsciiCode)
        return this.PackCommand((ushort) 0, ((IEnumerable<byte>) this.xBuffer.GetBytes(num, (int) uint16)).Select<byte, byte>((Func<byte, byte>) (m => m == (byte) 0 ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
      if (str == MelsecMcDataType.Y.AsciiCode)
        return this.PackCommand((ushort) 0, ((IEnumerable<byte>) this.yBuffer.GetBytes(num, (int) uint16)).Select<byte, byte>((Func<byte, byte>) (m => m == (byte) 0 ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
      if (str == MelsecMcDataType.B.AsciiCode)
        return this.PackCommand((ushort) 0, ((IEnumerable<byte>) this.bBuffer.GetBytes(num, (int) uint16)).Select<byte, byte>((Func<byte, byte>) (m => m == (byte) 0 ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
      if (str == MelsecMcDataType.L.AsciiCode)
        return this.PackCommand((ushort) 0, ((IEnumerable<byte>) this.lBuffer.GetBytes(num, (int) uint16)).Select<byte, byte>((Func<byte, byte>) (m => m == (byte) 0 ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
      if (str == MelsecMcDataType.S.AsciiCode)
        return this.PackCommand((ushort) 0, ((IEnumerable<byte>) this.sBuffer.GetBytes(num, (int) uint16)).Select<byte, byte>((Func<byte, byte>) (m => m == (byte) 0 ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>());
      return str == MelsecMcDataType.F.AsciiCode ? this.PackCommand((ushort) 0, ((IEnumerable<byte>) this.fBuffer.GetBytes(num, (int) uint16)).Select<byte, byte>((Func<byte, byte>) (m => m == (byte) 0 ? (byte) 48 /*0x30*/ : (byte) 49)).ToArray<byte>()) : this.PackCommand((ushort) 49242, (byte[]) null);
    }
    if (uint16 > (ushort) 960)
      return this.PackCommand((ushort) 49233, (byte[]) null);
    if (str == MelsecMcDataType.M.AsciiCode)
      return this.ReadAsciiPackCommand(this.mBuffer, num, uint16, true);
    if (str == MelsecMcDataType.X.AsciiCode)
      return this.ReadAsciiPackCommand(this.xBuffer, num, uint16, true);
    if (str == MelsecMcDataType.Y.AsciiCode)
      return this.ReadAsciiPackCommand(this.yBuffer, num, uint16, true);
    if (str == MelsecMcDataType.B.AsciiCode)
      return this.ReadAsciiPackCommand(this.bBuffer, num, uint16, true);
    if (str == MelsecMcDataType.L.AsciiCode)
      return this.ReadAsciiPackCommand(this.lBuffer, num, uint16, true);
    if (str == MelsecMcDataType.S.AsciiCode)
      return this.ReadAsciiPackCommand(this.sBuffer, num, uint16, true);
    if (str == MelsecMcDataType.F.AsciiCode)
      return this.ReadAsciiPackCommand(this.fBuffer, num, uint16, true);
    if (str == MelsecMcDataType.D.AsciiCode)
      return this.ReadAsciiPackCommand(this.dBuffer, num, uint16, false);
    if (str == MelsecMcDataType.W.AsciiCode)
      return this.ReadAsciiPackCommand(this.wBuffer, num, uint16, false);
    if (str == MelsecMcDataType.R.AsciiCode)
      return this.ReadAsciiPackCommand(this.rBuffer, num, uint16, false);
    return str == MelsecMcDataType.ZR.AsciiCode ? this.ReadAsciiPackCommand(this.zrBuffer, num, uint16, false) : this.PackCommand((ushort) 49242, (byte[]) null);
  }

  private byte[] WriteByMessage(byte[] command)
  {
    if (!this.EnableWrite)
      return (byte[]) null;
    ushort count = this.ByteTransform.TransUInt16(command, 8);
    int destIndex = (int) command[6] * 65536 /*0x010000*/ + (int) command[5] * 256 /*0x0100*/ + (int) command[4];
    if (command[2] == (byte) 1)
    {
      byte[] actualDataHelper = McBinaryHelper.ExtractActualDataHelper(command.RemoveBegin<byte>(10), true);
      if ((int) command[7] == (int) MelsecMcDataType.M.DataCode)
        this.mBuffer.SetBytes(((IEnumerable<byte>) actualDataHelper).Take<byte>((int) count).ToArray<byte>(), destIndex);
      else if ((int) command[7] == (int) MelsecMcDataType.X.DataCode)
        this.xBuffer.SetBytes(((IEnumerable<byte>) actualDataHelper).Take<byte>((int) count).ToArray<byte>(), destIndex);
      else if ((int) command[7] == (int) MelsecMcDataType.Y.DataCode)
        this.yBuffer.SetBytes(((IEnumerable<byte>) actualDataHelper).Take<byte>((int) count).ToArray<byte>(), destIndex);
      else if ((int) command[7] == (int) MelsecMcDataType.B.DataCode)
        this.bBuffer.SetBytes(((IEnumerable<byte>) actualDataHelper).Take<byte>((int) count).ToArray<byte>(), destIndex);
      else if ((int) command[7] == (int) MelsecMcDataType.L.DataCode)
        this.lBuffer.SetBytes(((IEnumerable<byte>) actualDataHelper).Take<byte>((int) count).ToArray<byte>(), destIndex);
      else if ((int) command[7] == (int) MelsecMcDataType.S.DataCode)
      {
        this.sBuffer.SetBytes(((IEnumerable<byte>) actualDataHelper).Take<byte>((int) count).ToArray<byte>(), destIndex);
      }
      else
      {
        if ((int) command[7] != (int) MelsecMcDataType.F.DataCode)
          throw new Exception(StringResources.Language.NotSupportedDataType);
        this.fBuffer.SetBytes(((IEnumerable<byte>) actualDataHelper).Take<byte>((int) count).ToArray<byte>(), destIndex);
      }
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.M.DataCode)
    {
      this.mBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, 10))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.X.DataCode)
    {
      this.xBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, 10))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.Y.DataCode)
    {
      this.yBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, 10))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.B.DataCode)
    {
      this.bBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, 10))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.L.DataCode)
    {
      this.lBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, 10))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.S.DataCode)
    {
      this.sBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, 10))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.F.DataCode)
    {
      this.fBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(SoftBasic.ArrayRemoveBegin<byte>(command, 10))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.D.DataCode)
    {
      this.dBuffer.SetBytes(SoftBasic.ArrayRemoveBegin<byte>(command, 10), destIndex * 2);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.W.DataCode)
    {
      this.wBuffer.SetBytes(SoftBasic.ArrayRemoveBegin<byte>(command, 10), destIndex * 2);
      return new byte[0];
    }
    if ((int) command[7] == (int) MelsecMcDataType.R.DataCode)
    {
      this.rBuffer.SetBytes(SoftBasic.ArrayRemoveBegin<byte>(command, 10), destIndex * 2);
      return new byte[0];
    }
    if ((int) command[7] != (int) MelsecMcDataType.ZR.DataCode)
      throw new Exception(StringResources.Language.NotSupportedDataType);
    this.zrBuffer.SetBytes(SoftBasic.ArrayRemoveBegin<byte>(command, 10), destIndex * 2);
    return new byte[0];
  }

  private byte[] WriteAsciiByMessage(byte[] command)
  {
    ushort uint16 = Convert.ToUInt16(Encoding.ASCII.GetString(command, 16 /*0x10*/, 4), 16 /*0x10*/);
    string str = Encoding.ASCII.GetString(command, 8, 2);
    int destIndex = !(str == MelsecMcDataType.X.AsciiCode) && !(str == MelsecMcDataType.Y.AsciiCode) && !(str == MelsecMcDataType.W.AsciiCode) && !(str == MelsecMcDataType.B.AsciiCode) && !(str == MelsecMcDataType.L.AsciiCode) ? Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 6)) : Convert.ToInt32(Encoding.ASCII.GetString(command, 10, 6), 16 /*0x10*/);
    if (command[7] == (byte) 49)
    {
      byte[] array = ((IEnumerable<byte>) command.RemoveBegin<byte>(20)).Select<byte, byte>((Func<byte, byte>) (m => m != (byte) 49 ? (byte) 0 : (byte) 1)).ToArray<byte>();
      if (str == MelsecMcDataType.M.AsciiCode)
        this.mBuffer.SetBytes(((IEnumerable<byte>) array).Take<byte>((int) uint16).ToArray<byte>(), destIndex);
      else if (str == MelsecMcDataType.X.AsciiCode)
        this.xBuffer.SetBytes(((IEnumerable<byte>) array).Take<byte>((int) uint16).ToArray<byte>(), destIndex);
      else if (str == MelsecMcDataType.Y.AsciiCode)
        this.yBuffer.SetBytes(((IEnumerable<byte>) array).Take<byte>((int) uint16).ToArray<byte>(), destIndex);
      else if (str == MelsecMcDataType.B.AsciiCode)
        this.bBuffer.SetBytes(((IEnumerable<byte>) array).Take<byte>((int) uint16).ToArray<byte>(), destIndex);
      else if (str == MelsecMcDataType.L.AsciiCode)
        this.lBuffer.SetBytes(((IEnumerable<byte>) array).Take<byte>((int) uint16).ToArray<byte>(), destIndex);
      else if (str == MelsecMcDataType.S.AsciiCode)
      {
        this.sBuffer.SetBytes(((IEnumerable<byte>) array).Take<byte>((int) uint16).ToArray<byte>(), destIndex);
      }
      else
      {
        if (!(str == MelsecMcDataType.F.AsciiCode))
          throw new Exception(StringResources.Language.NotSupportedDataType);
        this.fBuffer.SetBytes(((IEnumerable<byte>) array).Take<byte>((int) uint16).ToArray<byte>(), destIndex);
      }
      return new byte[0];
    }
    if (str == MelsecMcDataType.M.AsciiCode)
    {
      this.mBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if (str == MelsecMcDataType.X.AsciiCode)
    {
      this.xBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if (str == MelsecMcDataType.Y.AsciiCode)
    {
      this.yBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if (str == MelsecMcDataType.B.AsciiCode)
    {
      this.bBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if (str == MelsecMcDataType.L.AsciiCode)
    {
      this.lBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if (str == MelsecMcDataType.S.AsciiCode)
    {
      this.sBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if (str == MelsecMcDataType.F.AsciiCode)
    {
      this.fBuffer.SetBytes(((IEnumerable<bool>) SoftBasic.ByteToBoolArray(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)))).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), destIndex);
      return new byte[0];
    }
    if (str == MelsecMcDataType.D.AsciiCode)
    {
      this.dBuffer.SetBytes(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)), destIndex * 2);
      return new byte[0];
    }
    if (str == MelsecMcDataType.W.AsciiCode)
    {
      this.wBuffer.SetBytes(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)), destIndex * 2);
      return new byte[0];
    }
    if (str == MelsecMcDataType.R.AsciiCode)
    {
      this.rBuffer.SetBytes(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)), destIndex * 2);
      return new byte[0];
    }
    if (!(str == MelsecMcDataType.ZR.AsciiCode))
      throw new Exception(StringResources.Language.NotSupportedDataType);
    this.zrBuffer.SetBytes(MelsecHelper.TransAsciiByteArrayToByteArray(command.RemoveBegin<byte>(20)), destIndex * 2);
    return new byte[0];
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 458752 /*0x070000*/)
      throw new Exception("File is not correct");
    this.mBuffer.SetBytes(content, 0, 0, 65536 /*0x010000*/);
    this.xBuffer.SetBytes(content, 65536 /*0x010000*/, 0, 65536 /*0x010000*/);
    this.yBuffer.SetBytes(content, 131072 /*0x020000*/, 0, 65536 /*0x010000*/);
    this.dBuffer.SetBytes(content, 196608 /*0x030000*/, 0, 131072 /*0x020000*/);
    this.wBuffer.SetBytes(content, 327680 /*0x050000*/, 0, 131072 /*0x020000*/);
    if (content.Length >= 12)
    {
      this.bBuffer.SetBytes(content, 458752 /*0x070000*/, 0, 65536 /*0x010000*/);
      this.rBuffer.SetBytes(content, 524288 /*0x080000*/, 0, 131072 /*0x020000*/);
      this.zrBuffer.SetBytes(content, 655360 /*0x0A0000*/, 0, 131072 /*0x020000*/);
    }
    if (content.Length < 15)
      return;
    this.dBuffer.SetBytes(content, 786432 /*0x0C0000*/, 131072 /*0x020000*/, 131072 /*0x020000*/);
    this.lBuffer.SetBytes(content, 917504 /*0x0E0000*/, 0, 65536 /*0x010000*/);
  }

  /// <inheritdoc />
  [HslMqttApi]
  protected override byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[983040 /*0x0F0000*/];
    Array.Copy((Array) this.mBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.xBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.yBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.dBuffer.GetBytes(), 0, (Array) destinationArray, 196608 /*0x030000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.wBuffer.GetBytes(), 0, (Array) destinationArray, 327680 /*0x050000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.bBuffer.GetBytes(), 0, (Array) destinationArray, 458752 /*0x070000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.rBuffer.GetBytes(), 0, (Array) destinationArray, 524288 /*0x080000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.zrBuffer.GetBytes(), 0, (Array) destinationArray, 655360 /*0x0A0000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.dBuffer.GetBytes(), 131072 /*0x020000*/, (Array) destinationArray, 786432 /*0x0C0000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.lBuffer.GetBytes(), 0, (Array) destinationArray, 917504 /*0x0E0000*/, 65536 /*0x010000*/);
    return destinationArray;
  }

  /// <summary>释放当前的对象</summary>
  /// <param name="disposing">是否托管对象</param>
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.xBuffer?.Dispose();
      this.yBuffer?.Dispose();
      this.mBuffer?.Dispose();
      this.dBuffer?.Dispose();
      this.wBuffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <summary>
  /// 获取或设置当前的通信格式是否是二进制<br />
  /// Get or set whether the current communication format is binary
  /// </summary>
  public bool IsBinary
  {
    get => this.isBinary;
    set => this.isBinary = value;
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecMcServer[{this.Port}]";
}
