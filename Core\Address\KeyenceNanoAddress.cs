﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.KeyenceNanoAddress
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>基恩士上位链路协议的地址类对象</summary>
public class KeyenceNanoAddress : DeviceAddressDataBase
{
  /// <summary>实例化一个默认的对象</summary>
  public KeyenceNanoAddress()
  {
  }

  /// <summary>通过指定的参数来实例化对象</summary>
  /// <param name="dataCode">数据类型</param>
  /// <param name="address">偏移地址</param>
  /// <param name="splits">切割但愿长度</param>
  public KeyenceNanoAddress(string dataCode, int address, int splits)
  {
    this.DataCode = dataCode;
    this.AddressStart = address;
    this.SplitLength = splits;
  }

  /// <summary>
  /// 获取或设置等待读取的数据的代码<br />
  /// Get or set the code of the data waiting to be read
  /// </summary>
  public string DataCode { get; set; }

  /// <summary>
  /// 获取或设置读取的时候切割的数据长度信息<br />
  /// Gets or sets the data length information that is cut when reading
  /// </summary>
  public int SplitLength { get; set; }

  /// <summary>
  /// 获取地址的字符串表示方式<br />
  /// Gets a string representation of the address
  /// </summary>
  /// <returns>字符串信息</returns>
  public string GetAddressStartFormat()
  {
    switch (this.DataCode)
    {
      case "":
      case "CR":
      case "LR":
      case "MR":
        return this.AddressStart >= 16 /*0x10*/ ? $"{this.AddressStart / 16 /*0x10*/}{this.AddressStart % 16 /*0x10*/:D2}" : $"{this.AddressStart % 16 /*0x10*/}";
      case "B":
      case "VB":
      case "W":
        return this.AddressStart.ToString("X");
      default:
        return this.AddressStart.ToString();
    }
  }

  /// <summary>
  /// 该地址的默认数据类型是否是位地址<br />
  /// Whether the default data type for this address is a bit address
  /// </summary>
  /// <returns>是否位地址</returns>
  public bool IsBitAddressDefault()
  {
    switch (this.DataCode)
    {
      case "":
      case "B":
      case "CR":
      case "LR":
      case "MR":
      case "R":
      case "VB":
        return true;
      default:
        return false;
    }
  }

  /// <summary>从指定的地址信息解析成真正的设备地址信息</summary>
  /// <param name="address">地址信息</param>
  /// <param name="length">数据长度</param>
  public override void Parse(string address, ushort length)
  {
    OperateResult<KeyenceNanoAddress> from = KeyenceNanoAddress.ParseFrom(address, length);
    if (!from.IsSuccess)
      return;
    this.AddressStart = from.Content.AddressStart;
    this.DataCode = from.Content.DataCode;
    this.SplitLength = from.Content.SplitLength;
  }

  /// <summary>
  /// 位地址转换方法，101等同于10.1等同于10*16+1=161<br />
  /// Bit address conversion method, 101 is equivalent to 10.1 is equivalent to 10 * 16 + 1 = 161
  /// </summary>
  /// <param name="address">地址信息</param>
  /// <returns>实际的位地址信息</returns>
  private static int CalculateAddress(string address)
  {
    return address.IndexOf(".") >= 0 ? Convert.ToInt32(address.Substring(0, address.IndexOf("."))) * 16 /*0x10*/ + HslHelper.CalculateBitStartIndex(address.Substring(address.IndexOf(".") + 1)) : (address.Length > 2 ? Convert.ToInt32(address.Substring(0, address.Length - 2)) * 16 /*0x10*/ + Convert.ToInt32(address.Substring(address.Length - 2)) : Convert.ToInt32(address));
  }

  /// <summary>解析出一个基恩士上位链路协议的地址信息</summary>
  /// <param name="address">字符串地址</param>
  /// <param name="length">长度信息</param>
  /// <returns>成功地址</returns>
  public static OperateResult<KeyenceNanoAddress> ParseFrom(string address, ushort length)
  {
    try
    {
      if (address.StartsWith("CTH") || address.StartsWith("cth"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("CTH", Convert.ToInt32(address.Substring(3)), 2));
      if (address.StartsWith("CTC") || address.StartsWith("ctc"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("CTC", Convert.ToInt32(address.Substring(3)), 4));
      if (address.StartsWith("CR") || address.StartsWith("cr"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("CR", KeyenceNanoAddress.CalculateAddress(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("MR") || address.StartsWith("mr"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("MR", KeyenceNanoAddress.CalculateAddress(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("LR") || address.StartsWith("lr"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("LR", KeyenceNanoAddress.CalculateAddress(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("DM") || address.StartsWith("dm"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("DM", Convert.ToInt32(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("CM") || address.StartsWith("cm"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("CM", Convert.ToInt32(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("TM") || address.StartsWith("tm"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("TM", Convert.ToInt32(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("VM") || address.StartsWith("vm"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("VM", Convert.ToInt32(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("VB") || address.StartsWith("vb"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("VB", Convert.ToInt32(address.Substring(2), 16 /*0x10*/), 256 /*0x0100*/));
      if (address.StartsWith("EM") || address.StartsWith("em"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("EM", Convert.ToInt32(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("FM") || address.StartsWith("fm"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("FM", Convert.ToInt32(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("ZF") || address.StartsWith("zf"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("ZF", Convert.ToInt32(address.Substring(2)), 256 /*0x0100*/));
      if (address.StartsWith("AT") || address.StartsWith("at"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("AT", Convert.ToInt32(address.Substring(2)), 8));
      if (address.StartsWith("TS") || address.StartsWith("ts"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("TS", Convert.ToInt32(address.Substring(2)), 64 /*0x40*/));
      if (address.StartsWith("TC") || address.StartsWith("tc"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("TC", Convert.ToInt32(address.Substring(2)), 64 /*0x40*/));
      if (address.StartsWith("CC") || address.StartsWith("cc"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("CC", Convert.ToInt32(address.Substring(2)), 64 /*0x40*/));
      if (address.StartsWith("CS") || address.StartsWith("cs"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("CS", Convert.ToInt32(address.Substring(2)), 64 /*0x40*/));
      if (address.StartsWith("W") || address.StartsWith("w"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("W", Convert.ToInt32(address.Substring(1), 16 /*0x10*/), 256 /*0x0100*/));
      if (address.StartsWith("Z") || address.StartsWith("z"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("Z", Convert.ToInt32(address.Substring(1)), 12));
      if (address.StartsWith("R") || address.StartsWith("r"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("", KeyenceNanoAddress.CalculateAddress(address.Substring(1)), 256 /*0x0100*/));
      if (address.StartsWith("B") || address.StartsWith("b"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("B", Convert.ToInt32(address.Substring(1), 16 /*0x10*/), 256 /*0x0100*/));
      if (address.StartsWith("T") || address.StartsWith("t"))
        return OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("T", Convert.ToInt32(address.Substring(1)), 64 /*0x40*/));
      return address.StartsWith("C") || address.StartsWith("c") ? OperateResult.CreateSuccessResult<KeyenceNanoAddress>(new KeyenceNanoAddress("C", Convert.ToInt32(address.Substring(1)), 64 /*0x40*/)) : throw new Exception(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<KeyenceNanoAddress>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
  }
}
