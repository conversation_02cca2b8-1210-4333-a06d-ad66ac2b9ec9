﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.CJT.CJT188
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Instrument.CJT.Helper;
using HslCommunication.Instrument.DLT.Helper;
using HslCommunication.Reflection;
using System;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Instrument.CJT;

/// <summary>城市建设部的188协议，基于DJ/T188-2004实现的协议</summary>
public class CJT188 : DeviceSerialPort, ICjt188, IReadWriteDevice, IReadWriteNet
{
  private string station = "1";

  /// <summary>
  /// 指定地址域来实例化一个对象，地址域是一个14个字符的BCD码，例如：14910000729011<br />
  /// Specify the address field, to instantiate an object, which address field is a 14-character BCD code, for example: 14910000729011
  /// </summary>
  /// <param name="station">设备的地址信息，是一个14字符的BCD码</param>
  public CJT188(string station)
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.station = station;
    this.ReceiveEmptyDataCount = 5;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new CJT188Message(this.StationMatch);
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> ReadFromCoreServer(byte[] send)
  {
    OperateResult<byte[]> operateResult = base.ReadFromCoreServer(send);
    if (!operateResult.IsSuccess)
      return operateResult;
    int headCode68H = DLT645Helper.FindHeadCode68H(operateResult.Content);
    return headCode68H > 0 ? OperateResult.CreateSuccessResult<byte[]>(operateResult.Content.RemoveBegin<byte>(headCode68H)) : operateResult;
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    if (!this.EnableCodeFE)
      return base.PackCommandWithHeader(command);
    return SoftBasic.SpliceArray<byte>(new byte[2]
    {
      (byte) 254,
      (byte) 254
    }, command);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.ICjt188.ActiveDeveice" />
  public OperateResult ActiveDeveice()
  {
    return (OperateResult) this.ReadFromCoreServer(new byte[2]
    {
      (byte) 254,
      (byte) 254
    }, false, false);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.Read(HslCommunication.Instrument.CJT.Helper.ICjt188,System.String,System.Int32)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return CJT188Helper.Read((ICjt188) this, address, (int) length);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.Write(HslCommunication.Instrument.CJT.Helper.ICjt188,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return CJT188Helper.Write((ICjt188) this, address, value);
  }

  /// <inheritdoc />
  [HslMqttApi("ReadFloatArray", "")]
  public override OperateResult<float[]> ReadFloat(string address, ushort length)
  {
    return CJT188Helper.ReadValue<float>((ICjt188) this, address, length, new Func<string, float>(float.Parse));
  }

  /// <inheritdoc />
  [HslMqttApi("ReadDoubleArray", "")]
  public override OperateResult<double[]> ReadDouble(string address, ushort length)
  {
    return CJT188Helper.ReadValue<double>((ICjt188) this, address, length, new Func<string, double>(double.Parse));
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    return ByteTransformHelper.GetResultFromArray<string>(this.ReadStringArray(address));
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.ReadStringArray(HslCommunication.Instrument.CJT.Helper.ICjt188,System.String)" />
  public OperateResult<string[]> ReadStringArray(string address)
  {
    return CJT188Helper.ReadStringArray((ICjt188) this, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.CJT188.ReadFloat(System.String,System.UInt16)" />
  public override async Task<OperateResult<float[]>> ReadFloatAsync(string address, ushort length)
  {
    OperateResult<float[]> operateResult = await Task.Run<OperateResult<float[]>>((Func<OperateResult<float[]>>) (() => this.ReadFloat(address, length)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.CJT188.ReadDouble(System.String,System.UInt16)" />
  public override async Task<OperateResult<double[]>> ReadDoubleAsync(string address, ushort length)
  {
    OperateResult<double[]> operateResult = await Task.Run<OperateResult<double[]>>((Func<OperateResult<double[]>>) (() => this.ReadDouble(address, length)));
    return operateResult;
  }

  /// <inheritdoc />
  public override async Task<OperateResult<string>> ReadStringAsync(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<string> operateResult = await Task.Run<OperateResult<string>>((Func<OperateResult<string>>) (() => this.ReadString(address, length, encoding)));
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.ReadAddress(HslCommunication.Instrument.CJT.Helper.ICjt188)" />
  public OperateResult<string> ReadAddress() => CJT188Helper.ReadAddress((ICjt188) this);

  /// <inheritdoc cref="M:HslCommunication.Instrument.CJT.Helper.CJT188Helper.WriteAddress(HslCommunication.Instrument.CJT.Helper.ICjt188,System.String)" />
  public OperateResult WriteAddress(string address)
  {
    return CJT188Helper.WriteAddress((ICjt188) this, address);
  }

  /// <summary>
  /// 获取或设置仪表的类型，通常是 0x10:冷水水表  0x11:生活热水水表  0x12:直饮水水表  0x13:中水水表  0x20:热量表(热量)  0x21:热量表(冷量)  0x30:燃气表  0x40:电度表
  /// </summary>
  public byte InstrumentType { get; set; }

  /// <summary>
  /// 获取或设置当前的地址域信息，是一个14个字符的BCD码，例如：14910000729011<br />
  /// Get or set the current address domain information, which is a 14-character BCD code, for example: 14910000729011
  /// </summary>
  public string Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Instrument.DLT.Helper.IDlt645.EnableCodeFE" />
  public bool EnableCodeFE { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.CJT188Message.StationMatch" />
  public bool StationMatch { get; set; } = false;

  /// <inheritdoc />
  public override string ToString() => $"CJT188[{this.PortName}:{this.BaudRate}]";
}
