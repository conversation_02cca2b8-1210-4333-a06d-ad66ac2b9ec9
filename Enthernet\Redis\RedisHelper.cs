﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Enthernet.Redis.RedisHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.Text;

#nullable disable
namespace HslCommunication.Enthernet.Redis;

/// <summary>提供了redis辅助类的一些方法</summary>
public class RedisHelper
{
  /// <summary>将字符串数组打包成一个redis的报文信息</summary>
  /// <param name="commands">字节数据信息</param>
  /// <returns>结果报文信息</returns>
  public static byte[] PackStringCommand(string[] commands)
  {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append('*');
    stringBuilder.Append(commands.Length.ToString());
    stringBuilder.Append("\r\n");
    for (int index = 0; index < commands.Length; ++index)
    {
      stringBuilder.Append('$');
      stringBuilder.Append(Encoding.UTF8.GetBytes(commands[index]).Length.ToString());
      stringBuilder.Append("\r\n");
      stringBuilder.Append(commands[index]);
      stringBuilder.Append("\r\n");
    }
    return Encoding.UTF8.GetBytes(stringBuilder.ToString());
  }

  /// <summary>生成一个订阅多个主题的报文信息</summary>
  /// <param name="topics">多个的主题信息</param>
  /// <returns>结果报文信息</returns>
  public static byte[] PackSubscribeCommand(string[] topics)
  {
    List<string> stringList = new List<string>();
    stringList.Add("SUBSCRIBE");
    stringList.AddRange((IEnumerable<string>) topics);
    return RedisHelper.PackStringCommand(stringList.ToArray());
  }

  /// <summary>生成一个取消订阅多个主题的报文信息</summary>
  /// <param name="topics">多个的主题信息</param>
  /// <returns>结果报文信息</returns>
  public static byte[] PackUnSubscribeCommand(string[] topics)
  {
    List<string> stringList = new List<string>();
    stringList.Add("UNSUBSCRIBE");
    stringList.AddRange((IEnumerable<string>) topics);
    return RedisHelper.PackStringCommand(stringList.ToArray());
  }

  /// <summary>从原始的结果数据对象中提取出数字数据</summary>
  /// <param name="commandLine">原始的字节数据</param>
  /// <returns>带有结果对象的数据信息</returns>
  public static OperateResult<int> GetNumberFromCommandLine(byte[] commandLine)
  {
    try
    {
      return OperateResult.CreateSuccessResult<int>(Convert.ToInt32(Encoding.UTF8.GetString(commandLine).TrimEnd('\r', '\n').Substring(1)));
    }
    catch (Exception ex)
    {
      return new OperateResult<int>(ex.Message);
    }
  }

  /// <summary>从原始的结果数据对象中提取出数字数据</summary>
  /// <param name="commandLine">原始的字节数据</param>
  /// <returns>带有结果对象的数据信息</returns>
  public static OperateResult<long> GetLongNumberFromCommandLine(byte[] commandLine)
  {
    try
    {
      return OperateResult.CreateSuccessResult<long>(Convert.ToInt64(Encoding.UTF8.GetString(commandLine).TrimEnd('\r', '\n').Substring(1)));
    }
    catch (Exception ex)
    {
      return new OperateResult<long>(ex.Message);
    }
  }

  /// <summary>从结果的数据对象里提取字符串的信息</summary>
  /// <param name="commandLine">原始的字节数据</param>
  /// <returns>带有结果对象的数据信息</returns>
  public static OperateResult<string[]> GetStringFromCommandLine(byte[] commandLine)
  {
    try
    {
      if (commandLine[0] != (byte) 36)
        return new OperateResult<string[]>(Encoding.UTF8.GetString(commandLine));
      int num1 = -1;
      int num2 = -1;
      for (int index = 0; index < commandLine.Length; ++index)
      {
        if (commandLine[index] == (byte) 13 || commandLine[index] == (byte) 10)
          num1 = index;
        if (commandLine[index] == (byte) 10)
        {
          num2 = index;
          break;
        }
      }
      int int32 = Convert.ToInt32(Encoding.UTF8.GetString(commandLine, 1, num1 - 1));
      if (int32 < 0)
        return new OperateResult<string[]>("(nil) None Value");
      return OperateResult.CreateSuccessResult<string[]>(new string[1]
      {
        Encoding.UTF8.GetString(commandLine, num2 + 1, int32)
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<string[]>(ex.Message);
    }
  }

  /// <summary>从redis的结果数据中分析出所有的字符串信息</summary>
  /// <param name="commandLine">结果数据</param>
  /// <returns>带有结果对象的数据信息</returns>
  public static OperateResult<string[]> GetStringsFromCommandLine(byte[] commandLine)
  {
    try
    {
      List<string> stringList = new List<string>();
      if (commandLine[0] != (byte) 42)
        return new OperateResult<string[]>(Encoding.UTF8.GetString(commandLine));
      int index1 = 0;
      for (int index2 = 0; index2 < commandLine.Length; ++index2)
      {
        if (commandLine[index2] == (byte) 13 || commandLine[index2] == (byte) 10)
        {
          index1 = index2;
          break;
        }
      }
      int int32_1 = Convert.ToInt32(Encoding.UTF8.GetString(commandLine, 1, index1 - 1));
      for (int index3 = 0; index3 < int32_1; ++index3)
      {
        int num1 = -1;
        for (int index4 = index1; index4 < commandLine.Length; ++index4)
        {
          if (commandLine[index4] == (byte) 10)
          {
            num1 = index4;
            break;
          }
        }
        index1 = num1 + 1;
        if (commandLine[index1] == (byte) 36)
        {
          int num2 = -1;
          for (int index5 = index1; index5 < commandLine.Length; ++index5)
          {
            if (commandLine[index5] == (byte) 13 || commandLine[index5] == (byte) 10)
            {
              num2 = index5;
              break;
            }
          }
          int int32_2 = Convert.ToInt32(Encoding.UTF8.GetString(commandLine, index1 + 1, num2 - index1 - 1));
          if (int32_2 >= 0)
          {
            for (int index6 = index1; index6 < commandLine.Length; ++index6)
            {
              if (commandLine[index6] == (byte) 10)
              {
                num1 = index6;
                break;
              }
            }
            int index7 = num1 + 1;
            stringList.Add(Encoding.UTF8.GetString(commandLine, index7, int32_2));
            index1 = index7 + int32_2;
          }
          else
            stringList.Add((string) null);
        }
        else
        {
          int num3 = -1;
          for (int index8 = index1; index8 < commandLine.Length; ++index8)
          {
            if (commandLine[index8] == (byte) 13 || commandLine[index8] == (byte) 10)
            {
              num3 = index8;
              break;
            }
          }
          stringList.Add(Encoding.UTF8.GetString(commandLine, index1, num3 - index1 - 1));
        }
      }
      return OperateResult.CreateSuccessResult<string[]>(stringList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<string[]>(ex.Message);
    }
  }
}
