﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.LogNetBase
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>日志存储类的基类，提供一些基础的服务</summary>
/// <remarks>
/// 基于此类可以实现任意的规则的日志存储规则，欢迎大家补充实现，本组件实现了3个日志类
/// <list type="number">
/// <item>单文件日志类 <see cref="T:HslCommunication.LogNet.LogNetSingle" /></item>
/// <item>根据文件大小的类 <see cref="T:HslCommunication.LogNet.LogNetFileSize" /></item>
/// <item>根据时间进行存储的类 <see cref="T:HslCommunication.LogNet.LogNetDateTime" /></item>
/// </list>
/// </remarks>
public abstract class LogNetBase : IDisposable
{
  /// <summary>文件存储的锁</summary>
  protected SimpleHybirdLock m_fileSaveLock;
  private HslMessageDegree m_messageDegree = HslMessageDegree.DEBUG;
  private Queue<HslMessageItem> m_WaitForSave;
  private SimpleHybirdLock m_simpleHybirdLock;
  private int m_SaveStatus = 0;
  private List<string> filtrateKeyword;
  private object filtrateLock;
  private string lastLogSaveFileName = string.Empty;
  private int lastConsoleDegree = -1;
  private bool disposedValue = false;

  /// <summary>
  /// 实例化一个日志对象<br />
  /// Instantiate a log object
  /// </summary>
  public LogNetBase()
  {
    this.m_fileSaveLock = new SimpleHybirdLock();
    this.m_simpleHybirdLock = new SimpleHybirdLock();
    this.m_WaitForSave = new Queue<HslMessageItem>();
    this.filtrateKeyword = new List<string>();
    this.filtrateLock = new object();
  }

  /// <inheritdoc cref="E:HslCommunication.LogNet.ILogNet.BeforeSaveToFile" />
  public event EventHandler<HslEventArgs> BeforeSaveToFile = null;

  private void OnBeforeSaveToFile(HslEventArgs args)
  {
    EventHandler<HslEventArgs> beforeSaveToFile = this.BeforeSaveToFile;
    if (beforeSaveToFile == null)
      return;
    beforeSaveToFile((object) this, args);
  }

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.LogSaveMode" />
  public LogSaveMode LogSaveMode { get; protected set; }

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.LogNetStatistics" />
  public LogStatistics LogNetStatistics { get; set; }

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.ConsoleOutput" />
  public bool ConsoleOutput { get; set; }

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.LogThreadID" />
  public bool LogThreadID { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.LogStxAsciiCode" />
  public bool LogStxAsciiCode { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.HourDeviation" />
  public int HourDeviation { get; set; } = 0;

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.EncoderShouldEmitUTF8Identifier" />
  public bool EncoderShouldEmitUTF8Identifier { get; set; } = true;

  /// <inheritdoc cref="P:HslCommunication.LogNet.ILogNet.ConsoleColorEnable" />
  public bool ConsoleColorEnable { get; set; } = true;

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteDebug(System.String)" />
  [HslMqttApi]
  public void WriteDebug(string text) => this.WriteDebug(string.Empty, text);

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteDebug(System.String,System.String)" />
  [HslMqttApi(ApiTopic = "WriteDebugKeyWord")]
  public void WriteDebug(string keyWord, string text)
  {
    this.RecordMessage(HslMessageDegree.DEBUG, keyWord, text);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteInfo(System.String)" />
  [HslMqttApi]
  public void WriteInfo(string text) => this.WriteInfo(string.Empty, text);

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteInfo(System.String,System.String)" />
  [HslMqttApi(ApiTopic = "WriteInfoKeyWord")]
  public void WriteInfo(string keyWord, string text)
  {
    this.RecordMessage(HslMessageDegree.INFO, keyWord, text);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteWarn(System.String)" />
  [HslMqttApi]
  public void WriteWarn(string text) => this.WriteWarn(string.Empty, text);

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteWarn(System.String,System.String)" />
  [HslMqttApi(ApiTopic = "WriteWarnKeyWord")]
  public void WriteWarn(string keyWord, string text)
  {
    this.RecordMessage(HslMessageDegree.WARN, keyWord, text);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteError(System.String)" />
  [HslMqttApi]
  public void WriteError(string text) => this.WriteError(string.Empty, text);

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteError(System.String,System.String)" />
  [HslMqttApi(ApiTopic = "WriteErrorKeyWord")]
  public void WriteError(string keyWord, string text)
  {
    this.RecordMessage(HslMessageDegree.ERROR, keyWord, text);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteFatal(System.String)" />
  [HslMqttApi]
  public void WriteFatal(string text) => this.WriteFatal(string.Empty, text);

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteFatal(System.String,System.String)" />
  [HslMqttApi(ApiTopic = "WriteFatalKeyWord")]
  public void WriteFatal(string keyWord, string text)
  {
    this.RecordMessage(HslMessageDegree.FATAL, keyWord, text);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteException(System.String,System.Exception)" />
  public void WriteException(string keyWord, Exception ex)
  {
    this.WriteException(keyWord, string.Empty, ex);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteException(System.String,System.String,System.Exception)" />
  public void WriteException(string keyWord, string text, Exception ex)
  {
    this.RecordMessage(HslMessageDegree.FATAL, keyWord, LogNetManagment.GetSaveStringFromException(text, ex));
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.RecordMessage(HslCommunication.LogNet.HslMessageDegree,System.String,System.String)" />
  public void RecordMessage(HslMessageDegree degree, string keyWord, string text)
  {
    this.WriteToFile(degree, keyWord, text);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteDescrition(System.String)" />
  [HslMqttApi]
  public void WriteDescrition(string description)
  {
    if (string.IsNullOrEmpty(description))
      return;
    StringBuilder sb = new StringBuilder("\u0002");
    sb.Append(Environment.NewLine);
    sb.Append("\u0002/");
    int num = 118 - this.CalculateStringOccupyLength(description);
    if (num >= 8)
    {
      int count = (num - 8) / 2;
      this.AppendCharToStringBuilder(sb, '*', count);
      sb.Append("   ");
      sb.Append(description);
      sb.Append("   ");
      if (num % 2 == 0)
        this.AppendCharToStringBuilder(sb, '*', count);
      else
        this.AppendCharToStringBuilder(sb, '*', count + 1);
    }
    else if (num >= 2)
    {
      int count = (num - 2) / 2;
      this.AppendCharToStringBuilder(sb, '*', count);
      sb.Append(description);
      if (num % 2 == 0)
        this.AppendCharToStringBuilder(sb, '*', count);
      else
        this.AppendCharToStringBuilder(sb, '*', count + 1);
    }
    else
      sb.Append(description);
    sb.Append("/");
    sb.Append(Environment.NewLine);
    this.RecordMessage(HslMessageDegree.None, string.Empty, sb.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteAnyString(System.String)" />
  [HslMqttApi]
  public void WriteAnyString(string text)
  {
    this.RecordMessage(HslMessageDegree.None, string.Empty, text);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.WriteNewLine" />
  [HslMqttApi]
  public void WriteNewLine()
  {
    this.RecordMessage(HslMessageDegree.None, string.Empty, "\u0002" + Environment.NewLine);
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.SetMessageDegree(HslCommunication.LogNet.HslMessageDegree)" />
  public void SetMessageDegree(HslMessageDegree degree) => this.m_messageDegree = degree;

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.FiltrateKeyword(System.String)" />
  [HslMqttApi]
  public void FiltrateKeyword(string keyword)
  {
    lock (this.filtrateLock)
    {
      if (this.filtrateKeyword.Contains(keyword))
        return;
      this.filtrateKeyword.Add(keyword);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.LogNet.ILogNet.RemoveFiltrate(System.String)" />
  [HslMqttApi]
  public void RemoveFiltrate(string keyword)
  {
    lock (this.filtrateLock)
    {
      if (!this.filtrateKeyword.Contains(keyword))
        return;
      this.filtrateKeyword.Remove(keyword);
    }
  }

  private void WriteToFile(HslMessageDegree degree, string keyword, string text)
  {
    if (degree > this.m_messageDegree)
      return;
    this.AddItemToCache(this.GetHslMessageItem(degree, keyword, text), true);
  }

  private void AddItemToCache(HslMessageItem item, bool start)
  {
    this.m_simpleHybirdLock.Enter();
    this.m_WaitForSave.Enqueue(item);
    this.m_simpleHybirdLock.Leave();
    if (!start)
      return;
    this.StartSaveFile();
  }

  private void StartSaveFile()
  {
    if (Interlocked.CompareExchange(ref this.m_SaveStatus, 1, 0) != 0)
      return;
    ThreadPool.QueueUserWorkItem(new WaitCallback(this.ThreadPoolSaveFile), (object) null);
  }

  private HslMessageItem GetAndRemoveLogItem()
  {
    HslMessageItem andRemoveLogItem = (HslMessageItem) null;
    this.m_simpleHybirdLock.Enter();
    try
    {
      andRemoveLogItem = this.m_WaitForSave.Count > 0 ? this.m_WaitForSave.Dequeue() : (HslMessageItem) null;
    }
    catch
    {
    }
    this.m_simpleHybirdLock.Leave();
    return andRemoveLogItem;
  }

  private List<HslMessageItem> GetAndRemoveLogItemArray()
  {
    List<HslMessageItem> removeLogItemArray = new List<HslMessageItem>();
    this.m_simpleHybirdLock.Enter();
    try
    {
      while (this.m_WaitForSave.Count > 0)
        removeLogItemArray.Add(this.m_WaitForSave.Dequeue());
    }
    catch
    {
    }
    this.m_simpleHybirdLock.Leave();
    return removeLogItemArray;
  }

  private void SetConsoleWriteForeColor(HslMessageDegree degree)
  {
    if (!this.ConsoleColorEnable)
      return;
    if (this.lastConsoleDegree < 0)
      this.lastConsoleDegree = (int) degree;
    if ((HslMessageDegree) this.lastConsoleDegree == degree)
      return;
    this.lastConsoleDegree = (int) degree;
    switch (degree)
    {
      case HslMessageDegree.FATAL:
        Console.ForegroundColor = ConsoleColor.DarkRed;
        break;
      case HslMessageDegree.ERROR:
        Console.ForegroundColor = ConsoleColor.Red;
        break;
      case HslMessageDegree.WARN:
        Console.ForegroundColor = ConsoleColor.Yellow;
        break;
      case HslMessageDegree.INFO:
        Console.ForegroundColor = ConsoleColor.White;
        break;
      case HslMessageDegree.DEBUG:
        Console.ForegroundColor = ConsoleColor.DarkGray;
        break;
      default:
        Console.ForegroundColor = ConsoleColor.White;
        break;
    }
  }

  private void ConsoleWriteLog(HslMessageItem log)
  {
    this.SetConsoleWriteForeColor(log.Degree);
    Console.WriteLine(this.HslMessageFormat(log, false));
  }

  private void ThreadPoolSaveFile(object obj)
  {
    HslMessageItem andRemoveLogItem = this.GetAndRemoveLogItem();
    this.m_fileSaveLock.Enter();
    string fileSaveName = this.GetFileSaveName();
    bool createNewLogFile = false;
    if (!string.IsNullOrEmpty(fileSaveName))
    {
      if (fileSaveName != this.lastLogSaveFileName)
      {
        createNewLogFile = true;
        this.lastLogSaveFileName = fileSaveName;
      }
      StreamWriter streamWriter = (StreamWriter) null;
      try
      {
        streamWriter = new StreamWriter(fileSaveName, true, this.EncoderShouldEmitUTF8Identifier ? Encoding.UTF8 : (Encoding) new UTF8Encoding(false));
        for (; andRemoveLogItem != null; andRemoveLogItem = this.GetAndRemoveLogItem())
        {
          if (!andRemoveLogItem.HasLogOutput)
          {
            andRemoveLogItem.HasLogOutput = true;
            if (this.ConsoleOutput)
              this.ConsoleWriteLog(andRemoveLogItem);
            this.OnBeforeSaveToFile(new HslEventArgs()
            {
              HslMessage = andRemoveLogItem
            });
            this.LogNetStatistics?.StatisticsAdd();
          }
          bool flag = true;
          lock (this.filtrateLock)
            flag = !this.filtrateKeyword.Contains(andRemoveLogItem.KeyWord);
          if (andRemoveLogItem.Cancel)
            flag = false;
          if (flag)
          {
            streamWriter.Write(this.HslMessageFormat(andRemoveLogItem, true));
            streamWriter.Write(Environment.NewLine);
            streamWriter.Flush();
          }
        }
      }
      catch (Exception ex)
      {
        if (ex is DirectoryNotFoundException)
        {
          try
          {
            string directoryName = Path.GetDirectoryName(fileSaveName);
            if (!string.IsNullOrEmpty(directoryName) && !Directory.Exists(directoryName))
              Directory.CreateDirectory(directoryName);
          }
          catch
          {
          }
        }
        Interlocked.Increment(ref andRemoveLogItem.WriteRetryTimes);
        this.AddItemToCache(andRemoveLogItem, false);
      }
      finally
      {
        streamWriter?.Dispose();
      }
    }
    else if (this.ConsoleOutput)
    {
      if (andRemoveLogItem != null)
      {
        this.ConsoleWriteLog(andRemoveLogItem);
        this.OnBeforeSaveToFile(new HslEventArgs()
        {
          HslMessage = andRemoveLogItem
        });
      }
      for (List<HslMessageItem> removeLogItemArray = this.GetAndRemoveLogItemArray(); removeLogItemArray.Count > 0; removeLogItemArray = this.GetAndRemoveLogItemArray())
      {
        StringBuilder stringBuilder = new StringBuilder();
        HslMessageDegree degree = removeLogItemArray[0].Degree;
        for (int index = 0; index < removeLogItemArray.Count; ++index)
        {
          if (removeLogItemArray[index].Degree != degree)
          {
            this.SetConsoleWriteForeColor(degree);
            Console.Write(stringBuilder.ToString());
            degree = removeLogItemArray[index].Degree;
            stringBuilder = new StringBuilder();
          }
          stringBuilder.AppendLine(this.HslMessageFormat(removeLogItemArray[index], false));
          if (index >= removeLogItemArray.Count - 1)
          {
            this.SetConsoleWriteForeColor(degree);
            Console.Write(stringBuilder.ToString());
          }
          this.OnBeforeSaveToFile(new HslEventArgs()
          {
            HslMessage = removeLogItemArray[index]
          });
        }
      }
    }
    else
    {
      for (; andRemoveLogItem != null; andRemoveLogItem = this.GetAndRemoveLogItem())
      {
        if (this.ConsoleOutput)
          this.ConsoleWriteLog(andRemoveLogItem);
        this.OnBeforeSaveToFile(new HslEventArgs()
        {
          HslMessage = andRemoveLogItem
        });
      }
    }
    this.m_fileSaveLock.Leave();
    Interlocked.Exchange(ref this.m_SaveStatus, 0);
    this.OnWriteCompleted(createNewLogFile);
    if (this.m_WaitForSave.Count <= 0)
      return;
    HslHelper.ThreadSleep(0);
    this.StartSaveFile();
  }

  /// <summary>
  /// 根据需要存储的日志消息，获取实际存储的字符串，重写本方法就可以自定义输出内容<br />
  /// According to the log message that needs to be stored, get the actual stored string, and the rewrite method can customize the output content
  /// </summary>
  /// <param name="hslMessage">等待存储或是输出的日志对象</param>
  /// <param name="writeFile">是否输出到文件里</param>
  /// <returns>最终等待存储的字符串内容</returns>
  protected virtual string HslMessageFormat(HslMessageItem hslMessage, bool writeFile)
  {
    StringBuilder stringBuilder = new StringBuilder();
    if (hslMessage.Degree != HslMessageDegree.None)
    {
      if (writeFile && this.LogStxAsciiCode)
        stringBuilder.Append("\u0002");
      stringBuilder.Append("[");
      stringBuilder.Append(LogNetManagment.GetDegreeDescription(hslMessage.Degree));
      stringBuilder.Append("] ");
      stringBuilder.Append(hslMessage.Time.ToString("yyyy-MM-dd HH:mm:ss.fff"));
      stringBuilder.Append(" ");
      if (this.LogThreadID)
      {
        stringBuilder.Append("Thread:[");
        stringBuilder.Append(hslMessage.ThreadId.ToString("D3"));
        stringBuilder.Append("] ");
      }
      if (hslMessage.WriteRetryTimes == 2)
        stringBuilder.Append("[Retry] ");
      else if (hslMessage.WriteRetryTimes > 2)
        stringBuilder.Append($"[Retry:{hslMessage.WriteRetryTimes}] ");
      if (!string.IsNullOrEmpty(hslMessage.KeyWord))
      {
        stringBuilder.Append(hslMessage.KeyWord);
        stringBuilder.Append(" : ");
      }
    }
    stringBuilder.Append(hslMessage.Text);
    return stringBuilder.ToString();
  }

  /// <inheritdoc />
  public override string ToString() => $"LogNetBase[{this.LogSaveMode}]";

  /// <inheritdoc />
  protected virtual string GetFileSaveName() => string.Empty;

  /// <summary>
  /// 当写入文件完成的时候触发，这时候已经释放了文件的句柄了。<br />
  /// Triggered when writing to the file is complete, and the file handle has been released.
  /// </summary>
  protected virtual void OnWriteCompleted(bool createNewLogFile)
  {
  }

  private HslMessageItem GetHslMessageItem(HslMessageDegree degree, string keyWord, string text)
  {
    if (this.HourDeviation == 0)
      return new HslMessageItem()
      {
        KeyWord = keyWord,
        Degree = degree,
        Text = text,
        ThreadId = Thread.CurrentThread.ManagedThreadId
      };
    return new HslMessageItem()
    {
      KeyWord = keyWord,
      Degree = degree,
      Text = text,
      ThreadId = Thread.CurrentThread.ManagedThreadId,
      Time = DateTime.Now.AddHours((double) this.HourDeviation)
    };
  }

  private int CalculateStringOccupyLength(string str)
  {
    if (string.IsNullOrEmpty(str))
      return 0;
    int stringOccupyLength = 0;
    for (int index = 0; index < str.Length; ++index)
    {
      if (str[index] >= '一' && str[index] <= '龻')
        stringOccupyLength += 2;
      else
        ++stringOccupyLength;
    }
    return stringOccupyLength;
  }

  private void AppendCharToStringBuilder(StringBuilder sb, char c, int count)
  {
    for (int index = 0; index < count; ++index)
      sb.Append(c);
  }

  /// <summary>释放资源</summary>
  /// <param name="disposing">是否初次调用</param>
  protected virtual void Dispose(bool disposing)
  {
    if (this.disposedValue)
      return;
    if (disposing)
    {
      this.BeforeSaveToFile = (EventHandler<HslEventArgs>) null;
      this.m_simpleHybirdLock.Enter();
      this.m_WaitForSave.Clear();
      this.m_simpleHybirdLock.Leave();
      this.m_simpleHybirdLock.Dispose();
      this.m_fileSaveLock.Dispose();
    }
    this.disposedValue = true;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose() => this.Dispose(true);
}
