﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.Helper.IHostLink
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Profinet.Omron.Helper;

/// <summary>HostLink的接口实现</summary>
public interface IHostLink : IReadWriteDevice, IReadWriteNet
{
  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.ICF" />
  byte ICF { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.DA2" />
  byte DA2 { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.SA2" />
  byte SA2 { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.SID" />
  byte SID { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.ResponseWaitTime" />
  byte ResponseWaitTime { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.UnitNumber" />
  byte UnitNumber { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLinkOverTcp.ReadSplits" />
  int ReadSplits { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.Helper.IOmronFins.PlcType" />
  OmronPlcType PlcType { get; set; }
}
