﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.LogNetException
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>日志存储回调的异常信息</summary>
/// <summary>使用其他的异常信息来初始化日志异常</summary>
/// <param name="innerException">异常信息</param>
public class LogNetException(Exception innerException) : Exception(innerException.Message, innerException)
{
}
