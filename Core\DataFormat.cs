﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.DataFormat
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Core;

/// <summary>
/// 应用于多字节数据的解析或是生成格式<br />
/// Parsing or generating format for multibyte data
/// </summary>
public enum DataFormat
{
  /// <summary>按照顺序排序</summary>
  ABCD,
  /// <summary>按照单字反转</summary>
  BADC,
  /// <summary>按照双字反转</summary>
  CDAB,
  /// <summary>按照倒序排序</summary>
  DCBA,
}
