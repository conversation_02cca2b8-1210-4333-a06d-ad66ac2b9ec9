﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.AllenBradley.AllenBradleyItemValue
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Xml.Linq;

#nullable disable
namespace HslCommunication.Profinet.AllenBradley;

/// <summary>AB PLC的标签节点数据信息</summary>
public class AllenBradleyItemValue
{
  /// <summary>实例化一个默认的对象</summary>
  public AllenBradleyItemValue()
  {
  }

  /// <summary>指定XML元素的资源来实例化对象信息</summary>
  /// <param name="element">XML元素信息</param>
  public AllenBradleyItemValue(XElement element) => this.LoadByXml(element);

  /// <summary>当前标签的名称信息</summary>
  public string Name { get; set; }

  /// <summary>真实的数组缓存</summary>
  public byte[] Buffer { get; set; }

  /// <summary>是否是数组的数据</summary>
  public bool IsArray { get; set; }

  /// <summary>单个单位的数据长度信息</summary>
  public int TypeLength { get; set; } = 1;

  /// <summary>数据类型信息</summary>
  public ushort TypeCode { get; set; } = 193;

  /// <summary>将值转换为同等描述的序列化字符串信息</summary>
  /// <returns>Xml元素</returns>
  public XElement ToXml()
  {
    XElement xml = new XElement((XName) nameof (AllenBradleyItemValue));
    xml.SetAttributeValue((XName) "Name", (object) this.Name);
    xml.SetAttributeValue((XName) "TypeCode", (object) this.TypeCode);
    xml.SetAttributeValue((XName) "IsArray", (object) this.IsArray);
    xml.SetAttributeValue((XName) "TypeLength", (object) this.TypeLength);
    if (this.Buffer != null)
      xml.SetAttributeValue((XName) "Buffer", (object) this.Buffer.ToHexString());
    return xml;
  }

  private T GetXmlValue<T>(XElement element, string name, T defaultValue, Func<string, T> trans)
  {
    XAttribute xattribute = element.Attribute((XName) name);
    if (xattribute == null)
      return defaultValue;
    try
    {
      return trans(xattribute.Value);
    }
    catch
    {
      return defaultValue;
    }
  }

  /// <summary>从xml元素加载当前的节点数据信息</summary>
  /// <param name="element">元素信息</param>
  public void LoadByXml(XElement element)
  {
    if (!(element.Name == (XName) nameof (AllenBradleyItemValue)))
      return;
    this.Name = this.GetXmlValue<string>(element, "Name", this.Name, (Func<string, string>) (m => m));
    this.TypeCode = this.GetXmlValue<ushort>(element, "TypeCode", this.TypeCode, new Func<string, ushort>(ushort.Parse));
    this.IsArray = this.GetXmlValue<bool>(element, "IsArray", this.IsArray, new Func<string, bool>(bool.Parse));
    this.TypeLength = this.GetXmlValue<int>(element, "TypeLength", this.TypeLength, new Func<string, int>(int.Parse));
    this.Buffer = this.GetXmlValue<string>(element, "Buffer", "", (Func<string, string>) (m => m)).ToHexBytes();
  }
}
