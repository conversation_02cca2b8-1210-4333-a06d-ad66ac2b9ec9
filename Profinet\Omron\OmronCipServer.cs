﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronCipServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.AllenBradley;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>欧姆龙的CIP虚拟服务器</summary>
public class OmronCipServer : AllenBradleyServer
{
  /// <inheritdoc />
  public override void AddTagValue(string key, string value, int maxLength)
  {
    byte[] bytes = Encoding.UTF8.GetBytes(value);
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = false,
      Buffer = SoftBasic.ArrayExpandToLength<byte>(SoftBasic.SpliceArray<byte>(BitConverter.GetBytes((ushort) bytes.Length), Encoding.UTF8.GetBytes(value)), maxLength),
      TypeLength = maxLength,
      TypeCode = (ushort) 208 /*0xD0*/
    });
  }

  /// <inheritdoc />
  public override void AddTagValue(string key, string[] value, int maxLength)
  {
    byte[] numArray = new byte[maxLength * value.Length];
    for (int index = 0; index < value.Length; ++index)
    {
      byte[] bytes = Encoding.UTF8.GetBytes(value[index]);
      BitConverter.GetBytes((ushort) bytes.Length).CopyTo((Array) numArray, maxLength * index);
      bytes.CopyTo((Array) numArray, maxLength * index + 2);
    }
    this.AddTagValue(key, new AllenBradleyItemValue()
    {
      IsArray = true,
      Buffer = numArray,
      TypeLength = maxLength,
      TypeCode = (ushort) 208 /*0xD0*/
    });
  }

  /// <inheritdoc />
  public override OperateResult<string> ReadString(
    string address,
    ushort length,
    Encoding encoding)
  {
    OperateResult<byte[]> result = this.Read(address, length);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result);
    if (result.Content.Length < 2)
      return OperateResult.CreateSuccessResult<string>(encoding.GetString(result.Content));
    int uint16 = (int) BitConverter.ToUInt16(result.Content, 0);
    return OperateResult.CreateSuccessResult<string>(encoding.GetString(result.Content, 2, uint16));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, string value, Encoding encoding)
  {
    if (this.IsNeedCreateTag(address))
    {
      this.AddTagValue(address, value, 1024 /*0x0400*/);
      return OperateResult.CreateSuccessResult();
    }
    bool flag = false;
    int addressIndex = this.GetAddressIndex(ref address);
    this.simpleHybird.Enter();
    if (this.abValues.ContainsKey(address))
    {
      flag = true;
      AllenBradleyItemValue abValue = this.abValues[address];
      byte[] buffer = abValue.Buffer;
      if (buffer != null && buffer.Length >= 2)
      {
        byte[] bytes = encoding.GetBytes(value);
        BitConverter.GetBytes((ushort) bytes.Length).CopyTo((Array) abValue.Buffer, addressIndex * abValue.TypeLength);
        if (bytes.Length != 0)
          Array.Copy((Array) bytes, 0, (Array) abValue.Buffer, 2 + addressIndex * abValue.TypeLength, Math.Min(bytes.Length, abValue.Buffer.Length - 2));
      }
    }
    this.simpleHybird.Leave();
    return !flag ? (OperateResult) new OperateResult<bool>(StringResources.Language.AllenBradley04) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override byte[] ReadByCommand(PipeSession session, byte[] cipCore)
  {
    if (session.Tag is string)
      return base.ReadByCommand(session, cipCore);
    byte[] pathCommand = this.ByteTransform.TransByte(cipCore, 2, (int) cipCore[1] * 2);
    string requestPathCommand = AllenBradleyHelper.ParseRequestPathCommand(pathCommand);
    OperateResult<byte[], ushort> operateResult;
    if (session.Tag is CipSessionTag tag && tag.IsConnectedCIP)
    {
      ushort uint16 = BitConverter.ToUInt16(cipCore, 2 + pathCommand.Length);
      operateResult = this.ReadWithType(requestPathCommand, (int) uint16);
    }
    else
      operateResult = this.ReadWithType(requestPathCommand, -1);
    AllenBradleyItemValue addressItemValue = this.GetAddressItemValue(requestPathCommand);
    byte[] numArray = addressItemValue == null || addressItemValue.TypeCode != (ushort) 193 || !addressItemValue.IsArray ? AllenBradleyHelper.PackCommandResponse(operateResult.Content1, true) : AllenBradleyHelper.PackCommandResponse(SoftBasic.ByteToBoolArray(operateResult.Content1, operateResult.Content1.Length, (byte) 1).ToByteArray(), true);
    if (numArray.Length > 6)
      BitConverter.GetBytes(operateResult.Content2).CopyTo((Array) numArray, 4);
    return numArray;
  }

  /// <inheritdoc />
  protected override byte[] WriteByMessage(byte[] cipCore)
  {
    if (!this.EnableWrite)
      return AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
    byte[] pathCommand = this.ByteTransform.TransByte(cipCore, 2, (int) cipCore[1] * 2);
    string requestPathCommand = AllenBradleyHelper.ParseRequestPathCommand(pathCommand);
    ushort uint16_1 = BitConverter.ToUInt16(cipCore, 2 + pathCommand.Length);
    ushort uint16_2 = BitConverter.ToUInt16(cipCore, 4 + pathCommand.Length);
    byte[] numArray = this.ByteTransform.TransByte(cipCore, 6 + pathCommand.Length, cipCore.Length - 6 - pathCommand.Length);
    AllenBradleyItemValue addressItemValue = this.GetAddressItemValue(requestPathCommand);
    if (addressItemValue != null && addressItemValue.TypeCode == (ushort) 193 && addressItemValue.IsArray)
      return this.Write(requestPathCommand, numArray.SelectBegin<byte>(addressItemValue.Buffer.Length)).IsSuccess ? AllenBradleyHelper.PackCommandResponse(new byte[0], false) : AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
    if (uint16_1 == (ushort) 193 && uint16_2 == (ushort) 1)
    {
      bool flag = false;
      if (numArray.Length == 2 && numArray[0] == byte.MaxValue && numArray[1] == byte.MaxValue)
        flag = true;
      return this.Write(requestPathCommand, flag).IsSuccess ? AllenBradleyHelper.PackCommandResponse(new byte[0], false) : AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
    }
    return this.Write(requestPathCommand, numArray).IsSuccess ? AllenBradleyHelper.PackCommandResponse(new byte[0], false) : AllenBradleyHelper.PackCommandResponse((byte[]) null, false);
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronCipServer[{this.Port}]";
}
