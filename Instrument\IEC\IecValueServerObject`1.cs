﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.IEC.IecValueServerObject`1
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;
using System.IO;

#nullable disable
namespace HslCommunication.Instrument.IEC;

/// <summary>服务器端的IEC数据对象信息，可以实例化不连续地址，或是连续地址信息</summary>
/// <typeparam name="T"></typeparam>
public class IecValueServerObject<T>
{
  private Func<IecValueObject<T>, byte[]> _trans;
  private byte _typeID;

  /// <summary>实例化一个不连续地址的数据对象</summary>
  /// <param name="value"></param>
  /// <param name="typeID"></param>
  /// <param name="trans"></param>
  public IecValueServerObject(
    List<IecValueObject<T>> value,
    byte typeID,
    Func<IecValueObject<T>, byte[]> trans)
  {
    this._typeID = typeID;
    this._trans = trans;
    this.SetValues(value);
  }

  /// <summary>实例化一个连续地址的数据对象</summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">长度</param>
  /// <param name="typeID">数据标识</param>
  /// <param name="trans">原始字节转换关系</param>
  public IecValueServerObject(
    ushort address,
    int length,
    byte typeID,
    Func<IecValueObject<T>, byte[]> trans)
  {
    this._typeID = typeID;
    this._trans = trans;
    this.SetValues(address, length);
  }

  /// <summary>设置不连续的地址数据信息</summary>
  /// <param name="values"></param>
  public void SetValues(List<IecValueObject<T>> values)
  {
    this.Values = values;
    this.IsAddressContinuous = false;
    values.ForEach((Action<IecValueObject<T>>) (m => m.OnValueChanged = new Action<IecValueObject<T>>(this.Iec104ValueChanged)));
  }

  /// <summary>设置连续的地址数据信息</summary>
  /// <param name="address">起始地址</param>
  /// <param name="length">数据长度</param>
  public void SetValues(ushort address, int length)
  {
    this.Values = new List<IecValueObject<T>>();
    for (int index = 0; index < length; ++index)
      this.Values.Add(new IecValueObject<T>()
      {
        Address = (int) address + index,
        OnValueChanged = new Action<IecValueObject<T>>(this.Iec104ValueChanged)
      });
    this.IsAddressContinuous = true;
  }

  /// <summary>地址是否连续</summary>
  public bool IsAddressContinuous { get; private set; }

  /// <summary>类型ID信息</summary>
  public byte TypeID => this._typeID;

  /// <summary>IEC数据对象值</summary>
  public List<IecValueObject<T>> Values { get; private set; }

  /// <summary>当值发生变化的时候</summary>
  public Action<IecValueServerObject<T>, IecValueObject<T>> OnIecValueChanged { get; set; }

  private List<byte[]> GetAsduCommandByList(
    List<IecValueObject<T>> iecValues,
    bool isAddressContinuous,
    byte reason,
    byte station)
  {
    List<byte[]> asduCommandByList = new List<byte[]>();
    int index1 = 0;
    int num1 = this._trans(iecValues[0]).Length + (isAddressContinuous ? 0 : 3);
    if (this._typeID != (byte) 1 && this._typeID != (byte) 3)
      ++num1;
    int num2;
    int num3;
    if (isAddressContinuous)
    {
      for (; index1 < iecValues.Count; index1 += num2)
      {
        MemoryStream ms = new MemoryStream();
        ms.WriteByte(this._typeID);
        int num4 = 128 /*0x80*/;
        int val2 = 240 /*0xF0*/ / num1;
        num2 = Math.Min(iecValues.Count - index1, val2);
        if (num2 > (int) sbyte.MaxValue)
          num2 = (int) sbyte.MaxValue;
        int num5 = num4 | num2;
        ms.WriteByte((byte) num5);
        ms.WriteByte(reason);
        ms.WriteByte((byte) 0);
        ms.WriteByte(station);
        ms.WriteByte((byte) 0);
        ms.Write(BitConverter.GetBytes((ushort) iecValues[index1].Address));
        ms.WriteByte((byte) 0);
        for (int index2 = 0; index2 < num2; ++index2)
        {
          ms.Write(this._trans(iecValues[index2 + index1]));
          if (this._typeID != (byte) 1 && this._typeID != (byte) 3)
            ms.WriteByte(iecValues[index2 + index1].Quality);
        }
        asduCommandByList.Add(ms.ToArray());
      }
    }
    else
    {
      for (; index1 < iecValues.Count; index1 += num3)
      {
        MemoryStream ms = new MemoryStream();
        ms.WriteByte(this._typeID);
        int num6 = 0;
        int val2 = 243 / num1;
        num3 = Math.Min(iecValues.Count - index1, val2);
        if (num3 > (int) sbyte.MaxValue)
          num3 = (int) sbyte.MaxValue;
        int num7 = num6 | num3;
        ms.WriteByte((byte) num7);
        ms.WriteByte(reason);
        ms.WriteByte((byte) 0);
        ms.WriteByte(station);
        ms.WriteByte((byte) 0);
        for (int index3 = 0; index3 < num3; ++index3)
        {
          ms.Write(BitConverter.GetBytes((ushort) iecValues[index3 + index1].Address));
          ms.WriteByte((byte) 0);
          ms.Write(this._trans(iecValues[index3 + index1]));
          if (this._typeID != (byte) 1 && this._typeID != (byte) 3)
            ms.WriteByte(iecValues[index3 + index1].Quality);
        }
        asduCommandByList.Add(ms.ToArray());
      }
    }
    return asduCommandByList;
  }

  /// <summary>获取发送的Asdu报文信息</summary>
  /// <param name="reason">传送原因</param>
  /// <param name="station">公共地址</param>
  /// <returns></returns>
  public List<byte[]> GetAsduCommand(byte reason, byte station)
  {
    return this.GetAsduCommandByList(this.Values, this.IsAddressContinuous, reason, station);
  }

  /// <summary>获取一个突发的数据变化的报文信息</summary>
  /// <param name="iecValue">数据变化值</param>
  /// <param name="station">公共地址</param>
  /// <returns>发送客户端的报文</returns>
  public byte[] GetAsduBreakOut(IecValueObject<T> iecValue, byte station)
  {
    return this.GetAsduCommandByList(new List<IecValueObject<T>>()
    {
      iecValue
    }, false, (byte) 3, station)[0];
  }

  private void Iec104ValueChanged(IecValueObject<T> iecValue)
  {
    Action<IecValueServerObject<T>, IecValueObject<T>> onIecValueChanged = this.OnIecValueChanged;
    if (onIecValueChanged == null)
      return;
    onIecValueChanged(this, iecValue);
  }
}
