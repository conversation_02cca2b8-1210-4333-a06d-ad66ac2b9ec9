﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.UrlDecoder
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Text;

#nullable disable
namespace HslCommunication.BasicFramework;

internal class UrlDecoder
{
  private int _bufferSize;
  private int _numChars;
  private char[] _charBuffer;
  private int _numBytes;
  private byte[] _byteBuffer;
  private Encoding _encoding;

  private void FlushBytes()
  {
    if (this._numBytes <= 0)
      return;
    this._numChars += this._encoding.GetChars(this._byteBuffer, 0, this._numBytes, this._charBuffer, this._numChars);
    this._numBytes = 0;
  }

  internal UrlDecoder(int bufferSize, Encoding encoding)
  {
    this._bufferSize = bufferSize;
    this._encoding = encoding;
    this._charBuffer = new char[bufferSize];
  }

  internal void AddChar(char ch)
  {
    if (this._numBytes > 0)
      this.FlushBytes();
    this._charBuffer[this._numChars++] = ch;
  }

  internal void AddByte(byte b)
  {
    if (this._byteBuffer == null)
      this._byteBuffer = new byte[this._bufferSize];
    this._byteBuffer[this._numBytes++] = b;
  }

  internal string GetString()
  {
    if (this._numBytes > 0)
      this.FlushBytes();
    return this._numChars > 0 ? new string(this._charBuffer, 0, this._numChars) : string.Empty;
  }
}
