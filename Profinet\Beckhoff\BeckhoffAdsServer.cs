﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Beckhoff.BeckhoffAdsServer
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.Beckhoff.Helper;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

#nullable disable
namespace HslCommunication.Profinet.Beckhoff;

/// <summary>倍福Ads协议的虚拟服务器</summary>
public class BeckhoffAdsServer : DeviceServer
{
  private SoftBuffer mBuffer;
  private SoftBuffer iBuffer;
  private SoftBuffer qBuffer;
  private Dictionary<string, AdsTagItem> adsValues;
  private object dicLock = new object();
  private int memoryAddress;
  private const int DataPoolLength = 65536 /*0x010000*/;
  private Timer timer;

  /// <summary>
  /// 实例化一个基于ADS协议的虚拟的倍福PLC对象，可以用来和<see cref="T:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet" />进行通信测试。
  /// </summary>
  public BeckhoffAdsServer()
  {
    this.mBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.iBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.qBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 2;
    this.memoryAddress = 1048576 /*0x100000*/;
    this.adsValues = new Dictionary<string, AdsTagItem>();
    this.timer = new Timer(new TimerCallback(this.ThreadKeepAlive), (object) null, 2000, 2000);
  }

  private void ThreadKeepAlive(object state)
  {
    if (!this.IsStarted)
      return;
    foreach (PipeSession pipeSession in this.GetCommunicationServer().GetPipeSessions())
      pipeSession.Communication.Send(AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.RouterNotification, new byte[8]));
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    byte[] bytes = new byte[196608 /*0x030000*/];
    this.mBuffer.GetBytes().CopyTo((Array) bytes, 0);
    this.iBuffer.GetBytes().CopyTo((Array) bytes, 65536 /*0x010000*/);
    this.qBuffer.GetBytes().CopyTo((Array) bytes, 131072 /*0x020000*/);
    return bytes;
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    if (content.Length < 196608 /*0x030000*/)
      throw new Exception("File is not correct");
    this.mBuffer.SetBytes(content, 0, 65536 /*0x010000*/);
    this.iBuffer.SetBytes(content, 65536 /*0x010000*/, 65536 /*0x010000*/);
    this.qBuffer.SetBytes(content, 131072 /*0x020000*/, 65536 /*0x010000*/);
  }

  private int TransAddressOffset(int indexGroup, int address)
  {
    switch (indexGroup)
    {
      case 61472:
        return address >= 128000 ? address - 128000 : address;
      case 61473:
        return address >= 1024000 /*0x0FA000*/ ? address - 1024000 /*0x0FA000*/ : address;
      case 61488:
        return address >= 256000 ? address - 256000 : address;
      case 61489:
        return address >= 2048000 ? address - 2048000 : address;
      default:
        return address;
    }
  }

  private OperateResult<byte[]> TagsAction(
    string tagName,
    Func<AdsTagItem, int, OperateResult<byte[]>> action)
  {
    lock (this.dicLock)
    {
      if (this.adsValues.ContainsKey(tagName))
        return action(this.adsValues[tagName], 0);
      if (Regex.IsMatch(tagName, "\\[[0-9]+\\]$"))
      {
        int length = tagName.LastIndexOf('[');
        int num1 = int.Parse(tagName.Substring(length + 1).RemoveLast(1));
        tagName = tagName.Substring(0, length);
        if (this.adsValues.ContainsKey(tagName))
        {
          int num2 = num1 * this.adsValues[tagName].TypeLength;
          return num2 >= this.adsValues[tagName].Buffer.Length ? new OperateResult<byte[]>(StringResources.Language.AllenBradley04) : action(this.adsValues[tagName], num2);
        }
      }
      return new OperateResult<byte[]>(StringResources.Language.AllenBradley04);
    }
  }

  private OperateResult<byte[]> ReadTags(string tagName, int length)
  {
    return this.TagsAction(tagName, (Func<AdsTagItem, int, OperateResult<byte[]>>) ((tag, index) => OperateResult.CreateSuccessResult<byte[]>(tag.Buffer.SelectMiddle<byte>(index, Math.Min(length, tag.Buffer.Length - index)))));
  }

  private OperateResult WriteTags(string tagName, byte[] value)
  {
    return (OperateResult) this.TagsAction(tagName, (Func<AdsTagItem, int, OperateResult<byte[]>>) ((tag, index) =>
    {
      Array.Copy((Array) value, 0, (Array) tag.Buffer, index, Math.Min(value.Length, tag.Buffer.Length - index));
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    }));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<uint, uint> operateResult = AdsHelper.AnalysisAddress(address, false);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<byte[]>();
    operateResult.Content2 = (uint) this.TransAddressOffset((int) operateResult.Content1, (int) operateResult.Content2);
    if (operateResult.Content1 == 61443U)
      return this.ReadTags(address.Substring(2), (int) length);
    switch (operateResult.Content1)
    {
      case 16416:
        return OperateResult.CreateSuccessResult<byte[]>(this.mBuffer.GetBytes((int) operateResult.Content2, (int) length));
      case 61472:
        return OperateResult.CreateSuccessResult<byte[]>(this.iBuffer.GetBytes((int) operateResult.Content2, (int) length));
      case 61488:
        return OperateResult.CreateSuccessResult<byte[]>(this.qBuffer.GetBytes((int) operateResult.Content2, (int) length));
      default:
        return new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Beckhoff.BeckhoffAdsNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<uint, uint> operateResult = AdsHelper.AnalysisAddress(address, false);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult.ConvertFailed<byte[]>();
    operateResult.Content2 = (uint) this.TransAddressOffset((int) operateResult.Content1, (int) operateResult.Content2);
    if (operateResult.Content1 == 61443U)
      return this.WriteTags(address.Substring(2), value);
    switch (operateResult.Content1)
    {
      case 16416:
        this.mBuffer.SetBytes(value, (int) operateResult.Content2);
        break;
      case 61472:
        this.iBuffer.SetBytes(value, (int) operateResult.Content2);
        break;
      case 61488:
        this.qBuffer.SetBytes(value, (int) operateResult.Content2);
        break;
      default:
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<uint, uint> operateResult = AdsHelper.AnalysisAddress(address, true);
    if (!operateResult.IsSuccess)
      return operateResult.ConvertFailed<bool[]>();
    operateResult.Content2 = (uint) this.TransAddressOffset((int) operateResult.Content1, (int) operateResult.Content2);
    if (operateResult.Content1 == 61443U)
    {
      OperateResult<byte[]> result = this.ReadTags(address.Substring(2), (int) length);
      return !result.IsSuccess ? OperateResult.CreateFailedResult<bool[]>((OperateResult) result) : OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) result.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
    }
    switch (operateResult.Content1)
    {
      case 16417:
        return OperateResult.CreateSuccessResult<bool[]>(this.mBuffer.GetBool((int) operateResult.Content2, (int) length));
      case 61473:
        return OperateResult.CreateSuccessResult<bool[]>(this.iBuffer.GetBool((int) operateResult.Content2, (int) length));
      case 61489:
        return OperateResult.CreateSuccessResult<bool[]>(this.qBuffer.GetBool((int) operateResult.Content2, (int) length));
      default:
        return new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IReadWriteNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<uint, uint> operateResult = AdsHelper.AnalysisAddress(address, true);
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult.ConvertFailed<bool[]>();
    operateResult.Content2 = (uint) this.TransAddressOffset((int) operateResult.Content1, (int) operateResult.Content2);
    if (operateResult.Content1 == 61443U)
      return this.WriteTags(address.Substring(2), ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>());
    switch (operateResult.Content1)
    {
      case 16417:
        this.mBuffer.SetBool(value, (int) operateResult.Content2);
        break;
      case 61473:
        this.iBuffer.SetBool(value, (int) operateResult.Content2);
        break;
      case 61489:
        this.qBuffer.SetBool(value, (int) operateResult.Content2);
        break;
      default:
        return new OperateResult(StringResources.Language.NotSupportedDataType);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new AdsNetMessage();

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    return OperateResult.CreateSuccessResult<byte[]>(this.ReadFromAdsCore(session, receive));
  }

  private byte[] PackCommand(byte[] cmd, int err, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    byte[] numArray1 = new byte[32 /*0x20*/ + data.Length];
    Array.Copy((Array) cmd, 0, (Array) numArray1, 0, 32 /*0x20*/);
    byte[] numArray2 = numArray1.SelectBegin<byte>(8);
    byte[] numArray3 = numArray1.SelectMiddle<byte>(8, 8);
    numArray2.CopyTo((Array) numArray1, 8);
    numArray3.CopyTo((Array) numArray1, 0);
    numArray1[18] = (byte) 5;
    numArray1[19] = (byte) 0;
    BitConverter.GetBytes(data.Length).CopyTo((Array) numArray1, 20);
    BitConverter.GetBytes(err).CopyTo((Array) numArray1, 24);
    numArray1[11] = (byte) 0;
    if (data.Length != 0)
      data.CopyTo((Array) numArray1, 32 /*0x20*/);
    return AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.Command, numArray1);
  }

  private byte[] PackDataResponse(int err, byte[] data)
  {
    if (data == null)
      return BitConverter.GetBytes(err);
    byte[] numArray = new byte[8 + data.Length];
    BitConverter.GetBytes(err).CopyTo((Array) numArray, 0);
    BitConverter.GetBytes(data.Length).CopyTo((Array) numArray, 4);
    if (data.Length != 0)
      data.CopyTo((Array) numArray, 8);
    return numArray;
  }

  private byte[] ReadFromAdsCore(PipeSession session, byte[] receive)
  {
    switch ((AmsTcpHeaderFlags) BitConverter.ToUInt16(receive, 0))
    {
      case AmsTcpHeaderFlags.Command:
        receive = receive.RemoveBegin<byte>(6);
        if (session.Tag == null)
        {
          session.Tag = (object) 1;
          this.LogNet?.WriteDebug(this.ToString(), $"TargetId:{AdsHelper.GetAmsNetIdString(receive, 0)} SenderId:{AdsHelper.GetAmsNetIdString(receive, 8)}");
        }
        switch (BitConverter.ToInt16(receive, 16 /*0x10*/))
        {
          case 2:
            return this.ReadByCommand(receive);
          case 3:
            return this.WriteByCommand(receive);
          case 9:
            return this.ReadWriteByCommand(receive);
          default:
            return this.PackCommand(receive, 32 /*0x20*/, (byte[]) null);
        }
      case AmsTcpHeaderFlags.PortConnect:
        return AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.PortConnect, AdsHelper.StrToAMSNetId("*************.1.1:32957"));
      case AmsTcpHeaderFlags.GetLocalNetId:
        return AdsHelper.PackAmsTcpHelper(AmsTcpHeaderFlags.GetLocalNetId, AdsHelper.StrToAMSNetId("*************.1.1"));
      default:
        this.LogNet?.WriteDebug(this.ToString(), "Unknown Source: " + receive.ToHexString(' '));
        return (byte[]) null;
    }
  }

  private OperateResult<byte[]> ReadByCommand(
    byte[] command,
    int indexGroup,
    int address,
    int length)
  {
    address = this.TransAddressOffset(indexGroup, address);
    switch (indexGroup)
    {
      case 16416:
        return OperateResult.CreateSuccessResult<byte[]>(this.mBuffer.GetBytes(address, length));
      case 16417:
        return OperateResult.CreateSuccessResult<byte[]>(((IEnumerable<bool>) this.mBuffer.GetBool(address, length)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>());
      case 61445:
        uint num = (uint) address;
        lock (this.dicLock)
        {
          foreach (AdsTagItem adsTagItem in this.adsValues.Values)
          {
            if ((int) adsTagItem.Location == (int) num)
              return OperateResult.CreateSuccessResult<byte[]>(adsTagItem.Buffer.SelectBegin<byte>(Math.Min(length, adsTagItem.Buffer.Length)));
          }
          return new OperateResult<byte[]>()
          {
            Content = this.PackCommand(command, 0, this.PackDataResponse(1808, (byte[]) null))
          };
        }
      case 61472:
        return OperateResult.CreateSuccessResult<byte[]>(this.iBuffer.GetBytes(address, length));
      case 61473:
        return OperateResult.CreateSuccessResult<byte[]>(((IEnumerable<bool>) this.iBuffer.GetBool(address, length)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>());
      case 61488:
        return OperateResult.CreateSuccessResult<byte[]>(this.qBuffer.GetBytes(address, length));
      case 61489:
        return OperateResult.CreateSuccessResult<byte[]>(((IEnumerable<bool>) this.qBuffer.GetBool(address, length)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>());
      default:
        return new OperateResult<byte[]>()
        {
          Content = this.PackCommand(command, 64 /*0x40*/, (byte[]) null)
        };
    }
  }

  private byte[] ReadByCommand(byte[] command)
  {
    try
    {
      int int32_1 = BitConverter.ToInt32(command, 32 /*0x20*/);
      int int32_2 = BitConverter.ToInt32(command, 36);
      int int32_3 = BitConverter.ToInt32(command, 40);
      OperateResult<byte[]> operateResult = this.ReadByCommand(command, int32_1, int32_2, int32_3);
      return !operateResult.IsSuccess ? operateResult.Content : this.PackCommand(command, 0, this.PackDataResponse(0, operateResult.Content));
    }
    catch
    {
      return this.PackCommand(command, 164, (byte[]) null);
    }
  }

  private byte[] WriteByCommand(byte[] command)
  {
    if (!this.EnableWrite)
      return this.PackCommand(command, 16 /*0x10*/, (byte[]) null);
    try
    {
      int int32_1 = BitConverter.ToInt32(command, 32 /*0x20*/);
      int int32_2 = BitConverter.ToInt32(command, 36);
      BitConverter.ToInt32(command, 40);
      byte[] numArray = command.RemoveBegin<byte>(44);
      int destIndex = this.TransAddressOffset(int32_1, int32_2);
      switch (int32_1)
      {
        case 16416:
          this.mBuffer.SetBytes(numArray, destIndex);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 16417:
          this.mBuffer.SetBool(((IEnumerable<byte>) numArray).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>(), destIndex);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61445:
          uint num = (uint) destIndex;
          lock (this.dicLock)
          {
            foreach (AdsTagItem adsTagItem in this.adsValues.Values)
            {
              if ((int) adsTagItem.Location == (int) num)
              {
                Array.Copy((Array) numArray, 0, (Array) adsTagItem.Buffer, 0, Math.Min(numArray.Length, adsTagItem.Buffer.Length));
                return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
              }
            }
            return this.PackCommand(command, 0, this.PackDataResponse(1808, (byte[]) null));
          }
        case 61472:
          this.iBuffer.SetBytes(numArray, destIndex);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61473:
          this.iBuffer.SetBool(((IEnumerable<byte>) numArray).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>(), destIndex);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61488:
          this.qBuffer.SetBytes(numArray, destIndex);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61489:
          this.qBuffer.SetBool(((IEnumerable<byte>) numArray).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>(), destIndex);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        default:
          return this.PackCommand(command, 64 /*0x40*/, (byte[]) null);
      }
    }
    catch
    {
      return this.PackCommand(command, 164, (byte[]) null);
    }
  }

  private byte[] ReadWriteByCommand(byte[] command)
  {
    try
    {
      int int32_1 = BitConverter.ToInt32(command, 32 /*0x20*/);
      int int32_2 = BitConverter.ToInt32(command, 36);
      BitConverter.ToInt32(command, 40);
      BitConverter.ToInt32(command, 44);
      byte[] numArray = command.RemoveBegin<byte>(48 /*0x30*/);
      switch (int32_1)
      {
        case 16416:
          this.mBuffer.SetBytes(numArray, int32_2);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 16417:
          this.mBuffer.SetBytes(numArray, int32_2);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61443:
          if (numArray[numArray.Length - 1] == (byte) 0)
            numArray = numArray.RemoveLast<byte>(1);
          string key = Encoding.ASCII.GetString(numArray);
          lock (this.dicLock)
            return this.adsValues.ContainsKey(key) ? this.PackCommand(command, 0, this.PackDataResponse(0, BitConverter.GetBytes(this.adsValues[key].Location))) : this.PackCommand(command, 0, this.PackDataResponse(1808, (byte[]) null));
        case 61472:
          this.iBuffer.SetBytes(numArray, int32_2);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61473:
          this.iBuffer.SetBytes(numArray, int32_2);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61488:
          this.qBuffer.SetBytes(numArray, int32_2);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61489:
          this.qBuffer.SetBytes(numArray, int32_2);
          return this.PackCommand(command, 0, this.PackDataResponse(0, (byte[]) null));
        case 61568:
          List<byte> byteList = new List<byte>();
          for (int index = 0; index < numArray.Length / 12; ++index)
          {
            int int32_3 = BitConverter.ToInt32(numArray, 12 * index);
            int int32_4 = BitConverter.ToInt32(numArray, 12 * index + 4);
            int int32_5 = BitConverter.ToInt32(numArray, 12 * index + 8);
            OperateResult<byte[]> operateResult = this.ReadByCommand(command, int32_3, int32_4, int32_5);
            if (!operateResult.IsSuccess)
              return operateResult.Content;
            byteList.AddRange((IEnumerable<byte>) operateResult.Content);
          }
          return this.PackCommand(command, 0, this.PackDataResponse(0, byteList.ToArray()));
        default:
          return this.PackCommand(command, 64 /*0x40*/, (byte[]) null);
      }
    }
    catch
    {
      return this.PackCommand(command, 164, (byte[]) null);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,HslCommunication.Profinet.AllenBradley.AllenBradleyItemValue)" />
  public void AddTagValue(string key, AdsTagItem value)
  {
    value.Location = (uint) Interlocked.Increment(ref this.memoryAddress);
    lock (this.dicLock)
    {
      if (this.adsValues.ContainsKey(key))
        this.adsValues[key] = value;
      else
        this.adsValues.Add(key, value);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,HslCommunication.Profinet.AllenBradley.AllenBradleyItemValue)" />
  public void AddTagValue(string key, bool value)
  {
    string key1 = key;
    string name = key;
    byte[] buffer;
    if (!value)
      buffer = new byte[1];
    else
      buffer = new byte[1]{ (byte) 1 };
    AdsTagItem adsTagItem = new AdsTagItem(name, buffer, 1);
    this.AddTagValue(key1, adsTagItem);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,HslCommunication.Profinet.AllenBradley.AllenBradleyItemValue)" />
  public void AddTagValue(string key, bool[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, ((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Int16)" />
  public void AddTagValue(string key, short value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 2));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Int16[])" />
  public void AddTagValue(string key, short[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 2));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.UInt16)" />
  public void AddTagValue(string key, ushort value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 2));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.UInt16[])" />
  public void AddTagValue(string key, ushort[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 2));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Int32)" />
  public void AddTagValue(string key, int value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 4));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Int32[])" />
  public void AddTagValue(string key, int[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 4));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.UInt32)" />
  public void AddTagValue(string key, uint value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 4));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.UInt32[])" />
  public void AddTagValue(string key, uint[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 4));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Int64)" />
  public void AddTagValue(string key, long value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 8));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Int64[])" />
  public void AddTagValue(string key, long[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 8));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.UInt64)" />
  public void AddTagValue(string key, ulong value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 8));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.UInt64[])" />
  public void AddTagValue(string key, ulong[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 8));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Single)" />
  public void AddTagValue(string key, float value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 4));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Single[])" />
  public void AddTagValue(string key, float[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 4));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Double)" />
  public void AddTagValue(string key, double value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 8));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.Double[])" />
  public void AddTagValue(string key, double[] value)
  {
    this.AddTagValue(key, new AdsTagItem(key, this.ByteTransform.TransByte(value), 8));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.AllenBradley.AllenBradleyServer.AddTagValue(System.String,System.String,System.Int32)" />
  public void AddTagValue(string key, string value, int maxLength)
  {
    byte[] length = SoftBasic.ArrayExpandToLength<byte>(Encoding.UTF8.GetBytes(value), maxLength);
    this.AddTagValue(key, new AdsTagItem(key, length, maxLength));
  }

  /// <inheritdoc />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.mBuffer.Dispose();
      this.iBuffer.Dispose();
      this.qBuffer.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"BeckhoffAdsServer[{this.Port}]";
}
