﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.LSIS.LSCnet
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.LSIS.Helper;
using HslCommunication.Reflection;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.LSIS;

/// <summary>
/// XGB Cnet I/F module supports Serial Port. The address can carry station number information, for example: s=2;D100
/// </summary>
/// <remarks>
/// XGB 主机的通道 0 仅支持 1:1 通信。 对于具有主从格式的 1:N 系统，在连接 XGL-C41A 模块的通道 1 或 XGB 主机中使用 RS-485 通信。 XGL-C41A 模块支持 RS-422/485 协议。
/// </remarks>
public class LSCnet : DeviceSerialPort, IReadWriteDeviceStation, IReadWriteDevice, IReadWriteNet
{
  /// <summary>Instantiate a Default object</summary>
  public LSCnet()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 2;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc />
  public override OperateResult<byte[]> UnpackResponseContent(byte[] send, byte[] response)
  {
    return LSCnetHelper.UnpackResponseContent(send, response);
  }

  /// <inheritdoc cref="P:HslCommunication.Core.Net.IReadWriteDeviceStation.Station" />
  public byte Station { get; set; } = 5;

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.ReadByte(System.String)" />
  [HslMqttApi("ReadByte", "")]
  public OperateResult<byte> ReadByte(string address)
  {
    return ByteTransformHelper.GetResultFromArray<byte>(this.Read(address, (ushort) 1));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.Write(System.String,System.Byte)" />
  [HslMqttApi("WriteByte", "")]
  public OperateResult Write(string address, byte value)
  {
    return this.Write(address, new byte[1]{ value });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String)" />
  [HslMqttApi("ReadBool", "")]
  public override OperateResult<bool> ReadBool(string address)
  {
    return LSCnetHelper.ReadBool((IReadWriteDevice) this, (int) this.Station, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.ReadBool(HslCommunication.Core.Net.IReadWriteDeviceStation,System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return LSCnetHelper.ReadBool((IReadWriteDeviceStation) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.ReadCoil(System.String)" />
  public OperateResult<bool> ReadCoil(string address) => this.ReadBool(address);

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.ReadCoil(System.String,System.UInt16)" />
  public OperateResult<bool[]> ReadCoil(string address, ushort length)
  {
    return this.ReadBool(address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnetOverTcp.WriteCoil(System.String,System.Boolean)" />
  public OperateResult WriteCoil(string address, bool value) => this.Write(address, value);

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Boolean)" />
  [HslMqttApi("WriteBool", "")]
  public override OperateResult Write(string address, bool value)
  {
    return LSCnetHelper.Write((IReadWriteDevice) this, (int) this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.ReadBool(System.String)" />
  public override async Task<OperateResult<bool>> ReadBoolAsync(string address)
  {
    OperateResult<bool> operateResult = await LSCnetHelper.ReadBoolAsync((IReadWriteDevice) this, (int) this.Station, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.ReadBool(System.String,System.UInt16)" />
  public override async Task<OperateResult<bool[]>> ReadBoolAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await LSCnetHelper.ReadBoolAsync((IReadWriteDeviceStation) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.ReadCoil(System.String)" />
  public async Task<OperateResult<bool>> ReadCoilAsync(string address)
  {
    OperateResult<bool> operateResult = await this.ReadBoolAsync(address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.ReadCoil(System.String,System.UInt16)" />
  public async Task<OperateResult<bool[]>> ReadCoilAsync(string address, ushort length)
  {
    OperateResult<bool[]> operateResult = await this.ReadBoolAsync(address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.WriteCoil(System.String,System.Boolean)" />
  public async Task<OperateResult> WriteCoilAsync(string address, bool value)
  {
    OperateResult operateResult = await this.WriteAsync(address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.Write(System.String,System.Boolean)" />
  public override async Task<OperateResult> WriteAsync(string address, bool value)
  {
    OperateResult operateResult = await LSCnetHelper.WriteAsync((IReadWriteDevice) this, (int) this.Station, address, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Read(HslCommunication.Core.Net.IReadWriteDeviceStation,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return LSCnetHelper.Read((IReadWriteDeviceStation) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String[])" />
  public OperateResult<byte[]> Read(string[] address)
  {
    return LSCnetHelper.Read((IReadWriteDevice) this, (int) this.Station, address);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.Helper.LSCnetHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Int32,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return LSCnetHelper.Write((IReadWriteDevice) this, (int) this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.Read(System.String,System.UInt16)" />
  public override async Task<OperateResult<byte[]>> ReadAsync(string address, ushort length)
  {
    OperateResult<byte[]> operateResult = await LSCnetHelper.ReadAsync((IReadWriteDeviceStation) this, address, length);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.Read(System.String[])" />
  public async Task<OperateResult<byte[]>> ReadAsync(string[] address)
  {
    OperateResult<byte[]> operateResult = await LSCnetHelper.ReadAsync((IReadWriteDevice) this, (int) this.Station, address);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.LSIS.LSCnet.Write(System.String,System.Byte[])" />
  public override async Task<OperateResult> WriteAsync(string address, byte[] value)
  {
    OperateResult operateResult = await LSCnetHelper.WriteAsync((IReadWriteDevice) this, (int) this.Station, address, value);
    return operateResult;
  }

  /// <inheritdoc />
  public override string ToString() => $"LSCnet[{this.PortName}:{this.BaudRate}]";
}
