﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.CJT.Helper.CJTControl
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Instrument.CJT.Helper;

/// <summary>控制码信息</summary>
public class CJTControl
{
  /// <summary>读数据</summary>
  public const byte ReadData = 1;
  /// <summary>写数据</summary>
  public const byte WriteData = 4;
  /// <summary>读地址</summary>
  public const byte ReadAddress = 3;
  /// <summary>写地址</summary>
  public const byte WriteAddress = 21;
}
