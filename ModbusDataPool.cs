﻿// Decompiled with JetBrains decompiler
// Type: ModbusDataPool
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication;
using HslCommunication.BasicFramework;
using HslCommunication.Core.Address;
using HslCommunication.ModBus;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
internal class ModbusDataPool : IDisposable
{
  private byte station = 1;
  private SoftBuffer coilBuffer;
  private SoftBuffer inputBuffer;
  private SoftBuffer registerBuffer;
  private SoftBuffer inputRegisterBuffer;
  private const int DataPoolLength = 65536 /*0x010000*/;

  public ModbusDataPool(byte station)
  {
    this.coilBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.inputBuffer = new SoftBuffer(65536 /*0x010000*/);
    this.registerBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.inputRegisterBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.registerBuffer.IsBoolReverseByWord = true;
    this.inputRegisterBuffer.IsBoolReverseByWord = true;
    this.station = station;
  }

  public OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<ModbusAddress> result = ModbusInfo.AnalysisAddress(address, this.station, true, (byte) 3);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (result.Content.Function == 3)
      return OperateResult.CreateSuccessResult<byte[]>(this.registerBuffer.GetBytes(result.Content.AddressStart * 2, (int) length * 2));
    return result.Content.Function == 4 ? OperateResult.CreateSuccessResult<byte[]>(this.inputRegisterBuffer.GetBytes(result.Content.AddressStart * 2, (int) length * 2)) : new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
  }

  public OperateResult Write(string address, byte[] value)
  {
    OperateResult<ModbusAddress> result = ModbusInfo.AnalysisAddress(address, this.station, true, (byte) 3);
    if (!result.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    if (result.Content.Function == 3 || result.Content.Function == 6 || result.Content.Function == 16 /*0x10*/)
    {
      this.registerBuffer.SetBytes(value, result.Content.AddressStart * 2);
      return OperateResult.CreateSuccessResult();
    }
    if (result.Content.Function != 4)
      return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
    this.inputRegisterBuffer.SetBytes(value, result.Content.AddressStart * 2);
    return OperateResult.CreateSuccessResult();
  }

  public OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    if (address.IndexOf('.') < 0)
    {
      OperateResult<ModbusAddress> result = ModbusInfo.AnalysisAddress(address, this.station, true, (byte) 1);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
      if (result.Content.Function == 1 || result.Content.Function == 5 || result.Content.Function == 15)
        return OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.coilBuffer.GetBytes(result.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>());
      return result.Content.Function == 2 ? OperateResult.CreateSuccessResult<bool[]>(((IEnumerable<byte>) this.inputBuffer.GetBytes(result.Content.AddressStart, (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>()) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
    }
    try
    {
      int int32 = Convert.ToInt32(address.Substring(address.IndexOf('.') + 1));
      address = address.Substring(0, address.IndexOf('.'));
      OperateResult<ModbusAddress> result = ModbusInfo.AnalysisAddress(address, this.station, true, (byte) 3);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
      int destIndex = result.Content.AddressStart * 16 /*0x10*/ + int32;
      if (result.Content.Function == 3)
        return OperateResult.CreateSuccessResult<bool[]>(this.registerBuffer.GetBool(destIndex, (int) length));
      return result.Content.Function == 4 ? OperateResult.CreateSuccessResult<bool[]>(this.inputRegisterBuffer.GetBool(destIndex, (int) length)) : new OperateResult<bool[]>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<bool[]>(ex.Message);
    }
  }

  public OperateResult Write(string address, bool[] value)
  {
    if (address.IndexOf('.') < 0)
    {
      OperateResult<ModbusAddress> result = ModbusInfo.AnalysisAddress(address, this.station, true, (byte) 1);
      if (!result.IsSuccess)
        return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
      if (result.Content.Function == 1 || result.Content.Function == 15 || result.Content.Function == 5)
      {
        this.coilBuffer.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => m ? (byte) 1 : (byte) 0)).ToArray<byte>(), result.Content.AddressStart);
        return OperateResult.CreateSuccessResult();
      }
      if (result.Content.Function != 2)
        return (OperateResult) new OperateResult<byte[]>(StringResources.Language.NotSupportedDataType);
      this.inputBuffer.SetBytes(((IEnumerable<bool>) value).Select<bool, byte>((Func<bool, byte>) (m => m ? (byte) 1 : (byte) 0)).ToArray<byte>(), result.Content.AddressStart);
      return OperateResult.CreateSuccessResult();
    }
    try
    {
      int int32 = Convert.ToInt32(address.Substring(address.IndexOf('.') + 1));
      address = address.Substring(0, address.IndexOf('.'));
      OperateResult<ModbusAddress> operateResult = ModbusInfo.AnalysisAddress(address, this.station, true, (byte) 3);
      if (!operateResult.IsSuccess)
        return (OperateResult) operateResult;
      int destIndex = operateResult.Content.AddressStart * 16 /*0x10*/ + int32;
      if (operateResult.Content.Function == 3)
      {
        this.registerBuffer.SetBool(value, destIndex);
        return OperateResult.CreateSuccessResult();
      }
      if (operateResult.Content.Function != 4)
        return new OperateResult(StringResources.Language.NotSupportedDataType);
      this.inputRegisterBuffer.SetBool(value, destIndex);
      return OperateResult.CreateSuccessResult();
    }
    catch (Exception ex)
    {
      return new OperateResult(ex.Message);
    }
  }

  /// <summary>读取地址的线圈的通断情况</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <returns><c>True</c>或是<c>False</c></returns>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public bool ReadCoil(string address)
  {
    return this.coilBuffer.GetByte((int) ushort.Parse(address)) > (byte) 0;
  }

  /// <summary>批量读取地址的线圈的通断情况</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <param name="length">读取长度</param>
  /// <returns><c>True</c>或是<c>False</c></returns>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public bool[] ReadCoil(string address, ushort length)
  {
    return ((IEnumerable<byte>) this.coilBuffer.GetBytes((int) ushort.Parse(address), (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>();
  }

  /// <summary>写入线圈的通断值</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <param name="data">是否通断</param>
  /// <returns><c>True</c>或是<c>False</c></returns>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public void WriteCoil(string address, bool data)
  {
    ushort index = ushort.Parse(address);
    this.coilBuffer.SetValue(data ? (byte) 1 : (byte) 0, (int) index);
  }

  /// <summary>写入线圈数组的通断值</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <param name="data">是否通断</param>
  /// <returns><c>True</c>或是<c>False</c></returns>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public void WriteCoil(string address, bool[] data)
  {
    if (data == null)
      return;
    ushort destIndex = ushort.Parse(address);
    this.coilBuffer.SetBytes(((IEnumerable<bool>) data).Select<bool, byte>((Func<bool, byte>) (m => m ? (byte) 1 : (byte) 0)).ToArray<byte>(), (int) destIndex);
  }

  /// <summary>读取地址的离散线圈的通断情况</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <returns><c>True</c>或是<c>False</c></returns>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public bool ReadDiscrete(string address)
  {
    return this.inputBuffer.GetByte((int) ushort.Parse(address)) > (byte) 0;
  }

  /// <summary>批量读取地址的离散线圈的通断情况</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <param name="length">读取长度</param>
  /// <returns><c>True</c>或是<c>False</c></returns>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public bool[] ReadDiscrete(string address, ushort length)
  {
    return ((IEnumerable<byte>) this.inputBuffer.GetBytes((int) ushort.Parse(address), (int) length)).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>();
  }

  /// <summary>写入离散线圈的通断值</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <param name="data">是否通断</param>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public void WriteDiscrete(string address, bool data)
  {
    ushort index = ushort.Parse(address);
    this.inputBuffer.SetValue(data ? (byte) 1 : (byte) 0, (int) index);
  }

  /// <summary>写入离散线圈数组的通断值</summary>
  /// <param name="address">起始地址，示例："100"</param>
  /// <param name="data">是否通断</param>
  /// <exception cref="T:System.IndexOutOfRangeException"></exception>
  public void WriteDiscrete(string address, bool[] data)
  {
    if (data == null)
      return;
    ushort destIndex = ushort.Parse(address);
    this.inputBuffer.SetBytes(((IEnumerable<bool>) data).Select<bool, byte>((Func<bool, byte>) (m => m ? (byte) 1 : (byte) 0)).ToArray<byte>(), (int) destIndex);
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpServer.SaveToBytes" />
  public byte[] SaveToBytes()
  {
    byte[] destinationArray = new byte[393216 /*0x060000*/];
    Array.Copy((Array) this.coilBuffer.GetBytes(), 0, (Array) destinationArray, 0, 65536 /*0x010000*/);
    Array.Copy((Array) this.inputBuffer.GetBytes(), 0, (Array) destinationArray, 65536 /*0x010000*/, 65536 /*0x010000*/);
    Array.Copy((Array) this.registerBuffer.GetBytes(), 0, (Array) destinationArray, 131072 /*0x020000*/, 131072 /*0x020000*/);
    Array.Copy((Array) this.inputRegisterBuffer.GetBytes(), 0, (Array) destinationArray, 262144 /*0x040000*/, 131072 /*0x020000*/);
    return destinationArray;
  }

  /// <inheritdoc cref="M:HslCommunication.ModBus.ModbusTcpServer.LoadFromBytes(System.Byte[])" />
  public void LoadFromBytes(byte[] content, int index)
  {
    if (content.Length < 393216 /*0x060000*/)
      throw new Exception("File is not correct");
    this.coilBuffer.SetBytes(content, index, 0, 65536 /*0x010000*/);
    this.inputBuffer.SetBytes(content, 65536 /*0x010000*/ + index, 0, 65536 /*0x010000*/);
    this.registerBuffer.SetBytes(content, 131072 /*0x020000*/ + index, 0, 131072 /*0x020000*/);
    this.inputRegisterBuffer.SetBytes(content, 262144 /*0x040000*/ + index, 0, 131072 /*0x020000*/);
  }

  /// <inheritdoc />
  public void Dispose()
  {
    this.coilBuffer?.Dispose();
    this.inputBuffer?.Dispose();
    this.registerBuffer?.Dispose();
    this.inputRegisterBuffer?.Dispose();
  }
}
