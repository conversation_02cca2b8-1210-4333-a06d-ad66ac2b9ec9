﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.S7PlusStream
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System.IO;
using System.Net.Sockets;

#nullable disable
namespace HslCommunication.Profinet.Siemens;

/// <summary>S7-Plus协议专用的数据流</summary>
internal class S7PlusStream : NetworkStream
{
  private SiemensS7Plus siemens;
  private MemoryStream ms = new MemoryStream();
  private Socket socket;

  /// <summary>
  /// 使用指定的<see cref="T:System.Net.Sockets.Socket" />对象，是否所属权来实例化一个对象
  /// </summary>
  /// <param name="socket">套接字对象</param>
  /// <param name="ownsSocket">是否所属权</param>
  /// <param name="siemens">当前的PLC对象</param>
  public S7PlusStream(Socket socket, bool ownsSocket, SiemensS7Plus siemens)
    : base(socket, ownsSocket)
  {
    this.socket = socket;
    this.siemens = siemens;
  }

  /// <inheritdoc />
  public override void Write(byte[] buffer, int offset, int size)
  {
    if (buffer[0] == (byte) 22 && buffer[5] == (byte) 1)
      buffer[2] = (byte) 1;
    byte[] buffer1 = offset != 0 || buffer.Length != size ? SiemensS7Plus.BuildWithTPKTAndISO(buffer.SelectMiddle<byte>(offset, size)) : SiemensS7Plus.BuildWithTPKTAndISO(buffer);
    if (buffer1 == null)
      return;
    base.Write(buffer1, 0, buffer1.Length);
  }

  /// <inheritdoc />
  public override int Read(byte[] buffer, int offset, int size)
  {
    if (this.ms.Position >= this.ms.Length)
    {
      byte[] numArray = NetSupport.ReadBytesFromSocket(this.socket, 7);
      int num = (int) numArray[2] * 256 /*0x0100*/ + (int) numArray[3];
      this.ms = new MemoryStream(num);
      if (num > 0)
        this.ms.Write(NetSupport.ReadBytesFromSocket(this.socket, num), 0, num);
    }
    return this.ms.Read(buffer, offset, size);
  }
}
