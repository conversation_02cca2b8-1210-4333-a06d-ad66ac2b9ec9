﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.FATEK.Helper.IFatekProgram
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Profinet.FATEK.Helper;

/// <summary>FatekProgram协议的接口</summary>
public interface IFatekProgram : IReadWriteNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Run(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  OperateResult Run(byte station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.IFatekProgram.Run(System.Byte)" />
  OperateResult Run();

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.Stop(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  OperateResult Stop(byte station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.IFatekProgram.Stop(System.Byte)" />
  OperateResult Stop();

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.FatekProgramHelper.ReadStatus(HslCommunication.Core.IReadWriteDevice,System.Byte)" />
  OperateResult<bool[]> ReadStatus(byte station);

  /// <inheritdoc cref="M:HslCommunication.Profinet.FATEK.Helper.IFatekProgram.ReadStatus(System.Byte)" />
  OperateResult<bool[]> ReadStatus();
}
