﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronFinsNetHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.Omron.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// Omron PLC的FINS协议相关的辅助类，主要是一些地址解析，读写的指令生成。<br />
/// The auxiliary classes related to the FINS protocol of Omron PLC are mainly some address resolution and the generation of read and write instructions.
/// </summary>
public class OmronFinsNetHelper
{
  /// <summary>同时读取多个地址的命令报文信息</summary>
  /// <param name="address">多个地址</param>
  /// <param name="plcType">PLC的类型信息</param>
  /// <returns>命令报文信息</returns>
  public static OperateResult<List<byte[]>> BuildReadCommand(string[] address, OmronPlcType plcType)
  {
    List<byte[]> numArrayList = new List<byte[]>();
    List<string[]> strArrayList = SoftBasic.ArraySplitByLength<string>(address, 89);
    for (int index1 = 0; index1 < strArrayList.Count; ++index1)
    {
      string[] strArray = strArrayList[index1];
      byte[] numArray = new byte[2 + 4 * strArray.Length];
      numArray[0] = (byte) 1;
      numArray[1] = (byte) 4;
      for (int index2 = 0; index2 < strArray.Length; ++index2)
      {
        OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(strArray[index2], (ushort) 1, plcType);
        if (!from.IsSuccess)
          return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from);
        numArray[2 + 4 * index2] = from.Content.WordCode;
        numArray[3 + 4 * index2] = (byte) (from.Content.AddressStart / 16 /*0x10*/ / 256 /*0x0100*/);
        numArray[4 + 4 * index2] = (byte) (from.Content.AddressStart / 16 /*0x10*/ % 256 /*0x0100*/);
        numArray[5 + 4 * index2] = (byte) (from.Content.AddressStart % 16 /*0x10*/);
      }
      numArrayList.Add(numArray);
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <summary>
  /// 根据读取的地址，长度，是否位读取创建Fins协议的核心报文<br />
  /// According to the read address, length, whether to read the core message that creates the Fins protocol
  /// </summary>
  /// <param name="plcType">PLC的类型信息</param>
  /// <param name="address">地址，具体格式请参照示例说明</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="isBit">是否使用位读取</param>
  /// <param name="splitLength">读取的长度切割，默认500</param>
  /// <returns>带有成功标识的Fins核心报文</returns>
  public static OperateResult<List<byte[]>> BuildReadCommand(
    OmronPlcType plcType,
    string address,
    ushort length,
    bool isBit,
    int splitLength = 500)
  {
    OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(address, length, plcType);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from);
    List<byte[]> numArrayList = new List<byte[]>();
    int[] array = SoftBasic.SplitIntegerToArray((int) length, isBit ? 1998 : splitLength);
    for (int index = 0; index < array.Length; ++index)
    {
      numArrayList.Add(OmronFinsNetHelper.BuildReadCommand(from.Content, (ushort) array[index], isBit));
      from.Content.AddressStart += isBit ? array[index] : array[index] * 16 /*0x10*/;
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.BuildReadCommand(HslCommunication.Profinet.Omron.OmronPlcType,System.String,System.UInt16,System.Boolean,System.Int32)" />
  public static byte[] BuildReadCommand(OmronFinsAddress address, ushort length, bool isBit)
  {
    return new byte[8]
    {
      (byte) 1,
      (byte) 1,
      !isBit ? address.WordCode : address.BitCode,
      (byte) (address.AddressStart / 16 /*0x10*/ / 256 /*0x0100*/),
      (byte) (address.AddressStart / 16 /*0x10*/ % 256 /*0x0100*/),
      (byte) (address.AddressStart % 16 /*0x10*/),
      (byte) ((uint) length / 256U /*0x0100*/),
      (byte) ((uint) length % 256U /*0x0100*/)
    };
  }

  /// <summary>
  /// 根据写入的地址，数据，是否位写入生成Fins协议的核心报文<br />
  /// According to the written address, data, whether the bit is written to generate the core message of the Fins protocol
  /// </summary>
  /// <param name="plcType">PLC的类型信息</param>
  /// <param name="address">地址内容，具体格式请参照示例说明</param>
  /// <param name="value">实际的数据</param>
  /// <param name="isBit">是否位数据</param>
  /// <returns>带有成功标识的Fins核心报文</returns>
  public static OperateResult<byte[]> BuildWriteWordCommand(
    OmronPlcType plcType,
    string address,
    byte[] value,
    bool isBit)
  {
    OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(address, (ushort) 0, plcType);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) from);
    byte[] numArray = new byte[8 + value.Length];
    numArray[0] = (byte) 1;
    numArray[1] = (byte) 2;
    numArray[2] = !isBit ? from.Content.WordCode : from.Content.BitCode;
    numArray[3] = (byte) (from.Content.AddressStart / 16 /*0x10*/ / 256 /*0x0100*/);
    numArray[4] = (byte) (from.Content.AddressStart / 16 /*0x10*/ % 256 /*0x0100*/);
    numArray[5] = (byte) (from.Content.AddressStart % 16 /*0x10*/);
    if (isBit)
    {
      numArray[6] = (byte) (value.Length / 256 /*0x0100*/);
      numArray[7] = (byte) (value.Length % 256 /*0x0100*/);
    }
    else
    {
      numArray[6] = (byte) (value.Length / 2 / 256 /*0x0100*/);
      numArray[7] = (byte) (value.Length / 2 % 256 /*0x0100*/);
    }
    value.CopyTo((Array) numArray, 8);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>
  /// 验证欧姆龙的Fins-TCP返回的数据是否正确的数据，如果正确的话，并返回所有的数据内容<br />
  /// Verify that the data returned by Omron's Fins-TCP is correct data, if correct, and return all data content
  /// </summary>
  /// <param name="response">来自欧姆龙返回的数据内容</param>
  /// <returns>带有是否成功的结果对象</returns>
  public static OperateResult<byte[]> ResponseValidAnalysis(byte[] response)
  {
    if (response.Length < 16 /*0x10*/)
      return new OperateResult<byte[]>(StringResources.Language.OmronReceiveDataError);
    int int32 = BitConverter.ToInt32(new byte[4]
    {
      response[15],
      response[14],
      response[13],
      response[12]
    }, 0);
    return int32 > 0 ? new OperateResult<byte[]>(int32, OmronFinsNetHelper.GetStatusDescription(int32)) : OmronFinsNetHelper.UdpResponseValidAnalysis(response.RemoveBegin<byte>(16 /*0x10*/));
  }

  /// <summary>
  /// 验证欧姆龙的Fins-Udp返回的数据是否正确的数据，如果正确的话，并返回所有的数据内容<br />
  /// Verify that the data returned by Omron's Fins-Udp is correct data, if correct, and return all data content
  /// </summary>
  /// <param name="response">来自欧姆龙返回的数据内容</param>
  /// <returns>带有是否成功的结果对象</returns>
  public static OperateResult<byte[]> UdpResponseValidAnalysis(byte[] response)
  {
    if (response.Length < 14)
      return new OperateResult<byte[]>(StringResources.Language.OmronReceiveDataError);
    int err = (int) response[12] * 256 /*0x0100*/ + (int) response[13];
    if (response[12].GetBoolByIndex(7))
    {
      int mainCode = (int) response[12] & (int) sbyte.MaxValue;
      int subCode = (int) response[13] & 63 /*0x3F*/;
      return new OperateResult<byte[]>(err, OmronFinsNetHelper.GetEndCodeDescription(mainCode, subCode));
    }
    if (response[10] == (byte) 1 & response[11] == (byte) 1 || response[10] == (byte) 1 & response[11] == (byte) 4 || response[10] == (byte) 2 & response[11] == (byte) 1 || response[10] == (byte) 3 & response[11] == (byte) 6 || response[10] == (byte) 5 & response[11] == (byte) 1 || response[10] == (byte) 5 & response[11] == (byte) 2 || response[10] == (byte) 6 & response[11] == (byte) 1 || response[10] == (byte) 6 & response[11] == (byte) 32 /*0x20*/ || response[10] == (byte) 7 & response[11] == (byte) 1 || response[10] == (byte) 9 & response[11] == (byte) 32 /*0x20*/ || response[10] == (byte) 33 & response[11] == (byte) 2 || response[10] == (byte) 34 & response[11] == (byte) 2)
    {
      try
      {
        byte[] destinationArray = new byte[response.Length - 14];
        if (destinationArray.Length != 0)
          Array.Copy((Array) response, 14, (Array) destinationArray, 0, destinationArray.Length);
        OperateResult<byte[]> successResult = OperateResult.CreateSuccessResult<byte[]>(destinationArray);
        if (destinationArray.Length == 0)
          successResult.IsSuccess = false;
        successResult.ErrorCode = err;
        successResult.Message = $"{OmronFinsNetHelper.GetStatusDescription(err)} Received:{SoftBasic.ByteToHexString(response, ' ')}";
        if (response[10] == (byte) 1 & response[11] == (byte) 4)
        {
          byte[] numArray = destinationArray.Length != 0 ? new byte[destinationArray.Length * 2 / 3] : new byte[0];
          for (int index = 0; index < destinationArray.Length / 3; ++index)
          {
            numArray[index * 2] = destinationArray[index * 3 + 1];
            numArray[index * 2 + 1] = destinationArray[index * 3 + 2];
          }
          successResult.Content = numArray;
        }
        return successResult;
      }
      catch (Exception ex)
      {
        return new OperateResult<byte[]>($"UdpResponseValidAnalysis failed: {ex.Message}{Environment.NewLine}Content: {response.ToHexString(' ')}");
      }
    }
    else
    {
      OperateResult<byte[]> successResult = OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
      successResult.ErrorCode = err;
      successResult.Message = $"{OmronFinsNetHelper.GetStatusDescription(err)} Received:{SoftBasic.ByteToHexString(response, ' ')}";
      return successResult;
    }
  }

  private static string GetEndCodeDescription(int mainCode, int subCode)
  {
    switch (mainCode)
    {
      case 0:
        if (subCode == 0)
          return "Normal completion";
        if (subCode == 1)
          return "Data link status: Service was canceled";
        break;
      case 1:
        switch (subCode)
        {
          case 1:
            return "Local node is not participating in the network.";
          case 2:
            return "Token does not arrive. [Set the local node to within the maximum node address.]";
          case 3:
            return "Send was not possible during the specified number of retries.";
          case 4:
            return "Cannot send because maximum number of event frames exceeded.";
          case 5:
            return "Node address setting error occurred";
          case 6:
            return "The same node address has been set twice in the same network";
        }
        break;
      case 2:
        switch (subCode)
        {
          case 1:
            return "The destination node is not in the network";
          case 2:
            return "There is no Unit with the specified unit address.";
          case 3:
            return "The third node does not exist";
          case 4:
            return "The destination node is busy";
          case 5:
            return "The message was destroyed by noise.";
        }
        break;
      case 3:
        switch (subCode)
        {
          case 1:
            return "An error occurred in the communications controller";
          case 2:
            return "A CPU error occurred in the destination CPU Unit.";
          case 3:
            return "A response was not returned because an error occurred in the Board";
          case 4:
            return "The unit number was set incorrectly";
        }
        break;
      case 4:
        if (subCode == 1)
          return "The Unit/Board does not support the specified command code";
        if (subCode == 2)
          return "The command cannot be executed because the model or version is incorrect";
        break;
      case 5:
        switch (subCode)
        {
          case 1:
            return "The destination network or node address is not set in the routing tables";
          case 2:
            return "Relaying is not possible because there are no routing tables";
          case 3:
            return "There is an error in the routing tables";
          case 4:
            return "An attempt was made to send to a network that was over 3 networks away";
        }
        break;
      case 16 /*0x10*/:
        switch (subCode)
        {
          case 1:
            return "The command is longer than the maximum permissible length";
          case 2:
            return "The command is shorter than the minimum permissible length";
          case 3:
            return "The designated number of elements differs from the number of write data items";
          case 4:
            return "An incorrect format was used";
          case 5:
            return "Either the relay table in the local node or the local network table in the relay node is incorrect.";
        }
        break;
      case 17:
        switch (subCode)
        {
          case 1:
            return "The specified word does not exist in the memory area or there is no EM Area";
          case 2:
            return "The access size specification is incorrect or an odd word address is specified";
          case 3:
            return "The start address in command process is beyond the accessible area";
          case 4:
            return "The end address in command process is beyond the accessible area";
          case 11:
            return "The response format is longer than the maximum permissible length.";
        }
        break;
      case 32 /*0x20*/:
        switch (subCode)
        {
          case 2:
            return "The program area is protected";
          case 4:
            return "The search data does not exist.";
          case 5:
            return "A non-existing program number has been specified";
        }
        break;
      case 33:
        if (subCode == 1)
          return "The specified area is read-only.";
        if (subCode == 2)
          return "The program area is protected.";
        break;
    }
    return StringResources.Language.UnknownError;
  }

  /// <summary>
  /// 根据欧姆龙返回的错误码，获取错误信息的字符串描述文本<br />
  /// According to the error code returned by Omron, get the string description text of the error message
  /// </summary>
  /// <param name="err">错误码</param>
  /// <returns>文本描述</returns>
  public static string GetStatusDescription(int err)
  {
    switch (err)
    {
      case 0:
        return StringResources.Language.OmronStatus0;
      case 1:
        return StringResources.Language.OmronStatus1;
      case 2:
        return StringResources.Language.OmronStatus2;
      case 3:
        return StringResources.Language.OmronStatus3;
      case 32 /*0x20*/:
        return StringResources.Language.OmronStatus20;
      case 33:
        return StringResources.Language.OmronStatus21;
      case 34:
        return StringResources.Language.OmronStatus22;
      case 35:
        return StringResources.Language.OmronStatus23;
      case 36:
        return StringResources.Language.OmronStatus24;
      case 37:
        return StringResources.Language.OmronStatus25;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>
  /// 从欧姆龙PLC中读取想要的数据，返回读取结果，读取长度的单位为字，地址格式为"D100","C100","W100","H100","A100"<br />
  /// Read the desired data from the Omron PLC and return the read result. The unit of the read length is word. The address format is "D100", "C100", "W100", "H100", "A100"
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <param name="address">读取地址，格式为"D100","C100","W100","H100","A100"</param>
  /// <param name="length">读取的数据长度</param>
  /// <param name="splits">分割信息</param>
  /// <returns>带成功标志的结果数据对象</returns>
  public static OperateResult<byte[]> Read(
    IOmronFins omron,
    string address,
    ushort length,
    int splits)
  {
    OperateResult<List<byte[]>> result1 = OmronFinsNetHelper.BuildReadCommand(omron.PlcType, address, length, false, splits);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result1);
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < result1.Content.Count; ++index)
    {
      OperateResult<byte[]> result2 = omron.ReadFromCoreServer(result1.Content[index]);
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) result2);
      byteList.AddRange((IEnumerable<byte>) result2.Content);
    }
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  /// <summary>
  /// 从欧姆龙PLC中读取多个地址的数据，返回读取结果，每个地址按照字为单位读取，地址格式为"D100","C100","W100","H100","A100"
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <param name="address">从欧姆龙PLC中读取多个地址的数据，返回读取结果，每个地址按照字为单位读取，地址格式为"D100","C100","W100","H100","A100"</param>
  /// <returns>带成功标志的结果数据对象</returns>
  public static OperateResult<byte[]> Read(IOmronFins omron, string[] address)
  {
    OperateResult<List<byte[]>> result = OmronFinsNetHelper.BuildReadCommand(address, omron.PlcType);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : omron.ReadFromCoreServer((IEnumerable<byte[]>) result.Content);
  }

  /// <summary>
  /// 向PLC写入数据，数据格式为原始的字节类型，地址格式为"D100","C100","W100","H100","A100"<br />
  /// Write data to PLC, the data format is the original byte type, and the address format is "D100", "C100", "W100", "H100", "A100"
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <param name="address">初始地址</param>
  /// <param name="value">原始的字节数据</param>
  /// <returns>结果</returns>
  public static OperateResult Write(IOmronFins omron, string address, byte[] value)
  {
    OperateResult<byte[]> operateResult1 = OmronFinsNetHelper.BuildWriteWordCommand(omron.PlcType, address, value, false);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = omron.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.Read(HslCommunication.Profinet.Omron.Helper.IOmronFins,System.String,System.UInt16,System.Int32)" />
  public static async Task<OperateResult<byte[]>> ReadAsync(
    IOmronFins omron,
    string address,
    ushort length,
    int splits)
  {
    OperateResult<List<byte[]>> command = OmronFinsNetHelper.BuildReadCommand(omron.PlcType, address, length, false, splits);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    List<byte> contentArray = new List<byte>();
    for (int i = 0; i < command.Content.Count; ++i)
    {
      OperateResult<byte[]> read = await omron.ReadFromCoreServerAsync(command.Content[i]);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>((OperateResult) read);
      contentArray.AddRange((IEnumerable<byte>) read.Content);
      read = (OperateResult<byte[]>) null;
    }
    return OperateResult.CreateSuccessResult<byte[]>(contentArray.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.Write(HslCommunication.Profinet.Omron.Helper.IOmronFins,System.String,System.Byte[])" />
  public static async Task<OperateResult> WriteAsync(
    IOmronFins omron,
    string address,
    byte[] value)
  {
    OperateResult<byte[]> command = OmronFinsNetHelper.BuildWriteWordCommand(omron.PlcType, address, value, false);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await omron.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) read;
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.Read(HslCommunication.Profinet.Omron.Helper.IOmronFins,System.String[])" />
  public static async Task<OperateResult<byte[]>> ReadAsync(IOmronFins omron, string[] address)
  {
    OperateResult<List<byte[]>> command = OmronFinsNetHelper.BuildReadCommand(address, omron.PlcType);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult = await omron.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content);
    return operateResult;
  }

  /// <summary>
  /// 从欧姆龙PLC中批量读取位软元件，地址格式为"D100.0","C100.0","W100.0","H100.0","A100.0"<br />
  /// Read bit devices in batches from Omron PLC with address format "D100.0", "C100.0", "W100.0", "H100.0", "A100.0"
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <param name="address">读取地址，格式为"D100","C100","W100","H100","A100"</param>
  /// <param name="length">读取的长度</param>
  /// <param name="splits">分割信息</param>
  /// <returns>带成功标志的结果数据对象</returns>
  public static OperateResult<bool[]> ReadBool(
    IOmronFins omron,
    string address,
    ushort length,
    int splits)
  {
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
    {
      if (!address.Contains("."))
        address += ".0";
      return HslHelper.ReadBool((IReadWriteNet) omron, address, length, reverseByWord: true);
    }
    OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(address, length, omron.PlcType);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    int num = 0;
    List<bool> boolList = new List<bool>();
    while (num < (int) length)
    {
      byte[] send = OmronFinsNetHelper.BuildReadCommand(from.Content, (ushort) ((uint) length - (uint) num), true);
      OperateResult<byte[]> result = omron.ReadFromCoreServer(send);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) result);
      if (result.Content.Length == 0)
        return new OperateResult<bool[]>(result.ErrorCode, result.Message);
      IEnumerable<bool> bools = ((IEnumerable<byte>) result.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0));
      boolList.AddRange(bools);
      num += bools.Count<bool>();
      from.Content.AddressStart += bools.Count<bool>();
    }
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  /// <summary>
  /// 向PLC中位软元件写入bool数组，返回是否写入成功，比如你写入D100,values[0]对应D100.0，地址格式为"D100.0","C100.0","W100.0","H100.0","A100.0"<br />
  /// Write the bool array to the PLC's median device and return whether the write was successful. For example, if you write D100, values [0] corresponds to D100.0
  /// and the address format is "D100.0", "C100.0", "W100. 0 "," H100.0 "," A100.0 "
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <param name="address">要写入的数据地址</param>
  /// <param name="values">要写入的实际数据，可以指定任意的长度</param>
  /// <returns>返回写入结果</returns>
  public static OperateResult Write(IOmronFins omron, string address, bool[] values)
  {
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
      return new OperateResult("DR and IR address not support bit write");
    if (omron.PlcType == OmronPlcType.CV && (address.StartsWithAndNumber("CIO") || address.StartsWithAndNumber("C")))
      return ReadWriteNetHelper.WriteBoolWithWord((IReadWriteNet) omron, address, values, reverseWord: true);
    OperateResult<byte[]> operateResult1 = OmronFinsNetHelper.BuildWriteWordCommand(omron.PlcType, address, ((IEnumerable<bool>) values).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), true);
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = omron.ReadFromCoreServer(operateResult1.Content);
    return !operateResult2.IsSuccess ? (OperateResult) operateResult2 : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.ReadBool(HslCommunication.Profinet.Omron.Helper.IOmronFins,System.String,System.UInt16,System.Int32)" />
  public static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IOmronFins omron,
    string address,
    ushort length,
    int splits)
  {
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
    {
      if (!address.Contains("."))
        address += ".0";
      OperateResult<bool[]> operateResult = await HslHelper.ReadBoolAsync((IReadWriteNet) omron, address, length, reverseByWord: true);
      return operateResult;
    }
    OperateResult<OmronFinsAddress> analysis = OmronFinsAddress.ParseFrom(address, length, omron.PlcType);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) analysis);
    int len = 0;
    List<bool> contentArray = new List<bool>();
    while (len < (int) length)
    {
      byte[] cmd = OmronFinsNetHelper.BuildReadCommand(analysis.Content, (ushort) ((uint) length - (uint) len), true);
      OperateResult<byte[]> read = await omron.ReadFromCoreServerAsync(cmd);
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<bool[]>((OperateResult) read);
      if (read.Content.Length == 0)
        return new OperateResult<bool[]>(read.ErrorCode, read.Message);
      IEnumerable<bool> array = ((IEnumerable<byte>) read.Content).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0));
      contentArray.AddRange(array);
      len += array.Count<bool>();
      analysis.Content.AddressStart += array.Count<bool>();
      cmd = (byte[]) null;
      read = (OperateResult<byte[]>) null;
      array = (IEnumerable<bool>) null;
    }
    return OperateResult.CreateSuccessResult<bool[]>(contentArray.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.Write(HslCommunication.Profinet.Omron.Helper.IOmronFins,System.String,System.Boolean[])" />
  public static async Task<OperateResult> WriteAsync(
    IOmronFins omron,
    string address,
    bool[] values)
  {
    if (address.StartsWith("DR", StringComparison.OrdinalIgnoreCase) || address.StartsWith("IR", StringComparison.OrdinalIgnoreCase))
      return new OperateResult("DR and IR address not support bit write");
    if (omron.PlcType == OmronPlcType.CV && (address.StartsWithAndNumber("CIO") || address.StartsWithAndNumber("C")))
    {
      OperateResult operateResult = await ReadWriteNetHelper.WriteBoolWithWordAsync((IReadWriteNet) omron, address, values, reverseWord: true).ConfigureAwait(false);
      return operateResult;
    }
    OperateResult<byte[]> command = OmronFinsNetHelper.BuildWriteWordCommand(omron.PlcType, address, ((IEnumerable<bool>) values).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>(), true);
    if (!command.IsSuccess)
      return (OperateResult) command;
    OperateResult<byte[]> read = await omron.ReadFromCoreServerAsync(command.Content);
    return read.IsSuccess ? OperateResult.CreateSuccessResult() : (OperateResult) read;
  }

  /// <summary>
  /// 将CPU单元的操作模式更改为RUN，从而使PLC能够执行其程序。<br />
  /// Changes the CPU Unit’s operating mode to RUN, enabling the PLC to execute its program.
  /// </summary>
  /// <remarks>
  /// 当执行RUN时，CPU单元将开始运行。 在执行RUN之前，您必须确认系统的安全性。 启用“禁止覆盖受保护程序”设置时，无法执行此命令。<br />
  /// The CPU Unit will start operation when RUN is executed. You must confirm the safety of the system before executing RUN.
  /// When the “prohibit overwriting of protected program” setting is enabled, this command cannot be executed.
  /// </remarks>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <returns>是否启动成功</returns>
  public static OperateResult Run(IReadWriteDevice omron)
  {
    return (OperateResult) omron.ReadFromCoreServer(new byte[5]
    {
      (byte) 4,
      (byte) 1,
      byte.MaxValue,
      byte.MaxValue,
      (byte) 4
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.Run(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult> RunAsync(IReadWriteDevice omron)
  {
    OperateResult<byte[]> operateResult = await omron.ReadFromCoreServerAsync(new byte[5]
    {
      (byte) 4,
      (byte) 1,
      byte.MaxValue,
      byte.MaxValue,
      (byte) 4
    }).ConfigureAwait(false);
    return (OperateResult) operateResult;
  }

  /// <summary>
  /// 将CPU单元的操作模式更改为PROGRAM，停止程序执行。<br />
  /// Changes the CPU Unit’s operating mode to PROGRAM, stopping program execution.
  /// </summary>
  /// <remarks>
  /// 当执行STOP时，CPU单元将停止操作。 在执行STOP之前，您必须确认系统的安全性。<br />
  /// The CPU Unit will stop operation when STOP is executed. You must confirm the safety of the system before executing STOP.
  /// </remarks>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <returns>是否停止成功</returns>
  public static OperateResult Stop(IReadWriteDevice omron)
  {
    return (OperateResult) omron.ReadFromCoreServer(new byte[4]
    {
      (byte) 4,
      (byte) 2,
      byte.MaxValue,
      byte.MaxValue
    });
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.Stop(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult> StopAsync(IReadWriteDevice omron)
  {
    OperateResult<byte[]> operateResult = await omron.ReadFromCoreServerAsync(new byte[4]
    {
      (byte) 4,
      (byte) 2,
      byte.MaxValue,
      byte.MaxValue
    }).ConfigureAwait(false);
    return (OperateResult) operateResult;
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取CPU的一些数据信息，主要包含型号，版本，一些数据块的大小<br />
  /// <b>[Authorization]</b> Read some data information of the CPU, mainly including the model, version, and the size of some data blocks
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <returns>是否读取成功</returns>
  public static OperateResult<OmronCpuUnitData> ReadCpuUnitData(IReadWriteDevice omron)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<OmronCpuUnitData>(StringResources.Language.InsufficientPrivileges);
    return omron.ReadFromCoreServer(new byte[3]
    {
      (byte) 5,
      (byte) 1,
      (byte) 0
    }).Then<OmronCpuUnitData>((Func<byte[], OperateResult<OmronCpuUnitData>>) (m => OperateResult.CreateSuccessResult<OmronCpuUnitData>(new OmronCpuUnitData(m))));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.ReadCpuUnitData(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult<OmronCpuUnitData>> ReadCpuUnitDataAsync(
    IReadWriteDevice omron)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<OmronCpuUnitData>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = await omron.ReadFromCoreServerAsync(new byte[3]
    {
      (byte) 5,
      (byte) 1,
      (byte) 0
    }).ConfigureAwait(false);
    return operateResult.Then<OmronCpuUnitData>((Func<byte[], OperateResult<OmronCpuUnitData>>) (m => OperateResult.CreateSuccessResult<OmronCpuUnitData>(new OmronCpuUnitData(m))));
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取CPU单元的一些操作状态数据，主要包含运行状态，工作模式，错误信息等。<br />
  /// <b>[Authorization]</b> Read some operating status data of the CPU unit, mainly including operating status, working mode, error information, etc.
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <returns>是否读取成功</returns>
  public static OperateResult<OmronCpuUnitStatus> ReadCpuUnitStatus(IReadWriteDevice omron)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<OmronCpuUnitStatus>(StringResources.Language.InsufficientPrivileges);
    return omron.ReadFromCoreServer(new byte[2]
    {
      (byte) 6,
      (byte) 1
    }).Then<OmronCpuUnitStatus>((Func<byte[], OperateResult<OmronCpuUnitStatus>>) (m => OperateResult.CreateSuccessResult<OmronCpuUnitStatus>(new OmronCpuUnitStatus(m))));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.ReadCpuUnitStatus(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult<OmronCpuUnitStatus>> ReadCpuUnitStatusAsync(
    IReadWriteDevice omron)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<OmronCpuUnitStatus>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = await omron.ReadFromCoreServerAsync(new byte[2]
    {
      (byte) 6,
      (byte) 1
    }).ConfigureAwait(false);
    return operateResult.Then<OmronCpuUnitStatus>((Func<byte[], OperateResult<OmronCpuUnitStatus>>) (m => OperateResult.CreateSuccessResult<OmronCpuUnitStatus>(new OmronCpuUnitStatus(m))));
  }

  private static OperateResult<DateTime> CreatePlcTime(byte[] buffer)
  {
    try
    {
      string hexString = buffer.ToHexString();
      return OperateResult.CreateSuccessResult<DateTime>(new DateTime(Convert.ToInt32(DateTime.Now.Year.ToString().Substring(0, 2) + hexString.Substring(0, 2)), Convert.ToInt32(hexString.Substring(2, 2)), Convert.ToInt32(hexString.Substring(4, 2)), Convert.ToInt32(hexString.Substring(6, 2)), Convert.ToInt32(hexString.Substring(8, 2)), Convert.ToInt32(hexString.Substring(10, 2))));
    }
    catch (Exception ex)
    {
      return new OperateResult<DateTime>($"Prase Time failed: {ex.Message}{Environment.NewLine}Source: {buffer.ToHexString(' ')}");
    }
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取CPU的时间信息<br />
  /// <b>[Authorization]</b> Read the time information of the CPU
  /// </summary>
  /// <param name="omron">PLC设备的连接对象</param>
  /// <returns>是否读取成功</returns>
  public static OperateResult<DateTime> ReadCpuTime(IReadWriteDevice omron)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<DateTime>(StringResources.Language.InsufficientPrivileges);
    return omron.ReadFromCoreServer(new byte[2]
    {
      (byte) 7,
      (byte) 1
    }).Then<DateTime>((Func<byte[], OperateResult<DateTime>>) (m => OmronFinsNetHelper.CreatePlcTime(m)));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNetHelper.ReadCpuUnitStatus(HslCommunication.Core.IReadWriteDevice)" />
  public static async Task<OperateResult<DateTime>> ReadCpuTimeAsync(IReadWriteDevice omron)
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<DateTime>(StringResources.Language.InsufficientPrivileges);
    OperateResult<byte[]> operateResult = await omron.ReadFromCoreServerAsync(new byte[2]
    {
      (byte) 7,
      (byte) 1
    }).ConfigureAwait(false);
    return operateResult.Then<DateTime>((Func<byte[], OperateResult<DateTime>>) (m => OmronFinsNetHelper.CreatePlcTime(m)));
  }
}
