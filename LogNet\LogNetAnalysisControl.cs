﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.LogNet.LogNetAnalysisControl
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;

#nullable disable
namespace HslCommunication.LogNet;

/// <summary>一个用于日志分析的控件</summary>
public class LogNetAnalysisControl : UserControl
{
  private string m_LogSource = string.Empty;
  private Button selectButton = (Button) null;
  private List<DateTime> listPaint = new List<DateTime>();
  private List<LogNetAnalysisControl.PaintItem> listRender = new List<LogNetAnalysisControl.PaintItem>();
  private StringFormat stringFormat = new StringFormat()
  {
    Alignment = StringAlignment.Center,
    LineAlignment = StringAlignment.Center
  };
  /// <summary>必需的设计器变量。</summary>
  private IContainer components = (IContainer) null;
  private TextBox textBox1;
  private TextBox textBox2;
  private Label label1;
  private TextBox textBox3;
  private Button userButton_Debug;
  private Button userButton_Info;
  private Button userButton_Warn;
  private Button userButton_Error;
  private Button userButton_Fatal;
  private Button userButton_All;
  private Label label2;
  private Button userButton_source;
  private TabControl tabControl1;
  private TabPage tabPage1;
  private TabPage tabPage2;
  private PictureBox pictureBox1;
  private CheckBox checkBox1;
  private TextBox textBox4;
  private CheckBox checkBox2;

  /// <summary>实例化一个控件信息</summary>
  public LogNetAnalysisControl() => this.InitializeComponent();

  private void LogNetAnalysisControl_Load(object sender, EventArgs e)
  {
    this.userButton_Debug.Text = StringResources.Language.LogNetDebug ?? "";
    this.userButton_Info.Text = StringResources.Language.LogNetInfo ?? "";
    this.userButton_Warn.Text = StringResources.Language.LogNetWarn ?? "";
    this.userButton_Error.Text = StringResources.Language.LogNetError ?? "";
    this.userButton_Fatal.Text = StringResources.Language.LogNetFatal ?? "";
    this.userButton_All.Text = StringResources.Language.LogNetAll ?? "";
    this.label2.Text = StringResources.Language.LogNetTimeSelect;
    this.checkBox1.Text = StringResources.Language.LogNetUseExpress;
    this.tabPage1.Text = StringResources.Language.LogNetDataView;
    this.tabPage2.Text = StringResources.Language.LogNetDistributedView;
    this.userButton_source.Text = StringResources.Language.LogNetSource;
    this.checkBox2.Text = StringResources.Language.LogNetCharZero;
  }

  /// <summary>设置日志的数据源</summary>
  /// <param name="logSource">直接从日志文件中读到的数据或是来自网络的数据</param>
  public void SetLogNetSource(string logSource)
  {
    this.m_LogSource = logSource;
    this.SetLogNetSourceView();
  }

  private void SetLogNetSourceView()
  {
    if (string.IsNullOrEmpty(this.m_LogSource))
      return;
    this.AnalysisLogSource(DateTime.MinValue, DateTime.MaxValue, StringResources.Language.LogNetAll);
    this.selectButton = this.userButton_All;
  }

  /// <summary>从现有的日志中筛选数据</summary>
  /// <param name="degree">等级</param>
  private void FilterLogSource(string degree)
  {
    if (string.IsNullOrEmpty(this.m_LogSource))
      return;
    DateTime result1 = DateTime.MinValue;
    DateTime result2 = DateTime.MaxValue;
    if (!DateTime.TryParse(this.textBox2.Text, out result1))
    {
      int num1 = (int) MessageBox.Show(StringResources.Language.LogNetStartTimeWrong);
    }
    else if (!DateTime.TryParse(this.textBox3.Text, out result2))
    {
      int num2 = (int) MessageBox.Show(StringResources.Language.LogNetFinishTimeWrong);
    }
    else
      this.AnalysisLogSource(result1, result2, degree);
  }

  /// <summary>底层的数据分析筛选</summary>
  /// <param name="start"></param>
  /// <param name="end"></param>
  /// <param name="degree"></param>
  private void AnalysisLogSource(DateTime start, DateTime end, string degree)
  {
    if (string.IsNullOrEmpty(this.m_LogSource))
      return;
    StringBuilder stringBuilder = new StringBuilder();
    List<Match> matchList = new List<Match>(Regex.Matches(this.m_LogSource, "\u0002\\[[^\u0002]+").OfType<Match>());
    int num1 = 0;
    int num2 = 0;
    int num3 = 0;
    int num4 = 0;
    int num5 = 0;
    int num6 = 0;
    List<DateTime> dateTimeList = new List<DateTime>();
    for (int index = 0; index < matchList.Count; ++index)
    {
      Match match = matchList[index];
      string str = match.Value.Substring(2, 5);
      DateTime dateTime = Convert.ToDateTime(match.Value.Substring(match.Value.IndexOf('2'), 19));
      if (start == DateTime.MinValue)
      {
        if (index == 0)
          this.textBox2.Text = match.Value.Substring(match.Value.IndexOf('2'), 19);
        if (index == matchList.Count - 1)
          this.textBox3.Text = match.Value.Substring(match.Value.IndexOf('2'), 19);
      }
      if (start <= dateTime && dateTime <= end && (!this.checkBox1.Checked || Regex.IsMatch(match.Value, this.textBox4.Text)))
      {
        if (str.StartsWith(StringResources.Language.LogNetDebug))
          ++num1;
        else if (str.StartsWith(StringResources.Language.LogNetInfo))
          ++num2;
        else if (str.StartsWith(StringResources.Language.LogNetWarn))
          ++num3;
        else if (str.StartsWith(StringResources.Language.LogNetError))
          ++num4;
        else if (str.StartsWith(StringResources.Language.LogNetFatal))
          ++num5;
        ++num6;
        if (degree == StringResources.Language.LogNetAll || str.StartsWith(degree))
        {
          stringBuilder.Append(match.Value.Substring(1));
          dateTimeList.Add(dateTime);
        }
      }
    }
    this.userButton_Debug.Text = $"{StringResources.Language.LogNetDebug} ({num1})";
    this.userButton_Info.Text = $"{StringResources.Language.LogNetInfo} ({num2})";
    this.userButton_Warn.Text = $"{StringResources.Language.LogNetWarn} ({num3})";
    this.userButton_Error.Text = $"{StringResources.Language.LogNetError} ({num4})";
    this.userButton_Fatal.Text = $"{StringResources.Language.LogNetFatal} ({num5})";
    this.userButton_All.Text = $"{StringResources.Language.LogNetAll} ({num6})";
    if (this.checkBox2.Checked)
      stringBuilder = stringBuilder.Replace("\0", "\\0");
    this.textBox1.Text = stringBuilder.ToString();
    this.listPaint = dateTimeList;
    if (this.pictureBox1.Width > 10)
      this.pictureBox1.Image = (Image) this.PaintData(this.pictureBox1.Width, this.pictureBox1.Height);
  }

  private void UserButtonSetSelected(Button userButton)
  {
    if (this.selectButton == userButton)
      return;
    this.selectButton = userButton;
  }

  private void userButton_Debug_Click(object sender, EventArgs e)
  {
    this.UserButtonSetSelected(this.userButton_Debug);
    this.FilterLogSource(StringResources.Language.LogNetDebug);
  }

  private void userButton_Info_Click(object sender, EventArgs e)
  {
    this.UserButtonSetSelected(this.userButton_Info);
    this.FilterLogSource(StringResources.Language.LogNetInfo);
  }

  private void userButton_Warn_Click(object sender, EventArgs e)
  {
    this.UserButtonSetSelected(this.userButton_Warn);
    this.FilterLogSource(StringResources.Language.LogNetWarn);
  }

  private void userButton_Error_Click(object sender, EventArgs e)
  {
    this.UserButtonSetSelected(this.userButton_Error);
    this.FilterLogSource(StringResources.Language.LogNetError);
  }

  private void userButton_Fatal_Click(object sender, EventArgs e)
  {
    this.UserButtonSetSelected(this.userButton_Fatal);
    this.FilterLogSource(StringResources.Language.LogNetFatal);
  }

  private void userButton_All_Click(object sender, EventArgs e)
  {
    this.UserButtonSetSelected(this.userButton_All);
    this.FilterLogSource(StringResources.Language.LogNetAll);
  }

  private void userButton_source_Click(object sender, EventArgs e) => this.SetLogNetSourceView();

  private Bitmap PaintData(int width, int height)
  {
    if (width < 200)
      width = 200;
    if (height < 100)
      height = 100;
    Bitmap bitmap = new Bitmap(width, height);
    Graphics g = Graphics.FromImage((Image) bitmap);
    Font font = new Font("宋体", 12f);
    StringFormat stringFormat = new StringFormat()
    {
      Alignment = StringAlignment.Far,
      LineAlignment = StringAlignment.Center
    };
    Pen penDash = new Pen(Color.LightGray, 1f);
    penDash.DashStyle = DashStyle.Custom;
    penDash.DashPattern = new float[2]{ 5f, 5f };
    g.Clear(Color.White);
    if (this.listPaint.Count <= 5)
    {
      using (StringFormat format = new StringFormat()
      {
        Alignment = StringAlignment.Center,
        LineAlignment = StringAlignment.Center
      })
        g.DrawString(StringResources.Language.LogNetDataTooLittle, font, Brushes.DeepSkyBlue, (RectangleF) new Rectangle(0, 0, width, height), format);
    }
    else
    {
      int length = (width - 60) / 6;
      TimeSpan timeSpan = this.listPaint.Max<DateTime>() - this.listPaint.Min<DateTime>();
      DateTime dateTime = this.listPaint.Min<DateTime>();
      double num1 = timeSpan.TotalSeconds / (double) length;
      int[] source = new int[length];
      for (int index1 = 0; index1 < this.listPaint.Count; ++index1)
      {
        int index2 = (int) ((this.listPaint[index1] - dateTime).TotalSeconds / num1);
        if (index2 < 0)
          index2 = 0;
        if (index2 == length)
          --index2;
        ++source[index2];
      }
      int max = ((IEnumerable<int>) source).Max();
      int min = 0;
      LogNetAnalysisControl.PaintItem[] collection = new LogNetAnalysisControl.PaintItem[length];
      for (int index = 0; index < source.Length; ++index)
        collection[index] = new LogNetAnalysisControl.PaintItem()
        {
          Count = source[index],
          Start = this.listPaint[0].AddSeconds((double) index * num1),
          End = index != source.Length - 1 ? this.listPaint[0].AddSeconds((double) (index + 1) * num1) : this.listPaint[this.listPaint.Count - 1]
        };
      this.listRender = new List<LogNetAnalysisControl.PaintItem>((IEnumerable<LogNetAnalysisControl.PaintItem>) collection);
      int num2 = 50;
      int right = 10;
      int num3 = 20;
      int down = 30;
      g.DrawLine(Pens.DimGray, num2, num3 - 10, num2, height - down);
      g.DrawLine(Pens.DimGray, num2, height - down + 1, width - right, height - down + 1);
      g.SmoothingMode = SmoothingMode.HighQuality;
      SoftPainting.PaintTriangle(g, Brushes.DimGray, new Point(num2, num3 - 10), 5, GraphDirection.Upward);
      g.SmoothingMode = SmoothingMode.None;
      int degree = 8;
      if (height < 500)
      {
        if (max < 15 && max > 1)
          degree = max;
      }
      else
        degree = height >= 700 ? (max >= 40 || max <= 1 ? 24 : max) : (max >= 25 || max <= 1 ? 16 /*0x10*/ : max);
      SoftPainting.PaintCoordinateDivide(g, Pens.DimGray, penDash, font, Brushes.DimGray, stringFormat, degree, max, min, width, height, num2, right, num3, down);
      stringFormat.Alignment = StringAlignment.Center;
      g.DrawString("Totle: " + this.listPaint.Count.ToString(), font, Brushes.DodgerBlue, new RectangleF((float) num2, 0.0f, (float) (width - num2 - right), (float) num3), stringFormat);
      int num4 = num2 + 2;
      for (int index = 0; index < collection.Length; ++index)
      {
        float y = SoftPainting.ComputePaintLocationY(max, min, height - num3 - down, collection[index].Count) + (float) num3;
        RectangleF rect = new RectangleF((float) num4, y, 5f, (float) (height - down) - y);
        if ((double) rect.Height <= 0.0 && collection[index].Count > 0)
          rect = new RectangleF((float) num4, (float) (height - down - 1), 5f, 1f);
        g.FillRectangle(Brushes.Tomato, rect);
        num4 += 6;
      }
      g.DrawLine(Pens.DimGray, num4, num3 - 10, num4, height - down);
      g.SmoothingMode = SmoothingMode.HighQuality;
      SoftPainting.PaintTriangle(g, Brushes.DimGray, new Point(num4, num3 - 10), 5, GraphDirection.Upward);
      g.SmoothingMode = SmoothingMode.None;
    }
    stringFormat.Dispose();
    font.Dispose();
    penDash.Dispose();
    g.Dispose();
    return bitmap;
  }

  private bool IsMouseEnter { get; set; }

  private LogNetAnalysisControl.PaintItem ClickSelected { get; set; }

  private Point pointMove { get; set; }

  private void pictureBox1_Paint(object sender, PaintEventArgs e)
  {
    if (!this.IsMouseEnter || this.ClickSelected == null || this.pictureBox1.Width <= 100)
      return;
    string s = $"{this.ClickSelected.Start.ToString("yyyy-MM-dd HH:mm:ss")}  -  {this.ClickSelected.End.ToString("yyyy-MM-dd HH:mm:ss")}{Environment.NewLine}Count:{this.ClickSelected.Count.ToString()}";
    e.Graphics.DrawString(s, this.Font, Brushes.DimGray, (RectangleF) new Rectangle(50, this.pictureBox1.Height - 27, this.pictureBox1.Width - 60, 30), this.stringFormat);
    Graphics graphics = e.Graphics;
    Pen deepPink = Pens.DeepPink;
    Point pointMove = this.pointMove;
    int x1 = pointMove.X;
    pointMove = this.pointMove;
    int x2 = pointMove.X;
    int y2 = this.pictureBox1.Height - 30;
    graphics.DrawLine(deepPink, x1, 15, x2, y2);
  }

  private void pictureBox1_MouseEnter(object sender, EventArgs e) => this.IsMouseEnter = true;

  private void pictureBox1_MouseLeave(object sender, EventArgs e)
  {
    this.IsMouseEnter = false;
    this.pictureBox1.Refresh();
  }

  private void pictureBox1_MouseMove(object sender, MouseEventArgs e)
  {
    if (!this.IsMouseEnter || e.Y <= 20 || e.Y >= this.pictureBox1.Height - 30 || e.X <= 51 || e.X >= this.pictureBox1.Width - 10 || (e.X - 52) % 6 == 5)
      return;
    int index = (e.X - 52) / 6;
    if (index < this.listRender.Count)
    {
      this.pointMove = e.Location;
      this.ClickSelected = this.listRender[index];
      this.pictureBox1.Refresh();
    }
  }

  private void pictureBox1_SizeChanged(object sender, EventArgs e)
  {
    if (this.pictureBox1.Width <= 10)
      return;
    this.pictureBox1.Image = (Image) this.PaintData(this.pictureBox1.Width, this.pictureBox1.Height);
  }

  private void pictureBox1_DoubleClick(object sender, EventArgs e)
  {
    if (!this.IsMouseEnter)
      return;
    int num;
    if (this.pointMove.Y > 20)
    {
      Point pointMove = this.pointMove;
      if (pointMove.Y < this.pictureBox1.Height - 30)
      {
        pointMove = this.pointMove;
        if (pointMove.X > 51)
        {
          pointMove = this.pointMove;
          num = pointMove.X < this.pictureBox1.Width - 10 ? 1 : 0;
          goto label_6;
        }
      }
    }
    num = 0;
label_6:
    if (num != 0 && this.selectButton != null && (this.ClickSelected.End - this.ClickSelected.Start).TotalSeconds > 3.0)
    {
      this.textBox2.Text = this.ClickSelected.Start.ToString("yyyy-MM-dd HH:mm:ss");
      this.textBox3.Text = this.ClickSelected.End.ToString("yyyy-MM-dd HH:mm:ss");
      this.AnalysisLogSource(this.ClickSelected.Start, this.ClickSelected.End, this.selectButton.Text.Substring(0, 2));
    }
  }

  /// <summary>清理所有正在使用的资源。</summary>
  /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  /// <summary>
  /// 设计器支持所需的方法 - 不要修改
  /// 使用代码编辑器修改此方法的内容。
  /// </summary>
  private void InitializeComponent()
  {
    this.textBox1 = new TextBox();
    this.textBox2 = new TextBox();
    this.label1 = new Label();
    this.textBox3 = new TextBox();
    this.label2 = new Label();
    this.tabControl1 = new TabControl();
    this.tabPage1 = new TabPage();
    this.tabPage2 = new TabPage();
    this.pictureBox1 = new PictureBox();
    this.checkBox1 = new CheckBox();
    this.textBox4 = new TextBox();
    this.userButton_source = new Button();
    this.userButton_All = new Button();
    this.userButton_Fatal = new Button();
    this.userButton_Error = new Button();
    this.userButton_Warn = new Button();
    this.userButton_Info = new Button();
    this.userButton_Debug = new Button();
    this.checkBox2 = new CheckBox();
    this.tabControl1.SuspendLayout();
    this.tabPage1.SuspendLayout();
    this.tabPage2.SuspendLayout();
    ((ISupportInitialize) this.pictureBox1).BeginInit();
    this.SuspendLayout();
    this.textBox1.BorderStyle = BorderStyle.FixedSingle;
    this.textBox1.Dock = DockStyle.Fill;
    this.textBox1.Font = new Font("宋体", 12f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.textBox1.Location = new Point(3, 3);
    this.textBox1.Multiline = true;
    this.textBox1.Name = "textBox1";
    this.textBox1.ScrollBars = ScrollBars.Vertical;
    this.textBox1.Size = new Size(728, 439);
    this.textBox1.TabIndex = 0;
    this.textBox2.Font = new Font("宋体", 10.5f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.textBox2.Location = new Point(92, 4);
    this.textBox2.Name = "textBox2";
    this.textBox2.Size = new Size(156, 23);
    this.textBox2.TabIndex = 2;
    this.label1.AutoSize = true;
    this.label1.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.label1.Location = new Point(264, 6);
    this.label1.Name = "label1";
    this.label1.Size = new Size(28, 17);
    this.label1.TabIndex = 3;
    this.label1.Text = "----";
    this.textBox3.Font = new Font("宋体", 10.5f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.textBox3.Location = new Point(304, 4);
    this.textBox3.Name = "textBox3";
    this.textBox3.Size = new Size(156, 23);
    this.textBox3.TabIndex = 4;
    this.label2.AutoSize = true;
    this.label2.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.label2.Location = new Point(3, 8);
    this.label2.Name = "label2";
    this.label2.Size = new Size(68, 17);
    this.label2.TabIndex = 12;
    this.label2.Text = "时间选择：";
    this.tabControl1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
    this.tabControl1.Controls.Add((Control) this.tabPage1);
    this.tabControl1.Controls.Add((Control) this.tabPage2);
    this.tabControl1.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.tabControl1.Location = new Point(6, 34);
    this.tabControl1.Name = "tabControl1";
    this.tabControl1.SelectedIndex = 0;
    this.tabControl1.Size = new Size(742, 475);
    this.tabControl1.TabIndex = 15;
    this.tabPage1.Controls.Add((Control) this.textBox1);
    this.tabPage1.Location = new Point(4, 26);
    this.tabPage1.Name = "tabPage1";
    this.tabPage1.Padding = new Padding(3);
    this.tabPage1.Size = new Size(734, 445);
    this.tabPage1.TabIndex = 0;
    this.tabPage1.Text = "数据视图";
    this.tabPage1.UseVisualStyleBackColor = true;
    this.tabPage2.Controls.Add((Control) this.pictureBox1);
    this.tabPage2.Location = new Point(4, 26);
    this.tabPage2.Name = "tabPage2";
    this.tabPage2.Padding = new Padding(3);
    this.tabPage2.Size = new Size(734, 440);
    this.tabPage2.TabIndex = 1;
    this.tabPage2.Text = "分布视图";
    this.tabPage2.UseVisualStyleBackColor = true;
    this.pictureBox1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
    this.pictureBox1.Location = new Point(6, 11);
    this.pictureBox1.Name = "pictureBox1";
    this.pictureBox1.Size = new Size(708, 402);
    this.pictureBox1.TabIndex = 0;
    this.pictureBox1.TabStop = false;
    this.pictureBox1.SizeChanged += new EventHandler(this.pictureBox1_SizeChanged);
    this.pictureBox1.Paint += new PaintEventHandler(this.pictureBox1_Paint);
    this.pictureBox1.DoubleClick += new EventHandler(this.pictureBox1_DoubleClick);
    this.pictureBox1.MouseEnter += new EventHandler(this.pictureBox1_MouseEnter);
    this.pictureBox1.MouseLeave += new EventHandler(this.pictureBox1_MouseLeave);
    this.pictureBox1.MouseMove += new MouseEventHandler(this.pictureBox1_MouseMove);
    this.checkBox1.AutoSize = true;
    this.checkBox1.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.checkBox1.Location = new Point(514, 5);
    this.checkBox1.Name = "checkBox1";
    this.checkBox1.Size = new Size(111, 21);
    this.checkBox1.TabIndex = 16 /*0x10*/;
    this.checkBox1.Text = "使用正则表达式";
    this.checkBox1.UseVisualStyleBackColor = true;
    this.textBox4.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
    this.textBox4.Font = new Font("宋体", 10.5f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.textBox4.Location = new Point(514, 27);
    this.textBox4.Name = "textBox4";
    this.textBox4.Size = new Size(227, 23);
    this.textBox4.TabIndex = 17;
    this.userButton_source.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
    this.userButton_source.BackColor = Color.Transparent;
    this.userButton_source.Font = new Font("微软雅黑", 9f);
    this.userButton_source.Location = new Point(650, 513);
    this.userButton_source.Margin = new Padding(3, 4, 3, 4);
    this.userButton_source.Name = "userButton_source";
    this.userButton_source.Size = new Size(98, 25);
    this.userButton_source.TabIndex = 13;
    this.userButton_source.Text = "源日志";
    this.userButton_source.UseVisualStyleBackColor = false;
    this.userButton_source.Click += new EventHandler(this.userButton_source_Click);
    this.userButton_All.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
    this.userButton_All.BackColor = Color.Transparent;
    this.userButton_All.Font = new Font("微软雅黑", 9f);
    this.userButton_All.Location = new Point(525, 513);
    this.userButton_All.Margin = new Padding(3, 4, 3, 4);
    this.userButton_All.Name = "userButton_All";
    this.userButton_All.Size = new Size(98, 25);
    this.userButton_All.TabIndex = 11;
    this.userButton_All.Text = "全部";
    this.userButton_All.UseVisualStyleBackColor = false;
    this.userButton_All.Click += new EventHandler(this.userButton_All_Click);
    this.userButton_Fatal.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
    this.userButton_Fatal.BackColor = Color.Transparent;
    this.userButton_Fatal.Font = new Font("微软雅黑", 9f);
    this.userButton_Fatal.Location = new Point(422, 513);
    this.userButton_Fatal.Margin = new Padding(3, 4, 3, 4);
    this.userButton_Fatal.Name = "userButton_Fatal";
    this.userButton_Fatal.Size = new Size(98, 25);
    this.userButton_Fatal.TabIndex = 10;
    this.userButton_Fatal.Text = "致命";
    this.userButton_Fatal.UseVisualStyleBackColor = false;
    this.userButton_Fatal.Click += new EventHandler(this.userButton_Fatal_Click);
    this.userButton_Error.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
    this.userButton_Error.BackColor = Color.Transparent;
    this.userButton_Error.Font = new Font("微软雅黑", 9f);
    this.userButton_Error.Location = new Point(318, 513);
    this.userButton_Error.Margin = new Padding(3, 4, 3, 4);
    this.userButton_Error.Name = "userButton_Error";
    this.userButton_Error.Size = new Size(98, 25);
    this.userButton_Error.TabIndex = 9;
    this.userButton_Error.Text = "错误";
    this.userButton_Error.UseVisualStyleBackColor = false;
    this.userButton_Error.Click += new EventHandler(this.userButton_Error_Click);
    this.userButton_Warn.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
    this.userButton_Warn.BackColor = Color.Transparent;
    this.userButton_Warn.Font = new Font("微软雅黑", 9f);
    this.userButton_Warn.Location = new Point(214, 513);
    this.userButton_Warn.Margin = new Padding(3, 4, 3, 4);
    this.userButton_Warn.Name = "userButton_Warn";
    this.userButton_Warn.Size = new Size(98, 25);
    this.userButton_Warn.TabIndex = 8;
    this.userButton_Warn.Text = "警告";
    this.userButton_Warn.UseVisualStyleBackColor = false;
    this.userButton_Warn.Click += new EventHandler(this.userButton_Warn_Click);
    this.userButton_Info.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
    this.userButton_Info.BackColor = Color.Transparent;
    this.userButton_Info.Font = new Font("微软雅黑", 9f);
    this.userButton_Info.Location = new Point(110, 513);
    this.userButton_Info.Margin = new Padding(3, 4, 3, 4);
    this.userButton_Info.Name = "userButton_Info";
    this.userButton_Info.Size = new Size(98, 25);
    this.userButton_Info.TabIndex = 7;
    this.userButton_Info.Text = "信息";
    this.userButton_Info.UseVisualStyleBackColor = false;
    this.userButton_Info.Click += new EventHandler(this.userButton_Info_Click);
    this.userButton_Debug.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
    this.userButton_Debug.BackColor = Color.Transparent;
    this.userButton_Debug.Font = new Font("微软雅黑", 9f);
    this.userButton_Debug.Location = new Point(6, 513);
    this.userButton_Debug.Margin = new Padding(3, 4, 3, 4);
    this.userButton_Debug.Name = "userButton_Debug";
    this.userButton_Debug.Size = new Size(98, 25);
    this.userButton_Debug.TabIndex = 6;
    this.userButton_Debug.Text = "调试";
    this.userButton_Debug.UseVisualStyleBackColor = false;
    this.userButton_Debug.Click += new EventHandler(this.userButton_Debug_Click);
    this.checkBox2.AutoSize = true;
    this.checkBox2.Font = new Font("微软雅黑", 9f, FontStyle.Regular, GraphicsUnit.Point, (byte) 134);
    this.checkBox2.Location = new Point(669, 4);
    this.checkBox2.Name = "checkBox2";
    this.checkBox2.Size = new Size(67, 21);
    this.checkBox2.TabIndex = 18;
    this.checkBox2.Text = "\\0 转义";
    this.checkBox2.UseVisualStyleBackColor = true;
    this.AutoScaleMode = AutoScaleMode.None;
    this.Controls.Add((Control) this.checkBox2);
    this.Controls.Add((Control) this.textBox4);
    this.Controls.Add((Control) this.checkBox1);
    this.Controls.Add((Control) this.tabControl1);
    this.Controls.Add((Control) this.userButton_source);
    this.Controls.Add((Control) this.label2);
    this.Controls.Add((Control) this.userButton_All);
    this.Controls.Add((Control) this.userButton_Fatal);
    this.Controls.Add((Control) this.userButton_Error);
    this.Controls.Add((Control) this.userButton_Warn);
    this.Controls.Add((Control) this.userButton_Info);
    this.Controls.Add((Control) this.userButton_Debug);
    this.Controls.Add((Control) this.textBox3);
    this.Controls.Add((Control) this.label1);
    this.Controls.Add((Control) this.textBox2);
    this.Name = nameof (LogNetAnalysisControl);
    this.Size = new Size(752, 542);
    this.Load += new EventHandler(this.LogNetAnalysisControl_Load);
    this.tabControl1.ResumeLayout(false);
    this.tabPage1.ResumeLayout(false);
    this.tabPage1.PerformLayout();
    this.tabPage2.ResumeLayout(false);
    ((ISupportInitialize) this.pictureBox1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  private class PaintItem
  {
    public DateTime Start { get; set; }

    public DateTime End { get; set; }

    public int Count { get; set; }
  }
}
