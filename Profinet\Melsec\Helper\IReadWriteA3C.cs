﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.Helper.IReadWriteA3C
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Profinet.Melsec.Helper;

/// <summary>
/// 三菱的A3C协议类接口对象，具有站号，是否和校验的属性<br />
/// Mitsubishi's A3C protocol interface object, which has the attributes of station number, and checksum
/// </summary>
public interface IReadWriteA3C : IReadWriteDevice, IReadWriteNet
{
  /// <summary>
  /// 当前A3C协议的站编号信息<br />
  /// Station number information of the current A3C protocol
  /// </summary>
  byte Station { get; set; }

  /// <summary>
  /// 当前的A3C协议是否使用和校验，默认使用<br />
  /// Whether the current A3C protocol uses sum check, it is used by default
  /// </summary>
  bool SumCheck { get; set; }

  /// <summary>
  /// 当前的A3C协议的格式信息，可选格式1，2，3，4，默认格式1<br />
  /// Format information of the current A3C protocol, optional format 1, 2, 3, 4, default format 1
  /// </summary>
  int Format { get; set; }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.Helper.IReadWriteMc.EnableWriteBitToWordRegister" />
  bool EnableWriteBitToWordRegister { get; set; }
}
