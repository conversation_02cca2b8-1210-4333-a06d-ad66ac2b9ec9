﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Freedom.FreedomSerial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Reflection;
using System;

#nullable disable
namespace HslCommunication.Profinet.Freedom;

/// <summary>
/// 基于串口的自由协议，需要在地址里传入报文信息，也可以传入数据偏移信息，<see cref="P:HslCommunication.Core.Device.DeviceCommunication.ByteTransform" />默认为<see cref="T:HslCommunication.Core.RegularByteTransform" />
/// </summary>
/// <example>
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\FreedomExample.cs" region="Sample5" title="实例化" />
/// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Profinet\FreedomExample.cs" region="Sample6" title="读取" />
/// </example>
public class FreedomSerial : DeviceSerialPort
{
  /// <summary>实例化一个默认的对象</summary>
  public FreedomSerial() => this.ByteTransform = (IByteTransform) new RegularByteTransform();

  /// <inheritdoc cref="P:HslCommunication.Profinet.Freedom.FreedomTcpNet.CheckResponseStatus" />
  public Func<byte[], byte[], OperateResult> CheckResponseStatus { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Freedom.FreedomTcpNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "特殊的地址格式，需要采用解析包起始地址的报文，例如 modbus 协议为 stx=9;00 00 00 00 00 06 01 03 00 64 00 01")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    int parameter = HslHelper.ExtractParameter(ref address, "stx", 0);
    byte[] hexBytes = address.ToHexBytes();
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(hexBytes);
    if (!operateResult.IsSuccess)
      return operateResult;
    if (this.CheckResponseStatus != null)
    {
      OperateResult result = this.CheckResponseStatus(hexBytes, operateResult.Content);
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<byte[]>(result);
    }
    return parameter >= operateResult.Content.Length ? new OperateResult<byte[]>(StringResources.Language.ReceiveDataLengthTooShort) : OperateResult.CreateSuccessResult<byte[]>(operateResult.Content.RemoveBegin<byte>(parameter));
  }

  /// <inheritdoc />
  public override OperateResult Write(string address, byte[] value)
  {
    return (OperateResult) this.Read(address, (ushort) 0);
  }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"FreedomSerial<{this.ByteTransform.GetType()}>[{this.PortName}:{this.BaudRate}]";
  }
}
