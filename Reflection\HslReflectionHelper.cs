﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Reflection.HslReflectionHelper
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Enthernet.Redis;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Reflection;

/// <summary>反射的辅助类</summary>
public class HslReflectionHelper
{
  /// <summary>从属性中获取对应的设备类型的地址特性信息</summary>
  /// <param name="deviceType">设备类型信息</param>
  /// <param name="property">属性信息</param>
  /// <returns>设备类型信息</returns>
  public static HslDeviceAddressAttribute GetHslDeviceAddressAttribute(
    Type deviceType,
    PropertyInfo property)
  {
    object[] customAttributes = property.GetCustomAttributes(typeof (HslDeviceAddressAttribute), false);
    if (customAttributes == null)
      return (HslDeviceAddressAttribute) null;
    HslDeviceAddressAttribute addressAttribute1 = (HslDeviceAddressAttribute) null;
    for (int index = 0; index < customAttributes.Length; ++index)
    {
      HslDeviceAddressAttribute addressAttribute2 = (HslDeviceAddressAttribute) customAttributes[index];
      if (addressAttribute2.DeviceType != (Type) null && addressAttribute2.DeviceType == deviceType)
      {
        addressAttribute1 = addressAttribute2;
        break;
      }
    }
    if (addressAttribute1 == null)
    {
      for (int index = 0; index < customAttributes.Length; ++index)
      {
        HslDeviceAddressAttribute addressAttribute3 = (HslDeviceAddressAttribute) customAttributes[index];
        if (addressAttribute3.DeviceType == (Type) null)
        {
          addressAttribute1 = addressAttribute3;
          break;
        }
      }
    }
    return addressAttribute1;
  }

  /// <inheritdoc cref="M:HslCommunication.Reflection.HslReflectionHelper.GetHslDeviceAddressAttribute(System.Type,System.Reflection.PropertyInfo)" />
  public static HslDeviceAddressAttribute[] GetHslDeviceAddressAttributeArray(
    Type deviceType,
    PropertyInfo property)
  {
    object[] customAttributes = property.GetCustomAttributes(typeof (HslDeviceAddressAttribute), false);
    if (customAttributes == null)
      return (HslDeviceAddressAttribute[]) null;
    List<HslDeviceAddressAttribute> addressAttributeList = new List<HslDeviceAddressAttribute>();
    for (int index = 0; index < customAttributes.Length; ++index)
    {
      HslDeviceAddressAttribute addressAttribute = (HslDeviceAddressAttribute) customAttributes[index];
      if (addressAttribute.DeviceType != (Type) null && addressAttribute.DeviceType == deviceType)
        addressAttributeList.Add(addressAttribute);
    }
    if (addressAttributeList.Count == 0)
    {
      for (int index = 0; index < customAttributes.Length; ++index)
      {
        HslDeviceAddressAttribute addressAttribute = (HslDeviceAddressAttribute) customAttributes[index];
        if (addressAttribute.DeviceType == (Type) null)
          addressAttributeList.Add(addressAttribute);
      }
    }
    return addressAttributeList.ToArray();
  }

  /// <summary>
  /// 从设备里读取支持Hsl特性的数据内容，该特性为<see cref="T:HslCommunication.Reflection.HslDeviceAddressAttribute" />，详细参考论坛的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="readWrite">读写接口的实现</param>
  /// <returns>包含是否成功的结果对象</returns>
  public static OperateResult<T> Read<T>(IReadWriteNet readWrite) where T : class, new()
  {
    Type type = typeof (T);
    object instance = type.Assembly.CreateInstance(type.FullName);
    foreach (PropertyInfo property in type.GetProperties(BindingFlags.Instance | BindingFlags.Public))
    {
      Type propertyType = property.PropertyType;
      if (propertyType == typeof (string[]))
      {
        HslDeviceAddressAttribute[] addressAttributeArray = HslReflectionHelper.GetHslDeviceAddressAttributeArray(readWrite.GetType(), property);
        if (addressAttributeArray != null && addressAttributeArray.Length != 0)
        {
          string[] strArray = new string[addressAttributeArray.Length];
          for (int index = 0; index < addressAttributeArray.Length; ++index)
          {
            OperateResult<string> result = readWrite.ReadString(addressAttributeArray[index].Address, addressAttributeArray[index].Length >= 0 ? (ushort) addressAttributeArray[index].Length : (ushort) 1, addressAttributeArray[index].GetEncoding());
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            strArray[index] = result.Content;
          }
          property.SetValue(instance, (object) strArray, (object[]) null);
        }
      }
      else
      {
        HslDeviceAddressAttribute addressAttribute = HslReflectionHelper.GetHslDeviceAddressAttribute(readWrite.GetType(), property);
        if (addressAttribute != null)
        {
          if (propertyType == typeof (byte))
          {
            MethodInfo method = readWrite.GetType().GetMethod("ReadByte", new Type[1]
            {
              typeof (string)
            });
            if (method == (MethodInfo) null)
              return new OperateResult<T>(readWrite.GetType().Name + " not support read byte value. ");
            OperateResult<byte> result = (OperateResult<byte>) method.Invoke((object) readWrite, new object[1]
            {
              (object) addressAttribute.Address
            });
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (short))
          {
            OperateResult<short> result = readWrite.ReadInt16(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (short[]))
          {
            OperateResult<short[]> result = readWrite.ReadInt16(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (ushort))
          {
            OperateResult<ushort> result = readWrite.ReadUInt16(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (ushort[]))
          {
            OperateResult<ushort[]> result = readWrite.ReadUInt16(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (int))
          {
            OperateResult<int> result = readWrite.ReadInt32(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (int[]))
          {
            OperateResult<int[]> result = readWrite.ReadInt32(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (uint))
          {
            OperateResult<uint> result = readWrite.ReadUInt32(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (uint[]))
          {
            OperateResult<uint[]> result = readWrite.ReadUInt32(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (long))
          {
            OperateResult<long> result = readWrite.ReadInt64(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (long[]))
          {
            OperateResult<long[]> result = readWrite.ReadInt64(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (ulong))
          {
            OperateResult<ulong> result = readWrite.ReadUInt64(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (ulong[]))
          {
            OperateResult<ulong[]> result = readWrite.ReadUInt64(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (float))
          {
            OperateResult<float> result = readWrite.ReadFloat(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (float[]))
          {
            OperateResult<float[]> result = readWrite.ReadFloat(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (double))
          {
            OperateResult<double> result = readWrite.ReadDouble(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (double[]))
          {
            OperateResult<double[]> result = readWrite.ReadDouble(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (string))
          {
            OperateResult<string> result = readWrite.ReadString(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1, addressAttribute.GetEncoding());
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (byte[]))
          {
            OperateResult<byte[]> result = readWrite.Read(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (bool))
          {
            OperateResult<bool> result = readWrite.ReadBool(addressAttribute.Address);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
          else if (propertyType == typeof (bool[]))
          {
            OperateResult<bool[]> result = readWrite.ReadBool(addressAttribute.Address, addressAttribute.Length >= 0 ? (ushort) addressAttribute.Length : (ushort) 1);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            property.SetValue(instance, (object) result.Content, (object[]) null);
          }
        }
      }
    }
    return OperateResult.CreateSuccessResult<T>((T) instance);
  }

  /// <summary>
  /// 从设备里读取支持Hsl特性的数据内容，该特性为<see cref="T:HslCommunication.Reflection.HslDeviceAddressAttribute" />，详细参考论坛的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="data">自定义的数据对象</param>
  /// <param name="readWrite">数据读写对象</param>
  /// <returns>包含是否成功的结果对象</returns>
  /// <exception cref="T:System.ArgumentNullException"></exception>
  public static OperateResult Write<T>(T data, IReadWriteNet readWrite) where T : class, new()
  {
    if ((object) data == null)
      throw new ArgumentNullException(nameof (data));
    Type type = typeof (T);
    T obj = data;
    foreach (PropertyInfo property in type.GetProperties(BindingFlags.Instance | BindingFlags.Public))
    {
      Type propertyType = property.PropertyType;
      if (propertyType == typeof (string[]))
      {
        HslDeviceAddressAttribute[] addressAttributeArray = HslReflectionHelper.GetHslDeviceAddressAttributeArray(readWrite.GetType(), property);
        if (addressAttributeArray != null && addressAttributeArray.Length != 0)
        {
          string[] strArray = (string[]) property.GetValue((object) obj, (object[]) null);
          for (int index = 0; index < addressAttributeArray.Length; ++index)
          {
            OperateResult operateResult = readWrite.Write(addressAttributeArray[index].Address, strArray[index], addressAttributeArray[index].GetEncoding());
            if (!operateResult.IsSuccess)
              return operateResult;
          }
        }
      }
      else
      {
        HslDeviceAddressAttribute addressAttribute = HslReflectionHelper.GetHslDeviceAddressAttribute(readWrite.GetType(), property);
        if (addressAttribute != null)
        {
          if (propertyType == typeof (byte))
          {
            MethodInfo method = readWrite.GetType().GetMethod(nameof (Write), new Type[2]
            {
              typeof (string),
              typeof (byte)
            });
            if (method == (MethodInfo) null)
              return (OperateResult) new OperateResult<T>(readWrite.GetType().Name + " not support write byte value. ");
            byte num = (byte) property.GetValue((object) obj, (object[]) null);
            OperateResult result = (OperateResult) method.Invoke((object) readWrite, new object[2]
            {
              (object) addressAttribute.Address,
              (object) num
            });
            if (!result.IsSuccess)
              return (OperateResult) OperateResult.CreateFailedResult<T>(result);
          }
          else if (propertyType == typeof (short))
          {
            short num = (short) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (short[]))
          {
            short[] values = (short[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (ushort))
          {
            ushort num = (ushort) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (ushort[]))
          {
            ushort[] values = (ushort[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (int))
          {
            int num = (int) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (int[]))
          {
            int[] values = (int[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (uint))
          {
            uint num = (uint) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (uint[]))
          {
            uint[] values = (uint[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (long))
          {
            long num = (long) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (long[]))
          {
            long[] values = (long[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (ulong))
          {
            ulong num = (ulong) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (ulong[]))
          {
            ulong[] values = (ulong[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (float))
          {
            float num = (float) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (float[]))
          {
            float[] values = (float[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (double))
          {
            double num = (double) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, num);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (double[]))
          {
            double[] values = (double[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, values);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (string))
          {
            string str = (string) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, str, addressAttribute.GetEncoding());
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (byte[]))
          {
            byte[] numArray = (byte[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, numArray);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (bool))
          {
            bool flag = (bool) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, flag);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
          else if (propertyType == typeof (bool[]))
          {
            bool[] flagArray = (bool[]) property.GetValue((object) obj, (object[]) null);
            OperateResult operateResult = readWrite.Write(addressAttribute.Address, flagArray);
            if (!operateResult.IsSuccess)
              return operateResult;
          }
        }
      }
    }
    return (OperateResult) OperateResult.CreateSuccessResult<T>(obj);
  }

  /// <summary>
  /// 根据类型信息，直接从原始字节解析出类型对象，然后赋值给对应的对象，该对象的属性需要支持特性 <see cref="T:HslCommunication.Reflection.HslStructAttribute" /> 才支持设置
  /// </summary>
  /// <typeparam name="T">类型信息</typeparam>
  /// <param name="buffer">缓存信息</param>
  /// <param name="startIndex">起始偏移地址</param>
  /// <param name="byteTransform">数据变换规则对象</param>
  /// <returns>新的实例化的类型对象</returns>
  public static T PraseStructContent<T>(
    byte[] buffer,
    int startIndex,
    IByteTransform byteTransform)
    where T : class, new()
  {
    Type type = typeof (T);
    object instance = type.Assembly.CreateInstance(type.FullName);
    HslReflectionHelper.PraseStructContent(instance, buffer, startIndex, byteTransform);
    return (T) instance;
  }

  /// <summary>
  /// 根据结构体的定义，将原始字节的数据解析出来，然后赋值给对应的对象，该对象的属性需要支持特性 <see cref="T:HslCommunication.Reflection.HslStructAttribute" /> 才支持设置
  /// </summary>
  /// <param name="obj">类型对象信息</param>
  /// <param name="buffer">读取的缓存数据信息</param>
  /// <param name="startIndex">起始的偏移地址</param>
  /// <param name="byteTransform">数据变换规则对象</param>
  public static void PraseStructContent(
    object obj,
    byte[] buffer,
    int startIndex,
    IByteTransform byteTransform)
  {
    foreach (PropertyInfo property in obj.GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public))
    {
      object[] customAttributes = property.GetCustomAttributes(typeof (HslStructAttribute), false);
      if (customAttributes != null)
      {
        HslStructAttribute hslStructAttribute = customAttributes.Length != 0 ? (HslStructAttribute) customAttributes[0] : (HslStructAttribute) null;
        if (hslStructAttribute != null)
        {
          Type propertyType = property.PropertyType;
          if (propertyType == typeof (byte))
            property.SetValue(obj, (object) buffer[startIndex + hslStructAttribute.Index], (object[]) null);
          else if (propertyType == typeof (byte[]))
            property.SetValue(obj, (object) buffer.SelectMiddle<byte>(startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (short))
            property.SetValue(obj, (object) byteTransform.TransInt16(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (short[]))
            property.SetValue(obj, (object) byteTransform.TransInt16(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (ushort))
            property.SetValue(obj, (object) byteTransform.TransUInt16(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (ushort[]))
            property.SetValue(obj, (object) byteTransform.TransUInt16(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (int))
            property.SetValue(obj, (object) byteTransform.TransInt32(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (int[]))
            property.SetValue(obj, (object) byteTransform.TransInt32(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (uint))
            property.SetValue(obj, (object) byteTransform.TransUInt32(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (uint[]))
            property.SetValue(obj, (object) byteTransform.TransUInt32(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (long))
            property.SetValue(obj, (object) byteTransform.TransInt64(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (long[]))
            property.SetValue(obj, (object) byteTransform.TransInt64(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (ulong))
            property.SetValue(obj, (object) byteTransform.TransUInt64(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (ulong[]))
            property.SetValue(obj, (object) byteTransform.TransUInt64(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (float))
            property.SetValue(obj, (object) byteTransform.TransSingle(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (float[]))
            property.SetValue(obj, (object) byteTransform.TransSingle(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (double))
            property.SetValue(obj, (object) byteTransform.TransDouble(buffer, startIndex + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (double[]))
            property.SetValue(obj, (object) byteTransform.TransDouble(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length), (object[]) null);
          else if (propertyType == typeof (string))
          {
            Encoding utF8 = Encoding.UTF8;
            Encoding encoding = !string.IsNullOrEmpty(hslStructAttribute.Encoding) ? (!hslStructAttribute.Encoding.Equals("ASCII", StringComparison.OrdinalIgnoreCase) ? (!hslStructAttribute.Encoding.Equals("UNICODE", StringComparison.OrdinalIgnoreCase) ? (!hslStructAttribute.Encoding.Equals("ANSI", StringComparison.OrdinalIgnoreCase) ? (!hslStructAttribute.Encoding.Equals("UTF8", StringComparison.OrdinalIgnoreCase) ? (!hslStructAttribute.Encoding.Equals("BIG-UNICODE", StringComparison.OrdinalIgnoreCase) ? (!hslStructAttribute.Encoding.Equals("GB2312", StringComparison.OrdinalIgnoreCase) ? Encoding.GetEncoding(hslStructAttribute.Encoding) : Encoding.GetEncoding("GB2312")) : Encoding.BigEndianUnicode) : Encoding.UTF8) : Encoding.Default) : Encoding.Unicode) : Encoding.ASCII) : Encoding.ASCII;
            if (hslStructAttribute.StringMode == 0)
              property.SetValue(obj, (object) byteTransform.TransString(buffer, startIndex + hslStructAttribute.Index, hslStructAttribute.Length, encoding), (object[]) null);
            else if (hslStructAttribute.StringMode == 1)
              property.SetValue(obj, (object) byteTransform.TransString(buffer, startIndex + hslStructAttribute.Index + 1, (int) buffer[startIndex + hslStructAttribute.Index], encoding), (object[]) null);
            else if (hslStructAttribute.StringMode == 2)
              property.SetValue(obj, (object) byteTransform.TransString(buffer, startIndex + hslStructAttribute.Index + 2, (int) buffer[startIndex + hslStructAttribute.Index + 1], encoding), (object[]) null);
            else if (hslStructAttribute.StringMode == 3)
              property.SetValue(obj, (object) byteTransform.TransString(buffer, startIndex + hslStructAttribute.Index + 2, (int) byteTransform.TransUInt16(buffer, startIndex + hslStructAttribute.Index), encoding), (object[]) null);
            else if (hslStructAttribute.StringMode == 4)
              property.SetValue(obj, (object) byteTransform.TransString(buffer, startIndex + hslStructAttribute.Index + 4, (int) byteTransform.TransUInt16(buffer, startIndex + hslStructAttribute.Index + 2), encoding), (object[]) null);
            else if (hslStructAttribute.StringMode == 5)
              property.SetValue(obj, (object) byteTransform.TransString(buffer, startIndex + hslStructAttribute.Index + 4, byteTransform.TransInt32(buffer, startIndex + hslStructAttribute.Index), encoding), (object[]) null);
          }
          else if (propertyType == typeof (bool))
            property.SetValue(obj, (object) buffer.GetBoolByIndex(startIndex * 8 + hslStructAttribute.Index), (object[]) null);
          else if (propertyType == typeof (bool[]))
          {
            bool[] flagArray = new bool[hslStructAttribute.Length];
            for (int index = 0; index < flagArray.Length; ++index)
              flagArray[index] = buffer.GetBoolByIndex(startIndex * 8 + hslStructAttribute.Index + index);
            property.SetValue(obj, (object) flagArray, (object[]) null);
          }
          else
          {
            object instance = propertyType.Assembly.CreateInstance(propertyType.FullName);
            HslReflectionHelper.PraseStructContent(instance, buffer, startIndex + hslStructAttribute.Index, byteTransform);
            property.SetValue(obj, instance, (object[]) null);
          }
        }
      }
    }
  }

  /// <summary>使用表达式树的方式来给一个属性赋值</summary>
  /// <param name="propertyInfo">属性信息</param>
  /// <param name="obj">对象信息</param>
  /// <param name="objValue">实际的值</param>
  public static void SetPropertyExp<T, K>(PropertyInfo propertyInfo, T obj, K objValue)
  {
    ParameterExpression instance = Expression.Parameter(typeof (T), nameof (obj));
    ParameterExpression parameterExpression = Expression.Parameter(propertyInfo.PropertyType, nameof (objValue));
    Expression.Lambda<Action<T, K>>((Expression) Expression.Call((Expression) instance, propertyInfo.GetSetMethod(), (Expression) parameterExpression), instance, parameterExpression).Compile()(obj, objValue);
  }

  /// <summary>
  /// 从设备里读取支持Hsl特性的数据内容，该特性为<see cref="T:HslCommunication.Reflection.HslDeviceAddressAttribute" />，详细参考论坛的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="readWrite">读写接口的实现</param>
  /// <returns>包含是否成功的结果对象</returns>
  public static async Task<OperateResult<T>> ReadAsync<T>(IReadWriteNet readWrite) where T : class, new()
  {
    Type type = typeof (T);
    object obj = type.Assembly.CreateInstance(type.FullName);
    PropertyInfo[] propertyInfoArray = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
    for (int index = 0; index < propertyInfoArray.Length; ++index)
    {
      PropertyInfo property = propertyInfoArray[index];
      Type propertyType = property.PropertyType;
      if (propertyType == typeof (string[]))
      {
        HslDeviceAddressAttribute[] hslAttributes = HslReflectionHelper.GetHslDeviceAddressAttributeArray(readWrite.GetType(), property);
        if (hslAttributes != null && hslAttributes.Length != 0)
        {
          string[] strings = new string[hslAttributes.Length];
          for (int i = 0; i < hslAttributes.Length; ++i)
          {
            OperateResult<string> valueResult = await readWrite.ReadStringAsync(hslAttributes[i].Address, hslAttributes[i].Length >= 0 ? (ushort) hslAttributes[i].Length : (ushort) 1, hslAttributes[i].GetEncoding());
            if (!valueResult.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
            strings[i] = valueResult.Content;
            valueResult = (OperateResult<string>) null;
          }
          property.SetValue(obj, (object) strings, (object[]) null);
          hslAttributes = (HslDeviceAddressAttribute[]) null;
          strings = (string[]) null;
        }
        else
          continue;
      }
      else
      {
        object[] attribute = property.GetCustomAttributes(typeof (HslDeviceAddressAttribute), false);
        if (attribute != null)
        {
          HslDeviceAddressAttribute hslAttribute = HslReflectionHelper.GetHslDeviceAddressAttribute(readWrite.GetType(), property);
          if (hslAttribute != null)
          {
            if (propertyType == typeof (byte))
            {
              MethodInfo readByteMethod = readWrite.GetType().GetMethod("ReadByteAsync", new Type[1]
              {
                typeof (string)
              });
              if (readByteMethod == (MethodInfo) null)
                return new OperateResult<T>(readWrite.GetType().Name + " not support read byte value. ");
              if (!(readByteMethod.Invoke((object) readWrite, new object[1]
              {
                (object) hslAttribute.Address
              }) is Task readByteTask))
                return new OperateResult<T>(readWrite.GetType().Name + " not task type result. ");
              await readByteTask;
              OperateResult<byte> valueResult = readByteTask.GetType().GetProperty("Result").GetValue((object) readByteTask, (object[]) null) as OperateResult<byte>;
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              readByteMethod = (MethodInfo) null;
              readByteTask = (Task) null;
              valueResult = (OperateResult<byte>) null;
            }
            else if (propertyType == typeof (short))
            {
              OperateResult<short> valueResult = await readWrite.ReadInt16Async(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<short>) null;
            }
            else if (propertyType == typeof (short[]))
            {
              OperateResult<short[]> valueResult = await readWrite.ReadInt16Async(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<short[]>) null;
            }
            else if (propertyType == typeof (ushort))
            {
              OperateResult<ushort> valueResult = await readWrite.ReadUInt16Async(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<ushort>) null;
            }
            else if (propertyType == typeof (ushort[]))
            {
              OperateResult<ushort[]> valueResult = await readWrite.ReadUInt16Async(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<ushort[]>) null;
            }
            else if (propertyType == typeof (int))
            {
              OperateResult<int> valueResult = await readWrite.ReadInt32Async(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<int>) null;
            }
            else if (propertyType == typeof (int[]))
            {
              OperateResult<int[]> valueResult = await readWrite.ReadInt32Async(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<int[]>) null;
            }
            else if (propertyType == typeof (uint))
            {
              OperateResult<uint> valueResult = await readWrite.ReadUInt32Async(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<uint>) null;
            }
            else if (propertyType == typeof (uint[]))
            {
              OperateResult<uint[]> valueResult = await readWrite.ReadUInt32Async(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<uint[]>) null;
            }
            else if (propertyType == typeof (long))
            {
              OperateResult<long> valueResult = await readWrite.ReadInt64Async(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<long>) null;
            }
            else if (propertyType == typeof (long[]))
            {
              OperateResult<long[]> valueResult = await readWrite.ReadInt64Async(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<long[]>) null;
            }
            else if (propertyType == typeof (ulong))
            {
              OperateResult<ulong> valueResult = await readWrite.ReadUInt64Async(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<ulong>) null;
            }
            else if (propertyType == typeof (ulong[]))
            {
              OperateResult<ulong[]> valueResult = await readWrite.ReadUInt64Async(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<ulong[]>) null;
            }
            else if (propertyType == typeof (float))
            {
              OperateResult<float> valueResult = await readWrite.ReadFloatAsync(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<float>) null;
            }
            else if (propertyType == typeof (float[]))
            {
              OperateResult<float[]> valueResult = await readWrite.ReadFloatAsync(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<float[]>) null;
            }
            else if (propertyType == typeof (double))
            {
              OperateResult<double> valueResult = await readWrite.ReadDoubleAsync(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<double>) null;
            }
            else if (propertyType == typeof (double[]))
            {
              OperateResult<double[]> valueResult = await readWrite.ReadDoubleAsync(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<double[]>) null;
            }
            else if (propertyType == typeof (string))
            {
              OperateResult<string> valueResult = await readWrite.ReadStringAsync(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1, hslAttribute.GetEncoding());
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<string>) null;
            }
            else if (propertyType == typeof (byte[]))
            {
              OperateResult<byte[]> valueResult = await readWrite.ReadAsync(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<byte[]>) null;
            }
            else if (propertyType == typeof (bool))
            {
              OperateResult<bool> valueResult = await readWrite.ReadBoolAsync(hslAttribute.Address);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<bool>) null;
            }
            else if (propertyType == typeof (bool[]))
            {
              OperateResult<bool[]> valueResult = await readWrite.ReadBoolAsync(hslAttribute.Address, hslAttribute.Length >= 0 ? (ushort) hslAttribute.Length : (ushort) 1);
              if (!valueResult.IsSuccess)
                return OperateResult.CreateFailedResult<T>((OperateResult) valueResult);
              property.SetValue(obj, (object) valueResult.Content, (object[]) null);
              valueResult = (OperateResult<bool[]>) null;
            }
            attribute = (object[]) null;
            hslAttribute = (HslDeviceAddressAttribute) null;
          }
          else
            continue;
        }
        else
          continue;
      }
      propertyType = (Type) null;
      property = (PropertyInfo) null;
    }
    propertyInfoArray = (PropertyInfo[]) null;
    return OperateResult.CreateSuccessResult<T>((T) obj);
  }

  /// <summary>
  /// 从设备里读取支持Hsl特性的数据内容，该特性为<see cref="T:HslCommunication.Reflection.HslDeviceAddressAttribute" />，详细参考论坛的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="data">自定义的数据对象</param>
  /// <param name="readWrite">数据读写对象</param>
  /// <returns>包含是否成功的结果对象</returns>
  /// <exception cref="T:System.ArgumentNullException"></exception>
  public static async Task<OperateResult> WriteAsync<T>(T data, IReadWriteNet readWrite) where T : class, new()
  {
    if ((object) (T) data == null)
      throw new ArgumentNullException(nameof (data));
    Type type = typeof (T);
    T obj = data;
    PropertyInfo[] propertyInfoArray = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
    for (int index = 0; index < propertyInfoArray.Length; ++index)
    {
      PropertyInfo property = propertyInfoArray[index];
      Type propertyType = property.PropertyType;
      if (propertyType == typeof (string[]))
      {
        HslDeviceAddressAttribute[] hslAttributes = HslReflectionHelper.GetHslDeviceAddressAttributeArray(readWrite.GetType(), property);
        if (hslAttributes != null && hslAttributes.Length != 0)
        {
          string[] strings = (string[]) property.GetValue((object) obj, (object[]) null);
          for (int i = 0; i < hslAttributes.Length; ++i)
          {
            OperateResult writeResult = await readWrite.WriteAsync(hslAttributes[i].Address, strings[i], hslAttributes[i].GetEncoding());
            if (!writeResult.IsSuccess)
              return writeResult;
            writeResult = (OperateResult) null;
          }
          hslAttributes = (HslDeviceAddressAttribute[]) null;
          strings = (string[]) null;
        }
        else
          continue;
      }
      else
      {
        object[] attribute = property.GetCustomAttributes(typeof (HslDeviceAddressAttribute), false);
        if (attribute != null)
        {
          HslDeviceAddressAttribute hslAttribute = HslReflectionHelper.GetHslDeviceAddressAttribute(readWrite.GetType(), property);
          if (hslAttribute != null)
          {
            if (propertyType == typeof (byte))
            {
              MethodInfo method = readWrite.GetType().GetMethod(nameof (WriteAsync), new Type[2]
              {
                typeof (string),
                typeof (byte)
              });
              if (method == (MethodInfo) null)
                return (OperateResult) new OperateResult<T>(readWrite.GetType().Name + " not support write byte value. ");
              byte value = (byte) property.GetValue((object) obj, (object[]) null);
              if (!(method.Invoke((object) readWrite, new object[2]
              {
                (object) hslAttribute.Address,
                (object) value
              }) is Task writeTask))
                return new OperateResult(readWrite.GetType().Name + " not task type result. ");
              await writeTask;
              OperateResult valueResult = writeTask.GetType().GetProperty("Result").GetValue((object) writeTask, (object[]) null) as OperateResult;
              if (!valueResult.IsSuccess)
                return (OperateResult) OperateResult.CreateFailedResult<T>(valueResult);
              method = (MethodInfo) null;
              writeTask = (Task) null;
              valueResult = (OperateResult) null;
            }
            else if (propertyType == typeof (short))
            {
              short value = (short) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (short[]))
            {
              short[] value = (short[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (short[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (ushort))
            {
              ushort value = (ushort) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (ushort[]))
            {
              ushort[] value = (ushort[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (ushort[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (int))
            {
              int value = (int) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (int[]))
            {
              int[] value = (int[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (int[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (uint))
            {
              uint value = (uint) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (uint[]))
            {
              uint[] value = (uint[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (uint[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (long))
            {
              long value = (long) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (long[]))
            {
              long[] value = (long[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (long[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (ulong))
            {
              ulong value = (ulong) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (ulong[]))
            {
              ulong[] value = (ulong[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (ulong[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (float))
            {
              float value = (float) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (float[]))
            {
              float[] value = (float[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (float[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (double))
            {
              double value = (double) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (double[]))
            {
              double[] value = (double[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (double[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (string))
            {
              string value = (string) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value, hslAttribute.GetEncoding());
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (string) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (byte[]))
            {
              byte[] value = (byte[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (byte[]) null;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (bool))
            {
              bool value = (bool) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              writeResult = (OperateResult) null;
            }
            else if (propertyType == typeof (bool[]))
            {
              bool[] value = (bool[]) property.GetValue((object) obj, (object[]) null);
              OperateResult writeResult = await readWrite.WriteAsync(hslAttribute.Address, value);
              if (!writeResult.IsSuccess)
                return writeResult;
              value = (bool[]) null;
              writeResult = (OperateResult) null;
            }
            attribute = (object[]) null;
            hslAttribute = (HslDeviceAddressAttribute) null;
          }
          else
            continue;
        }
        else
          continue;
      }
      propertyType = (Type) null;
      property = (PropertyInfo) null;
    }
    propertyInfoArray = (PropertyInfo[]) null;
    return (OperateResult) OperateResult.CreateSuccessResult<T>(obj);
  }

  internal static void SetPropertyObjectValue(PropertyInfo property, object obj, string value)
  {
    Type propertyType = property.PropertyType;
    if (propertyType == typeof (short))
      property.SetValue(obj, (object) short.Parse(value), (object[]) null);
    else if (propertyType == typeof (ushort))
      property.SetValue(obj, (object) ushort.Parse(value), (object[]) null);
    else if (propertyType == typeof (int))
      property.SetValue(obj, (object) int.Parse(value), (object[]) null);
    else if (propertyType == typeof (uint))
      property.SetValue(obj, (object) uint.Parse(value), (object[]) null);
    else if (propertyType == typeof (long))
      property.SetValue(obj, (object) long.Parse(value), (object[]) null);
    else if (propertyType == typeof (ulong))
      property.SetValue(obj, (object) ulong.Parse(value), (object[]) null);
    else if (propertyType == typeof (float))
      property.SetValue(obj, (object) float.Parse(value), (object[]) null);
    else if (propertyType == typeof (double))
      property.SetValue(obj, (object) double.Parse(value), (object[]) null);
    else if (propertyType == typeof (string))
      property.SetValue(obj, (object) value, (object[]) null);
    else if (propertyType == typeof (byte))
      property.SetValue(obj, (object) byte.Parse(value), (object[]) null);
    else if (propertyType == typeof (bool))
      property.SetValue(obj, (object) bool.Parse(value), (object[]) null);
    else
      property.SetValue(obj, (object) value, (object[]) null);
  }

  internal static void SetPropertyObjectValueArray(
    PropertyInfo property,
    object obj,
    string[] values)
  {
    Type propertyType = property.PropertyType;
    if (propertyType == typeof (short[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, short>((Func<string, short>) (m => short.Parse(m))).ToArray<short>(), (object[]) null);
    else if (propertyType == typeof (List<short>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, short>((Func<string, short>) (m => short.Parse(m))).ToList<short>(), (object[]) null);
    else if (propertyType == typeof (ushort[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, ushort>((Func<string, ushort>) (m => ushort.Parse(m))).ToArray<ushort>(), (object[]) null);
    else if (propertyType == typeof (List<ushort>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, ushort>((Func<string, ushort>) (m => ushort.Parse(m))).ToList<ushort>(), (object[]) null);
    else if (propertyType == typeof (int[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, int>((Func<string, int>) (m => int.Parse(m))).ToArray<int>(), (object[]) null);
    else if (propertyType == typeof (List<int>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, int>((Func<string, int>) (m => int.Parse(m))).ToList<int>(), (object[]) null);
    else if (propertyType == typeof (uint[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, uint>((Func<string, uint>) (m => uint.Parse(m))).ToArray<uint>(), (object[]) null);
    else if (propertyType == typeof (List<uint>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, uint>((Func<string, uint>) (m => uint.Parse(m))).ToList<uint>(), (object[]) null);
    else if (propertyType == typeof (long[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, long>((Func<string, long>) (m => long.Parse(m))).ToArray<long>(), (object[]) null);
    else if (propertyType == typeof (List<long>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, long>((Func<string, long>) (m => long.Parse(m))).ToList<long>(), (object[]) null);
    else if (propertyType == typeof (ulong[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, ulong>((Func<string, ulong>) (m => ulong.Parse(m))).ToArray<ulong>(), (object[]) null);
    else if (propertyType == typeof (List<ulong>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, ulong>((Func<string, ulong>) (m => ulong.Parse(m))).ToList<ulong>(), (object[]) null);
    else if (propertyType == typeof (float[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, float>((Func<string, float>) (m => float.Parse(m))).ToArray<float>(), (object[]) null);
    else if (propertyType == typeof (List<float>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, float>((Func<string, float>) (m => float.Parse(m))).ToList<float>(), (object[]) null);
    else if (propertyType == typeof (double[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, double>((Func<string, double>) (m => double.Parse(m))).ToArray<double>(), (object[]) null);
    else if (propertyType == typeof (double[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, double>((Func<string, double>) (m => double.Parse(m))).ToList<double>(), (object[]) null);
    else if (propertyType == typeof (string[]))
      property.SetValue(obj, (object) values, (object[]) null);
    else if (propertyType == typeof (List<string>))
      property.SetValue(obj, (object) new List<string>((IEnumerable<string>) values), (object[]) null);
    else if (propertyType == typeof (byte[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, byte>((Func<string, byte>) (m => byte.Parse(m))).ToArray<byte>(), (object[]) null);
    else if (propertyType == typeof (List<byte>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, byte>((Func<string, byte>) (m => byte.Parse(m))).ToList<byte>(), (object[]) null);
    else if (propertyType == typeof (bool[]))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, bool>((Func<string, bool>) (m => bool.Parse(m))).ToArray<bool>(), (object[]) null);
    else if (propertyType == typeof (List<bool>))
      property.SetValue(obj, (object) ((IEnumerable<string>) values).Select<string, bool>((Func<string, bool>) (m => bool.Parse(m))).ToList<bool>(), (object[]) null);
    else
      property.SetValue(obj, (object) values, (object[]) null);
  }

  /// <summary>
  /// 从设备里读取支持Hsl特性的数据内容，
  /// 该特性为<see cref="T:HslCommunication.Reflection.HslRedisKeyAttribute" />，<see cref="T:HslCommunication.Reflection.HslRedisListItemAttribute" />，
  /// <see cref="T:HslCommunication.Reflection.HslRedisListAttribute" />，<see cref="T:HslCommunication.Reflection.HslRedisHashFieldAttribute" />
  /// 详细参考代码示例的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="redis">Redis的数据对象</param>
  /// <returns>包含是否成功的结果对象</returns>
  public static OperateResult<T> Read<T>(RedisClient redis) where T : class, new()
  {
    Type type = typeof (T);
    object instance = type.Assembly.CreateInstance(type.FullName);
    PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
    List<PropertyInfoKeyName> source1 = new List<PropertyInfoKeyName>();
    List<PropertyInfoHashKeyName> source2 = new List<PropertyInfoHashKeyName>();
    foreach (PropertyInfo property in properties)
    {
      object[] customAttributes1 = property.GetCustomAttributes(typeof (HslRedisKeyAttribute), false);
      if (customAttributes1 != null && customAttributes1.Length != 0)
      {
        HslRedisKeyAttribute redisKeyAttribute = (HslRedisKeyAttribute) customAttributes1[0];
        source1.Add(new PropertyInfoKeyName(property, redisKeyAttribute.KeyName));
      }
      else
      {
        object[] customAttributes2 = property.GetCustomAttributes(typeof (HslRedisListItemAttribute), false);
        if (customAttributes2 != null && customAttributes2.Length != 0)
        {
          HslRedisListItemAttribute listItemAttribute = (HslRedisListItemAttribute) customAttributes2[0];
          OperateResult<string> result = redis.ReadListByIndex(listItemAttribute.ListKey, listItemAttribute.Index);
          if (!result.IsSuccess)
            return OperateResult.CreateFailedResult<T>((OperateResult) result);
          HslReflectionHelper.SetPropertyObjectValue(property, instance, result.Content);
        }
        else
        {
          object[] customAttributes3 = property.GetCustomAttributes(typeof (HslRedisListAttribute), false);
          if (customAttributes3 != null && customAttributes3.Length != 0)
          {
            HslRedisListAttribute redisListAttribute = (HslRedisListAttribute) customAttributes3[0];
            OperateResult<string[]> result = redis.ListRange(redisListAttribute.ListKey, redisListAttribute.StartIndex, redisListAttribute.EndIndex);
            if (!result.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) result);
            HslReflectionHelper.SetPropertyObjectValueArray(property, instance, result.Content);
          }
          else
          {
            object[] customAttributes4 = property.GetCustomAttributes(typeof (HslRedisHashFieldAttribute), false);
            if (customAttributes4 != null && customAttributes4.Length != 0)
            {
              HslRedisHashFieldAttribute hashFieldAttribute = (HslRedisHashFieldAttribute) customAttributes4[0];
              source2.Add(new PropertyInfoHashKeyName(property, hashFieldAttribute.HaskKey, hashFieldAttribute.Field));
            }
          }
        }
      }
    }
    if (source1.Count > 0)
    {
      OperateResult<string[]> result = redis.ReadKey(source1.Select<PropertyInfoKeyName, string>((Func<PropertyInfoKeyName, string>) (m => m.KeyName)).ToArray<string>());
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<T>((OperateResult) result);
      for (int index = 0; index < source1.Count; ++index)
        HslReflectionHelper.SetPropertyObjectValue(source1[index].PropertyInfo, instance, result.Content[index]);
    }
    if (source2.Count > 0)
    {
      foreach (var data in source2.GroupBy<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.KeyName)).Select(g => new
      {
        Key = g.Key,
        Values = g.ToArray<PropertyInfoHashKeyName>()
      }))
      {
        if (data.Values.Length == 1)
        {
          OperateResult<string> result = redis.ReadHashKey(data.Key, data.Values[0].Field);
          if (!result.IsSuccess)
            return OperateResult.CreateFailedResult<T>((OperateResult) result);
          HslReflectionHelper.SetPropertyObjectValue(data.Values[0].PropertyInfo, instance, result.Content);
        }
        else
        {
          OperateResult<string[]> result = redis.ReadHashKey(data.Key, ((IEnumerable<PropertyInfoHashKeyName>) data.Values).Select<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.Field)).ToArray<string>());
          if (!result.IsSuccess)
            return OperateResult.CreateFailedResult<T>((OperateResult) result);
          for (int index = 0; index < data.Values.Length; ++index)
            HslReflectionHelper.SetPropertyObjectValue(data.Values[index].PropertyInfo, instance, result.Content[index]);
        }
      }
    }
    return OperateResult.CreateSuccessResult<T>((T) instance);
  }

  /// <summary>
  /// 从设备里写入支持Hsl特性的数据内容，
  /// 该特性为<see cref="T:HslCommunication.Reflection.HslRedisKeyAttribute" /> ，<see cref="T:HslCommunication.Reflection.HslRedisHashFieldAttribute" />
  /// 需要注意的是写入并不支持<see cref="T:HslCommunication.Reflection.HslRedisListAttribute" />，<see cref="T:HslCommunication.Reflection.HslRedisListItemAttribute" />特性，详细参考代码示例的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="data">等待写入的数据参数</param>
  /// <param name="redis">Redis的数据对象</param>
  /// <returns>包含是否成功的结果对象</returns>
  public static OperateResult Write<T>(T data, RedisClient redis) where T : class, new()
  {
    Type type = typeof (T);
    T obj = data;
    PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
    List<PropertyInfoKeyName> source1 = new List<PropertyInfoKeyName>();
    List<PropertyInfoHashKeyName> source2 = new List<PropertyInfoHashKeyName>();
    foreach (PropertyInfo property in properties)
    {
      object[] customAttributes1 = property.GetCustomAttributes(typeof (HslRedisKeyAttribute), false);
      if (customAttributes1 != null && customAttributes1.Length != 0)
      {
        HslRedisKeyAttribute redisKeyAttribute = (HslRedisKeyAttribute) customAttributes1[0];
        source1.Add(new PropertyInfoKeyName(property, redisKeyAttribute.KeyName, property.GetValue((object) obj, (object[]) null).ToString()));
      }
      else
      {
        object[] customAttributes2 = property.GetCustomAttributes(typeof (HslRedisHashFieldAttribute), false);
        if (customAttributes2 != null && customAttributes2.Length != 0)
        {
          HslRedisHashFieldAttribute hashFieldAttribute = (HslRedisHashFieldAttribute) customAttributes2[0];
          source2.Add(new PropertyInfoHashKeyName(property, hashFieldAttribute.HaskKey, hashFieldAttribute.Field, property.GetValue((object) obj, (object[]) null).ToString()));
        }
      }
    }
    if (source1.Count > 0)
    {
      OperateResult operateResult = redis.WriteKey(source1.Select<PropertyInfoKeyName, string>((Func<PropertyInfoKeyName, string>) (m => m.KeyName)).ToArray<string>(), source1.Select<PropertyInfoKeyName, string>((Func<PropertyInfoKeyName, string>) (m => m.Value)).ToArray<string>());
      if (!operateResult.IsSuccess)
        return operateResult;
    }
    if (source2.Count > 0)
    {
      foreach (var data1 in source2.GroupBy<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.KeyName)).Select(g => new
      {
        Key = g.Key,
        Values = g.ToArray<PropertyInfoHashKeyName>()
      }))
      {
        if (data1.Values.Length == 1)
        {
          OperateResult operateResult = (OperateResult) redis.WriteHashKey(data1.Key, data1.Values[0].Field, data1.Values[0].Value);
          if (!operateResult.IsSuccess)
            return operateResult;
        }
        else
        {
          OperateResult operateResult = redis.WriteHashKey(data1.Key, ((IEnumerable<PropertyInfoHashKeyName>) data1.Values).Select<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.Field)).ToArray<string>(), ((IEnumerable<PropertyInfoHashKeyName>) data1.Values).Select<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.Value)).ToArray<string>());
          if (!operateResult.IsSuccess)
            return operateResult;
        }
      }
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 从设备里读取支持Hsl特性的数据内容，
  /// 该特性为<see cref="T:HslCommunication.Reflection.HslRedisKeyAttribute" />，<see cref="T:HslCommunication.Reflection.HslRedisListItemAttribute" />，
  /// <see cref="T:HslCommunication.Reflection.HslRedisListAttribute" />，<see cref="T:HslCommunication.Reflection.HslRedisHashFieldAttribute" />
  /// 详细参考代码示例的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="redis">Redis的数据对象</param>
  /// <returns>包含是否成功的结果对象</returns>
  public static async Task<OperateResult<T>> ReadAsync<T>(RedisClient redis) where T : class, new()
  {
    Type type = typeof (T);
    object obj = type.Assembly.CreateInstance(type.FullName);
    PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
    List<PropertyInfoKeyName> keyPropertyInfos = new List<PropertyInfoKeyName>();
    List<PropertyInfoHashKeyName> propertyInfoHashKeys = new List<PropertyInfoHashKeyName>();
    PropertyInfo[] propertyInfoArray = properties;
    for (int index = 0; index < propertyInfoArray.Length; ++index)
    {
      PropertyInfo property = propertyInfoArray[index];
      object[] attributes = property.GetCustomAttributes(typeof (HslRedisKeyAttribute), false);
      object[] objArray1 = attributes;
      if (objArray1 != null && objArray1.Length != 0)
      {
        HslRedisKeyAttribute attribute = (HslRedisKeyAttribute) attributes[0];
        keyPropertyInfos.Add(new PropertyInfoKeyName(property, attribute.KeyName));
      }
      else
      {
        attributes = property.GetCustomAttributes(typeof (HslRedisListItemAttribute), false);
        object[] objArray2 = attributes;
        if (objArray2 != null && objArray2.Length != 0)
        {
          HslRedisListItemAttribute attribute = (HslRedisListItemAttribute) attributes[0];
          OperateResult<string> read = await redis.ReadListByIndexAsync(attribute.ListKey, attribute.Index);
          if (!read.IsSuccess)
            return OperateResult.CreateFailedResult<T>((OperateResult) read);
          HslReflectionHelper.SetPropertyObjectValue(property, obj, read.Content);
        }
        else
        {
          attributes = property.GetCustomAttributes(typeof (HslRedisListAttribute), false);
          object[] objArray3 = attributes;
          if (objArray3 != null && objArray3.Length != 0)
          {
            HslRedisListAttribute attribute = (HslRedisListAttribute) attributes[0];
            OperateResult<string[]> read = await redis.ListRangeAsync(attribute.ListKey, attribute.StartIndex, attribute.EndIndex);
            if (!read.IsSuccess)
              return OperateResult.CreateFailedResult<T>((OperateResult) read);
            HslReflectionHelper.SetPropertyObjectValueArray(property, obj, read.Content);
          }
          else
          {
            attributes = property.GetCustomAttributes(typeof (HslRedisHashFieldAttribute), false);
            object[] objArray4 = attributes;
            if (objArray4 != null && objArray4.Length != 0)
            {
              HslRedisHashFieldAttribute attribute = (HslRedisHashFieldAttribute) attributes[0];
              propertyInfoHashKeys.Add(new PropertyInfoHashKeyName(property, attribute.HaskKey, attribute.Field));
            }
            else
            {
              attributes = (object[]) null;
              property = (PropertyInfo) null;
            }
          }
        }
      }
    }
    propertyInfoArray = (PropertyInfo[]) null;
    if (keyPropertyInfos.Count > 0)
    {
      OperateResult<string[]> readKeys = await redis.ReadKeyAsync(keyPropertyInfos.Select<PropertyInfoKeyName, string>((Func<PropertyInfoKeyName, string>) (m => m.KeyName)).ToArray<string>());
      if (!readKeys.IsSuccess)
        return OperateResult.CreateFailedResult<T>((OperateResult) readKeys);
      for (int i = 0; i < keyPropertyInfos.Count; ++i)
        HslReflectionHelper.SetPropertyObjectValue(keyPropertyInfos[i].PropertyInfo, obj, readKeys.Content[i]);
      readKeys = (OperateResult<string[]>) null;
    }
    if (propertyInfoHashKeys.Count > 0)
    {
      IEnumerable<\u003C\u003Ef__AnonymousType2<string, PropertyInfoHashKeyName[]>> tmp = propertyInfoHashKeys.GroupBy<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.KeyName)).Select(g => new
      {
        Key = g.Key,
        Values = g.ToArray<PropertyInfoHashKeyName>()
      });
      foreach (var item in tmp)
      {
        if (item.Values.Length == 1)
        {
          OperateResult<string> readKey = await redis.ReadHashKeyAsync(item.Key, item.Values[0].Field);
          if (!readKey.IsSuccess)
            return OperateResult.CreateFailedResult<T>((OperateResult) readKey);
          HslReflectionHelper.SetPropertyObjectValue(item.Values[0].PropertyInfo, obj, readKey.Content);
          readKey = (OperateResult<string>) null;
        }
        else
        {
          OperateResult<string[]> readKeys = await redis.ReadHashKeyAsync(item.Key, ((IEnumerable<PropertyInfoHashKeyName>) item.Values).Select<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.Field)).ToArray<string>());
          if (!readKeys.IsSuccess)
            return OperateResult.CreateFailedResult<T>((OperateResult) readKeys);
          for (int i = 0; i < item.Values.Length; ++i)
            HslReflectionHelper.SetPropertyObjectValue(item.Values[i].PropertyInfo, obj, readKeys.Content[i]);
          readKeys = (OperateResult<string[]>) null;
        }
      }
      tmp = null;
    }
    return OperateResult.CreateSuccessResult<T>((T) obj);
  }

  /// <summary>
  /// 从设备里写入支持Hsl特性的数据内容，
  /// 该特性为<see cref="T:HslCommunication.Reflection.HslRedisKeyAttribute" /> ，<see cref="T:HslCommunication.Reflection.HslRedisHashFieldAttribute" />
  /// 需要注意的是写入并不支持<see cref="T:HslCommunication.Reflection.HslRedisListAttribute" />，<see cref="T:HslCommunication.Reflection.HslRedisListItemAttribute" />特性，详细参考代码示例的操作说明。
  /// </summary>
  /// <typeparam name="T">自定义的数据类型对象</typeparam>
  /// <param name="data">等待写入的数据参数</param>
  /// <param name="redis">Redis的数据对象</param>
  /// <returns>包含是否成功的结果对象</returns>
  public static async Task<OperateResult> WriteAsync<T>(T data, RedisClient redis) where T : class, new()
  {
    Type type = typeof (T);
    T obj = data;
    PropertyInfo[] properties = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);
    List<PropertyInfoKeyName> keyPropertyInfos = new List<PropertyInfoKeyName>();
    List<PropertyInfoHashKeyName> propertyInfoHashKeys = new List<PropertyInfoHashKeyName>();
    PropertyInfo[] propertyInfoArray = properties;
    for (int index = 0; index < propertyInfoArray.Length; ++index)
    {
      PropertyInfo property = propertyInfoArray[index];
      object[] attributes = property.GetCustomAttributes(typeof (HslRedisKeyAttribute), false);
      object[] objArray1 = attributes;
      if (objArray1 != null && objArray1.Length != 0)
      {
        HslRedisKeyAttribute attribute = (HslRedisKeyAttribute) attributes[0];
        keyPropertyInfos.Add(new PropertyInfoKeyName(property, attribute.KeyName, property.GetValue((object) obj, (object[]) null).ToString()));
      }
      else
      {
        attributes = property.GetCustomAttributes(typeof (HslRedisHashFieldAttribute), false);
        object[] objArray2 = attributes;
        if (objArray2 != null && objArray2.Length != 0)
        {
          HslRedisHashFieldAttribute attribute = (HslRedisHashFieldAttribute) attributes[0];
          propertyInfoHashKeys.Add(new PropertyInfoHashKeyName(property, attribute.HaskKey, attribute.Field, property.GetValue((object) obj, (object[]) null).ToString()));
        }
        else
        {
          attributes = (object[]) null;
          property = (PropertyInfo) null;
        }
      }
    }
    propertyInfoArray = (PropertyInfo[]) null;
    if (keyPropertyInfos.Count > 0)
    {
      OperateResult writeResult = await redis.WriteKeyAsync(keyPropertyInfos.Select<PropertyInfoKeyName, string>((Func<PropertyInfoKeyName, string>) (m => m.KeyName)).ToArray<string>(), keyPropertyInfos.Select<PropertyInfoKeyName, string>((Func<PropertyInfoKeyName, string>) (m => m.Value)).ToArray<string>());
      if (!writeResult.IsSuccess)
        return writeResult;
      writeResult = (OperateResult) null;
    }
    if (propertyInfoHashKeys.Count > 0)
    {
      IEnumerable<\u003C\u003Ef__AnonymousType2<string, PropertyInfoHashKeyName[]>> tmp = propertyInfoHashKeys.GroupBy<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.KeyName)).Select(g => new
      {
        Key = g.Key,
        Values = g.ToArray<PropertyInfoHashKeyName>()
      });
      foreach (var item in tmp)
      {
        if (item.Values.Length == 1)
        {
          OperateResult<int> operateResult = await redis.WriteHashKeyAsync(item.Key, item.Values[0].Field, item.Values[0].Value);
          OperateResult writeResult = (OperateResult) operateResult;
          operateResult = (OperateResult<int>) null;
          if (!writeResult.IsSuccess)
            return writeResult;
          writeResult = (OperateResult) null;
        }
        else
        {
          OperateResult writeResult = await redis.WriteHashKeyAsync(item.Key, ((IEnumerable<PropertyInfoHashKeyName>) item.Values).Select<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.Field)).ToArray<string>(), ((IEnumerable<PropertyInfoHashKeyName>) item.Values).Select<PropertyInfoHashKeyName, string>((Func<PropertyInfoHashKeyName, string>) (m => m.Value)).ToArray<string>());
          if (!writeResult.IsSuccess)
            return writeResult;
          writeResult = (OperateResult) null;
        }
      }
      tmp = null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 从Json数据里解析出真实的数据信息，根据方法参数列表的类型进行反解析，然后返回实际的数据数组<br />
  /// Analyze the real data information from the Json data, perform de-analysis according to the type of the method parameter list,
  /// and then return the actual data array
  /// </summary>
  /// <param name="context">当前的会话内容</param>
  /// <param name="request">当用于Http请求的时候关联的请求头对象</param>
  /// <param name="parameters">提供的参数列表信息</param>
  /// <param name="json">参数变量信息</param>
  /// <returns>已经填好的实际数据的参数数组对象</returns>
  public static object[] GetParametersFromJson(
    ISessionContext context,
    HttpListenerRequest request,
    ParameterInfo[] parameters,
    string json)
  {
    JObject jobject = string.IsNullOrEmpty(json) ? new JObject() : JObject.Parse(json);
    object[] parametersFromJson = new object[parameters.Length];
    for (int index = 0; index < parameters.Length; ++index)
    {
      string str = parameters[index].Name;
      if (!jobject.ContainsKey(str))
      {
        if (str == "value" && jobject.ContainsKey("values"))
          str = "values";
        else if (str == "values" && jobject.ContainsKey("value"))
          str = "value";
      }
      if (parameters[index].ParameterType == typeof (byte))
        parametersFromJson[index] = (object) jobject.Value<byte>((object) str);
      else if (parameters[index].ParameterType == typeof (short))
        parametersFromJson[index] = (object) jobject.Value<short>((object) str);
      else if (parameters[index].ParameterType == typeof (ushort))
        parametersFromJson[index] = (object) jobject.Value<ushort>((object) str);
      else if (parameters[index].ParameterType == typeof (int))
        parametersFromJson[index] = (object) jobject.Value<int>((object) str);
      else if (parameters[index].ParameterType == typeof (uint))
        parametersFromJson[index] = (object) jobject.Value<uint>((object) str);
      else if (parameters[index].ParameterType == typeof (long))
        parametersFromJson[index] = (object) jobject.Value<long>((object) str);
      else if (parameters[index].ParameterType == typeof (ulong))
        parametersFromJson[index] = (object) jobject.Value<ulong>((object) str);
      else if (parameters[index].ParameterType == typeof (double))
        parametersFromJson[index] = (object) jobject.Value<double>((object) str);
      else if (parameters[index].ParameterType == typeof (float))
        parametersFromJson[index] = (object) jobject.Value<float>((object) str);
      else if (parameters[index].ParameterType == typeof (bool))
        parametersFromJson[index] = (object) jobject.Value<bool>((object) str);
      else if (parameters[index].ParameterType == typeof (string))
        parametersFromJson[index] = (object) jobject.Value<string>((object) str);
      else if (parameters[index].ParameterType == typeof (DateTime))
        parametersFromJson[index] = (object) jobject.Value<DateTime>((object) str);
      else if (parameters[index].ParameterType == typeof (byte[]))
        parametersFromJson[index] = (object) jobject.Value<string>((object) str).ToHexBytes();
      else if (parameters[index].ParameterType == typeof (short[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, short>((Func<JToken, short>) (m => m.Value<short>())).ToArray<short>();
      else if (parameters[index].ParameterType == typeof (ushort[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, ushort>((Func<JToken, ushort>) (m => m.Value<ushort>())).ToArray<ushort>();
      else if (parameters[index].ParameterType == typeof (int[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, int>((Func<JToken, int>) (m => m.Value<int>())).ToArray<int>();
      else if (parameters[index].ParameterType == typeof (uint[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, uint>((Func<JToken, uint>) (m => m.Value<uint>())).ToArray<uint>();
      else if (parameters[index].ParameterType == typeof (long[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, long>((Func<JToken, long>) (m => m.Value<long>())).ToArray<long>();
      else if (parameters[index].ParameterType == typeof (ulong[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, ulong>((Func<JToken, ulong>) (m => m.Value<ulong>())).ToArray<ulong>();
      else if (parameters[index].ParameterType == typeof (float[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, float>((Func<JToken, float>) (m => m.Value<float>())).ToArray<float>();
      else if (parameters[index].ParameterType == typeof (double[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, double>((Func<JToken, double>) (m => m.Value<double>())).ToArray<double>();
      else if (parameters[index].ParameterType == typeof (bool[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, bool>((Func<JToken, bool>) (m => m.Value<bool>())).ToArray<bool>();
      else if (parameters[index].ParameterType == typeof (string[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, string>((Func<JToken, string>) (m => m.Value<string>())).ToArray<string>();
      else if (parameters[index].ParameterType == typeof (DateTime[]))
        parametersFromJson[index] = (object) ((IEnumerable<JToken>) jobject[str].ToArray<JToken>()).Select<JToken, DateTime>((Func<JToken, DateTime>) (m => m.Value<DateTime>())).ToArray<DateTime>();
      else if (parameters[index].ParameterType == typeof (ISessionContext))
        parametersFromJson[index] = (object) context;
      else if (parameters[index].ParameterType == typeof (HttpListenerRequest))
        parametersFromJson[index] = (object) request;
      else if (parameters[index].ParameterType.IsArray)
        parametersFromJson[index] = jobject[str].ToObject(parameters[index].ParameterType);
      else if (parameters[index].ParameterType == typeof (JObject))
      {
        try
        {
          parametersFromJson[index] = (object) (JObject) jobject[str];
        }
        catch
        {
          parametersFromJson[index] = (object) JObject.Parse(jobject.Value<string>((object) str));
        }
      }
      else
      {
        try
        {
          parametersFromJson[index] = jobject[str].ToObject(parameters[index].ParameterType);
        }
        catch
        {
          parametersFromJson[index] = JObject.Parse(jobject.Value<string>((object) str)).ToObject(parameters[index].ParameterType);
        }
      }
    }
    return parametersFromJson;
  }

  /// <summary>
  /// 从url数据里解析出真实的数据信息，根据方法参数列表的类型进行反解析，然后返回实际的数据数组<br />
  /// Analyze the real data information from the url data, perform de-analysis according to the type of the method parameter list,
  /// and then return the actual data array
  /// </summary>
  /// <param name="context">当前的会话内容</param>
  /// <param name="request">当用于Http请求的时候关联的请求头对象</param>
  /// <param name="parameters">提供的参数列表信息</param>
  /// <param name="url">参数变量信息</param>
  /// <returns>已经填好的实际数据的参数数组对象</returns>
  public static object[] GetParametersFromUrl(
    ISessionContext context,
    HttpListenerRequest request,
    ParameterInfo[] parameters,
    string url)
  {
    if (url.IndexOf('?') > 0)
      url = url.Substring(url.IndexOf('?') + 1);
    string[] strArray = url.Split(new char[1]{ '&' }, StringSplitOptions.RemoveEmptyEntries);
    Dictionary<string, string> dictionary = new Dictionary<string, string>(strArray.Length);
    for (int index = 0; index < strArray.Length; ++index)
    {
      if (!string.IsNullOrEmpty(strArray[index]) && strArray[index].IndexOf('=') > 0)
        dictionary.Add(strArray[index].Substring(0, strArray[index].IndexOf('=')).Trim(' '), strArray[index].Substring(strArray[index].IndexOf('=') + 1));
    }
    object[] parametersFromUrl = new object[parameters.Length];
    for (int index = 0; index < parameters.Length; ++index)
      parametersFromUrl[index] = !(parameters[index].ParameterType == typeof (byte)) ? (!(parameters[index].ParameterType == typeof (short)) ? (!(parameters[index].ParameterType == typeof (ushort)) ? (!(parameters[index].ParameterType == typeof (int)) ? (!(parameters[index].ParameterType == typeof (uint)) ? (!(parameters[index].ParameterType == typeof (long)) ? (!(parameters[index].ParameterType == typeof (ulong)) ? (!(parameters[index].ParameterType == typeof (double)) ? (!(parameters[index].ParameterType == typeof (float)) ? (!(parameters[index].ParameterType == typeof (bool)) ? (!(parameters[index].ParameterType == typeof (string)) ? (!(parameters[index].ParameterType == typeof (DateTime)) ? (!(parameters[index].ParameterType == typeof (byte[])) ? (!(parameters[index].ParameterType == typeof (short[])) ? (!(parameters[index].ParameterType == typeof (ushort[])) ? (!(parameters[index].ParameterType == typeof (int[])) ? (!(parameters[index].ParameterType == typeof (uint[])) ? (!(parameters[index].ParameterType == typeof (long[])) ? (!(parameters[index].ParameterType == typeof (ulong[])) ? (!(parameters[index].ParameterType == typeof (float[])) ? (!(parameters[index].ParameterType == typeof (double[])) ? (!(parameters[index].ParameterType == typeof (bool[])) ? (!(parameters[index].ParameterType == typeof (string[])) ? (!(parameters[index].ParameterType == typeof (DateTime[])) ? (!(parameters[index].ParameterType == typeof (ISessionContext)) ? (!(parameters[index].ParameterType == typeof (HttpListenerRequest)) ? JToken.Parse(dictionary[parameters[index].Name]).ToObject(parameters[index].ParameterType) : (object) request) : (object) context) : (object) dictionary[parameters[index].Name].ToStringArray<DateTime>()) : (object) dictionary[parameters[index].Name].ToStringArray<string>()) : (object) dictionary[parameters[index].Name].ToStringArray<bool>()) : (object) dictionary[parameters[index].Name].ToStringArray<double>()) : (object) dictionary[parameters[index].Name].ToStringArray<float>()) : (object) dictionary[parameters[index].Name].ToStringArray<ulong>()) : (object) dictionary[parameters[index].Name].ToStringArray<long>()) : (object) dictionary[parameters[index].Name].ToStringArray<uint>()) : (object) dictionary[parameters[index].Name].ToStringArray<int>()) : (object) dictionary[parameters[index].Name].ToStringArray<ushort>()) : (object) dictionary[parameters[index].Name].ToStringArray<short>()) : (object) dictionary[parameters[index].Name].ToHexBytes()) : (object) DateTime.Parse(dictionary[parameters[index].Name])) : (object) dictionary[parameters[index].Name]) : (object) bool.Parse(dictionary[parameters[index].Name])) : (object) float.Parse(dictionary[parameters[index].Name])) : (object) double.Parse(dictionary[parameters[index].Name])) : (object) ulong.Parse(dictionary[parameters[index].Name])) : (object) long.Parse(dictionary[parameters[index].Name])) : (object) uint.Parse(dictionary[parameters[index].Name])) : (object) int.Parse(dictionary[parameters[index].Name])) : (object) ushort.Parse(dictionary[parameters[index].Name])) : (object) short.Parse(dictionary[parameters[index].Name])) : (object) byte.Parse(dictionary[parameters[index].Name]);
    return parametersFromUrl;
  }

  /// <summary>
  /// 从方法的参数列表里，提取出实际的示例参数信息，返回一个json对象，注意：该数据是示例的数据，具体参数的限制参照服务器返回的数据声明。<br />
  /// From the parameter list of the method, extract the actual example parameter information, and return a json object. Note: The data is the example data,
  /// and the specific parameter restrictions refer to the data declaration returned by the server.
  /// </summary>
  /// <param name="method">当前需要解析的方法名称</param>
  /// <param name="parameters">当前的参数列表信息</param>
  /// <returns>当前的参数对象信息</returns>
  public static JObject GetParametersFromJson(MethodInfo method, ParameterInfo[] parameters)
  {
    JObject parametersFromJson = new JObject();
    DateTime dateTime;
    for (int index = 0; index < parameters.Length; ++index)
    {
      if (parameters[index].ParameterType == typeof (byte))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (long) (byte) parameters[index].DefaultValue : 0L));
      else if (parameters[index].ParameterType == typeof (short))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (long) (short) parameters[index].DefaultValue : 0L));
      else if (parameters[index].ParameterType == typeof (ushort))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (long) (ushort) parameters[index].DefaultValue : 0L));
      else if (parameters[index].ParameterType == typeof (int))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (long) (int) parameters[index].DefaultValue : 0L));
      else if (parameters[index].ParameterType == typeof (uint))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (long) (uint) parameters[index].DefaultValue : 0L));
      else if (parameters[index].ParameterType == typeof (long))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (long) parameters[index].DefaultValue : 0L));
      else if (parameters[index].ParameterType == typeof (ulong))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (ulong) parameters[index].DefaultValue : 0UL));
      else if (parameters[index].ParameterType == typeof (double))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (double) parameters[index].DefaultValue : 0.0));
      else if (parameters[index].ParameterType == typeof (float))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (float) parameters[index].DefaultValue : 0.0f));
      else if (parameters[index].ParameterType == typeof (bool))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue && (bool) parameters[index].DefaultValue));
      else if (parameters[index].ParameterType == typeof (string))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? (string) parameters[index].DefaultValue : ""));
      else if (parameters[index].ParameterType == typeof (DateTime))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        string str;
        if (!parameters[index].HasDefaultValue)
        {
          dateTime = DateTime.Now;
          str = dateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
        else
        {
          dateTime = (DateTime) parameters[index].DefaultValue;
          str = dateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
        JValue jvalue = new JValue(str);
        jobject.Add(name, (JToken) jvalue);
      }
      else if (parameters[index].ParameterType == typeof (byte[]))
        parametersFromJson.Add(parameters[index].Name, (JToken) new JValue(parameters[index].HasDefaultValue ? ((byte[]) parameters[index].DefaultValue).ToHexString() : "00 1A 2B 3C 4D"));
      else if (parameters[index].ParameterType == typeof (short[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        short[] content;
        if (!parameters[index].HasDefaultValue)
          content = new short[3]
          {
            (short) 1,
            (short) 2,
            (short) 3
          };
        else
          content = (short[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (ushort[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        ushort[] content;
        if (!parameters[index].HasDefaultValue)
          content = new ushort[3]
          {
            (ushort) 1,
            (ushort) 2,
            (ushort) 3
          };
        else
          content = (ushort[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (int[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        int[] content;
        if (!parameters[index].HasDefaultValue)
          content = new int[3]{ 1, 2, 3 };
        else
          content = (int[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (uint[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        uint[] content;
        if (!parameters[index].HasDefaultValue)
          content = new uint[3]{ 1U, 2U, 3U };
        else
          content = (uint[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (long[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        long[] content;
        if (!parameters[index].HasDefaultValue)
          content = new long[3]{ 1L, 2L, 3L };
        else
          content = (long[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (ulong[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        ulong[] content;
        if (!parameters[index].HasDefaultValue)
          content = new ulong[3]{ 1UL, 2UL, 3UL };
        else
          content = (ulong[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (float[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        float[] content;
        if (!parameters[index].HasDefaultValue)
          content = new float[3]{ 1f, 2f, 3f };
        else
          content = (float[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (double[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        double[] content;
        if (!parameters[index].HasDefaultValue)
          content = new double[3]{ 1.0, 2.0, 3.0 };
        else
          content = (double[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (bool[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        bool[] content;
        if (!parameters[index].HasDefaultValue)
          content = new bool[3]{ true, false, false };
        else
          content = (bool[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object) content);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (string[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        string[] strArray;
        if (!parameters[index].HasDefaultValue)
          strArray = new string[3]{ "1", "2", "3" };
        else
          strArray = (string[]) parameters[index].DefaultValue;
        JArray jarray = new JArray((object[]) strArray);
        jobject.Add(name, (JToken) jarray);
      }
      else if (parameters[index].ParameterType == typeof (DateTime[]))
      {
        JObject jobject = parametersFromJson;
        string name = parameters[index].Name;
        string[] strArray1;
        if (!parameters[index].HasDefaultValue)
        {
          string[] strArray2 = new string[1];
          dateTime = DateTime.Now;
          strArray2[0] = dateTime.ToString("yyyy-MM-dd HH:mm:ss");
          strArray1 = strArray2;
        }
        else
          strArray1 = ((IEnumerable<DateTime>) (DateTime[]) parameters[index].DefaultValue).Select<DateTime, string>((Func<DateTime, string>) (m => m.ToString("yyyy-MM-dd HH:mm:ss"))).ToArray<string>();
        JArray jarray = new JArray((object[]) strArray1);
        jobject.Add(name, (JToken) jarray);
      }
      else if (!(parameters[index].ParameterType == typeof (ISessionContext)) && !(parameters[index].ParameterType == typeof (HttpListenerRequest)))
      {
        if (parameters[index].ParameterType.IsArray)
          parametersFromJson.Add(parameters[index].Name, parameters[index].HasDefaultValue ? JToken.FromObject(parameters[index].DefaultValue) : JToken.FromObject(HslReflectionHelper.GetObjFromArrayParameterType(parameters[index].ParameterType)));
        else
          parametersFromJson.Add(parameters[index].Name, JToken.FromObject(parameters[index].HasDefaultValue ? parameters[index].DefaultValue : Activator.CreateInstance(parameters[index].ParameterType)));
      }
    }
    return parametersFromJson;
  }

  private static object GetObjFromArrayParameterType(Type parameterType)
  {
    Type[] genericArguments = parameterType.GetGenericArguments();
    Type type = genericArguments.Length == 0 ? parameterType.GetElementType() : genericArguments[0];
    Array instance = Array.CreateInstance(type, 3);
    for (int index = 0; index < 3; ++index)
      instance.SetValue(Activator.CreateInstance(type), index);
    return (object) instance;
  }

  /// <summary>
  /// 将一个对象转换成 <see cref="T:HslCommunication.OperateResult`1" /> 的string 类型的对象，用于远程RPC的数据交互
  /// </summary>
  /// <param name="obj">自定义的对象</param>
  /// <returns>转换之后的结果对象</returns>
  public static OperateResult<string> GetOperateResultJsonFromObj(object obj)
  {
    if (!(obj is OperateResult operateResult))
      return OperateResult.CreateSuccessResult<string>(obj == null ? string.Empty : obj.ToJsonString());
    OperateResult<string> resultJsonFromObj = new OperateResult<string>();
    resultJsonFromObj.IsSuccess = operateResult.IsSuccess;
    resultJsonFromObj.ErrorCode = operateResult.ErrorCode;
    resultJsonFromObj.Message = operateResult.Message;
    if (!operateResult.IsSuccess)
      return resultJsonFromObj;
    PropertyInfo property1 = obj.GetType().GetProperty("Content");
    if (property1 != (PropertyInfo) null)
    {
      object obj1 = property1.GetValue(obj, (object[]) null);
      if (obj1 != null)
        resultJsonFromObj.Content = obj1.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property2 = obj.GetType().GetProperty("Content1");
    if (property2 == (PropertyInfo) null)
      return resultJsonFromObj;
    PropertyInfo property3 = obj.GetType().GetProperty("Content2");
    if (property3 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property4 = obj.GetType().GetProperty("Content3");
    if (property4 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property5 = obj.GetType().GetProperty("Content4");
    if (property5 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null),
        Content3 = property4.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property6 = obj.GetType().GetProperty("Content5");
    if (property6 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null),
        Content3 = property4.GetValue(obj, (object[]) null),
        Content4 = property5.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property7 = obj.GetType().GetProperty("Content6");
    if (property7 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null),
        Content3 = property4.GetValue(obj, (object[]) null),
        Content4 = property5.GetValue(obj, (object[]) null),
        Content5 = property6.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property8 = obj.GetType().GetProperty("Content7");
    if (property8 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null),
        Content3 = property4.GetValue(obj, (object[]) null),
        Content4 = property5.GetValue(obj, (object[]) null),
        Content5 = property6.GetValue(obj, (object[]) null),
        Content6 = property7.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property9 = obj.GetType().GetProperty("Content8");
    if (property9 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null),
        Content3 = property4.GetValue(obj, (object[]) null),
        Content4 = property5.GetValue(obj, (object[]) null),
        Content5 = property6.GetValue(obj, (object[]) null),
        Content6 = property7.GetValue(obj, (object[]) null),
        Content7 = property8.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property10 = obj.GetType().GetProperty("Content9");
    if (property10 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null),
        Content3 = property4.GetValue(obj, (object[]) null),
        Content4 = property5.GetValue(obj, (object[]) null),
        Content5 = property6.GetValue(obj, (object[]) null),
        Content6 = property7.GetValue(obj, (object[]) null),
        Content7 = property8.GetValue(obj, (object[]) null),
        Content8 = property9.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    PropertyInfo property11 = obj.GetType().GetProperty("Content10");
    if (property11 == (PropertyInfo) null)
    {
      resultJsonFromObj.Content = new
      {
        Content1 = property2.GetValue(obj, (object[]) null),
        Content2 = property3.GetValue(obj, (object[]) null),
        Content3 = property4.GetValue(obj, (object[]) null),
        Content4 = property5.GetValue(obj, (object[]) null),
        Content5 = property6.GetValue(obj, (object[]) null),
        Content6 = property7.GetValue(obj, (object[]) null),
        Content7 = property8.GetValue(obj, (object[]) null),
        Content8 = property9.GetValue(obj, (object[]) null),
        Content9 = property10.GetValue(obj, (object[]) null)
      }.ToJsonString();
      return resultJsonFromObj;
    }
    resultJsonFromObj.Content = new
    {
      Content1 = property2.GetValue(obj, (object[]) null),
      Content2 = property3.GetValue(obj, (object[]) null),
      Content3 = property4.GetValue(obj, (object[]) null),
      Content4 = property5.GetValue(obj, (object[]) null),
      Content5 = property6.GetValue(obj, (object[]) null),
      Content6 = property7.GetValue(obj, (object[]) null),
      Content7 = property8.GetValue(obj, (object[]) null),
      Content8 = property9.GetValue(obj, (object[]) null),
      Content9 = property10.GetValue(obj, (object[]) null),
      Content10 = property11.GetValue(obj, (object[]) null)
    }.ToJsonString();
    return resultJsonFromObj;
  }

  /// <summary>
  /// 根据提供的类型对象，解析出符合 <see cref="T:HslCommunication.Reflection.HslDeviceAddressAttribute" /> 特性的地址列表
  /// </summary>
  /// <param name="valueType">数据类型</param>
  /// <param name="deviceType">设备类型</param>
  /// <param name="obj">类型的对象信息</param>
  /// <param name="byteTransform">数据变换对象</param>
  /// <returns>地址列表信息</returns>
  public static List<HslAddressProperty> GetHslPropertyInfos(
    Type valueType,
    Type deviceType,
    object obj,
    IByteTransform byteTransform)
  {
    List<HslAddressProperty> hslPropertyInfos = new List<HslAddressProperty>();
    PropertyInfo[] properties = valueType.GetProperties(BindingFlags.Instance | BindingFlags.Public);
    int num = 0;
    foreach (PropertyInfo property in properties)
    {
      HslDeviceAddressAttribute addressAttribute = HslReflectionHelper.GetHslDeviceAddressAttribute(deviceType, property);
      if (addressAttribute != null)
      {
        HslAddressProperty hslAddressProperty1 = new HslAddressProperty();
        hslAddressProperty1.PropertyInfo = property;
        hslAddressProperty1.DeviceAddressAttribute = addressAttribute;
        hslAddressProperty1.ByteOffset = num;
        Type propertyType = property.PropertyType;
        if (propertyType == typeof (byte))
        {
          ++num;
          if (obj != null)
            hslAddressProperty1.Buffer = new byte[1]
            {
              (byte) property.GetValue(obj, (object[]) null)
            };
        }
        else if (propertyType == typeof (short))
        {
          num += 2;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((short) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (short[]))
        {
          num += 2 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((short[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (ushort))
        {
          num += 2;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((ushort) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (ushort[]))
        {
          num += 2 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((ushort[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (int))
        {
          num += 4;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((int) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (int[]))
        {
          num += 4 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((int[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (uint))
        {
          num += 4;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((uint) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (uint[]))
        {
          num += 4 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((uint[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (long))
        {
          num += 8;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((long) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (long[]))
        {
          num += 8 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((long[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (ulong))
        {
          num += 8;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((ulong) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (ulong[]))
        {
          num += 8 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((ulong[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (float))
        {
          num += 4;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((float) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (float[]))
        {
          num += 4 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((float[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (double))
        {
          num += 8;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((double) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (double[]))
        {
          num += 8 * (addressAttribute.Length > 0 ? addressAttribute.Length : 1);
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((double[]) property.GetValue(obj, (object[]) null));
        }
        else if (propertyType == typeof (string))
        {
          num += addressAttribute.Length > 0 ? addressAttribute.Length : 1;
          if (obj != null)
            hslAddressProperty1.Buffer = byteTransform.TransByte((string) property.GetValue(obj, (object[]) null), Encoding.ASCII);
        }
        else if (propertyType == typeof (byte[]))
        {
          num += addressAttribute.Length > 0 ? addressAttribute.Length : 1;
          if (obj != null)
            hslAddressProperty1.Buffer = (byte[]) property.GetValue(obj, (object[]) null);
        }
        else if (propertyType == typeof (bool))
        {
          ++num;
          if (obj != null)
          {
            HslAddressProperty hslAddressProperty2 = hslAddressProperty1;
            byte[] numArray;
            if (!(bool) property.GetValue(obj, (object[]) null))
              numArray = new byte[1];
            else
              numArray = new byte[1]{ (byte) 1 };
            hslAddressProperty2.Buffer = numArray;
          }
        }
        else if (propertyType == typeof (bool[]))
        {
          num += addressAttribute.Length > 0 ? addressAttribute.Length : 1;
          if (obj != null)
            hslAddressProperty1.Buffer = ((IEnumerable<bool>) (bool[]) property.GetValue(obj, (object[]) null)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
        }
        hslAddressProperty1.ByteLength = num - hslAddressProperty1.ByteOffset;
        hslPropertyInfos.Add(hslAddressProperty1);
      }
    }
    return hslPropertyInfos;
  }

  /// <summary>根据地址列表信息，数据缓存，自动解析基础类型的数据，赋值到自定义的对象上去</summary>
  /// <param name="byteTransform">数据解析对象</param>
  /// <param name="obj">数据对象信息</param>
  /// <param name="properties">地址属性列表</param>
  /// <param name="buffer">缓存数据信息</param>
  public static void SetPropertyValueFrom(
    IByteTransform byteTransform,
    object obj,
    List<HslAddressProperty> properties,
    byte[] buffer)
  {
    foreach (HslAddressProperty property in properties)
    {
      Type propertyType = property.PropertyInfo.PropertyType;
      object obj1 = (object) null;
      if (propertyType == typeof (byte))
        obj1 = (object) buffer[property.ByteOffset];
      else if (propertyType == typeof (short))
        obj1 = (object) byteTransform.TransInt16(buffer, property.ByteOffset);
      else if (propertyType == typeof (short[]))
        obj1 = (object) byteTransform.TransInt16(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (ushort))
        obj1 = (object) byteTransform.TransUInt16(buffer, property.ByteOffset);
      else if (propertyType == typeof (ushort[]))
        obj1 = (object) byteTransform.TransUInt16(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (int))
        obj1 = (object) byteTransform.TransInt32(buffer, property.ByteOffset);
      else if (propertyType == typeof (int[]))
        obj1 = (object) byteTransform.TransInt32(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (uint))
        obj1 = (object) byteTransform.TransUInt32(buffer, property.ByteOffset);
      else if (propertyType == typeof (uint[]))
        obj1 = (object) byteTransform.TransUInt32(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (long))
        obj1 = (object) byteTransform.TransInt64(buffer, property.ByteOffset);
      else if (propertyType == typeof (long[]))
        obj1 = (object) byteTransform.TransInt64(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (ulong))
        obj1 = (object) byteTransform.TransUInt64(buffer, property.ByteOffset);
      else if (propertyType == typeof (ulong[]))
        obj1 = (object) byteTransform.TransUInt64(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (float))
        obj1 = (object) byteTransform.TransSingle(buffer, property.ByteOffset);
      else if (propertyType == typeof (float[]))
        obj1 = (object) byteTransform.TransSingle(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (double))
        obj1 = (object) byteTransform.TransDouble(buffer, property.ByteOffset);
      else if (propertyType == typeof (double[]))
        obj1 = (object) byteTransform.TransDouble(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (string))
        obj1 = (object) Encoding.ASCII.GetString(buffer, property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (byte[]))
        obj1 = (object) buffer.SelectMiddle<byte>(property.ByteOffset, property.DeviceAddressAttribute.GetDataLength());
      else if (propertyType == typeof (bool))
        obj1 = (object) (buffer[property.ByteOffset] > (byte) 0);
      else if (propertyType == typeof (bool[]))
        obj1 = (object) ((IEnumerable<byte>) buffer.SelectMiddle<byte>(property.ByteOffset, property.DeviceAddressAttribute.GetDataLength())).Select<byte, bool>((Func<byte, bool>) (m => m > (byte) 0)).ToArray<bool>();
      if (obj1 != null)
        property.PropertyInfo.SetValue(obj, obj1, (object[]) null);
    }
  }
}
