﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Singleton
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Threading;

#nullable disable
namespace HslCommunication.Core;

/// <summary>一个双检锁的示例，适合一些占内存的静态数据对象，获取的时候才实例化真正的对象</summary>
internal sealed class Singleton
{
  private static object m_lock = new object();
  private static Singleton SValue = (Singleton) null;

  public static Singleton GetSingleton()
  {
    if (Singleton.SValue != null)
      return Singleton.SValue;
    Monitor.Enter(Singleton.m_lock);
    if (Singleton.SValue == null)
    {
      Singleton singleton = new Singleton();
      Volatile.Write<Singleton>(ref Singleton.SValue, singleton);
      Singleton.SValue = new Singleton();
    }
    Monitor.Exit(Singleton.m_lock);
    return Singleton.SValue;
  }
}
