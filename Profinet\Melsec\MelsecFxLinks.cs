﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Melsec.MelsecFxLinks
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Profinet.Melsec.Helper;
using HslCommunication.Reflection;

#nullable disable
namespace HslCommunication.Profinet.Melsec;

/// <summary>
/// 三菱计算机链接协议，适用FX3U系列，FX3G，FX3S等等系列，通常在PLC侧连接的是485的接线口<br />
/// Mitsubishi Computer Link Protocol, suitable for FX3U series, FX3G, FX3S, etc., usually the 485 connection port is connected on the PLC side
/// </summary>
/// <remarks>关于在PLC侧的配置信息，协议：专用协议  传送控制步骤：格式一  站号设置：0</remarks>
public class MelsecFxLinks : 
  DeviceSerialPort,
  IReadWriteFxLinks,
  IReadWriteDevice,
  IReadWriteNet,
  IReadWriteDeviceStation
{
  private byte station = 0;
  private byte watiingTime = 0;
  private bool sumCheck = true;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.#ctor" />
  public MelsecFxLinks()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new MelsecFxLinksMessage(this.Format, this.SumCheck);
  }

  /// <inheritdoc />
  public override byte[] PackCommandWithHeader(byte[] command)
  {
    return MelsecFxLinksHelper.PackCommandWithHeader((IReadWriteFxLinks) this, command);
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.Station" />
  public byte Station
  {
    get => this.station;
    set => this.station = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.WaittingTime" />
  public byte WaittingTime
  {
    get => this.watiingTime;
    set
    {
      if (this.watiingTime > (byte) 15)
        this.watiingTime = (byte) 15;
      else
        this.watiingTime = value;
    }
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.SumCheck" />
  public bool SumCheck
  {
    get => this.sumCheck;
    set => this.sumCheck = value;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.Format" />
  public int Format { get; set; } = 1;

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return MelsecFxLinksHelper.Read((IReadWriteFxLinks) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return MelsecFxLinksHelper.Write((IReadWriteFxLinks) this, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return MelsecFxLinksHelper.ReadBool((IReadWriteFxLinks) this, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return MelsecFxLinksHelper.Write((IReadWriteFxLinks) this, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.StartPLC(System.String)" />
  [HslMqttApi(Description = "Start the PLC operation, you can carry additional parameter information and specify the station number. Example: s=2; Note: The semicolon is required.")]
  public OperateResult StartPLC(string parameter = "")
  {
    return MelsecFxLinksHelper.StartPLC((IReadWriteFxLinks) this, parameter);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.StopPLC(System.String)" />
  [HslMqttApi(Description = "Stop PLC operation, you can carry additional parameter information and specify the station number. Example: s=2; Note: The semicolon is required.")]
  public OperateResult StopPLC(string parameter = "")
  {
    return MelsecFxLinksHelper.StopPLC((IReadWriteFxLinks) this, parameter);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Melsec.MelsecFxLinksOverTcp.ReadPlcType(System.String)" />
  [HslMqttApi(Description = "Read the PLC model information, you can carry additional parameter information, and specify the station number. Example: s=2; Note: The semicolon is required.")]
  public OperateResult<string> ReadPlcType(string parameter = "")
  {
    return MelsecFxLinksHelper.ReadPlcType((IReadWriteFxLinks) this, parameter);
  }

  /// <inheritdoc />
  public override string ToString() => $"MelsecFxLinks[{this.PortName}:{this.BaudRate}]";
}
