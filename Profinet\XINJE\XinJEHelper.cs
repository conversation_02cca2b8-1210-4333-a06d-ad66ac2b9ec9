﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.XINJE.XinJEHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.ModBus;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Profinet.XINJE;

/// <summary>信捷PLC的相关辅助类</summary>
public class XinJEHelper
{
  private static int CalculateXinJEStartAddress(string address)
  {
    if (address.IndexOf('.') < 0)
      return Convert.ToInt32(address, 8);
    string[] strArray = address.Split(new char[1]{ '.' }, StringSplitOptions.RemoveEmptyEntries);
    return Convert.ToInt32(strArray[0], 8) * 8 + int.Parse(strArray[1]);
  }

  /// <summary>根据信捷PLC的地址，解析出转换后的modbus协议信息</summary>
  /// <param name="series">PLC的系列信息</param>
  /// <param name="address">汇川plc的地址信息</param>
  /// <param name="modbusCode">原始的对应的modbus信息</param>
  /// <returns>还原后的modbus地址</returns>
  public static OperateResult<string> PraseXinJEAddress(
    XinJESeries series,
    string address,
    byte modbusCode)
  {
    string str1 = string.Empty;
    OperateResult<int> parameter1 = HslHelper.ExtractParameter(ref address, "s");
    if (parameter1.IsSuccess)
      str1 = $"s={parameter1.Content};";
    string str2 = string.Empty;
    OperateResult<int> parameter2 = HslHelper.ExtractParameter(ref address, "x");
    if (parameter2.IsSuccess)
      str2 = $"x={parameter2.Content};";
    if (series == XinJESeries.XC)
    {
      try
      {
        if (Regex.IsMatch(address, "^X[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^Y[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^M[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^S[0-9]+", RegexOptions.IgnoreCase))
        {
          if (modbusCode == (byte) 3)
          {
            modbusCode = (byte) 1;
            str2 = "x=1;";
          }
        }
      }
      catch
      {
      }
      return XinJEHelper.PraseXinJEXCAddress(str1 + str2, address, modbusCode);
    }
    try
    {
      if (Regex.IsMatch(address, "^X[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^Y[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^M[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^S[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^SEM[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^HSC[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^SM[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^ET[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^HM[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^HS[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^HT[0-9]+", RegexOptions.IgnoreCase) || Regex.IsMatch(address, "^HC[0-9]+", RegexOptions.IgnoreCase))
      {
        if (modbusCode == (byte) 3)
        {
          modbusCode = (byte) 1;
          str2 = "x=1;";
        }
      }
    }
    catch
    {
    }
    return XinJEHelper.PraseXinJEXD1XD2XD3XL1XL3Address(str1 + str2, address, modbusCode);
  }

  private static int CalculateXC_D(string address)
  {
    int int32 = Convert.ToInt32(address);
    return int32 >= 8000 ? int32 - 8000 + 16384 /*0x4000*/ : int32;
  }

  /// <summary>根据信捷PLC的地址，解析出转换后的modbus协议信息，适用XC系列</summary>
  /// <param name="station">站号的特殊指定信息，可以为空</param>
  /// <param name="address">信捷plc的地址信息</param>
  /// <param name="modbusCode">原始的对应的modbus信息</param>
  /// <returns>还原后的modbus地址</returns>
  public static OperateResult<string> PraseXinJEXCAddress(
    string station,
    string address,
    byte modbusCode)
  {
    try
    {
      if (modbusCode == (byte) 1 || modbusCode == (byte) 2 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        string newAddress1;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[2]
        {
          "X",
          "Y"
        }, new int[2]{ 16384 /*0x4000*/, 18432 }, new Func<string, int>(XinJEHelper.CalculateXinJEStartAddress), out newAddress1))
          return OperateResult.CreateSuccessResult<string>(newAddress1);
        string newAddress2;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[3]
        {
          "S",
          "T",
          "C"
        }, new int[3]{ 20480 /*0x5000*/, 25600, 27648 }, out newAddress2))
          return OperateResult.CreateSuccessResult<string>(newAddress2);
        if (address.StartsWithAndNumber("M"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 >= 8000 ? OperateResult.CreateSuccessResult<string>(station + (int32 - 8000 + 24576 /*0x6000*/).ToString()) : OperateResult.CreateSuccessResult<string>(station + int32.ToString());
        }
        string newAddress3;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[1]
        {
          "D"
        }, new int[1], new Func<string, int>(XinJEHelper.CalculateXC_D), out newAddress3))
          return OperateResult.CreateSuccessResult<string>(newAddress3);
      }
      else
      {
        if (address.StartsWithAndNumber("D"))
          return OperateResult.CreateSuccessResult<string>(station + XinJEHelper.CalculateXC_D(address.Substring(1)).ToString());
        if (address.StartsWithAndNumber("F"))
        {
          int int32 = Convert.ToInt32(address.Substring(1));
          return int32 >= 8000 ? OperateResult.CreateSuccessResult<string>(station + (int32 - 8000 + 26624).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 + 18432).ToString());
        }
        string newAddress;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[3]
        {
          "E",
          "T",
          "C"
        }, new int[3]
        {
          28672 /*0x7000*/,
          12288 /*0x3000*/,
          14336
        }, out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  /// <summary>解析信捷的XD1,XD2,XD3,XL1,XL3系列的PLC的Modbus地址和内部软元件的对照</summary>
  /// <remarks>适用 XD1、XD2、XD3、XL1、XL3、XD5、XDM、XDC、XD5E、XDME、XL5、XL5E、XLME, XDH 只是支持的地址范围不一样而已</remarks>
  /// <param name="station">站号的特殊指定信息，可以为空</param>
  /// <param name="address">PLC内部的软元件的地址</param>
  /// <param name="modbusCode">默认的Modbus功能码</param>
  /// <returns>解析后的Modbus地址</returns>
  public static OperateResult<string> PraseXinJEXD1XD2XD3XL1XL3Address(
    string station,
    string address,
    byte modbusCode)
  {
    try
    {
      if (modbusCode == (byte) 1 || modbusCode == (byte) 2 || modbusCode == (byte) 15 || modbusCode == (byte) 5)
      {
        if (address.StartsWith("X") || address.StartsWith("x"))
        {
          int xinJeStartAddress = XinJEHelper.CalculateXinJEStartAddress(address.Substring(1));
          if (xinJeStartAddress < 4096 /*0x1000*/)
            return OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress + 20480 /*0x5000*/).ToString());
          if (xinJeStartAddress < 8192 /*0x2000*/)
            return OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress - 4096 /*0x1000*/ + 20736).ToString());
          return xinJeStartAddress < 12288 /*0x3000*/ ? OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress - 8192 /*0x2000*/ + 22736).ToString()) : OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress - 12288 /*0x3000*/ + 23536).ToString());
        }
        if (address.StartsWith("Y") || address.StartsWith("y"))
        {
          int xinJeStartAddress = XinJEHelper.CalculateXinJEStartAddress(address.Substring(1));
          if (xinJeStartAddress < 4096 /*0x1000*/)
            return OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress + 24576 /*0x6000*/).ToString());
          if (xinJeStartAddress < 8192 /*0x2000*/)
            return OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress - 4096 /*0x1000*/ + 24832).ToString());
          return xinJeStartAddress < 12288 /*0x3000*/ ? OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress - 8192 /*0x2000*/ + 26832).ToString()) : OperateResult.CreateSuccessResult<string>(station + (xinJeStartAddress - 12288 /*0x3000*/ + 27632).ToString());
        }
        string newAddress1;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[12]
        {
          "SEM",
          "HSC",
          "SM",
          "ET",
          "HM",
          "HS",
          "HT",
          "HC",
          "S",
          "T",
          "C",
          "M"
        }, new int[12]
        {
          49280,
          59648,
          36864 /*0x9000*/,
          49152 /*0xC000*/,
          49408,
          55552,
          57600,
          58624,
          28672 /*0x7000*/,
          40960 /*0xA000*/,
          45056 /*0xB000*/,
          0
        }, out newAddress1))
          return OperateResult.CreateSuccessResult<string>(newAddress1);
        string newAddress2;
        if (ModbusHelper.TransPointAddressToModbus(station, address, new string[3]
        {
          "D",
          "SD",
          "HD"
        }, new int[3]{ 0, 28672 /*0x7000*/, 41088 }, out newAddress2))
          return OperateResult.CreateSuccessResult<string>(newAddress2);
      }
      else
      {
        if (address.StartsWith("ID") || address.StartsWith("id"))
        {
          int int32 = Convert.ToInt32(address.Substring(2));
          if (int32 < 10000)
            return OperateResult.CreateSuccessResult<string>(station + (int32 + 20480 /*0x5000*/).ToString());
          if (int32 < 20000)
            return OperateResult.CreateSuccessResult<string>(station + (int32 - 10000 + 20736).ToString());
          return int32 < 30000 ? OperateResult.CreateSuccessResult<string>(station + (int32 - 20000 + 22736).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 30000 + 23536).ToString());
        }
        if (address.StartsWith("QD") || address.StartsWith("qd"))
        {
          int int32 = Convert.ToInt32(address.Substring(2));
          if (int32 < 10000)
            return OperateResult.CreateSuccessResult<string>(station + (int32 + 24576 /*0x6000*/).ToString());
          if (int32 < 20000)
            return OperateResult.CreateSuccessResult<string>(station + (int32 - 10000 + 24832).ToString());
          return int32 < 30000 ? OperateResult.CreateSuccessResult<string>(station + (int32 - 20000 + 26832).ToString()) : OperateResult.CreateSuccessResult<string>(station + (int32 - 30000 + 27632).ToString());
        }
        string newAddress;
        if (ModbusHelper.TransAddressToModbus(station, address, new string[13]
        {
          "HSCD",
          "ETD",
          "HSD",
          "HTD",
          "HCD",
          "SFD",
          "SD",
          "TD",
          "CD",
          "HD",
          "FD",
          "FS",
          "D"
        }, new int[13]
        {
          50304,
          40960 /*0xA000*/,
          47232,
          48256,
          49280,
          58560,
          28672 /*0x7000*/,
          32768 /*0x8000*/,
          36864 /*0x9000*/,
          41088,
          50368,
          62656,
          0
        }, out newAddress))
          return OperateResult.CreateSuccessResult<string>(newAddress);
      }
      return new OperateResult<string>(StringResources.Language.NotSupportedDataType);
    }
    catch (Exception ex)
    {
      return new OperateResult<string>(ex.Message);
    }
  }

  internal static OperateResult<List<byte[]>> BuildReadCommand(
    byte station,
    string address,
    ushort length,
    bool isBit)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, station);
    return !from.IsSuccess ? OperateResult.CreateFailedResult<List<byte[]>>((OperateResult) from) : XinJEHelper.BuildReadCommand(from.Content, length, isBit);
  }

  internal static OperateResult<List<byte[]>> BuildReadCommand(
    XinJEAddress address,
    ushort length,
    bool isBit)
  {
    List<byte[]> numArrayList = new List<byte[]>();
    int[] array = SoftBasic.SplitIntegerToArray((int) length, isBit ? 1920 : 120);
    for (int index = 0; index < array.Length; ++index)
    {
      byte[] numArray = new byte[8]
      {
        address.Station,
        isBit ? (byte) 30 : (byte) 32 /*0x20*/,
        address.DataCode,
        BitConverter.GetBytes(address.AddressStart)[2],
        BitConverter.GetBytes(address.AddressStart)[1],
        BitConverter.GetBytes(address.AddressStart)[0],
        BitConverter.GetBytes(array[index])[1],
        BitConverter.GetBytes(array[index])[0]
      };
      address.AddressStart += array[index];
      numArrayList.Add(numArray);
    }
    return OperateResult.CreateSuccessResult<List<byte[]>>(numArrayList);
  }

  internal static OperateResult<byte[]> BuildWriteWordCommand(
    byte station,
    string address,
    byte[] value)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, station);
    return !from.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) from) : XinJEHelper.BuildWriteWordCommand(from.Content, value);
  }

  internal static OperateResult<byte[]> BuildWriteWordCommand(XinJEAddress address, byte[] value)
  {
    byte[] numArray = new byte[9 + value.Length];
    numArray[0] = address.Station;
    numArray[1] = (byte) 33;
    numArray[2] = address.DataCode;
    numArray[3] = BitConverter.GetBytes(address.AddressStart)[2];
    numArray[4] = BitConverter.GetBytes(address.AddressStart)[1];
    numArray[5] = BitConverter.GetBytes(address.AddressStart)[0];
    numArray[6] = BitConverter.GetBytes(value.Length / 2)[1];
    numArray[7] = BitConverter.GetBytes(value.Length / 2)[0];
    numArray[8] = (byte) value.Length;
    value.CopyTo((Array) numArray, 9);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  internal static OperateResult<byte[]> BuildWriteBoolCommand(
    byte station,
    string address,
    bool[] value)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, station);
    return !from.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) from) : XinJEHelper.BuildWriteBoolCommand(from.Content, value);
  }

  internal static OperateResult<byte[]> BuildWriteBoolCommand(XinJEAddress address, bool[] value)
  {
    byte[] byteArray = value.ToByteArray();
    byte[] numArray = new byte[9 + byteArray.Length];
    numArray[0] = address.Station;
    numArray[1] = (byte) 31 /*0x1F*/;
    numArray[2] = address.DataCode;
    numArray[3] = BitConverter.GetBytes(address.AddressStart)[2];
    numArray[4] = BitConverter.GetBytes(address.AddressStart)[1];
    numArray[5] = BitConverter.GetBytes(address.AddressStart)[0];
    numArray[6] = BitConverter.GetBytes(value.Length)[1];
    numArray[7] = BitConverter.GetBytes(value.Length)[0];
    numArray[8] = (byte) byteArray.Length;
    byteArray.CopyTo((Array) numArray, 9);
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  internal static OperateResult<byte[]> Read(
    IModbus modbus,
    string address,
    ushort length,
    Func<string, ushort, OperateResult<byte[]>> funcRead)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, length, modbus.Station);
    if (!from.IsSuccess)
      return funcRead(address, length);
    XinJEAddress content = from.Content;
    if (content.AddressStart + (int) length <= content.CriticalAddress)
      return funcRead(address, length);
    List<byte> byteList = new List<byte>();
    if (content.AddressStart < content.CriticalAddress)
    {
      OperateResult<byte[]> operateResult = funcRead(address, (ushort) (content.CriticalAddress - content.AddressStart));
      if (!operateResult.IsSuccess)
        return operateResult;
      byteList.AddRange((IEnumerable<byte>) operateResult.Content);
      length -= (ushort) (content.CriticalAddress - content.AddressStart);
      content.AddressStart = content.CriticalAddress;
    }
    OperateResult<List<byte[]>> result = XinJEHelper.BuildReadCommand(content, length, false);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) result);
    OperateResult<byte[]> operateResult1 = modbus.ReadFromCoreServer((IEnumerable<byte[]>) result.Content);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    byteList.AddRange((IEnumerable<byte>) operateResult1.Content);
    return OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
  }

  internal static OperateResult Write(
    IModbus modbus,
    string address,
    byte[] value,
    Func<string, byte[], OperateResult> funcWrite)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!from.IsSuccess)
      return funcWrite(address, value);
    XinJEAddress content = from.Content;
    if (content.AddressStart + value.Length / 2 <= content.CriticalAddress)
      return funcWrite(address, value);
    if (content.AddressStart < content.CriticalAddress)
    {
      OperateResult operateResult = funcWrite(address, value.SelectBegin<byte>((content.CriticalAddress - content.AddressStart) * 2));
      if (!operateResult.IsSuccess)
        return operateResult;
      value = value.RemoveBegin<byte>((content.CriticalAddress - content.AddressStart) * 2);
      content.AddressStart = content.CriticalAddress;
    }
    OperateResult<byte[]> result = XinJEHelper.BuildWriteWordCommand(content, value);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : (OperateResult) modbus.ReadFromCoreServer(result.Content);
  }

  internal static OperateResult Write(
    IModbus modbus,
    string address,
    short value,
    Func<string, short, OperateResult> funcWrite)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!from.IsSuccess)
      return funcWrite(address, value);
    XinJEAddress content = from.Content;
    return content.AddressStart < content.CriticalAddress ? funcWrite(address, value) : modbus.Write(address, modbus.ByteTransform.TransByte(value));
  }

  internal static OperateResult Write(
    IModbus modbus,
    string address,
    ushort value,
    Func<string, ushort, OperateResult> funcWrite)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!from.IsSuccess)
      return funcWrite(address, value);
    XinJEAddress content = from.Content;
    return content.AddressStart < content.CriticalAddress ? funcWrite(address, value) : modbus.Write(address, modbus.ByteTransform.TransByte(value));
  }

  internal static OperateResult<bool[]> ReadBool(
    IModbus modbus,
    string address,
    ushort length,
    Func<string, ushort, OperateResult<bool[]>> funcRead)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, length, modbus.Station);
    if (!from.IsSuccess)
      return funcRead(address, length);
    XinJEAddress content = from.Content;
    if (content.AddressStart + (int) length <= content.CriticalAddress)
      return funcRead(address, length);
    List<bool> boolList = new List<bool>();
    if (content.AddressStart < content.CriticalAddress)
    {
      OperateResult<bool[]> operateResult = funcRead(address, (ushort) (content.CriticalAddress - content.AddressStart));
      if (!operateResult.IsSuccess)
        return operateResult;
      boolList.AddRange((IEnumerable<bool>) operateResult.Content);
      length -= (ushort) (content.CriticalAddress - content.AddressStart);
      content.AddressStart = content.CriticalAddress;
    }
    OperateResult<List<byte[]>> result1 = XinJEHelper.BuildReadCommand(content, length, true);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = modbus.ReadFromCoreServer((IEnumerable<byte[]>) result1.Content);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) result2);
    boolList.AddRange((IEnumerable<bool>) result2.Content.ToBoolArray().SelectBegin<bool>((int) length));
    return OperateResult.CreateSuccessResult<bool[]>(boolList.ToArray());
  }

  internal static OperateResult Write(
    IModbus modbus,
    string address,
    bool[] values,
    Func<string, bool[], OperateResult> funcWrite)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!from.IsSuccess)
      return funcWrite(address, values);
    XinJEAddress content = from.Content;
    if (content.AddressStart + values.Length <= content.CriticalAddress)
      return funcWrite(address, values);
    if (content.AddressStart < content.CriticalAddress)
    {
      OperateResult operateResult = funcWrite(address, values.SelectBegin<bool>(content.CriticalAddress - content.AddressStart));
      if (!operateResult.IsSuccess)
        return operateResult;
      values = values.RemoveBegin<bool>(content.CriticalAddress - content.AddressStart);
      content.AddressStart = content.CriticalAddress;
    }
    OperateResult<byte[]> result = XinJEHelper.BuildWriteBoolCommand(content, values);
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : (OperateResult) modbus.ReadFromCoreServer(result.Content);
  }

  internal static OperateResult Write(
    IModbus modbus,
    string address,
    bool value,
    Func<string, bool, OperateResult> funcWrite)
  {
    OperateResult<XinJEAddress> from = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!from.IsSuccess)
      return funcWrite(address, value);
    XinJEAddress content = from.Content;
    if (content.AddressStart < content.CriticalAddress)
      return funcWrite(address, value);
    return modbus.Write(address, new bool[1]{ value });
  }

  internal static async Task<OperateResult<byte[]>> ReadAsync(
    IModbus modbus,
    string address,
    ushort length,
    Func<string, ushort, Task<OperateResult<byte[]>>> funcRead)
  {
    OperateResult<XinJEAddress> analysis = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!analysis.IsSuccess)
    {
      OperateResult<byte[]> operateResult = await funcRead(address, length);
      return operateResult;
    }
    XinJEAddress xinJE = analysis.Content;
    if (xinJE.AddressStart + (int) length <= xinJE.CriticalAddress)
    {
      OperateResult<byte[]> operateResult = await funcRead(address, length);
      return operateResult;
    }
    List<byte> result = new List<byte>();
    if (xinJE.AddressStart < xinJE.CriticalAddress)
    {
      OperateResult<byte[]> read = await funcRead(address, (ushort) (xinJE.CriticalAddress - xinJE.AddressStart));
      if (!read.IsSuccess)
        return read;
      result.AddRange((IEnumerable<byte>) read.Content);
      length -= (ushort) (xinJE.CriticalAddress - xinJE.AddressStart);
      xinJE.AddressStart = xinJE.CriticalAddress;
      read = (OperateResult<byte[]>) null;
    }
    OperateResult<List<byte[]>> command = XinJEHelper.BuildReadCommand(analysis.Content, length, false);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> readAgain = await modbus.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content);
    if (!readAgain.IsSuccess)
      return readAgain;
    result.AddRange((IEnumerable<byte>) readAgain.Content);
    return OperateResult.CreateSuccessResult<byte[]>(result.ToArray());
  }

  internal static async Task<OperateResult> WriteAsync(
    IModbus modbus,
    string address,
    byte[] value,
    Func<string, byte[], Task<OperateResult>> funcWrite)
  {
    OperateResult<XinJEAddress> analysis = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!analysis.IsSuccess)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    XinJEAddress xinJE = analysis.Content;
    if (xinJE.AddressStart + value.Length / 2 <= xinJE.CriticalAddress)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    if (xinJE.AddressStart < xinJE.CriticalAddress)
    {
      OperateResult write = await funcWrite(address, value.SelectBegin<byte>((xinJE.CriticalAddress - xinJE.AddressStart) * 2));
      if (!write.IsSuccess)
        return write;
      value = value.RemoveBegin<byte>((xinJE.CriticalAddress - xinJE.AddressStart) * 2);
      xinJE.AddressStart = xinJE.CriticalAddress;
      write = (OperateResult) null;
    }
    OperateResult<byte[]> command = XinJEHelper.BuildWriteWordCommand(xinJE, value);
    if (!command.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult1;
  }

  internal static async Task<OperateResult> WriteAsync(
    IModbus modbus,
    string address,
    short value,
    Func<string, short, Task<OperateResult>> funcWrite)
  {
    OperateResult<XinJEAddress> analysis = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!analysis.IsSuccess)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    XinJEAddress xinJE = analysis.Content;
    if (xinJE.AddressStart < xinJE.CriticalAddress)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    OperateResult operateResult1 = await modbus.WriteAsync(address, modbus.ByteTransform.TransByte(value));
    return operateResult1;
  }

  internal static async Task<OperateResult> WriteAsync(
    IModbus modbus,
    string address,
    ushort value,
    Func<string, ushort, Task<OperateResult>> funcWrite)
  {
    OperateResult<XinJEAddress> analysis = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!analysis.IsSuccess)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    XinJEAddress xinJE = analysis.Content;
    if (xinJE.AddressStart < xinJE.CriticalAddress)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    OperateResult operateResult1 = await modbus.WriteAsync(address, modbus.ByteTransform.TransByte(value));
    return operateResult1;
  }

  internal static async Task<OperateResult<bool[]>> ReadBoolAsync(
    IModbus modbus,
    string address,
    ushort length,
    Func<string, ushort, Task<OperateResult<bool[]>>> funcRead)
  {
    OperateResult<XinJEAddress> analysis = XinJEAddress.ParseFrom(address, length, modbus.Station);
    if (!analysis.IsSuccess)
    {
      OperateResult<bool[]> operateResult = await funcRead(address, length);
      return operateResult;
    }
    XinJEAddress xinJE = analysis.Content;
    if (xinJE.AddressStart + (int) length <= xinJE.CriticalAddress)
    {
      OperateResult<bool[]> operateResult = await funcRead(address, length);
      return operateResult;
    }
    List<bool> result = new List<bool>();
    if (xinJE.AddressStart < xinJE.CriticalAddress)
    {
      OperateResult<bool[]> read = await funcRead(address, (ushort) (xinJE.CriticalAddress - xinJE.AddressStart));
      if (!read.IsSuccess)
        return read;
      result.AddRange((IEnumerable<bool>) read.Content);
      length -= (ushort) (xinJE.CriticalAddress - xinJE.AddressStart);
      xinJE.AddressStart = xinJE.CriticalAddress;
      read = (OperateResult<bool[]>) null;
    }
    OperateResult<List<byte[]>> command = XinJEHelper.BuildReadCommand(xinJE, length, true);
    if (!command.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) command);
    OperateResult<byte[]> readAgain = await modbus.ReadFromCoreServerAsync((IEnumerable<byte[]>) command.Content);
    if (!readAgain.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) readAgain);
    result.AddRange((IEnumerable<bool>) readAgain.Content.ToBoolArray().SelectBegin<bool>((int) length));
    return OperateResult.CreateSuccessResult<bool[]>(result.ToArray());
  }

  internal static async Task<OperateResult> WriteAsync(
    IModbus modbus,
    string address,
    bool[] values,
    Func<string, bool[], Task<OperateResult>> funcWrite)
  {
    OperateResult<XinJEAddress> analysis = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!analysis.IsSuccess)
    {
      OperateResult operateResult = await funcWrite(address, values);
      return operateResult;
    }
    XinJEAddress xinJE = analysis.Content;
    if (xinJE.AddressStart + values.Length <= xinJE.CriticalAddress)
    {
      OperateResult operateResult = await funcWrite(address, values);
      return operateResult;
    }
    if (xinJE.AddressStart < xinJE.CriticalAddress)
    {
      OperateResult write = await funcWrite(address, values.SelectBegin<bool>(xinJE.CriticalAddress - xinJE.AddressStart));
      if (!write.IsSuccess)
        return write;
      values = values.RemoveBegin<bool>(xinJE.CriticalAddress - xinJE.AddressStart);
      xinJE.AddressStart = xinJE.CriticalAddress;
      write = (OperateResult) null;
    }
    OperateResult<byte[]> command = XinJEHelper.BuildWriteBoolCommand(xinJE, values);
    if (!command.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) command);
    OperateResult<byte[]> operateResult1 = await modbus.ReadFromCoreServerAsync(command.Content);
    return (OperateResult) operateResult1;
  }

  internal static async Task<OperateResult> WriteAsync(
    IModbus modbus,
    string address,
    bool value,
    Func<string, bool, Task<OperateResult>> funcWrite)
  {
    OperateResult<XinJEAddress> analysis = XinJEAddress.ParseFrom(address, modbus.Station);
    if (!analysis.IsSuccess)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    XinJEAddress xinJE = analysis.Content;
    if (xinJE.AddressStart < xinJE.CriticalAddress)
    {
      OperateResult operateResult = await funcWrite(address, value);
      return operateResult;
    }
    OperateResult operateResult1 = await modbus.WriteAsync(address, new bool[1]
    {
      value
    });
    return operateResult1;
  }
}
