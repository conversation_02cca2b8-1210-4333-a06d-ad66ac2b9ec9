﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.Estun.EstunData
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using System.Text;

#nullable disable
namespace HslCommunication.Robot.Estun;

/// <summary>埃斯顿的数据类对象</summary>
public class EstunData
{
  /// <summary>实例化一个默认的对象</summary>
  public EstunData()
  {
  }

  /// <summary>使用指定的原始字节数据来实例化埃斯顿机器人对象</summary>
  /// <param name="source">原始字节数组</param>
  /// <param name="byteTransform">字节转换</param>
  public EstunData(byte[] source, IByteTransform byteTransform)
  {
    this.LoadBySourceData(source, byteTransform);
  }

  /// <summary>获取或设置当前的手动操作模式</summary>
  public bool ManualMode { get; set; }

  /// <summary>获取或设置当前的自动操作模式</summary>
  public bool AutoMode { get; set; }

  /// <summary>获取或设置当前的远程操作模式</summary>
  public bool RemoteMode { get; set; }

  /// <summary>获取或设置使能状态</summary>
  public bool EnableStatus { get; set; }

  /// <summary>获取或设置运行状态</summary>
  public bool RunStatus { get; set; }

  /// <summary>获取或设置错误状态</summary>
  public bool ErrorStatus { get; set; }

  /// <summary>获取或设置程序运行状态</summary>
  public bool ProgramRunStatus { get; set; }

  /// <summary>机器人正在动作</summary>
  public bool RobotMoving { get; set; }

  /// <summary>获取或设置当前加载的工程名</summary>
  public string ProjectName { get; set; }

  /// <summary>SimDout, 共计64位长度</summary>
  public bool[] DO { get; set; }

  /// <summary>机器人的执行命令状态，16长度的bool数组</summary>
  public ushort RobotCommandStatus { get; set; }

  /// <summary>用户的AO，32个长度</summary>
  public float[] AO { get; set; }

  /// <summary>全局的速度值</summary>
  public short GlobalSpeedValue { get; set; }

  /// <summary>SimDI, 共计64个bit</summary>
  public bool[] DI { get; set; }

  /// <summary>用户的AI，32个长度</summary>
  public float[] AI { get; set; }

  /// <summary>读写标志位</summary>
  public short ReadWriteFlag { get; set; }

  /// <summary>从原始的字节数据里加载</summary>
  /// <param name="source">原始字节数据</param>
  /// <param name="byteTransform">字节转换的类</param>
  public void LoadBySourceData(byte[] source, IByteTransform byteTransform)
  {
    this.ManualMode = source[7].GetBoolByIndex(0);
    this.AutoMode = source[7].GetBoolByIndex(1);
    this.RemoteMode = source[7].GetBoolByIndex(2);
    this.EnableStatus = source[7].GetBoolByIndex(3);
    this.RunStatus = source[7].GetBoolByIndex(4);
    this.ErrorStatus = source[7].GetBoolByIndex(5);
    this.ProgramRunStatus = source[7].GetBoolByIndex(6);
    this.RobotMoving = source[7].GetBoolByIndex(7);
    this.GlobalSpeedValue = byteTransform.TransInt16(source, 2);
    this.ProjectName = Encoding.ASCII.GetString(SoftBasic.BytesReverseByWord(source.SelectMiddle<byte>(8, 20))).TrimEnd(new char[1]);
    this.DO = SoftBasic.BytesReverseByWord(source.SelectMiddle<byte>(28, 8)).ToBoolArray();
    this.RobotCommandStatus = byteTransform.TransUInt16(source, 36);
    this.AO = byteTransform.TransSingle(source, 38, 16 /*0x10*/);
    this.DI = SoftBasic.BytesReverseByWord(source.SelectMiddle<byte>(126, 8)).ToBoolArray();
    this.AI = byteTransform.TransSingle(source, 134, 16 /*0x10*/);
    this.ReadWriteFlag = byteTransform.TransInt16(source, 198);
  }
}
