﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.MQTT.MqttHelper
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Pipe;
using HslCommunication.Core.Security;
using HslCommunication.Reflection;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.MQTT;

/// <summary>
/// Mqtt协议的辅助类，提供了一些协议相关的基础方法，方便客户端和服务器端一起调用。<br />
/// The auxiliary class of the Mqtt protocol provides some protocol-related basic methods for the client and server to call together.
/// </summary>
public class MqttHelper
{
  /// <summary>
  /// 根据数据的总长度，计算出剩余的数据长度信息<br />
  /// According to the total length of the data, calculate the remaining data length information
  /// </summary>
  /// <param name="length">数据的总长度</param>
  /// <returns>计算结果</returns>
  public static OperateResult<byte[]> CalculateLengthToMqttLength(int length)
  {
    if (length > 268435455 /*0x0FFFFFFF*/)
      return new OperateResult<byte[]>(StringResources.Language.MQTTDataTooLong);
    return length < 128 /*0x80*/ ? OperateResult.CreateSuccessResult<byte[]>(new byte[1]
    {
      (byte) length
    }) : (length < 16384 /*0x4000*/ ? OperateResult.CreateSuccessResult<byte[]>(new byte[2]
    {
      (byte) (length % 128 /*0x80*/ + 128 /*0x80*/),
      (byte) (length / 128 /*0x80*/)
    }) : (length < 2097152 /*0x200000*/ ? OperateResult.CreateSuccessResult<byte[]>(new byte[3]
    {
      (byte) (length % 128 /*0x80*/ + 128 /*0x80*/),
      (byte) (length / 128 /*0x80*/ % 128 /*0x80*/ + 128 /*0x80*/),
      (byte) (length / 128 /*0x80*/ / 128 /*0x80*/)
    }) : OperateResult.CreateSuccessResult<byte[]>(new byte[4]
    {
      (byte) (length % 128 /*0x80*/ + 128 /*0x80*/),
      (byte) (length / 128 /*0x80*/ % 128 /*0x80*/ + 128 /*0x80*/),
      (byte) (length / 128 /*0x80*/ / 128 /*0x80*/ % 128 /*0x80*/ + 128 /*0x80*/),
      (byte) (length / 128 /*0x80*/ / 128 /*0x80*/ / 128 /*0x80*/)
    })));
  }

  /// <summary>
  /// 将一个数据打包成一个mqtt协议的内容<br />
  /// Pack a piece of data into a mqtt protocol
  /// </summary>
  /// <param name="control">控制码</param>
  /// <param name="flags">标记</param>
  /// <param name="variableHeader">可变头的字节内容</param>
  /// <param name="payLoad">负载数据</param>
  /// <param name="aesCryptography">AES数据加密对象</param>
  /// <returns>带有是否成功的结果对象</returns>
  public static OperateResult<byte[]> BuildMqttCommand(
    byte control,
    byte flags,
    byte[] variableHeader,
    byte[] payLoad,
    AesCryptography aesCryptography = null)
  {
    control <<= 4;
    return MqttHelper.BuildMqttCommand((byte) ((uint) control | (uint) flags), variableHeader, payLoad, aesCryptography);
  }

  /// <summary>
  /// 将一个数据打包成一个mqtt协议的内容<br />
  /// Pack a piece of data into a mqtt protocol
  /// </summary>
  /// <param name="head">控制码加标记码</param>
  /// <param name="variableHeader">可变头的字节内容</param>
  /// <param name="payLoad">负载数据</param>
  /// <param name="aesCryptography">AES数据加密对象</param>
  /// <returns>带有是否成功的结果对象</returns>
  public static OperateResult<byte[]> BuildMqttCommand(
    byte head,
    byte[] variableHeader,
    byte[] payLoad,
    AesCryptography aesCryptography = null)
  {
    if (variableHeader == null)
      variableHeader = new byte[0];
    if (payLoad == null)
      payLoad = new byte[0];
    if (aesCryptography != null)
      payLoad = aesCryptography.Encrypt(payLoad);
    OperateResult<byte[]> lengthToMqttLength = MqttHelper.CalculateLengthToMqttLength(variableHeader.Length + payLoad.Length);
    if (!lengthToMqttLength.IsSuccess)
      return lengthToMqttLength;
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.WriteByte(head);
    memoryStream.Write(lengthToMqttLength.Content, 0, lengthToMqttLength.Content.Length);
    if (variableHeader.Length != 0)
      memoryStream.Write(variableHeader, 0, variableHeader.Length);
    if (payLoad.Length != 0)
      memoryStream.Write(payLoad, 0, payLoad.Length);
    return OperateResult.CreateSuccessResult<byte[]>(memoryStream.ToArray());
  }

  /// <summary>
  /// 将字符串打包成utf8编码，并且带有2个字节的表示长度的信息<br />
  /// Pack the string into utf8 encoding, and with 2 bytes of length information
  /// </summary>
  /// <param name="message">文本消息</param>
  /// <returns>打包之后的信息</returns>
  public static byte[] BuildSegCommandByString(string message)
  {
    return MqttHelper.BuildSegCommandByString(string.IsNullOrEmpty(message) ? new byte[0] : Encoding.UTF8.GetBytes(message));
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.BuildSegCommandByString(System.String)" />
  public static byte[] BuildSegCommandByString(byte[] message)
  {
    if (message == null)
      message = new byte[0];
    byte[] numArray = new byte[message.Length + 2];
    message.CopyTo((Array) numArray, 2);
    numArray[0] = (byte) (message.Length / 256 /*0x0100*/);
    numArray[1] = (byte) (message.Length % 256 /*0x0100*/);
    return numArray;
  }

  /// <summary>
  /// 从MQTT的缓存信息里，提取文本信息<br />
  /// Extract text information from MQTT cache information
  /// </summary>
  /// <param name="buffer">Mqtt的报文</param>
  /// <param name="index">索引</param>
  /// <returns>值</returns>
  public static string ExtraMsgFromBytes(byte[] buffer, ref int index)
  {
    int num = index;
    int count = (int) buffer[index] * 256 /*0x0100*/ + (int) buffer[index + 1];
    index = index + 2 + count;
    return Encoding.UTF8.GetString(buffer, num + 2, count);
  }

  /// <summary>
  /// 从MQTT的缓存信息里，提取文本信息<br />
  /// Extract text information from MQTT cache information
  /// </summary>
  /// <param name="buffer">Mqtt的报文</param>
  /// <param name="index">索引</param>
  /// <param name="topics">订阅的主题信息</param>
  /// <param name="qosLevels">订阅的QOs信息</param>
  /// <returns>值</returns>
  public static void ExtraSubscribeMsgFromBytes(
    byte[] buffer,
    ref int index,
    List<string> topics,
    List<byte> qosLevels)
  {
    int count = (int) buffer[index] * 256 /*0x0100*/ + (int) buffer[index + 1];
    topics.Add(Encoding.UTF8.GetString(buffer, index + 2, count));
    if (index + 2 + count < buffer.Length)
      qosLevels.Add(buffer[index + 2 + count]);
    else
      qosLevels.Add((byte) 0);
    index = index + 3 + count;
  }

  /// <summary>
  /// 从MQTT的缓存信息里，提取长度信息<br />
  /// Extract length information from MQTT cache information
  /// </summary>
  /// <param name="buffer">Mqtt的报文</param>
  /// <param name="index">索引</param>
  /// <returns>值</returns>
  public static int ExtraIntFromBytes(byte[] buffer, ref int index)
  {
    int num = (int) buffer[index] * 256 /*0x0100*/ + (int) buffer[index + 1];
    index += 2;
    return num;
  }

  /// <summary>
  /// 从MQTT的缓存信息里，提取长度信息<br />
  /// Extract length information from MQTT cache information
  /// </summary>
  /// <param name="data">数据信息</param>
  /// <returns>值</returns>
  public static byte[] BuildIntBytes(int data)
  {
    return new byte[2]
    {
      BitConverter.GetBytes(data)[1],
      BitConverter.GetBytes(data)[0]
    };
  }

  /// <summary>
  /// 创建MQTT连接服务器的报文信息<br />
  /// Create MQTT connection server message information
  /// </summary>
  /// <param name="connectionOptions">连接配置</param>
  /// <param name="protocol">协议的内容</param>
  /// <param name="rsa">数据加密对象</param>
  /// <returns>返回是否成功的信息</returns>
  public static OperateResult<byte[]> BuildConnectMqttCommand(
    MqttConnectionOptions connectionOptions,
    string protocol = "MQTT",
    RSACryptoServiceProvider rsa = null)
  {
    List<byte> byteList1 = new List<byte>();
    byteList1.AddRange((IEnumerable<byte>) new byte[2]
    {
      (byte) 0,
      (byte) 4
    });
    byteList1.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(protocol));
    byteList1.Add((byte) 4);
    byte num = 0;
    if (connectionOptions.WillMessage != null && !string.IsNullOrEmpty(connectionOptions.WillMessage.Topic) && protocol == "MQTT")
      num |= (byte) 4;
    if (connectionOptions.Credentials != null)
      num = (byte) ((uint) (byte) ((uint) num | 128U /*0x80*/) | 64U /*0x40*/);
    if (connectionOptions.CleanSession)
      num |= (byte) 2;
    byteList1.Add(num);
    if (connectionOptions.KeepAlivePeriod.TotalSeconds < 1.0)
      connectionOptions.KeepAlivePeriod = TimeSpan.FromSeconds(1.0);
    byte[] bytes = BitConverter.GetBytes((int) connectionOptions.KeepAlivePeriod.TotalSeconds);
    byteList1.Add(bytes[1]);
    byteList1.Add(bytes[0]);
    List<byte> byteList2 = new List<byte>();
    byteList2.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(connectionOptions.ClientId));
    if (connectionOptions.WillMessage != null && !string.IsNullOrEmpty(connectionOptions.WillMessage.Topic) && protocol == "MQTT")
    {
      byteList2.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(connectionOptions.WillMessage.Topic));
      byteList2.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(connectionOptions.WillMessage.Payload));
    }
    if (connectionOptions.Credentials != null)
    {
      byteList2.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(connectionOptions.Credentials.UserName));
      byteList2.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(connectionOptions.Credentials.Password));
    }
    return rsa == null ? MqttHelper.BuildMqttCommand((byte) 1, (byte) 0, byteList1.ToArray(), byteList2.ToArray()) : MqttHelper.BuildMqttCommand((byte) 1, (byte) 0, rsa.EncryptLargeData(byteList1.ToArray()), rsa.EncryptLargeData(byteList2.ToArray()));
  }

  /// <summary>
  /// 根据服务器返回的信息判断当前的连接是否是可用的<br />
  /// According to the information returned by the server to determine whether the current connection is available
  /// </summary>
  /// <param name="code">功能码</param>
  /// <param name="data">数据内容</param>
  /// <returns>是否可用的连接</returns>
  public static OperateResult CheckConnectBack(byte code, byte[] data)
  {
    if ((int) code >> 4 != 2)
      return new OperateResult("MQTT Connection Back Is Wrong: " + code.ToString());
    if (data.Length < 2)
      return new OperateResult("MQTT Connection Data Is Short: " + SoftBasic.ByteToHexString(data, ' '));
    int num1 = (int) data[1];
    int num2 = (int) data[0];
    return num1 > 0 ? new OperateResult(num1, MqttHelper.GetMqttCodeText(num1)) : OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 获取当前的错误的描述信息<br />
  /// Get a description of the current error
  /// </summary>
  /// <param name="status">状态信息</param>
  /// <returns>描述信息</returns>
  public static string GetMqttCodeText(int status)
  {
    switch (status)
    {
      case 1:
        return StringResources.Language.MQTTStatus01;
      case 2:
        return StringResources.Language.MQTTStatus02;
      case 3:
        return StringResources.Language.MQTTStatus03;
      case 4:
        return StringResources.Language.MQTTStatus04;
      case 5:
        return StringResources.Language.MQTTStatus05;
      default:
        return StringResources.Language.UnknownError;
    }
  }

  /// <summary>
  /// 创建Mqtt发送消息的命令<br />
  /// Create Mqtt command to send messages
  /// </summary>
  /// <param name="message">封装后的消息内容</param>
  /// <param name="aesCryptography">AES数据加密对象</param>
  /// <returns>结果内容</returns>
  public static OperateResult<byte[]> BuildPublishMqttCommand(
    MqttPublishMessage message,
    AesCryptography aesCryptography = null)
  {
    byte flags = 0;
    if (!message.IsSendFirstTime)
      flags |= (byte) 8;
    if (message.Message.Retain)
      flags |= (byte) 1;
    if (message.Message.QualityOfServiceLevel == MqttQualityOfServiceLevel.AtLeastOnce)
      flags |= (byte) 2;
    else if (message.Message.QualityOfServiceLevel == MqttQualityOfServiceLevel.ExactlyOnce)
      flags |= (byte) 4;
    else if (message.Message.QualityOfServiceLevel == MqttQualityOfServiceLevel.OnlyTransfer)
      flags |= (byte) 6;
    List<byte> byteList = new List<byte>();
    byteList.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(message.Message.Topic));
    if (message.Message.QualityOfServiceLevel != 0)
    {
      byteList.Add(BitConverter.GetBytes(message.Identifier)[1]);
      byteList.Add(BitConverter.GetBytes(message.Identifier)[0]);
    }
    return MqttHelper.BuildMqttCommand((byte) 3, flags, byteList.ToArray(), message.Message.Payload, aesCryptography);
  }

  /// <summary>
  /// 创建Mqtt发送消息的命令<br />
  /// Create Mqtt command to send messages
  /// </summary>
  /// <param name="topic">主题消息内容</param>
  /// <param name="payload">数据负载</param>
  /// <param name="retain">是否消息驻留</param>
  /// <param name="aesCryptography">AES数据加密对象</param>
  /// <returns>结果内容</returns>
  public static OperateResult<byte[]> BuildPublishMqttCommand(
    string topic,
    byte[] payload,
    bool retain = false,
    AesCryptography aesCryptography = null)
  {
    return MqttHelper.BuildMqttCommand((byte) 3, retain ? (byte) 1 : (byte) 0, MqttHelper.BuildSegCommandByString(topic), payload, aesCryptography);
  }

  /// <summary>
  /// 创建Mqtt订阅消息的命令<br />
  /// Command to create Mqtt subscription message
  /// </summary>
  /// <param name="message">订阅的主题</param>
  /// <returns>结果内容</returns>
  public static OperateResult<byte[]> BuildSubscribeMqttCommand(MqttSubscribeMessage message)
  {
    List<byte> byteList1 = new List<byte>();
    List<byte> byteList2 = new List<byte>();
    byteList1.Add(BitConverter.GetBytes(message.Identifier)[1]);
    byteList1.Add(BitConverter.GetBytes(message.Identifier)[0]);
    for (int index = 0; index < message.Topics.Length; ++index)
    {
      byteList2.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(message.Topics[index]));
      if (message.QualityOfServiceLevel == MqttQualityOfServiceLevel.AtMostOnce)
        byteList2.AddRange((IEnumerable<byte>) new byte[1]);
      else if (message.QualityOfServiceLevel == MqttQualityOfServiceLevel.AtLeastOnce)
        byteList2.AddRange((IEnumerable<byte>) new byte[1]
        {
          (byte) 1
        });
      else
        byteList2.AddRange((IEnumerable<byte>) new byte[1]
        {
          (byte) 2
        });
    }
    return MqttHelper.BuildMqttCommand((byte) 8, (byte) 2, byteList1.ToArray(), byteList2.ToArray());
  }

  /// <summary>
  /// 创建Mqtt取消订阅消息的命令<br />
  /// Create Mqtt unsubscribe message command
  /// </summary>
  /// <param name="message">订阅的主题</param>
  /// <returns>结果内容</returns>
  public static OperateResult<byte[]> BuildUnSubscribeMqttCommand(MqttSubscribeMessage message)
  {
    List<byte> byteList1 = new List<byte>();
    List<byte> byteList2 = new List<byte>();
    byteList1.Add(BitConverter.GetBytes(message.Identifier)[1]);
    byteList1.Add(BitConverter.GetBytes(message.Identifier)[0]);
    for (int index = 0; index < message.Topics.Length; ++index)
      byteList2.AddRange((IEnumerable<byte>) MqttHelper.BuildSegCommandByString(message.Topics[index]));
    return MqttHelper.BuildMqttCommand((byte) 10, (byte) 2, byteList1.ToArray(), byteList2.ToArray());
  }

  internal static int ExtraQosFromMqttCode(byte code)
  {
    return (((int) code & 4) == 4 ? 2 : 0) + (((int) code & 2) == 2 ? 1 : 0);
  }

  internal static MqttQualityOfServiceLevel GetFromQos(int qos)
  {
    MqttQualityOfServiceLevel fromQos = MqttQualityOfServiceLevel.AtMostOnce;
    switch (qos)
    {
      case 1:
        fromQos = MqttQualityOfServiceLevel.AtLeastOnce;
        break;
      case 2:
        fromQos = MqttQualityOfServiceLevel.ExactlyOnce;
        break;
      case 3:
        fromQos = MqttQualityOfServiceLevel.OnlyTransfer;
        break;
    }
    return fromQos;
  }

  internal static OperateResult<MqttClientApplicationMessage> ParseMqttClientApplicationMessage(
    MqttSession session,
    byte code,
    byte[] data)
  {
    try
    {
      bool flag1 = ((int) code & 8) == 8;
      int qos = MqttHelper.ExtraQosFromMqttCode(code);
      bool flag2 = ((int) code & 1) == 1;
      int num = 0;
      int index = 0;
      string str = MqttHelper.ExtraMsgFromBytes(data, ref index);
      if (qos > 0)
        num = MqttHelper.ExtraIntFromBytes(data, ref index);
      byte[] data1 = SoftBasic.ArrayRemoveBegin<byte>(data, index);
      if (session.IsAesCryptography && data1.Length != 0)
        data1 = session.AesCryptography.Decrypt(data1);
      MqttClientApplicationMessage applicationMessage = new MqttClientApplicationMessage();
      applicationMessage.ClientId = session.ClientId;
      applicationMessage.QualityOfServiceLevel = MqttHelper.GetFromQos(qos);
      applicationMessage.Retain = flag2;
      applicationMessage.Topic = str;
      applicationMessage.UserName = session.UserName;
      applicationMessage.Payload = data1;
      applicationMessage.MsgID = num;
      return OperateResult.CreateSuccessResult<MqttClientApplicationMessage>(applicationMessage);
    }
    catch (Exception ex)
    {
      return new OperateResult<MqttClientApplicationMessage>("ParseMqttClientApplicationMessage failed: " + ex.Message);
    }
  }

  /// <summary>
  /// 解析从MQTT接受的客户端信息，解析成实际的Topic数据及Payload数据<br />
  /// Parse the client information received from MQTT and parse it into actual Topic data and Payload data
  /// </summary>
  /// <param name="mqttCode">MQTT的命令码</param>
  /// <param name="data">接收的MQTT原始的消息内容</param>
  /// <param name="aesCryptography">AES数据加密信息</param>
  /// <returns>解析的数据结果信息</returns>
  public static OperateResult<string, byte[]> ExtraMqttReceiveData(
    byte mqttCode,
    byte[] data,
    AesCryptography aesCryptography = null)
  {
    if (data.Length < 2)
      return new OperateResult<string, byte[]>(StringResources.Language.ReceiveDataLengthTooShort + data.Length.ToString());
    int count = (int) data[0] * 256 /*0x0100*/ + (int) data[1];
    if (data.Length < 2 + count)
      return new OperateResult<string, byte[]>($"Code[{mqttCode:X2}] ExtraMqttReceiveData Error: {SoftBasic.ByteToHexString(data, ' ')}");
    string str = count > 0 ? Encoding.UTF8.GetString(data, 2, count) : string.Empty;
    byte[] numArray = new byte[data.Length - count - 2];
    Array.Copy((Array) data, count + 2, (Array) numArray, 0, numArray.Length);
    if (aesCryptography != null)
    {
      try
      {
        numArray = aesCryptography.Decrypt(numArray);
      }
      catch (Exception ex)
      {
        return new OperateResult<string, byte[]>("AES Decrypt failed: " + ex.Message);
      }
    }
    return OperateResult.CreateSuccessResult<string, byte[]>(str, numArray);
  }

  /// <summary>
  /// 使用指定的对象来返回网络的API接口，前提是传入的数据为json参数，返回的数据为json数据，详细参照说明<br />
  /// Use the specified object to return the API interface of the network,
  /// provided that the incoming data is json parameters and the returned data is json data,
  /// please refer to the description for details
  /// </summary>
  /// <param name="mqttSession">当前的对话状态</param>
  /// <param name="message">当前传入的消息内容</param>
  /// <param name="obj">等待解析的api解析的对象</param>
  /// <returns>等待返回客户的结果</returns>
  public static async Task<OperateResult<string>> HandleObjectMethod(
    MqttSession mqttSession,
    MqttClientApplicationMessage message,
    object obj)
  {
    string method = message.Topic;
    if (method.LastIndexOf('/') >= 0)
      method = method.Substring(method.LastIndexOf('/') + 1);
    MethodInfo methodInfo = obj.GetType().GetMethod(method);
    if (methodInfo == (MethodInfo) null)
      return new OperateResult<string>($"Current MqttSync Api ：[{method}] not exsist");
    OperateResult<MqttRpcApiInfo> apiResult = MqttHelper.GetMqttSyncServicesApiFromMethod("", methodInfo, obj);
    if (!apiResult.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) apiResult);
    OperateResult<string> operateResult = await MqttHelper.HandleObjectMethod(mqttSession, message, apiResult.Content);
    return operateResult;
  }

  /// <summary>
  /// 使用指定的对象来返回网络的API接口，前提是传入的数据为json参数，返回的数据为json数据，详细参照说明<br />
  /// Use the specified object to return the API interface of the network,
  /// provided that the incoming data is json parameters and the returned data is json data,
  /// please refer to the description for details
  /// </summary>
  /// <param name="mqttSession">当前的对话状态</param>
  /// <param name="message">当前传入的消息内容</param>
  /// <param name="apiInformation">当前已经解析好的Api内容对象</param>
  /// <returns>等待返回客户的结果</returns>
  public static async Task<OperateResult<string>> HandleObjectMethod(
    MqttSession mqttSession,
    MqttClientApplicationMessage message,
    MqttRpcApiInfo apiInformation)
  {
    object retObject = (object) null;
    if (apiInformation.PermissionAttribute != null)
    {
      if (!HslCommunication.Authorization.asdniasnfaksndiqwhawfskhfaiw())
        return new OperateResult<string>("Permission function need authorization ：" + StringResources.Language.InsufficientPrivileges);
      if (!apiInformation.PermissionAttribute.CheckClientID(mqttSession.ClientId))
        return new OperateResult<string>($"Mqtt RPC Api ：[{apiInformation.ApiTopic}] Check ClientID[{mqttSession.ClientId}] failed, access not permission");
      if (!apiInformation.PermissionAttribute.CheckUserName(mqttSession.UserName))
        return new OperateResult<string>($"Mqtt RPC Api ：[{apiInformation.ApiTopic}] Check Username[{mqttSession.UserName}] failed, access not permission");
    }
    try
    {
      if (apiInformation.Method != (MethodInfo) null)
      {
        string json = Encoding.UTF8.GetString(message.Payload);
        JObject jObject = string.IsNullOrEmpty(json) ? new JObject() : JObject.Parse(json);
        object[] paras = HslReflectionHelper.GetParametersFromJson((ISessionContext) mqttSession, (HttpListenerRequest) null, apiInformation.Method.GetParameters(), json);
        object obj = apiInformation.Method.Invoke(apiInformation.SourceObject, paras);
        if (obj is Task task)
        {
          await task;
          retObject = task.GetType().GetProperty("Result")?.GetValue((object) task, (object[]) null);
        }
        else
          retObject = obj;
        json = (string) null;
        obj = (object) null;
        task = (Task) null;
      }
      else if (apiInformation.Property != (PropertyInfo) null)
        retObject = apiInformation.Property.GetValue(apiInformation.SourceObject, (object[]) null);
    }
    catch (TargetInvocationException ex)
    {
      return new OperateResult<string>($"Mqtt RPC Api Call：[{apiInformation.ApiTopic}] Wrong，Reason：{SoftBasic.GetExceptionMessage(ex.InnerException)}");
    }
    catch (Exception ex)
    {
      string json = message.Payload == null ? string.Empty : Encoding.UTF8.GetString(message.Payload);
      return new OperateResult<string>($"Mqtt RPC Api Parse Json：[{apiInformation.ApiTopic}] Wrong，Reason：{ex.Message}{Environment.NewLine}Json: {json}");
    }
    return HslReflectionHelper.GetOperateResultJsonFromObj(retObject);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.GetSyncServicesApiInformationFromObject(System.String,System.Object,HslCommunication.Reflection.HslMqttPermissionAttribute)" />
  public static List<MqttRpcApiInfo> GetSyncServicesApiInformationFromObject(object obj)
  {
    return obj is Type type ? MqttHelper.GetSyncServicesApiInformationFromObject(type.Name, (object) type) : MqttHelper.GetSyncServicesApiInformationFromObject(obj.GetType().Name, obj);
  }

  /// <summary>
  /// 根据当前的对象定义的方法信息，获取到所有支持ApiTopic的方法列表信息，包含API名称，示例参数数据，描述信息。<br />
  /// According to the method information defined by the current object, the list information of all methods that support ApiTopic is obtained,
  /// including the API name, sample parameter data, and description information.
  /// </summary>
  /// <param name="api">指定的ApiTopic的前缀，可以理解为控制器，如果为空，就不携带控制器。</param>
  /// <param name="obj">实际的等待解析的对象</param>
  /// <param name="permissionAttribute">默认的权限特性</param>
  /// <returns>返回所有API说明的列表，类型为<see cref="T:HslCommunication.MQTT.MqttRpcApiInfo" /></returns>
  public static List<MqttRpcApiInfo> GetSyncServicesApiInformationFromObject(
    string api,
    object obj,
    HslMqttPermissionAttribute permissionAttribute = null)
  {
    Type type1;
    if (obj is Type type2)
    {
      type1 = type2;
      obj = (object) null;
    }
    else
      type1 = obj.GetType();
    MethodInfo[] methods = type1.GetMethods();
    List<MqttRpcApiInfo> informationFromObject1 = new List<MqttRpcApiInfo>();
    foreach (MethodInfo method in methods)
    {
      OperateResult<MqttRpcApiInfo> servicesApiFromMethod = MqttHelper.GetMqttSyncServicesApiFromMethod(api, method, obj, permissionAttribute);
      if (servicesApiFromMethod.IsSuccess)
        informationFromObject1.Add(servicesApiFromMethod.Content);
    }
    foreach (PropertyInfo property in type1.GetProperties())
    {
      OperateResult<HslMqttApiAttribute, MqttRpcApiInfo> servicesApiFromProperty = MqttHelper.GetMqttSyncServicesApiFromProperty(api, property, obj, permissionAttribute);
      if (servicesApiFromProperty.IsSuccess)
      {
        if (!servicesApiFromProperty.Content1.PropertyUnfold)
          informationFromObject1.Add(servicesApiFromProperty.Content2);
        else if (property.GetValue(obj, (object[]) null) != null)
        {
          List<MqttRpcApiInfo> informationFromObject2 = MqttHelper.GetSyncServicesApiInformationFromObject(servicesApiFromProperty.Content2.ApiTopic, property.GetValue(obj, (object[]) null), permissionAttribute);
          informationFromObject1.AddRange((IEnumerable<MqttRpcApiInfo>) informationFromObject2);
        }
      }
    }
    return informationFromObject1;
  }

  private static string GetReturnTypeDescription(Type returnType)
  {
    if (!returnType.IsSubclassOf(typeof (OperateResult)))
      return returnType.Name;
    if (returnType == typeof (OperateResult))
      return returnType.Name;
    if (returnType.GetProperty("Content") != (PropertyInfo) null)
      return $"OperateResult<{returnType.GetProperty("Content").PropertyType.Name}>";
    StringBuilder stringBuilder = new StringBuilder("OperateResult<");
    for (int index = 1; index <= 10 && returnType.GetProperty("Content" + index.ToString()) != (PropertyInfo) null; ++index)
    {
      if (index != 1)
        stringBuilder.Append(",");
      stringBuilder.Append(returnType.GetProperty("Content" + index.ToString()).PropertyType.Name);
    }
    stringBuilder.Append(">");
    return stringBuilder.ToString();
  }

  /// <summary>
  /// 根据当前的方法的委托信息和类对象，生成<see cref="T:HslCommunication.MQTT.MqttRpcApiInfo" />的API对象信息。
  /// </summary>
  /// <param name="api">Api头信息</param>
  /// <param name="method">方法的委托</param>
  /// <param name="obj">当前注册的API的源对象</param>
  /// <param name="permissionAttribute">默认的权限特性</param>
  /// <returns>返回是否成功的结果对象</returns>
  public static OperateResult<MqttRpcApiInfo> GetMqttSyncServicesApiFromMethod(
    string api,
    MethodInfo method,
    object obj,
    HslMqttPermissionAttribute permissionAttribute = null)
  {
    object[] customAttributes1 = method.GetCustomAttributes(typeof (HslMqttApiAttribute), false);
    if (customAttributes1 == null || customAttributes1.Length == 0)
      return new OperateResult<MqttRpcApiInfo>($"Current Api ：[{method}] not support Api attribute");
    HslMqttApiAttribute mqttApiAttribute = (HslMqttApiAttribute) customAttributes1[0];
    MqttRpcApiInfo mqttRpcApiInfo = new MqttRpcApiInfo();
    mqttRpcApiInfo.SourceObject = obj;
    mqttRpcApiInfo.Method = method;
    mqttRpcApiInfo.Description = mqttApiAttribute.Description;
    mqttRpcApiInfo.HttpMethod = mqttApiAttribute.HttpMethod.ToUpper();
    if (string.IsNullOrEmpty(mqttApiAttribute.ApiTopic))
      mqttApiAttribute.ApiTopic = method.Name;
    if (permissionAttribute == null)
    {
      object[] customAttributes2 = method.GetCustomAttributes(typeof (HslMqttPermissionAttribute), false);
      if (customAttributes2 != null && customAttributes2.Length != 0)
        mqttRpcApiInfo.PermissionAttribute = (HslMqttPermissionAttribute) customAttributes2[0];
    }
    else
      mqttRpcApiInfo.PermissionAttribute = permissionAttribute;
    mqttRpcApiInfo.ApiTopic = !string.IsNullOrEmpty(api) ? $"{api}/{mqttApiAttribute.ApiTopic}" : mqttApiAttribute.ApiTopic;
    ParameterInfo[] parameters = method.GetParameters();
    StringBuilder stringBuilder = new StringBuilder();
    if (method.ReturnType.IsSubclassOf(typeof (Task)))
      stringBuilder.Append($"Task<{MqttHelper.GetReturnTypeDescription(method.ReturnType.GetProperty("Result").PropertyType)}>");
    else
      stringBuilder.Append(MqttHelper.GetReturnTypeDescription(method.ReturnType));
    stringBuilder.Append(" ");
    stringBuilder.Append(mqttRpcApiInfo.ApiTopic);
    stringBuilder.Append("(");
    for (int index = 0; index < parameters.Length; ++index)
    {
      if (parameters[index].ParameterType != typeof (ISessionContext) && parameters[index].ParameterType != typeof (HttpListenerRequest))
      {
        stringBuilder.Append(parameters[index].ParameterType.Name);
        stringBuilder.Append(" ");
        stringBuilder.Append(parameters[index].Name);
        if (index != parameters.Length - 1)
          stringBuilder.Append(",");
      }
    }
    stringBuilder.Append(")");
    mqttRpcApiInfo.MethodSignature = stringBuilder.ToString();
    mqttRpcApiInfo.ExamplePayload = HslReflectionHelper.GetParametersFromJson(method, parameters).ToString();
    return OperateResult.CreateSuccessResult<MqttRpcApiInfo>(mqttRpcApiInfo);
  }

  /// <summary>
  /// 根据当前的方法的委托信息和类对象，生成<see cref="T:HslCommunication.MQTT.MqttRpcApiInfo" />的API对象信息。
  /// </summary>
  /// <param name="api">Api头信息</param>
  /// <param name="property">方法的委托</param>
  /// <param name="obj">当前注册的API的源对象</param>
  /// <param name="permissionAttribute">默认的权限特性</param>
  /// <returns>返回是否成功的结果对象</returns>
  public static OperateResult<HslMqttApiAttribute, MqttRpcApiInfo> GetMqttSyncServicesApiFromProperty(
    string api,
    PropertyInfo property,
    object obj,
    HslMqttPermissionAttribute permissionAttribute = null)
  {
    object[] customAttributes1 = property.GetCustomAttributes(typeof (HslMqttApiAttribute), false);
    if (customAttributes1 == null || customAttributes1.Length == 0)
      return new OperateResult<HslMqttApiAttribute, MqttRpcApiInfo>($"Current Api ：[{property}] not support Api attribute");
    HslMqttApiAttribute mqttApiAttribute = (HslMqttApiAttribute) customAttributes1[0];
    MqttRpcApiInfo mqttRpcApiInfo = new MqttRpcApiInfo();
    mqttRpcApiInfo.SourceObject = obj;
    mqttRpcApiInfo.Property = property;
    mqttRpcApiInfo.Description = mqttApiAttribute.Description;
    mqttRpcApiInfo.HttpMethod = mqttApiAttribute.HttpMethod.ToUpper();
    if (string.IsNullOrEmpty(mqttApiAttribute.ApiTopic))
      mqttApiAttribute.ApiTopic = property.Name;
    if (permissionAttribute == null)
    {
      object[] customAttributes2 = property.GetCustomAttributes(typeof (HslMqttPermissionAttribute), false);
      if (customAttributes2 != null && customAttributes2.Length != 0)
        mqttRpcApiInfo.PermissionAttribute = (HslMqttPermissionAttribute) customAttributes2[0];
    }
    else
      mqttRpcApiInfo.PermissionAttribute = permissionAttribute;
    mqttRpcApiInfo.ApiTopic = !string.IsNullOrEmpty(api) ? $"{api}/{mqttApiAttribute.ApiTopic}" : mqttApiAttribute.ApiTopic;
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.Append(MqttHelper.GetReturnTypeDescription(property.PropertyType));
    stringBuilder.Append(" ");
    stringBuilder.Append(mqttRpcApiInfo.ApiTopic);
    stringBuilder.Append(" { ");
    if (property.CanRead)
      stringBuilder.Append("get; ");
    if (property.CanWrite)
      stringBuilder.Append("set; ");
    stringBuilder.Append("}");
    mqttRpcApiInfo.MethodSignature = stringBuilder.ToString();
    mqttRpcApiInfo.ExamplePayload = string.Empty;
    return OperateResult.CreateSuccessResult<HslMqttApiAttribute, MqttRpcApiInfo>(mqttApiAttribute, mqttRpcApiInfo);
  }

  /// <summary>判断当前服务器的实际的 topic 的主题，是否满足通配符格式的订阅主题 subTopic</summary>
  /// <param name="topic">服务器的实际的主题信息</param>
  /// <param name="subTopic">客户端订阅的基于通配符的格式</param>
  /// <returns>如果返回True, 说明当前匹配成功，应该发送订阅操作</returns>
  public static bool CheckMqttTopicWildcards(string topic, string subTopic)
  {
    if (subTopic == "#")
      return true;
    if (subTopic.EndsWith("/#"))
    {
      if (subTopic.Contains("/+/"))
      {
        subTopic = subTopic.Replace("[", "\\[");
        subTopic = subTopic.Replace("]", "\\]");
        subTopic = subTopic.Replace(".", "\\.");
        subTopic = subTopic.Replace("*", "\\*");
        subTopic = subTopic.Replace("{", "\\{");
        subTopic = subTopic.Replace("}", "\\}");
        subTopic = subTopic.Replace("?", "\\?");
        subTopic = subTopic.Replace("$", "\\$");
        subTopic = subTopic.Replace("/+", "/[^/]+");
        subTopic = subTopic.RemoveLast(2);
        subTopic += "(/[\\S\\s]+$|$)";
        return Regex.IsMatch(topic, subTopic);
      }
      return subTopic.Length != 2 && (topic == subTopic.RemoveLast(2) || topic.StartsWith(subTopic.RemoveLast(1)));
    }
    if (subTopic == "+")
      return !topic.Contains("/");
    if (subTopic.EndsWith("/+"))
      return subTopic.Length != 2 && topic.StartsWith(subTopic.RemoveLast(1)) && topic.Length != subTopic.Length - 1 && !topic.Substring(subTopic.Length - 1).Contains("/");
    if (!subTopic.Contains("/+/"))
      return topic == subTopic;
    subTopic = subTopic.Replace("[", "\\[");
    subTopic = subTopic.Replace("]", "\\]");
    subTopic = subTopic.Replace(".", "\\.");
    subTopic = subTopic.Replace("*", "\\*");
    subTopic = subTopic.Replace("{", "\\{");
    subTopic = subTopic.Replace("}", "\\}");
    subTopic = subTopic.Replace("?", "\\?");
    subTopic = subTopic.Replace("$", "\\$");
    subTopic = subTopic.Replace("/+", "/[^/]+");
    return Regex.IsMatch(topic, subTopic);
  }

  private static OperateResult<int> CalculateMqttRemainingLength(List<byte> buffer)
  {
    if (buffer.Count > 4)
      return new OperateResult<int>("Receive Length is too long!");
    if (buffer.Count == 1)
      return OperateResult.CreateSuccessResult<int>((int) buffer[0]);
    if (buffer.Count == 2)
      return OperateResult.CreateSuccessResult<int>((int) buffer[0] - 128 /*0x80*/ + (int) buffer[1] * 128 /*0x80*/);
    return buffer.Count == 3 ? OperateResult.CreateSuccessResult<int>((int) buffer[0] - 128 /*0x80*/ + ((int) buffer[1] - 128 /*0x80*/) * 128 /*0x80*/ + (int) buffer[2] * 128 /*0x80*/ * 128 /*0x80*/) : OperateResult.CreateSuccessResult<int>((int) buffer[0] - 128 /*0x80*/ + ((int) buffer[1] - 128 /*0x80*/) * 128 /*0x80*/ + ((int) buffer[2] - 128 /*0x80*/) * 128 /*0x80*/ * 128 /*0x80*/ + (int) buffer[3] * 128 /*0x80*/ * 128 /*0x80*/ * 128 /*0x80*/);
  }

  /// <summary>
  /// 基于MQTT协议，从网络套接字中接收剩余的数据长度<br />
  /// Receives the remaining data length from the network socket based on the MQTT protocol
  /// </summary>
  /// <param name="pipe">实际的管道对象信息</param>
  /// <returns>网络中剩余的长度数据</returns>
  public static OperateResult<int> ReceiveMqttRemainingLength(CommunicationPipe pipe)
  {
    List<byte> buffer = new List<byte>();
    OperateResult<byte[]> result;
    do
    {
      result = pipe.Receive(1, 10000);
      if (result.IsSuccess)
        buffer.Add(result.Content[0]);
      else
        goto label_1;
    }
    while (result.Content[0] >= (byte) 128 /*0x80*/ && buffer.Count < 4);
    goto label_4;
label_1:
    return OperateResult.CreateFailedResult<int>((OperateResult) result);
label_4:
    return MqttHelper.CalculateMqttRemainingLength(buffer);
  }

  /// <summary>
  /// 接收一条完整的MQTT协议的报文信息，包含控制码和负载数据<br />
  /// Receive a message of a completed MQTT protocol, including control code and payload data
  /// </summary>
  /// <param name="pipe">实际的管道对象信息</param>
  /// <param name="timeOut">超时时间</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <returns>结果数据内容</returns>
  public static OperateResult<byte, byte[]> ReceiveMqttMessage(
    CommunicationPipe pipe,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    OperateResult<byte[]> result1 = pipe.Receive(1, timeOut);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) result1);
    OperateResult<int> mqttRemainingLength = MqttHelper.ReceiveMqttRemainingLength(pipe);
    if (!mqttRemainingLength.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) mqttRemainingLength);
    if ((int) result1.Content[0] >> 4 == 15)
      reportProgress = (Action<long, long>) null;
    if ((int) result1.Content[0] >> 4 == 0)
      reportProgress = (Action<long, long>) null;
    OperateResult<byte[]> result2 = pipe.Receive(mqttRemainingLength.Content, 60000, reportProgress);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<byte, byte[]>(result1.Content[0], result2.Content);
  }

  /// <summary>
  /// 基于MQTT协议，从网络套接字中接收剩余的数据长度<br />
  /// Receives the remaining data length from the network socket based on the MQTT protocol
  /// </summary>
  /// <typeparam name="T">当前的管道类型</typeparam>
  /// <param name="receive">接收数据的方法</param>
  /// <param name="pipe">实际的管道对象信息</param>
  /// <returns>网络中剩余的长度数据</returns>
  public static OperateResult<int> ReceiveMqttRemainingLength<T>(
    Func<T, int, int, Action<long, long>, OperateResult<byte[]>> receive,
    T pipe)
  {
    List<byte> buffer = new List<byte>();
    OperateResult<byte[]> result;
    do
    {
      result = receive(pipe, 1, 10000, (Action<long, long>) null);
      if (result.IsSuccess)
        buffer.Add(result.Content[0]);
      else
        goto label_1;
    }
    while (result.Content[0] >= (byte) 128 /*0x80*/ && buffer.Count < 4);
    goto label_4;
label_1:
    return OperateResult.CreateFailedResult<int>((OperateResult) result);
label_4:
    return MqttHelper.CalculateMqttRemainingLength(buffer);
  }

  /// <summary>
  /// 接收一条完整的MQTT协议的报文信息，包含控制码和负载数据<br />
  /// Receive a message of a completed MQTT protocol, including control code and payload data
  /// </summary>
  /// <typeparam name="T">当前的管道类型</typeparam>
  /// <param name="receive">接收数据的方法</param>
  /// <param name="pipe">实际的管道对象信息</param>
  /// <param name="timeOut">超时时间</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <returns>结果数据内容</returns>
  public static OperateResult<byte, byte[]> ReceiveMqttMessage<T>(
    Func<T, int, int, Action<long, long>, OperateResult<byte[]>> receive,
    T pipe,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    OperateResult<byte[]> result1 = receive(pipe, 1, timeOut, (Action<long, long>) null);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) result1);
    OperateResult<int> mqttRemainingLength = MqttHelper.ReceiveMqttRemainingLength<T>(receive, pipe);
    if (!mqttRemainingLength.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) mqttRemainingLength);
    if ((int) result1.Content[0] >> 4 == 15)
      reportProgress = (Action<long, long>) null;
    if ((int) result1.Content[0] >> 4 == 0)
      reportProgress = (Action<long, long>) null;
    OperateResult<byte[]> result2 = receive(pipe, mqttRemainingLength.Content, 60000, reportProgress);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) result2) : OperateResult.CreateSuccessResult<byte, byte[]>(result1.Content[0], result2.Content);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.ReceiveMqttRemainingLength(HslCommunication.Core.Pipe.CommunicationPipe)" />
  public static async Task<OperateResult<int>> ReceiveMqttRemainingLengthAsync(
    CommunicationPipe pipe)
  {
    List<byte> buffer = new List<byte>();
    OperateResult<byte[]> read;
    while (true)
    {
      read = await pipe.ReceiveAsync(1, 10000).ConfigureAwait(false);
      if (read.IsSuccess)
      {
        buffer.Add(read.Content[0]);
        if (read.Content[0] >= (byte) 128 /*0x80*/ && buffer.Count < 4)
          read = (OperateResult<byte[]>) null;
        else
          goto label_6;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<int>((OperateResult) read);
label_6:
    return MqttHelper.CalculateMqttRemainingLength(buffer);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.ReceiveMqttMessage(HslCommunication.Core.Pipe.CommunicationPipe,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static async Task<OperateResult<byte, byte[]>> ReceiveMqttMessageAsync(
    CommunicationPipe pipe,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable = pipe.ReceiveAsync(1, timeOut).ConfigureAwait(false);
    OperateResult<byte[]> readCode = await configuredTaskAwaitable;
    if (!readCode.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) readCode);
    OperateResult<int> readContentLength = await MqttHelper.ReceiveMqttRemainingLengthAsync(pipe).ConfigureAwait(false);
    if (!readContentLength.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) readContentLength);
    if ((int) readCode.Content[0] >> 4 == 15)
      reportProgress = (Action<long, long>) null;
    if ((int) readCode.Content[0] >> 4 == 0)
      reportProgress = (Action<long, long>) null;
    configuredTaskAwaitable = pipe.ReceiveAsync(readContentLength.Content, 60000, reportProgress).ConfigureAwait(false);
    OperateResult<byte[]> readContent = await configuredTaskAwaitable;
    return readContent.IsSuccess ? OperateResult.CreateSuccessResult<byte, byte[]>(readCode.Content[0], readContent.Content) : OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) readContent);
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.ReceiveMqttRemainingLength``1(System.Func{``0,System.Int32,System.Int32,System.Action{System.Int64,System.Int64},HslCommunication.OperateResult{System.Byte[]}},``0)" />
  public static async Task<OperateResult<int>> ReceiveMqttRemainingLengthAsync<T>(
    Func<T, int, int, Action<long, long>, Task<OperateResult<byte[]>>> receive,
    T pipe)
  {
    List<byte> buffer = new List<byte>();
    OperateResult<byte[]> rece;
    while (true)
    {
      rece = await receive(pipe, 1, 10000, (Action<long, long>) null);
      if (rece.IsSuccess)
      {
        buffer.Add(rece.Content[0]);
        if (rece.Content[0] >= (byte) 128 /*0x80*/ && buffer.Count < 4)
          rece = (OperateResult<byte[]>) null;
        else
          goto label_6;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<int>((OperateResult) rece);
label_6:
    return buffer.Count <= 4 ? (buffer.Count != 1 ? (buffer.Count != 2 ? (buffer.Count != 3 ? OperateResult.CreateSuccessResult<int>((int) buffer[0] - 128 /*0x80*/ + ((int) buffer[1] - 128 /*0x80*/) * 128 /*0x80*/ + ((int) buffer[2] - 128 /*0x80*/) * 128 /*0x80*/ * 128 /*0x80*/ + (int) buffer[3] * 128 /*0x80*/ * 128 /*0x80*/ * 128 /*0x80*/) : OperateResult.CreateSuccessResult<int>((int) buffer[0] - 128 /*0x80*/ + ((int) buffer[1] - 128 /*0x80*/) * 128 /*0x80*/ + (int) buffer[2] * 128 /*0x80*/ * 128 /*0x80*/)) : OperateResult.CreateSuccessResult<int>((int) buffer[0] - 128 /*0x80*/ + (int) buffer[1] * 128 /*0x80*/)) : OperateResult.CreateSuccessResult<int>((int) buffer[0])) : new OperateResult<int>("Receive Length is too long!");
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.ReceiveMqttMessage``1(System.Func{``0,System.Int32,System.Int32,System.Action{System.Int64,System.Int64},HslCommunication.OperateResult{System.Byte[]}},``0,System.Int32,System.Action{System.Int64,System.Int64})" />
  public static async Task<OperateResult<byte, byte[]>> ReceiveMqttMessageAsync<T>(
    Func<T, int, int, Action<long, long>, Task<OperateResult<byte[]>>> receive,
    T pipe,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    OperateResult<byte[]> readCode = await receive(pipe, 1, timeOut, (Action<long, long>) null);
    if (!readCode.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) readCode);
    OperateResult<int> readContentLength = await MqttHelper.ReceiveMqttRemainingLengthAsync<T>(receive, pipe);
    if (!readContentLength.IsSuccess)
      return OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) readContentLength);
    if ((int) readCode.Content[0] >> 4 == 15)
      reportProgress = (Action<long, long>) null;
    if ((int) readCode.Content[0] >> 4 == 0)
      reportProgress = (Action<long, long>) null;
    OperateResult<byte[]> readContent = await receive(pipe, readContentLength.Content, timeOut, reportProgress);
    return readContent.IsSuccess ? OperateResult.CreateSuccessResult<byte, byte[]>(readCode.Content[0], readContent.Content) : OperateResult.CreateFailedResult<byte, byte[]>((OperateResult) readContent);
  }

  /// <summary>
  /// 使用MQTT协议从socket接收指定长度的字节数组，然后全部写入到流中，可以指定进度报告<br />
  /// Use the MQTT protocol to receive a byte array of specified length from the socket, and then write all of them to the stream, and you can specify a progress report
  /// </summary>
  /// <param name="pipe">当前的管道对象信息</param>
  /// <param name="stream">数据流</param>
  /// <param name="fileSize">数据大小</param>
  /// <param name="timeOut">超时时间</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">取消的令牌操作信息</param>
  /// <returns>是否操作成功</returns>
  public static OperateResult ReceiveMqttStream(
    CommunicationPipe pipe,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    long num = 0;
    while (num < fileSize)
    {
      OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(pipe, timeOut);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      if (mqttMessage.Content1 == (byte) 0)
      {
        pipe?.CloseCommunication();
        return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
      }
      if (aesCryptography != null)
      {
        try
        {
          mqttMessage.Content2 = aesCryptography.Decrypt(mqttMessage.Content2);
        }
        catch (Exception ex)
        {
          pipe?.CloseCommunication();
          return new OperateResult("AES Decrypt file stream failed: " + ex.Message);
        }
      }
      OperateResult mqttStream1 = NetSupport.WriteStream(stream, mqttMessage.Content2);
      if (!mqttStream1.IsSuccess)
        return mqttStream1;
      num += (long) mqttMessage.Content2.Length;
      byte[] payLoad = new byte[16 /*0x10*/];
      BitConverter.GetBytes(num).CopyTo((Array) payLoad, 0);
      BitConverter.GetBytes(fileSize).CopyTo((Array) payLoad, 8);
      if (cancelToken != null && cancelToken.IsCancelled)
      {
        OperateResult mqttStream2 = pipe.Send(MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content);
        if (!mqttStream2.IsSuccess)
        {
          pipe?.CloseCommunication();
          return mqttStream2;
        }
        pipe?.CloseCommunication();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      OperateResult mqttStream3 = pipe.Send(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, payLoad).Content);
      if (!mqttStream3.IsSuccess)
        return mqttStream3;
      if (reportProgress != null)
        reportProgress(num, fileSize);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.ReceiveMqttStream(HslCommunication.Core.Pipe.CommunicationPipe,System.IO.Stream,System.Int64,System.Int32,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  public static async Task<OperateResult> ReceiveMqttStreamAsync(
    CommunicationPipe pipe,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    long already = 0;
    while (already < fileSize)
    {
      OperateResult<byte, byte[]> receive = await MqttHelper.ReceiveMqttMessageAsync(pipe, timeOut).ConfigureAwait(false);
      if (!receive.IsSuccess)
        return (OperateResult) receive;
      if (receive.Content1 == (byte) 0)
      {
        pipe?.CloseCommunication();
        return new OperateResult(Encoding.UTF8.GetString(receive.Content2));
      }
      if (aesCryptography != null)
      {
        try
        {
          receive.Content2 = aesCryptography.Decrypt(receive.Content2);
        }
        catch (Exception ex)
        {
          pipe?.CloseCommunication();
          return new OperateResult("AES Decrypt file stream failed: " + ex.Message);
        }
      }
      OperateResult write = await NetSupport.WriteStreamAsync(stream, receive.Content2);
      if (!write.IsSuccess)
        return write;
      already += (long) receive.Content2.Length;
      byte[] ack = new byte[16 /*0x10*/];
      BitConverter.GetBytes(already).CopyTo((Array) ack, 0);
      BitConverter.GetBytes(fileSize).CopyTo((Array) ack, 8);
      HslCancelToken hslCancelToken = cancelToken;
      if (hslCancelToken != null && hslCancelToken.IsCancelled)
      {
        OperateResult cancel = await pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content).ConfigureAwait(false);
        if (!cancel.IsSuccess)
        {
          pipe?.CloseCommunication();
          return cancel;
        }
        pipe?.CloseCommunication();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      OperateResult send = await pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, ack).Content).ConfigureAwait(false);
      if (!send.IsSuccess)
        return send;
      Action<long, long> action = reportProgress;
      if (action != null)
        action(already, fileSize);
      receive = (OperateResult<byte, byte[]>) null;
      write = (OperateResult) null;
      ack = (byte[]) null;
      send = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 使用MQTT协议将流中的数据读取到字节数组，然后都写入到socket里面，可以指定进度报告，主要用于将文件发送到网络。<br />
  /// Use the MQTT protocol to read the data in the stream into a byte array, and then write them all into the socket.
  /// You can specify a progress report, which is mainly used to send files to the network.
  /// </summary>
  /// <param name="pipe">当前的管道对象信息</param>
  /// <param name="stream">流</param>
  /// <param name="fileSize">总的数据大小</param>
  /// <param name="timeOut">超时信息</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">取消操作的令牌信息</param>
  /// <returns>是否操作成功</returns>
  public static OperateResult SendMqttStream(
    CommunicationPipe pipe,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    byte[] buffer = new byte[102400 /*0x019000*/];
    long num = 0;
    stream.Position = 0L;
    while (num < fileSize)
    {
      OperateResult<int> operateResult1 = NetSupport.ReadStream(stream, buffer);
      if (!operateResult1.IsSuccess)
      {
        pipe?.CloseCommunication();
        return (OperateResult) operateResult1;
      }
      num += (long) operateResult1.Content;
      if (cancelToken != null && cancelToken.IsCancelled)
      {
        OperateResult operateResult2 = pipe.Send(MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content);
        if (!operateResult2.IsSuccess)
        {
          pipe?.CloseCommunication();
          return operateResult2;
        }
        pipe?.CloseCommunication();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      OperateResult operateResult3 = pipe.Send(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, buffer.SelectBegin<byte>(operateResult1.Content), aesCryptography).Content);
      if (!operateResult3.IsSuccess)
      {
        pipe?.CloseCommunication();
        return operateResult3;
      }
      OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(pipe, timeOut);
      if (!mqttMessage.IsSuccess)
        return (OperateResult) mqttMessage;
      if (mqttMessage.Content1 == (byte) 0)
      {
        pipe?.CloseCommunication();
        return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
      }
      if (reportProgress != null)
        reportProgress(num, fileSize);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.SendMqttStream(HslCommunication.Core.Pipe.CommunicationPipe,System.IO.Stream,System.Int64,System.Int32,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  public static async Task<OperateResult> SendMqttStreamAsync(
    CommunicationPipe pipe,
    Stream stream,
    long fileSize,
    int timeOut,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    byte[] buffer = new byte[102400 /*0x019000*/];
    long already = 0;
    stream.Position = 0L;
    while (already < fileSize)
    {
      OperateResult<int> read = await NetSupport.ReadStreamAsync(stream, buffer).ConfigureAwait(false);
      if (!read.IsSuccess)
      {
        pipe?.CloseCommunication();
        return (OperateResult) read;
      }
      already += (long) read.Content;
      HslCancelToken hslCancelToken = cancelToken;
      ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable;
      if (hslCancelToken != null && hslCancelToken.IsCancelled)
      {
        configuredTaskAwaitable = pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, HslHelper.GetUTF8Bytes(StringResources.Language.UserCancelOperate)).Content).ConfigureAwait(false);
        OperateResult cancel = await configuredTaskAwaitable;
        if (!cancel.IsSuccess)
        {
          pipe?.CloseCommunication();
          return cancel;
        }
        pipe?.CloseCommunication();
        return new OperateResult(StringResources.Language.UserCancelOperate);
      }
      configuredTaskAwaitable = pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, buffer.SelectBegin<byte>(read.Content), aesCryptography).Content).ConfigureAwait(false);
      OperateResult write = await configuredTaskAwaitable;
      if (!write.IsSuccess)
      {
        pipe?.CloseCommunication();
        return write;
      }
      OperateResult<byte, byte[]> receive = await MqttHelper.ReceiveMqttMessageAsync(pipe, timeOut).ConfigureAwait(false);
      if (!receive.IsSuccess)
        return (OperateResult) receive;
      if (receive.Content1 == (byte) 0)
      {
        pipe?.CloseCommunication();
        return new OperateResult(Encoding.UTF8.GetString(receive.Content2));
      }
      Action<long, long> action = reportProgress;
      if (action != null)
        action(already, fileSize);
      read = (OperateResult<int>) null;
      write = (OperateResult) null;
      receive = (OperateResult<byte, byte[]>) null;
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 使用MQTT协议将一个文件发送到网络上去，需要指定文件名，保存的文件名，可选指定文件描述信息，进度报告<br />
  /// To send a file to the network using the MQTT protocol, you need to specify the file name, the saved file name,
  /// optionally specify the file description information, and the progress report
  /// </summary>
  /// <param name="pipe">当前的管道对象信息</param>
  /// <param name="filename">文件名称</param>
  /// <param name="servername">对方接收后保存的文件名</param>
  /// <param name="filetag">文件的描述信息</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">用户取消的令牌</param>
  /// <returns>是否操作成功</returns>
  public static OperateResult SendMqttFile(
    CommunicationPipe pipe,
    string filename,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    FileInfo fileInfo = new FileInfo(filename);
    if (!System.IO.File.Exists(filename))
    {
      OperateResult operateResult = pipe.Send(MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, Encoding.UTF8.GetBytes(StringResources.Language.FileNotExist)).Content);
      if (!operateResult.IsSuccess)
        return operateResult;
      pipe?.CloseCommunication();
      return new OperateResult(StringResources.Language.FileNotExist);
    }
    string[] data = new string[3]
    {
      servername,
      fileInfo.Length.ToString(),
      filetag
    };
    OperateResult operateResult1 = pipe.Send(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(data)).Content);
    if (!operateResult1.IsSuccess)
      return operateResult1;
    OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(pipe, 60000);
    if (!mqttMessage.IsSuccess)
      return (OperateResult) mqttMessage;
    if (mqttMessage.Content1 == (byte) 0)
    {
      pipe?.CloseCommunication();
      return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
    }
    try
    {
      OperateResult operateResult2 = new OperateResult();
      using (FileStream fileStream = new FileStream(filename, FileMode.Open, FileAccess.Read))
        operateResult2 = MqttHelper.SendMqttStream(pipe, (Stream) fileStream, fileInfo.Length, 60000, reportProgress, aesCryptography, cancelToken);
      return operateResult2;
    }
    catch (Exception ex)
    {
      pipe?.CloseCommunication();
      return new OperateResult("SendMqttStream Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.SendMqttFile(HslCommunication.Core.Pipe.CommunicationPipe,System.String,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  public static async Task<OperateResult> SendMqttFileAsync(
    CommunicationPipe pipe,
    string filename,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    FileInfo info = new FileInfo(filename);
    if (!System.IO.File.Exists(filename))
    {
      OperateResult notFoundResult = await pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 0, (byte[]) null, Encoding.UTF8.GetBytes(StringResources.Language.FileNotExist)).Content).ConfigureAwait(false);
      if (!notFoundResult.IsSuccess)
        return notFoundResult;
      pipe?.CloseCommunication();
      return new OperateResult(StringResources.Language.FileNotExist);
    }
    string[] array = new string[3]
    {
      servername,
      info.Length.ToString(),
      filetag
    };
    OperateResult sendResult = await pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(array)).Content).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return sendResult;
    OperateResult<byte, byte[]> check = await MqttHelper.ReceiveMqttMessageAsync(pipe, 60000).ConfigureAwait(false);
    if (!check.IsSuccess)
      return (OperateResult) check;
    if (check.Content1 == (byte) 0)
    {
      pipe?.CloseCommunication();
      return new OperateResult(Encoding.UTF8.GetString(check.Content2));
    }
    try
    {
      OperateResult result = new OperateResult();
      using (FileStream fs = new FileStream(filename, FileMode.Open, FileAccess.Read))
        result = await MqttHelper.SendMqttStreamAsync(pipe, (Stream) fs, info.Length, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
      return result;
    }
    catch (Exception ex)
    {
      pipe?.CloseCommunication();
      return new OperateResult("SendMqttStream Exception -> " + ex.Message);
    }
  }

  /// <summary>
  /// 使用MQTT协议将一个数据流发送到网络上去，需要保存的文件名，可选指定文件描述信息，进度报告<br />
  /// Use the MQTT protocol to send a data stream to the network, the file name that needs to be saved, optional file description information, progress report
  /// </summary>
  /// <param name="pipe">当前的管道对象信息</param>
  /// <param name="stream">数据流</param>
  /// <param name="servername">对方接收后保存的文件名</param>
  /// <param name="filetag">文件的描述信息</param>
  /// <param name="reportProgress">进度报告，第一个参数是已完成的字节数量，第二个参数是总字节数量。</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">用户取消的令牌信息</param>
  /// <returns>是否操作成功</returns>
  public static OperateResult SendMqttFile(
    CommunicationPipe pipe,
    Stream stream,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    string[] data = new string[3]
    {
      servername,
      stream.Length.ToString(),
      filetag
    };
    OperateResult operateResult = pipe.Send(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(data)).Content);
    if (!operateResult.IsSuccess)
      return operateResult;
    OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(pipe, 60000);
    if (!mqttMessage.IsSuccess)
      return (OperateResult) mqttMessage;
    if (mqttMessage.Content1 == (byte) 0)
    {
      pipe?.CloseCommunication();
      return new OperateResult(Encoding.UTF8.GetString(mqttMessage.Content2));
    }
    try
    {
      return MqttHelper.SendMqttStream(pipe, stream, stream.Length, 60000, reportProgress, aesCryptography, cancelToken);
    }
    catch (Exception ex)
    {
      pipe?.CloseCommunication();
      return new OperateResult("SendMqttStream Exception -> " + ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.SendMqttFile(HslCommunication.Core.Pipe.CommunicationPipe,System.IO.Stream,System.String,System.String,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  public static async Task<OperateResult> SendMqttFileAsync(
    CommunicationPipe pipe,
    Stream stream,
    string servername,
    string filetag,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    string[] array = new string[3]
    {
      servername,
      stream.Length.ToString(),
      filetag
    };
    OperateResult sendResult = await pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, HslProtocol.PackStringArrayToByte(array)).Content).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return sendResult;
    OperateResult<byte, byte[]> check = await MqttHelper.ReceiveMqttMessageAsync(pipe, 60000).ConfigureAwait(false);
    if (!check.IsSuccess)
      return (OperateResult) check;
    if (check.Content1 == (byte) 0)
    {
      pipe?.CloseCommunication();
      return new OperateResult(Encoding.UTF8.GetString(check.Content2));
    }
    try
    {
      OperateResult operateResult = await MqttHelper.SendMqttStreamAsync(pipe, stream, stream.Length, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
      return operateResult;
    }
    catch (Exception ex)
    {
      pipe?.CloseCommunication();
      return new OperateResult("SendMqttStream Exception -> " + ex.Message);
    }
  }

  /// <summary>
  /// 使用MQTT协议从网络接收字节数组，然后写入文件或流中，支持进度报告<br />
  /// Use MQTT protocol to receive byte array from the network, and then write it to file or stream, support progress report
  /// </summary>
  /// <param name="pipe">当前的管道对象信息</param>
  /// <param name="source">文件名或是流</param>
  /// <param name="reportProgress">进度报告</param>
  /// <param name="aesCryptography">AES数据加密对象，如果为空，则不进行加密</param>
  /// <param name="cancelToken">用户取消的令牌信息</param>
  /// <returns>是否操作成功，如果成功，携带文件基本信息</returns>
  public static OperateResult<FileBaseInfo> ReceiveMqttFile(
    CommunicationPipe pipe,
    object source,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    OperateResult<byte, byte[]> mqttMessage = MqttHelper.ReceiveMqttMessage(pipe, 60000);
    if (!mqttMessage.IsSuccess)
      return OperateResult.CreateFailedResult<FileBaseInfo>((OperateResult) mqttMessage);
    if (mqttMessage.Content1 == (byte) 0)
    {
      pipe?.CloseCommunication();
      return new OperateResult<FileBaseInfo>(Encoding.UTF8.GetString(mqttMessage.Content2));
    }
    FileBaseInfo fileBaseInfo = new FileBaseInfo();
    string[] strArray = HslProtocol.UnPackStringArrayFromByte(mqttMessage.Content2);
    fileBaseInfo.Name = strArray[0];
    fileBaseInfo.Size = long.Parse(strArray[1]);
    fileBaseInfo.Tag = strArray[2];
    pipe.Send(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, (byte[]) null).Content);
    try
    {
      OperateResult result = (OperateResult) null;
      switch (source)
      {
        case string path:
          using (FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write))
            result = MqttHelper.ReceiveMqttStream(pipe, (Stream) fileStream, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken);
          if (!result.IsSuccess)
          {
            if (System.IO.File.Exists(path))
              System.IO.File.Delete(path);
            return OperateResult.CreateFailedResult<FileBaseInfo>(result);
          }
          break;
        case Stream stream:
          MqttHelper.ReceiveMqttStream(pipe, stream, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken);
          break;
        default:
          throw new Exception("Not Supported Type");
      }
      return OperateResult.CreateSuccessResult<FileBaseInfo>(fileBaseInfo);
    }
    catch (Exception ex)
    {
      pipe?.CloseCommunication();
      return new OperateResult<FileBaseInfo>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.MQTT.MqttHelper.ReceiveMqttFile(HslCommunication.Core.Pipe.CommunicationPipe,System.Object,System.Action{System.Int64,System.Int64},HslCommunication.Core.Security.AesCryptography,HslCommunication.Core.HslCancelToken)" />
  public static async Task<OperateResult<FileBaseInfo>> ReceiveMqttFileAsync(
    CommunicationPipe pipe,
    object source,
    Action<long, long> reportProgress = null,
    AesCryptography aesCryptography = null,
    HslCancelToken cancelToken = null)
  {
    OperateResult<byte, byte[]> receiveFileInfo = await MqttHelper.ReceiveMqttMessageAsync(pipe, 60000).ConfigureAwait(false);
    if (!receiveFileInfo.IsSuccess)
      return OperateResult.CreateFailedResult<FileBaseInfo>((OperateResult) receiveFileInfo);
    if (receiveFileInfo.Content1 == (byte) 0)
    {
      pipe?.CloseCommunication();
      return new OperateResult<FileBaseInfo>(Encoding.UTF8.GetString(receiveFileInfo.Content2));
    }
    FileBaseInfo fileBaseInfo = new FileBaseInfo();
    string[] array = HslProtocol.UnPackStringArrayFromByte(receiveFileInfo.Content2);
    fileBaseInfo.Name = array[0];
    fileBaseInfo.Size = long.Parse(array[1]);
    fileBaseInfo.Tag = array[2];
    ConfiguredTaskAwaitable<OperateResult> configuredTaskAwaitable = pipe.SendAsync(MqttHelper.BuildMqttCommand((byte) 100, (byte[]) null, (byte[]) null).Content).ConfigureAwait(false);
    OperateResult operateResult = await configuredTaskAwaitable;
    try
    {
      OperateResult write = (OperateResult) null;
      switch (source)
      {
        case string savename:
          using (FileStream fs = new FileStream(savename, FileMode.Create, FileAccess.Write))
          {
            configuredTaskAwaitable = MqttHelper.ReceiveMqttStreamAsync(pipe, (Stream) fs, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
            write = await configuredTaskAwaitable;
          }
          if (!write.IsSuccess)
          {
            if (System.IO.File.Exists(savename))
              System.IO.File.Delete(savename);
            return OperateResult.CreateFailedResult<FileBaseInfo>(write);
          }
          break;
        case Stream stream:
          configuredTaskAwaitable = MqttHelper.ReceiveMqttStreamAsync(pipe, stream, fileBaseInfo.Size, 60000, reportProgress, aesCryptography, cancelToken).ConfigureAwait(false);
          write = await configuredTaskAwaitable;
          stream = (Stream) null;
          break;
        default:
          throw new Exception("Not Supported Type");
      }
      return OperateResult.CreateSuccessResult<FileBaseInfo>(fileBaseInfo);
    }
    catch (Exception ex)
    {
      pipe?.CloseCommunication();
      return new OperateResult<FileBaseInfo>(ex.Message);
    }
  }
}
