﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.S7PlusHelper.S7Tag
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System.Collections.Generic;
using System.IO;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Siemens.S7PlusHelper;

/// <summary>节点类对象</summary>
public class S7Tag : IS7Object
{
  /// <summary>数据名称</summary>
  public string Name { get; set; }

  /// <summary>数据ID信息，第一个为DB块的信息，第二个是数据信息，如果是结构体，则继续往后面拓展</summary>
  public List<uint> LID { get; set; }

  /// <summary>类型代号</summary>
  public byte TypeCode { get; set; }

  /// <summary>数组长度，如果小于0则表示标量的数据</summary>
  public int ArrayLength { get; set; } = -1;

  /// <summary>当类型为结构体的时候，关联的其他结构体的ID信息</summary>
  public uint StructID { get; set; }

  /// <summary>当类型为结构体的时候，标签的偏移信息</summary>
  public int StructOffset { get; set; }

  /// <summary>获取类型的文本描述</summary>
  /// <returns>string info</returns>
  public string GetTypeText()
  {
    string str;
    switch (this.TypeCode)
    {
      case 0:
        str = "Void";
        break;
      case 1:
        str = "Bool";
        break;
      case 2:
        str = "Byte";
        break;
      case 3:
        str = "Char";
        break;
      case 4:
        str = "Word";
        break;
      case 5:
        str = "Int";
        break;
      case 6:
        str = "DWord";
        break;
      case 7:
        str = "DInt";
        break;
      case 8:
        str = "Real";
        break;
      case 9:
        str = "Date";
        break;
      case 10:
        str = "TimeOfDay";
        break;
      case 11:
        str = "Time";
        break;
      case 12:
        str = "S5Time";
        break;
      case 13:
        str = "S5Count";
        break;
      case 14:
        str = "DateAndTime";
        break;
      case 15:
        str = "InteretTime";
        break;
      case 16 /*0x10*/:
        str = "Array";
        break;
      case 17:
        str = "Struct";
        break;
      case 18:
        str = "EndStruct";
        break;
      case 19:
        str = "String";
        break;
      case 28:
        str = "Counter";
        break;
      case 29:
        str = "Timer";
        break;
      case 48 /*0x30*/:
        str = "LReal";
        break;
      case 49:
        str = "ULInt";
        break;
      case 50:
        str = "LInt";
        break;
      case 51:
        str = "LWord";
        break;
      case 52:
        str = "USInt";
        break;
      case 53:
        str = "UInt";
        break;
      case 54:
        str = "UDInt";
        break;
      case 55:
        str = "SInt";
        break;
      case 56:
        str = "Bcd8";
        break;
      case 61:
        str = "WChar";
        break;
      case 62:
        str = "WString";
        break;
      default:
        str = $"Unknown({this.TypeCode})";
        break;
    }
    return this.ArrayLength >= 0 ? $"{str}[{this.ArrayLength}]" : str;
  }

  /// <summary>获取LID的文本信息</summary>
  /// <returns></returns>
  public string GetLIDText()
  {
    StringBuilder stringBuilder = new StringBuilder();
    if (this.LID != null)
    {
      for (int index = 0; index < this.LID.Count; ++index)
      {
        stringBuilder.Append(this.LID[index].ToString("X"));
        if (index != this.LID.Count - 1)
          stringBuilder.Append(".");
      }
    }
    return stringBuilder.ToString();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.S7PlusHelper.IS7Object.WriteMessgae(System.IO.MemoryStream)" />
  public void WriteMessgae(MemoryStream ms)
  {
    S7Object.WriteUint32(ms, 0U);
    S7Object.WriteUint32(ms, this.LID[0]);
    S7Object.WriteUint32(ms, (uint) this.LID.Count);
    if (this.LID[0] >= 2316173312U /*0x8A0E0000*/)
      S7Object.WriteUint32(ms, 2550U);
    else
      S7Object.WriteUint32(ms, 3736U);
    for (int index = 1; index < this.LID.Count; ++index)
      S7Object.WriteUint32(ms, this.LID[index]);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.S7PlusHelper.IS7Object.GetNumberOfFields" />
  public int GetNumberOfFields() => 3 + this.LID.Count;

  /// <summary>深度克隆一个对象</summary>
  /// <returns></returns>
  public S7Tag Clone()
  {
    S7Tag s7Tag = new S7Tag();
    s7Tag.Name = this.Name;
    if (this.LID != null)
      s7Tag.LID = new List<uint>((IEnumerable<uint>) this.LID.ToArray());
    s7Tag.TypeCode = this.TypeCode;
    s7Tag.ArrayLength = this.ArrayLength;
    s7Tag.StructID = this.StructID;
    s7Tag.StructOffset = this.StructOffset;
    return s7Tag;
  }
}
