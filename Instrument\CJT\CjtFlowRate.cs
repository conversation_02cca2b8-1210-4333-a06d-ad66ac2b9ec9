﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Instrument.CJT.CjtFlowRate
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Instrument.CJT;

/// <summary>CJT协议的流量数据，主要是用来获取水表流量及燃气流量的</summary>
public class CjtFlowRate
{
  /// <summary>当前累积流量</summary>
  public double CurrentFlowRate { get; set; }

  /// <summary>当前累计流量的单位</summary>
  public string CurrentUnit { get; set; }

  /// <summary>结算日累积流量</summary>
  public double SettlementDateFlowRate { get; set; }

  /// <summary>结算日的流量单位</summary>
  public string SettlementDateUnit { get; set; }

  /// <summary>实时时间</summary>
  public DateTime DateTime { get; set; }
}
