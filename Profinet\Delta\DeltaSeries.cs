﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Delta.DeltaSeries
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Delta;

/// <summary>台达的系列信息</summary>
public enum DeltaSeries
{
  /// <summary>台达的Dvp系列，适用DVP-ES/EX/EC/SS型号，DVP-SA/SC/SX/EH型号</summary>
  Dvp,
  /// <summary>适用于AS300系列</summary>
  AS,
}
