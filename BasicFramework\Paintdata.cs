﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.BasicFramework.Paintdata
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.BasicFramework;

/// <summary>包含整型和字符串描述的数据类型</summary>
public struct Paintdata
{
  /// <summary>数量</summary>
  public int Count { get; set; }

  /// <summary>描述</summary>
  public string Description { get; set; }
}
