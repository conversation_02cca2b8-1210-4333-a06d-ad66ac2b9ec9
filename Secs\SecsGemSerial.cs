﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.SecsGemSerial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Device;

#nullable disable
namespace HslCommunication.Secs;

/// <summary>串口类相关的Secs</summary>
public class SecsGemSerial : DeviceSerialPort
{
  private SoftIncrementCount incrementCount;

  /// <summary>实例化一个默认的对象</summary>
  public SecsGemSerial()
  {
    this.incrementCount = new SoftIncrementCount((long) uint.MaxValue);
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.WordLength = (ushort) 2;
  }

  /// <summary>执行SECS命令</summary>
  /// <param name="command">命令信息</param>
  /// <returns>是否成功的结果</returns>
  public OperateResult<byte[]> ExecuteCommand(byte[] command)
  {
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(new byte[1]
    {
      (byte) 5
    });
    if (!operateResult1.IsSuccess)
      return operateResult1;
    if (operateResult1.Content[0] != (byte) 4)
      return new OperateResult<byte[]>($"Send Enq to device, but receive [{operateResult1.Content[0]}], need receive [EOT]");
    OperateResult<byte[]> operateResult2;
    do
    {
      operateResult2 = this.ReadFromCoreServer(command);
    }
    while (operateResult2.IsSuccess);
    return operateResult2;
  }
}
