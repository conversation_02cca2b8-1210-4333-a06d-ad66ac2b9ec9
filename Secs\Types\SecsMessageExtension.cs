﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Secs.Types.SecsMessageExtension
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.Secs.Types;

/// <summary>扩展类</summary>
public static class SecsMessageExtension
{
  /// <summary>获取显示的字符串文本信息</summary>
  /// <param name="secsMessages">SECS消息类</param>
  /// <returns>字符串信息</returns>
  public static string ToRenderString(this SecsValue[] secsMessages)
  {
    if (secsMessages == null || secsMessages.Length == 0)
      return string.Empty;
    StringBuilder stringBuilder = new StringBuilder();
    foreach (SecsValue secsMessage in secsMessages)
    {
      stringBuilder.Append(Environment.NewLine);
      stringBuilder.Append(secsMessage.ToString());
    }
    return stringBuilder.ToString();
  }
}
