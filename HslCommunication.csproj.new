<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
    <AssemblyName>HslCommunication</AssemblyName>
    <RootNamespace>HslCommunication</RootNamespace>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyVersion>12.3.3.0</AssemblyVersion>
    <FileVersion>12.3.3.0</FileVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="BasicFramework\FormAuthorize.resx" />
    <EmbeddedResource Include="BasicFramework\FormPopup.resx" />
    <EmbeddedResource Include="LogNet\FormLogNetView.resx" />
    <EmbeddedResource Include="LogNet\LogNetAnalysisControl.resx" />
    <EmbeddedResource Include="Properties\Resources.resx" />
  </ItemGroup>

</Project>
