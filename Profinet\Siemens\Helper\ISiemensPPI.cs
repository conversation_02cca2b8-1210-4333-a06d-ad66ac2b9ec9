﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Siemens.Helper.ISiemensPPI
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;

#nullable disable
namespace HslCommunication.Profinet.Siemens.Helper;

/// <summary>西门子PPI的公用接口信息</summary>
public interface ISiemensPPI : IReadWriteNet
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.Start(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  OperateResult Start(string parameter = "");

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.Stop(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  OperateResult Stop(string parameter = "");

  /// <inheritdoc cref="M:HslCommunication.Profinet.Siemens.Helper.SiemensPPIHelper.ReadPlcType(HslCommunication.Core.IReadWriteDevice,System.String,System.Byte,System.Object)" />
  OperateResult<string> ReadPlcType(string parameter = "");
}
