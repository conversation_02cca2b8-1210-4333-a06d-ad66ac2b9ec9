﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.YASKAWA.YRCType
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Robot.YASKAWA;

/// <summary>YRC机器人的类型</summary>
public enum YRCType
{
  /// <summary>YRC1000 型号，含有六轴机器人</summary>
  YRC1000,
  /// <summary>YRC100 型号，含有七轴机器人</summary>
  YRC100,
}
