﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.ModbusRtuMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.ModBus;
using System.IO;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>ModbusRtu的协议类信息</summary>
public class ModbusRtuMessage : NetMessageBase, INetMessage
{
  /// <summary>指定是否检查站号来实例化一个对象</summary>
  /// <param name="stationCheck">是否检查站号</param>
  public ModbusRtuMessage(bool stationCheck) => this.StationCheckMatch = stationCheck;

  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => -1;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes() => 0;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckReceiveDataComplete(System.Byte[],System.IO.MemoryStream)" />
  public override bool CheckReceiveDataComplete(byte[] send, MemoryStream ms)
  {
    return ModbusInfo.CheckRtuReceiveDataComplete(send, ms.ToArray());
  }

  /// <inheritdoc />
  public override int CheckMessageMatch(byte[] send, byte[] receive)
  {
    return !this.StationCheckMatch ? 1 : ModbusInfo.CheckRtuMessageMatch(send, receive);
  }

  /// <inheritdoc cref="P:HslCommunication.ModBus.ModbusRtu.StationCheckMacth" />
  public bool StationCheckMatch { get; set; } = true;
}
