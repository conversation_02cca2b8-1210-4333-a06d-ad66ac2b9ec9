﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Vigor.VigorSerial
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Profinet.Vigor.Helper;
using HslCommunication.Reflection;
using System.Text.RegularExpressions;

#nullable disable
namespace HslCommunication.Profinet.Vigor;

/// <summary>
/// 丰炜通信协议的串口通信，支持VS系列，地址支持携带站号，例如 s=2;D100, 字地址支持 D,SD,R,T,C(C200-C255是32位寄存器), 位地址支持X,Y,M,SM,S,TS(定时器触点),TC（定时器线圈）,CS(计数器触点),CC（计数器线圈)<br />
/// The network port transparent transmission version of Fengwei communication protocol supports VS series, and the address supports carrying station number,
/// such as s=2;D100, word address supports D, SD, R, T, C (C200-C255 are 32-bit registers), Bit address supports X, Y, M, SM, S, TS (timer contact),
/// TC (timer coil), CS (counter contact), CC (counter coil)
/// </summary>
/// <remarks>
/// 串口默认的参数为 19200-8-N-1方式，暂时不支持对字寄存器(D,R)进行读写位操作，感谢随时关注库的更新日志
/// </remarks>
public class VigorSerial : DeviceSerialPort
{
  /// <summary>实例化一个默认的对象</summary>
  public VigorSerial()
  {
    this.ByteTransform = (IByteTransform) new RegularByteTransform();
    this.WordLength = (ushort) 1;
    this.ReceiveEmptyDataCount = 5;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new VigorSerialMessage();

  /// <inheritdoc />
  protected override ushort GetWordLength(string address, int length, int dataTypeLength)
  {
    if (!Regex.IsMatch(address, "^C2[0-9][0-9]$"))
      return base.GetWordLength(address, length, dataTypeLength);
    int num = length * dataTypeLength * 2 / 4;
    return num == 0 ? (ushort) 1 : (ushort) num;
  }

  /// <summary>获取或设置当前PLC的站号信息</summary>
  public byte Station { get; set; }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.Read(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    return VigorHelper.Read((IReadWriteDevice) this, this.Station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    return VigorHelper.Write((IReadWriteDevice) this, this.Station, address, value);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.ReadBool(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    return VigorHelper.ReadBool((IReadWriteDevice) this, this.Station, address, length);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Vigor.Helper.VigorHelper.Write(HslCommunication.Core.IReadWriteDevice,System.Byte,System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    return VigorHelper.Write((IReadWriteDevice) this, this.Station, address, value);
  }

  /// <inheritdoc />
  public override string ToString() => $"VigorSerial[{this.PortName}:{this.BaudRate}]";
}
