﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.CNC.Fanuc.FanucSeries0i
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.CNC.Fanuc;

/// <summary>一个FANUC的机床通信类对象</summary>
public class FanucSeries0i : NetworkDoubleBase
{
  private Encoding encoding;
  private FanucSysInfo fanucSysInfo;
  private short opPath = 1;

  /// <summary>根据IP及端口来实例化一个对象内容</summary>
  /// <param name="ipAddress">Ip地址信息</param>
  /// <param name="port">端口号</param>
  public FanucSeries0i(string ipAddress, int port = 8193)
  {
    this.IpAddress = ipAddress;
    this.Port = port;
    this.ByteTransform = (IByteTransform) new ReverseBytesTransform();
    this.encoding = Encoding.Default;
    this.ReceiveTimeOut = 30000;
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new CNCFanucSeriesMessage();

  /// <summary>
  /// 获取或设置当前的文本的字符编码信息，如果你不清楚，可以调用<see cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadLanguage" />方法来自动匹配。<br />
  /// Get or set the character encoding information of the current text.
  /// If you are not sure, you can call the <see cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadLanguage" /> method to automatically match.
  /// </summary>
  public Encoding TextEncoding
  {
    get => this.encoding;
    set => this.encoding = value;
  }

  /// <summary>
  /// 获取或设置当前操作的路径信息，默认为1，如果机床支持多路径的，可以设置为其他值。<br />
  /// Gets or sets the path information for the current operation, the default is 1, if the machine supports multipathing, it can be set to other values.
  /// </summary>
  [HslMqttApi(Description = "Gets or sets the path information for the current operation, the default is 1")]
  public short OperatePath
  {
    get => this.opPath;
    set => this.opPath = value;
  }

  /// <inheritdoc />
  protected override OperateResult InitializationOnConnect(Socket socket)
  {
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(socket, "a0 a0 a0 a0 00 01 01 01 00 02 00 02".ToHexBytes());
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(socket, this.BuildReadArray(this.BuildReadSingle((ushort) 24, 0, 0, 0, 0, 0)));
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    try
    {
      this.fanucSysInfo = new FanucSysInfo(operateResult2.Content);
    }
    catch
    {
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override OperateResult ExtraOnDisconnect(Socket socket)
  {
    return (OperateResult) this.ReadFromCoreServer(socket, "a0 a0 a0 a0 00 01 02 01 00 00".ToHexBytes());
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> InitializationOnConnectAsync(Socket socket)
  {
    OperateResult<byte[]> read1 = await this.ReadFromCoreServerAsync(socket, "a0 a0 a0 a0 00 01 01 01 00 02 00 02".ToHexBytes());
    if (!read1.IsSuccess)
      return (OperateResult) read1;
    OperateResult<byte[]> read2 = await this.ReadFromCoreServerAsync(socket, this.BuildReadArray(this.BuildReadSingle((ushort) 24, 0, 0, 0, 0, 0)));
    if (!read2.IsSuccess)
      return (OperateResult) read2;
    try
    {
      this.fanucSysInfo = new FanucSysInfo(read2.Content);
    }
    catch
    {
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override async Task<OperateResult> ExtraOnDisconnectAsync(Socket socket)
  {
    OperateResult<byte[]> operateResult = await this.ReadFromCoreServerAsync(socket, "a0 a0 a0 a0 00 01 02 01 00 00".ToHexBytes());
    return (OperateResult) operateResult;
  }

  private double GetFanucDouble(byte[] content, int index)
  {
    return this.GetFanucDouble(content, index, 1)[0];
  }

  private double[] GetFanucDouble(byte[] content, int index, int length)
  {
    double[] fanucDouble = new double[length];
    for (int index1 = 0; index1 < length; ++index1)
    {
      int num1 = this.ByteTransform.TransInt32(content, index + 8 * index1);
      int num2 = (int) this.ByteTransform.TransInt16(content, index + 8 * index1 + 6);
      fanucDouble[index1] = num1 != 0 ? Math.Round((double) num1 * Math.Pow(0.1, (double) num2), num2) : 0.0;
    }
    return fanucDouble;
  }

  private byte[] CreateFromFanucDouble(double value)
  {
    byte[] fromFanucDouble = new byte[8];
    this.ByteTransform.TransByte((int) (value * 1000.0)).CopyTo((Array) fromFanucDouble, 0);
    fromFanucDouble[5] = (byte) 10;
    fromFanucDouble[7] = (byte) 3;
    return fromFanucDouble;
  }

  private void ChangeTextEncoding(ushort code)
  {
    switch (code)
    {
      case 0:
        this.encoding = Encoding.Default;
        break;
      case 1:
      case 4:
        this.encoding = Encoding.GetEncoding("shift_jis", EncoderFallback.ReplacementFallback, (DecoderFallback) new DecoderReplacementFallback());
        break;
      case 6:
        this.encoding = Encoding.GetEncoding("ks_c_5601-1987");
        break;
      case 15:
        this.encoding = Encoding.Default;
        break;
      case 16 /*0x10*/:
        this.encoding = Encoding.GetEncoding("windows-1251");
        break;
      case 17:
        this.encoding = Encoding.GetEncoding("windows-1254");
        break;
    }
  }

  /// <summary>
  /// 获取fanuc机床设备的基本信息，型号，轴数量等等。<br />
  /// Get basic information about fanuc machines, models, number of axes and much more
  /// </summary>
  /// <returns>机床信息</returns>
  [HslMqttApi(Description = "Get basic information about fanuc machines, models, number of axes and much more")]
  public OperateResult<FanucSysInfo> ReadSysInfo()
  {
    return this.fanucSysInfo == null ? new OperateResult<FanucSysInfo>("Must connect device first!") : OperateResult.CreateSuccessResult<FanucSysInfo>(this.fanucSysInfo);
  }

  /// <summary>
  /// 主轴转速及进给倍率<br />
  /// Spindle speed and feedrate override
  /// </summary>
  /// <returns>主轴转速及进给倍率</returns>
  [HslMqttApi(Description = "Spindle speed and feedrate override")]
  public OperateResult<double, double> ReadSpindleSpeedAndFeedRate()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 164, 3, 0, 0, 0, 0), this.BuildReadSingle((ushort) 138, 1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 3, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 4, 0, 0, 0, 0), this.BuildReadSingle((ushort) 36, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 37, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 164, 3, 0, 0, 0, 0)));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<double, double>((OperateResult) result);
    List<byte[]> numArrayList = this.ExtraContentArray(result.Content.RemoveBegin<byte>(10));
    return OperateResult.CreateSuccessResult<double, double>(this.GetFanucDouble(numArrayList[5], 14), this.GetFanucDouble(numArrayList[4], 14));
  }

  /// <summary>
  /// 读取进给倍率<br />
  /// Read feedrate override
  /// </summary>
  /// <returns>进给倍率</returns>
  [HslMqttApi(Description = "Read feedrate override")]
  public OperateResult<int> ReadFeedRate()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadMulti((ushort) 2, (ushort) 32769, 12, 13, 0, 1, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<int>(result2) : OperateResult.CreateSuccessResult<int>(100 - ((int) this.ByteTransform.TransUInt16(extraContent, 14) - 155));
  }

  /// <summary>
  /// 读取主轴倍率<br />
  /// Read spindle override
  /// </summary>
  /// <returns>主轴倍率</returns>
  [HslMqttApi(Description = "Read spindle override")]
  public OperateResult<int> ReadSpindleRate()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadMulti((ushort) 2, (ushort) 32769, 30, 31 /*0x1F*/, 0, 1, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<int>(result2) : OperateResult.CreateSuccessResult<int>((int) this.ByteTransform.TransUInt16(extraContent, 14));
  }

  /// <summary>
  /// 读取程序名及程序号<br />
  /// Read program name and program number
  /// </summary>
  /// <returns>程序名及程序号</returns>
  [HslMqttApi(Description = "Read program name and program number")]
  public OperateResult<string, int> ReadSystemProgramCurrent()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 207, 0, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string, int>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string, int>(result2);
    int num = this.ByteTransform.TransInt32(extraContent, 14);
    return OperateResult.CreateSuccessResult<string, int>(extraContent.GetStringOrEndChar(18, 36, this.encoding), num);
  }

  /// <summary>
  /// 读取程序号信息<br />
  /// Read program number
  /// </summary>
  /// <returns>程序号信息</returns>
  [HslMqttApi(Description = "Read program number")]
  public OperateResult<int> ReadProgramNumber()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 28, 0, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<int>(result2) : OperateResult.CreateSuccessResult<int>(this.ByteTransform.TransInt32(extraContent, 14));
  }

  /// <summary>
  /// 读取机床的语言设定信息，具体值的含义参照API文档说明<br />
  /// Read the language setting information of the machine tool, refer to the API documentation for the meaning of the specific values
  /// </summary>
  /// <remarks>此处举几个常用值 0: 英语 1: 日语 2: 德语 3: 法语 4: 中文繁体 6: 韩语 15: 中文简体 16: 俄语 17: 土耳其语</remarks>
  /// <returns>返回的语言代号</returns>
  [HslMqttApi(Description = "Read the language setting information of the machine tool")]
  public OperateResult<ushort> ReadLanguage()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 141, 3281, 3281, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<ushort>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<ushort>(result2);
    ushort code = this.ByteTransform.TransUInt16(extraContent, 24);
    this.ChangeTextEncoding(code);
    return OperateResult.CreateSuccessResult<ushort>(code);
  }

  /// <summary>
  /// 读取宏变量，可以用来读取刀具号<br />
  /// Read macro variable, can be used to read tool number
  /// </summary>
  /// <param name="number">刀具号</param>
  /// <returns>读宏变量信息</returns>
  [HslMqttApi(Description = "Read macro variable, can be used to read tool number")]
  public OperateResult<double> ReadSystemMacroValue(int number)
  {
    return ByteTransformHelper.GetResultFromArray<double>(this.ReadSystemMacroValue(number, 1));
  }

  /// <summary>
  /// 读取宏变量，可以用来读取刀具号<br />
  /// Read macro variable, can be used to read tool number
  /// </summary>
  /// <param name="number">宏变量地址</param>
  /// <param name="length">读取的长度信息</param>
  /// <returns>是否成功</returns>
  [HslMqttApi(ApiTopic = "ReadSystemMacroValueArray", Description = "Read macro variable, can be used to read tool number")]
  public OperateResult<double[]> ReadSystemMacroValue(int number, int length)
  {
    int[] array = SoftBasic.SplitIntegerToArray(length, 5);
    int a = number;
    List<byte> byteList = new List<byte>();
    for (int index = 0; index < array.Length; ++index)
    {
      OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 21, a, a + array[index] - 1, 0, 0, 0)));
      if (!result.IsSuccess)
        return OperateResult.CreateFailedResult<double[]>((OperateResult) result);
      byteList.AddRange((IEnumerable<byte>) this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0].RemoveBegin<byte>(14));
      a += array[index];
    }
    try
    {
      return OperateResult.CreateSuccessResult<double[]>(this.GetFanucDouble(byteList.ToArray(), 0, length));
    }
    catch (Exception ex)
    {
      return new OperateResult<double[]>($"{ex.Message} Source:{byteList.ToArray().ToHexString(' ')}");
    }
  }

  /// <summary>
  /// 写宏变量，需要指定地址及写入的数据<br />
  /// Write macro variable, need to specify the address and write data
  /// </summary>
  /// <param name="number">地址</param>
  /// <param name="values">数据值</param>
  /// <returns>是否成功</returns>
  [HslMqttApi(Description = "Write macro variable, need to specify the address and write data")]
  public OperateResult WriteSystemMacroValue(int number, double[] values)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 22, number, number + values.Length - 1, 0, 0, values)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string, int>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <summary>
  /// 根据刀具号写入长度形状补偿，刀具号为1-24<br />
  /// Write length shape compensation according to the tool number, the tool number is 1-24
  /// </summary>
  /// <param name="cutter">刀具号，范围为1-24</param>
  /// <param name="offset">补偿值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "Write length shape compensation according to the tool number, the tool number is 1-24")]
  public OperateResult WriteCutterLengthShapeOffset(int cutter, double offset)
  {
    return this.WriteSystemMacroValue(11000 + cutter, new double[1]
    {
      offset
    });
  }

  /// <summary>
  /// 根据刀具号写入长度磨损补偿，刀具号为1-24<br />
  /// Write length wear compensation according to the tool number, the tool number is 1-24
  /// </summary>
  /// <param name="cutter">刀具号，范围为1-24</param>
  /// <param name="offset">补偿值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "Write length wear compensation according to the tool number, the tool number is 1-24")]
  public OperateResult WriteCutterLengthWearOffset(int cutter, double offset)
  {
    return this.WriteSystemMacroValue(10000 + cutter, new double[1]
    {
      offset
    });
  }

  /// <summary>
  /// 根据刀具号写入半径形状补偿，刀具号为1-24<br />
  /// Write radius shape compensation according to the tool number, the tool number is 1-24
  /// </summary>
  /// <param name="cutter">刀具号，范围为1-24</param>
  /// <param name="offset">补偿值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "Write radius shape compensation according to the tool number, the tool number is 1-24")]
  public OperateResult WriteCutterRadiusShapeOffset(int cutter, double offset)
  {
    return this.WriteSystemMacroValue(13000 + cutter, new double[1]
    {
      offset
    });
  }

  /// <summary>
  /// 根据刀具号写入半径磨损补偿，刀具号为1-24<br />
  /// Write radius wear compensation according to the tool number, the tool number is 1-24
  /// </summary>
  /// <param name="cutter">刀具号，范围为1-24</param>
  /// <param name="offset">补偿值</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "Write radius wear compensation according to the tool number, the tool number is 1-24")]
  public OperateResult WriteCutterRadiusWearOffset(int cutter, double offset)
  {
    return this.WriteSystemMacroValue(12000 + cutter, new double[1]
    {
      offset
    });
  }

  /// <summary>
  /// 读取伺服负载<br />
  /// Read servo load
  /// </summary>
  /// <returns>轴负载</returns>
  [HslMqttApi(Description = "Read servo load")]
  public OperateResult<double[]> ReadFanucAxisLoad()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 164, 2, 0, 0, 0, 0), this.BuildReadSingle((ushort) 137, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 86, 1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 164, 2, 0, 0, 0, 0)));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<double[]>((OperateResult) result);
    List<byte[]> numArrayList = this.ExtraContentArray(result.Content.RemoveBegin<byte>(10));
    int length = -1;
    if (numArrayList[0].Length >= 16 /*0x10*/)
      length = (int) this.ByteTransform.TransUInt16(numArrayList[0], 14);
    if (length < 0 || length * 8 + 14 > numArrayList[2].Length)
      length = (numArrayList[2].Length - 14) / 8;
    return OperateResult.CreateSuccessResult<double[]>(this.GetFanucDouble(numArrayList[2], 14, length));
  }

  /// <summary>
  /// 读取主轴负载<br />
  /// Read spindle load
  /// </summary>
  /// <returns>主轴负载</returns>
  [HslMqttApi(Description = "Read spindle load")]
  public OperateResult<double> ReadSpindleLoad()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 64 /*0x40*/, 4, -1, 0, 0, 0), this.BuildReadSingle((ushort) 64 /*0x40*/, 5, -1, 0, 0, 0), this.BuildReadSingle((ushort) 138, 0, 0, 0, 0, 0)));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) result);
    List<byte[]> numArrayList = this.ExtraContentArray(result.Content.RemoveBegin<byte>(10));
    return numArrayList[0].Length >= 18 ? OperateResult.CreateSuccessResult<double>(this.GetFanucDouble(numArrayList[0], 14)) : new OperateResult<double>("Read failed, data is too short: " + numArrayList[0].ToHexString(' '));
  }

  /// <summary>
  /// 读取机床的坐标，包括机械坐标，绝对坐标，相对坐标<br />
  /// Read the coordinates of the machine tool, including mechanical coordinates, absolute coordinates, and relative coordinates
  /// </summary>
  /// <returns>数控机床的坐标信息，包括机械坐标，绝对坐标，相对坐标</returns>
  [HslMqttApi(Description = "Read the coordinates of the machine tool, including mechanical coordinates, absolute coordinates, and relative coordinates")]
  public OperateResult<SysAllCoors> ReadSysAllCoors()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 164, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 137, -1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 2, 0, 0, 0, 0), this.BuildReadSingle((ushort) 163, 0, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 0, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 1, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 2, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 3, -1, 0, 0, 0), this.BuildReadSingle((ushort) 164, 0, 0, 0, 0, 0)));
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<SysAllCoors>((OperateResult) result);
    List<byte[]> numArrayList = this.ExtraContentArray(result.Content.RemoveBegin<byte>(10));
    int length = (int) this.ByteTransform.TransUInt16(numArrayList[0], 14);
    return OperateResult.CreateSuccessResult<SysAllCoors>(new SysAllCoors()
    {
      Absolute = this.GetFanucDouble(numArrayList[5], 14, length),
      Machine = this.GetFanucDouble(numArrayList[6], 14, length),
      Relative = this.GetFanucDouble(numArrayList[7], 14, length)
    });
  }

  /// <summary>
  /// 读取报警信息<br />
  /// Read alarm information
  /// </summary>
  /// <returns>机床的当前的所有的报警信息</returns>
  [HslMqttApi(Description = "Read alarm information")]
  public OperateResult<SysAlarm[]> ReadSystemAlarm()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 35, -1, 10, 2, 64 /*0x40*/, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<SysAlarm[]>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<SysAlarm[]>(result2);
    if (this.ByteTransform.TransUInt16(extraContent, 12) <= (ushort) 0)
      return OperateResult.CreateSuccessResult<SysAlarm[]>(new SysAlarm[0]);
    SysAlarm[] sysAlarmArray = new SysAlarm[(int) this.ByteTransform.TransUInt16(extraContent, 12) / 80 /*0x50*/];
    for (int index = 0; index < sysAlarmArray.Length; ++index)
    {
      sysAlarmArray[index] = new SysAlarm();
      sysAlarmArray[index].AlarmId = this.ByteTransform.TransInt32(extraContent, 14 + 80 /*0x50*/ * index);
      sysAlarmArray[index].Type = this.ByteTransform.TransInt16(extraContent, 20 + 80 /*0x50*/ * index);
      sysAlarmArray[index].Axis = this.ByteTransform.TransInt16(extraContent, 24 + 80 /*0x50*/ * index);
      ushort count = this.ByteTransform.TransUInt16(extraContent, 28 + 80 /*0x50*/ * index);
      sysAlarmArray[index].Message = this.encoding.GetString(extraContent, 30 + 80 /*0x50*/ * index, (int) count);
    }
    return OperateResult.CreateSuccessResult<SysAlarm[]>(sysAlarmArray);
  }

  /// <summary>
  /// 读取fanuc机床的时间，0是开机时间，1是运行时间，2是切割时间，3是循环时间，4是空闲时间，返回秒为单位的信息<br />
  /// Read the time of the fanuc machine tool, 0 is the boot time, 1 is the running time, 2 is the cutting time,
  /// 3 is the cycle time, 4 is the idle time, and returns the information in seconds.
  /// </summary>
  /// <param name="timeType">读取的时间类型</param>
  /// <returns>秒为单位的结果</returns>
  [HslMqttApi(Description = "Read the time of the fanuc machine tool, 0 is the boot time, 1 is the running time, 2 is the cutting time, 3 is the cycle time, 4 is the idle time, and returns the information in seconds.")]
  public OperateResult<long> ReadTimeData(int timeType)
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 288, timeType, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<long>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<long>(result2);
    int num1 = this.ByteTransform.TransInt32(extraContent, 18);
    long num2 = (long) this.ByteTransform.TransInt32(extraContent, 14);
    if (num1 < 0 || num1 > 60000 || num2 < 0L)
    {
      num1 = BitConverter.ToInt32(extraContent, 18);
      num2 = (long) BitConverter.ToInt32(extraContent, 14);
    }
    long num3 = (long) (num1 / 1000);
    return OperateResult.CreateSuccessResult<long>(num2 * 60L + num3);
  }

  /// <summary>
  /// 读取报警状态信息<br />
  /// Read alarm status information
  /// </summary>
  /// <returns>报警状态数据</returns>
  [HslMqttApi(Description = "Read alarm status information")]
  public OperateResult<int> ReadAlarmStatus()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 26, 0, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<int>(result2) : OperateResult.CreateSuccessResult<int>((int) this.ByteTransform.TransUInt16(extraContent, 16 /*0x10*/));
  }

  private OperateResult<SysStatusInfo> CreateSysStatusInfo(List<byte[]> result)
  {
    try
    {
      return OperateResult.CreateSuccessResult<SysStatusInfo>(new SysStatusInfo()
      {
        Dummy = result[1].Length >= 16 /*0x10*/ ? this.ByteTransform.TransInt16(result[1], 14) : (short) 0,
        TMMode = result[2].Length >= 16 /*0x10*/ ? this.ByteTransform.TransInt16(result[2], 14) : (short) 0,
        WorkMode = (CNCWorkMode) this.ByteTransform.TransInt16(result[0], 14),
        RunStatus = (CNCRunStatus) this.ByteTransform.TransInt16(result[0], 16 /*0x10*/),
        Motion = this.ByteTransform.TransInt16(result[0], 18),
        MSTB = this.ByteTransform.TransInt16(result[0], 20),
        Emergency = this.ByteTransform.TransInt16(result[0], 22),
        Alarm = this.ByteTransform.TransInt16(result[0], 24),
        Edit = this.ByteTransform.TransInt16(result[0], 26)
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<SysStatusInfo>("CreateSysStatusInfo failed: " + ex.Message);
    }
  }

  /// <summary>
  /// 读取系统的基本信息状态，工作模式，运行状态，是否急停等等操作<br />
  /// Read the basic information status of the system, working mode, running status, emergency stop, etc.
  /// </summary>
  /// <returns>结果信息数据</returns>
  [HslMqttApi(Description = "Read the basic information status of the system, working mode, running status, emergency stop, etc.")]
  public OperateResult<SysStatusInfo> ReadSysStatusInfo()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 25, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 225, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 152, 0, 0, 0, 0, 0)));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<SysStatusInfo>((OperateResult) result) : this.CreateSysStatusInfo(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10)));
  }

  private OperateResult<string[]> ParseAxisNames(byte[] content)
  {
    byte[] extraContent = this.ExtraContentArray(content.RemoveBegin<byte>(10))[0];
    OperateResult result = this.CheckSingleResultLeagle(extraContent);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<string[]>(result);
    int num = this.ByteTransform.TransInt32(extraContent, 10);
    List<string> stringList = new List<string>();
    for (int index = 0; index < num; index += 4)
    {
      if (index < extraContent.Length)
        stringList.Add(Encoding.ASCII.GetString(extraContent, 14 + index, 1));
    }
    return OperateResult.CreateSuccessResult<string[]>(stringList.ToArray());
  }

  /// <summary>
  /// 获取系统的轴名称信息，数组的长度表示有几个轴<br />
  /// Gets the axis name information of the system, and the length of the array indicates how many axes there are
  /// </summary>
  /// <returns>机床的轴名称列表</returns>
  [HslMqttApi(Description = "Gets the axis name information of the system, and the length of the array indicates how many axes there are")]
  public OperateResult<string[]> ReadAxisNames()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 137, 0, 0, 0, 0, 0)));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string[]>((OperateResult) result) : this.ParseAxisNames(result.Content);
  }

  /// <summary>读取调试信息，需要指定调式编号，轴编号</summary>
  /// <param name="number">Specify the diagnosis number. See the "MAINTENANCE MANUAL" of CNC about available diagnosis data number</param>
  /// <param name="axis">Specify the axis number. -1 means all axes </param>
  /// <param name="length">Specify the data block length</param>
  /// <returns>Reads the diagnosis specified by "number","axis" (only for the diagnosis with axis). The data format depends on each diagnosis. The </returns>
  public OperateResult<double[]> ReadDiagnoss(int number, int length, int axis)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 147, number, number + length - 1, axis, 0, 0)));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<double[]>((OperateResult) result) : this.ParseDiagnoss(result.Content, length);
  }

  private OperateResult<double[]> ParseDiagnoss(byte[] content, int length)
  {
    byte[] extraContent = this.ExtraContentArray(content.RemoveBegin<byte>(10))[0];
    OperateResult result = this.CheckSingleResultLeagle(extraContent);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<double[]>(result);
    List<double> doubleList = new List<double>();
    int num1 = 14;
    for (int index1 = 0; index1 < length; ++index1)
    {
      int num2 = num1 + 8;
      if (num2 < extraContent.Length)
      {
        for (int index2 = 0; index2 < 32 /*0x20*/ && num2 + index2 * 8 < extraContent.Length; ++index2)
          doubleList.Add(this.GetFanucDouble(extraContent, num2 + index2 * 8));
        num1 = num2 + 256 /*0x0100*/;
      }
      else
        break;
    }
    return OperateResult.CreateSuccessResult<double[]>(doubleList.ToArray());
  }

  /// <summary>
  /// 读取系统的主轴名称信息，返回的数组长度表示有几个主轴<br />
  /// Reads the system's spindle name information, and the returned array length indicates how many spindles there are
  /// </summary>
  /// <returns>机床的主轴信息</returns>
  [HslMqttApi(Description = "Reads the system's spindle name information, and the returned array length indicates how many spindles there are")]
  public OperateResult<string[]> ReadSpindleNames()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 138, 0, 0, 0, 0, 0)));
    return !result.IsSuccess ? OperateResult.CreateFailedResult<string[]>((OperateResult) result) : this.ParseAxisNames(result.Content);
  }

  /// <summary>
  /// 读取设备的程序列表<br />
  /// Read the program list of the device
  /// </summary>
  /// <returns>读取结果信息</returns>
  [HslMqttApi(Description = "Read the program list of the device")]
  public OperateResult<int[]> ReadProgramList()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 6, 1, 19, 0, 0, 0)));
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 6, 6667, 19, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int[]>((OperateResult) result1);
    if (!operateResult.IsSuccess)
      return OperateResult.CreateFailedResult<int[]>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<int[]>(result2);
    int length = (extraContent.Length - 14) / 72;
    int[] numArray = new int[length];
    for (int index = 0; index < length; ++index)
      numArray[index] = this.ByteTransform.TransInt32(extraContent, 14 + 72 * index);
    return OperateResult.CreateSuccessResult<int[]>(numArray);
  }

  /// <summary>
  /// 读取当前的刀具补偿信息<br />
  /// Read current tool compensation information
  /// </summary>
  /// <param name="cutterNumber">刀具数量</param>
  /// <returns>结果内容</returns>
  [HslMqttApi(Description = "Read current tool compensation information")]
  public OperateResult<CutterInfo[]> ReadCutterInfos(int cutterNumber = 24)
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 1, 0, 0)));
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) result2);
    OperateResult<byte[]> result3 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 2, 0, 0)));
    if (!result3.IsSuccess)
      return OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) result3);
    OperateResult<byte[]> result4 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 3, 0, 0)));
    return !result4.IsSuccess ? OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) result4) : this.ExtraCutterInfos(result1.Content, result2.Content, result3.Content, result4.Content, cutterNumber);
  }

  /// <summary>
  /// 读取当前的正在使用的刀具号<br />
  /// Read the tool number currently in use
  /// </summary>
  /// <returns>刀具号信息</returns>
  [HslMqttApi(Description = "Read the tool number currently in use")]
  public OperateResult<int> ReadCutterNumber()
  {
    OperateResult<double[]> result = this.ReadSystemMacroValue(4120, 1);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int>((OperateResult) result) : OperateResult.CreateSuccessResult<int>(Convert.ToInt32(result.Content[0]));
  }

  /// <summary>
  /// 读取寄存器的数据信息，需要传入寄存器的代码，起始地址，结束地址信息<br />
  /// To read the data information of the register, you need to pass in the code of the register, the start address, and the end address information
  /// </summary>
  /// <param name="code">寄存器代码</param>
  /// <param name="start">起始的地址</param>
  /// <param name="end">结束的地址</param>
  /// <returns>包含原始字节信息的结果对象</returns>
  [HslMqttApi(Description = "To read the data information of the register, you need to pass in the code of the register, the start address, and the end address information")]
  public OperateResult<byte[]> ReadData(int code, int start, int end)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadMulti((ushort) 2, (ushort) 32769, start, end, code, 0, 0)));
    if (!operateResult.IsSuccess)
      return operateResult;
    byte[] extraContent = this.ExtraContentArray(operateResult.Content.RemoveBegin<byte>(10))[0];
    OperateResult result = this.CheckSingleResultLeagle(extraContent);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result);
    int length = (int) this.ByteTransform.TransUInt16(extraContent, 12);
    return OperateResult.CreateSuccessResult<byte[]>(extraContent.SelectMiddle<byte>(14, length));
  }

  /// <summary>
  /// 将原始字节的数据写入到指定的寄存器里，需要传入寄存器的代码，起始地址，原始的字节数据信息<br />
  /// To write the original byte data into the specified register, you need to pass in the code of the register, the starting address, and the original byte data information
  /// </summary>
  /// <param name="code">寄存器代码</param>
  /// <param name="start">起始的地址</param>
  /// <param name="data">等待写入的原始字节数据</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "To write the original byte data into the specified register, you need to pass in the code of the register, the starting address, and the original byte data information")]
  public OperateResult WriteData(int code, int start, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 2, (ushort) 32770, start, start + data.Length - 1, code, 0, data)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string, int>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <summary>
  /// 读取PMC数据，需要传入起始地址和结束地址，返回byte[]数据信息<br />
  /// To read PMC data, you need to pass in the start address and length, and return byte[] data information
  /// </summary>
  /// <remarks>地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</remarks>
  /// <param name="address">起始地址，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</param>
  /// <param name="length">长度信息</param>
  /// <returns>读取结果</returns>
  [HslMqttApi(Description = "To read PMC data, you need to pass in the start address and length, and return byte[] data information")]
  public OperateResult<byte[]> ReadPMCData(string address, ushort length)
  {
    return FanucPMCAddress.ParseFrom(address, length).Then<byte[]>((Func<FanucPMCAddress, OperateResult<byte[]>>) (m => this.ReadData(m.DataCode, m.AddressStart, m.AddressEnd)));
  }

  /// <summary>
  /// 写入PMC数据，需要传入起始地址和，以及等待写入的byte[]数据信息<br />
  /// To write PMC data, you need to pass in the start address, as well as the byte[] data information waiting to be written
  /// </summary>
  /// <remarks>地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</remarks>
  /// <param name="address">起始地址，地址支持，G,F,Y,X,A,R,T,K,C,D,E 地址，例如 G5</param>
  /// <param name="value">等待写入的原始字节数据</param>
  /// <returns>是否写入成功</returns>
  [HslMqttApi(Description = "To write PMC data, you need to pass in the start address, as well as the byte[] data information waiting to be written")]
  public OperateResult WritePMCData(string address, byte[] value)
  {
    return FanucPMCAddress.ParseFrom(address, (ushort) 1).Then((Func<FanucPMCAddress, OperateResult>) (m => this.WriteData(m.DataCode, m.AddressStart, value)));
  }

  /// <summary>
  /// 读取工件尺寸<br />
  /// Read workpiece size
  /// </summary>
  /// <returns>结果数据信息</returns>
  [HslMqttApi(Description = "Read workpiece size")]
  public OperateResult<double[]> ReadDeviceWorkPiecesSize() => this.ReadSystemMacroValue(601, 20);

  /// <summary>
  /// 读取当前的程序内容，只能读取程序的片段，返回程序内容。<br />
  /// Read the current program content, only read the program fragments, and return the program content.
  /// </summary>
  /// <returns>程序内容</returns>
  [HslMqttApi(Description = "Read the current program content, only read the program fragments, and return the program content.")]
  public OperateResult<string> ReadCurrentProgram()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 32 /*0x20*/, 1428, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>(result2) : OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(extraContent, 18, extraContent.Length - 18));
  }

  /// <summary>
  /// 设置指定的程序号为当前的主程序，如果程序号不存在，返回错误信息<br />
  /// Set the specified program number as the current main program, if the program number does not exist, an error message will be returned
  /// </summary>
  /// <param name="programNum">程序号信息</param>
  /// <returns>是否设置成功</returns>
  [HslMqttApi(Description = "Set the specified program number as the current main program, if the program number does not exist, an error message will be returned.")]
  public OperateResult SetCurrentProgram(ushort programNum)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 3, (int) programNum, 0, 0, 0, 0)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<int, string>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <summary>
  /// 启动加工程序<br />
  /// Start the processing program
  /// </summary>
  /// <returns>是否启动成功</returns>
  [HslMqttApi(Description = "Start the processing program")]
  public OperateResult StartProcessing()
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 1, 0, 0, 0, 0, 0)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<int, string>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <summary>
  /// <b>[商业授权]</b> 将指定文件的NC加工程序，下载到数控机床里，返回是否下载成功<br />
  /// <b>[Authorization]</b> Download the NC machining program of the specified file to the CNC machine tool, and return whether the download is successful
  /// </summary>
  /// <remarks>
  /// 程序文件的内容必须%开始，%结束，下面是一个非常简单的例子：<br />
  /// %<br />
  /// O0006<br />
  /// G90G10L2P1<br />
  /// M30<br />
  /// %
  /// </remarks>
  /// <param name="file">程序文件的路径</param>
  /// <returns>是否下载成功</returns>
  [HslMqttApi(Description = "Download the NC machining program of the specified file to the CNC machine tool, and return whether the download is successful")]
  public OperateResult WriteProgramFile(string file)
  {
    return this.WriteProgramContent(File.ReadAllText(file));
  }

  /// <summary>
  /// <b>[商业授权]</b> 将指定程序内容的NC加工程序，写入到数控机床里，返回是否下载成功<br />
  /// <b>[Authorization]</b> Download the NC machining program to the CNC machine tool, and return whether the download is successful
  /// </summary>
  /// <remarks>
  /// 程序文件的内容必须%开始，%结束，下面是一个非常简单的例子：<br />
  /// %<br />
  /// O0006<br />
  /// G90G10L2P1<br />
  /// M30<br />
  /// %
  /// </remarks>
  /// <param name="program">程序内容信息</param>
  /// <param name="everyWriteSize">每次写入的长度信息</param>
  /// <param name="path">程序路径信息，默认为空，就是 //CNC_MEM/USER/PATH1/ 如果需要指定PATH2，需要输入 //CNC_MEM/USER/PATH2/</param>
  /// <returns>是否下载成功</returns>
  [HslMqttApi(Description = "Download the NC machining program to the CNC machine tool, and return whether the download is successful")]
  public OperateResult WriteProgramContent(string program, int everyWriteSize = 512 /*0x0200*/, string path = "")
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult(StringResources.Language.InsufficientPrivileges);
    OperateResult<Socket> socketAndConnect = this.CreateSocketAndConnect(this.IpAddress, this.Port, this.ConnectTimeOut);
    if (!socketAndConnect.IsSuccess)
      return (OperateResult) socketAndConnect.ConvertFailed<int>();
    OperateResult<byte[]> operateResult1 = this.ReadFromCoreServer(socketAndConnect.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
    if (!operateResult1.IsSuccess)
      return (OperateResult) operateResult1;
    OperateResult<byte[]> operateResult2 = this.ReadFromCoreServer(socketAndConnect.Content, this.BulidWriteProgramFilePre(path));
    if (!operateResult2.IsSuccess)
      return (OperateResult) operateResult2;
    List<byte[]> numArrayList = this.BulidWriteProgram(Encoding.ASCII.GetBytes(program), everyWriteSize);
    for (int index = 0; index < numArrayList.Count; ++index)
    {
      OperateResult<byte[]> operateResult3 = this.ReadFromCoreServer(socketAndConnect.Content, numArrayList[index], false);
      if (!operateResult3.IsSuccess)
        return (OperateResult) operateResult3;
    }
    OperateResult<byte[]> operateResult4 = this.ReadFromCoreServer(socketAndConnect.Content, new byte[10]
    {
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 0,
      (byte) 1,
      (byte) 19,
      (byte) 1,
      (byte) 0,
      (byte) 0
    });
    if (!operateResult4.IsSuccess)
      return (OperateResult) operateResult4;
    socketAndConnect.Content?.Close();
    if (operateResult4.Content.Length >= 14)
    {
      int err = (int) this.ByteTransform.TransInt16(operateResult4.Content, 12);
      if (err != 0)
        return (OperateResult) new OperateResult<string>(err, StringResources.Language.UnknownError);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// <b>[商业授权]</b> 读取指定程序号的程序内容，可以指定路径信息，路径默认为空就是主路径，//CNC_MEM/USER/PATH1/ ，也可以指定其他路径<br />
  /// <b>[Authorization]</b> Read the program content of the specified program number
  /// </summary>
  /// <param name="program">程序号或是程序名称</param>
  /// <param name="path">程序路径信息，默认为空，就是 //CNC_MEM/USER/PATH1/ 如果需要指定PATH2，需要输入 //CNC_MEM/USER/PATH2/</param>
  /// <returns>程序内容</returns>
  [HslMqttApi(Description = "Read the program content of the specified program number")]
  public OperateResult<string> ReadProgram(int program, string path = "")
  {
    return this.ReadProgram("O" + program.ToString(), path);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadProgram(System.Int32,System.String)" />
  public OperateResult<string> ReadProgram(string program, string path = "")
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<string>(StringResources.Language.InsufficientPrivileges);
    OperateResult<Socket> socketAndConnect = this.CreateSocketAndConnect(this.IpAddress, this.Port, this.ConnectTimeOut);
    if (!socketAndConnect.IsSuccess)
      return socketAndConnect.ConvertFailed<string>();
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(socketAndConnect.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    OperateResult<byte[]> result2 = this.ReadFromCoreServer(socketAndConnect.Content, this.BuildReadProgramPre(program, path));
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result2);
    int err = (int) result2.Content[12] * 256 /*0x0100*/ + (int) result2.Content[13];
    if (err != 0)
    {
      socketAndConnect.Content?.Close();
      return new OperateResult<string>(err, StringResources.Language.UnknownError);
    }
    StringBuilder stringBuilder = new StringBuilder();
    OperateResult<byte[]> result3;
    while (true)
    {
      result3 = this.ReadFromCoreServer(socketAndConnect.Content, (byte[]) null);
      if (result3.IsSuccess)
      {
        if (result3.Content[6] == (byte) 22)
          stringBuilder.Append(Encoding.ASCII.GetString(result3.Content, 10, result3.Content.Length - 10));
        else if (result3.Content[6] == (byte) 23)
          goto label_19;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<string>((OperateResult) result3);
label_19:
    this.Send(socketAndConnect.Content, new byte[10]
    {
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 0,
      (byte) 1,
      (byte) 23,
      (byte) 2,
      (byte) 0,
      (byte) 0
    });
    socketAndConnect.Content?.Close();
    return OperateResult.CreateSuccessResult<string>(stringBuilder.ToString());
  }

  /// <summary>
  /// 根据指定的程序号信息，删除当前的程序信息<br />
  /// According to the designated program number information, delete the current program information
  /// </summary>
  /// <param name="program">程序号</param>
  /// <returns>是否删除成功</returns>
  [HslMqttApi(Description = "According to the designated program number information, delete the current program information")]
  public OperateResult DeleteProgram(int program)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 5, program, 0, 0, 0, 0)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<int, string>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <summary>
  /// 根据指定的文件名来删除文件，如果是路径，则必须 '/' 结尾，如果是文件，则需要输入完整的文件名，例如：//CNC_MEM/USER/PATH2/O12<br />
  /// Delete the file according to the specified file name, if it is a path, it must end with '/', if it is a file, you need to enter the complete file name, for example: //CNC_MEM/USER/PATH2/O12
  /// </summary>
  /// <param name="fileName">文件名称，也可以是路径信息，例如：//CNC_MEM/USER/PATH2/O12</param>
  /// <returns>是否删除成功</returns>
  [HslMqttApi(Description = "Delete the file according to the specified file name, if it is a path, it must end with '/', if it is a file, you need to enter the complete file name, for example: //CNC_MEM/USER/PATH2/O12")]
  public OperateResult DeleteFile(string fileName)
  {
    byte[] data = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(fileName).CopyTo((Array) data, 0);
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 182, 0, 0, 0, 0, data)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <summary>
  /// 读取当前程序的前台路径<br />
  /// Read the foreground path of the current program
  /// </summary>
  /// <returns>程序的路径信息</returns>
  [HslMqttApi(Description = "Read the foreground path of the current program")]
  public OperateResult<string> ReadCurrentForegroundDir()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 176 /*0xB0*/, 1, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<string>(result2) : OperateResult.CreateSuccessResult<string>(extraContent.GetStringOrEndChar(14, extraContent.Length - 14, this.encoding));
  }

  /// <summary>读取指定路径下的所有的子路径和文件的信息，路径信息，例如 "//CNC_MEM/USER/"</summary>
  /// <param name="path">路径信息，例如 "//CNC_MEM/USER/"</param>
  /// <returns>文件及路径信息</returns>
  public OperateResult<FileDirInfo[]> ReadAllDirectoryAndFile(string path)
  {
    if (!path.EndsWith("/"))
      path += "/";
    byte[] data = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(path).CopyTo((Array) data, 0);
    OperateResult<int> result1 = this.ReadAllDirectoryAndFileCount(path);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<FileDirInfo[]>((OperateResult) result1);
    if (result1.Content == 0)
      return OperateResult.CreateSuccessResult<FileDirInfo[]>(new FileDirInfo[0]);
    int[] array = SoftBasic.SplitIntegerToArray(result1.Content, 20);
    List<FileDirInfo> fileDirInfoList = new List<FileDirInfo>();
    int a = 0;
    for (int index1 = 0; index1 < array.Length; ++index1)
    {
      OperateResult<byte[]> result2 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 179, a, array[index1], 1, 1, data)));
      if (!result2.IsSuccess)
        return OperateResult.CreateFailedResult<FileDirInfo[]>((OperateResult) result2);
      if (result2.Content.Length == 18 || this.ByteTransform.TransInt16(result2.Content, 10) == (short) 0)
      {
        result2 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 179, 0, 20, 1, 1, data)));
        if (!result2.IsSuccess)
          return OperateResult.CreateFailedResult<FileDirInfo[]>((OperateResult) result2);
      }
      byte[] extraContent = this.ExtraContentArray(result2.Content.RemoveBegin<byte>(10))[0];
      OperateResult result3 = this.CheckSingleResultLeagle(extraContent);
      if (!result3.IsSuccess)
        return OperateResult.CreateFailedResult<FileDirInfo[]>(result3);
      int num = (extraContent.Length - 14) / 128 /*0x80*/;
      for (int index2 = 0; index2 < num; ++index2)
        fileDirInfoList.Add(new FileDirInfo(this.ByteTransform, extraContent, 14 + 128 /*0x80*/ * index2));
      a += array[index1];
    }
    return OperateResult.CreateSuccessResult<FileDirInfo[]>(fileDirInfoList.ToArray());
  }

  /// <summary>
  /// 获取指定的路径里所有的文件夹数量和文件数量之和，路径示例：例如 "//CNC_MEM/USER/"， "//CNC_MEM/USER/PATH1/"
  /// </summary>
  /// <param name="path">路径信息，例如 "//CNC_MEM/USER/"</param>
  /// <returns>文件夹数量和文件数量之和</returns>
  public OperateResult<int> ReadAllDirectoryAndFileCount(string path)
  {
    if (!path.EndsWith("/"))
      path += "/";
    byte[] data = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(path).CopyTo((Array) data, 0);
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 180, 0, 0, 0, 0, data)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<int>(result2) : OperateResult.CreateSuccessResult<int>(this.ByteTransform.TransInt32(extraContent, 14) + this.ByteTransform.TransInt32(extraContent, 18));
  }

  /// <summary>
  /// 设置指定路径为当前路径<br />
  /// Set the specified path as the current path
  /// </summary>
  /// <param name="programName">程序名</param>
  /// <returns>结果信息</returns>
  [HslMqttApi(Description = "Set the specified path as the current path")]
  public OperateResult SetDeviceProgsCurr(string programName)
  {
    OperateResult<string> operateResult = this.ReadCurrentForegroundDir();
    if (!operateResult.IsSuccess)
      return (OperateResult) operateResult;
    byte[] data = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(operateResult.Content + programName).CopyTo((Array) data, 0);
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 186, 0, 0, 0, 0, data)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <summary>
  /// 读取机床的当前时间信息<br />
  /// Read the current time information of the machine tool
  /// </summary>
  /// <returns>时间信息</returns>
  [HslMqttApi(Description = "Read the current time information of the machine tool")]
  public OperateResult<DateTime> ReadCurrentDateTime()
  {
    OperateResult<double> result1 = this.ReadSystemMacroValue(3011);
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<DateTime>((OperateResult) result1);
    OperateResult<double> result2 = this.ReadSystemMacroValue(3012);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<DateTime>((OperateResult) result2);
    int int32 = Convert.ToInt32(result1.Content);
    string str1 = int32.ToString();
    int32 = Convert.ToInt32(result2.Content);
    string str2 = int32.ToString().PadLeft(6, '0');
    return OperateResult.CreateSuccessResult<DateTime>(new DateTime(int.Parse(str1.Substring(0, 4)), int.Parse(str1.Substring(4, 2)), int.Parse(str1.Substring(6)), int.Parse(str2.Substring(0, 2)), int.Parse(str2.Substring(2, 2)), int.Parse(str2.Substring(4))));
  }

  /// <summary>
  /// 读取当前的已加工的零件数量<br />
  /// Read the current number of processed parts
  /// </summary>
  /// <returns>已经加工的零件数量</returns>
  [HslMqttApi(Description = "Read the current number of processed parts")]
  public OperateResult<int> ReadCurrentProduceCount()
  {
    OperateResult<double> result = this.ReadSystemMacroValue(3901);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int>((OperateResult) result) : OperateResult.CreateSuccessResult<int>(Convert.ToInt32(result.Content));
  }

  /// <summary>
  /// 读取期望的加工的零件数量<br />
  /// Read the expected number of processed parts
  /// </summary>
  /// <returns>期望的加工的零件数量</returns>
  [HslMqttApi(Description = "Read the expected number of processed parts")]
  public OperateResult<int> ReadExpectProduceCount()
  {
    OperateResult<double> result = this.ReadSystemMacroValue(3902);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int>((OperateResult) result) : OperateResult.CreateSuccessResult<int>(Convert.ToInt32(result.Content));
  }

  /// <summary>
  /// 读取机床的操作信息<br />
  /// Read machine operation information
  /// </summary>
  /// <returns>操作信息列表</returns>
  [HslMqttApi(Description = "Read machine operation information")]
  public OperateResult<FanucOperatorMessage[]> ReadOperatorMessage()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 52, 0, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<FanucOperatorMessage[]>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<FanucOperatorMessage[]>(result2);
    List<FanucOperatorMessage> fanucOperatorMessageList = new List<FanucOperatorMessage>();
    int index = 12;
    do
    {
      ushort length = this.ByteTransform.TransUInt16(extraContent, index);
      fanucOperatorMessageList.Add(FanucOperatorMessage.CreateMessage(this.ByteTransform, extraContent.SelectMiddle<byte>(index + 2, (int) length), this.encoding));
      index = index + 2 + (int) length;
    }
    while (index < extraContent.Length);
    return OperateResult.CreateSuccessResult<FanucOperatorMessage[]>(fanucOperatorMessageList.ToArray());
  }

  /// <summary>
  /// 根据刀组号读取刀具信息，包括寿命及使用次数。<br />
  /// Tool information is read according to the tool group number, including life and number of uses.
  /// </summary>
  /// <param name="groupId">刀组号</param>
  /// <returns>刀具信息</returns>
  [HslMqttApi(Description = "Tool information is read according to the tool group number, including life and number of uses.")]
  public OperateResult<ToolInformation> ReadToolInfoByGroup(short groupId)
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 139, (int) groupId, (int) groupId, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<ToolInformation>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    if (!result2.IsSuccess)
      return OperateResult.CreateFailedResult<ToolInformation>(result2);
    this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 77, 2, 1, 2, 0, 0)));
    return OperateResult.CreateSuccessResult<ToolInformation>(new ToolInformation(extraContent, this.ByteTransform));
  }

  /// <summary>
  /// 读取当前正在使用的刀组号<br />
  /// Reads the knife group number that is currently in use
  /// </summary>
  /// <returns>到组号信息</returns>
  [HslMqttApi(Description = "Reads the knife group number that is currently in use")]
  public OperateResult<int> ReadUseToolGroupId()
  {
    OperateResult<byte[]> result1 = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 72, 0, 0, 0, 0, 0)));
    if (!result1.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) result1);
    byte[] extraContent = this.ExtraContentArray(result1.Content.RemoveBegin<byte>(10))[0];
    OperateResult result2 = this.CheckSingleResultLeagle(extraContent);
    return !result2.IsSuccess ? OperateResult.CreateFailedResult<int>(result2) : OperateResult.CreateSuccessResult<int>(this.ByteTransform.TransInt32(extraContent, 18));
  }

  /// <summary>
  /// 清除刀组号信息<br />
  /// Clear the knife group number information
  /// </summary>
  /// <param name="start">开始编号</param>
  /// <param name="end">结束编号</param>
  /// <returns>是否清楚成功</returns>
  [HslMqttApi(Description = "Clear the knife group number information")]
  public OperateResult ClearToolGroup(int start, int end)
  {
    OperateResult<byte[]> result = this.ReadFromCoreServer(this.BuildReadArray(this.BuildReadSingle((ushort) 82, start, end, 0, 0, 0)));
    return !result.IsSuccess ? (OperateResult) OperateResult.CreateFailedResult<int>((OperateResult) result) : this.CheckSingleResultLeagle(this.ExtraContentArray(result.Content.RemoveBegin<byte>(10))[0]);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSpindleSpeedAndFeedRate" />
  public async Task<OperateResult<double, double>> ReadSpindleSpeedAndFeedRateAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 164, 3, 0, 0, 0, 0), this.BuildReadSingle((ushort) 138, 1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 3, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 4, 0, 0, 0, 0), this.BuildReadSingle((ushort) 36, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 37, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 164, 3, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<double, double>((OperateResult) read);
    List<byte[]> result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10));
    return OperateResult.CreateSuccessResult<double, double>(this.GetFanucDouble(result[5], 14), this.GetFanucDouble(result[4], 14));
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSystemProgramCurrent" />
  public async Task<OperateResult<string, int>> ReadSystemProgramCurrentAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 207, 0, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string, int>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult check = this.CheckSingleResultLeagle(result);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<string, int>(check);
    int number = this.ByteTransform.TransInt32(result, 14);
    string name = result.GetStringOrEndChar(18, 36, this.encoding);
    return OperateResult.CreateSuccessResult<string, int>(name, number);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadProgramNumber" />
  public async Task<OperateResult<int>> ReadProgramNumberAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 28, 0, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult check = this.CheckSingleResultLeagle(result);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<int>(this.ByteTransform.TransInt32(result, 14)) : OperateResult.CreateFailedResult<int>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadLanguage" />
  public async Task<OperateResult<ushort>> ReadLanguageAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 141, 3281, 3281, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<ushort>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult check = this.CheckSingleResultLeagle(result);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<ushort>(check);
    ushort code = this.ByteTransform.TransUInt16(result, 24);
    this.ChangeTextEncoding(code);
    return OperateResult.CreateSuccessResult<ushort>(code);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSystemMacroValue(System.Int32)" />
  public async Task<OperateResult<double>> ReadSystemMacroValueAsync(int number)
  {
    OperateResult<double[]> result = await this.ReadSystemMacroValueAsync(number, 1);
    return ByteTransformHelper.GetResultFromArray<double>(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSystemMacroValue(System.Int32,System.Int32)" />
  public async Task<OperateResult<double[]>> ReadSystemMacroValueAsync(int number, int length)
  {
    int[] lenArray = SoftBasic.SplitIntegerToArray(length, 5);
    int index = number;
    List<byte> result = new List<byte>();
    for (int i = 0; i < lenArray.Length; ++i)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 21, index, index + lenArray[i] - 1, 0, 0, 0)));
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<double[]>((OperateResult) read);
      result.AddRange((IEnumerable<byte>) this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0].RemoveBegin<byte>(14));
      index += lenArray[i];
      read = (OperateResult<byte[]>) null;
    }
    try
    {
      return OperateResult.CreateSuccessResult<double[]>(this.GetFanucDouble(result.ToArray(), 0, length));
    }
    catch (Exception ex)
    {
      return new OperateResult<double[]>($"{ex.Message} Source:{result.ToArray().ToHexString(' ')}");
    }
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadCutterNumber" />
  public async Task<OperateResult<int>> ReadCutterNumberAsync()
  {
    OperateResult<double[]> read = await this.ReadSystemMacroValueAsync(4120, 1);
    OperateResult<int> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<int>(Convert.ToInt32(read.Content[0])) : OperateResult.CreateFailedResult<int>((OperateResult) read);
    read = (OperateResult<double[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteSystemMacroValue(System.Int32,System.Double[])" />
  public async Task<OperateResult> WriteSystemMacroValueAsync(int number, double[] values)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildWriteSingle((ushort) 22, number, number + values.Length - 1, 0, 0, values)));
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string, int>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    return this.CheckSingleResultLeagle(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteCutterLengthShapeOffset(System.Int32,System.Double)" />
  public async Task<OperateResult> WriteCutterLengthSharpOffsetAsync(int cutter, double offset)
  {
    OperateResult operateResult = await this.WriteSystemMacroValueAsync(11000 + cutter, new double[1]
    {
      offset
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteCutterLengthWearOffset(System.Int32,System.Double)" />
  public async Task<OperateResult> WriteCutterLengthWearOffsetAsync(int cutter, double offset)
  {
    OperateResult operateResult = await this.WriteSystemMacroValueAsync(10000 + cutter, new double[1]
    {
      offset
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteCutterRadiusShapeOffset(System.Int32,System.Double)" />
  public async Task<OperateResult> WriteCutterRadiusSharpOffsetAsync(int cutter, double offset)
  {
    OperateResult operateResult = await this.WriteSystemMacroValueAsync(13000 + cutter, new double[1]
    {
      offset
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteCutterRadiusWearOffset(System.Int32,System.Double)" />
  public async Task<OperateResult> WriteCutterRadiusWearOffsetAsync(int cutter, double offset)
  {
    OperateResult operateResult = await this.WriteSystemMacroValueAsync(12000 + cutter, new double[1]
    {
      offset
    });
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadFanucAxisLoad" />
  public async Task<OperateResult<double[]>> ReadFanucAxisLoadAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 164, 2, 0, 0, 0, 0), this.BuildReadSingle((ushort) 137, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 86, 1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 164, 2, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<double[]>((OperateResult) read);
    List<byte[]> result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10));
    int length = -1;
    if (result[0].Length >= 16 /*0x10*/)
      length = (int) this.ByteTransform.TransUInt16(result[0], 14);
    if (length < 0 || length * 8 + 14 > result[2].Length)
      length = (result[2].Length - 14) / 8;
    return OperateResult.CreateSuccessResult<double[]>(this.GetFanucDouble(result[2], 14, length));
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSpindleLoad" />
  public async Task<OperateResult<double>> ReadSpindleLoadAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 64 /*0x40*/, 4, -1, 0, 0, 0), this.BuildReadSingle((ushort) 64 /*0x40*/, 5, -1, 0, 0, 0), this.BuildReadSingle((ushort) 138, 0, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<double>((OperateResult) read);
    List<byte[]> result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10));
    return result[0].Length < 18 ? new OperateResult<double>("Read failed, data is too short: " + result[0].ToHexString(' ')) : OperateResult.CreateSuccessResult<double>(this.GetFanucDouble(result[0], 14));
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSysAllCoors" />
  public async Task<OperateResult<SysAllCoors>> ReadSysAllCoorsAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 164, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 137, -1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 1, 0, 0, 0, 0), this.BuildReadSingle((ushort) 136, 2, 0, 0, 0, 0), this.BuildReadSingle((ushort) 163, 0, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 0, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 1, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 2, -1, 0, 0, 0), this.BuildReadSingle((ushort) 38, 3, -1, 0, 0, 0), this.BuildReadSingle((ushort) 164, 0, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<SysAllCoors>((OperateResult) read);
    List<byte[]> result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10));
    int length = (int) this.ByteTransform.TransUInt16(result[0], 14);
    return OperateResult.CreateSuccessResult<SysAllCoors>(new SysAllCoors()
    {
      Absolute = this.GetFanucDouble(result[5], 14, length),
      Machine = this.GetFanucDouble(result[6], 14, length),
      Relative = this.GetFanucDouble(result[7], 14, length)
    });
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSystemAlarm" />
  public async Task<OperateResult<SysAlarm[]>> ReadSystemAlarmAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 35, -1, 10, 2, 64 /*0x40*/, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<SysAlarm[]>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult check = this.CheckSingleResultLeagle(result);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<SysAlarm[]>(check);
    if (this.ByteTransform.TransUInt16(result, 12) <= (ushort) 0)
      return OperateResult.CreateSuccessResult<SysAlarm[]>(new SysAlarm[0]);
    int length = (int) this.ByteTransform.TransUInt16(result, 12) / 80 /*0x50*/;
    SysAlarm[] alarms = new SysAlarm[length];
    for (int i = 0; i < alarms.Length; ++i)
    {
      alarms[i] = new SysAlarm();
      alarms[i].AlarmId = this.ByteTransform.TransInt32(result, 14 + 80 /*0x50*/ * i);
      alarms[i].Type = this.ByteTransform.TransInt16(result, 20 + 80 /*0x50*/ * i);
      alarms[i].Axis = this.ByteTransform.TransInt16(result, 24 + 80 /*0x50*/ * i);
      ushort msgLength = this.ByteTransform.TransUInt16(result, 28 + 80 /*0x50*/ * i);
      alarms[i].Message = this.encoding.GetString(result, 30 + 80 /*0x50*/ * i, (int) msgLength);
    }
    return OperateResult.CreateSuccessResult<SysAlarm[]>(alarms);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadTimeData(System.Int32)" />
  public async Task<OperateResult<long>> ReadTimeDataAsync(int timeType)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 288, timeType, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<long>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult check = this.CheckSingleResultLeagle(result);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<long>(check);
    int millisecond = this.ByteTransform.TransInt32(result, 18);
    long munite = (long) this.ByteTransform.TransInt32(result, 14);
    if (millisecond < 0 || millisecond > 60000 || munite < 0L)
    {
      millisecond = BitConverter.ToInt32(result, 18);
      munite = (long) BitConverter.ToInt32(result, 14);
    }
    long seconds = (long) (millisecond / 1000);
    return OperateResult.CreateSuccessResult<long>(munite * 60L + seconds);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadAlarmStatus" />
  public async Task<OperateResult<int>> ReadAlarmStatusAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 26, 0, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult check = this.CheckSingleResultLeagle(result);
    return check.IsSuccess ? OperateResult.CreateSuccessResult<int>((int) this.ByteTransform.TransUInt16(result, 16 /*0x10*/)) : OperateResult.CreateFailedResult<int>(check);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSysStatusInfo" />
  public async Task<OperateResult<SysStatusInfo>> ReadSysStatusInfoAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 25, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 225, 0, 0, 0, 0, 0), this.BuildReadSingle((ushort) 152, 0, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<SysStatusInfo>((OperateResult) read);
    List<byte[]> result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10));
    return this.CreateSysStatusInfo(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadAxisNames" />
  public async Task<OperateResult<string[]>> ReadAxisNamesAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 137, 0, 0, 0, 0, 0)));
    OperateResult<string[]> operateResult = read.IsSuccess ? this.ParseAxisNames(read.Content) : OperateResult.CreateFailedResult<string[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadDiagnoss(System.Int32,System.Int32,System.Int32)" />
  public async Task<OperateResult<double[]>> ReadDiagnossAsync(int number, int length, int axis)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 147, number, number + length - 1, axis, 0, 0)));
    OperateResult<double[]> operateResult = read.IsSuccess ? this.ParseDiagnoss(read.Content, length) : OperateResult.CreateFailedResult<double[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadSpindleNames" />
  public async Task<OperateResult<string[]>> ReadSpindleNamesAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 138, 0, 0, 0, 0, 0)));
    OperateResult<string[]> operateResult = read.IsSuccess ? this.ParseAxisNames(read.Content) : OperateResult.CreateFailedResult<string[]>((OperateResult) read);
    read = (OperateResult<byte[]>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadProgramList" />
  public async Task<OperateResult<int[]>> ReadProgramListAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 6, 1, 19, 0, 0, 0)));
    OperateResult<byte[]> check = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 6, 6667, 19, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<int[]>((OperateResult) read);
    if (!check.IsSuccess)
      return OperateResult.CreateFailedResult<int[]>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult checkResult = this.CheckSingleResultLeagle(result);
    if (!checkResult.IsSuccess)
      return OperateResult.CreateFailedResult<int[]>(checkResult);
    int length = (result.Length - 14) / 72;
    int[] programs = new int[length];
    for (int i = 0; i < length; ++i)
      programs[i] = this.ByteTransform.TransInt32(result, 14 + 72 * i);
    return OperateResult.CreateSuccessResult<int[]>(programs);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadCutterInfos(System.Int32)" />
  public async Task<OperateResult<CutterInfo[]>> ReadCutterInfosAsync(int cutterNumber = 24)
  {
    OperateResult<byte[]> read1 = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 0, 0, 0)));
    if (!read1.IsSuccess)
      return OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) read1);
    OperateResult<byte[]> read2 = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 1, 0, 0)));
    if (!read2.IsSuccess)
      return OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) read2);
    OperateResult<byte[]> read3 = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 2, 0, 0)));
    if (!read3.IsSuccess)
      return OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) read3);
    OperateResult<byte[]> read4 = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 8, 1, cutterNumber, 3, 0, 0)));
    return read4.IsSuccess ? this.ExtraCutterInfos(read1.Content, read2.Content, read3.Content, read4.Content, cutterNumber) : OperateResult.CreateFailedResult<CutterInfo[]>((OperateResult) read4);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadData(System.Int32,System.Int32,System.Int32)" />
  public async Task<OperateResult<byte[]>> ReadDataAsync(int code, int start, int end)
  {
    OperateResult<byte[]> read1 = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadMulti((ushort) 2, (ushort) 32769, start, end, code, 0, 0)));
    if (!read1.IsSuccess)
      return read1;
    byte[] result = this.ExtraContentArray(read1.Content.RemoveBegin<byte>(10))[0];
    OperateResult checkResult = this.CheckSingleResultLeagle(result);
    if (!checkResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(checkResult);
    int length = (int) this.ByteTransform.TransUInt16(result, 12);
    return OperateResult.CreateSuccessResult<byte[]>(result.SelectMiddle<byte>(14, length));
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteData(System.Int32,System.Int32,System.Byte[])" />
  public async Task<OperateResult> WriteDataAsync(int code, int start, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildWriteSingle((ushort) 2, (ushort) 32770, start, start + data.Length - 1, code, 0, data)));
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string, int>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    return this.CheckSingleResultLeagle(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadPMCData(System.String,System.UInt16)" />
  public async Task<OperateResult<byte[]>> ReadPMCDataAsync(string address, ushort length)
  {
    OperateResult<FanucPMCAddress> analysis = FanucPMCAddress.ParseFrom(address, length);
    if (!analysis.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    OperateResult<byte[]> operateResult = await this.ReadDataAsync(analysis.Content.DataCode, analysis.Content.AddressStart, analysis.Content.AddressEnd);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WritePMCData(System.String,System.Byte[])" />
  public async Task<OperateResult> WritePMCDataAsync(string address, byte[] value)
  {
    OperateResult<FanucPMCAddress> analysis = FanucPMCAddress.ParseFrom(address, (ushort) 1);
    if (!analysis.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) analysis);
    OperateResult operateResult = await this.WriteDataAsync(analysis.Content.DataCode, analysis.Content.AddressStart, value);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadDeviceWorkPiecesSize" />
  public async Task<OperateResult<double[]>> ReadDeviceWorkPiecesSizeAsync()
  {
    OperateResult<double[]> operateResult = await this.ReadSystemMacroValueAsync(601, 20);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadCurrentForegroundDir" />
  public async Task<OperateResult<string>> ReadCurrentForegroundDirAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 176 /*0xB0*/, 1, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult checkResult = this.CheckSingleResultLeagle(result);
    return checkResult.IsSuccess ? OperateResult.CreateSuccessResult<string>(result.GetStringOrEndChar(14, result.Length - 14, this.encoding)) : OperateResult.CreateFailedResult<string>(checkResult);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadAllDirectoryAndFile(System.String)" />
  public async Task<OperateResult<FileDirInfo[]>> ReadAllDirectoryAndFileAsync(string path)
  {
    if (!path.EndsWith("/"))
      path += "/";
    byte[] buffer = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(path).CopyTo((Array) buffer, 0);
    OperateResult<int> readCount = await this.ReadAllDirectoryAndFileCountAsync(path);
    if (!readCount.IsSuccess)
      return OperateResult.CreateFailedResult<FileDirInfo[]>((OperateResult) readCount);
    if (readCount.Content == 0)
      return OperateResult.CreateSuccessResult<FileDirInfo[]>(new FileDirInfo[0]);
    int[] splits = SoftBasic.SplitIntegerToArray(readCount.Content, 20);
    List<FileDirInfo> list = new List<FileDirInfo>();
    int already = 0;
    for (int j = 0; j < splits.Length; ++j)
    {
      OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 179, already, splits[j], 1, 1, buffer)));
      if (!read.IsSuccess)
        return OperateResult.CreateFailedResult<FileDirInfo[]>((OperateResult) read);
      if (read.Content.Length == 18 || this.ByteTransform.TransInt16(read.Content, 10) == (short) 0)
      {
        read = this.ReadFromCoreServer(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 179, 0, 20, 1, 1, buffer)));
        if (!read.IsSuccess)
          return OperateResult.CreateFailedResult<FileDirInfo[]>((OperateResult) read);
      }
      byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
      OperateResult checkResult = this.CheckSingleResultLeagle(result);
      if (!checkResult.IsSuccess)
        return OperateResult.CreateFailedResult<FileDirInfo[]>(checkResult);
      int count = (result.Length - 14) / 128 /*0x80*/;
      for (int i = 0; i < count; ++i)
        list.Add(new FileDirInfo(this.ByteTransform, result, 14 + 128 /*0x80*/ * i));
      already += splits[j];
      read = (OperateResult<byte[]>) null;
      result = (byte[]) null;
      checkResult = (OperateResult) null;
    }
    return OperateResult.CreateSuccessResult<FileDirInfo[]>(list.ToArray());
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadAllDirectoryAndFileCount(System.String)" />
  public async Task<OperateResult<int>> ReadAllDirectoryAndFileCountAsync(string path)
  {
    if (!path.EndsWith("/"))
      path += "/";
    byte[] buffer = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(path).CopyTo((Array) buffer, 0);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 180, 0, 0, 0, 0, buffer)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<int>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult checkResult = this.CheckSingleResultLeagle(result);
    return checkResult.IsSuccess ? OperateResult.CreateSuccessResult<int>(this.ByteTransform.TransInt32(result, 14) + this.ByteTransform.TransInt32(result, 18)) : OperateResult.CreateFailedResult<int>(checkResult);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.SetDeviceProgsCurr(System.String)" />
  public async Task<OperateResult> SetDeviceProgsCurrAsync(string programName)
  {
    OperateResult<string> path = await this.ReadCurrentForegroundDirAsync();
    if (!path.IsSuccess)
      return (OperateResult) path;
    byte[] buffer = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(path.Content + programName).CopyTo((Array) buffer, 0);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 186, 0, 0, 0, 0, buffer)));
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    return this.CheckSingleResultLeagle(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadCurrentDateTime" />
  public async Task<OperateResult<DateTime>> ReadCurrentDateTimeAsync()
  {
    OperateResult<double> read1 = await this.ReadSystemMacroValueAsync(3011);
    if (!read1.IsSuccess)
      return OperateResult.CreateFailedResult<DateTime>((OperateResult) read1);
    OperateResult<double> read2 = await this.ReadSystemMacroValueAsync(3012);
    if (!read2.IsSuccess)
      return OperateResult.CreateFailedResult<DateTime>((OperateResult) read2);
    string date = Convert.ToInt32(read1.Content).ToString();
    string time = Convert.ToInt32(read2.Content).ToString().PadLeft(6, '0');
    return OperateResult.CreateSuccessResult<DateTime>(new DateTime(int.Parse(date.Substring(0, 4)), int.Parse(date.Substring(4, 2)), int.Parse(date.Substring(6)), int.Parse(time.Substring(0, 2)), int.Parse(time.Substring(2, 2)), int.Parse(time.Substring(4))));
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadCurrentProduceCount" />
  public async Task<OperateResult<int>> ReadCurrentProduceCountAsync()
  {
    OperateResult<double> read = await this.ReadSystemMacroValueAsync(3901);
    OperateResult<int> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<int>(Convert.ToInt32(read.Content)) : OperateResult.CreateFailedResult<int>((OperateResult) read);
    read = (OperateResult<double>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadExpectProduceCount" />
  public async Task<OperateResult<int>> ReadExpectProduceCountAsync()
  {
    OperateResult<double> read = await this.ReadSystemMacroValueAsync(3902);
    OperateResult<int> operateResult = read.IsSuccess ? OperateResult.CreateSuccessResult<int>(Convert.ToInt32(read.Content)) : OperateResult.CreateFailedResult<int>((OperateResult) read);
    read = (OperateResult<double>) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadCurrentProgram" />
  public async Task<OperateResult<string>> ReadCurrentProgramAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 32 /*0x20*/, 1428, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    OperateResult checkResult = this.CheckSingleResultLeagle(result);
    return checkResult.IsSuccess ? OperateResult.CreateSuccessResult<string>(Encoding.ASCII.GetString(result, 18, result.Length - 18)) : OperateResult.CreateFailedResult<string>(checkResult);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.SetCurrentProgram(System.UInt16)" />
  public async Task<OperateResult> SetCurrentProgramAsync(ushort programNum)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 3, (int) programNum, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<int, string>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    return this.CheckSingleResultLeagle(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.StartProcessing" />
  public async Task<OperateResult> StartProcessingAsync()
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 1, 0, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<int, string>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    return this.CheckSingleResultLeagle(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteProgramFile(System.String)" />
  public async Task<OperateResult> WriteProgramFileAsync(
    string file,
    int everyWriteSize = 512 /*0x0200*/,
    string path = "")
  {
    string content = File.ReadAllText(file);
    OperateResult operateResult = await this.WriteProgramContentAsync(content, everyWriteSize, path);
    content = (string) null;
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.WriteProgramContent(System.String,System.Int32,System.String)" />
  public async Task<OperateResult> WriteProgramContentAsync(
    string program,
    int everyWriteSize = 512 /*0x0200*/,
    string path = "")
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult(StringResources.Language.InsufficientPrivileges);
    OperateResult<Socket> socket = await this.CreateSocketAndConnectAsync(this.IpAddress, this.Port, this.ConnectTimeOut);
    if (!socket.IsSuccess)
      return (OperateResult) socket.ConvertFailed<int>();
    OperateResult<byte[]> ini1 = await this.ReadFromCoreServerAsync(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
    if (!ini1.IsSuccess)
      return (OperateResult) ini1;
    OperateResult<byte[]> read1 = await this.ReadFromCoreServerAsync(socket.Content, this.BulidWriteProgramFilePre(path));
    if (!read1.IsSuccess)
      return (OperateResult) read1;
    List<byte[]> contents = this.BulidWriteProgram(Encoding.ASCII.GetBytes(program), everyWriteSize);
    for (int i = 0; i < contents.Count; ++i)
    {
      OperateResult<byte[]> read2 = await this.ReadFromCoreServerAsync(socket.Content, contents[i], false);
      if (!read2.IsSuccess)
        return (OperateResult) read2;
      read2 = (OperateResult<byte[]>) null;
    }
    OperateResult<byte[]> read3 = await this.ReadFromCoreServerAsync(socket.Content, new byte[10]
    {
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 0,
      (byte) 1,
      (byte) 19,
      (byte) 1,
      (byte) 0,
      (byte) 0
    });
    if (!read3.IsSuccess)
      return (OperateResult) read3;
    socket.Content?.Close();
    if (read3.Content.Length >= 14)
    {
      int err = (int) this.ByteTransform.TransInt16(read3.Content, 12);
      if (err != 0)
        return (OperateResult) new OperateResult<string>(err, StringResources.Language.UnknownError);
    }
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadProgram(System.Int32,System.String)" />
  public async Task<OperateResult<string>> ReadProgramAsync(int program, string path = "")
  {
    OperateResult<string> operateResult = await this.ReadProgramAsync("O" + program.ToString(), path);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.ReadProgram(System.Int32,System.String)" />
  public async Task<OperateResult<string>> ReadProgramAsync(string program, string path = "")
  {
    if (!Authorization.asdniasnfaksndiqwhawfskhfaiw())
      return new OperateResult<string>(StringResources.Language.InsufficientPrivileges);
    OperateResult<Socket> socket = await this.CreateSocketAndConnectAsync(this.IpAddress, this.Port, this.ConnectTimeOut);
    if (!socket.IsSuccess)
      return socket.ConvertFailed<string>();
    OperateResult<byte[]> ini1 = await this.ReadFromCoreServerAsync(socket.Content, "a0 a0 a0 a0 00 01 01 01 00 02 00 01".ToHexBytes());
    if (!ini1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) ini1);
    OperateResult<byte[]> read1 = await this.ReadFromCoreServerAsync(socket.Content, this.BuildReadProgramPre(program, path));
    if (!read1.IsSuccess)
      return OperateResult.CreateFailedResult<string>((OperateResult) read1);
    int err = (int) read1.Content[12] * 256 /*0x0100*/ + (int) read1.Content[13];
    if (err != 0)
    {
      socket.Content?.Close();
      return new OperateResult<string>(err, StringResources.Language.UnknownError);
    }
    StringBuilder sb = new StringBuilder();
    OperateResult<byte[]> read2;
    while (true)
    {
      read2 = await this.ReadFromCoreServerAsync(socket.Content, (byte[]) null);
      if (read2.IsSuccess)
      {
        if (read2.Content[6] == (byte) 22)
          sb.Append(Encoding.ASCII.GetString(read2.Content, 10, read2.Content.Length - 10));
        else if (read2.Content[6] == (byte) 23)
          goto label_20;
        read2 = (OperateResult<byte[]>) null;
      }
      else
        break;
    }
    return OperateResult.CreateFailedResult<string>((OperateResult) read2);
label_20:
    OperateResult send = await this.SendAsync(socket.Content, new byte[10]
    {
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 0,
      (byte) 1,
      (byte) 23,
      (byte) 2,
      (byte) 0,
      (byte) 0
    });
    socket.Content?.Close();
    return OperateResult.CreateSuccessResult<string>(sb.ToString());
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.DeleteProgram(System.Int32)" />
  public async Task<OperateResult> DeleteProgramAsync(int program)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildReadSingle((ushort) 5, program, 0, 0, 0, 0)));
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<int, string>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    return this.CheckSingleResultLeagle(result);
  }

  /// <inheritdoc cref="M:HslCommunication.CNC.Fanuc.FanucSeries0i.DeleteFile(System.String)" />
  public async Task<OperateResult> DeleteFileAsync(string fileName)
  {
    byte[] buffer = new byte[256 /*0x0100*/];
    Encoding.ASCII.GetBytes(fileName).CopyTo((Array) buffer, 0);
    OperateResult<byte[]> read = await this.ReadFromCoreServerAsync(this.BuildReadArray(this.BuildWriteSingle((ushort) 1, (ushort) 182, 0, 0, 0, 0, buffer)));
    if (!read.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<string>((OperateResult) read);
    byte[] result = this.ExtraContentArray(read.Content.RemoveBegin<byte>(10))[0];
    return this.CheckSingleResultLeagle(result);
  }

  /// <summary>构建读取一个命令的数据内容</summary>
  /// <param name="code">命令码</param>
  /// <param name="a">第一个参数内容</param>
  /// <param name="b">第二个参数内容</param>
  /// <param name="c">第三个参数内容</param>
  /// <param name="d">第四个参数内容</param>
  /// <param name="e">第五个参数内容</param>
  /// <returns>总报文信息</returns>
  private byte[] BuildReadSingle(ushort code, int a, int b, int c, int d, int e)
  {
    return this.BuildReadMulti((ushort) 1, code, a, b, c, d, e);
  }

  /// <summary>构建读取多个命令的数据内容</summary>
  /// <param name="mode">模式</param>
  /// <param name="code">命令码</param>
  /// <param name="a">第一个参数内容</param>
  /// <param name="b">第二个参数内容</param>
  /// <param name="c">第三个参数内容</param>
  /// <param name="d">第四个参数内容</param>
  /// <param name="e">第五个参数内容</param>
  /// <returns>总报文信息</returns>
  private byte[] BuildReadMulti(ushort mode, ushort code, int a, int b, int c, int d, int e)
  {
    byte[] numArray = new byte[28];
    this.ByteTransform.TransByte((ushort) numArray.Length).CopyTo((Array) numArray, 0);
    this.ByteTransform.TransByte(mode).CopyTo((Array) numArray, 2);
    this.ByteTransform.TransByte(this.opPath).CopyTo((Array) numArray, 4);
    this.ByteTransform.TransByte(code).CopyTo((Array) numArray, 6);
    this.ByteTransform.TransByte(a).CopyTo((Array) numArray, 8);
    this.ByteTransform.TransByte(b).CopyTo((Array) numArray, 12);
    this.ByteTransform.TransByte(c).CopyTo((Array) numArray, 16 /*0x10*/);
    this.ByteTransform.TransByte(d).CopyTo((Array) numArray, 20);
    this.ByteTransform.TransByte(e).CopyTo((Array) numArray, 24);
    return numArray;
  }

  /// <summary>创建写入byte[]数组的报文信息</summary>
  /// <param name="mode">模式</param>
  /// <param name="code">命令码</param>
  /// <param name="a">第一个参数内容</param>
  /// <param name="b">第二个参数内容</param>
  /// <param name="c">第三个参数内容</param>
  /// <param name="d">第四个参数内容</param>
  /// <param name="data">等待写入的byte数组信息</param>
  /// <returns>总报文信息</returns>
  private byte[] BuildWriteSingle(
    ushort mode,
    ushort code,
    int a,
    int b,
    int c,
    int d,
    byte[] data)
  {
    byte[] numArray = new byte[28 + data.Length];
    this.ByteTransform.TransByte((ushort) numArray.Length).CopyTo((Array) numArray, 0);
    this.ByteTransform.TransByte(mode).CopyTo((Array) numArray, 2);
    this.ByteTransform.TransByte(this.opPath).CopyTo((Array) numArray, 4);
    this.ByteTransform.TransByte(code).CopyTo((Array) numArray, 6);
    this.ByteTransform.TransByte(a).CopyTo((Array) numArray, 8);
    this.ByteTransform.TransByte(b).CopyTo((Array) numArray, 12);
    this.ByteTransform.TransByte(c).CopyTo((Array) numArray, 16 /*0x10*/);
    this.ByteTransform.TransByte(d).CopyTo((Array) numArray, 20);
    this.ByteTransform.TransByte(data.Length).CopyTo((Array) numArray, 24);
    if (data.Length != 0)
      data.CopyTo((Array) numArray, 28);
    return numArray;
  }

  /// <summary>创建写入单个double数组的报文信息</summary>
  /// <param name="code">功能码</param>
  /// <param name="a">第一个参数内容</param>
  /// <param name="b">第二个参数内容</param>
  /// <param name="c">第三个参数内容</param>
  /// <param name="d">第四个参数内容</param>
  /// <param name="data">等待写入的double数组信息</param>
  /// <returns>总报文信息</returns>
  private byte[] BuildWriteSingle(ushort code, int a, int b, int c, int d, double[] data)
  {
    byte[] data1 = new byte[data.Length * 8];
    for (int index = 0; index < data.Length; ++index)
      this.CreateFromFanucDouble(data[index]).CopyTo((Array) data1, 0);
    return this.BuildWriteSingle((ushort) 1, code, a, b, c, d, data1);
  }

  /// <summary>创建多个命令报文的总报文信息</summary>
  /// <param name="commands">报文命令的数组</param>
  /// <returns>总报文信息</returns>
  private byte[] BuildReadArray(params byte[][] commands)
  {
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.Write(new byte[10]
    {
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 0,
      (byte) 1,
      (byte) 33,
      (byte) 1,
      (byte) 0,
      (byte) 30
    }, 0, 10);
    memoryStream.Write(this.ByteTransform.TransByte((ushort) commands.Length), 0, 2);
    for (int index = 0; index < commands.Length; ++index)
      memoryStream.Write(commands[index], 0, commands[index].Length);
    byte[] array = memoryStream.ToArray();
    this.ByteTransform.TransByte((ushort) (array.Length - 10)).CopyTo((Array) array, 8);
    return array;
  }

  private byte[] BulidWriteProgramFilePre(string path)
  {
    if (!string.IsNullOrEmpty(path))
    {
      if (!path.EndsWith("/"))
        path += "/";
      if (!path.StartsWith("N:"))
        path = "N:" + path;
    }
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.Write(new byte[10]
    {
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 0,
      (byte) 1,
      (byte) 17,
      (byte) 1,
      (byte) 2,
      (byte) 4
    }, 0, 10);
    memoryStream.Write(new byte[4]
    {
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 1
    }, 0, 4);
    memoryStream.Write(new byte[512 /*0x0200*/], 0, 512 /*0x0200*/);
    byte[] array = memoryStream.ToArray();
    if (!string.IsNullOrEmpty(path))
      Encoding.ASCII.GetBytes(path).CopyTo((Array) array, 14);
    return array;
  }

  /// <summary>创建读取运行程序的报文信息</summary>
  /// <param name="programName">程序号</param>
  /// <param name="path">程序路径信息</param>
  /// <returns>总报文</returns>
  private byte[] BuildReadProgramPre(string programName, string path = "")
  {
    if (!string.IsNullOrEmpty(path))
    {
      if (!path.EndsWith("/"))
        path += "/";
      if (!path.StartsWith("N:"))
        path = "N:" + path;
    }
    MemoryStream memoryStream = new MemoryStream();
    memoryStream.Write(new byte[10]
    {
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 160 /*0xA0*/,
      (byte) 0,
      (byte) 1,
      (byte) 21,
      (byte) 1,
      (byte) 2,
      (byte) 4
    }, 0, 10);
    memoryStream.Write(new byte[4]
    {
      (byte) 0,
      (byte) 0,
      (byte) 0,
      (byte) 1
    }, 0, 4);
    memoryStream.Write(new byte[512 /*0x0200*/], 0, 512 /*0x0200*/);
    byte[] array = memoryStream.ToArray();
    Encoding.ASCII.GetBytes(string.IsNullOrEmpty(path) ? $"{programName}-{programName}" : path + programName).CopyTo((Array) array, 14);
    return array;
  }

  private List<byte[]> BulidWriteProgram(byte[] program, int everyWriteSize)
  {
    List<byte[]> numArrayList = new List<byte[]>();
    int[] array1 = SoftBasic.SplitIntegerToArray(program.Length, everyWriteSize);
    int offset = 0;
    for (int index = 0; index < array1.Length; ++index)
    {
      MemoryStream memoryStream = new MemoryStream();
      memoryStream.Write(new byte[10]
      {
        (byte) 160 /*0xA0*/,
        (byte) 160 /*0xA0*/,
        (byte) 160 /*0xA0*/,
        (byte) 160 /*0xA0*/,
        (byte) 0,
        (byte) 1,
        (byte) 18,
        (byte) 4,
        (byte) 0,
        (byte) 0
      }, 0, 10);
      memoryStream.Write(program, offset, array1[index]);
      byte[] array2 = memoryStream.ToArray();
      this.ByteTransform.TransByte((ushort) (array2.Length - 10)).CopyTo((Array) array2, 8);
      numArrayList.Add(array2);
      offset += array1[index];
    }
    return numArrayList;
  }

  /// <summary>从机床返回的数据里解析出实际的数据内容，去除了一些多余的信息报文。</summary>
  /// <param name="content">返回的报文信息</param>
  /// <returns>解析之后的报文信息</returns>
  private List<byte[]> ExtraContentArray(byte[] content)
  {
    List<byte[]> numArrayList = new List<byte[]>();
    int num1 = (int) this.ByteTransform.TransUInt16(content, 0);
    int index1 = 2;
    for (int index2 = 0; index2 < num1; ++index2)
    {
      int num2 = (int) this.ByteTransform.TransUInt16(content, index1);
      if (num2 < 6 && numArrayList.Count > 0)
      {
        index1 -= 2;
        num2 = (int) this.ByteTransform.TransUInt16(content, index1) + 2;
      }
      if (num2 + index1 > content.Length)
        num2 = content.Length - index1;
      numArrayList.Add(content.SelectMiddle<byte>(index1 + 2, num2 - 2));
      index1 += num2;
    }
    return numArrayList;
  }

  private OperateResult<CutterInfo[]> ExtraCutterInfos(
    byte[] content1,
    byte[] content2,
    byte[] content3,
    byte[] content4,
    int cutterNumber)
  {
    List<byte[]> numArrayList1 = this.ExtraContentArray(content1.RemoveBegin<byte>(10));
    List<byte[]> numArrayList2 = this.ExtraContentArray(content2.RemoveBegin<byte>(10));
    List<byte[]> numArrayList3 = this.ExtraContentArray(content3.RemoveBegin<byte>(10));
    List<byte[]> numArrayList4 = this.ExtraContentArray(content4.RemoveBegin<byte>(10));
    bool flag1 = this.ByteTransform.TransInt16(numArrayList1[0], 6) == (short) 0;
    bool flag2 = this.ByteTransform.TransInt16(numArrayList2[0], 6) == (short) 0;
    bool flag3 = this.ByteTransform.TransInt16(numArrayList3[0], 6) == (short) 0;
    bool flag4 = this.ByteTransform.TransInt16(numArrayList4[0], 6) == (short) 0;
    CutterInfo[] cutterInfoArray = new CutterInfo[cutterNumber];
    for (int index = 0; index < cutterInfoArray.Length; ++index)
    {
      cutterInfoArray[index] = new CutterInfo();
      cutterInfoArray[index].LengthSharpOffset = flag1 ? this.GetFanucDouble(numArrayList1[0], 14 + 8 * index) : double.NaN;
      cutterInfoArray[index].LengthWearOffset = flag2 ? this.GetFanucDouble(numArrayList2[0], 14 + 8 * index) : double.NaN;
      cutterInfoArray[index].RadiusSharpOffset = flag3 ? this.GetFanucDouble(numArrayList3[0], 14 + 8 * index) : double.NaN;
      cutterInfoArray[index].RadiusWearOffset = flag4 ? this.GetFanucDouble(numArrayList4[0], 14 + 8 * index) : double.NaN;
    }
    return OperateResult.CreateSuccessResult<CutterInfo[]>(cutterInfoArray);
  }

  private OperateResult CheckSingleResultLeagle(byte[] result)
  {
    int err = (int) result[6] * 256 /*0x0100*/ + (int) result[7];
    return err != 0 ? (OperateResult) new OperateResult<int>(err, StringResources.Language.UnknownError) : OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  public override string ToString() => $"FanucSeries0i[{this.IpAddress}:{this.Port}]";
}
