﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.OpenProtocol.ParameterSetMessages
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Collections.Generic;

#nullable disable
namespace HslCommunication.Profinet.OpenProtocol;

/// <summary>参数设置相关的类</summary>
public class ParameterSetMessages
{
  private OpenProtocolNet openProtocol;

  /// <summary>指定Open通信类实例化一个对象</summary>
  /// <param name="openProtocol">开放协议的对象</param>
  public ParameterSetMessages(OpenProtocolNet openProtocol) => this.openProtocol = openProtocol;

  /// <summary>
  /// A request to get the valid parameter set IDs from the controller.
  /// </summary>
  /// <returns>IDs</returns>
  public OperateResult<int[]> ParameterSetIDUpload()
  {
    OperateResult<string> result = this.openProtocol.ReadCustomer(10, 1, -1, -1, (List<string>) null);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<int[]>((OperateResult) result) : this.PraseMID0011(result.Content);
  }

  /// <summary>
  /// Request to upload parameter set data from the controller.
  /// </summary>
  /// <param name="id">parameter set id</param>
  /// <returns>parameter set data</returns>
  public OperateResult<ParameterSetData> ParameterSetDataUpload(int id)
  {
    OperateResult<string> result = this.openProtocol.ReadCustomer(12, 1, -1, -1, new List<string>()
    {
      id.ToString("D3")
    });
    return !result.IsSuccess ? OperateResult.CreateFailedResult<ParameterSetData>((OperateResult) result) : this.PraseMID0012(result.Content);
  }

  /// <summary>
  /// A subscription for the parameter set selection. Each time a new parameter set is selected the MID 0015 Parameter set selected is sent to the integrator.
  /// </summary>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult ParameterSetSelectedSubscribe()
  {
    return (OperateResult) this.openProtocol.ReadCustomer(14, 1, -1, -1, (List<string>) null);
  }

  /// <summary>
  /// Reset the subscription for the parameter set selection.
  /// </summary>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult ParameterSetSelectedUnsubscribe()
  {
    return (OperateResult) this.openProtocol.ReadCustomer(17, 1, -1, -1, (List<string>) null);
  }

  /// <summary>Select a parameter set.</summary>
  /// <param name="id">id</param>
  /// <returns>是否选择成功的结果对象</returns>
  public OperateResult SelectParameterSet(int id)
  {
    return (OperateResult) this.openProtocol.ReadCustomer(18, 1, -1, -1, new List<string>()
    {
      id.ToString("D3")
    });
  }

  /// <summary>
  /// This message gives the possibility to set the batch size of a parameter set at run time.
  /// </summary>
  /// <param name="id">Parameter set ID</param>
  /// <param name="batchSize">Batch size</param>
  /// <returns>是否成功的结果对象</returns>
  public OperateResult SetParameterSetBatchSize(int id, int batchSize)
  {
    return (OperateResult) this.openProtocol.ReadCustomer(19, 1, -1, -1, new List<string>()
    {
      id.ToString("D3"),
      batchSize.ToString("D2")
    });
  }

  /// <summary>
  /// This message gives the possibility to reset the batch counter of the running parameter set, at run time.
  /// </summary>
  /// <param name="id">Parameter set ID</param>
  /// <returns>是否操作成功的结果对象</returns>
  public OperateResult ResetParameterSetBatchCounter(int id)
  {
    return (OperateResult) this.openProtocol.ReadCustomer(20, 1, -1, -1, new List<string>()
    {
      id.ToString("D3")
    });
  }

  private OperateResult<int[]> PraseMID0011(string reply)
  {
    try
    {
      int int32 = Convert.ToInt32(reply.Substring(20, 3));
      int[] numArray = new int[int32];
      for (int index = 0; index < int32; ++index)
        numArray[index] = Convert.ToInt32(reply.Substring(23 + index * 3, 3));
      return OperateResult.CreateSuccessResult<int[]>(numArray);
    }
    catch (Exception ex)
    {
      return new OperateResult<int[]>($"MID0011 prase failed: {ex.Message}{Environment.NewLine}Source: {reply}");
    }
  }

  private OperateResult<ParameterSetData> PraseMID0012(string reply)
  {
    try
    {
      return OperateResult.CreateSuccessResult<ParameterSetData>(new ParameterSetData()
      {
        ParameterSetID = Convert.ToInt32(reply.Substring(22, 3)),
        ParameterSetName = reply.Substring(27, 25).Trim(),
        RotationDirection = reply[54] == '1' ? "CW" : "CCW",
        BatchSize = Convert.ToInt32(reply.Substring(57, 2)),
        TorqueMin = Convert.ToDouble(reply.Substring(61, 6)) / 100.0,
        TorqueMax = Convert.ToDouble(reply.Substring(69, 6)) / 100.0,
        TorqueFinalTarget = Convert.ToDouble(reply.Substring(77, 6)) / 100.0,
        AngleMin = Convert.ToInt32(reply.Substring(85, 5)),
        AngleMax = Convert.ToInt32(reply.Substring(92, 5)),
        AngleFinalTarget = Convert.ToInt32(reply.Substring(99, 5))
      });
    }
    catch (Exception ex)
    {
      return new OperateResult<ParameterSetData>($"MID0013 prase failed: {ex.Message}{Environment.NewLine}Source: {reply}");
    }
  }
}
