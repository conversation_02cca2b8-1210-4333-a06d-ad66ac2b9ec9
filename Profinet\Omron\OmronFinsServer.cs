﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronFinsServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core;
using HslCommunication.Core.Address;
using HslCommunication.Core.Device;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using HslCommunication.Core.Pipe;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// 欧姆龙的虚拟服务器，支持DM区，CIO区，Work区，Hold区，Auxiliary区，可以方便的进行测试<br />
/// Omron's virtual server supports DM area, CIO area, Work area, Hold area, and Auxiliary area, which can be easily tested
/// </summary>
public class OmronFinsServer : DeviceServer
{
  protected SoftBuffer dBuffer;
  protected SoftBuffer cioBuffer;
  protected SoftBuffer wBuffer;
  protected SoftBuffer hBuffer;
  protected SoftBuffer arBuffer;
  protected SoftBuffer emBuffer;
  protected SoftBuffer cfBuffer;
  protected SoftBuffer irBuffer;
  protected SoftBuffer drBuffer;
  protected bool connectionInitialization = true;
  private const int DataPoolLength = 65536 /*0x010000*/;

  /// <summary>
  /// 实例化一个Fins协议的服务器<br />
  /// Instantiate a Fins protocol server
  /// </summary>
  public OmronFinsServer()
  {
    this.dBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.cioBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.wBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.hBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.arBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.emBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.cfBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.irBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.drBuffer = new SoftBuffer(131072 /*0x020000*/);
    this.dBuffer.IsBoolReverseByWord = true;
    this.cioBuffer.IsBoolReverseByWord = true;
    this.wBuffer.IsBoolReverseByWord = true;
    this.hBuffer.IsBoolReverseByWord = true;
    this.arBuffer.IsBoolReverseByWord = true;
    this.emBuffer.IsBoolReverseByWord = true;
    this.cfBuffer.IsBoolReverseByWord = true;
    this.irBuffer.IsBoolReverseByWord = true;
    this.drBuffer.IsBoolReverseByWord = true;
    this.WordLength = (ushort) 1;
    this.ByteTransform = (IByteTransform) new RegularByteTransform(DataFormat.CDAB);
  }

  /// <inheritdoc cref="P:HslCommunication.Core.IByteTransform.DataFormat" />
  public DataFormat DataFormat
  {
    get => this.ByteTransform.DataFormat;
    set => this.ByteTransform.DataFormat = value;
  }

  private OperateResult<SoftBuffer, OmronFinsAddress> GetWordAddressBuffer(string address)
  {
    OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<SoftBuffer, OmronFinsAddress>((OperateResult) from);
    if ((int) from.Content.WordCode == (int) OmronFinsDataType.DM.WordCode)
      return OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.dBuffer, from.Content);
    if ((int) from.Content.WordCode == (int) OmronFinsDataType.CIO.WordCode)
      return OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.cioBuffer, from.Content);
    if ((int) from.Content.WordCode == (int) OmronFinsDataType.WR.WordCode)
      return OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.wBuffer, from.Content);
    if ((int) from.Content.WordCode == (int) OmronFinsDataType.HR.WordCode)
      return OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.hBuffer, from.Content);
    if ((int) from.Content.WordCode == (int) OmronFinsDataType.AR.WordCode)
      return OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.arBuffer, from.Content);
    if (from.Content.WordCode == (byte) 188)
      return OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.drBuffer, from.Content);
    if (from.Content.WordCode == (byte) 220)
      return OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.irBuffer, from.Content);
    return address.StartsWith("E", StringComparison.OrdinalIgnoreCase) ? OperateResult.CreateSuccessResult<SoftBuffer, OmronFinsAddress>(this.emBuffer, from.Content) : new OperateResult<SoftBuffer, OmronFinsAddress>(StringResources.Language.NotSupportedDataType);
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Read(System.String,System.UInt16)" />
  [HslMqttApi("ReadByteArray", "")]
  public override OperateResult<byte[]> Read(string address, ushort length)
  {
    OperateResult<SoftBuffer, OmronFinsAddress> wordAddressBuffer = this.GetWordAddressBuffer(address);
    return !wordAddressBuffer.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) wordAddressBuffer) : OperateResult.CreateSuccessResult<byte[]>(wordAddressBuffer.Content1.GetBytes(wordAddressBuffer.Content2.AddressStart / 16 /*0x10*/ * 2, (int) length * 2));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Byte[])" />
  [HslMqttApi("WriteByteArray", "")]
  public override OperateResult Write(string address, byte[] value)
  {
    OperateResult<SoftBuffer, OmronFinsAddress> wordAddressBuffer = this.GetWordAddressBuffer(address);
    if (!wordAddressBuffer.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<byte[]>((OperateResult) wordAddressBuffer);
    wordAddressBuffer.Content1.SetBytes(value, wordAddressBuffer.Content2.AddressStart / 16 /*0x10*/ * 2);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.ReadBool(System.String,System.UInt16)" />
  [HslMqttApi("ReadBoolArray", "")]
  public override OperateResult<bool[]> ReadBool(string address, ushort length)
  {
    OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    if ((int) from.Content.BitCode == (int) OmronFinsDataType.DM.BitCode)
      return OperateResult.CreateSuccessResult<bool[]>(this.dBuffer.GetBool(from.Content.AddressStart, (int) length));
    if ((int) from.Content.BitCode == (int) OmronFinsDataType.CIO.BitCode)
      return OperateResult.CreateSuccessResult<bool[]>(this.cioBuffer.GetBool(from.Content.AddressStart, (int) length));
    if ((int) from.Content.BitCode == (int) OmronFinsDataType.WR.BitCode)
      return OperateResult.CreateSuccessResult<bool[]>(this.wBuffer.GetBool(from.Content.AddressStart, (int) length));
    if ((int) from.Content.BitCode == (int) OmronFinsDataType.HR.BitCode)
      return OperateResult.CreateSuccessResult<bool[]>(this.hBuffer.GetBool(from.Content.AddressStart, (int) length));
    if ((int) from.Content.BitCode == (int) OmronFinsDataType.AR.BitCode)
      return OperateResult.CreateSuccessResult<bool[]>(this.arBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.BitCode == (byte) 7)
      return OperateResult.CreateSuccessResult<bool[]>(this.cfBuffer.GetBool(from.Content.AddressStart, (int) length));
    if (from.Content.WordCode == (byte) 188)
      return OperateResult.CreateSuccessResult<bool[]>(this.drBuffer.GetBool(from.Content.AddressStart, (int) length));
    return from.Content.WordCode == (byte) 220 ? OperateResult.CreateSuccessResult<bool[]>(this.irBuffer.GetBool(from.Content.AddressStart, (int) length)) : OperateResult.CreateSuccessResult<bool[]>(this.emBuffer.GetBool(from.Content.AddressStart, (int) length));
  }

  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsNet.Write(System.String,System.Boolean[])" />
  [HslMqttApi("WriteBoolArray", "")]
  public override OperateResult Write(string address, bool[] value)
  {
    OperateResult<OmronFinsAddress> from = OmronFinsAddress.ParseFrom(address);
    if (!from.IsSuccess)
      return (OperateResult) OperateResult.CreateFailedResult<bool[]>((OperateResult) from);
    if ((int) from.Content.BitCode == (int) OmronFinsDataType.DM.BitCode)
      this.dBuffer.SetBool(value, from.Content.AddressStart);
    else if ((int) from.Content.BitCode == (int) OmronFinsDataType.CIO.BitCode)
      this.cioBuffer.SetBool(value, from.Content.AddressStart);
    else if ((int) from.Content.BitCode == (int) OmronFinsDataType.WR.BitCode)
      this.wBuffer.SetBool(value, from.Content.AddressStart);
    else if ((int) from.Content.BitCode == (int) OmronFinsDataType.HR.BitCode)
      this.hBuffer.SetBool(value, from.Content.AddressStart);
    else if ((int) from.Content.BitCode == (int) OmronFinsDataType.AR.BitCode)
      this.arBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.BitCode == (byte) 7)
      this.cfBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.WordCode == (byte) 188)
      this.drBuffer.SetBool(value, from.Content.AddressStart);
    else if (from.Content.WordCode == (byte) 220)
      this.irBuffer.SetBool(value, from.Content.AddressStart);
    else
      this.emBuffer.SetBool(value, from.Content.AddressStart);
    return OperateResult.CreateSuccessResult();
  }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage() => (INetMessage) new FinsMessage();

  /// <inheritdoc />
  protected override OperateResult ThreadPoolLoginAfterClientCheck(
    PipeSession session,
    IPEndPoint endPoint)
  {
    if (this.connectionInitialization)
    {
      CommunicationPipe communication = session.Communication;
      OperateResult<byte[]> message = communication.ReceiveMessage(this.GetNewNetMessage(), (byte[]) null, false, logMessage: (Action<byte[]>) (m => this.LogRevcMessage(m, session)));
      if (!message.IsSuccess)
        return (OperateResult) message;
      byte[] bytes = SoftBasic.HexStringToBytes("46 49 4E 53 00 00 00 10 00 00 00 01 00 00 00 00 00 00 00 01 00 00 00 02");
      this.LogSendMessage(bytes, session);
      OperateResult operateResult = communication.Send(bytes);
      if (!operateResult.IsSuccess)
        return operateResult;
    }
    return base.ThreadPoolLoginAfterClientCheck(session, endPoint);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    byte[] numArray = this.ReadFromFinsCore(receive.RemoveBegin<byte>(26));
    if (receive != null && receive.Length > 25)
    {
      numArray[20] = receive[23];
      numArray[23] = receive[20];
      numArray[25] = receive[25];
    }
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <summary>
  /// 当收到mc协议的报文的时候应该触发的方法，允许继承重写，来实现自定义的返回，或是数据监听。<br />
  /// The method that should be triggered when a message of the mc protocol is received is allowed to be inherited and rewritten to achieve a custom return or data monitoring.
  /// </summary>
  /// <param name="finsCore">mc报文</param>
  /// <returns>返回的报文信息</returns>
  protected virtual byte[] ReadFromFinsCore(byte[] finsCore)
  {
    if (finsCore.Length == 0)
      return (byte[]) null;
    if (finsCore[0] == (byte) 1 && finsCore[1] == (byte) 1)
    {
      byte[] data = this.ReadByCommand(finsCore);
      return this.PackCommand(data == null ? 2 : 0, finsCore, data);
    }
    if (finsCore[0] == (byte) 1 && finsCore[1] == (byte) 2)
      return !this.EnableWrite ? this.PackCommand(3, finsCore, (byte[]) null) : this.PackCommand(0, finsCore, this.WriteByMessage(finsCore));
    if (finsCore[0] == (byte) 1 && finsCore[1] == (byte) 4)
    {
      byte[] data = this.ReadByMultiCommand(finsCore);
      return this.PackCommand(data == null ? 2 : 0, finsCore, data);
    }
    if (finsCore[0] == (byte) 4 && finsCore[1] == (byte) 1)
      return this.PackCommand(0, finsCore, (byte[]) null);
    if (finsCore[0] == (byte) 4 && finsCore[1] == (byte) 2)
      return this.PackCommand(0, finsCore, (byte[]) null);
    if (finsCore[0] == (byte) 5 && finsCore[1] == (byte) 1)
      return this.PackCommand(0, finsCore, "43 4A 32 4D 2D 43 50 55 33 31 20 20 20 20 20 20 20 20 20 20 30 32 2E 30 31 00 00 00 00 00 30 32 2E 31 30 00 00 00 00 00 00 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 01 00 00 80 01 80 01 80 01 80 00 00 00 00 00 00 00 00 02 01 00 00 0A 17 80 00 08 01 00 00 00 00 00".ToHexBytes());
    if (finsCore[0] == (byte) 6 && finsCore[1] == (byte) 1)
      return this.PackCommand(0, finsCore, "05 02 00 00 00 00 00 00 00 00 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20".ToHexBytes());
    if (finsCore[0] != (byte) 7 || finsCore[1] != (byte) 1)
      return this.PackCommand(3, finsCore, (byte[]) null);
    byte[] finsCore1 = finsCore;
    DateTime now = DateTime.Now;
    string str1 = now.ToString("yy-MM-dd-HH-mm-ss").Replace("-", "");
    now = DateTime.Now;
    string str2 = ((int) now.DayOfWeek).ToString("D2");
    byte[] hexBytes = (str1 + str2).ToHexBytes();
    return this.PackCommand(0, finsCore1, hexBytes);
  }

  /// <summary>
  /// 将核心报文打包的方法，追加报文头<br />
  /// The method of packing the core message, adding the message header
  /// </summary>
  /// <param name="status">错误码</param>
  /// <param name="finsCore">Fins的核心报文</param>
  /// <param name="data">核心的内容</param>
  /// <returns>完整的报文信息</returns>
  protected virtual byte[] PackCommand(int status, byte[] finsCore, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    byte[] numArray = new byte[30 + data.Length];
    SoftBasic.HexStringToBytes("46 49 4E 53 00 00 00 0000 00 00 02 00 00 00 00C0 00 02 00 EF 00 00 33 00 00 00 00 00 00").CopyTo((Array) numArray, 0);
    if (data.Length != 0)
      data.CopyTo((Array) numArray, 30);
    numArray[26] = finsCore[0];
    numArray[27] = finsCore[1];
    BitConverter.GetBytes(numArray.Length - 8).ReverseNew<byte>().CopyTo((Array) numArray, 4);
    BitConverter.GetBytes(status).ReverseNew<byte>().CopyTo((Array) numArray, 12);
    return numArray;
  }

  private SoftBuffer GetSoftBuffer(byte code, int startIndex)
  {
    if ((int) code == (int) OmronFinsDataType.DM.BitCode || (int) code == (int) OmronFinsDataType.DM.WordCode)
      return this.dBuffer;
    if ((int) code == (int) OmronFinsDataType.CIO.BitCode || (int) code == (int) OmronFinsDataType.CIO.WordCode)
      return this.cioBuffer;
    if ((int) code == (int) OmronFinsDataType.WR.BitCode || (int) code == (int) OmronFinsDataType.WR.WordCode)
      return this.wBuffer;
    if ((int) code == (int) OmronFinsDataType.HR.BitCode || (int) code == (int) OmronFinsDataType.HR.WordCode)
      return this.hBuffer;
    if ((int) code == (int) OmronFinsDataType.AR.BitCode || (int) code == (int) OmronFinsDataType.AR.WordCode)
      return this.arBuffer;
    switch (code)
    {
      case 0:
        return startIndex >= 45056 /*0xB000*/ ? this.arBuffer : this.cioBuffer;
      case 7:
        return this.cfBuffer;
      case 128 /*0x80*/:
        return startIndex >= 45056 /*0xB000*/ ? this.arBuffer : this.cioBuffer;
      case 188:
        return this.drBuffer;
      case 220:
        return this.irBuffer;
      default:
        if ((byte) 32 /*0x20*/ <= code && code < (byte) 48 /*0x30*/ || (byte) 208 /*0xD0*/ <= code && code < (byte) 224 /*0xE0*/ || (byte) 160 /*0xA0*/ <= code && code < (byte) 176 /*0xB0*/ || (byte) 80 /*0x50*/ <= code && code < (byte) 96 /*0x60*/ || (byte) 144 /*0x90*/ <= code && code < (byte) 153 || code == (byte) 10)
          return this.emBuffer;
        throw new Exception(StringResources.Language.NotSupportedDataType);
    }
  }

  private byte[] ReadByCommand(byte[] command)
  {
    if ((int) command[2] == (int) OmronFinsDataType.DM.BitCode || (int) command[2] == (int) OmronFinsDataType.CIO.BitCode || (int) command[2] == (int) OmronFinsDataType.WR.BitCode || (int) command[2] == (int) OmronFinsDataType.HR.BitCode || (int) command[2] == (int) OmronFinsDataType.AR.BitCode || command[2] == (byte) 0 || command[2] == (byte) 7 || command[2] == (byte) 10 || (byte) 32 /*0x20*/ <= command[2] && command[2] < (byte) 48 /*0x30*/ || (byte) 208 /*0xD0*/ <= command[2] && command[2] < (byte) 224 /*0xE0*/)
    {
      ushort length = (ushort) ((uint) command[6] * 256U /*0x0100*/ + (uint) command[7]);
      int num = ((int) command[3] * 256 /*0x0100*/ + (int) command[4]) * 16 /*0x10*/ + (int) command[5];
      SoftBuffer softBuffer = this.GetSoftBuffer(command[2], num);
      if ((command[2] == (byte) 0 || command[2] == (byte) 128 /*0x80*/) && num >= 45056 /*0xB000*/)
        num -= 45056 /*0xB000*/;
      return ((IEnumerable<bool>) softBuffer.GetBool(num, (int) length)).Select<bool, byte>((Func<bool, byte>) (m => !m ? (byte) 0 : (byte) 1)).ToArray<byte>();
    }
    if ((int) command[2] != (int) OmronFinsDataType.DM.WordCode && (int) command[2] != (int) OmronFinsDataType.CIO.WordCode && (int) command[2] != (int) OmronFinsDataType.WR.WordCode && (int) command[2] != (int) OmronFinsDataType.HR.WordCode && (int) command[2] != (int) OmronFinsDataType.AR.WordCode && command[2] != (byte) 128 /*0x80*/ && command[2] != (byte) 188 && command[2] != (byte) 220 && command[2] != (byte) 152 && ((byte) 160 /*0xA0*/ > command[2] || command[2] >= (byte) 176 /*0xB0*/) && ((byte) 80 /*0x50*/ > command[2] || command[2] >= (byte) 96 /*0x60*/))
      return new byte[0];
    ushort num1 = (ushort) ((uint) command[6] * 256U /*0x0100*/ + (uint) command[7]);
    int startIndex = (int) command[3] * 256 /*0x0100*/ + (int) command[4];
    if (num1 > (ushort) 999)
      return (byte[]) null;
    SoftBuffer softBuffer1 = this.GetSoftBuffer(command[2], startIndex);
    if ((command[2] == (byte) 0 || command[2] == (byte) 128 /*0x80*/) && startIndex >= 45056 /*0xB000*/)
      startIndex -= 45056 /*0xB000*/;
    return softBuffer1.GetBytes(startIndex * 2, (int) num1 * 2);
  }

  private byte[] ReadByMultiCommand(byte[] command)
  {
    MemoryStream ms = new MemoryStream();
    for (int index = 2; index < command.Length; index += 4)
    {
      int startIndex = (int) command[index + 1] * 256 /*0x0100*/ + (int) command[index + 2];
      if ((int) command[index] == (int) OmronFinsDataType.DM.WordCode || (int) command[index] == (int) OmronFinsDataType.CIO.WordCode || (int) command[index] == (int) OmronFinsDataType.WR.WordCode || (int) command[index] == (int) OmronFinsDataType.HR.WordCode || (int) command[index] == (int) OmronFinsDataType.AR.WordCode || command[index] == (byte) 128 /*0x80*/ || command[index] == (byte) 188 || command[index] == (byte) 220 || command[index] == (byte) 152 || (byte) 160 /*0xA0*/ <= command[index] && command[index] < (byte) 176 /*0xB0*/ || (byte) 80 /*0x50*/ <= command[index] && command[index] < (byte) 96 /*0x60*/)
      {
        ms.WriteByte(command[index]);
        SoftBuffer softBuffer = this.GetSoftBuffer(command[index], startIndex);
        if ((command[2] == (byte) 0 || command[2] == (byte) 128 /*0x80*/) && startIndex >= 45056 /*0xB000*/)
          startIndex -= 45056 /*0xB000*/;
        ms.Write(softBuffer.GetBytes(startIndex * 2, 2));
      }
    }
    return ms.ToArray();
  }

  private byte[] WriteByMessage(byte[] command)
  {
    if ((int) command[2] == (int) OmronFinsDataType.DM.BitCode || (int) command[2] == (int) OmronFinsDataType.CIO.BitCode || (int) command[2] == (int) OmronFinsDataType.WR.BitCode || (int) command[2] == (int) OmronFinsDataType.HR.BitCode || (int) command[2] == (int) OmronFinsDataType.AR.BitCode || command[2] == (byte) 0 || command[2] == (byte) 7 || (byte) 32 /*0x20*/ <= command[2] && command[2] < (byte) 48 /*0x30*/ || (byte) 208 /*0xD0*/ <= command[2] && command[2] < (byte) 224 /*0xE0*/)
    {
      ushort num1 = (ushort) ((uint) command[6] * 256U /*0x0100*/ + (uint) command[7]);
      int num2 = ((int) command[3] * 256 /*0x0100*/ + (int) command[4]) * 16 /*0x10*/ + (int) command[5];
      bool[] array = ((IEnumerable<byte>) SoftBasic.ArrayRemoveBegin<byte>(command, 8)).Select<byte, bool>((Func<byte, bool>) (m => m == (byte) 1)).ToArray<bool>();
      SoftBuffer softBuffer = this.GetSoftBuffer(command[2], num2);
      if ((command[2] == (byte) 0 || command[2] == (byte) 128 /*0x80*/) && num2 >= 45056 /*0xB000*/)
        num2 -= 45056 /*0xB000*/;
      softBuffer.SetBool(array, num2);
      return new byte[0];
    }
    ushort num = (ushort) ((uint) command[6] * 256U /*0x0100*/ + (uint) command[7]);
    int startIndex = (int) command[3] * 256 /*0x0100*/ + (int) command[4];
    byte[] data = SoftBasic.ArrayRemoveBegin<byte>(command, 8);
    SoftBuffer softBuffer1 = this.GetSoftBuffer(command[2], startIndex);
    if ((command[2] == (byte) 0 || command[2] == (byte) 128 /*0x80*/) && startIndex >= 45056 /*0xB000*/)
      startIndex -= 45056 /*0xB000*/;
    softBuffer1.SetBytes(data, startIndex * 2);
    return new byte[0];
  }

  /// <inheritdoc />
  protected override void LoadFromBytes(byte[] content)
  {
    SoftBuffer.LoadFromBuffer(content, this.dBuffer, this.cioBuffer, this.wBuffer, this.hBuffer, this.arBuffer, this.emBuffer, this.cfBuffer, this.irBuffer, this.drBuffer);
  }

  /// <inheritdoc />
  protected override byte[] SaveToBytes()
  {
    return SoftBuffer.ToMemoryStream(this.dBuffer, this.cioBuffer, this.wBuffer, this.hBuffer, this.arBuffer, this.emBuffer, this.cfBuffer, this.irBuffer, this.drBuffer).ToArray();
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  protected override void Dispose(bool disposing)
  {
    if (disposing)
    {
      this.dBuffer?.Dispose();
      this.cioBuffer?.Dispose();
      this.wBuffer?.Dispose();
      this.hBuffer?.Dispose();
      this.arBuffer?.Dispose();
      this.emBuffer?.Dispose();
      this.cfBuffer?.Dispose();
    }
    base.Dispose(disposing);
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronFinsServer[{this.Port}]";
}
