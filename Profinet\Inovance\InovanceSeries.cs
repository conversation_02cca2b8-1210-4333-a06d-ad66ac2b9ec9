﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Inovance.InovanceSeries
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Inovance;

/// <summary>汇川的系列枚举信息</summary>
public enum InovanceSeries
{
  /// <summary>适用于AM400、 AM400_800、 AC800 等系列</summary>
  AM,
  /// <summary>适用于H3U, XP 等系列</summary>
  H3U,
  /// <summary>适用于H5U 系列</summary>
  H5U,
  /// <summary>是用于Easy系列的PLC，适用于Easy系列的PLC通信</summary>
  Easy,
}
