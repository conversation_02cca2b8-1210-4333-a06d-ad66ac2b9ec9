﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Geniitek.VibrationSensorActualValue
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

#nullable disable
namespace HslCommunication.Profinet.Geniitek;

/// <summary>振动传感器的加速度值</summary>
public struct VibrationSensorActualValue
{
  /// <summary>X轴的实时加速度</summary>
  public float AcceleratedSpeedX { get; set; }

  /// <summary>Y轴的实时加速度</summary>
  public float AcceleratedSpeedY { get; set; }

  /// <summary>Z轴的实时加速度</summary>
  public float AcceleratedSpeedZ { get; set; }

  /// <inheritdoc />
  public override string ToString()
  {
    return $"ActualValue[{this.AcceleratedSpeedX},{this.AcceleratedSpeedY},{this.AcceleratedSpeedZ}]";
  }
}
