﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Pipe.CommunicationPipe
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Reflection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

#nullable disable
namespace HslCommunication.Core.Pipe;

/// <summary>
/// 用于通信的管道信息，包含基本的通信实现，当前类是抽象类，需要继承实现具体的收发报文才可以使用。<br />
/// Channel information for communication, including the basic communication implementation, the current class is an abstract class, need to inherit the implementation of specific messages can be used.
/// </summary>
public abstract class CommunicationPipe : IDisposable
{
  private bool disposedValue;
  private int receiveTimeOut = 5000;
  private int sleepTime = 0;
  private bool useServerActivePush = false;
  private int connectErrorCount = 0;
  private ICommunicationLock communicationLock;
  /// <summary>当启用设备方主动发送数据时，用于同步访问方法的信号同步功能</summary>
  protected AutoResetEvent autoResetEvent;
  /// <summary>当启用设备方主动发送数据时，用于应答服务机制的数据缓存</summary>
  protected byte[] bufferQA = (byte[]) null;
  /// <summary>
  /// 是否是长连接的状态<br />
  /// Whether it is a long connection state
  /// </summary>
  protected bool isPersistentConn = false;

  /// <summary>
  /// 实例化一个默认的构造对象<br />
  /// Instantiate a default constructor object
  /// </summary>
  public CommunicationPipe()
  {
    this.communicationLock = (ICommunicationLock) new CommunicationLockSimple();
  }

  /// <summary>
  /// 获取或设置接收服务器反馈的时间，如果为负数，则不接收反馈 <br />
  /// Gets or sets the time to receive server feedback, and if it is a negative number, does not receive feedback
  /// </summary>
  /// <example>
  /// 设置1秒的接收超时的示例
  /// <code lang="cs" source="HslCommunication_Net45.Test\Documentation\Samples\Core\NetworkDoubleBase.cs" region="ReceiveTimeOutExample" title="ReceiveTimeOut示例" />
  /// </example>
  /// <remarks>超时的通常原因是服务器端没有配置好，导致访问失败，为了不卡死软件，所以有了这个超时的属性。</remarks>
  public int ReceiveTimeOut
  {
    get => this.receiveTimeOut;
    set => this.receiveTimeOut = value;
  }

  /// <summary>
  /// 获取或设置在正式接收对方返回数据前的时候，需要休息的时间，当设置为0的时候，不需要休息。<br />
  /// Get or set the time required to rest before officially receiving the data from the other party. When it is set to 0, no rest is required.
  /// </summary>
  [HslMqttApi(HttpMethod = "GET", Description = "Get or set the time required to rest before officially receiving the data from the other party. When it is set to 0, no rest is required.")]
  public int SleepTime
  {
    get => this.sleepTime;
    set => this.sleepTime = value;
  }

  /// <summary>
  /// 获取或设置当前的管道是否激活从设备主动推送的功能，设置为 true 时支持主动从设备方接收数据信息<br />
  /// Gets or sets whether the current pipeline activates the function of actively pushing data from the device. If this is set to true, it supports actively receiving data information from the device
  /// </summary>
  public bool UseServerActivePush
  {
    get => this.useServerActivePush;
    set
    {
      if (value)
      {
        if (this.autoResetEvent == null)
          this.autoResetEvent = new AutoResetEvent(false);
        this.isPersistentConn = true;
      }
      this.useServerActivePush = value;
    }
  }

  /// <summary>
  /// 获取或设置当前管道的线程锁对象，默认是简单的一个互斥锁<br />
  /// Gets or sets the thread lock object of the current pipeline, which defaults to a simple mutex
  /// </summary>
  public ICommunicationLock CommunicationLock
  {
    get => this.communicationLock;
    set => this.communicationLock = value;
  }

  /// <summary>
  /// 获取或设置当前的管道是否是长连接，仅对于串口及TCP是有效的，默认都是长连接<br />
  /// Gets or sets whether the current pipe is a long connection. This is valid only for serial ports and TCP. The default is a long connection
  /// </summary>
  public bool IsPersistentConnection { get; set; } = true;

  /// <summary>
  /// 根据给定的消息，发送的数据，接收到数据来判断是否接收完成报文<br />
  /// According to the given message, sent data, received data to determine whether to receive the completed message
  /// </summary>
  /// <param name="netMessage">消息类对象</param>
  /// <param name="sendValue">发送的数据内容</param>
  /// <param name="ms">接收数据的流</param>
  /// <returns>是否接收完成数据</returns>
  protected bool CheckMessageComplete(
    INetMessage netMessage,
    byte[] sendValue,
    ref MemoryStream ms)
  {
    if (netMessage == null)
      return true;
    if (netMessage is SpecifiedCharacterMessage characterMessage)
    {
      byte[] array = ms.ToArray();
      byte[] bytes = BitConverter.GetBytes(characterMessage.ProtocolHeadBytesLength);
      switch ((int) bytes[3] & 15)
      {
        case 1:
          if (array.Length > (int) characterMessage.EndLength && (int) array[array.Length - 1 - (int) characterMessage.EndLength] == (int) bytes[1])
            return true;
          break;
        case 2:
          if (array.Length > (int) characterMessage.EndLength + 1 && (int) array[array.Length - 2 - (int) characterMessage.EndLength] == (int) bytes[1] && (int) array[array.Length - 1 - (int) characterMessage.EndLength] == (int) bytes[0])
            return true;
          break;
      }
    }
    else if (netMessage.ProtocolHeadBytesLength > 0)
    {
      byte[] numArray = ms.ToArray();
      if (numArray.Length >= netMessage.ProtocolHeadBytesLength)
      {
        int length = netMessage.PependedUselesByteLength(numArray);
        if (length > 0)
        {
          numArray = numArray.RemoveBegin<byte>(length);
          ms = new MemoryStream();
          ms.Write(numArray);
          if (numArray.Length < netMessage.ProtocolHeadBytesLength)
            return false;
        }
        netMessage.HeadBytes = numArray.SelectBegin<byte>(netMessage.ProtocolHeadBytesLength);
        netMessage.SendBytes = sendValue;
        int lengthByHeadBytes = netMessage.GetContentLengthByHeadBytes();
        if (numArray.Length >= netMessage.ProtocolHeadBytesLength + lengthByHeadBytes)
        {
          if (netMessage.ProtocolHeadBytesLength > netMessage.HeadBytes.Length)
          {
            ms = new MemoryStream();
            ms.Write(numArray.RemoveBegin<byte>(netMessage.ProtocolHeadBytesLength - netMessage.HeadBytes.Length));
          }
          return true;
        }
      }
    }
    else if (netMessage.CheckReceiveDataComplete(sendValue, ms))
      return true;
    return false;
  }

  /// <summary>
  /// 重置当前的连续错误计数为0，并且返回重置前时候的值<br />
  /// Resets the current consecutive error count to 0 and returns the value before the reset
  /// </summary>
  /// <returns>重置前的值</returns>
  public int ResetConnectErrorCount() => Interlocked.Exchange(ref this.connectErrorCount, 0);

  /// <summary>
  /// 自增当前的连续错误计数，并且获取自增后的值信息，最大到10亿为止，无法继续增加了。<br />
  /// Increment the current continuous error count, and obtain the value information after increment, up to 1 billion, can not continue to increase.
  /// </summary>
  /// <returns>自增后的值信息</returns>
  protected int IncrConnectErrorCount()
  {
    int num = Interlocked.Increment(ref this.connectErrorCount);
    if (num > 1000000000)
      Interlocked.Exchange(ref this.connectErrorCount, 1000000000);
    return num;
  }

  /// <summary>
  /// 主动引发一个管道错误，从而让管道可以重新打开<br />
  /// Actively causes a pipe error so that the pipe can be reopened
  /// </summary>
  public void RaisePipeError() => Interlocked.CompareExchange(ref this.connectErrorCount, 1, 0);

  /// <summary>
  /// 当前管道的缓存里是否有数据存在，有就返回 <c>True</c><br />
  /// If there is any data in the cache of the current pipeline, return <c>True</c>
  /// </summary>
  /// <returns>是否有缓存数据</returns>
  public virtual bool HasCacheData() => false;

  /// <summary>
  /// 当前的管道连接对象是否发生了错误，这里的错误通常是串口，或是网络错误<br />
  /// Whether there is an error in the current pipe connection object, where the error is usually a serial port, or a network error
  /// </summary>
  /// <returns>是否发生了通道的异常</returns>
  public virtual bool IsConnectError() => this.connectErrorCount > 0;

  /// <summary>
  /// 发送数据到当前的管道中去<br />
  /// Send data to the current pipe
  /// </summary>
  /// <param name="data">等待发送的数据</param>
  /// <returns>是否发送成功</returns>
  public OperateResult Send(byte[] data)
  {
    return data == null ? OperateResult.CreateSuccessResult() : this.Send(data, 0, data.Length);
  }

  /// <summary>
  /// 将一个数据缓存中的指定的部分字段，发送到当前的管道中去<br />
  /// Sends the specified partial field from a data cache to the current pipeline
  /// </summary>
  /// <param name="data">等待发送的缓存数据</param>
  /// <param name="offset">起始偏移的地址</param>
  /// <param name="size">发送的字节长度信息</param>
  /// <returns>是否发送成功</returns>
  public virtual OperateResult Send(byte[] data, int offset, int size)
  {
    return (OperateResult) new OperateResult<int>(StringResources.Language.NotSupportedFunction);
  }

  /// <summary>
  /// 从管道里，接收指定长度的报文数据信息，如果长度指定为-1，表示接收不超过2048字节的动态长度。另外可以指定超时时间，进度报告等<br />
  /// Receives the packet data of a specified length from the pipe. If the length is set to -1,
  /// it indicates that the dynamic length of the packet is not more than 2048 bytes. You can also specify timeouts, progress reports, etc
  /// </summary>
  /// <param name="length">接收的长度信息</param>
  /// <param name="timeOut">指定的超时时间</param>
  /// <param name="reportProgress">进行进度报告的委托</param>
  /// <returns>是否接收成功的结果对象</returns>
  public virtual OperateResult<byte[]> Receive(
    int length,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!Authorization.nzugaydgwadawdibbas())
      return new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    OperateResult<byte[]> receiveBuffer = NetSupport.CreateReceiveBuffer(length);
    if (!receiveBuffer.IsSuccess)
      return receiveBuffer;
    OperateResult<int> result = this.Receive(receiveBuffer.Content, 0, length, timeOut, reportProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>((OperateResult) result) : OperateResult.CreateSuccessResult<byte[]>(length > 0 ? receiveBuffer.Content : receiveBuffer.Content.SelectBegin<byte>(result.Content));
  }

  /// <summary>
  /// 接收固定长度的字节数组，允许指定超时时间，默认为60秒，当length大于0时，接收固定长度的数据内容，当length小于0时，buffer长度的缓存数据<br />
  /// Receiving a fixed-length byte array, allowing a specified timeout time. The default is 60 seconds. When length is greater than 0,
  /// fixed-length data content is received. When length is less than 0, random data information of a length not greater than 2048 is received.
  /// </summary>
  /// <param name="buffer">等待接收的数据缓存信息</param>
  /// <param name="offset">开始接收数据的偏移地址</param>
  /// <param name="length">准备接收的数据长度，当length大于0时，接收固定长度的数据内容，当length小于0时，接收不大于2048长度的随机数据信息</param>
  /// <param name="timeOut">单位：毫秒，超时时间，默认为60秒，如果设置小于0，则不检查超时时间</param>
  /// <param name="reportProgress">进行进度报告的委托</param>
  /// <returns>包含了字节数据的结果类</returns>
  public virtual OperateResult<int> Receive(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    return new OperateResult<int>(StringResources.Language.NotSupportedFunction);
  }

  /// <summary>
  /// 开始后台接收相关的报文数据，当<see cref="P:HslCommunication.Core.Pipe.CommunicationPipe.UseServerActivePush" />为True时，则使用本方法
  /// </summary>
  public virtual OperateResult StartReceiveBackground(INetMessage netMessage)
  {
    return OperateResult.CreateSuccessResult();
  }

  /// <summary>
  /// 打开当前的管道信息，返回是否成功打开的结果对象，并通过属性 "Content" 指示当前是否为新创建的连接对象，如果是，则该值为 true<br />
  /// Opens the current pipe information, returns whether the result object was successfully opened,
  /// and indicates whether the current connection object is a newly created connection object through the property "Content", if so, the value is true
  /// </summary>
  /// <returns>是否打开成功的结果对象</returns>
  public virtual OperateResult<bool> OpenCommunication()
  {
    return new OperateResult<bool>(StringResources.Language.NotSupportedFunction);
  }

  /// <summary>
  /// 关闭当前的管道信息，返回是否关闭成功的结果对象<br />
  /// Close the current pipeline information and return the result object whether the closure was successful
  /// </summary>
  /// <returns>是否关闭成功</returns>
  public virtual OperateResult CloseCommunication()
  {
    return (OperateResult) new OperateResult<int>(StringResources.Language.NotSupportedFunction);
  }

  /// <summary>
  /// 设置当前的问答状态下的缓存数据<br />
  /// Set the cache data in the current Q&amp;A state
  /// </summary>
  /// <param name="buffer">设置的缓存</param>
  protected void SetBufferQA(byte[] buffer)
  {
    this.bufferQA = buffer;
    this.autoResetEvent.Set();
  }

  /// <summary>
  /// 用来决定当前接收的消息是否是问答服务的消息，可由外界设置委托来决定<br />
  /// It is used to determine whether the currently received message is a message of the question answering service, which can be determined by the external setting delegate
  /// </summary>
  public Func<CommunicationPipe, OperateResult<byte[]>, bool> DecideWhetherQAMessageFunction { get; set; }

  /// <summary>
  /// 包含了一个复杂的逻辑，从管道里根据当前的消息格式定义，接收报文信息，这个报文可能是来自服务器主动推送的。具体可以通过参数 <paramref name="useActivePush" /> 来特殊控制。<br />
  /// Contains a complex logic from the pipeline, according to the current message format definition, to receive message information,
  /// this message may be actively pushed from the server. The parameter <paramref name="useActivePush" /> can be used for special control.
  /// </summary>
  /// <param name="netMessage">消息对象</param>
  /// <param name="sendValue">发送的数据，大多数的情况，都可以为空</param>
  /// <param name="useActivePush">是否使用服务方主动推送的数据，默认为 true</param>
  /// <param name="reportProgress">进行进度报告的委托</param>
  /// <param name="logMessage">用于消息记录的日志信息</param>
  /// <returns>是否</returns>
  public virtual OperateResult<byte[]> ReceiveMessage(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    if (this.useServerActivePush & useActivePush)
    {
      if (this.autoResetEvent.WaitOne(this.ReceiveTimeOut))
      {
        if (netMessage != null)
          netMessage.HeadBytes = this.bufferQA;
        if (logMessage != null)
          logMessage(this.bufferQA);
        return OperateResult.CreateSuccessResult<byte[]>(this.bufferQA);
      }
      this.CloseCommunication();
      return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), StringResources.Language.ReceiveDataTimeout + this.ReceiveTimeOut.ToString());
    }
    if (netMessage == null || netMessage.ProtocolHeadBytesLength == -1)
    {
      if (netMessage != null && netMessage.SendBytes == null)
        netMessage.SendBytes = sendValue;
      DateTime now = DateTime.Now;
      MemoryStream ms = new MemoryStream();
      OperateResult<byte[]> byMessage;
      do
      {
        byMessage = this.ReceiveByMessage(this.ReceiveTimeOut, (INetMessage) null, reportProgress);
        if (byMessage.IsSuccess)
        {
          if (byMessage.Content != null && byMessage.Content.Length != 0)
          {
            ms.Write(byMessage.Content);
            if (logMessage != null)
              logMessage(byMessage.Content);
          }
          if (netMessage != null)
          {
            if (netMessage.CheckReceiveDataComplete(sendValue, ms))
              goto label_20;
          }
          else
            goto label_18;
        }
        else
          goto label_12;
      }
      while (this.ReceiveTimeOut < 0 || (DateTime.Now - now).TotalMilliseconds <= (double) this.ReceiveTimeOut);
      goto label_22;
label_12:
      return byMessage;
label_18:
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
label_20:
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
label_22:
      return new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataTimeout}{this.ReceiveTimeOut.ToString()} Received: {ms.ToArray().ToHexString(' ')}");
    }
    OperateResult<byte[]> byMessage1 = this.ReceiveByMessage(this.ReceiveTimeOut, netMessage, reportProgress);
    if (byMessage1.IsSuccess && logMessage != null)
      logMessage(byMessage1.Content);
    return byMessage1;
  }

  /// <summary>
  /// 将数据发送到当前的管道里，并从管道接收相关的数据信息，可以指定消息类型，发送数据，是否有数据响应，休眠时间<br />
  /// To send data to the current pipeline and receive relevant data information from the pipeline, you can specify the message type,
  /// the data sent, whether there is a data response, and the sleep time
  /// </summary>
  /// <param name="netMessage">当前接收的消息体信息</param>
  /// <param name="sendValue">等待发送的数据</param>
  /// <param name="hasResponseData">是否有数据返回</param>
  /// <param name="sleep">休眠时间</param>
  /// <param name="logMessage">用于消息记录的日志信息</param>
  /// <returns>读取的结果对象</returns>
  protected OperateResult<byte[]> ReadFromCoreServerHelper(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    int sleep,
    Action<byte[]> logMessage = null)
  {
    if (netMessage != null)
      netMessage.SendBytes = sendValue;
    OperateResult result = this.Send(sendValue);
    if (!result.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(result);
    if (this.ReceiveTimeOut < 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!hasResponseData)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (sleep > 0)
      HslHelper.ThreadSleep(sleep);
    DateTime now = DateTime.Now;
    int num = 0;
    OperateResult<byte[]> message;
    do
    {
      message = this.ReceiveMessage(netMessage, sendValue, logMessage: logMessage);
      if (message.IsSuccess)
      {
        if (netMessage != null)
        {
          switch (netMessage.CheckMessageMatch(sendValue, message.Content))
          {
            case 0:
              goto label_14;
            case 1:
              goto label_18;
            default:
              ++num;
              continue;
          }
        }
        else
          goto label_18;
      }
      else
        goto label_11;
    }
    while (this.ReceiveTimeOut < 0 || (DateTime.Now - now).TotalMilliseconds <= (double) this.ReceiveTimeOut);
    goto label_16;
label_11:
    return message;
label_14:
    return new OperateResult<byte[]>($"INetMessage.CheckMessageMatch failed{Environment.NewLine}{StringResources.Language.Send}: {SoftBasic.ByteToHexString(sendValue, ' ')}{Environment.NewLine}{StringResources.Language.Receive}: {SoftBasic.ByteToHexString(message.Content, ' ')}");
label_16:
    return new OperateResult<byte[]>($"Receive Message timeout: {this.ReceiveTimeOut.ToString()} CheckMessageMatch times:{num.ToString()}");
label_18:
    if (netMessage == null || netMessage.CheckHeadBytesLegal((byte[]) null))
      return OperateResult.CreateSuccessResult<byte[]>(message.Content);
    return new OperateResult<byte[]>($"{StringResources.Language.CommandHeadCodeCheckFailed}{Environment.NewLine}{StringResources.Language.Send}: {SoftBasic.ByteToHexString(sendValue, ' ')}{Environment.NewLine}{StringResources.Language.Receive}: {SoftBasic.ByteToHexString(message.Content, ' ')}");
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.ReadFromCoreServerHelper(HslCommunication.Core.IMessage.INetMessage,System.Byte[],System.Boolean,System.Int32,System.Action{System.Byte[]})" />
  public virtual OperateResult<byte[]> ReadFromCoreServer(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    OperateResult<byte[]> operateResult = this.ReadFromCoreServerHelper(netMessage, sendValue, hasResponseData, this.SleepTime, logMessage);
    if (!operateResult.IsSuccess)
    {
      if (operateResult.ErrorCode >= 0 || operateResult.ErrorCode == int.MinValue)
        ;
      return operateResult;
    }
    this.ResetConnectErrorCount();
    return operateResult;
  }

  /// <summary>
  /// 接收一条完整的 <seealso cref="T:HslCommunication.Core.IMessage.INetMessage" /> 数据内容，需要指定超时时间，单位为毫秒。 <br />
  /// Receive a complete <seealso cref="T:HslCommunication.Core.IMessage.INetMessage" /> data content, Need to specify a timeout period in milliseconds
  /// </summary>
  /// <param name="timeOut">超时时间，单位：毫秒</param>
  /// <param name="netMessage">消息的格式定义</param>
  /// <param name="reportProgress">接收消息的时候的进度报告</param>
  /// <returns>带有是否成功的byte数组对象</returns>
  private OperateResult<byte[]> ReceiveByMessage(
    int timeOut,
    INetMessage netMessage,
    Action<long, long> reportProgress = null)
  {
    if (netMessage == null)
      return this.Receive(-1, timeOut);
    if (netMessage.ProtocolHeadBytesLength < 0)
    {
      byte[] bytes = BitConverter.GetBytes(netMessage.ProtocolHeadBytesLength);
      int num = (int) bytes[3] & 15;
      OperateResult<byte[]> byMessage1 = (OperateResult<byte[]>) null;
      switch (num)
      {
        case 1:
          byMessage1 = this.ReceiveCommandLineFromPipe(bytes[1], timeOut);
          break;
        case 2:
          byMessage1 = this.ReceiveCommandLineFromPipe(bytes[1], bytes[0], timeOut);
          break;
      }
      if (byMessage1 == null)
        return new OperateResult<byte[]>("Receive by specified code failed, length check failed");
      if (!byMessage1.IsSuccess)
        return byMessage1;
      netMessage.HeadBytes = byMessage1.Content;
      if (!(netMessage is SpecifiedCharacterMessage characterMessage) || characterMessage.EndLength == (byte) 0)
        return byMessage1;
      OperateResult<byte[]> byMessage2 = this.Receive((int) characterMessage.EndLength, timeOut);
      if (!byMessage2.IsSuccess)
        return byMessage2;
      return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(byMessage1.Content, byMessage2.Content));
    }
    OperateResult<byte[]> byMessage3 = this.Receive(netMessage.ProtocolHeadBytesLength, timeOut);
    if (!byMessage3.IsSuccess)
      return byMessage3;
    int length = netMessage.PependedUselesByteLength(byMessage3.Content);
    int num1 = 0;
    while (length >= netMessage.ProtocolHeadBytesLength)
    {
      byMessage3 = this.Receive(netMessage.ProtocolHeadBytesLength, timeOut);
      if (!byMessage3.IsSuccess)
        return byMessage3;
      length = netMessage.PependedUselesByteLength(byMessage3.Content);
      ++num1;
      if (num1 > 10)
        break;
    }
    if (length > 0)
    {
      OperateResult<byte[]> byMessage4 = this.Receive(length, timeOut);
      if (!byMessage4.IsSuccess)
        return byMessage4;
      byMessage3.Content = SoftBasic.SpliceArray<byte>(byMessage3.Content.RemoveBegin<byte>(length), byMessage4.Content);
    }
    netMessage.HeadBytes = byMessage3.Content;
    int lengthByHeadBytes = netMessage.GetContentLengthByHeadBytes();
    if (lengthByHeadBytes <= 0)
      return OperateResult.CreateSuccessResult<byte[]>(byMessage3.Content);
    byte[] buffer = new byte[netMessage.HeadBytes.Length + lengthByHeadBytes];
    netMessage.HeadBytes.CopyTo((Array) buffer, 0);
    OperateResult result = (OperateResult) this.Receive(buffer, netMessage.HeadBytes.Length, lengthByHeadBytes, timeOut, reportProgress);
    return !result.IsSuccess ? OperateResult.CreateFailedResult<byte[]>(result) : OperateResult.CreateSuccessResult<byte[]>(buffer);
  }

  /// <summary>
  /// 接收一行命令数据，需要自己指定这个结束符，默认超时时间为60秒，也即是60000，单位是毫秒<br />
  /// To receive a line of command data, you need to specify the terminator yourself. The default timeout is 60 seconds, which is 60,000, in milliseconds.
  /// </summary>
  /// <param name="endCode">结束符信息</param>
  /// <param name="timeout">超时时间，默认为60000，单位为毫秒，也就是60秒</param>
  /// <returns>带有结果对象的数据信息</returns>
  private OperateResult<byte[]> ReceiveCommandLineFromPipe(byte endCode, int timeout = 60000)
  {
    try
    {
      List<byte> byteList = new List<byte>(128 /*0x80*/);
      DateTime now = DateTime.Now;
      bool flag = false;
      while ((DateTime.Now - now).TotalMilliseconds < (double) timeout)
      {
        OperateResult<byte[]> commandLineFromPipe = this.Receive(1, timeout);
        if (!commandLineFromPipe.IsSuccess)
          return commandLineFromPipe;
        byteList.AddRange((IEnumerable<byte>) commandLineFromPipe.Content);
        if ((int) commandLineFromPipe.Content[0] == (int) endCode)
        {
          flag = true;
          break;
        }
      }
      return !flag ? new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataTimeout} {timeout.ToString()}") : OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <summary>
  /// 接收一行命令数据，需要自己指定这个结束符，默认超时时间为60秒，也即是60000，单位是毫秒<br />
  /// To receive a line of command data, you need to specify the terminator yourself. The default timeout is 60 seconds, which is 60,000, in milliseconds.
  /// </summary>
  /// <param name="endCode1">结束符1信息</param>
  /// <param name="endCode2">结束符2信息</param>
  /// 
  ///             /// <param name="timeout">超时时间，默认无穷大，单位毫秒</param>
  /// <returns>带有结果对象的数据信息</returns>
  private OperateResult<byte[]> ReceiveCommandLineFromPipe(
    byte endCode1,
    byte endCode2,
    int timeout = 60000)
  {
    try
    {
      List<byte> byteList = new List<byte>(128 /*0x80*/);
      DateTime now = DateTime.Now;
      bool flag = false;
      while ((DateTime.Now - now).TotalMilliseconds < (double) timeout)
      {
        OperateResult<byte[]> commandLineFromPipe = this.Receive(1, timeout);
        if (!commandLineFromPipe.IsSuccess)
          return commandLineFromPipe;
        byteList.AddRange((IEnumerable<byte>) commandLineFromPipe.Content);
        if ((int) commandLineFromPipe.Content[0] == (int) endCode2 && byteList.Count > 1 && (int) byteList[byteList.Count - 2] == (int) endCode1)
        {
          flag = true;
          break;
        }
      }
      return !flag ? new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataTimeout} {timeout.ToString()}") : OperateResult.CreateSuccessResult<byte[]>(byteList.ToArray());
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.Send(System.Byte[])" />
  public async Task<OperateResult> SendAsync(byte[] data)
  {
    if (data == null)
      return OperateResult.CreateSuccessResult();
    OperateResult operateResult = await this.SendAsync(data, 0, data.Length).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.Send(System.Byte[],System.Int32,System.Int32)" />
  public virtual async Task<OperateResult> SendAsync(byte[] data, int offset, int size)
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.Send(data, offset, size))).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.Receive(System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public virtual async Task<OperateResult<byte[]>> ReceiveAsync(
    int length,
    int timeOut,
    Action<long, long> reportProgress = null)
  {
    if (length == 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!Authorization.nzugaydgwadawdibbas())
      return new OperateResult<byte[]>(StringResources.Language.AuthorizationFailed);
    OperateResult<byte[]> buffer = NetSupport.CreateReceiveBuffer(length);
    if (!buffer.IsSuccess)
      return buffer;
    OperateResult<int> receive = await this.ReceiveAsync(buffer.Content, 0, length, timeOut, reportProgress).ConfigureAwait(false);
    return receive.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(length > 0 ? buffer.Content : buffer.Content.SelectBegin<byte>(receive.Content)) : OperateResult.CreateFailedResult<byte[]>((OperateResult) receive);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.Receive(System.Byte[],System.Int32,System.Int32,System.Int32,System.Action{System.Int64,System.Int64})" />
  public virtual async Task<OperateResult<int>> ReceiveAsync(
    byte[] buffer,
    int offset,
    int length,
    int timeOut = 60000,
    Action<long, long> reportProgress = null)
  {
    OperateResult<int> async = await Task.Run<OperateResult<int>>((Func<OperateResult<int>>) (() => this.Receive(buffer, offset, length, timeOut, reportProgress))).ConfigureAwait(false);
    return async;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.OpenCommunication" />
  public virtual async Task<OperateResult<bool>> OpenCommunicationAsync()
  {
    OperateResult<bool> operateResult = await Task.Run<OperateResult<bool>>((Func<OperateResult<bool>>) (() => this.OpenCommunication())).ConfigureAwait(false);
    return operateResult;
  }

  public virtual async Task<OperateResult> CloseCommunicationAsync()
  {
    OperateResult operateResult = await Task.Run<OperateResult>((Func<OperateResult>) (() => this.CloseCommunication())).ConfigureAwait(false);
    return operateResult;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.ReceiveCommandLineFromPipe(System.Byte,System.Int32)" />
  private async Task<OperateResult<byte[]>> ReceiveCommandLineFromPipeAsync(
    byte endCode,
    int timeout = 60000)
  {
    try
    {
      List<byte> bufferArray = new List<byte>(128 /*0x80*/);
      DateTime st = DateTime.Now;
      bool bOK = false;
      while ((DateTime.Now - st).TotalMilliseconds < (double) timeout)
      {
        OperateResult<byte[]> headResult = await this.ReceiveAsync(1, timeout).ConfigureAwait(false);
        if (!headResult.IsSuccess)
          return headResult;
        bufferArray.AddRange((IEnumerable<byte>) headResult.Content);
        if ((int) headResult.Content[0] == (int) endCode)
        {
          bOK = true;
          break;
        }
        headResult = (OperateResult<byte[]>) null;
      }
      return bOK ? OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray()) : new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataTimeout} {timeout.ToString()}");
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.ReceiveCommandLineFromPipe(System.Byte,System.Byte,System.Int32)" />
  private async Task<OperateResult<byte[]>> ReceiveCommandLineFromPipeAsync(
    byte endCode1,
    byte endCode2,
    int timeout = 60000)
  {
    try
    {
      List<byte> bufferArray = new List<byte>(128 /*0x80*/);
      DateTime st = DateTime.Now;
      bool bOK = false;
      while ((DateTime.Now - st).TotalMilliseconds < (double) timeout)
      {
        OperateResult<byte[]> headResult = await this.ReceiveAsync(1, timeout).ConfigureAwait(false);
        if (!headResult.IsSuccess)
          return headResult;
        bufferArray.AddRange((IEnumerable<byte>) headResult.Content);
        if ((int) headResult.Content[0] == (int) endCode2 && bufferArray.Count > 1 && (int) bufferArray[bufferArray.Count - 2] == (int) endCode1)
        {
          bOK = true;
          break;
        }
        headResult = (OperateResult<byte[]>) null;
      }
      return bOK ? OperateResult.CreateSuccessResult<byte[]>(bufferArray.ToArray()) : new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataTimeout} {timeout.ToString()}");
    }
    catch (Exception ex)
    {
      return new OperateResult<byte[]>(ex.Message);
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.ReceiveByMessage(System.Int32,HslCommunication.Core.IMessage.INetMessage,System.Action{System.Int64,System.Int64})" />
  private async Task<OperateResult<byte[]>> ReceiveByMessageAsync(
    int timeOut,
    INetMessage netMessage,
    Action<long, long> reportProgress = null)
  {
    if (netMessage == null)
    {
      OperateResult<byte[]> byMessageAsync = await this.ReceiveAsync(-1, timeOut).ConfigureAwait(false);
      return byMessageAsync;
    }
    if (netMessage.ProtocolHeadBytesLength < 0)
    {
      byte[] headCode = BitConverter.GetBytes(netMessage.ProtocolHeadBytesLength);
      int codeLength = (int) headCode[3] & 15;
      OperateResult<byte[]> receive = (OperateResult<byte[]>) null;
      ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable;
      switch (codeLength)
      {
        case 1:
          configuredTaskAwaitable = this.ReceiveCommandLineFromPipeAsync(headCode[1], timeOut).ConfigureAwait(false);
          receive = await configuredTaskAwaitable;
          break;
        case 2:
          receive = await this.ReceiveCommandLineFromPipeAsync(headCode[1], headCode[0], timeOut).ConfigureAwait(false);
          break;
      }
      if (receive == null)
        return new OperateResult<byte[]>("Receive by specified code failed, length check failed");
      if (!receive.IsSuccess)
        return receive;
      netMessage.HeadBytes = receive.Content;
      if (!(netMessage is SpecifiedCharacterMessage message) || message.EndLength == (byte) 0)
        return receive;
      configuredTaskAwaitable = this.ReceiveAsync((int) message.EndLength, timeOut).ConfigureAwait(false);
      OperateResult<byte[]> endResult = await configuredTaskAwaitable;
      if (!endResult.IsSuccess)
        return endResult;
      return OperateResult.CreateSuccessResult<byte[]>(SoftBasic.SpliceArray<byte>(receive.Content, endResult.Content));
    }
    ConfiguredTaskAwaitable<OperateResult<byte[]>> configuredTaskAwaitable1 = this.ReceiveAsync(netMessage.ProtocolHeadBytesLength, timeOut).ConfigureAwait(false);
    OperateResult<byte[]> headResult = await configuredTaskAwaitable1;
    if (!headResult.IsSuccess)
      return headResult;
    int start = netMessage.PependedUselesByteLength(headResult.Content);
    int cycleCount = 0;
    while (start >= netMessage.ProtocolHeadBytesLength)
    {
      configuredTaskAwaitable1 = this.ReceiveAsync(netMessage.ProtocolHeadBytesLength, timeOut).ConfigureAwait(false);
      headResult = await configuredTaskAwaitable1;
      if (!headResult.IsSuccess)
        return headResult;
      start = netMessage.PependedUselesByteLength(headResult.Content);
      ++cycleCount;
      if (cycleCount > 10)
        break;
    }
    if (start > 0)
    {
      configuredTaskAwaitable1 = this.ReceiveAsync(start, timeOut).ConfigureAwait(false);
      OperateResult<byte[]> head2Result = await configuredTaskAwaitable1;
      if (!head2Result.IsSuccess)
        return head2Result;
      headResult.Content = SoftBasic.SpliceArray<byte>(headResult.Content.RemoveBegin<byte>(start), head2Result.Content);
      head2Result = (OperateResult<byte[]>) null;
    }
    netMessage.HeadBytes = headResult.Content;
    int contentLength = netMessage.GetContentLengthByHeadBytes();
    if (contentLength <= 0)
      return OperateResult.CreateSuccessResult<byte[]>(headResult.Content);
    byte[] result = new byte[netMessage.HeadBytes.Length + contentLength];
    netMessage.HeadBytes.CopyTo((Array) result, 0);
    OperateResult<int> operateResult = await this.ReceiveAsync(result, netMessage.HeadBytes.Length, contentLength, timeOut, reportProgress).ConfigureAwait(false);
    OperateResult contentResult = (OperateResult) operateResult;
    operateResult = (OperateResult<int>) null;
    return contentResult.IsSuccess ? OperateResult.CreateSuccessResult<byte[]>(result) : OperateResult.CreateFailedResult<byte[]>(contentResult);
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.ReceiveMessage(HslCommunication.Core.IMessage.INetMessage,System.Byte[],System.Boolean,System.Action{System.Int64,System.Int64},System.Action{System.Byte[]})" />
  public virtual async Task<OperateResult<byte[]>> ReceiveMessageAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool useActivePush = true,
    Action<long, long> reportProgress = null,
    Action<byte[]> logMessage = null)
  {
    if (this.useServerActivePush & useActivePush)
    {
      if (this.autoResetEvent.WaitOne(this.ReceiveTimeOut))
      {
        if (netMessage != null)
          netMessage.HeadBytes = this.bufferQA;
        Action<byte[]> action = logMessage;
        if (action != null)
          action(this.bufferQA);
        return OperateResult.CreateSuccessResult<byte[]>(this.bufferQA);
      }
      this.CloseCommunication();
      return new OperateResult<byte[]>(-this.IncrConnectErrorCount(), StringResources.Language.ReceiveDataTimeout + this.ReceiveTimeOut.ToString());
    }
    if (netMessage == null || netMessage.ProtocolHeadBytesLength == -1)
    {
      if (netMessage != null && netMessage.SendBytes == null)
        netMessage.SendBytes = sendValue;
      DateTime startTime = DateTime.Now;
      MemoryStream ms = new MemoryStream();
      OperateResult<byte[]> read;
      while (true)
      {
        read = await this.ReceiveByMessageAsync(this.ReceiveTimeOut, (INetMessage) null, reportProgress).ConfigureAwait(false);
        if (read.IsSuccess)
        {
          if (read.Content != null && read.Content.Length != 0)
          {
            ms.Write(read.Content);
            Action<byte[]> action = logMessage;
            if (action != null)
              action(read.Content);
          }
          if (netMessage != null)
          {
            if (!netMessage.CheckReceiveDataComplete(sendValue, ms))
            {
              if (this.ReceiveTimeOut < 0 || (DateTime.Now - startTime).TotalMilliseconds <= (double) this.ReceiveTimeOut)
                read = (OperateResult<byte[]>) null;
              else
                goto label_23;
            }
            else
              goto label_21;
          }
          else
            goto label_19;
        }
        else
          break;
      }
      return read;
label_19:
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
label_21:
      return OperateResult.CreateSuccessResult<byte[]>(ms.ToArray());
label_23:
      return new OperateResult<byte[]>($"{StringResources.Language.ReceiveDataTimeout}{this.ReceiveTimeOut.ToString()} Received: {ms.ToArray().ToHexString(' ')}");
    }
    OperateResult<byte[]> read1 = await this.ReceiveByMessageAsync(this.ReceiveTimeOut, netMessage, reportProgress).ConfigureAwait(false);
    if (read1.IsSuccess)
    {
      Action<byte[]> action = logMessage;
      if (action != null)
        action(read1.Content);
    }
    return read1;
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.ReadFromCoreServerHelper(HslCommunication.Core.IMessage.INetMessage,System.Byte[],System.Boolean,System.Int32,System.Action{System.Byte[]})" />
  protected async Task<OperateResult<byte[]>> ReadFromCoreServerHelperAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    int sleep,
    Action<byte[]> logMessage = null)
  {
    if (netMessage != null)
      netMessage.SendBytes = sendValue;
    OperateResult sendResult = await this.SendAsync(sendValue).ConfigureAwait(false);
    if (!sendResult.IsSuccess)
      return OperateResult.CreateFailedResult<byte[]>(sendResult);
    if (this.ReceiveTimeOut < 0)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (!hasResponseData)
      return OperateResult.CreateSuccessResult<byte[]>(new byte[0]);
    if (sleep > 0)
      HslHelper.ThreadSleep(sleep);
    DateTime start = DateTime.Now;
    int times = 0;
    OperateResult<byte[]> resultReceive;
    do
    {
      resultReceive = await this.ReceiveMessageAsync(netMessage, sendValue, logMessage: logMessage).ConfigureAwait(false);
      if (resultReceive.IsSuccess)
      {
        if (netMessage != null)
        {
          int check = netMessage.CheckMessageMatch(sendValue, resultReceive.Content);
          switch (check)
          {
            case 0:
              goto label_16;
            case 1:
              goto label_20;
            default:
              ++times;
              continue;
          }
        }
        else
          goto label_20;
      }
      else
        goto label_13;
    }
    while (this.ReceiveTimeOut < 0 || (DateTime.Now - start).TotalMilliseconds <= (double) this.ReceiveTimeOut);
    goto label_18;
label_13:
    return resultReceive;
label_16:
    return new OperateResult<byte[]>($"INetMessage.CheckMessageMatch failed{Environment.NewLine}{StringResources.Language.Send}: {SoftBasic.ByteToHexString(sendValue, ' ')}{Environment.NewLine}{StringResources.Language.Receive}: {SoftBasic.ByteToHexString(resultReceive.Content, ' ')}");
label_18:
    return new OperateResult<byte[]>($"Receive Message timeout: {this.ReceiveTimeOut.ToString()} CheckMessageMatch times:{times.ToString()}");
label_20:
    if (netMessage == null || netMessage.CheckHeadBytesLegal((byte[]) null))
      return OperateResult.CreateSuccessResult<byte[]>(resultReceive.Content);
    return new OperateResult<byte[]>($"{StringResources.Language.CommandHeadCodeCheckFailed}{Environment.NewLine}{StringResources.Language.Send}: {SoftBasic.ByteToHexString(sendValue, ' ')}{Environment.NewLine}{StringResources.Language.Receive}: {SoftBasic.ByteToHexString(resultReceive.Content, ' ')}");
  }

  /// <inheritdoc cref="M:HslCommunication.Core.Pipe.CommunicationPipe.ReadFromCoreServer(HslCommunication.Core.IMessage.INetMessage,System.Byte[],System.Boolean,System.Action{System.Byte[]})" />
  public virtual async Task<OperateResult<byte[]>> ReadFromCoreServerAsync(
    INetMessage netMessage,
    byte[] sendValue,
    bool hasResponseData,
    Action<byte[]> logMessage = null)
  {
    OperateResult<byte[]> read = await this.ReadFromCoreServerHelperAsync(netMessage, sendValue, hasResponseData, this.SleepTime, logMessage).ConfigureAwait(false);
    if (!read.IsSuccess)
    {
      if (read.ErrorCode >= 0 || read.ErrorCode == int.MinValue)
        ;
      return read;
    }
    this.ResetConnectErrorCount();
    return read;
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  protected virtual void Dispose(bool disposing)
  {
    if (!disposing)
      return;
    this.autoResetEvent?.Dispose();
    this.communicationLock?.Dispose();
  }

  /// <inheritdoc cref="M:System.IDisposable.Dispose" />
  public void Dispose()
  {
    if (this.disposedValue)
      return;
    this.Dispose(true);
    GC.SuppressFinalize((object) this);
    this.disposedValue = true;
  }
}
