﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.IMessage.MelsecA1EAsciiMessage
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;
using System.Text;

#nullable disable
namespace HslCommunication.Core.IMessage;

/// <summary>三菱的A兼容1E帧ASCII协议解析规则</summary>
public class MelsecA1EAsciiMessage : NetMessageBase, INetMessage
{
  /// <inheritdoc cref="P:HslCommunication.Core.IMessage.INetMessage.ProtocolHeadBytesLength" />
  public int ProtocolHeadBytesLength => 4;

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.GetContentLengthByHeadBytes" />
  public int GetContentLengthByHeadBytes()
  {
    if (this.HeadBytes[2] == (byte) 53 && this.HeadBytes[3] == (byte) 66)
      return 4;
    if (this.HeadBytes[2] != (byte) 48 /*0x30*/ || this.HeadBytes[3] != (byte) 48 /*0x30*/)
      return 0;
    int num = Convert.ToInt32(Encoding.ASCII.GetString(this.SendBytes, 20, 2), 16 /*0x10*/);
    if (num == 0)
      num = 256 /*0x0100*/;
    switch (this.HeadBytes[1])
    {
      case 48 /*0x30*/:
        return num % 2 == 1 ? num + 1 : num;
      case 49:
        return num * 4;
      case 50:
      case 51:
        return 0;
      default:
        return 0;
    }
  }

  /// <inheritdoc cref="M:HslCommunication.Core.IMessage.INetMessage.CheckHeadBytesLegal(System.Byte[])" />
  public override bool CheckHeadBytesLegal(byte[] token)
  {
    return this.HeadBytes != null && (int) this.HeadBytes[0] - (int) this.SendBytes[0] == 8;
  }
}
