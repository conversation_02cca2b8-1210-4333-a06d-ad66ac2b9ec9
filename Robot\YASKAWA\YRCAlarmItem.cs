﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Robot.YASKAWA.YRCAlarmItem
// Assembly: HslCommunication, Version=12.3.3.0, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.Core;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Robot.YASKAWA;

/// <summary>安川的报警信息</summary>
public class YRCAlarmItem
{
  /// <summary>实例化一个默认的对象</summary>
  public YRCAlarmItem()
  {
  }

  /// <summary>使用原始数据来实例化一个报警的对象</summary>
  /// <param name="byteTransform">字节的变换顺序</param>
  /// <param name="content">原始字节数据</param>
  /// <param name="encoding">字符串的编码信息</param>
  public YRCAlarmItem(IByteTransform byteTransform, byte[] content, Encoding encoding)
  {
    this.AlarmCode = byteTransform.TransInt32(content, 0);
    this.Time = Convert.ToDateTime(Encoding.ASCII.GetString(content, 16 /*0x10*/, 16 /*0x10*/));
    this.Message = encoding.GetString(content.RemoveBegin<byte>(32 /*0x20*/));
  }

  /// <summary>报警代码</summary>
  public int AlarmCode { get; set; }

  /// <summary>报警发生的时间</summary>
  public DateTime Time { get; set; }

  /// <summary>报警文字列名称</summary>
  public string Message { get; set; }

  /// <inheritdoc />
  public override string ToString() => $"[{this.AlarmCode}] Time:[{this.Time}] {this.Message}";
}
