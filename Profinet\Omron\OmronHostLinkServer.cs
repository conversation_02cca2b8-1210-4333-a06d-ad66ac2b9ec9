﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Profinet.Omron.OmronHostLinkServer
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using HslCommunication.BasicFramework;
using HslCommunication.Core.IMessage;
using HslCommunication.Core.Net;
using System;
using System.Text;

#nullable disable
namespace HslCommunication.Profinet.Omron;

/// <summary>
/// 欧姆龙的HostLink虚拟服务器，支持DM区，CIO区，Work区，Hold区，Auxiliary区，可以方便的进行测试<br />
/// Omron's HostLink virtual server supports DM area, CIO area, Work area, Hold area, and Auxiliary area, which can be easily tested
/// </summary>
/// <remarks>支持TCP的接口以及串口，方便客户端进行测试，或是开发用于教学的虚拟服务器对象</remarks>
public class OmronHostLinkServer : OmronFinsServer
{
  /// <inheritdoc cref="M:HslCommunication.Profinet.Omron.OmronFinsServer.#ctor" />
  public OmronHostLinkServer()
  {
    this.connectionInitialization = false;
    this.LogMsgFormatBinary = false;
  }

  /// <inheritdoc cref="P:HslCommunication.Profinet.Omron.OmronHostLink.UnitNumber" />
  public byte UnitNumber { get; set; }

  /// <inheritdoc />
  protected override INetMessage GetNewNetMessage()
  {
    return (INetMessage) new SpecifiedCharacterMessage((byte) 13);
  }

  /// <inheritdoc />
  protected override OperateResult<byte[]> ReadFromCoreServer(PipeSession session, byte[] receive)
  {
    if (receive.Length < 22)
      return new OperateResult<byte[]>("Uknown Data：" + receive.ToHexString(' '));
    byte[] numArray = this.ReadFromFinsCore(SoftBasic.HexStringToBytes(Encoding.ASCII.GetString(receive, 14, receive.Length - 18)));
    numArray[13] = receive[12];
    numArray[14] = receive[13];
    return OperateResult.CreateSuccessResult<byte[]>(numArray);
  }

  /// <inheritdoc />
  protected override byte[] PackCommand(int status, byte[] finsCore, byte[] data)
  {
    if (data == null)
      data = new byte[0];
    data = SoftBasic.BytesToAsciiBytes(data);
    byte[] numArray = new byte[27 + data.Length];
    Encoding.ASCII.GetBytes("@00FA0040000000").CopyTo((Array) numArray, 0);
    Encoding.ASCII.GetBytes(this.UnitNumber.ToString("X2")).CopyTo((Array) numArray, 1);
    if (data.Length != 0)
      data.CopyTo((Array) numArray, 23);
    Encoding.ASCII.GetBytes(finsCore.SelectBegin<byte>(2).ToHexString()).CopyTo((Array) numArray, 15);
    Encoding.ASCII.GetBytes(status.ToString("X4")).CopyTo((Array) numArray, 19);
    int num = (int) numArray[0];
    for (int index = 1; index < numArray.Length - 4; ++index)
      num ^= (int) numArray[index];
    SoftBasic.BuildAsciiBytesFrom((byte) num).CopyTo((Array) numArray, numArray.Length - 4);
    numArray[numArray.Length - 2] = (byte) 42;
    numArray[numArray.Length - 1] = (byte) 13;
    return numArray;
  }

  /// <inheritdoc />
  protected override bool CheckSerialReceiveDataComplete(byte[] buffer, int receivedLength)
  {
    return receivedLength > 1 && buffer[receivedLength - 1] == (byte) 13;
  }

  /// <inheritdoc />
  public override string ToString() => $"OmronHostLinkServer[{this.Port}]";
}
