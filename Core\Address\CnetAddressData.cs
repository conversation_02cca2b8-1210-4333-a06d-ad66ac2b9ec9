﻿// Decompiled with JetBrains decompiler
// Type: HslCommunication.Core.Address.CnetAddressData
// Assembly: HslCommunication, Version=********, Culture=neutral, PublicKeyToken=3d72ad3b6b5ec0e3
// MVID: 8961A602-B2CC-4111-8AE2-0BC46C18A579
// Assembly location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.dll
// XML documentation location: C:\Users\<USER>\Downloads\HslCommunicationDemo-v12.3.3\HslCommunication.xml

using System;

#nullable disable
namespace HslCommunication.Core.Address;

/// <summary>Cnet协议的地址数据信息，未完成</summary>
public class CnetAddressData : DeviceAddressDataBase
{
  /// <summary>
  /// 数据的代号，通常是 'P', 'M', 'L', 'K', 'F', 'T', 'C', 'D', 'S', 'Q', 'I', 'N', 'U', 'Z', 'R'
  /// </summary>
  public string DataCode { get; set; }

  /// <summary>数据的类型，通常是 X,B,W,D,L</summary>
  public string DataType { get; set; }

  /// <summary>
  /// 从实际的PLC的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual PLC address
  /// </summary>
  /// <param name="address">西门子的地址数据信息</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<CnetAddressData> ParseFrom(string address)
  {
    return CnetAddressData.ParseFrom(address, (ushort) 0);
  }

  /// <summary>
  /// 从实际的PLC的地址里面解析出地址对象<br />
  /// Resolve the address object from the actual PLC address
  /// </summary>
  /// <param name="address">PLC的地址数据信息</param>
  /// <param name="length">读取的数据长度</param>
  /// <returns>是否成功的结果对象</returns>
  public static OperateResult<CnetAddressData> ParseFrom(string address, ushort length)
  {
    CnetAddressData cnetAddressData = new CnetAddressData();
    try
    {
      cnetAddressData.Length = length;
      if (address[1] != 'X')
        ;
    }
    catch (Exception ex)
    {
      return new OperateResult<CnetAddressData>(DeviceAddressDataBase.GetUnsupportedAddressInfo(address, ex));
    }
    return OperateResult.CreateSuccessResult<CnetAddressData>(cnetAddressData);
  }
}
